<template>
  <div v-if="visible" class="info-content">
    <div class="tab-title">
      <div class="title-text">运行信息</div>
      <div class="tab-title-oper">
        <el-button type="primary" size="small" @click="getNodeInfo"> 刷新 </el-button>
        <span class="iconfont icon-close-small test-close-icon" @click="close"></span>
      </div>
    </div>
    <div v-loading="refreshLoading" class="tab-content">
      <div class="info-label text-ellipsis">
        <div :title="'组件名称：' + nodeInfo.componentName">
          组件名称：{{ nodeInfo.componentName }}
        </div>
      </div>
      <div class="info-label text-ellipsis">输入数据量：{{ nodeInfo.numRecordsIn }}</div>
      <div class="info-label text-ellipsis">输出数据量：{{ nodeInfo.numRecordsOut }}</div>
      <div class="info-label text-ellipsis">失败数据量：{{ nodeInfo.dirtyRecordsNum }}</div>
      <div class="info-label text-ellipsis">TPS：{{ nodeInfo.tps }}</div>
      <div class="info-label text-ellipsis">传输延时：{{ nodeInfo.translateDelay }}</div>
      <div class="info-label text-ellipsis">处理延时：{{ nodeInfo.processDelay }}</div>
      <div class="info-label text-ellipsis">预警数量：{{ nodeInfo.alterNum }}</div>
      <div class="info-label text-ellipsis">
        近一分钟数据处理量：{{ nodeInfo.lastOneMinsProcess }}
      </div>
      <div class="info-label text-ellipsis">
        近十分钟数据处理量：{{ nodeInfo.lastTenMinsProcess }}
      </div>
      <div class="info-label text-ellipsis">
        近一小时数据处理量：{{ nodeInfo.lastOneHourProcess }}
      </div>
      <div class="info-label text-ellipsis">近一天数据处理量：{{ nodeInfo.lastOneDayProcess }}</div>
      <div v-for="(val, key) in nodeInfo.metricExps" :key="val" class="info-label text-ellipsis">
        {{ key }}：{{ val }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { get } from '@/apis/utils/net';
import { URL_COMPONENT_METRICS } from '@/apis/commonApi';

@Component
export default class NodeInfo extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ required: true }) flowId!: string;
  @Prop({ required: true }) nodeId!: string | number;

  refreshLoading = false;
  nodeInfo: any = {};

  @Watch('nodeId')
  nodeIdChange() {
    this.getNodeInfo();
  }

  created() {
    this.getNodeInfo();
  }

  async getNodeInfo() {
    this.refreshLoading = true;
    const { success, data, msg } = await get(URL_COMPONENT_METRICS, {
      jobId: this.flowId,
      nodeId: this.nodeId
    });
    this.refreshLoading = false;
    if (success) {
      this.nodeInfo = data;
      return;
    }
    this.$tip.error(msg);
  }

  close() {
    this.$emit('update:visible', false);
  }
}
</script>

<style lang="scss" scoped>
.info-content {
  position: absolute;
  bottom: 0px;
  height: 340px;
  width: 100%;
  background-color: #fff;
  border-top: 1px solid $--bs-color-border-lighter;
  .tab-title-oper {
    display: flex;
    align-items: center;
  }
  .test-close-icon {
    margin-left: 10px;
    font-size: 20px;
    cursor: pointer;
  }
  .tab-content {
    display: flex;
    flex-wrap: wrap;
    height: 200px;
    padding-left: 16px;
    padding-right: 16px;

    .info-label {
      width: 25%;
      font-size: 12px;
      border: 10px solid #fff;
    }
  }
}
</style>
