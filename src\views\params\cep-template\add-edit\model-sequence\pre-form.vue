<template>
  <!-- 主体内容 -->
  <el-form
    ref="formRef"
    :rules="rules"
    :model="formData"
    class="base-form"
    :label-width="labelWidth"
    label-position="right"
  >
    <template v-for="el in renderList">
      <el-form-item
        v-if="Array.isArray(el.children) && !el.hidden"
        :key="el.name"
        :label="el.label"
      >
        <div
          class="base-form__content base-form__content--two"
          :class="{ 'base-form__content--tooltip': el.tooltip }"
        >
          <form-item
            v-for="item in el.children"
            :key="item.name"
            :config="item"
            :label-width="'0px'"
            :form-data="formData.maximumAllowableTimeInterval"
          />
        </div>
        <!-- 提示 -->
        <el-tooltip v-if="el.tooltip" effect="light" :content="el.tooltip" placement="bottom">
          <i class="iconfont icon-wenhao"></i>
        </el-tooltip>
      </el-form-item>
      <form-item v-else :key="el.name" :form-data="formData" :config="el" />
    </template>
  </el-form>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Watch, Vue } from 'vue-property-decorator';
import type { BaseInfoRenderItem } from '../../type';
import FormItem from '../component/form-item.vue';
import elForm from 'bs-ui-pro/packages/form/index.js';

@Component({ components: { FormItem } })
export default class PreForm extends Vue {
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ type: Array, default: () => [] }) groupList!: any[];
  @PropSync('data', { default: () => ({}) }) formData!: any;
  @Ref('formRef') readonly form!: elForm;
  private labelWidth = '77px';
  private renderList: BaseInfoRenderItem[] = [
    {
      label: '时间间隔',
      name: 'maximumAllowableTimeInterval',
      tooltip: '两个个体模式间的最大时间间隔，超过这个时间间隔则匹配失败',
      children: [
        {
          label: '',
          name: 'timeValue',
          type: 'number',
          placeholder: '请输入模式匹配最大允许时间间隔'
        },
        {
          label: '',
          name: 'timeValueType',
          type: 'select',
          placeholder: '请选择模式匹配最大允许时间间隔单位',
          options: [
            {
              label: '毫秒',
              value: 'MILLISECOND'
            },
            {
              label: '秒',
              value: 'SECOND'
            },
            {
              label: '分',
              value: 'MINUTES'
            },
            {
              label: '小时',
              value: 'HOURS'
            },
            {
              label: '天',
              value: 'DAYS'
            }
          ]
        }
      ]
    },
    {
      label: '跳过策略',
      name: 'sequenceSkipStrategy',
      tooltip:
        '对于一个给定的模式，同一个事件可能会分配到多个成功的匹配上。为了控制一个事件会分配到多少个匹配上，你需要指定跳过策略。',
      type: 'select',
      options: [
        {
          label: 'NO_SKIP',
          value: 'NO_SKIP'
        },
        {
          label: 'SKIP_TO_NEXT',
          value: 'SKIP_TO_NEXT'
        },
        {
          label: 'SKIP_PAST_LAST_EVENT',
          value: 'SKIP_PAST_LAST_EVENT'
        },
        {
          label: 'SKIP_TO_FIRST',
          value: 'SKIP_TO_FIRST'
        },
        {
          label: 'SKIP_TO_LAST',
          value: 'SKIP_TO_LAST'
        }
      ],
      placeholder: '请选择模式序列跳过策略'
    },
    {
      hidden: true,
      label: '模式名称',
      name: 'groupName',
      tooltip: '当使用SKIP_TO_FIRST和SKIP_TO_LAST策略时，需要指定一个合法的PatternName。',
      type: 'select',
      options: [],
      placeholder: '请选择模式名称'
    }
  ];
  get rules() {
    return {
      sequenceSkipStrategy: {
        required: true,
        message: '请选择模式序列跳过策略',
        trigger: 'change'
      },
      groupName: {
        required: ['SKIP_TO_FIRST', 'SKIP_TO_LAST'].includes(this.formData.sequenceSkipStrategy),
        trigger: 'change',
        values: this.renderList[2].options?.map(({ value }: any) => value),
        validator: (rule, value, callback) => {
          if (!rule.required) return callback();
          if (!value) return callback(new Error('请选择模式名称'));
          if (!rule.values.includes(value)) return callback(new Error('请选择有效模式名称'));
          callback();
        }
      }
    };
  }

  @Watch('formData.sequenceSkipStrategy', { immediate: true })
  handlerStrategyChange(value: string) {
    const result = ['SKIP_TO_FIRST', 'SKIP_TO_LAST'].includes(value);
    this.$set(this.renderList[2], 'hidden', !result);
  }
  @Watch('formData.groupCepPatternConditionList', { immediate: true, deep: true })
  handlerList() {
    this.$set(this.renderList[2], 'options', this.generateOptions());
  }
  @Watch('groupList', { immediate: true })
  handlerGroupList() {
    this.$set(this.renderList[2], 'options', this.generateOptions());
  }

  generateOptions() {
    return this.formData.groupCepPatternConditionList
      .map(({ modelOrGroupName }: any) => {
        if (this.groupList.includes(modelOrGroupName)) return null;
        return {
          label: modelOrGroupName,
          value: modelOrGroupName
        };
      })
      .filter(Boolean);
  }
  validate() {
    return this.form.validate();
  }
}
</script>

<style lang="scss" scoped>
::v-deep .base-form {
  &__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    &--tooltip {
      margin-right: 7px;
    }
    &--one {
      > div {
        width: 100%;
      }
    }
    &--two {
      > div {
        width: 48%;
      }
    }
  }
}
.base {
  &-form {
    display: block;
    padding-left: 20px;
    width: 587px;
    box-sizing: border-box;
    ::v-deep .el-form-item__content {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }
}
</style>
