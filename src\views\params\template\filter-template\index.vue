<template>
  <pro-page title="过滤模板" :fixed-header="false" class="filter">
    <div slot="operation">
      <bs-search
        v-model="searchObj.search"
        class="filter-search"
        size="small"
        placeholder="请输入模板名称"
        @input="debounceSearch"
      />
      <el-button v-access="'PA.SETTING.MODEL.FILTER.ADD'" type="primary" size="small" @click="add">
        新建
      </el-button>
    </div>
    <pro-table
      ref="proTable"
      v-loading="tableLoading"
      :columns="columnData"
      :request="request"
      :actions="actions"
      :options="options"
      @action-click="handleActionClick"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { post, del } from '@/apis/utils/net';
import { cloneDeep, debounce } from 'lodash';
@Component({
  name: 'ElementFilterTemplate'
})
export default class ElementFilterTemplate extends PaBase {
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 }
  };
  columnData = [];
  options = {
    actionHandle: ({ actions }) => {
      return actions.filter(({ access }) =>
        this.$store.state.userInfo.authorities.includes(access)
      );
    },
    pageOptions: {
      pageSize: 20,
      total: 1,
      layout: 'total, sizes, prev, pager, next, jumper',
      pageSizes: [20, 40, 60, 80],
      pagerCount: 7
    }
  };
  actions = [
    {
      label: '编辑',
      icon: 'iconfont icon-bianji',
      value: 'edit',
      access: 'PA.SETTING.MODEL.FILTER.EDIT'
    },
    {
      label: '删除',
      icon: 'iconfont icon-shanchu',
      value: 'del',
      access: 'PA.SETTING.MODEL.FILTER.DELETE'
    },
    {
      label: '查看历史版本',
      icon: 'iconfont icon-lishi',
      value: 'viewHistory',
      access: 'PA.SETTING.MODEL.FILTER.MENU'
    }
  ];
  debounceSearch = debounce(this.search, 500);
  activated() {
    this.$refs.proTable!['loadData']();
  }

  // 新建
  add() {
    this.$router.push({
      path: 'filterTemplateDetail',
      query: { id: '', title: '新建模板' }
    });
  }

  // 编辑
  edit(row) {
    this.$router.push({
      path: 'filterTemplateDetail',
      query: { id: row.id, title: `过滤模板：${row.filterTemplateName || '编辑模板'}` }
    });
  }

  // 删除
  del(row) {
    this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const { error, msg, success } = await del('/rs/pa/filter/deleteById', {
        id: row.id
      });
      if (success) {
        this.$message({ message: '删除模板成功', type: 'success' });
        this.$refs.proTable!['loadData']();
      } else {
        this.$message({ message: msg || error, type: 'error' });
      }
    });
  }

  // 查看历史版本
  viewHistory(row) {
    this.$router.push({
      path: 'filterTemplateHistory',
      query: {
        id: row.id,
        title: `历史过滤模板：${row.filterTemplateName || '历史模板'}`
      }
    });
  }

  async request(page) {
    this.tableLoading = true;
    const searchObj = cloneDeep(this.searchObj);
    searchObj.search = searchObj.search.trim();
    try {
      const { data, success, msg } = await post('/rs/pa/filter/list', {
        search: searchObj.search,
        pageData: page
      });
      if (success) {
        data.columnData.forEach((el) => {
          if (el.prop) {
            el.value = el.prop;
            if (el.value === 'updateTime') el.valueProps = { type: 'datetime' };
          }
        });
        this.columnData = data.columnData;
        this.tableLoading = false;
        return { data: data.tableData, total: data.pageData.total };
      } else {
        this.$message.error(msg);
      }
      this.tableLoading = false;
    } catch {
      this.tableLoading = false;
    }
  }

  handleActionClick(event, { row }) {
    this[event](row);
  }

  search() {
    this.$refs.proTable!['loadDataAndReset']();
  }
}
</script>

<style lang="scss" scoped>
.filter {
  background: #fff;
  height: calc(100vh - 114px);
  &-search {
    width: 210px;
    margin-right: 10px;
  }
  &-tooltip {
    cursor: pointer;
  }
  &-icon {
    margin: 0 5px;
  }
}
</style>
