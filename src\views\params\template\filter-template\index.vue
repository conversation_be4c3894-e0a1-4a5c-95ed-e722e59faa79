<template>
  <pro-page :title="$t('pa.menu.filterTemplate')" :fixed-header="false" class="filter">
    <div slot="operation">
      <bs-search
        v-model="searchObj.search"
        class="filter-search"
        size="small"
        :placeholder="$t('pa.params.template.name')"
        @input="debounceSearch"
      />
      <el-button v-access="'PA.SETTING.MODEL.FILTER.ADD'" type="primary" size="small" @click="add">
        {{ $t('pa.action.add') }}
      </el-button>
    </div>
    <pro-table
      ref="proTable"
      :columns="columnData"
      :request="request"
      :actions="actions"
      :options="options"
      :init-request="false"
      @refresh="refresh"
      @action-click="handleActionClick"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { post, del } from '@/apis/utils/net';
import { cloneDeep, debounce } from 'lodash';
@Component({
  name: 'ElementFilterTemplate'
})
export default class ElementFilterTemplate extends Vue {
  refreshStatus = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 }
  };
  columnData = [];
  options = {
    actionHandle: ({ actions }) => {
      return actions.filter(({ access }) => this.$store.getters.authorities.includes(access));
    },
    pageOptions: {
      pageSize: this.$store.getters.pageSize,
      currentPage: 1,
      total: 1
    }
  };
  actions = [
    {
      label: this.$t('pa.action.edit'),
      icon: 'iconfont icon-bianji',
      value: 'edit',
      access: 'PA.SETTING.MODEL.FILTER.EDIT'
    },
    {
      label: this.$t('pa.action.del'),
      icon: 'iconfont icon-shanchu',
      value: 'del',
      access: 'PA.SETTING.MODEL.FILTER.DELETE'
    },
    {
      label: this.$t('pa.action.viewVersion'),
      icon: 'iconfont icon-lishi',
      value: 'viewHistory',
      access: 'PA.SETTING.MODEL.FILTER.MENU'
    }
  ];
  debounceSearch = debounce(this.search, 500);
  activated() {
    this.$refs.proTable!['loadData']();
  }

  // 新建
  add() {
    this.$router.push({
      path: 'filterTemplateDetail',
      query: { id: '', title: this.$t('pa.params.template.addTemplate') as string }
    });
  }

  // 编辑
  edit(row) {
    this.$router.push({
      path: 'filterTemplateDetail',
      query: {
        id: row.id,
        title: `${this.$t('pa.menu.filterTemplate')}：${
          row.filterTemplateName || this.$t('pa.params.template.editTemplate')
        }`
      }
    });
  }

  // 删除
  del(row) {
    this.$confirm(this.$t('pa.params.delConfirm') as string, this.$t('pa.prompt') as string).then(async () => {
      const { error, msg, success } = await del('/rs/pa/filter/deleteById', {
        id: row.id
      });
      if (success) {
        this.$message.success(this.$t('pa.params.template.delSuccess'));
        this.$refs.proTable!['loadData']();
      } else {
        this.$message.error(msg || error);
      }
    });
  }

  // 查看历史版本
  viewHistory(row) {
    this.$router.push({
      path: 'filterTemplateHistory',
      query: {
        id: row.id,
        title: `${this.$t('pa.params.template.historicalFilterTemplate')}：${
          row.filterTemplateName || this.$t('pa.params.template.historicalTemplate')
        }`
      }
    });
  }

  async request(page) {
    const searchObj = cloneDeep(this.searchObj);
    searchObj.search = searchObj.search.trim();
    const { data, success, msg } = await post('/rs/pa/filter/list', {
      search: searchObj.search,
      pageData: page.page
    });
    if (success) {
      data.columnData.forEach((el) => {
        if (el.prop) {
          el.value = el.prop;
          if (this.isEn && el.prop === 'filterTemplateName') el.width = 240;
          if (el.value === 'updateTime') el.valueProps = { type: 'datetime' };
        }
      });
      this.columnData = data.columnData;
      this.refreshStatus = true;
      return { data: data.tableData, total: data.pageData.total };
    } else {
      this.refreshStatus = false;
      this.$message.error(msg);
    }
  }

  handleActionClick(event, { row }) {
    this[event](row);
  }

  search() {
    this.$refs.proTable!['loadDataAndReset']();
  }

  //点击刷新图标刷新
  refresh() {
    if (this.refreshStatus) this.$message.success(this.$t('pa.tip.refreshSuccess'));
  }
}
</script>

<style lang="scss" scoped>
.filter {
  &-search {
    width: 210px;
    margin-right: 10px;
  }
}
</style>
