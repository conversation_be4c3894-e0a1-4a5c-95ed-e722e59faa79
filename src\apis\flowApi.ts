import { get, post, put, postFile, del } from './utils/net';

const getFlowById = (data: unknown) => {
  return get('/rs/pa/job/findById', data);
};

// 流程管理：获取项目和流程信息
const getFlowList = (data: unknown) => {
  return get('/rs/pa/job/search', data);
};

// 获取项目详情
const getProjectById = (data: unknown) => {
  return get('/rs/pa/project/findById', data);
};

// 新建项目
const addProject = (data: unknown) => {
  return post('/rs/pa/project/add', data);
};

// 更新项目
const updateProject = (data: unknown) => {
  return put('/rs/pa/project/update', data);
};

// 删除项目
const deleteProject = (data: unknown) => {
  return del('/rs/pa/project/deleteById', data);
};

// 导出流程
const exportFlows = (data: unknown) => {
  return postFile('/rs/pa/job/export', data);
};

// 预发布流程
const prePublishFlows = (data: unknown) => {
  return post('/rs/pa/job/prePublish', data);
};

// 发布流程
const publishFlows = (data: unknown) => {
  return post('/rs/pa/job/publish', data);
};

// 取消发布流程
const cancelPubFlows = (data: unknown) => {
  return post('/rs/pa/job/cancelPublish', data);
};

// 预启动流程
const preOnlineFlows = (data: unknown) => {
  return post('/rs/pa/job/preOnline', data);
};

// 启动流程
const onlineFlows = (data: unknown) => {
  return post('/rs/pa/job/online', data);
};

// 停止流程
const offlineFlows = (data: unknown) => {
  return post('/rs/pa/job/offline', data);
};

// 获取项目列表
const getProjectsByOrgId = (data: unknown) => {
  return get('/rs/pa/project/getProjectsByOrgId', data);
};

// 新建流程
const addFlow = (data: unknown) => {
  return post('/rs/pa/job/add', data);
};

// 更新流程
const updateFlow = (data: unknown) => {
  return put('/rs/pa/job/update', data);
};

// 移动流程
const moveFlow = (data: unknown) => {
  return post('/rs/pa/job/move', data);
};

// 复制流程
const copyFlow = (data: unknown) => {
  return post('/rs/pa/job/copy', data);
};

// 删除流程
const deleteFlow = (data: unknown) => {
  return del('/rs/pa/job/deleteById', {}, { data: data });
};

// 获取流程节点的引用关系
const getComponentDataGlobalRef = (data: unknown) => {
  return get('/rs/pa/job/componentDataGlobalRef', data);
};

// 全量搜索接口
const getFullSearchList = (data: unknown, type: string) => {
  return post(`/rs/pa/project/fullSearch?projectSelectType=${type}`, data);
};

// 项目列表搜索接口
const getProjectList = ({ name, sortByName }) => {
  return get(`/rs/pa/project/projectList?projectName=${name}&sortByName=${sortByName}`);
};

// 根据项目id和流程类型查询流程
const getFlowListBy = (data: unknown) => {
  return post('/rs/pa/job/listJob', data);
};

// 获取hive组件配置参数
const getHiveInfo = ({ resId, level1 }) => {
  return get(`/rs/pa/res/hiveInfo?resId=${resId}&tableName=${level1}`);
};
export {
  getFlowById,
  getFullSearchList,
  getProjectList,
  getFlowList,
  getProjectById,
  addProject,
  updateProject,
  deleteProject,
  exportFlows,
  prePublishFlows,
  publishFlows,
  cancelPubFlows,
  preOnlineFlows,
  onlineFlows,
  offlineFlows,
  getProjectsByOrgId,
  addFlow,
  updateFlow,
  moveFlow,
  copyFlow,
  deleteFlow,
  getComponentDataGlobalRef,
  getFlowListBy,
  getHiveInfo
};
