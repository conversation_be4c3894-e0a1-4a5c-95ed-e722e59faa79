export default {};
export interface FormData {
  computationalEngine: string;
  serviceAddress: string;
  serviceId: string;
  namespace: string;
  dimensionality: string;
  dimensionalValue: string;
  deadline: string;
  unit: string;
  indicatorConfiguration: ConditionItem[];
  batchSize: number;
  timeout: number;
  pushFields: string;
  keyByFields: any[];
  resultFields: string;
  scriptList: string[];
  pushOrQueryKey: string;
}
export interface ConditionItem {
  indicatorId: string;
  indicatorName: string;
  udf: string;
  deadline: string;
  timeToRollForward: number;
  unit: string;
}
