<template>
  <pro-grid v-loading="loading" type="info" :title="$t('pa.clusterResourceUsage')">
    <!-- body -->
    <div class="use-info-body">
      <!-- left -->
      <div class="use-info-left">
        <bs-table
          v-for="(it, $index) in renderList"
          :key="clusterType + $index"
          :column-data="it"
          :data="getData(it)"
          :column-settings="false"
        />
      </div>
      <div class="use-info-right">
        <flink-pie-chart
          v-show="showSlots"
          name="Slots"
          :title="$t('pa.orgSlots')"
          :pie-data="slotsData"
          :legend-data="[$t('pa.orgUsed'), $t('pa.orgAllocated'), $t('pa.orgSurplus')]"
        />
        <flink-pie-chart
          v-show="showMemory"
          :name="$t('pa.home.memory')"
          :title="$t('pa.home.memory')"
          :pie-data="memoryData"
          :legend-data="[$t('pa.orgAllocated'), $t('pa.orgUsed'), $t('pa.orgSurplus')]"
        />
        <flink-pie-chart
          v-show="showMemory"
          name="CPU"
          :title="$t('pa.orgCPU')"
          :pie-data="cpuData"
          :legend-data="[$t('pa.orgAllocated'), $t('pa.orgUsed'), $t('pa.orgSurplus')]"
        />
      </div>
    </div>
  </pro-grid>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getFlinkUseInfo } from '@/apis/serviceApi';

@Component({
  components: { FlinkPieChart: () => import('../components/flink-pie-chart.vue') }
})
export default class FlinkUseInfo extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: () => ({}) }) params!: any;

  loading = false;
  id = '';
  slotsData: any[] = [];
  memoryData: any[] = [];
  cpuData: any[] = [];

  clusterType = 'YARN_PER_JOB';
  useInfo: any = {};

  get showMemory() {
    return ['YARN_PER_JOB', 'YARN_APPLICATION'].includes(this.clusterType);
  }
  get showSlots() {
    return ['STANDALONE', 'YARN_SESSION'].includes(this.clusterType);
  }
  get renderList() {
    return [
      this.showMemory && [
        {
          label: this.$t('pa.clusterMemory'),
          value: 'clusterMemory',
          unit: 'MB'
        },
        {
          label: this.$t('pa.clusterUsedMemory'),
          value: 'clusterUsedMemory',
          unit: 'MB'
        },

        {
          label: this.$t('pa.clusterRemainingMemory'),
          value: 'clusterResidualMemory',
          unit: 'MB'
        },

        {
          label: this.$t('pa.clusterResidualMemory'),
          value: 'clusterCpu'
        },

        {
          label: this.$t('pa.clusterUsedCpu'),
          value: 'clusterUsedCpu'
        },

        {
          label: this.$t('pa.clusterResidualCpu'),
          value: 'clusterResidualCpu'
        }
      ],
      this.showMemory && [
        {
          label: this.$t('pa.orgMemory'),
          value: 'orgMemory',
          unit: 'MB'
        },

        {
          label: this.$t('pa.orgUsedMemory'),
          value: 'orgUsedMemory',
          unit: 'MB'
        },

        {
          label: this.$t('pa.orgResidualMemory'),
          value: 'orgResidualMemory',
          unit: 'MB'
        },

        {
          label: this.$t('pa.orgChildrenMemory'),
          value: 'orgChildrenMemory',
          unit: 'MB'
        },

        {
          label: this.$t('pa.orgCpu'),
          value: 'orgCpu'
        },

        {
          label: this.$t('pa.orgUsedCpu'),
          value: 'orgUsedCpu'
        },
        {
          label: this.$t('pa.orgResidualCpu'),
          value: 'orgResidualCpu'
        },
        {
          label: this.$t('pa.orgChildrenCpu'),
          value: 'orgChildrenCpu'
        }
      ],
      this.showSlots && [
        {
          label: this.$t('pa.clusterSlots'),
          value: 'clusterSlots'
        },
        {
          label: this.$t('pa.clusterUsedSlots'),
          value: 'clusterUsedSlots'
        },
        {
          label: this.$t('pa.clusterResidualSlots'),
          value: 'clusterResidualSlots'
        },
        {
          label: this.$t('pa.taskManage'),
          value: 'taskManage'
        }
      ],
      this.showSlots && [
        {
          label: this.$t('pa.orgAvailableSlots'),
          value: 'orgSlots'
        },
        {
          label: this.$t('pa.orgUsedSlots'),
          value: 'orgUsedSlots'
        },
        {
          label: this.$t('pa.orgResidualSlots'),
          value: 'orgResidualSlots'
        },
        {
          label: this.$t('pa.orgChildrenSlots'),
          value: 'orgChildrenSlots'
        }
      ]
    ].filter(Boolean);
  }

  created() {
    this.id = this.$route.query.id as string;
    const { clusterType } = JSON.parse(this.data.resProperty);
    this.clusterType = clusterType;
    this.getUseInfo();
  }
  async getUseInfo() {
    try {
      this.loading = true;
      const { success, data, error } = await getFlinkUseInfo(this.id);
      if (!success) return this.$message.error(error);
      this.useInfo = data;
      this.slotsData = [
        {
          name: this.$t('pa.orgUsed'),
          value: this.useInfo.orgUsedSlots
        },
        {
          name: this.$t('pa.orgAllocated'),
          value: this.useInfo.orgChildrenSlots
        },
        {
          name: this.$t('pa.orgSurplus'),
          value: this.useInfo.orgResidualSlots
        }
      ];
      this.memoryData = [
        {
          name: this.$t('pa.orgAllocated'),
          value: this.useInfo.orgChildrenMemory
        },
        {
          name: this.$t('pa.orgUsed'),
          value: this.useInfo.orgUsedMemory
        },
        {
          name: this.$t('pa.orgSurplus'),
          value: this.useInfo.orgResidualMemory
        }
      ];
      this.cpuData = [
        {
          name: this.$t('pa.orgAllocated'),
          value: this.useInfo.orgChildrenCpu
        },
        {
          name: this.$t('pa.orgUsed'),
          value: this.useInfo.orgUsedCpu
        },
        {
          name: this.$t('pa.orgSurplus'),
          value: this.useInfo.orgResidualCpu
        }
      ];
    } finally {
      this.loading = false;
    }
  }
  // 生成表格数据
  getData(it: any) {
    return [
      it.reduce((res: any, v: any) => {
        res[v.value] = `${this.useInfo[v.value]}${v.unit || ''}`;
        return res;
      }, {})
    ];
  }
}
</script>
<style lang="scss" scoped>
::v-deep .use-info__label {
  color: #444;
}
.use-info {
  &-body {
    width: 100%;
    overflow: hidden;
  }
  &-left {
    width: 100%;
    .bs-table {
      width: 100%;
      margin-bottom: 20px;
    }
  }
  &-right {
    display: flex;
  }
}
</style>
