<template>
  <pro-grid title="基本信息" type="info">
    <!-- 主体内容 -->
    <el-form
      ref="formRef"
      :rules="rules"
      :model="formData"
      class="base-form"
      :label-width="labelWidth"
      label-position="right"
    >
      <template v-for="el in renderList">
        <el-form-item
          v-if="Array.isArray(el.children) && !el.hidden"
          :key="el.name"
          :label="el.label"
        >
          <div
            class="base-form__content base-form__content--two"
            :class="{ 'base-form__content--tooltip': el.tooltip }"
          >
            <form-item
              v-for="item in el.children"
              :key="item.name"
              :config="item"
              :label-width="'0px'"
              :form-data="formData.maximumAllowableTimeInterval"
            />
          </div>
          <!-- 提示 -->
          <el-tooltip v-if="el.tooltip" effect="light" :content="el.tooltip" placement="bottom">
            <i class="iconfont icon-wenhao"></i>
          </el-tooltip>
        </el-form-item>
        <form-item v-else :key="el.name" :form-data="formData" :config="el" />
      </template>
    </el-form>
  </pro-grid>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import type { BaseInfo, BaseInfoRenderItem } from '../../type';
import { generateBaseInfo, hide } from '../../util';
import FormItem from '../component/form-item.vue';
import elForm from 'bs-ui-pro/packages/form/index.js';

@Component({ components: { FormItem }, directives: { hide } })
export default class BaseConfig extends Vue {
  @Prop({ default: false }) isEdit!: boolean;
  @Prop({ default: false }) disabled!: boolean;
  @PropSync('data', { default: () => generateBaseInfo() }) formData!: BaseInfo;
  @Ref('formRef') readonly form!: elForm;
  private labelWidth = '100px';
  private rules: any = {
    patternName: [
      {
        required: true,
        message: '请输入模式名称',
        trigger: 'blur'
      },
      {
        min: 1,
        max: 30,
        message: '模式名称的长度在1到30个字符',
        trigger: 'blur'
      }
    ]
  };
  private renderList: BaseInfoRenderItem[] = [
    {
      label: '模板名称',
      name: 'patternName',
      type: 'input',
      maxlength: 30,
      minlength: 1,
      placeholder: '请输入模板名称',
      disabled: this.isEdit
    },
    {
      label: '备注',
      name: 'memo',
      type: 'textarea',
      autosize: { minRows: 4, maxRows: 7 },
      placeholder: '请输入备注'
    }
  ];
  validate() {
    return this.form.validate();
  }
}
</script>

<style lang="scss" scoped>
::v-deep .base-form {
  &__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    &--tooltip {
      margin-right: 7px;
    }
    &--one {
      > div {
        width: 100%;
      }
    }
    &--two {
      > div {
        width: 48%;
      }
    }
  }
}
.base {
  &-form {
    display: block;
    padding-left: 20px;
    width: 587px;
    box-sizing: border-box;
    ::v-deep .el-form-item__content {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }
}
</style>
