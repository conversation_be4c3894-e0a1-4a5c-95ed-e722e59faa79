<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">资源分配</div>
      <div class="bs-page__header-operation">
        <el-input v-model="searchObj.search" placeholder="输入机构名称" />
        <el-button
          v-if="this.hasFeatureAuthority('PA.ELE.SERVICE.FLINK.ALLOCATE')"
          style="margin-left: 10px"
          type="primary"
          @click="allocate"
        >
          分配
        </el-button>
      </div>
    </div>
    <div class="tab-content" :style="{ height: myTableData.tableData.length ? height : '180px' }">
      <base-table
        v-loading="nodeTableLoading"
        :height="'calc(100% - 59px)'"
        :table-data="myTableData"
        :table-config="nodeTableConfig"
        @handleSelectionChange="handleSelectionChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>
    <allocate
      :visible="dialogVisible"
      :win-width="winWidth"
      :is-per-job="isPerJob"
      :org-id="resRecord.orgId"
      :select-orgs="selectOrgs"
      :to-queue-org-id="toQueueOrgId"
      @close="closeDialog"
    />
  </div>
</template>
<script lang="ts">
import { Component, Inject, Watch } from 'vue-property-decorator';
import { URL_FLINK_CHILDRENORGRESLIST } from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {
    'base-table': () => import('@/components/base-table.vue'),
    allocate: () => import('../modals/allocate.vue')
  }
})
export default class FlinkResAllocate extends PaBase {
  height = '458px';
  resRecord: any = {};
  dialogVisible = false;

  private searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: {}
  };
  private myTableData: ITableData = {
    columnData: [],
    tableData: [],
    pageData: {
      pageSize: 10,
      currentPage: 1,
      total: 0
    }
  };
  allTableData: any = [];
  private nodeTableData: ITableData = {
    columnData: [],
    tableData: []
  };
  private nodeTableConfig: ITableConfig = {
    width: 200,
    type: 'selection',
    columnsExtend: {
      edit: []
    }
  };
  private nodeTableLoading = false;
  private selections: any = [];
  private isPerJob = false;
  private winWidth = '';
  private selectOrgs: any = [];
  private toQueueOrgId = '';
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  created() {
    this.loadData(this.comDetailRecord.val || {});
  }
  allocate() {
    if (this.selections.length === 0) {
      this.$message.warning('请选择记录');
      return;
    }
    const resProperty = JSON.parse(this.resRecord.resProperty);
    this.isPerJob = ['YARN_PER_JOB', 'YARN_APPLICATION'].includes(resProperty.clusterType);
    this.winWidth = this.isPerJob ? '70%' : '50%';
    this.dialogVisible = true;
    this.selectOrgs = _.cloneDeep(this.selections);
  }
  closeDialog(needFresh) {
    if (needFresh === true) {
      this.getListData();
    }
    this.dialogVisible = false;
  }
  getListData() {
    this.nodeTableLoading = true;
    this.doPost(URL_FLINK_CHILDRENORGRESLIST + '?clusterId=' + this.resRecord.id, {}).then(
      (resp: any) => {
        this.parseResponse(resp, () => {
          if (!resp.data.tableData) {
            resp.data.tableData = [];
          }
          this.allTableData = resp.data.tableData;
          this.nodeTableData = {
            ...resp.data
          };
          this.myTableData = {
            columnData: resp.data.columnData,
            tableData: resp.data.tableData.slice(0, 10),
            pageData: {
              currentPage: 1,
              pageSize: 10,
              total: resp.data.tableData.length
            }
          };
        });
        this.nodeTableLoading = false;
      }
    );
  }
  async loadData(data: any) {
    this.resRecord = data;
    this.toQueueOrgId = this.resRecord.orgId;
    this.getListData();
  }

  handleSelectionChange(val) {
    this.selections = val;
  }

  handleCurrentChange(val) {
    if (this.myTableData.pageData) {
      this.myTableData.pageData.currentPage = val;
    }
    this.myTableData.tableData = this.allTableData.slice(10 * (val - 1), 10 * val);
  }

  @Watch('searchObj.search')
  watchSearch(val) {
    if (val !== '') {
      this.myTableData.tableData = [];
      const filterData = this.nodeTableData.tableData.filter((n) => {
        // if (n.orgName.indexOf(val) >= 0) {
        //   this.myTableData.tableData.push(_.cloneDeep(n));
        // }
        return n.orgName.indexOf(val) >= 0;
      });
      this.allTableData = _.cloneDeep(filterData);
      this.myTableData.tableData = this.allTableData.slice(0, 10);
      this.myTableData.pageData = {
        currentPage: 1,
        pageSize: 10,
        total: this.allTableData.length
      };
    } else {
      this.myTableData = _.cloneDeep(
        Object.assign(this.nodeTableData, {
          pageData: {
            currentPage: 1,
            pageSize: 10,
            total: this.nodeTableData.tableData.length
          }
        })
      );
      this.allTableData = _.cloneDeep(this.nodeTableData);
    }
  }
}
</script>
<style scoped></style>
