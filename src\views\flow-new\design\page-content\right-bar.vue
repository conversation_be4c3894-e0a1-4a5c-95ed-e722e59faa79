<template>
  <div class="rightBar__container">
    <template v-for="el in actionList">
      <div
        v-if="el.show"
        :key="el.value"
        :class="{
          'rightBar-item': true,
          'rightBar-item--hightLight': el.hightLight,
          'rightBar-item--dsiabled': el.dsiabled
        }"
        @click="handler(el.value, el.dsiabled)"
      >
        {{ el.label }}
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { hasPermission } from '@/utils';
import { ContentType } from '../interface';

@Component
export default class RightBar extends Vue {
  @Prop({ default: '' }) flowId!: string;
  @Prop({ default: 'DEV' }) flowStatus!: string;
  @Prop({ default: false }) showConfig!: boolean;
  @Prop({ default: false }) showCompile!: boolean;
  @Prop({ default: false }) fromMonitorFlow!: boolean;
  @PropSync('showTest', { default: false }) test!: boolean;
  @PropSync('showCompConfig', { default: false }) compConfig!: boolean;
  @PropSync('showResoConfig', { default: false }) resoConfig!: boolean;
  @PropSync('currentTab', { default: 'CANVAS' }) tabName!: string;

  get actionList() {
    return [
      {
        icon: 'iconfont icon-huabu',
        label: '画布',
        value: 'CANVAS',
        show: this.tabName !== 'CANVAS'
      },
      {
        icon: 'iconfont icon-baocun',
        label: '配置',
        value: 'config',
        show:
          this.tabName === 'CANVAS' &&
          hasPermission('PA.FLOW.FLOW_MGR.CONF') &&
          !this.fromMonitorFlow,
        hightLight: this.compConfig || this.resoConfig
      },
      {
        icon: 'iconfont icon-yujing',
        label: '预警',
        value: 'WARNING',
        show: hasPermission('PA.FLOW.FLOW_MGR.WARN'),
        hightLight: this.tabName === 'WARNING'
      },
      {
        icon: 'iconfont icon-lishiyunhang',
        label: '历史运行',
        value: 'HISTORY',
        show: hasPermission('PA.FLOW.FLOW_MGR.HISTORY'),
        hightLight: this.tabName === 'HISTORY'
      },
      {
        icon: 'iconfont icon-banben',
        label: '版本',
        value: 'VERSION',
        show: hasPermission('PA.FLOW.FLOW_MGR.VERSION'),
        hightLight: this.tabName === 'VERSION'
      },
      {
        icon: 'iconfont icon-rizhi',
        label: '日志',
        value: 'LOG',
        show: hasPermission('PA.FLOW.FLOW_MGR.LOG'),
        hightLight: this.tabName === 'LOG'
      },
      {
        icon: 'iconfont icon-yuandaima',
        label: '源码',
        value: 'CODE',
        show: hasPermission('PA.FLOW.FLOW_MGR.SOURCE'),
        hightLight: this.tabName === 'CODE'
      }
    ];
  }
  handler(value, dsiabled = false) {
    if (dsiabled) return;
    this.test = this.compConfig = this.resoConfig = false;
    switch (value) {
      case 'config':
        this.$emit('config');
        break;
      case 'CANVAS':
        this.tabName = ContentType.CANVAS;
        break;
      case 'WARNING':
        this.tabName = ContentType.WARNING;
        break;
      case 'HISTORY':
        this.tabName = ContentType.HISTORY;
        break;
      case 'VERSION':
        this.tabName = ContentType.VERSION;
        break;
      case 'LOG':
        this.tabName = ContentType.LOG;
        break;
      case 'CODE':
        this.handleCode();
        break;
    }
  }
  async handleCode() {
    (this.$parent as any).loading = true;
    const result = await (this.$parent as any)?.checkFlow('code');
    (this.$parent as any).loading = false;
    result && (this.tabName = ContentType.CODE);
  }
}
</script>

<style lang="scss" scoped>
$Width: 50px;
.rightBar {
  &__container {
    width: $Width;
    height: 100%;
    border-left: 1px solid #f1f1f1;
    font-size: 14px;
    font-weight: 400;
    color: #777777;
    line-height: 18px;
  }
  &-item {
    margin: 0 auto;
    padding: 14px 0;
    border-bottom: 1px solid #f1f1f1;
    writing-mode: vertical-rl;
    cursor: pointer;
    &:nth-last-child(1) {
      border-bottom: 0;
    }
    &:hover {
      font-weight: 500;
      color: #377cff;
    }
    &--hightLight {
      font-weight: 500;
      color: #377cff;
    }
    &--dsiabled {
      color: #c8c8c8;
      cursor: not-allowed;
      &:hover {
        font-weight: 400;
        color: #c8c8c8;
      }
    }
  }
}
</style>
