<template>
  <div class="rightBar__container" :class="{ 'rightBar__container--us': isEn }">
    <template v-for="el in actionList">
      <div
        v-if="el.show"
        :key="el.value"
        :class="{
          'rightBar-item': true,
          'rightBar-item--hightLight': el.hightLight,
          'rightBar-item--dsiabled': el.dsiabled
        }"
        @click="handler(el.value, el.dsiabled)"
      >
        {{ el.label }}
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Inject, Prop, PropSync, Vue } from 'vue-property-decorator';
import { hasPermission, isFlinkSql } from '@/utils';
import { ContentType } from '../interface';

@Component
export default class RightBar extends Vue {
  @Prop({ default: '' }) flowId!: string;
  @Prop({ default: '' }) flowType!: string;
  @Prop({ default: 'DEV' }) flowStatus!: string;
  @Prop({ default: false }) showConfig!: boolean;
  @Prop({ default: false }) showSession!: boolean;
  @Prop({ default: false }) fromMonitorFlow!: boolean;
  @Prop({ default: false }) showSnippetLibrary!: boolean;
  @PropSync('showTest', { default: false }) test!: boolean;
  @PropSync('showCompConfig', { default: false }) compConfig!: boolean;
  @PropSync('showResoConfig', { default: false }) resoConfig!: boolean;
  @PropSync('currentTab', { default: 'CANVAS' }) tabName!: string;
  @Inject('enableSql') enableSql;

  /* 是否为流程监控 */
  get isMonitor() {
    return this.$route.name === 'flowMonitorDetail';
  }
  //  流程类型：自定义JAR包
  get isJar() {
    return this.flowType === 'UDJ';
  }

  get actionList() {
    return [
      {
        icon: 'iconfont icon-huabu',
        label: isFlinkSql(this.flowType) || this.isJar ? this.$t('pa.flow.editContent') : this.$t('pa.flow.canvas'), // sql或jar类型流程展示编辑区
        value: 'CANVAS',
        show: this.tabName !== 'CANVAS'
      },
      {
        icon: 'iconfont icon-baocun',
        label: this.$t('pa.flow.config1'),
        value: 'config',
        show: this.tabName === 'CANVAS' && hasPermission('PA.FLOW.FLOW_MGR.CONF') && !this.fromMonitorFlow,
        hightLight: this.compConfig || this.resoConfig
      },
      {
        icon: 'iconfont icon-yujing',
        label: this.$t('pa.flow.session'),
        value: 'SESSION',
        show: this.enableSql && hasPermission('PA.FLOW.FLOW_MGR.SESSION') && isFlinkSql(this.flowType),
        hightLight: this.showSession
      },
      {
        icon: 'iconfont icon-yujing',
        label: this.$t('pa.flow.warnning'),
        value: 'WARNING',
        show: !this.isJar ? hasPermission('PA.FLOW.FLOW_MGR.WARN') : false,
        hightLight: this.tabName === 'WARNING'
      },
      {
        icon: 'iconfont icon-yujing',
        label: this.$t('pa.flow.codeLibrary'),
        value: 'SNIPPET',
        show: this.enableSql && hasPermission('PA.FLOW.FLOW_MGR.SQL_CODE') && isFlinkSql(this.flowType) && !this.isMonitor,
        hightLight: this.showSnippetLibrary
      },
      {
        icon: 'iconfont icon-lishiyunhang',
        label: this.$t('pa.flow.historyRun1'),
        value: 'HISTORY',
        show: hasPermission('PA.FLOW.FLOW_MGR.HISTORY'),
        hightLight: this.tabName === 'HISTORY'
      },
      {
        icon: 'iconfont icon-banben',
        label: this.$t('pa.flow.version1'),
        value: 'VERSION',
        show: hasPermission('PA.FLOW.FLOW_MGR.VERSION'),
        hightLight: this.tabName === 'VERSION'
      },
      {
        icon: 'iconfont icon-rizhi',
        label: this.$t('pa.flow.log'),
        value: 'LOG',
        show: !this.isJar ? hasPermission('PA.FLOW.FLOW_MGR.LOG') : false,
        hightLight: this.tabName === 'LOG'
      },
      {
        icon: 'iconfont icon-yuandaima',
        label: this.$t('pa.flow.code'),
        value: 'CODE',
        show: !this.isJar ? hasPermission('PA.FLOW.FLOW_MGR.SOURCE') && !isFlinkSql(this.flowType) : false,
        hightLight: this.tabName === 'CODE'
      }
    ];
  }

  handler(value, dsiabled = false) {
    if (dsiabled) return;
    this.test = this.compConfig = this.resoConfig = false;
    switch (value) {
      case 'config':
        this.$emit('config');
        break;
      case 'CANVAS':
        this.tabName = ContentType.CANVAS;
        break;
      case 'SESSION':
        this.tabName = ContentType.CANVAS;
        this.$emit('session');
        break;
      case 'SNIPPET':
        this.$emit('snippet-library');
        break;
      case 'WARNING':
        this.tabName = ContentType.WARNING;
        break;
      case 'HISTORY':
        this.tabName = ContentType.HISTORY;
        break;
      case 'VERSION':
        this.tabName = ContentType.VERSION;
        break;
      case 'LOG':
        this.tabName = ContentType.LOG;
        break;
      case 'CODE':
        this.handleCode();
        break;
    }
  }
  async handleCode() {
    this.tabName = ContentType.CODE;
  }
}
</script>

<style lang="scss" scoped>
$Width: 50px;
.rightBar {
  &__container {
    width: $Width;
    height: 100%;
    border-left: 1px solid #f1f1f1;
    font-size: 14px;
    font-weight: 400;
    color: #777777;
    line-height: 18px;
    &--us {
      overflow: scroll;
    }
  }
  &-item {
    margin: 0 auto;
    padding: 14px 0;
    border-bottom: 1px solid #f1f1f1;
    writing-mode: vertical-rl;
    cursor: pointer;
    &:nth-last-child(1) {
      border-bottom: 0;
    }
    &:hover {
      font-weight: 500;
      color: #377cff;
    }
    &--hightLight {
      font-weight: 500;
      color: #377cff;
    }
    &--dsiabled {
      color: #c8c8c8;
      cursor: not-allowed;
      &:hover {
        font-weight: 400;
        color: #c8c8c8;
      }
    }
  }
}
</style>
