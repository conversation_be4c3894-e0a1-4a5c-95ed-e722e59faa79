<template>
  <bs-dialog
    :width="width"
    :title="title"
    :visible.sync="display"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    @close="closeDialog"
    @confirm="submit"
  >
    <div class="sql-processing__udf">
      <div class="sql-processing__udf--select">
        <span>请选择UDF：</span>
        <el-select
          v-model="udf"
          multiple
          placeholder="请选择"
          style="width: 280px; margin-left: 20px"
          filterable
          collapse-tags
          @change="changeLabel"
        >
          <el-option
            v-for="item in options"
            :key="item.udfId"
            :label="item.udfName"
            :value="item.udfId"
          />
        </el-select>
      </div>

      <!-- 代码编辑器 -->
      <flink-sql-editor
        :code="sql"
        :origin-code="sql"
        :height="editorHeight"
        :options="editorOptions"
        @saveCode="handleSaveCode"
      />
      <!-- 输出视图 -->
      <div class="sql-processing__view">
        <el-button type="primary" :disabled="disabled" @click="getOutputView">输出视图</el-button>
        <!-- tips -->
        <el-tooltip effect="light" :content="tips" placement="bottom">
          <i class="iconfont icon-wenhao sql-processing__view__icon"></i>
        </el-tooltip>
      </div>
      <!-- 视图 -->
      <el-table
        ref="table"
        class="sql-processing-table"
        stripe
        border
        size="mini"
        :data="viewList"
        :height="tableHeight"
        @selection-change="handleSelectionChange"
      >
        <!-- 选择框 -->
        <el-table-column v-if="!disabled" type="selection" align="center" width="150" />
        <!-- 视图名 -->
        <el-table-column align="center" label="视图名" prop="name" />
      </el-table>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import { cloneDeep } from 'lodash';
import { post } from '@/apis/utils/net';
import { URL_GET_ANALYSIS_VIEW, URL_UDF_USELIST } from '@/apis/commonApi';
import FlinkSqlEditor from '@/components/flink-sql-editor/index.vue';
import Table from 'bs-ui-pro/lib/table';

@Component({
  components: { FlinkSqlEditor }
})
export default class SqlProcessing extends Vue {
  @Prop() data!: any;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @PropSync('show', { type: Boolean, default: false }) display!: boolean;
  @Ref('table') readonly table!: Table; // 表格ref
  private width = '50%';
  private title = 'sql处理';
  private editorHeight = '300px'; // 编辑器高度
  private editorOptions = {
    mode: 'text/flink-sql',
    indentUnit: 2,
    smartIndent: true,
    lineNumbers: true,
    showCursorWhenSelecting: true,
    lineWiseCopyCut: true,
    autofocus: true,
    lineWrapping: true,
    foldGutter: true,
    readOnly: false,
    hintOptions: { tables: {} }
  }; // 编辑器配置
  private tips = '根据模版CREATE TEMPORARY VIEW ...智能解析出视图信息'; // 提示信息
  private tableHeight = 200; // 表格高度
  private sql = ''; // sql语句
  private viewList: any[] = []; // 视图列表
  private outputFields: any[] = []; // 输出信息
  private options: any[] = []; // udf下拉列表
  private udf: any = []; // udf
  private udfSelect: any = [];
  private tables: any = {}; // hintOptions tables
  async created() {
    const { properties = {}, outputFields = [], inputFields = [] } = this.data;

    if (Array.isArray(inputFields)) {
      this.tables = inputFields.reduce((pre: any, { name, fields }: any) => {
        if (Array.isArray(fields) && name) {
          pre[name] = fields;
        }
        return pre;
      }, {});
      this.$set(this.editorOptions.hintOptions, 'tables', this.tables);
    }
    // 获取udf 列表
    const { error, data, success }: any = await post(URL_UDF_USELIST);
    const udfName: any = [];
    if (success) {
      data.forEach((item) => {
        this.options.push({
          udfName: item.udfName,
          udfId: item.id
        });
      });
      if (properties && properties.udf && properties.udf.length) {
        properties.udf.forEach((element: any) => {
          this.udf.push(element.udfId);
          udfName.push(element.udfName);
        });
        this.udfSelect = cloneDeep(properties.udf);
      }
    } else {
      this.$tip.error(error);
    }
    // 编辑时给udf选中项加上代码补全提示
    if (udfName.length) {
      udfName.forEach((item) => {
        this.tables[item] = [];
      });
      this.$set(this.editorOptions.hintOptions, 'tables', this.tables);
    }
    this.sql =
      properties && properties.sql
        ? properties.sql
        : '-- 根据模版编写视图 可被系统智能解析\nCREATE TEMPORARY VIEW IF NOT EXISTS view_name AS \nSELECT ...';
    this.getOutputView(false);
    this.outputFields = Array.isArray(outputFields) ? [...outputFields] : [];
    this.editorOptions.readOnly = this.disabled;
  }

  //  udf选择
  changeLabel(val) {
    this.udfSelect = [];
    const udfName: any = [];
    this.options.forEach((element) => {
      if (val.indexOf(element.udfId) >= 0) {
        this.udfSelect.push(element);
        udfName.push(element.udfName);
      }
    });
    // 给udf列表加上补全提示
    if (udfName.length) {
      udfName.forEach((item) => {
        this.tables[item] = [];
      });
    }
    this.$set(this.editorOptions.hintOptions, 'tables', this.tables);
  }

  /* 获取输出视图 */
  async getOutputView(tip: any) {
    const { sql } = this;
    if (!sql) {
      if (tip) {
        this.$tip.warning('请输入sql语句');
      }
      return;
    }
    const { nodeId } = this.data;
    const { error, data, success }: any = await post(`${URL_GET_ANALYSIS_VIEW}?nodeId=${nodeId}`, {
      sql
    });
    if (success) {
      this.viewList = Array.isArray(data) ? data : [];
      this.$nextTick(() => {
        const { viewList, outputFields } = this;
        viewList.forEach((el) => {
          const item = outputFields.find(({ name }: any) => name === el.name);
          if (item) {
            this.table.toggleRowSelection(el);
          }
        });
      });
      return;
    }
    this.$tip.error(error);
  }

  /* 代码保存 */
  handleSaveCode(code: string) {
    this.sql = code;
  }

  /* table选择事件 */
  handleSelectionChange(data: any) {
    this.outputFields = data.map((item: any) => ({
      ...item,
      outputable: true
    }));
  }

  /* 确定 */
  submit() {
    const { length } = this.outputFields;
    if (length < 1) {
      this.$tip.warning('请至少选择一条视图');
      return;
    }
    this.closeDialog(true);
  }

  /* 取消关闭 */
  @Emit('close')
  private closeDialog(needUpdate) {
    if (needUpdate) {
      const { data, outputFields, sql } = this;
      const jobNode = cloneDeep(data);
      jobNode.outputFields = cloneDeep(outputFields);
      jobNode.properties = { sql, udf: this.udfSelect };
      this.reset();
      return { needUpdate, jobNode };
    }
    this.reset();
  }

  /* reset */
  reset() {
    this.display = false;
    this.viewList = [];
    this.sql = '';
    this.outputFields = [];
  }
}
</script>
<style lang="scss" scoped>
.sql-processing {
  &__udf {
    display: flex;
    flex-direction: column;
    text-align: left;
    margin-bottom: 10px;
    &--select {
      margin-bottom: 10px;
    }
  }

  &__view {
    display: flex;
    align-items: center;
    margin: 10px 0;
    width: 100%;
    text-align: left;

    &__icon {
      font-size: 18px;
      margin-left: 10px;
    }
  }

  &__table {
    margin: 10px 0 0;
  }

  &-table {
    border: 1px solid #ebeef5;
  }
}
</style>
