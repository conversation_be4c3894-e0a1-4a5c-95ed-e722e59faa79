<template>
  <table-block
    :title="$t('pa.taskMgrInfo')"
    :height="height"
    :loading="loading"
    :table-data="tableData"
    :column-data="columnData"
  />
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getTaskMgrInfo } from '@/apis/serviceApi';
import { safeArray, timeFormat } from '@/utils';
@Component({
  components: { TableBlock: () => import('../components/table-block.vue') }
})
export default class FlinkTaskManagerInfo extends Vue {
  @Prop({ default: () => ({}) }) params!: any;

  loading = false;
  id = '';
  tableData: any[] = [];
  columnData: any[] = [
    {
      value: 'id',
      label: 'ID',
      minWidth: '200'
    },
    {
      value: 'timeSinceLastHeartbeat',
      label: this.$t('pa.timeSinceLastHeartbeat'),
      width: '170'
    },
    {
      value: 'slotsNumber',
      label: this.$t('pa.slotsNumber'),
      width: '200'
    },
    {
      value: 'freeSlots',
      label: this.$t('pa.freeSlots'),
      width: '200'
    },
    {
      value: 'cpuCores',
      label: this.$t('pa.cpuCores'),
      width: '200'
    },
    {
      value: 'physicalMemory',
      label: this.$t('pa.physicalMemory')
    },
    {
      value: 'freeMemory',
      label: this.$t('pa.freeMemory')
    },
    {
      value: 'managedMemory',
      label: this.$t('pa.managedMemory'),
      width: '130'
    }
  ];

  get height() {
    return this.params?.height || '300px';
  }
  created() {
    this.id = this.$route.query.id as string;
    this.getTaskMgrData();
  }
  async getTaskMgrData() {
    try {
      this.loading = true;
      const { success, data, error } = await getTaskMgrInfo(this.id);
      if (!success) return this.$message.error(error);
      this.tableData = safeArray(data?.taskmanagers).map((it) => {
        return {
          id: it.id,
          timeSinceLastHeartbeat: timeFormat(it.timeSinceLastHeartbeat),
          slotsNumber: it.slotsNumber,
          freeSlots: it.freeSlots,
          cpuCores: it.hardware.cpuCores,
          physicalMemory: this.calcSize(it.hardware.physicalMemory),
          freeMemory: this.calcSize(it.hardware.freeMemory),
          managedMemory: this.calcSize(it.hardware.managedMemory)
        };
      });
    } finally {
      this.loading = false;
    }
  }
  calcSize(size: number) {
    let unit = 'MB';
    let result = size / 1024 / 1024;
    if (result > 1024) {
      unit = 'GB';
      result = result / 1024;
    }
    // Math.round(result, 2)
    return Math.round(result) + unit;
  }
}
</script>
