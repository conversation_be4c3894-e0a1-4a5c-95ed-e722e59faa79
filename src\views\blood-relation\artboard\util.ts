import uniqueId from 'lodash/uniqueId';

export const mapping = {
  JOB: '流程',
  SERVICE: '资源',
  TABLE: '表',
  VIEW: '视图'
};
export const RuleMapping = {
  up: 'node',
  upFlow: 'node[type="JOB"]',
  upResource: 'node[type="SERVICE"]',
  upTable: 'node[type="TABLE"]',
  upView: 'node[type="VIEW"]',
  down: 'node',
  downFlow: 'node[type="JOB"]',
  downResource: 'node[type="SERVICE"]',
  downTable: 'node[type="TABLE"]',
  downView: 'node[type="VIEW"]'
};

export const selectMapping = {
  upFlow: 'JOB',
  upResource: 'SERVICE',
  upTable: 'TABLE',
  upView: 'VIEW',
  downFlow: 'JOB',
  downResource: 'SERVICE',
  downTable: 'TABLE',
  downView: 'VIEW'
};

export const isValidArray = (o) => Array.isArray(o) && o.length > 0;
export const validArray = (o) => (isValidArray(o) ? o.filter(Boolean) : []);

export const generateComponentType = (type: string) => {
  if (type.includes('up')) return 'SOURCE';
  if (type.includes('down')) return 'SINK';
  return 'SOURCE,SINK';
};

export const generateParams = (type: string, data: any = {}) => {
  if (type === 'getHomology') {
    return {
      lineageResType: data.resType,
      namespace: data.nameSpace,
      serviceHost: data.serviceHost,
      serviceId: data.serviceId,
      resourceCsv: data.resourceCsv || data.resourceList
    };
  }
  if (type === 'head') {
    return {
      serviceId: data.serviceId,
      resType: data.resType,
      resourceCsv: data.resourceList
    };
  }
  return {
    componentType: generateComponentType(type),
    selectType: selectMapping[type] ? selectMapping[type] : 'JOB,SERVICE,TABLE,VIEW',
    isDynamic: data.isDynamic,
    serviceId: data.serviceId,
    serviceType: data.resType,
    resourceCsv: data.resourceCsv || data.resourceList || '无',
    relatedInformation: data.relatedInformation
  };
};

export const isDynamicNode = (type: string) => ['SERVICE', 'TABLE'].includes(type);

export const uuid = () => {
  const url = URL.createObjectURL(new Blob([]));
  URL.revokeObjectURL(url);
  return url.substring(url.lastIndexOf('/') + 1);
};
export const generateFileName = (ext?: string) => (ext ? `${uuid()}.${ext}` : uuid());

export const downloadFile = (file: any = new Blob([]), fileName = '未知文件') => {
  const el = document.createElement('a');
  el.download = fileName;
  el.style.display = 'none';
  el.href = URL.createObjectURL(file);
  document.body.appendChild(el);
  el.click();
  URL.revokeObjectURL(el.href);
  document.body.removeChild(el);
};

export const filterAll = (arr: any = [], callBack) => {
  return arr.reduce(
    (pre: any[], next) => {
      pre[callBack(next) ? '0' : '1'].push(next);
      return pre;
    },
    [[], []]
  );
};

export const generateParentNode = () => ({ classes: ['parent'], data: { id: uniqueId() } });
export const generateNodeInfo = (data: any) => ({
  id: data.id,
  resType: data.resType,
  isDynamic: data.isDynamic,
  nameSpace: data.nameSpace,
  serviceId: data.serviceId,
  serviceType: data.serviceType,
  resourceCsv: encodeURIComponent(data.resourceList),
  serviceHost: data.serviceHost,
  relatedInformation: encodeURIComponent(data.relatedInformation)
});
export const generateEdge = (data: any, showHomologyNodes) => ({
  group: 'edges',
  classes: [showHomologyNodes ? 'label' : ''],
  data: {
    id: `${data.sourceId}_${data.targetId}`,
    source: data.sourceId,
    target: data.targetId,
    label: data.sideInformation,
    type: 'normal'
  }
});
export const generateEdge1 = (data: any, id: any, parent = null) => ({
  group: 'edges',
  classes: ['homology', parent],
  data: {
    id: `${id}_${data.id}`,
    source: id,
    target: data.id,
    label: data.sideInformation,
    type: 'homology'
  }
});
export const generateEdgeInfo = (data: any) => ({
  id: data.id,
  source: { id: data.sourceId },
  target: { id: data.targetId }
});

// attribute
export const generateNode = (data: any, showLable = false, parent = null, isHomology = false) => {
  const tags = [data.isDynamic && !isHomology ? '动' : ''];
  const name = mapping[data.resType] || '';
  return {
    group: 'nodes',
    classes: [
      showLable ? 'label' : '',
      data.resType,
      data.serviceType,
      isHomology ? 'homology' : '',
      isHomology ? parent : ''
    ],
    data: {
      id: data.id,
      tags,
      type: data.resType,
      name,
      label: data.resType === 'JOB' ? `${data.projectName}-${data.jobName}` : data.serviceName,
      parent,
      attribute: {
        isNormal: true, // 普通节点
        isDynamic: data.isDynamic, // 动态节点
        isHomologyMaster: isDynamicNode(data.resType), // 同源主节点
        isHomology // 同源节点
      },
      showHomology: false, // 显示同源
      dom: generateNodeContainer({ id: data.id, tags, name })
    },
    renderedPosition: 'rp'
  };
};
export const generateNodeContainer = ({ id = '', tags = [], name = '' }: any) => {
  const div = document.createElement('div');
  div.classList.add('Node__container');
  div.setAttribute('id', id);
  generateNodeContent(div, tags, name);
  return div;
};
export const generateNodeContent = (parent, tags = [], name = '') => {
  tags.filter(Boolean).forEach((el, index) => {
    const tag = document.createElement('div');
    tag.classList.add('Node-tag');
    tag.classList.add(`Node-tag--t${index}`);
    const span = document.createElement('span');
    span.classList.add('Node-tag__span');
    span.innerHTML = el;
    tag.appendChild(span);
    parent.appendChild(tag);
  });
  if (name) {
    const dom = document.createElement('div');
    dom.classList.add('Node-name');
    dom.innerHTML = name;
    parent.appendChild(dom);
  }
};
export const dataURLtoBlob = (url) => {
  const arr = url.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]);
  let i = bstr.length;
  const u8arr = new Uint8Array(i);
  while (i--) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};
