<template>
  <bs-dialog
    size="large"
    :title="$t('pa.monitor.warningRule.detail.warningRecord')"
    :visible.sync="display"
    :footer-visible="false"
    class="warn-record-dialog"
    @close="$emit('refresh')"
  >
    <!-- body -->
    <div v-loading="loading" class="warn-record-body">
      <!-- header -->
      <div class="warn-record-header">
        <bs-select
          v-model="stateType"
          size="small"
          class="marR10"
          :class="{ 'warn-record-header-select--us': isEn }"
          :options="stateTypeOptions"
          :placeholder="this.$t('pa.monitor.text16')"
          clearable
          @change="getRecordData(false)"
        />
        <bs-select
          v-model="timeType"
          size="small"
          class="no-right-border-select"
          :class="{ 'no-right-border-select--us': isEn }"
          :options="timeTypeOptions"
          @change="getRecordData(false)"
        />
        <!-- date-picker -->
        <el-date-picker
          v-model="date"
          type="datetimerange"
          size="small"
          class="date-picker"
          :range-separator="$t('pa.flow.zhi')"
          :start-placeholder="$t('pa.flow.startDate')"
          :end-placeholder="$t('pa.flow.endDate')"
          :picker-options="{ shortcuts }"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="getRecordData(false)"
        />
        <el-button v-access="'PA.MONITOR.WARN.RECORD_READ'" size="small" type="primary" @click="handler">
          {{ $t('pa.monitor.warningRule.detail.handle') }}
        </el-button>
        <el-button v-access="'PA.MONITOR.WARN.RECORD_READ'" size="small" type="primary" @click="handlerAll">
          {{ $t('pa.monitor.warningRule.detail.allHandle') }}
        </el-button>
      </div>
      <!-- table -->
      <bs-table
        selection
        :height="height"
        :data="tableData"
        :column-settings="false"
        :column-data="columnData"
        :page-data="params.pageData"
        @refresh="getRecordList(true)"
        @page-change="handlePageChange"
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
      >
        <!-- operator -->
        <template slot="operator" slot-scope="{ row }">
          <el-tooltip :content="$t('pa.action.detail')" effect="light">
            <i class="iconfont icon-chakan" @click="handleViewDetail(row)"></i>
          </el-tooltip>
        </template>
      </bs-table>
      <!-- 详情 -->
      <bs-dialog
        v-if="showDetailDialog"
        size="medium"
        :title="$t('pa.monitor.warningRule.detail.detailInfo')"
        append-to-body
        :visible.sync="showDetailDialog"
        @confirm="handleConfirm"
      >
        <pre class="pre-style">{{ rowInfo }}</pre>
      </bs-dialog>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, PropSync, Prop, Vue } from 'vue-property-decorator';
import { getRecordList, updateState, updateAllState } from '@/apis/warnRuleApi';
import { dayjs, debounce, safeArray, timeFormat } from '@/utils';
@Component
export default class WarnRecordDialog extends Vue {
  @PropSync('show', { required: true, default: false }) display!: boolean;
  @Prop({ default: () => ({}) }) data!: any;

  loading = false;
  date: any[] = [];
  shortcuts: any[] = [
    {
      text: this.$t('pa.monitor.warningRule.detail.lastWeek'),
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        picker.$emit('pick', [start, end]);
      }
    },
    {
      text: this.$t('pa.monitor.warningRule.detail.lastMonth'),
      onClick(picker) {
        const end = new Date();
        const start = dayjs().subtract(1, 'months').toDate();
        picker.$emit('pick', [start, end]);
      }
    },
    {
      text: this.$t('pa.monitor.warningRule.detail.lastThreeMonths'),
      onClick(picker) {
        const end = new Date();
        const start = dayjs().subtract(3, 'months').toDate();
        picker.$emit('pick', [start, end]);
      }
    }
  ];
  params: any = {
    pageData: { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 },
    sortData: { stateType: 'ASC', createTime: 'DESC' }
  };
  columnData: any[] = [];
  tableData: any[] = [];
  checkedRows: any = [];
  enumsDataMap: any = new Map();
  getRecordData: any = debounce(this.getRecordList, 500);
  showDetailDialog = false;
  rowInfo = '';
  rowId = '';
  stateType = '';
  stateTypeOptions: any = [
    {
      label: this.$t('pa.flow.processed'),
      value: 'ON'
    },
    {
      label: this.$t('pa.monitor.text17'),
      value: 'OFF'
    }
  ];
  timeType = 'createTime';
  timeTypeOptions: any = [
    {
      label: this.$t('pa.flow.createTime'),
      value: 'createTime'
    },
    {
      label: this.$t('pa.handleTime'),
      value: 'confirmTime'
    }
  ];

  get height() {
    return this.checkedRows.length ? 'calc(70vh - 285px)' : 'calc(70vh - 210px)';
  }

  created() {
    this.getRecordList();
  }

  async getRecordList(refresh = false) {
    try {
      this.loading = true;
      const query: any = {
        ruleId: this.data.id || '',
        stateType: this.stateType,
        timeType: this.timeType,
        begTime: (this.date && this.date[0]) || '',
        endTime: (this.date && this.date[1]) || ''
      };
      const { success, data, error } = await getRecordList(query, this.params);
      if (!success) return this.$message.error(error);
      this.columnData = safeArray(data?.columnData)
        .map((it) => ({ value: it.prop, ...it }))
        .concat({ label: this.$t('pa.action.action'), value: 'operator', fixed: 'right', width: 80 });
      this.columnData.forEach((it) => {
        it.dataType === 'Enum' && this.enumsDataMap.set(it.prop, it.enumData);
      });
      this.tableData = safeArray(data?.tableData).map((it) => {
        it.createTime = timeFormat(it.createTime);
        it.confirmTime = it.confirmTime ? timeFormat(it.confirmTime) : '';
        it.sendState = this.enumsDataMap.get('sendState')[it.sendState];
        it.stateType = this.enumsDataMap.get('stateType')[it.stateType];
        return it;
      });
      this.params.pageData.total = data?.pageData?.total || 0;
      refresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    } finally {
      this.loading = false;
    }
  }
  async handler() {
    if (this.checkedRows.length < 1) return this.$message.warning(this.$t('pa.monitor.flow.action.leastOne'));
    await this.updateState(this.checkedRows);
    this.getRecordList();
    this.showDetailDialog = false;
  }
  async handlerAll() {
    if (this.data?.recordNoReadCount === 0)
      return this.$message.warning(this.$t('pa.monitor.warningRule.detail.noNeedToBeProcessed'));
    try {
      this.loading = true;
      const { success, error, msg } = await updateAllState(this.data.id);
      if (!success) return this.$message.error(msg || error);
      this.$message.success(msg);
      this.getRecordList();
    } finally {
      this.loading = false;
    }
  }
  handlePageChange(currentPage: number, pageSize: number) {
    this.params.pageData.currentPage = currentPage;
    this.params.pageData.pageSize = pageSize;
    this.getRecordData();
  }
  handleSortChange(value: any) {
    this.params.sortData = value;
    this.getRecordData();
  }
  handleSelectionChange(rows: any[]) {
    this.checkedRows = rows.map(({ id, ruleId }) => ({ id, ruleId, stateType: 'ON' }));
  }
  handleViewDetail(row: any) {
    this.showDetailDialog = true;
    this.rowInfo = row.detailInfo;
    this.rowId = row.id;
  }
  async handleConfirm() {
    await this.updateState([{ id: this.rowId, ruleId: this.data.id, stateType: 'ON' }]);
    this.getRecordList();
    this.showDetailDialog = false;
  }
  async updateState(params: any[]) {
    try {
      this.loading = true;
      const { success, error, msg } = await updateState(params);
      if (!success) return this.$message.error(msg || error);
      this.$message.success(msg);
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style scoped lang="scss">
.warn-record {
  &-dialog {
    ::v-deep .el-dialog__body {
      padding: 0;
    }
    .iconfont {
      cursor: pointer;
    }
  }
  &-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 10px 20px;
    &-select {
      &--us {
        width: 270px;
        ::v-deep .el-select {
          width: 270px;
        }
      }
    }
    .el-date-editor {
      margin-right: 10px;
    }
    .no-right-border-select {
      width: 100px;
      flex-shrink: 0;
      &--us {
        width: 145px;
      }
      ::v-deep {
        .el-input__inner {
          border-top-right-radius: 0px;
          border-bottom-right-radius: 0px;
          border-right: 0px;
          padding-right: 20px;
          padding-left: 10px;
        }
      }
    }
    .date-picker {
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
      ::v-deep .el-range-separator {
        width: 28px;
      }
      ::v-deep .el-range__close-icon {
        display: none;
      }
    }
  }
}
</style>
