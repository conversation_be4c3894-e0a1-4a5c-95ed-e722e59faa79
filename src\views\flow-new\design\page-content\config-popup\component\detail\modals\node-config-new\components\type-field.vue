<template>
  <bs-page-select
    v-model="innerValue"
    size="medium"
    :placeholder="placeholder"
    filterable
    clearable
    :disabled="disabled"
    :page-size="pageSize"
    :request="request"
    :options="options || []"
    @change="handleChange"
  >
    <div slot-scope="{ item }" class="field-option">
      <div>
        <span>{{ item.fieldName || item.label }}</span>
        <bs-tag v-if="item.isPrimaryKey">{{ $t('pa.flow.mainKey') }}</bs-tag>
        <bs-tag v-if="item.isPartition" color="green">{{ $t('pa.flow.fenqu') }}</bs-tag>
      </div>
      <span class="field-option__type">{{ item.fieldType }}</span>
    </div>
  </bs-page-select>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { getRequestData, runRequest } from '../utils';

// 存储流程报错信息
@Component({ name: 'TypeField' })
export default class TypeField extends Vue {
  // 表单配置信息
  @Prop() value!: string[];
  @Prop() config!: any;
  @Prop() disabled!: boolean;
  @Prop() formData!: any;
  get placeholder() {
    return (this.config.componentProps || {}).placeholder;
  }
  get pageSize() {
    return (this.config.componentProps || {}).pageSize || 10;
  }
  get options() {
    return [{ fieldName: '1', value: 'ss', isPrimaryKey: true, isPartition: true, fieldType: 'String' }];
  }
  get orgId() {
    return this.$store.getters.orgId;
  }
  innerValue = this.value || [];
  async request() {
    const { data } = await runRequest(this.config.request, Object.assign(this.formData || {}, { orgId: this.orgId }));
    // 此处需要将后端固定格式进行转译
    // { fieldName: '1', value: 'ss', isPrimaryKey: true, isPartition: true, fieldType: 'String' }
    return { data: getRequestData(data, { value: 'fieldName', label: 'fieldName' }) };
  }
  handleChange(val) {
    this.$emit('change', val);
  }
}
</script>

<style lang="scss" scoped>
.field-option {
  display: inline-flex;
  justify-content: space-between;
  width: calc(100% - 14px);
  padding-left: 10px;
  .bs-tag {
    margin-left: 6px;
  }
  &__type {
    color: $--bs-color-primary;
    font-weight: 700;
  }
}
</style>
