<template>
  <div class="dialog-footer">
    <el-switch v-model="printLog" :disabled="disabled" :inactive-text="$t('pa.flow.logText')" />
    <el-tooltip effect="light" :content="$t('pa.flow.msg316')">
      <i class="iconfont icon-wenhao" :class="{ 'icon-wenhao--us': isEn }"></i>
    </el-tooltip>
  </div>
</template>
<script lang="ts">
import { Component, ModelSync, Prop, Vue } from 'vue-property-decorator';

@Component
export default class PrintLog extends Vue {
  @ModelSync('value', 'input', { type: Boolean }) printLog!: boolean;
  @Prop() disabled!: boolean;
}
</script>
<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  align-items: center;
  .icon-wenhao {
    position: absolute;
    left: 110px;
    &--us {
      left: 130px;
    }
  }
  ::v-deep .el-switch__label {
    margin-right: 30px;
    color: #444;
  }
}
</style>
