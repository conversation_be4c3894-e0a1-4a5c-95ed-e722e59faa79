<template>
  <pro-page title="数据字典" :fixed-header="false" class="dictionary">
    <div slot="operation">
      <el-button type="primary" size="small" @click="refresh">刷新</el-button>
    </div>
    <div class="content">
      <div class="left-content">
        <div class="content-header">
          <bs-search
            v-model="searchObj.search"
            size="small"
            placeholder="请输入编码或名称"
            style="margin-right: 10px"
            @search="fetchList"
          />
          <el-button
            v-if="hasFeatureAuthority('PA.SETTING.DIC.ADD')"
            size="small"
            type="primary"
            @click="add"
          >
            新建
          </el-button>
        </div>
        <div class="content-body">
          <div class="left-table__header">
            <span class="left-table__header-code">编码</span>
            <span class="left-table__header-code">名称</span>
          </div>
          <div v-loading="tableLoading" class="left-table__body">
            <div
              v-for="item in tableData"
              v-show="tableData.length"
              :key="item.id"
              :class="['left-table__item', parentCodeObj.code === item.code ? 'active' : '']"
              @click="rowClick(item)"
            >
              <span class="code" :title="item.code">{{ item.code }}</span>
              <span class="title" :title="item.title">{{ item.title }}</span>
              <div class="operation">
                <el-tooltip content="编辑" effect="light">
                  <i class="iconfont icon-bianji" @click.stop="edit(item)"></i>
                </el-tooltip>
                <el-tooltip content="删除" effect="light">
                  <i class="iconfont icon-shanchu" @click.stop="del(item)"></i>
                </el-tooltip>
              </div>
            </div>
            <div v-if="!tableData.length" class="left-table__body--empty">
              <span class="left-table__body--text">暂无数据</span>
            </div>
          </div>
          <div class="left-table__footer">
            <el-pagination
              layout="prev, pager, next"
              background
              :total="searchObj.pageData.total"
              :page-size="searchObj.pageData.pageSize"
              :current-change="hanlePageChange"
            />
          </div>
        </div>
      </div>
      <div class="right-content">
        <div class="right-content-header">
          <div class="title">
            <span>名称：{{ parentCodeObj.title }}；</span>
            <span>编码：{{ parentCodeObj.code }} </span>
          </div>
          <bs-search
            v-model="subSearchObj.search"
            class="right-content__search"
            placeholder="请输入编码或名称"
            size="small"
            clearable
            @search="fetchList2"
          />
          <el-button
            v-if="hasFeatureAuthority('PA.SETTING.DIC.ADD')"
            class="dictionary-create"
            size="small"
            type="primary"
            @click="addSub"
          >
            新建
          </el-button>
        </div>
        <div class="content-body">
          <div class="base-table-content">
            <bs-table
              v-loading="subTableLoading"
              height="calc(100vh - 284px)"
              expand
              :data="subTableData.tableData"
              :column-data="subTableData.columnData"
              :page-data="subSearchObj.pageData"
              :column-settings="false"
              @page-change="handleCurrentChange2"
              @refresh="getSubListData"
            >
              <template slot="operator" slot-scope="{ row }">
                <el-tooltip
                  v-for="item in subTableConfigButtons"
                  :key="item.label"
                  v-access="item.authCode"
                  :content="item.label"
                  effect="light"
                  class="dictionary-tooltip"
                >
                  <i
                    :class="item.class"
                    class="dictionary-icon"
                    @click="operateHandler(item.event, row)"
                  ></i>
                </el-tooltip>
              </template>
              <template slot="expand" slot-scope="{ row }">
                <el-form label-position="left">
                  <template v-for="item in expandList">
                    <el-form-item v-if="row[item.value]" :key="item.value" :label="item.label">
                      <span>{{ row[item.value] }}</span>
                    </el-form-item>
                  </template>
                </el-form>
              </template>
            </bs-table>
          </div>
        </div>
      </div>
    </div>
    <add-edit
      :visible="dialogVisible2"
      :title="dialogTitle2"
      :data="recordData2"
      :form-loading="formLoading2"
      @close="closeSubDialog"
    />
    <add-edit-parent
      v-if="dialogVisible"
      :visible="dialogVisible"
      :title="dialogTitle"
      :data="recordData"
      :form-loading="formLoading"
      @close="closeDialog"
    />
  </pro-page>
</template>
<script lang="ts">
import { Component } from 'vue-property-decorator';
import { URL_DIC_LIST, URL_DIC_FIND, URL_DIC_DELETE, URL_DIC_SUB_LIST } from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
import { cloneDeep } from 'lodash';
import moment from 'moment';
@Component({
  components: {
    'add-edit': () => import('./modals/add-edit.vue'),
    'add-edit-parent': () => import('./modals/add-edit-parent.vue')
  }
})
export default class Dic extends PaBase {
  formLoading = false;
  dialogVisible = false;
  dialogTitle = '新建';
  recordData: any = {};
  tableLoading = false;
  subTableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: {
      parentCode: 'DESC',
      createTime: 'DESC'
    }
  };
  // 左侧表格列表
  tableData: any[] = [];
  fetchList: any = _.debounce(this.getListData, 500);
  fetchList2: any = _.debounce(this.getSubListData, 500);
  formLoading2 = false;
  dialogVisible2 = false;
  dialogTitle2 = '新建';
  recordData2: any = {};
  parentCodeObj: any = '';
  subSearchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: {}
  };
  // 二级表格信息
  subTableData: ITableData = {
    columnData: [],
    tableData: []
  };
  subTableConfigButtons = [
    {
      label: '编辑',
      class: 'iconfont icon-bianji',
      event: 'editSub',
      authCode: 'PA.SETTING.DIC.EDIT'
    },
    {
      label: '删除',
      class: 'iconfont icon-shanchu',
      event: 'deleteSub',
      authCode: 'PA.SETTING.DIC.DELETE'
    }
  ];
  expandList = [
    {
      label: '值1',
      value: 'value1'
    },
    {
      label: '值2',
      value: 'value2'
    },
    {
      label: '值3',
      value: 'value3'
    },
    {
      label: '值4',
      value: 'value4'
    }
  ];
  created() {
    this.getListData();
  }
  operateHandler(event, row) {
    this[event](row);
  }
  // 左侧分页回调
  hanlePageChange(val) {
    this.searchObj.pageData.currentPage = val;
    this.fetchList();
  }
  // 新建父类
  add() {
    this.dialogVisible = true;
    this.recordData = null;
    this.dialogTitle = '新建分类';
  }
  // 编辑父类
  edit(row: any) {
    this.dialogTitle = '编辑分类';
    this.formLoading = true;
    const params = { id: row.id };
    this.doGet(URL_DIC_FIND, { params: params }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.recordData = resp.data;
        this.dialogVisible = true;
      });
      this.formLoading = false;
    });
  }
  // 删除父类
  del(row: any) {
    this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        this.tableLoading = true;
        const ids: any[] = [];
        ids.push(row.id);
        this.doDelete(URL_DIC_DELETE, { data: ids }).then((resp: any) => {
          this.parseResponse(resp, () => {
            this.getListData();
          });
          this.tableLoading = false;
        });
      })
      .catch(() => {
        return true;
      });
  }
  // 父级数据新建弹窗关闭
  closeDialog(needFresh: any) {
    if (needFresh === true) {
      this.getListData();
    }
    this.dialogVisible = false;
  }
  // 二级数据新建弹窗关闭
  closeSubDialog(needFresh: any) {
    if (needFresh === true) {
      this.getSubListData();
    }
    this.dialogVisible2 = false;
  }
  // 获取左侧列表
  getListData() {
    this.tableLoading = true;
    const searchObj = cloneDeep(this.searchObj);
    searchObj.search = searchObj.search.trim();
    this.doPost(URL_DIC_LIST, searchObj).then((resp: any) => {
      const { tableData = [], pageData } = resp.data;
      this.tableData = tableData;
      this.searchObj.pageData = pageData;
      if (tableData && tableData.length) {
        this.parentCodeObj = tableData[0];
        this.getSubListData();
      }
      this.tableLoading = false;
    });
  }

  rowClick(row) {
    this.parentCodeObj = row;
    this.getSubListData();
  }

  // 获取二级数据
  getSubListData() {
    this.subTableLoading = true;
    const subSearchObj = cloneDeep(this.subSearchObj);
    subSearchObj.search = subSearchObj.search.trim();
    this.doPost(URL_DIC_SUB_LIST + '?parentCode=' + this.parentCodeObj.code, subSearchObj).then(
      (resp: any) => {
        this.parseResponse(resp, () => {
          resp.data.columnData.push({
            label: '操作',
            value: 'operator',
            minWidth: '110px'
          });
          resp.data.columnData.forEach((el) => {
            if (el.prop) {
              el.value = el.prop;
              delete el.prop;
            }
          });
          resp.data.tableData.forEach((el) => {
            el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
            el.value = `${el.value1 ? '值1：' + el.value1 : ''}
            ${el.value2 ? '值2：' + el.value2 : ''}
            ${el.value3 ? '值3：' + el.value3 : ''}
            ${el.value4 ? '值4：' + el.value4 : ''}`;
          });
          this.subSearchObj.pageData = resp.data.pageData;
          this.subTableData = {
            ...resp.data
          };
        });
        this.subTableLoading = false;
      }
    );
  }

  handleCurrentChange2(currentPage, pageSize) {
    this.subSearchObj.pageData.currentPage = currentPage;
    this.subSearchObj.pageData.pageSize = pageSize;
    this.fetchList2();
  }

  handleSortChange2(val) {
    this.subSearchObj.sortData = val;
    this.fetchList2();
  }

  addSub() {
    if (this.parentCodeObj.code === undefined) {
      this.$message.error('上级编码不存在请重新选择分类');
      return;
    }
    this.recordData2 = { parentCode: this.parentCodeObj.code };
    this.dialogVisible2 = true;
    this.dialogTitle2 = '新建';
  }

  deleteSub(row: any) {
    this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        this.subTableLoading = true;
        const ids: any[] = [];
        ids.push(row.id);
        this.doDelete(URL_DIC_DELETE, { data: ids }).then((resp: any) => {
          this.parseResponse(resp, () => {
            this.getSubListData();
          });
          this.subTableLoading = false;
        });
      })
      .catch(() => {
        return true;
      });
  }

  editSub(row: any) {
    this.dialogTitle2 = '编辑';
    this.formLoading2 = true;
    const params = { id: row.id };
    this.doGet(URL_DIC_FIND, { params: params }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.recordData2 = resp.data;
        this.dialogVisible2 = true;
      });
      this.formLoading2 = false;
    });
  }

  // 刷新
  refresh() {
    this.getSubListData();
  }
}
</script>

<style lang="scss" scoped>
.dictionary {
  background: #fff;
  height: calc(100vh - 114px);
  &-tooltip {
    cursor: pointer;
  }
  &-create {
    margin-right: 13px;
  }
  &-icon {
    margin: 0 5px;
  }
}
.base-table-content {
  // height: calc(65vh - 100px);
  z-index: 200;
}
.content {
  display: flex;
  padding: 0;
  height: calc(100vh - 178px);
}
.right-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 40px;
}
.content-header {
  display: flex;
  align-items: center;
  height: 50px;
  padding: 0 20px;
  border-bottom: 1px solid #f1f1f1;
}
.right-content-header {
  display: flex;
  align-items: center;
  height: 50px;
  padding: 0px 0px 0px 20px;
  border-bottom: 1px solid #f1f1f1;
}
.content-body {
  width: 100%;
  height: calc(100vh - 240px);
}
.left-content {
  width: 440px;
  height: 100%;
  border-right: 1px solid #f1f1f1;
  .content-header {
    justify-content: flex-end;
  }
  .left-table__header {
    display: flex;
    height: 50px;
    align-items: center;
    padding: 0 20px;
    border-bottom: 1px solid #f1f1f1;
    font-weight: 700;
    &-code {
      margin-left: 20px;
      width: 100px;
    }
    &-name {
      flex: 1;
    }
  }
  .left-table__body {
    height: calc(100% - 85px);
    padding: 10px;
    overflow-y: scroll;
    border-bottom: 1px solid #f1f1f1;
    &--empty {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &--text {
      color: #909399;
    }
    .left-table__item {
      display: flex;
      height: 40px;
      align-items: center;
      cursor: pointer;
      .code {
        width: 100px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-left: 20px;
      }
      .title {
        margin-left: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }
      .operation {
        width: 70px;
        text-align: right;
        display: none;
        > i {
          margin-right: 8px;
        }
      }
    }
    .left-table__item:hover,
    .left-table__item.active {
      background: #f6f6f6;
      border-radius: 6px;
      .operation {
        display: block;
      }
    }
  }
  .left-table__footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 41px;
  }
}
.right-content {
  flex: 1;
  height: 100%;
  overflow: hidden;
  &__search {
    margin-right: 10px;
  }
  .title {
    flex: 1;
    display: flex;
    align-items: center;
    span {
      margin-left: 8px;
    }
    &::before {
      display: block;
      content: '';
      width: 8px;
      height: 8px;
      background: #ff9c00;
      border-radius: 2px;
    }
  }
  .content-header {
    text-align: right;
    border-bottom: none;
  }
  ::v-deep .el-table__cell.el-table__expanded-cell {
    .el-form-item {
      color: #777777;
      margin-bottom: 16px;
      .el-form-item__label,
      .el-form-item__content {
        line-height: 20px;
      }
      .el-form-item__content > div {
        font-size: 12px !important;
      }
    }
  }
}
</style>
