<template>
  <bs-dialog
    size="large"
    :title="$t('pa.dataPreview')"
    :visible.sync="display"
    :footer-visible="false"
    class="preview-dialog"
  >
    <div ref="previewDialogBody" v-loading="loading" :style="{ height: loading ? '80vh' : '100%' }">
      <div class="preview-dialog__header">
        <!-- tabs -->
        <el-tabs v-model="activeName" class="preview-dialog-tabs">
          <el-tab-pane :label="$t('pa.listView')" name="tableDisplay" />
          <el-tab-pane :label="$t('pa.rawData')" name="originData" />
        </el-tabs>
        <!-- 查询 -->
        <el-form
          ref="formRef"
          inline
          :model="formData"
          :rules="formRule"
          class="preview-dialog__header--search"
          :style="{ marginTop: formIsValid ? '' : '-18px' }"
          @validate="handleValidate"
        >
          <!-- consumeMode -->
          <el-form-item prop="consumeMode">
            <bs-select
              v-model="formData.consumeMode"
              clearable
              size="small"
              :options="offsetOptions"
              :placeholder="$t('pa.placeholder.consumeMode')"
              @change="handleModeChange()"
            />
          </el-form-item>
          <!-- groupId -->
          <el-form-item v-if="isGroupOffsets" prop="groupId">
            <el-input v-model="formData.groupId" size="small" clearable :placeholder="$t('pa.placeholder.groupId')" />
          </el-form-item>
          <!-- startTimestamp -->
          <el-form-item v-if="isTimestamp" prop="startTimestamp">
            <el-input v-model="formData.startTimestamp" size="small" clearable :placeholder="$t('pa.placeholder.time')" />
          </el-form-item>
          <!-- partitionOffset -->
          <el-form-item v-if="isSpecificOffsets" prop="partitionOffset">
            <el-input
              v-model="formData.partitionOffset"
              style="width: 280px"
              clearable
              size="small"
              :placeholder="$t('pa.placeholder.partitionOffset')"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="getTopicData">{{ $t('pa.action.search') }}</el-button>
          </el-form-item>
        </el-form>
      </div>
      <!-- 列表展示 -->
      <div v-if="activeName === 'tableDisplay'" class="preview-dialog__body">
        <!-- table -->
        <bs-table
          height="200"
          size="mini"
          paging-front
          highlight-current-row
          :column-settings="false"
          :checked-row="curRow"
          :data="tableData"
          :column-data="columnData"
          :page-data="pageData"
          @page-change="handlePageChange"
          @row-click="handleRowClick"
        />
        <!-- 列表展示下方切换Key、Value、Timestamp区域 -->
        <div class="row-content--header">
          <el-tabs v-model="activeType" type="border-card" class="border-card-tabs" @tab-click="getShowData">
            <el-tab-pane label="Key" name="key" />
            <el-tab-pane label="Value" name="value" />
            <el-tab-pane label="Timestamp" name="timestamp" />
          </el-tabs>
          <div v-if="activeType !== 'timestamp'" class="view-type-search">
            <span>{{ $t('pa.dataViewMode') }}</span>
            <bs-select v-model="viewType" :options="viewTypeOptions" @change="getShowData" />
          </div>
        </div>
        <div class="row-content--body">
          <div v-if="viewType === 'text'">{{ showRowData }}</div>
          <pre v-else><code>{{ showRowData }}</code></pre>
        </div>
      </div>
      <!-- 原始数据 -->
      <div v-if="activeName === 'originData'" class="orignal-data">
        <!-- tabs -->
        <el-tabs v-model="activeOrigin" type="border-card" class="border-card-tabs">
          <el-tab-pane :label="$t('pa.onlyValue')" name="onlyValue" />
          <el-tab-pane :label="$t('pa.all')" name="all" />
        </el-tabs>
        <div v-if="activeOrigin === 'onlyValue'" class="tabContent">
          <div v-for="(item, index) in originData.onlyValue" :key="index" class="value-string">{{ item }}</div>
        </div>
        <div v-if="activeOrigin === 'all'" class="tabContent">
          <div v-for="(item, index) in originData.all" :key="index" class="value-string">{{ item }}</div>
        </div>
      </div>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, PropSync, Prop, Ref, Vue } from 'vue-property-decorator';
import { getTopicPreviewData } from '@/apis/serviceApi';
import { safeArray, safeParse, timeFormat } from '@/utils';
import { isEmpty } from 'lodash';
import ElForm from 'bs-ui-pro/lib/form';
import xmlFormat from 'xml-formatter';

@Component
export default class TopicPreviewDialog extends Vue {
  @PropSync('show', { type: Boolean, default: true }) display!: boolean;
  @Prop({ default: () => ({}) }) data!: any;
  @Ref('formRef') readonly form!: ElForm;

  loading = false;
  formData: any = { consumeMode: '', groupId: '', startTimestamp: '', partitionOffset: '' };
  offsetOptions: any[] = [
    { value: 'earliest-offset', label: 'earliest-offset' },
    { value: 'latest-offset', label: 'latest-offset' },
    { value: 'group-offsets', label: 'group-offsets' },
    { value: 'timestamp', label: 'timestamp' },
    { value: 'specific-offsets', label: 'specific-offsets' }
  ];
  columnData: any[] = [
    {
      label: 'Partition',
      value: 'partition'
    },
    {
      label: 'Offset',
      value: 'offset'
    },
    {
      label: 'Key',
      value: 'key'
    },
    {
      label: 'Value',
      value: 'value'
    },
    {
      label: 'Timestamp',
      value: 'timestamp'
    }
  ];
  pageData = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 };
  tableData: any[] = [];
  curRow: any = {};
  originData: { all: string[]; onlyValue: string[] } = {
    all: [],
    onlyValue: []
  };
  activeName = 'tableDisplay';
  activeType = 'value';
  viewType = 'text';
  viewTypeOptions = [
    { label: 'Text', value: 'text' },
    { label: 'JSON', value: 'json' },
    { label: 'XML', value: 'xml' }
  ];
  activeOrigin = 'onlyValue';
  showRowData = '';
  formIsValid = true;
  get id() {
    return (this.$route.query.id as string) || '';
  }
  get isGroupOffsets() {
    return this.formData.consumeMode === 'group-offsets';
  }
  get isTimestamp() {
    return this.formData.consumeMode === 'timestamp';
  }
  get isSpecificOffsets() {
    return this.formData.consumeMode === 'specific-offsets';
  }
  get formRule() {
    return {
      consumeMode: { required: true, message: this.$t('pa.placeholder.consumeMode'), trigger: 'change' },
      groupId: { required: this.isGroupOffsets, message: this.$t('pa.placeholder.groupId'), trigger: 'blur' },
      startTimestamp: { required: this.isTimestamp, message: this.$t('pa.placeholder.time'), trigger: 'blur' },
      partitionOffset: { required: this.isSpecificOffsets, message: this.$t('pa.placeholder.input'), trigger: 'blur' }
    };
  }

  async handleValidate(val, valid) {
    this.formIsValid = valid;
  }

  async getTopicData() {
    try {
      this.formIsValid = true;
      this.activeType = 'value';
      await this.form.validate();
      this.loading = true;
      const params = {
        id: this.id,
        topic: this.data?.topic,
        ...this.formData
      };
      const { success, data, error } = await getTopicPreviewData(params);
      if (!success) return this.$message.error(error);
      this.tableData = safeArray(data).map((el, index) => ({ ...el, id: index }));
      this.pageData.total = data.length;
      this.$nextTick(() => {
        this.handleRowClick(this.tableData[0]);
      });
      this.originData.all = this.tableData.map((el) => JSON.stringify(el)); // 原始数据-全部
      this.originData.onlyValue = this.tableData.map((el) => el.value); // 原始数据-仅value
    } catch {
      this.formIsValid = false;
    } finally {
      this.showRowData = '';
      this.curRow = {};
      this.loading = false;
    }
  }

  handlePageChange(page: number, size: number) {
    this.pageData.currentPage = page;
    this.pageData.pageSize = size;
  }
  handleRowClick(row) {
    this.curRow = row;
    this.getShowData();
  }
  getShowData() {
    if (this.tableData.length === 0) return;
    if (isEmpty(this.curRow)) return this.$message.error(this.$t('pa.tip.choseOne'));
    const getValueByViewType = (data, type) => {
      let result = '';
      switch (this.viewType) {
        case 'text':
          result = data;
          break;
        case 'json':
          result = getJsonData(data, type);
          break;
        default:
          result = getXmlData(data);
          break;
      }
      return result;
    };
    const getJsonData = (data, type) => {
      try {
        let result = type === 'key' ? safeParse(data) : data;
        if (typeof result === 'string') return 'Not valid JSON string'; // 尝试将数据解析为JSON
        return JSON.stringify(result, null, 2);
      } catch (error) {
        return 'Not valid JSON string';
      }
    };
    const getXmlData = (data: string) => {
      const isValidXML = (xmlStr) => {
        try {
          new DOMParser().parseFromString(xmlStr, 'text/xml');
          return true;
        } catch (e) {
          return false;
        }
      };
      try {
        if (!isValidXML(data)) return 'Not valid XML string';
        return xmlFormat(data);
      } catch (e) {
        return 'Not valid XML string';
      }
    };

    switch (this.activeType) {
      case 'key':
        this.showRowData = getValueByViewType(this.curRow?.key, 'key');
        break;
      case 'value':
        this.showRowData = getValueByViewType(this.curRow?.value, 'value');
        break;
      default:
        this.showRowData = this.$t('pa.tip.timeFormat', [timeFormat(this.curRow?.timestamp)]);
        break;
    }
  }
  handleModeChange() {
    this.formData.groupId = this.formData.startTimestamp = this.formData.partitionOffset = '';
  }
}
</script>

<style lang="scss" scoped>
$height: 80vh;
$tabsHeight: 30px;
.preview-dialog {
  ::v-deep .el-dialog__body {
    padding: 0;
    height: $height;
    max-height: $height;
    background: $--bs-color-background-page;
  }
  &-body {
    background: $--bs-color-background-page;
  }
  &__header {
    display: flex;
    align-items: center;
    height: 50px;
    padding: 0 20px;
    background: #fff;
    &--search {
      text-align: right;
      .el-form-item {
        margin-bottom: 0;
        &:last-of-type {
          margin-right: 0;
        }
      }
    }
  }

  &-tabs {
    height: 45px;
    margin: 0 10px;
    ::v-deep .el-tabs {
      &__header {
        line-height: 45px !important;
      }
      &__item {
        height: 45px !important;
        line-height: 45px !important;
      }
    }
  }
  .row-content {
    &--header {
      padding: 20px 20px 0;
      overflow: hidden;
      ::v-deep .el-tabs {
        float: left;
        vertical-align: middle;
      }
      .view-type-search {
        float: right;
        vertical-align: middle;
        margin-top: -7px;
        span {
          margin-right: 5px;
        }
      }
      &::after {
        content: '';
        display: inline-block;
        height: 0;
        width: 0;
        clear: both;
      }
    }
    &--body {
      overflow: auto;
      height: calc(80vh - 367px);
      margin: 0 20px 20px;
      padding: 10px;
      background: #fff;
      border: 1px solid $--bs-color-border-light;
    }
  }
  .border-card-tabs {
    display: inline-grid;
    height: $tabsHeight;
    ::v-deep .el-tabs__header {
      line-height: $tabsHeight !important;
    }
    ::v-deep .el-tabs__item {
      height: $tabsHeight;
      line-height: $tabsHeight;
    }
    ::v-deep .el-tabs__item.is-active {
      background-color: $--bs-color-primary;
      color: #fff;
    }
  }
  .tabContent {
    padding: 10px;
  }
  .orignal-data {
    height: calc(100% - 50px);
    overflow: hidden;
    padding: 15px 34px 10px;
    background: #fff;
    .tabContent {
      height: calc(100% - 55px);
      overflow: auto;
    }
    .value-string {
      white-space: nowrap;
    }
  }
}
</style>
