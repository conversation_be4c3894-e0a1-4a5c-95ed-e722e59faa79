import { CssStyleDeclaration } from 'cytoscape';
/* 基础样式 */
const base = {
  selector: 'node',
  style: {
    'font-size': '12px',
    'border-width': 0,
    'border-opacity': 0,
    shape: 'ellipse'
  }
};
const nodeSelected = {
  selector: 'node:selected',
  style: {
    'border-style': 'solid',
    'border-width': 2,
    'border-color': '#ff9630',
    'border-opacity': 1
  }
};
const edgeSelected = {
  selector: 'edge:selected',
  style: {
    width: 2,
    'line-color': '#6fc332',
    'target-arrow-color': '#6fc332'
  }
};
const edge = {
  selector: 'edge',
  style: {
    'font-family': 'MicrosoftYaHei',
    'font-size': '12px',
    'text-wrap': 'wrap',
    'text-valign': 'center',
    'text-halign': 'center',
    'line-height': 1.1,
    width: 1,
    'curve-style': 'bezier',
    'control-point-step-size': 60,
    'target-arrow-shape': 'triangle',
    'target-arrow-color': '#CBCBCB',
    'line-color': '#DBDBDB',
    'line-style': 'solid',
    'arrow-scale': 0.8
  }
};

/* 流程节点样式 */
const JOB = {
  selector: 'node.JOB',
  style: {
    'background-color': '#ff2de0'
  }
};
/* 资源节点样式 */
const SERVICE = {
  selector: 'node.SERVICE',
  style: {
    'background-color': '#6fc332'
  }
};
/* 表节点样式 */
const TABLE = {
  selector: 'node.TABLE',
  style: {
    'background-color': '#0098fd'
  }
};
/* 视图点样式 */
const VIEW = {
  selector: 'node.VIEW',
  style: {
    'background-color': '#9f55d5'
  }
};
/* 中心点样式 */
const center = {
  selector: 'node.center',
  style: {
    'background-color': '#ff2de0'
  }
};

const parent = {
  selector: 'node.parent',
  style: {
    'background-color': '#FAFCFF',
    'border-width': 1,
    'border-color': '#aaaaaa',
    'border-style': 'dashed',
    'border-opacity': 0,
    shape: 'round-rectangle'
  }
};
const showParent = {
  selector: 'node.showParent',
  style: {
    'border-opacity': 1
  }
};
const hidden = {
  selector: '.hidden',
  style: {
    opacity: '0',
    'border-opacity': 0
  }
};
const homology = {
  selector: 'edge.homology',
  style: {
    width: 1,
    'line-style': 'dashed',
    'target-arrow-shape': 'none'
  }
};

const label = {
  selector: '.label',
  style: {
    label: 'data(label)',
    'text-valign': 'bottom',
    'text-halign': 'center',
    'font-size': 6,
    color: '#808080',
    'text-margin-y': 2
  }
};

const result: CssStyleDeclaration = [
  edge,
  base,
  label,
  JOB,
  SERVICE,
  TABLE,
  VIEW,
  center,
  parent,
  showParent,
  hidden,
  homology,
  nodeSelected,
  edgeSelected
];
export default result;
