import { MessageBox } from 'bs-ui-pro';
import { Vue, Component } from 'vue-property-decorator';
import '../flow-list/style/flow-list.scss';
@Component
export default class BeforeLeave extends Vue {
  // 处理this上不存在对应属性的ts报错
  isClose = false;
  flowStatus;
  baseInfo;
  isChange;
  isRender;
  get saveConfrimMsg() {
    return this.$t('pa.flow.msg21', [this.baseInfo.jobName]);
  }
  activated() {
    this.watchWindowOnload();
    this.isClose = false;
  }
  beforeRouteLeave(to, from, next) {
    this.isRender = false;
    if (to.name === 'refresh') return next();
    this.flowStatus === 'DEV' ? this.checkLeave(next) : next();
  }
  beforeRouteUpdate(to, from, next) {
    this.isRender = false;
    this.flowStatus === 'DEV' ? this.checkLeave(next) : next();
  }
  // 监听页面离开事件
  async checkLeave(next: (flag?: boolean) => unknown) {
    if (!this.isChange || this.isClose) return next();
    try {
      await MessageBox.confirm(this.$t('pa.flow.msg21', [this.baseInfo.jobName]), this.$t('pa.flow.tip'), {
        type: 'warning',
        customClass: 'flow-list__confirm'
      });
      this.isChange = false;
      next();
    } catch (e) {
      next(false);
    }
  }
  // 处理tab关闭前的逻辑
  handleTabsNavClose() {
    (this as any).$tabNav.beforeClose = async (pane) => {
      // 关闭页签为当前流程设计tab页时触发
      // 血缘关系跳转过来后，query上无id 所以将/flow?id 改为 /flow?
      if (pane.active && pane.name.includes('/flow?')) {
        if (this.isChange && this.$route.query.flowId) {
          try {
            const res = await this.$confirm(this.saveConfrimMsg, this.$t('pa.flow.tip'), {
              customClass: 'flow-list__confirm'
            });
            res === 'confirm' && (this.isClose = true);
            return true;
          } catch {
            // 进入catch分支，阻止tab关闭
            throw new Error('error');
          }
        }
      }
      return;
    };
  }
  // 监听浏览器页面刷新事件
  watchWindowOnload() {
    window.onbeforeunload = (e) => {
      if (this.isChange) {
        const info = window.event || e;
        info.returnValue = this.$t('pa.flow.leaveMsg');
      }
    };
  }
}
