<template>
  <div class="chart">
    <div :id="name" class="chart-container"></div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import echarts from 'echarts';
@Component
export default class Chart extends Vue {
  // echarts配置项
  @Prop({ default: () => {} }) option!: object;
  // 图表名称，作为获取dom的唯一标识
  @Prop({ default: '' }) name!: string;
  private chart: any = null;
  private chartDom: HTMLDivElement | null = null;
  private xLabel = '';
  @Watch('option', { deep: true })
  handleOptionChange(val) {
    this.chart.setOption(val);
  }
  @Watch('$store.state.others.isResize')
  handleResizeChange() {
    this.resizeCharts();
  }
  @Watch('$store.state.others.isCollapse')
  handleIsCollapseChange() {
    this.resizeCharts();
  }
  activated() {
    window.dispatchEvent(new Event('resize'));
  }
  mounted() {
    this.initChart();
    if (this.name !== 'distribution') {
      this.chart.setOption(this.option);
    }
    if (this.name === 'process') {
      this.chart.on('mouseover', this.xAxisOver);
      this.chart.on('mouseout', this.xAxisout);
    }
    window.addEventListener('resize', this.resizeCharts);
  }
  // 初始化图表
  initChart() {
    this.chartDom = document.querySelector(`#${this.name}`) as HTMLDivElement;
    this.chart = echarts.init(this.chartDom, {}, { renderer: 'svg' });
    if (this.name === 'distribution') {
      this.resetOption();
    }
  }
  resetOption() {
    if ((this.chartDom as HTMLDivElement).offsetWidth < 450) {
      this.chart.setOption(this.option);
      this.chart.setOption({
        legend: {
          orient: 'vertical',
          right: '5%',
          top: 'center',
          itemGap: 12,
          itemWidth: 8,
          itemHeight: 8,
          borderRadius: 4
        },
        series: [
          {
            radius:
              this.$store.state.others.isResize || !this.$store.state.others.isCollapse
                ? ['44%', '59%']
                : ['48%', '68%']
          }
        ]
      });
    } else {
      this.chart.setOption(this.option);
    }
  }
  formateTooltip() {
    return this.xLabel;
  }
  xAxisOver(params) {
    if (params.componentType === 'xAxis') {
      this.xLabel = params.value;
      const [offsetX, offsetY] = [params.event.offsetX - 40, params.event.offsetY + 10];
      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: 'inherit'
            }
          },
          formatter: this.formateTooltip,
          extraCssText: `padding:12px;box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16);
          max-width:400px; white-space:pre-wrap;
    border-radius: 4px;
    opacity: 0.98;`,
          alwaysShowContent: true
        }
      });
      this.chart.dispatchAction({
        type: 'showTip',
        position: [offsetX, offsetY],
        seriesIndex: 0,
        dataIndex: 0
      });
    }
  }
  xAxisout(params) {
    if (params.componentType === 'xAxis') {
      this.xLabel = '';
      this.chart.setOption(this.option);
      this.chart.setOption({ tooltip: { alwaysShowContent: false } });
    }
  }
  resizeCharts() {
    if (this.name === 'distribution') {
      this.resetOption();
    }
    this.chart.resize();
  }
}
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 170px;
}
</style>
