{"forms": [{"type": "input", "prop": "title", "label": "名称", "componentProps": {"maxlength": 30, "placeholder": "请输入名称", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入名称", "trigger": "blur"}, {"min": 2, "max": 30, "message": "长度在 2 到 30 个字符", "trigger": "blur"}]}, {"type": "textarea", "prop": "url", "label": "JDBC连接串", "componentProps": {"rows": 3, "maxlength": 1024, "placeholder": "请输入JDBC连接串，jdbc:clickhouse://${ip}:${port}/${schema}或jdbc:oracle:thin:@${ip}:${port}:${schema}"}, "rules": [{"required": true, "message": "请输入JDBC连接串", "trigger": "blur"}, {"pattern": "/^\\S*$/", "message": "JDBC连接串中不能有空格或换行", "trigger": "blur"}]}, {"type": "input", "prop": "username", "label": "用户名", "componentProps": {"maxlength": 30, "placeholder": "请输入用户名", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入用户名", "trigger": "blur"}]}, {"type": "password", "prop": "password", "label": "密码", "componentProps": {"maxlength": 100, "placeholder": "请输入密码"}, "rules": [{"required": true, "message": "请输入密码", "trigger": "blur"}]}, {"type": "textarea", "prop": "memo", "label": "备注", "componentProps": {"maxlength": 255, "placeholder": "请输入备注", "rows": 5}}]}