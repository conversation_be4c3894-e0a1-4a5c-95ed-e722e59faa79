<template>
  <bs-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    :confirm-button="{ disabled }"
    append-to-body
    @close="closeDialog(false)"
    @confirm="submit('ruleForm')"
  >
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-position="left"
      :label-width="isEn ? '140px' : '80px'"
      class="form-content"
    >
      <el-form-item :label="$t('pa.flow.label12')" prop="resId">
        <el-tooltip :disabled="!filterInfo" effect="light">
          <div slot="content">
            <div v-for="(filter, index) in filterInfo" :key="filter.rule + index">
              <div class="filter-title">{{ $t('pa.params.template.detail.filterRule') }}{{ index + 1 }}</div>
              <div v-for="(condition, index1) in filter.conditions" :key="condition.fieldName + index1" class="filter-body">
                {{ $t('pa.flow.condition') }}{{ index1 + 1 }}：{{ condition.fieldName }} ({{ $t('pa.flow.category') }}：{{
                  condition.fieldType
                }})
                {{ condition.operator }}
                {{ condition.fieldValue }}
              </div>
            </div>
          </div>
          <el-select
            v-model="ruleForm.resId"
            :placeholder="$t('pa.flow.placeholder29')"
            clearable
            filterable
            :disabled="disabled"
            style="width: 100%"
          >
            <el-option v-for="rule in filterList" :key="rule.id" :label="rule.filterTemplateName" :value="rule.id" />
          </el-select>
        </el-tooltip>
      </el-form-item>
      <el-form-item :label="$t('pa.flow.label13')" prop="updateInterval">
        <el-input v-model="ruleForm.updateInterval" type="number" :disabled="disabled">
          <template slot="append">{{ $t('pa.flow.s') }}</template>
        </el-input>
      </el-form-item>
    </el-form>
    <print-log slot="footer-left" v-model="printLog" :disabled="disabled" />
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { get } from '@/apis/utils/net';
import { cloneDeep } from 'lodash';
import PrintLog from './components/print-log.vue';

@Component({
  components: {
    PrintLog
  }
})
export default class DynamicFilter extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop({ default: false }) disabled!: boolean;

  printLog = false;
  ruleForm: any = {
    resId: '',
    updateInterval: 600
  };
  rules: any = {
    resId: [{ required: true, message: this.$t('pa.flow.placeholder29'), trigger: 'change' }],
    updateInterval: [{ required: true, message: this.$t('pa.flow.msg202'), trigger: 'blur' }]
  };

  filterList: any = [];
  get title() {
    const { nodeName = '', componentName = '' } = this.data || {};
    return this.$t('pa.flow.msg288', [nodeName, componentName]);
  }
  async created() {
    await this.getList();
    const { properties } = this.data;
    this.printLog = this.data.printLog || false;
    if (properties) {
      this.ruleForm = properties;
      this.ruleForm.resId = properties.id;
    }
  }

  async getList() {
    // 获取模板列表
    const { data, success } = await get('/rs/pa/filter/findAll');
    if (success) {
      this.filterList = data;
    }
  }

  get filterInfo() {
    if (this.filterList.length > 0 && this.ruleForm.resId) {
      let filterInfo = this.filterList.find((item) => item.id === this.ruleForm.resId).filterInfo;
      /* eslint-disable-next-line */
      filterInfo = eval(filterInfo);
      return filterInfo;
    } else {
      return '';
    }
  }

  submit(formName: any) {
    const nodeDot = cloneDeep(this.data);
    const formRef: any = this.$refs[formName];
    formRef.validate((valid) => {
      if (valid) {
        nodeDot.outputFields = nodeDot.inputFields;
        nodeDot.outputFields.forEach((item) => {
          item.outputable = true;
        });
        nodeDot.properties = cloneDeep(this.ruleForm);
        nodeDot.properties.id = this.ruleForm.resId;
        delete nodeDot.properties.resId;
        nodeDot.printLog = this.printLog;
        this.closeDialog(true, nodeDot);
      }
    });
  }

  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    return { needUpdate, jobNode };
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__content .el-input-group {
  vertical-align: baseline;
}

.filter-title {
  font-size: 14px;
  font-weight: bolder;
  padding-bottom: 10px;
}
.filter-body {
  padding-bottom: 10px;
}
</style>
