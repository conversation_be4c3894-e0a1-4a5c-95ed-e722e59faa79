<template>
  <el-dialog
    :title="data.nodeName + '组件配置'"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    width="40%"
    append-to-body
  >
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-position="left"
      label-width="80px"
      class="form-content"
    >
      <el-form-item label="过滤模板" prop="resId">
        <el-tooltip effect="light">
          <div v-if="filterInfo" slot="content">
            <div v-for="(filter, index) in filterInfo" :key="filter.rule + index">
              <div class="filter-title">{{ filter.rule }}</div>
              <div
                v-for="(condition, index1) in filter.conditions"
                :key="condition.fieldName + index1"
                class="filter-body"
              >
                {{ condition.rule }}：{{ condition.fieldName }} (类型：{{ condition.fieldType }})
                {{ condition.operator }} {{ condition.fieldValue }}
              </div>
            </div>
          </div>
          <el-select
            v-model="ruleForm.resId"
            placeholder="请选择过滤模板"
            clearable
            filterable
            :disabled="disabled"
            style="width: 100%"
          >
            <el-option
              v-for="rule in filterList"
              :key="rule.id"
              :label="rule.filterTemplateName"
              :value="rule.id"
            />
          </el-select>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="更新频率" prop="updateInterval">
        <el-input v-model="ruleForm.updateInterval" type="number">
          <template slot="append">秒</template>
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <div style="width: 60%">
        <el-switch
          v-model="printLog"
          :disabled="disabled"
          style="display: block; float: left"
          active-color="#13ce66"
          inactive-color="#ff4949"
          active-text="开启日志"
          inactive-text="关闭日志"
        />
      </div>
      <el-button @click="closeDialog(false)">取消</el-button>
      <el-button v-if="!disabled" type="primary" @click="submit('ruleForm')">确定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { get } from '@/apis/utils/net';
import { cloneDeep } from 'lodash';

@Component
export default class DynamicFilter extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop({ default: false }) disabled!: boolean;

  printLog = false;
  ruleForm: any = {
    resId: '',
    updateInterval: 600
  };
  rules: any = {
    resId: [{ required: true, message: '请选择过滤模板', trigger: 'change' }],
    updateInterval: [{ required: true, message: '请填写更新频率', trigger: 'blur' }]
  };

  filterList: any = [];

  async created() {
    await this.getList();
    const { properties } = this.data;
    this.printLog = this.data.printLog || false;
    if (properties) {
      this.ruleForm = properties;
    }
  }

  async getList() {
    // 获取模板列表
    const { data, success } = await get('/rs/pa/filter/findAll');
    if (success) {
      this.filterList = data;
    }
  }

  get filterInfo() {
    if (this.filterList.length > 0 && this.ruleForm.resId) {
      let filterInfo = this.filterList.find((item) => item.id === this.ruleForm.resId).filterInfo;
      /* eslint-disable-next-line */
      filterInfo = eval(filterInfo);
      return filterInfo;
    } else {
      return '';
    }
  }

  submit(formName: any) {
    const nodeDot = cloneDeep(this.data);
    const formRef: any = this.$refs[formName];
    formRef.validate((valid) => {
      if (valid) {
        nodeDot.outputFields = nodeDot.inputFields;
        nodeDot.outputFields.forEach((item) => {
          item.outputable = true;
        });
        nodeDot.properties = this.ruleForm;
        nodeDot.printLog = this.printLog;
        this.closeDialog(true, nodeDot);
      }
    });
  }

  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    return { needUpdate, jobNode };
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__content .el-input-group {
  vertical-align: baseline;
}
.filter-title {
  font-size: 14px;
  font-weight: bolder;
  padding-bottom: 10px;
}
.filter-body {
  padding-bottom: 10px;
}
</style>
