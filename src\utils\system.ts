export default {
  isProduction: () => process.env.NODE_ENV === 'production',
  isDevelopment: () => process.env.NODE_ENV === 'development'
};

// export const getServiceRouter = async (menuList: any[]) => {
//   try {
//     const { data } = await get(URL_RESCONF_GETRESTYPELIST);
//     if (data) {
//       const target = find(menuList, { access: 'PA.ELE.MENU' });
//       const serviceRouter: any = [];
//       if (target) {
//         const service = find(target.children, { access: 'PA.ELE.SERVICE.MENU' });
//         service.children = [];
//         data.forEach((n) => {
//           service.children.push({
//             name: n.label,
//             access: n.listConf.menuAuthCode,
//             layIndex: 3,
//             index: '/element/service/' + toLower(n.type)
//           });
//           serviceRouter.push(
//             {
//               path: '/element/service/' + toLower(n.type),
//               name: toLower(n.type),
//               meta: {
//                 access: n.listConf.menuAuthCode, // 权限信息
//                 resType: n.type,
//                 title: n.label
//               },
//               props: { resType: n.type },
//               component: () => import('@/views/element/service/custom/index.vue')
//             },
//             {
//               path: '/element/service/' + toLower(n.type) + '/detail',
//               name: toLower(n.type) + 'detail',
//               meta: { access: '' },
//               beforeEnter: (to, from, next) => {
//                 to.meta.title = to.query.title || 'ServiceCustom';
//                 next();
//               },
//               component: () => import('@/views/element/service/custom/detail.vue')
//             }
//           );
//         });
//       }
//       return serviceRouter || [];
//     }
//     return Promise.reject(new Error('the interface named session/user is no content'));
//   } catch (e) {
//     return Promise.reject(e);
//   }
// };
