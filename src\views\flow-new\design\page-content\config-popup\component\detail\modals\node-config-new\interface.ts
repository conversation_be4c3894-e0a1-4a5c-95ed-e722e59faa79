/* 基础表单类型 */
type BaseFormType =
  | 'select'
  | 'input'
  | 'textarea'
  | 'text'
  | 'password'
  | 'radio-group'
  | 'checkbox'
  | 'checkbox-group'
  | 'input-number'
  | 'page-select'
  | 'cascader'
  | 'time'
  | 'timerange'
  | 'date'
  | 'datetime'
  | 'daterange'
  | 'datetimerange'
  | 'switch'
  | 'custom';

/* 自定义表单类型
 * table: 选择表及字段配置
 * unit: 带单位选择的表单项
 */
type CustomFormType = 'table' | 'field' | 'serve' | 'unit';

/* 表单分组信息 */
interface Group {
  // base 基础参数  advance 高级参数  custom 自定义参数 output 输出字段
  name: string;
  title: string;
  // 字段名  如output分组可默认配置为outputFields，custom默认配置为customData
  prop?: string;
  data?: { name: string; value: string }[];
  // 自定义字段  当name=output时， 配置自定义字段相关的内容
  customFields?: { multiple: boolean; label: string };
  request?: string | BaseRequest;
}

interface BaseRequestProps {
  list?: string;
  value?: string;
  label?: string;
  saveParams?: string[] | Record<string, string>;
}

/* 表单项选择数据源请求 */
interface BaseRequest {
  // 远程请求地址
  url: string;
  // params请求依赖的key合集 优先从formData中获取,若不存在从nodeData中获取，如orgId
  params?: string[];
  // body请求参数
  data?: string[];
  // 请求方法
  method?: 'post' | 'POST' | 'get' | 'GET';
  // 从接口返回的数据中获取需要转换的字段
  // list当data的第一层级不是数据源时 需要根据字段获取数据源 如data.fields
  // saveParams 需要保存到formData中的字段合集
  // 如Hive输出组件 选择表后需要保存部分字段至formData
  props?: BaseRequestProps;
}
/* type为table时，获取表字段信息请求 */
interface FieldRequest {
  url: string;
  method?: 'post' | 'POST' | 'get' | 'GET';
  // params请求依赖的key合集 优先从formData中获取,若不存在从nodeData中获取，如orgId
  params: string[];
}

interface BaseOption {
  value: string;
  label: string;
  children?: BaseOption[];
}
/* 表单校验规则 */
interface Rule {
  required?: boolean;
  pattern?: boolean;
  message: string;
  trigger?: string | string[];
  validator?: string | any; // 内置校验方法，直接对应方法名或者校验逻辑方法的字符串
}

/* 表单项配置 */
interface FormItem {
  // 表单项类型
  type: BaseFormType | CustomFormType;
  // 字段名
  label?: string;
  // 字段对应的key
  prop?: string;
  // type为table时生效，对应输出的字段合集的key
  fieldProp?: string;
  // 所属分组 若配置了字段信息
  group?: string;
  // 依赖的表单字段
  deps?: string[];
  // 是否展示该表单项
  visible?: string[] | string;
  // 是否使用上游的输出字段作为下拉数据源
  useInput?: boolean;
  // 气泡提示文案
  tooltip?: string;
  // 默认值
  defaultVal?: unknown;
  // 前端组件相关配置
  componentProps?: Record<string, any>;
  // 下拉数据源
  options?: BaseOption[];
  // 表单校验快捷配置参数 必填
  required?: boolean;
  // 表单规则校验
  rules?: Rule[];
  // 单位 当type=unit时有用
  units?: string | string[];
  // 值是否需要拼接 当type=unit时有用
  spliceUnit?: boolean;
  // 远程请求数据源
  request?: string | BaseRequest;
  // type为 table时有效，获取表字段的接口配置
  fieldRequest?: string | FieldRequest;
  children?: FormItem[];
}
interface NodeConfig {
  labelWidth?: number;
  /* 默认为collapse 折叠面板  divider 分割线 false为不分组 */
  groupType: 'divider' | 'collapse' | false;
  groups?: Group[];
  forms: FormItem[];
  /* 该字段决定最终输出字段outputFields的生成方式
   * all 表示全量输入输出, outputFields根据inputFields生成
   * allow-change-order 表示全量输入输出，但是可以更换顺序 （暂不支持）
   * ['a', 'b']，outputFields会根据formData中获取的a和b字段的字段生成
   * const outputFields = [...(this.formData.a || []), ...(this.formData.b || [])]
   */
  outputs?: string[] | 'all';
}

export { NodeConfig, FormItem, Group, BaseRequest, BaseRequestProps };
