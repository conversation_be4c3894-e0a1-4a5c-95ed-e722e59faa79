<template>
  <div ref="rootView" class="relation__container">
    <div id="mountNode"></div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Ref, Watch, Vue } from 'vue-property-decorator';
import G6 from '@antv/g6';
import { throttle } from 'lodash';

@Component
export default class LogicRelation extends Vue {
  @Prop({ type: String, default: '' }) logicExpression!: string;
  @Prop({ default: () => ({}) }) expressionMapping!: any;
  @Ref('rootView') readonly rootView!: HTMLDivElement;
  private graph: any = null;
  private graphData: any = {};
  private initLogicRelation = throttle(this.parseExpression, 600);

  get expression() {
    return this.logicExpression.replace(/\s*/g, '');
  }

  @Watch('expression')
  handleExpressionChange() {
    this.expression && this.initLogicRelation();
  }

  created() {
    this.expression && this.initLogicRelation();
  }

  parseExpression() {
    try {
      /* 合法性校验 */
      require('acorn').parse(this.expression, { ecmaVersion: 2023 });
      /* 数据处理 */
      const str = this.preHandler(this.expression)
        .replace(/\s*/g, '')
        .replace(/\&\&/g, '_&&_')
        .replace(/\|\|/g, '_||_');
      this.graphData = this.generateRelationData(str);
      this.renderGraph();
    } catch {
      this.$tip.error('请输入合法的逻辑表达式');
    }
  }

  preHandler(str) {
    // 方法入口
    function build(param) {
      param = param.replaceAll(' ', '');
      param = param.replaceAll('&&', ' && ');
      param = param.replaceAll('||', ' || ');
      const stringList = allParentheses(param);
      // 将 && 加上括号, 括号里的部分不加
      let res = putParentheses(param);
      for (let i = 0; i < stringList.length; i++) {
        const now = stringList[i];
        // 递归
        res = res.replaceAll(now, build(now));
      }
      return res;
    }

    function putParentheses(param) {
      // 已匹配到的括号数
      let leftBrackets = 0;
      // 是否有自己加的左括号
      let haveKuo = false;
      let result = '';
      for (let i = 0; i < param.length; i++) {
        const nowChar = param[i];
        result += nowChar;
        if (nowChar === '(') {
          leftBrackets++;
          continue;
        }
        if (nowChar === ')') {
          leftBrackets--;
        }
        if (leftBrackets !== 0 || nowChar !== ' ') {
          continue;
        }

        if (!haveKuo && i + 4 < param.length && param[i + 4] === '&') {
          result += '(';
          haveKuo = true;
        } else if (haveKuo && (i + 1 >= param.length || param[i + 1] === '|')) {
          result += ')';
          haveKuo = false;
        }
      }
      if (haveKuo) {
        result += ')';
      }
      return result;
    }

    /**获取所有外层括号的值*/
    function allParentheses(param) {
      // true || (false && false)
      // 左括号的数量
      let leftBrackets = 0;
      // 是否已经匹配到
      let isMatched = false;
      let s = '';
      const resultList: any[] = [];
      for (let i = 0; i < param.length; i++) {
        const c = param[i];
        if (c === '(') {
          leftBrackets++;
          isMatched = true;
        } else if (c === ')') {
          leftBrackets--;
        }
        if (isMatched) {
          s += c;
        }
        if (isMatched && leftBrackets === 0) {
          resultList.push(s.substring(1, s.length - 1));
          s = '';
          isMatched = false;
        }
      }
      return resultList;
    }

    return build(str);
  }

  generateRelationData(str: string) {
    const obj = this.getBracketContent(str).reduce((pre, next, index) => {
      pre[`$${index}`] = next;
      str = str.replace(next, `$${index}`);
      return pre;
    }, {});
    return this.handleTargetArr(str.split('_'), obj);
  }

  getBracketContent(str: string) {
    let i = -1;
    const len = str.length;
    let stack = 0;
    let start = 0;
    const result: any[] = [];

    while (++i < len) {
      const item = str.charAt(i);
      if (item === '(') {
        if (stack === 0) {
          start = i;
        }
        stack++;
      } else if (item === ')') {
        stack--;
        if (stack === 0) {
          result.push(str.substring(start, i + 1));
        }
      }
    }

    return result;
  }

  handleTargetArr(arr: any[] = [], obj: any = {}) {
    return arr.reduce((pre, next) => {
      if (['&&', '||'].includes(next)) {
        return { label: next, children: [pre] };
      }
      const temp = next.includes('$') ? this.handleTargetStr(obj[next]) : { label: next };
      if (pre.children) {
        pre.children.push(temp);
        return pre;
      }
      return temp;
    }, {});
  }

  handleTargetStr(rowStr: string) {
    const str: any = rowStr.substr(1, rowStr.length - 2);
    return !str.includes('(') && !str.includes(')')
      ? this.handleTargetArr(str.split('_'))
      : this.generateRelationData(str);
  }

  mounted() {
    this.initGraph();
  }

  initGraph() {
    const { offsetWidth, offsetHeight } = this.rootView;
    this.graph = new G6.TreeGraph({
      container: 'mountNode',
      width: offsetWidth,
      height: offsetHeight - 20,
      fitView: true,
      fitViewPadding: 20,
      fitCenter: true,
      modes: {
        default: [
          'drag-canvas',
          'zoom-canvas',
          {
            type: 'collapse-expand',
            onChange(item: any, collapsed) {
              item.get('model').collapsed = collapsed;
              return true;
            }
          }
        ]
      },
      defaultNode: {
        size: 40,
        anchorPoints: [
          [0, 0.5],
          [1, 0.5]
        ],
        style: {
          stroke: '#C2D0F0',
          fill: '#FFFFFF'
        },
        labelCfg: {
          style: {
            fontSize: 16,
            fontWeight: 500,
            fill: '#444444'
          }
        }
      },
      defaultEdge: {
        type: 'cubic-horizontal',
        style: {
          lineWidth: 2,
          stroke: '#D4DBE9'
        }
      },
      layout: {
        type: 'compactBox',
        direction: 'LR',
        getId(d) {
          return d.id;
        },
        getHeight() {
          return 100;
        },
        getWidth() {
          return 100;
        },
        getVGap() {
          return 10;
        },
        getHGap() {
          return 80;
        }
      },
      plugins: this.generatePlugins()
    });
    this.renderGraph();
  }

  generatePlugins() {
    const tooltip = new G6.Tooltip({
      className: 'relation-tooltip',
      offsetX: -50,
      offsetY: 10,
      shouldBegin: (e: any) => !['&&', '||'].includes(e.item.getModel().label),
      getContent: (e: any) => `${this.expressionMapping[e.item.getModel().label]}`,
      itemTypes: ['node'],
      fixToNode: [0.5, 1]
    });
    return [tooltip];
  }

  renderGraph() {
    if (this.graph && Object.keys(this.graphData).length) {
      this.graph.data(this.graphData);
      this.graph.node(({ label }: any) => {
        if (['&&', '||'].includes(label)) {
          const stroke = label === '&&' ? '#F8B205' : '#3EC7C7';
          return {
            type: 'diamond',
            style: {
              fill: label === '&&' ? '#FFF5EA' : '#E9FAFA',
              stroke
            },
            labelCfg: {
              style: {
                fill: stroke,
                fontSize: 14,
                fontWeight: 500
              }
            }
          };
        }
        return {};
      });
      this.graph.render();
      this.graph.on('node:mouseenter', (e) => {
        this.graph.setItemState(e.item, 'active', true);
      });
      this.graph.on('node:mouseleave', (e) => {
        this.graph.setItemState(e.item, 'active', false);
      });
      this.graph.fitView();
    }
  }
}
</script>
<style lang="scss" scoped>
.relation {
  &__container {
    width: 100%;
    height: 100%;
    background: #fafcff;
  }

  &-tooltip {
    width: 100px;
    height: 100px;
    background: red;
    width: 120px;
    background-color: #555;
    color: #fff;
    text-align: center;
    padding: 5px 0;
    border-radius: 6px;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.6s;

    &::after {
      content: '';
      position: absolute;
      bottom: 100%;
      left: 50%;
      margin-left: -5px;
      border-width: 5px;
      border-style: solid;
      border-color: transparent transparent #555 transparent;
    }
  }
}
</style>
