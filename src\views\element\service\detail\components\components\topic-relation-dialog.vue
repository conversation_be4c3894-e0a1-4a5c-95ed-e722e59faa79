<template>
  <bs-dialog size="medium" :title="title" :visible.sync="display" class="relation-dialog">
    <div v-loading="loading" style="height: 100%">
      <!-- header -->
      <div class="relation-dialog-header">
        <bs-select v-model="type" :options="typeOptions" style="width: 150px" @change="handleTypeChange" />
      </div>
      <bs-table
        height="100%"
        :data="tableData"
        :page-data="pageData"
        :column-data="columnData"
        :column-settings="false"
        style="height: calc(100% - 102px)"
        @page-change="handlePageChange"
      >
        <!-- status_JOB -->
        <template slot="status_JOB" slot-scope="{ row }">
          <bs-tag :color="getStatusColor(row.status)">{{ row.status }}</bs-tag>
        </template>
      </bs-table>
    </div>
    <!-- footer -->
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleDownload">{{ $t('pa.action.download') }}</el-button>
      <el-button @click="display = false">{{ $t('pa.action.close') }}</el-button>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, PropSync, Prop, Vue } from 'vue-property-decorator';
import { getTopicRelationList, downloadTopicRelation } from '@/apis/serviceApi';
import { timeFormat, safeArray } from '@/utils';
@Component
export default class TopicRelationDialog extends Vue {
  @PropSync('show', { type: Boolean, default: true }) display!: boolean;
  @Prop({ default: () => ({}) }) data!: any;

  loading = false;
  type = 'JOB';
  typeOptions: any[] = [
    { value: 'JOB', label: this.$t('pa.process') },
    { value: 'TABLE', label: this.$t('pa.table') }
  ];
  tableData: any[] = [];
  columnData: any[] = [];
  pageData = {
    layout: 'total, prev, pager, next, jumper',
    pageSize: this.$store.getters.pageSize || 25,
    currentPage: 1,
    total: 0
  };

  get title() {
    return this.$t('pa.preferenceRelation', [this.data.topic || '']);
  }

  created() {
    this.getRelationList();
  }

  /* 获取引用关系 */
  async getRelationList() {
    try {
      this.loading = true;
      const { success, data, error } = await getTopicRelationList(this.type, this.data.id, this.pageData);
      if (!success) return this.$message.error(error);
      const valueList: string[] = [];
      this.columnData = safeArray(data?.columnData).map((it) => {
        const value = it.value || it.prop;
        valueList.push(value);
        return { ...it, value: `${value}_${this.type}` };
      });
      this.tableData = safeArray(data?.tableData)
        .sort((a: any, b: any) => b?.createTime - a?.createTime)
        .map((it) => {
          it.createTime = timeFormat(it.createTime);
          Object.keys(it).forEach((key) => {
            if (!valueList.includes(key)) return;
            it[`${key}_${this.type}`] = it[key];
          });
          return it;
        });
      this.pageData.total = data?.pageData?.total || 0;
    } finally {
      this.loading = false;
    }
  }
  /* 类型 */
  handleTypeChange() {
    this.pageData.currentPage = 1;
    this.getRelationList();
  }
  /* 分页 */
  handlePageChange(currentPage: number, pageSize: number) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    this.getRelationList();
  }
  /* 状态颜色 */
  getStatusColor(status: string) {
    return {
      上线: 'yellow',
      上线中: 'yellow',
      发布: 'green',
      发布中: 'green',
      开发: ''
    }[status];
  }
  handleDownload() {
    downloadTopicRelation(this.type, this.data.id);
  }
}
</script>
<style lang="scss" scoped>
.relation-dialog {
  ::v-deep .el-dialog__body {
    padding: 0;
    height: calc(70vh - 108px);
    overflow: hidden;
  }
  &-header {
    padding: 10px 20px;
    text-align: right;
  }
}
</style>
