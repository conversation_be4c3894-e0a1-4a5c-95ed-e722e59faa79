import Vue from 'vue';
import { BsDialog } from 'bs-ui-pro';
import DirDetail from './dir-detail.vue';
import DirEditing from './dir-editing.vue';
import FlowEditing from './flow-editing.vue';
import FlowMovingCopying from './flow-moving-copying.vue';
import ProjectEditing from './project-editing.vue';
import store from '@/store';
import cookie from 'js-cookie';
import i18n from '@/i18n';
const fn = (options, child) => {
  const props = Object.assign({ visible: true, size: 'medium' }, options);
  const attrs = { width: cookie.get('Language') === 'en-US' ? '800px' : '720px' }; // 兼容bs-dialog

  return new Promise<boolean>((res, rej) => {
    const dialogC = Vue.extend({
      render(h) {
        return h(BsDialog, { props, attrs, on: { close: () => close(), confirm: (done) => confirm(done) } }, [child(h)]);
      }
    });
    // 生成弹窗实例
    const dialogInstance = new dialogC({ store, i18n }).$mount();
    document.body.appendChild(dialogInstance.$el);

    const close = (v = false) => {
      dialogInstance.$el.remove();
      dialogInstance.$destroy();
      res(v);
    };
    const confirm = (done) => {
      const _done = (bol) => {
        done && done();
        bol && close(true);
      };
      // TODO: 此处需要优化
      if (
        dialogInstance.$children[0] &&
        dialogInstance.$children[0].$slots.default &&
        (dialogInstance.$children[0].$slots.default[0] as any).componentInstance.confirm
      ) {
        (dialogInstance.$children[0].$slots.default[0] as any).componentInstance.confirm(_done);
      } else {
        close();
        res(true);
      }
    };
  });
};
// 打开【查看目录详情】的弹窗
export const openDirDetailDialog = (id: string) => {
  return fn({ title: i18n.t('pa.flow.msg258'), footerVisible: false, size: 'small' }, (h) =>
    h(DirDetail, { props: { id }, slot: 'default' })
  );
};
// 打开【新建目录/新建子目录/编辑目录】弹窗
export const openDirEditingDialog = ({ id, projectName, parentId, projectId }: any) => {
  const title = id ? i18n.t('pa.flow.eDir') : parentId ? i18n.t('pa.flow.createSubDir') : i18n.t('pa.flow.createDir');
  return fn({ title, confirmLoading: true }, (h) =>
    h(DirEditing, { props: { id, projectName, parentId, projectId }, slot: 'default' })
  );
};
// 打开【新建流程/编辑流程】弹窗
export const openFlowEditingDialog = ({ id, projectId, parentId }: any) => {
  return fn({ title: id ? i18n.t('pa.flow.eFlow') : i18n.t('pa.flow.creatFlow'), confirmLoading: true }, (h) =>
    h(FlowEditing, { props: { id, projectId, parentId }, slot: 'default' })
  );
};

// 打开【流程移动/复制】弹窗
export const openFlowMovingDailog = ({ flows, type }: any) => {
  return fn({ title: type === 'move' ? i18n.t('pa.flow.mFlow') : i18n.t('pa.flow.cFlow'), confirmLoading: true }, (h) =>
    h(FlowMovingCopying, { props: { flows, type }, slot: 'default' })
  );
};

// 打开【项目新建编辑】弹窗
export const openProjectEditingDialog = (projectId?) => {
  return fn(
    { title: projectId ? i18n.t('pa.flow.editProject') : i18n.t('pa.flow.createProject'), confirmLoading: true },
    (h) => h(ProjectEditing, { props: { projectId }, slot: 'default' })
  );
};
