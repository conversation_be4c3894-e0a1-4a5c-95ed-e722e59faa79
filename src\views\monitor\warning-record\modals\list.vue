<template>
  <div class="dis">
    <bs-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :title="title"
      :visible.sync="visible"
      width="90%"
      :before-close="closeDialog"
    >
      <div class="operate-box">
        <el-button
          v-if="hasFeatureAuthority('PA.MONITOR.WARN.RECORD_READ')"
          type="primary"
          style="margin-left: 10px; float: right"
          @click="confirmAll"
        >
          全部处理
        </el-button>
        <el-button
          v-if="hasFeatureAuthority('PA.MONITOR.WARN.RECORD_READ')"
          type="primary"
          style="margin-left: 10px; float: right"
          @click="confirmBatch"
        >
          处理
        </el-button>
        <el-button type="primary" style="margin-left: 10px; float: right" @click="getListData">
          查询
        </el-button>
        <el-date-picker
          v-model="datePicker"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="float: right"
        />
      </div>
      <bs-table
        v-loading="tableLoading"
        :height="selectedList.length ? '350' : '400'"
        selection
        :data="tableData.tableData"
        :column-data="tableData.columnData"
        :page-data="searchObj.pageData"
        @page-change="handleCurrentChange"
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
        @refresh="getListData"
      >
        <template slot="operator" slot-scope="{ row }">
          <el-tooltip content="详情" effect="light">
            <i v-if="hasAuthority(row)" class="iconfont icon-chakan" @click="detail(row)"></i>
          </el-tooltip>
        </template>
      </bs-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog(false)">关 闭</el-button>
      </span>
    </bs-dialog>
    <bs-dialog title="详细信息" :visible.sync="dialogVisible" size="medium">
      <pre class="pre-style">{{ rowInfo }}</pre>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </bs-dialog>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Watch, Emit } from 'vue-property-decorator';
import {
  URL_WARNRECORD_LISTBYRULEID,
  URL_WARNRECORD_UPDATESTATE,
  URL_WARNRECORD_UPDATEALLSTATE
} from '@/apis/commonApi';
import * as _ from 'lodash';
import moment from 'moment';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {
    'base-table': () => import('@/components/base-table.vue')
  }
})
export default class WarningRecord extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: {} }) data!: any;
  @Prop({ default: 0 }) nodeId!: number;
  title = '预警记录';

  dialogVisible = false;
  rowInfo = '';
  rowId = '';

  datePicker = '';
  pickerOptions: any = {
    shortcuts: [
      {
        text: '最近一周',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit('pick', [start, end]);
        }
      },
      {
        text: '最近一个月',
        onClick(picker) {
          const end = new Date();
          const start = moment().subtract(1, 'months').toDate();
          picker.$emit('pick', [start, end]);
        }
      },
      {
        text: '最近三个月',
        onClick(picker) {
          const end = new Date();
          const start = moment().subtract(3, 'months').toDate();
          picker.$emit('pick', [start, end]);
        }
      }
    ]
  };

  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: {
      stateType: 'ASC',
      createTime: 'DESC'
    }
  };
  tableData: ITableData = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  tableConfig: ITableConfig = {
    type: 'selection',
    width: 60,
    columnsExtend: {
      edit: [
        {
          tipMessage: '详情',
          handler: this.detail.bind(this),
          iconfont: 'icon-chakan',
          hasAuthority: this.hasAuthority.bind(this)
        }
      ]
    }
  };
  fetchList: any = _.debounce(this.getListData, 500);
  selectedList: any = [];
  handleCurrentChange(currentPage, pageSize) {
    this.searchObj.pageData.currentPage = currentPage;
    this.searchObj.pageData.pageSize = pageSize;
    this.fetchList();
  }

  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.fetchList();
  }
  hasAuthority() {
    return true;
  }
  detail(row: any) {
    this.dialogVisible = true;
    this.rowInfo = row.detailInfo;
    this.rowId = row.id;
  }
  getListData() {
    this.tableLoading = true;
    this.doPost(
      URL_WARNRECORD_LISTBYRULEID +
        (this.nodeId ? '?nodeId=' + this.nodeId : '?ruleId=' + this.data.id) +
        '&begTime=' +
        _.toString((this.datePicker && this.datePicker[0]) || '') +
        '&endTime=' +
        _.toString((this.datePicker && this.datePicker[1]) || ''),
      this.searchObj
    ).then((resp: any) => {
      this.parseResponse(resp, () => {
        const { tableData, columnData, pageData } = resp.data;
        columnData.forEach((el) => {
          el.value = el.prop;
        });
        columnData.push({ label: '操作', value: 'operator', width: 80, fixed: 'right' });
        tableData.forEach((el) => {
          el.createTime = moment(el.createTime).format('YYYY-MM-DD HH:mm:ss');
          el.confirmTime = moment(el.confirmTime).format('YYYY-MM-DD HH:mm:ss');
          el.sendState = el.sendState === 'OK' ? '发送' : '静默';
        });
        this.searchObj.pageData = pageData;
        this.tableData = {
          columnData,
          tableData
        };
      });
      this.tableLoading = false;
    });
  }
  handleSelectionChange(sel: any) {
    this.selectedList = [];
    sel.forEach((m) => {
      this.selectedList.push({
        id: m.id,
        ruleId: m.ruleId,
        stateType: 'ON'
      });
    });
  }
  confirmAll() {
    if (this.data.recordNoReadCount === 0) {
      this.$message.warning('没有需要处理的记录');
      return;
    }
    this.tableLoading = true;
    this.doGet(URL_WARNRECORD_UPDATEALLSTATE, {
      params: {
        ruleId: this.data.id
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.getListData();
      });
      this.tableLoading = false;
    });
  }
  confirmBatch() {
    if (this.selectedList.length === 0) {
      this.$message.warning('请选择记录');
      return;
    }
    this.updateState(this.selectedList, () => {
      this.getListData();
    });
  }
  confirm() {
    this.updateState(
      [
        {
          id: this.rowId,
          ruleId: this.data.id,
          stateType: 'ON'
        }
      ],
      () => {
        this.dialogVisible = false;
        this.getListData();
      }
    );
  }
  updateState(array: any, callback: () => void) {
    this.tableLoading = true;
    this.doPut(URL_WARNRECORD_UPDATESTATE, array).then((resp: any) => {
      this.parseResponse(resp, () => {
        array.forEach((item) => {
          const row: any = _.find(this.tableData.tableData, { id: item.id });
          this.$set(row, 'stateType', 'ON');
        });
        callback();
      });
      this.tableLoading = false;
    });
  }

  @Emit('close')
  private closeDialog() {
    // do nothing
  }

  @Watch('visible')
  onVisibleChange(val) {
    if (val) {
      this.getListData();
      this.datePicker = '';
    } else {
      this.selectedList = [];
    }
  }
}
</script>

<style scoped lang="scss">
.operate-box {
  overflow: hidden;
  margin-bottom: 10px;
}
.dis {
  .el-dialog__wrapper {
    margin-top: -10vh;
  }
}
.pre-style {
  overflow: auto;
  text-align: left;
  width: 100%;
  height: 600px;
  font-size: 15px;
  white-space: break-spaces;
}
</style>
