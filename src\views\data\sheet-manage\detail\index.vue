<template>
  <pro-page :active-tab="activeTab" :tabs="tabs" class="page-content" @tab-click="handleTabClick">
    <div slot="operation">
      <el-button v-if="authStatus && activeTab === '1'" type="primary" style="marginleft: 10px" @click="handleClick">
        {{ $t('pa.action.edit') }}
      </el-button>
      <el-button v-if="activeTab === '2'" type="primary" size="small" @click="exportRelationList">
        {{ $t('pa.action.downloadRelation') }}
      </el-button>
    </div>
    <!-- 表信息 -->
    <pro-grid v-if="activeTab === '1'" direction="column" :gutter="18" class="first_content">
      <!-- 表信息 -->
      <pro-grid type="info" :title="$t('pa.data.table.detail.tableInfo')" class="detail_content">
        <div class="tab-content">
          <div class="tableName item">{{ $t('pa.data.table.detail.tableName') }}：{{ detailSource.tableName }}</div>
          <div class="item">{{ $t('pa.data.table.detail.chineseName') }}：{{ detailSource.tableNameCn }}</div>
          <div class="item">{{ $t('pa.data.table.detail.businessCaliber') }}：{{ detailSource.businessExplain }}</div>
          <div class="item top" style="width: 500px; display: flex">
            <div>{{ $t('pa.creator') }}：{{ detailSource.createdBy }}</div>
            <div class="creator">{{ $t('pa.data.table.detail.creatorPhone') }}：{{ detailSource.createdByMobile }}</div>
          </div>
        </div>
      </pro-grid>
      <!-- 服务信息 -->
      <pro-grid type="info" :title="$t('pa.data.table.detail.serviceInfo')" class="detail_content">
        <div class="tab-content">
          <!-- 查看时展示 -->
          <div class="infoDetail">
            <div>{{ $t('pa.data.table.detail.serviceType') }}：{{ info.resType }}</div>
            <div v-if="info.resType === 'JDBC'">{{ $t('pa.data.table.detail.databaseType') }}：{{ info.jdbcType }}</div>
            <div>{{ $t('pa.home.service') }}：{{ detailSource.resName }}</div>
            <div v-if="configInfo.level1">{{ configInfo.level1.label }}：{{ detailSource.level1 }}</div>
            <div v-if="configInfo.level1 && configInfo.level1.type === 'cascader'">
              {{ configInfo.level1.componentCfg.level2.label }}：{{ info[configInfo.level1.componentCfg.level2.model] }}
            </div>
          </div>
          <div>{{ $t('pa.data.table.detail.serviceAddress') }}： {{ serviceUrl }}</div>
        </div>
      </pro-grid>
      <!-- 字段信息 -->
      <pro-grid type="info" :title="$t('pa.data.table.detail.tableFields')" class="detail_content">
        <div v-if="info.resType !== 'HIVE'" slot="operation" class="info-bar">
          <el-checkbox v-model="isSuperChart" :disabled="true">{{ $t('pa.data.seniorFields') }}</el-checkbox>
          <el-tooltip effect="light" placement="top" :content="$t('pa.data.table.tooltip.tooltip2')">
            <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer"></i>
          </el-tooltip>
        </div>

        <div class="tab-content">
          <Fields :list="cloneTableData" :res-type="info.resType" />
          <SuperField v-if="isSuperChart" :disabled="true" :fields="advanceChartList" :table="cloneTableData" />
        </div>
      </pro-grid>
      <!-- 连接器 -->
      <pro-grid type="info" class="detail_content">
        <div slot="title" class="title-text">
          <span>{{ $t('pa.data.table.detail.connector') }}</span>
          <el-select v-if="allProperties.length > 1" v-model="ConnectSelectValue" style="margin-left: 15px" disabled>
            <el-option
              v-for="element in allProperties"
              :key="element.id"
              :label="element.componentName"
              :value="element.id"
            />
          </el-select>
        </div>
        <div v-if="info.resType !== 'HIVE'" slot="operation" class="info-bar">
          <el-checkbox v-model="isSuperConnector">{{ $t('pa.data.table.detail.seniorConnector') }}</el-checkbox>
          <el-tooltip effect="light" placement="top" :content="$t('pa.data.table.tooltip.tooltip3')">
            <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer"></i>
          </el-tooltip>
        </div>

        <div v-if="connectList.length" class="tab-content" style="max-height: 400px; overflow: auto; padding: 20px">
          <div class="head-info">{{ $t('pa.data.table.detail.baseConnector') }}</div>
          <div class="table-list connectList">
            <div style="background: #fafdff" class="el-row-list">
              <div class="grid-content">{{ $t('pa.data.table.detail.attribute') }}</div>
              <div class="grid-content">{{ $t('pa.data.table.detail.value') }}</div>
            </div>
            <div v-if="noData" class="noData">{{ $t('pa.noData') }}</div>
            <div v-for="item in connectList" :key="item.name" :class="{ even: item.even }">
              <div v-if="!item.needHide" class="el-row-list">
                <div class="grid-content">
                  <p class="gird-content__key" :title="item.name">
                    <span v-if="item.required" style="color: red; margin-right: 2px"> * </span>
                    {{ item.name }}
                  </p>
                  <el-tooltip effect="light" :content="item.keyExplain" placement="top">
                    <div class="iconfont icon-wenhao data-card-icon"></div>
                  </el-tooltip>
                </div>
                <div class="grid-content">{{ item.defaultValue }}</div>
              </div>
            </div>
          </div>
          <div v-if="isSuperConnector">
            <div class="head-info">
              <div class="name">{{ $t('pa.data.table.detail.seniorConnector') }}</div>
            </div>
            <el-table :data="advancedConnect" class="last_table" size="mini">
              <el-table-column prop="key" :label="$t('pa.data.table.detail.connectorNature')" min-width="130">
                <template slot-scope="{ row }">
                  {{ row.key }}
                </template>
              </el-table-column>
              <el-table-column prop="value" :label="$t('pa.data.table.detail.value')" min-width="130">
                <template slot-scope="{ row }">
                  {{ row.value }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </pro-grid>
    </pro-grid>
    <!-- 引用关系 -->
    <reference-relation
      v-show="activeTab === '2'"
      :id="this.$route.query.id"
      ref="reference"
      :type="selectType"
      relation="table"
    />
    <!-- 版本管理 -->
    <SheetVersion v-show="activeTab === '3'" :share-flag="shareFlag" />
    <!-- 源码 -->
    <div v-if="activeTab === '4'" class="code-content">
      <bs-code
        :value="sqlData"
        language="sql"
        :read-only="false"
        :extra-style="extraCodeStyle"
        :extra-operations="extraOperations"
      />
    </div>
    <!-- 数据预览弹窗 -->
    <DataPreviewDialog
      v-if="showDataPreviewDialog"
      :id="id"
      :visible.sync="showDataPreviewDialog"
      :request="dataPreviewRequest"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, InjectReactive, Watch, Vue } from 'vue-property-decorator';
import { URL_CONNECTORINFO, URL_TABLE_FIND_BY_ID, URL_TABLE_RESCONF, URL_GETURL } from '@/apis/commonApi';
import ReferenceRelation from '@/views/data/sheet-manage/components/reference-relation.vue';
import { isShowFieldColumn, tabs } from './index';
import DataPreviewDialog from '../../modals/data-preview-dialog.vue';
import { dataPreviewForTable } from '@/apis/dataApi';
import { get } from '@/apis/utils/net';
import { hasPermission } from '@/utils';
import { toString } from 'lodash';
@Component({
  name: 'DataAddEdit',
  components: {
    SuperField: () => import('../add-edit/components/super-fields.vue'),
    SheetVersion: () => import('../version/index.vue'),
    Fields: () => import('./field.vue'),
    ReferenceRelation,
    DataPreviewDialog
  }
})
export default class DataAddEdit extends Vue {
  @InjectReactive('isCloud') isCloud!: boolean;

  loading = false;
  authStatus = false;
  shareFlag = false;
  info = {
    resType: '', // 服务类型
    service: '',
    jdbcType: '' //数据库类型
  };
  configInfo: any = {};
  id: any;
  ConnectSelectValue = ''; // 连接器下拉框选择值
  connectList: any = []; // 连接器字段
  serviceList: any = [];
  advanceChartList: any = [];
  advancedConnect: any = [];
  noData = false;
  chartList: any = []; // 表前缀列表
  chartListBack: any = []; //表后缀列表
  selectType = 'JOB';
  activeTab = '1';
  tabs = tabs;
  // 添加字段
  isSuperChart = false;
  isSuperConnector = false;
  cloneTableData: any = [];
  extraCodeStyle = {
    height: 'calc(100vh - 264px)'
  };
  extraOperations = [{ icon: 'el-icon-document-copy', text: this.$t('pa.action.copy'), handle: this.handleCodeCopy }];
  sqlData = '';

  // 数据预览字段
  showDataPreviewDialog = false;
  dataPreviewRequest = dataPreviewForTable;
  serviceUrl = '';

  getRelationList(val) {
    this.selectType = val;
  }
  // 根据字段判断redis连接器数据
  @Watch('cloneTableData')
  dataChange(val) {
    if (this.info.resType === 'REDIS') {
      this.connectList = isShowFieldColumn(val, this.connectList);
    }
  }
  handleCodeCopy() {
    this.$copyText(this.sqlData);
    this.$message.success(this.$t('pa.data.copySuccess'));
  }

  handleTabClick(val) {
    this.activeTab = val;
  }

  created() {
    this.onCreated();
  }

  get total() {
    return this.cloneTableData.length;
  }
  // 跳转编辑
  handleClick() {
    const { id, title } = this.$route.query;
    (this.$route as any).meta.isReplaced = true;
    this.$router
      .push({
        path: '/data/sheetEdit',
        query: { id: id, status: 'edit', title: title }
      })
      .then(() => {
        (this.$route as any).meta.isReplaced = undefined;
      });
  }

  async getResTypeList(data: any = false) {
    this.info.resType = data.resType;
    this.resTypeChange(data); // 查询服务和服务信息配置
  }

  // 查询服务
  queryService(data) {
    const { resId } = data;
    get(URL_GETURL + '?resId=' + resId).then((resp: any) => {
      this.serviceUrl = resp.data;
    });
  }

  // 所有连接器属性
  allProperties: any = [];

  parseResponse(resp: any, callback?: any) {
    const msg = toString(resp.msg);
    if (resp.success) {
      if (msg !== '') {
        this.$message.success(resp.msg);
      }
    } else {
      if (msg !== '') {
        this.$message.error(resp.msg);
      }
    }
    if (resp.success && callback) {
      callback();
    }
  }

  // 服务类型值改变时
  async resTypeChange(data: any = false) {
    await this.queryService(data);

    // 根据服务类型查询服务信息配置
    const jsonApi = URL_TABLE_RESCONF + this.info.resType.toLowerCase() + '/sql.json';
    await get(jsonApi).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.configInfo = resp.data;
        if (this.configInfo.level1 && this.configInfo.level1.type === 'cascader') {
          this.info[this.configInfo.level1.componentCfg.level2.model] =
            data[this.configInfo.level1.componentCfg.level2.model];
        }
      });
    });

    // 编辑状态下赋值
    if (data.id) {
      this.queryConnect(data);
      // 填充字段信息
      const baseFieldInfo = JSON.parse(data.baseFieldInfo);
      baseFieldInfo.forEach((item) => {
        let items: any = {
          ...item,
          partition: item.partition ? item.partition : '0'
        };
        if (this.info.resType === 'HBASE') {
          items = { ...items, columnFamily: item.columnFamily };
        }
        this.cloneTableData.push(items);
      });
    }
  }
  async queryConnect(data: any = false) {
    // 根据服务类型接口查询连接器
    let connectList: any = [];
    const api = URL_CONNECTORINFO + this.info.resType;
    await get(api).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.allProperties = resp.data;
        connectList = JSON.parse(resp.data.find((item) => item.id === data.connectorId).properties);
        this.ConnectSelectValue = data.connectorId;
        const showList: any = [];
        // 填充返回数据
        connectList.forEach((element) => {
          if (!element.needHide) {
            showList.push(element);
          }
          if (data) {
            const connectorInfo = JSON.parse(data.connectorInfo);
            if (connectorInfo && connectorInfo[element.name]) {
              element.defaultValue = connectorInfo[element.name];
            }
          }
        });
        this.noData = !showList.length;
        // 根据字段信息展示基础连接器
        this.connectList = isShowFieldColumn(this.cloneTableData, connectList);
      });
    });
    this.loading = false;
  }

  detailSource: any = {};
  async onCreated() {
    this.id = this.$route.query.id;
    this.loading = true;
    const api = URL_TABLE_FIND_BY_ID + '?id=' + this.id;
    let data: any = {};
    await get(api).then((resp: any) => {
      this.parseResponse(resp, () => {
        data = resp!.data;
      });
    });
    this.shareFlag = data.shareFlag;
    this.authStatus = hasPermission('PA.DATA.TABLE.EDIT') && !this.shareFlag;
    this.detailSource = data;
    // 对服务信息编辑时赋值
    this.getResTypeList(data);
    // sql预览信息
    this.sqlData = data.sqlEncryptContent;
    data.jdbcType && (this.info.jdbcType = data.jdbcType);
    // 填充高级 表字段
    const advanceChartList = JSON.parse(data.advanceFieldInfo);
    if (advanceChartList.length) {
      this.isSuperChart = true;
    }
    this.advanceChartList = advanceChartList;
    // 填充高级 连接器
    const advancedConnect = JSON.parse(data.advanceConnectorInfo);
    if (advancedConnect.length) {
      this.isSuperConnector = true;
    }
    advancedConnect.forEach((element) => {
      this.advancedConnect.push({
        key: Object.keys(element)[0],
        value: element[Object.keys(element)[0]]
      });
    });
    this.loading = false;
  }
  exportRelationList() {
    const relation = this.$refs['reference'] as any;
    relation.exportRelationList();
  }
}
</script>
<style scoped lang="scss">
@import './style.scss';
</style>
