<template>
  <pro-page :active-tab="activeTab" :tabs="tabs" class="page-content" @tab-click="handleTabClick">
    <div slot="operation">
      <el-button
        v-if="authStatus && status === '1'"
        type="primary"
        style="marginleft: 10px"
        @click="handleClick"
      >
        编辑
      </el-button>
      <el-button
        v-if="activeTab === '1'"
        style="marginleft: 10px"
        type="primary"
        @click="showDataPreviewDialog = true"
      >
        数据预览
      </el-button>
      <div v-if="activeTab === '2'">
        <el-select
          v-if="relationTypeList"
          v-model="selectType"
          style="margin: 0 16px"
          @change="getRelationList"
        >
          <el-option
            v-for="item in relationTypeList"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
        <el-button type="primary" size="small" @click="exportRelationList">
          下载引用关系
        </el-button>
      </div>
    </div>
    <!-- 表信息 -->
    <div v-if="activeTab === '1'" class="first_content">
      <div class="detail_content no-bg-color-content">
        <div class="tab-title"><div class="title-text">表信息</div></div>
        <div class="tab-content" style="border: none">
          <div class="tableName item">表名：{{ detailSource.tableName }}</div>
          <div class="item">中文名：{{ detailSource.tableNameCn }}</div>
          <div class="item">业务口径：{{ detailSource.businessExplain }}</div>
          <div class="item top" style="width: 500px; display: flex">
            <div>创建人：{{ detailSource.createdBy }}</div>
            <div class="creator">创建人电话：{{ detailSource.createdByMobile }}</div>
          </div>
        </div>
      </div>
      <!-- 服务信息 -->
      <div class="detail_content no-bg-color-content">
        <div class="tab-title"><div class="title-text">服务信息</div></div>
        <div class="tab-content">
          <!-- 查看时展示 -->
          <div v-if="configInfo.level1" class="infoDetail">
            <div>服务类型：{{ info.resType }}</div>
            <div>服务：{{ detailSource.resName }}</div>
            <div>{{ configInfo.level1.label }}：{{ detailSource.level1 }}</div>
            <div v-if="configInfo.level1 && configInfo.level1.type === 'cascader'">
              {{ configInfo.level1.componentCfg.level2.label }}：{{
                info[configInfo.level1.componentCfg.level2.model]
              }}
            </div>
          </div>
          <div>服务地址： {{ serviceUrl }}</div>
        </div>
      </div>
      <!-- 字段信息 -->
      <div class="detail_content no-bg-color-content">
        <div class="tab-title">
          <div class="info">
            <div class="title-text">表字段</div>
            <div v-if="info.resType !== 'HIVE'" class="info-bar">
              <el-checkbox v-model="isSuperChart">高级字段</el-checkbox>
              <el-tooltip
                effect="light"
                placement="top"
                :content="'flink内置字段，处理时间、水位线等'"
              >
                <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer"></i>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="tab-content">
          <Fields :list="cloneTableData" :res-type="info.resType" />
          <SuperField
            v-if="isSuperChart"
            ref="superFields"
            :fields="advanceChartList"
            :table="cloneTableData"
            :status="status"
          />
        </div>
      </div>
    </div>
    <!-- 连接器 -->
    <div v-if="activeTab === '1'" class="detail_content" style="margin-bottom: 0px">
      <div class="no-bg-color-content">
        <div class="tab-title">
          <div class="title-text">
            <span>连接器</span>
            <el-select
              v-if="allProperties.length > 1"
              v-model="ConnectSelectValue"
              style="margin-left: 15px"
              disabled
            >
              <el-option
                v-for="element in allProperties"
                :key="element.id"
                :label="element.componentName"
                :value="element.id"
              />
            </el-select>
          </div>
          <div v-if="info.resType !== 'HIVE'" class="info-bar">
            <el-checkbox v-model="isSuperConnector">高级连接器</el-checkbox>
            <el-tooltip effect="light" placement="top" :content="'连接器高级属性配置'">
              <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer"></i>
            </el-tooltip>
          </div>
        </div>
        <div
          v-if="connectList.length"
          class="tab-content no-bg-color-content"
          style="max-height: 400px; overflow: auto; padding: 20px"
        >
          <div class="head-info">基础连接器</div>
          <div class="table-list connectList">
            <div style="background: #fafdff" class="el-row-list">
              <div class="grid-content">属 性</div>
              <div class="grid-content">值</div>
            </div>
            <div v-if="noData" class="noData">暂无数据</div>
            <div v-for="item in connectList" :key="item.name" :class="{ even: item.even }">
              <div v-if="!item.needHide" class="el-row-list">
                <div class="grid-content">
                  <p style="text-align: center; min-width: 150px">
                    <span v-if="item.required" style="color: red; margin-right: 2px"> * </span>
                    {{ item.name }}
                  </p>
                  <el-tooltip effect="light" :content="item.keyExplain" placement="top">
                    <div class="iconfont icon-wenhao data-card-icon"></div>
                  </el-tooltip>
                </div>
                <div class="grid-content">{{ item.defaultValue }}</div>
              </div>
            </div>
          </div>
          <div v-if="isSuperConnector">
            <div class="head-info">
              <div class="name">高级连接器</div>
            </div>
            <el-table :data="advancedConnect" class="last_table" size="mini">
              <el-table-column prop="key" label="连接器属性" min-width="130">
                <template slot-scope="{ row }">
                  {{ row.key }}
                </template>
              </el-table-column>
              <el-table-column prop="value" label="值" min-width="130">
                <template slot-scope="{ row }">
                  {{ row.value }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <!-- 引用关系 -->
    <reference-relation
      v-show="activeTab === '2'"
      :id="this.$route.query.id"
      ref="reference"
      :type="selectType"
      relation="table"
    />
    <!-- 版本管理 -->
    <SheetVersion v-show="activeTab === '3'" />
    <!-- 源码 -->
    <div v-if="activeTab === '4'" class="code-content">
      <bs-code
        :value="sqlData"
        language="sql"
        :read-only="false"
        :extra-style="extraCodeStyle"
        :extra-operations="extraOperations"
      />
    </div>
    <!-- 数据预览弹窗 -->
    <DataPreviewDialog
      v-if="showDataPreviewDialog"
      :id="id"
      :visible.sync="showDataPreviewDialog"
      :request="dataPreviewRequest"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import {
  URL_CONNECTORINFO,
  URL_TABLE_FIND_BY_ID,
  URL_TABLE_RESCONF,
  URLLISTRES
} from '@/apis/commonApi';
import ReferenceRelation from '@/views/data/sheet-manage/components/reference-relation.vue';
import { isShowFieldColumn, relationTypeList, tabs } from './index';
import ClipboardJS from 'clipboard';
import DataPreviewDialog from '../../modals/data-preview-dialog.vue';
import { dataPreviewForTable } from '@/apis/dataApi';
@Component({
  name: 'DataAddEdit',
  components: {
    SuperField: () => import('./superFiels.vue'),
    SheetVersion: () => import('../version/index.vue'),
    Fields: () => import('./field.vue'),
    ReferenceRelation,
    DataPreviewDialog
  }
})
export default class DataAddEdit extends PaBase {
  status: any = '1';
  loading = false;
  authStatus = false;
  info = {
    resType: '', // 服务类型
    service: ''
  };
  configInfo: any = {};
  id: any;
  ConnectSelectValue = ''; // 连接器下拉框选择值
  connectList: any = []; // 连接器字段
  serviceList: any = [];
  advanceChartList: any = [];
  advancedConnect: any = [];
  noData = false;
  chartList: any = []; // 表前缀列表
  chartListBack: any = []; //表后缀列表
  selectType = 'JOB';
  relationTypeList = relationTypeList;
  activeTab = '1';
  tabs = tabs;
  // 添加字段
  isSuperChart = false;
  isSuperConnector = false;
  cloneTableData: any = [];
  extraCodeStyle = {
    height: 'calc(100vh - 264px)'
  };
  extraOperations = [{ icon: 'el-icon-document-copy', text: '复制' }];
  clipboard: any = null;
  sqlData = '';

  // 数据预览字段
  showDataPreviewDialog = false;
  dataPreviewRequest = dataPreviewForTable;
  serviceUrl = '';

  getRelationList(val) {
    this.selectType = val;
  }
  // 根据字段判断redis连接器数据
  @Watch('cloneTableData')
  dataChange(val) {
    if (this.info.resType === 'REDIS') {
      this.connectList = isShowFieldColumn(val, this.connectList);
    }
  }
  // 初始化剪切版
  initClipboard() {
    if (this.clipboard) return;
    this.$nextTick(() => {
      const target = document.querySelector('.el-icon-document-copy')!.parentNode as Element;
      this.clipboard = new ClipboardJS(target, {
        text: () => {
          return this.sqlData;
        }
      });
      this.clipboard.on('success', (e) => {
        this.$message.success('复制成功！');
        e.clearSelection();
      });
      this.clipboard.on('error', (e) => {
        this.$message.error('复制不成功，请重新复制！');
        e.clearSelection();
      });
    });
  }
  handleTabClick(val) {
    this.activeTab = val;
    val === '4' && this.initClipboard();
  }

  created() {
    this.onCreated();
  }

  get total() {
    return this.cloneTableData.length;
  }
  // 跳转编辑
  handleClick() {
    const { id, title } = this.$route.query;
    this.$router.push({
      path: '/data/sheetEdit',
      query: { id: id, status: '2', title: title }
    });
  }

  async getResTypeList(data: any = false) {
    this.info.resType = data.resType;
    this.resTypeChange(data); // 查询服务和服务信息配置
  }

  // 查询服务
  async queryService(data) {
    const { resName } = data;
    await this.doPost(URLLISTRES + this.info.resType).then((resp: any) => {
      const niu = resp.data.find((item) => item.title === resName);
      this.serviceUrl = niu && niu.url;
    });
  }

  // 所有连接器属性
  allProperties: any = [];

  // 服务类型值改变时
  async resTypeChange(data: any = false) {
    await this.queryService(data);

    // 根据服务类型查询服务信息配置
    const jsonApi = URL_TABLE_RESCONF + this.info.resType.toLowerCase() + '/sql.json';
    await this.doGet(jsonApi).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.configInfo = resp.data;
        if (this.configInfo.level1 && this.configInfo.level1.type === 'cascader') {
          this.info[this.configInfo.level1.componentCfg.level2.model] =
            data[this.configInfo.level1.componentCfg.level2.model];
        }
      });
    });

    // 编辑状态下赋值
    if (data.id) {
      this.queryConnect(data);
      // 填充字段信息
      const baseFieldInfo = JSON.parse(data.baseFieldInfo);
      baseFieldInfo.forEach((item) => {
        let items: any = {
          ...item,
          partition: item.partition ? item.partition : '0'
        };
        if (this.info.resType === 'HBASE') {
          items = { ...items, columnFamily: item.columnFamily };
        }
        this.cloneTableData.push(items);
      });
    }
  }
  async queryConnect(data: any = false) {
    // 根据服务类型接口查询连接器
    let connectList: any = [];
    const api = URL_CONNECTORINFO + this.info.resType;
    await this.doGet(api).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.allProperties = resp.data;
        connectList = JSON.parse(resp.data.find((item) => item.id === data.connectorId).properties);
        this.ConnectSelectValue = data.connectorId;
        // 填充返回数据
        connectList.forEach((element) => {
          if (data) {
            const connectorInfo = JSON.parse(data.connectorInfo);
            if (connectorInfo && connectorInfo[element.name]) {
              element.defaultValue = connectorInfo[element.name];
            }
          }
        });
        // 根据字段信息展示基础连接器
        this.connectList = isShowFieldColumn(this.cloneTableData, connectList);
      });
    });
    this.loading = false;
  }

  detailSource: any = {};
  async onCreated() {
    this.id = this.$route.query.id;
    this.loading = true;
    const api = URL_TABLE_FIND_BY_ID + '?id=' + this.id;
    let data: any = {};
    await this.doGet(api).then((resp: any) => {
      this.parseResponse(resp, () => {
        data = resp!.data;
      });
    });
    this.authStatus =
      data.authStatus === 'YES' && this.hasFeatureAuthority('PA.DATA.TABLE.EDIT') ? true : false;
    this.detailSource = data;
    // 对服务信息编辑时赋值
    this.getResTypeList(data);
    // sql预览信息
    this.sqlData = data.sqlEncryptContent;

    // 填充高级 表字段
    const advanceChartList = JSON.parse(data.advanceFieldInfo);
    if (advanceChartList.length) {
      this.isSuperChart = true;
    }
    this.advanceChartList = advanceChartList;
    // 填充高级 连接器
    const advancedConnect = JSON.parse(data.advanceConnectorInfo);
    if (advancedConnect.length) {
      this.isSuperConnector = true;
    }
    advancedConnect.forEach((element) => {
      this.advancedConnect.push({
        key: Object.keys(element)[0],
        value: element[Object.keys(element)[0]]
      });
    });
    this.loading = false;
  }
  exportRelationList() {
    const relation = this.$refs['reference'] as any;
    relation.exportRelationList();
  }
}
</script>
<style scoped lang="scss">
@import './style.scss';
</style>
