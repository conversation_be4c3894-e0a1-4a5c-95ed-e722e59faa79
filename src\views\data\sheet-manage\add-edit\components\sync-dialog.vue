<template>
  <bs-dialog :title="$t('pa.data.udf.detail.fieldSync')" size="large" :visible.sync="display" class="sync-dialog">
    <div class="operator-bar">
      <bs-search
        v-model="keyword"
        :placeholder="$t('pa.data.table.detail.placeholder.fieldPlaceholder1')"
        @change="handleSearch"
      />
    </div>
    <!-- table -->
    <bs-table
      v-loading="loading"
      crossing
      selection
      size="mini"
      paging-front
      :height="height"
      :data="tableData"
      :page-data="pageData"
      :column-settings="false"
      :column-data="columnData"
      :selectable="(row) => !row.disabled"
      @page-change="handlePageChange"
      @selection-change="handleSelectionChange"
    >
      <template slot="header-fieldName" slot-scope="{ row }">
        <span style="color: red; margin-right: 4px">*</span>{{ row.label }}
      </template>
      <template slot="header-fieldType" slot-scope="{ row }">
        <span style="color: red; margin-right: 4px">*</span>{{ row.label }}
      </template>
    </bs-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="display = false">{{ $t('pa.action.close') }}</el-button>
      <el-button type="primary" :disabled="!checkedRows.length" @click="handleSync">{{ $t('pa.flow.confirm') }}</el-button>
      <el-button type="primary" @click="handleAllSync">{{ $t('pa.data.udf.detail.SyncAll') }}</el-button>
    </span>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, PropSync, Prop, Vue } from 'vue-property-decorator';
import { getAutoSyncTable } from '@/apis/dataApi';
import { includesPro, safeArray } from '@/utils';
// 将数据库中的字段同步到表里：和用户自定义的一致的字段不同步
@Component
export default class SyncDialog extends Vue {
  @PropSync('show', { required: true, default: false }) display!: boolean;
  @Prop({ default: () => null }) data!: any;
  @Prop({ default: () => [] }) showList!: any[];

  loading = false;
  keyword = '';
  tableData: any = [];
  checkedRows: any[] = [];
  rawTableData: any[] = [];
  pageData = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 };

  get height() {
    return this.checkedRows.length ? 'calc(70vh - 283px)' : 'calc(70vh - 208px)';
  }
  get isHbase() {
    return this.data?.resType === 'HBASE';
  }

  get columnData() {
    return [
      { label: this.$t('pa.data.table.detail.fieldName'), value: 'fieldName' },
      { label: this.$t('pa.data.table.detail.fieldType'), value: 'fieldType' },
      this.isHbase && { label: this.$t('pa.data.table.detail.columnCluster'), value: 'columnFamily' }
    ].filter(Boolean);
  }

  async created() {
    try {
      this.loading = true;
      await this.getTableData();
    } finally {
      this.loading = false;
    }
  }

  async getTableData() {
    const { success, data, error } = await getAutoSyncTable(this.data);
    if (!success) return this.$message.error(error);
    this.rawTableData = safeArray(data).map((it) => {
      const disabled = this.showList.some((el) => {
        return this.isHbase
          ? el.fieldName === it.fieldName && el.columnFamily === it.columnFamily
          : el.fieldName === it.fieldName;
      });
      return { ...it, disabled };
    });
    this.handleSearch();
    // const table = data.map((item: any) => {
    //   const disabled = !!this.showList.find((tar: any) => {
    //     return this.isHbase
    //       ? tar.fieldName === item.fieldName && tar.columnFamily === item.columnFamily
    //       : tar.fieldName === item.fieldName;
    //   });
    //   return Object.assign(item, { disabled });
    // });
  }
  handleSearch() {
    this.tableData = this.rawTableData.filter(({ fieldName }) => includesPro(fieldName, this.keyword));
    this.pageData.total = this.tableData.length;
    this.pageData.currentPage = 1;
  }
  handlePageChange(curPage, pageSize) {
    this.pageData.currentPage = curPage;
    this.pageData.pageSize = pageSize;
  }
  handleSelectionChange(rows: any[]) {
    this.checkedRows = rows;
  }
  handleSync() {
    this.$emit('sync', this.checkedRows);
    this.display = false;
  }
  handleAllSync() {
    this.$emit(
      'sync',
      this.rawTableData.filter(({ disabled }) => !disabled)
    );
    this.display = false;
  }
}
</script>
<style lang="scss" scoped>
.sync-dialog {
  .operator-bar {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 50px;
    padding-right: 20px;
  }
  ::v-deep .el-dialog {
    &__body {
      padding: 0;
    }
  }
}
</style>
