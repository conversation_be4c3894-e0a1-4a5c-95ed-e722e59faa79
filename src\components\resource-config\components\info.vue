<template>
  <div>
    <el-form-item v-for="el in renderList" :key="el.name" :prop="el.name" :label="el.label" :rules="rules[el.name]">
      <div
        :class="[
          'resource-item',
          el.tip ? 'resource-item--tip' : '',
          el.name === 'originalJobName' ? 'resource-item__name' : ''
        ]"
      >
        <!-- 流程名称前缀 -->
        <bs-select
          v-if="el.name === 'originalJobName'"
          v-model="form.prefix"
          class="resource-item__name--prefix"
          :placeholder="$t('pa.flow.placeholder45')"
          clearable
          disabled
          :options="prefixList"
        />
        <!-- input -->
        <el-input
          v-if="el.type === 'textarea' || el.type === 'text'"
          v-model="form[el.name]"
          autocomplete="off"
          :type="el.type"
          :maxlength="el.maxlength"
          disabled
          show-word-limit
          clearable
          :placeholder="el.placeholder"
        />
        <!-- 流程名称后缀 -->
        <bs-select
          v-if="el.name === 'originalJobName'"
          v-model="form.suffix"
          class="resource-item__name--suffix"
          :placeholder="$t('pa.flow.placeholder46')"
          clearable
          disabled
          :options="suffixList"
        />
        <!-- 父级目录 -->
        <bs-cascader
          v-if="el.name === 'projectId'"
          v-model="form[el.name]"
          :options="dirOptions"
          :props="{ checkStrictly: true, emitPath: false }"
          disabled
          style="width: 100%"
        >
          <template slot-scope="{ node, data }">
            <span>{{ data.label }}</span>
            <bs-tag v-if="node.level === 1" style="margin-left: 4px">{{ $t('pa.flow.rDir') }}</bs-tag>
          </template>
        </bs-cascader>
      </div>
    </el-form-item>
  </div>
</template>

<script lang="ts">
import { Component, PropSync, Vue } from 'vue-property-decorator';
import { getItemListByType } from '@/apis/dataApi';
import { getProjectTree } from '@/apis/flowNewApi';

@Component
export default class InfoConfig extends Vue {
  @PropSync('data', { type: Object, default: () => ({}) }) form!: any;

  private renderList: any[] = [
    {
      name: 'originalJobName',
      label: this.$t('pa.name'),
      type: 'text',
      maxlength: 30,
      placeholder: this.$t('pa.flow.placeholder8')
    },
    {
      name: 'jobType',
      label: this.$t('pa.flow.category'),
      type: 'text',
      placeholder: this.$t('pa.flow.placeholder9'),
      disabled: true
    },
    {
      name: 'projectId',
      label: this.$t('pa.flow.fDir'),
      type: 'cascader'
    },
    {
      name: 'memo',
      label: this.$t('pa.notes'),
      type: 'textarea',
      maxlength: 255,
      placeholder: this.$t('pa.flow.placeholder10')
    }
  ]; // 渲染列表
  private prefixList = []; // 名称前缀列表
  private suffixList = []; // 名称后缀列表

  private rules: any = {
    originalJobName: [{ required: true, message: this.$t('pa.flow.msg31'), trigger: 'blur' }],
    projectId: [{ required: true, message: this.$t('pa.flow.placeholder44') }]
  };
  dirOptions = [];

  created() {
    this.getPrefixAndSuffixList(true);
    this.getPrefixAndSuffixList();
    this.getProjectTree();
  }

  // 获取前缀、后缀列表
  async getPrefixAndSuffixList(isPrefix = false) {
    const { data, success, error, msg } = await getItemListByType({ itemType: `JOB_${isPrefix ? 'PRE' : 'SUF'}` });
    if (!success) return this.$message.error(error || msg);
    this[isPrefix ? 'prefixList' : 'suffixList'] = data.map((el) => ({ label: el, value: el }));
  }
  // 获取项目下的目录树
  async getProjectTree() {
    const { data = [] } = await getProjectTree({ rootProjectId: this.$route.query.id || this.form.projectId });
    const transform = (data) => {
      return data.map(({ nodeId, nodeName, children }) => ({
        value: nodeId,
        label: nodeName,
        children: Array.isArray(children) && children.length ? transform(children) : undefined
      }));
    };
    this.dirOptions = transform([data]);
  }
}
</script>
