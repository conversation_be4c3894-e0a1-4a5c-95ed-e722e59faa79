<template>
  <div :class="containerClass">
    <node-type v-for="(el, index) in list" :key="index" :data="el" :direction="dir[index]" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import NodeType from './node.vue';

@Component({ components: { NodeType } })
export default class ListItem extends Vue {
  @Prop({ default: 0 }) index!: number;
  @Prop({ default: () => [] }) data!: any;

  get isLeftArrow() {
    return !(this.index % 2);
  }
  get dir() {
    return this.isLeftArrow ? ['left', 'left', 'bottom'] : ['bottom', 'right', 'right'];
  }
  get list() {
    return this.isLeftArrow ? this.data : this.data.reverse();
  }

  get containerClass() {
    return {
      list__container: true,
      'list__container--right': !this.isLeftArrow,
      'list__container--min': this.data.length < 3
    };
  }
}
</script>
<style lang="scss" scoped>
.list {
  &__container {
    display: flex;
    justify-content: flex-start;
    &--right {
      justify-content: flex-end;
    }
    &--min {
      height: 34px;
    }
  }
}
</style>
