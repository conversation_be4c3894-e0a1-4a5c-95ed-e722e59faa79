<template>
  <pro-grid type="info" style="margin-bottom: 20px; width: 100%">
    <div slot="title" :class="['view-tab-title', 'choose-title', isTabelError ? 'error' : '']">
      <div>选择表</div>
      <el-cascader
        v-show="status !== '2'"
        ref="cascader"
        v-model="tableValue"
        :options="tableList"
        style="margin-left: 20px; width: 340px"
        :props="{ multiple: true }"
        collapse-tags
        @change="handleTableChange"
      />
      <span v-if="isTabelError" class="error-tips">请选择表</span>
    </div>
    <div :class="['tab-content', isError ? 'error' : '']">
      <div class="tab-content__title">
        <div style="flex: 1; disply: flex; align-items: center">
          <span>基础字段</span>
          <el-input
            v-model="searchValue"
            placeholder="输入字段名模糊搜索"
            style="margin-left: 10px; width: 300px"
            @input="searchField"
          >
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>
        <el-button type="primary" size="small" @click="resetFieldList"> 重置 </el-button>
        <el-button type="primary" size="small" @click="delFieldList(null)"> 删除 </el-button>
      </div>
      <el-table
        class="base-field-table"
        :data="showFieldList"
        style="width: 100%; max-height: 400px; overflow: auto; margin-bottom: 20px"
        @selection-change="handleFieldSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <!-- 列蔟 -->
        <el-table-column
          v-if="resType === 'HBASE'"
          prop="columnFamily"
          label="列簇"
          min-width="130"
        />
        <el-table-column prop="fieldName" label="字段名" min-width="130" />
        <el-table-column prop="fieldNameCn" label="中文名" min-width="130" />
        <el-table-column prop="fieldType" label="字段类型" min-width="130" />
        <el-table-column prop="primaryKey" label="主键" min-width="130">
          <template slot-scope="{ row }">
            <el-switch
              v-model="row.primaryKey"
              active-value="1"
              inactive-value="0"
              :disabled="true"
            />
          </template>
        </el-table-column>
        <el-table-column prop="partition" label="分区" min-width="130">
          <template slot-scope="{ row }">
            <el-switch
              v-model="row.partition"
              active-value="1"
              inactive-value="0"
              :disabled="true"
            />
          </template>
        </el-table-column>
        <el-table-column prop="businessExplain" label="业务口径" min-width="130" />
        <el-table-column prop="action" label="操作" width="100" align="center">
          <div slot-scope="scope">
            <i class="delicon iconfont icon-shanchu" @click="delFieldList(scope.row)"></i>
          </div>
        </el-table-column>
      </el-table>
      <p class="error-tips">{{ errorMsg }}</p>
      <el-pagination
        style="text-align: right"
        :current-page="page.currentPage"
        :page-sizes="pageSizes"
        :total="fieldList.length"
        layout="total, prev, pager, next, sizes, jumper"
        :page-size="page.pageSize"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <div v-if="showAdvancedFieldList">
        <p class="tab-content__title">高级字段</p>
        <el-table class="advanced-field-table" :data="advancedFieldList">
          <el-table-column prop="value" label="表字段" />
        </el-table>
      </div>
    </div>
  </pro-grid>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { getSearchableTables, getTableInfo } from '@/apis/dataApi';
import { cloneDeep } from 'lodash';
@Component({})
export default class FieldTable extends Vue {
  // 操作类型
  @Prop() value!: any[];
  @Prop() status!: '0' | '1' | '2';
  @Prop() data!: any;
  tableValue: any[] = [];
  // 数据表列表
  tableList: any = [];
  // 表字段列表
  fieldList: any[] = [];
  // 源表字段列表 用于重置
  sourceFieldList: any[] = [];
  // 勾选字段信息
  selectedFields = [];
  // 高级表字段列表
  advancedFieldList: any = [];
  // 字段分页信息
  page = {
    currentPage: 1,
    pageSize: 10
  };
  pageSizes = [10, 20, 40, 80, 100];
  // 服务类型
  resType = '';
  // 显示数据表错误
  isTabelError = false;
  // 显示字段错误
  isError = false;
  errorMsg = '';
  // 是否是初次进入该组件
  isInit = true;
  get showAdvancedFieldList() {
    if (this.status === '2') {
      if (this.advancedFieldList.length > 0) return true;
      return false;
    }
    return true;
  }
  // 用于显示前端分页展示的数据
  get showFieldList() {
    const { currentPage, pageSize } = this.page;
    const start = (currentPage - 1) * pageSize;
    const end = currentPage * pageSize;
    return this.fieldList.slice(start, end);
  }

  // 输入查询字段值
  searchValue = '';
  allTableData: any = [];
  // 查询字段
  searchField(val) {
    if (!this.allTableData.length) {
      this.allTableData = cloneDeep(this.fieldList);
    }
    this.searchValue = val;
    if (val) {
      this.fieldList = this.allTableData.filter((item) => {
        return item.fieldName && item.fieldName.includes(val);
      });
    } else {
      this.fieldList = cloneDeep(this.allTableData);
    }
  }
  @Watch('fieldList.length')
  handleFieldListChange() {
    this.$emit('field-change', [...this.fieldList]);
  }
  @Watch('value', { immediate: true })
  handleValueChange(val) {
    this.tableValue = val;
    this.resType = (val[0] ? val[0][0] : '').toUpperCase();
    if (this.isInit) {
      this.queryFieldList();
    }
  }
  created() {
    // 查询多选表
    this.queryTableList();
  }
  // 设置错误状态
  setError(type = 'field', msg = '') {
    if (type === 'field') {
      this.isError = true;
      this.errorMsg = msg;
    } else {
      this.isTabelError = true;
    }
  }
  // 移除错误
  removeError(type = 'field') {
    if (type === 'field') {
      this.isError = false;
      this.errorMsg = '';
    } else {
      this.isTabelError = false;
    }
  }
  // 获取数据表列表
  async queryTableList() {
    await getSearchableTables().then((resp: any) => {
      this.tableList = resp.data;
    });
  }
  // 选择数据表回调
  handleTableChange(val) {
    this.searchValue = '';
    this.allTableData = [];
    const items: any[] = [];
    // 已选表为空 清空字段列表
    if (val.length === 0) {
      this.setError('table');
      this.fieldList = [];
      return;
    }
    if (val.length > 1) {
      this.tableValue.forEach((item) => {
        if (item[0] !== this.resType) {
          items.push(item);
        }
      });
      items.length && (this.tableValue = items);
    }
    this.removeError('table');
    this.$emit('update:value', this.tableValue);
    this.resType = (this.tableValue[0] ? this.tableValue[0][0] : '').toUpperCase();
    this.queryFieldList();
  }
  // 获取字段列表
  queryFieldList() {
    // 重置页码
    this.page.currentPage = 1;
    if (!this.tableValue.length) {
      this.fieldList = [];
      return;
    }
    // 初始进入从详情接口获取数据 新建状态不需要
    if (this.isInit && this.status !== '0') {
      const { baseFieldInfo = '', advanceFieldInfo = '' } = this.data || {};
      const fields: any = JSON.parse(baseFieldInfo || '[]');
      this.fieldList = fields;
      this.sourceFieldList = [...fields];
      this.advancedFieldList = JSON.parse(advanceFieldInfo || '[]');
      this.isInit = false;
      return;
    } else {
      this.isInit = false;
    }
    const ids = this.tableValue.map((item) => item[1]);
    return getTableInfo(ids).then((resp: any) => {
      // 获取基础字段和高级字段列表
      const fields: any = resp.data[0].value || [];
      this.fieldList = fields;
      this.sourceFieldList = [...fields];
      this.advancedFieldList = resp.data[1].value || [];
      if (this.sourceFieldList.length || this.advancedFieldList.length) {
        this.removeError();
      }
    });
  }
  // 重置表字段列表
  resetFieldList() {
    this.$confirm('重置后将会恢复初始状态，你确定进行重置吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      // 此处不需要改动字段信息所以不做深层拷贝
      this.fieldList = [...this.sourceFieldList];
    });
  }
  // 删除表字段列表
  delFieldList(row: any) {
    if (!row && this.selectedFields.length === 0) {
      this.$message.warning('请选择需要删除的字段');
      return;
    }
    const names =
      (row && [row.fieldName]) || this.selectedFields.map((field: any) => field.fieldName);
    this.$confirm(`确认要删除${names.join(',')}吗`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const { selectedFields, fieldList } = this;
      // row存在表示单个删除
      if (row) {
        fieldList.splice(fieldList.indexOf(row), 1);
        return;
      }
      // 全选直接清空
      if (selectedFields.length === fieldList.length) {
        this.fieldList = [];
        return;
      }
      selectedFields.forEach((item) => {
        fieldList.splice(fieldList.indexOf(item), 1);
      });
    });
  }
  // 选择字段回调
  handleFieldSelectionChange(rows) {
    this.selectedFields = rows;
  }
  handleSizeChange(val) {
    this.page.pageSize = val;
    this.page.currentPage = 1;
  }
  handleCurrentChange(val) {
    this.page.currentPage = val;
  }
}
</script>
<style lang="scss" scoped>
.view-tab-title {
  display: flex;
  align-items: center;
}
.base-field-table {
  border: 1px solid $--bs-color-border-lighter;
  border-bottom: none;
}
.tab-content {
  width: 100%;
  border-top: 1px solid $--bs-color-border-lighter;
  padding: 20px 25px;
  .tab-content__title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
}
.error-tips {
  display: none;
  color: $--bs-color-red;
  padding-top: 4px;
  font-weight: normal;
}
.view-tab-title.error {
  ::v-deep .el-cascader .el-input__inner {
    border: 1px solid $--bs-color-red;
  }
  .error-tips {
    display: block;
    margin-left: 10px;
  }
}
.tab-content.error {
  ::v-deep .el-table.base-field-table {
    border: 1px solid $--bs-color-red;
  }
  .error-tips {
    display: block;
    margin-top: -20px;
  }
}
.delicon {
  cursor: pointer;
}
.depentTable {
  padding-left: 10px;
}
.advanced-field-table {
  border: 1px solid $--bs-color-border-lighter;
  border-bottom: none;
  width: 100%;
  max-height: 400px;
  overflow: auto;
}
</style>
