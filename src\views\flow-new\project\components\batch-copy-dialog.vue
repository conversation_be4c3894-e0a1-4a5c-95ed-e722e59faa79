<template>
  <bs-dialog
    v-loading="loading"
    size="medium"
    :title="isCopy ? $t('pa.flow.copy') : $t('pa.flow.move')"
    append-to-body
    class="batch-copy-dialog"
    :visible.sync="display"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="batch-copy-head">
      <span class="headTitle">{{ headTitle }}</span>
    </div>
    <div class="batch-copy-content">
      <bs-transfer
        ref="transfer"
        v-model="copyList"
        :titles="[$t('pa.flow.selectableProject'), $t('pa.flow.selectedProject')]"
        :data="data"
        filterable
        check-change
        target-order="unshift"
      />
    </div>
    <!-- 底部 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="display = false">{{ $t('pa.flow.cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('pa.flow.save') }}</el-button>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, PropSync } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { batchCopy, batchMove, getProjectList } from '@/apis/flowNewApi';

@Component({
  components: {}
})
export default class BatchOnlineDialog extends PaBase {
  @Prop({ type: Array, default: () => [] }) list!: any[];
  @Prop({ type: Boolean, default: true }) isCopy!: boolean;
  @PropSync('show', { type: Boolean, default: true }) display!: boolean;
  private loading = false;
  copyList = [];
  data: any = [];
  headTitle = '';
  created() {
    this.getHead();
    this.getData();
  }
  getHead() {
    const headTitle =
      this.$t('pa.will') +
      this.list
        .slice(0, 3)
        .map(({ jobName }) => {
          return `【${jobName}】`;
        })
        .join('、') +
      `${this.list.length > 3 ? '...' : ''}` +
      `${this.list.length}个流程${this.isCopy ? this.$t('pa.flow.copy') : this.$t('pa.flow.move')}到已选项目`;
    this.headTitle = headTitle;
  }
  async getData() {
    this.loading = true;
    const { data, success, msg, error } = await getProjectList({
      name: '',
      sortByName: false
    });
    if (success) {
      const projects: any = [];
      data.forEach((project) => {
        projects.push({
          label: project.projectName,
          key: project.projectId
        });
      });
      this.data = projects;
      this.loading = false;
    } else {
      this.loading = false;
      this.$message.error(error || msg);
    }
  }
  async submit() {
    if (this.copyList.length > 0) {
      const jobIds: any = this.list.map((e: any) => (e.paJob ? e.paJob.id : e.id));
      const params: any = {
        jobIds: jobIds,
        projectIds: this.copyList
      };
      this.loading = true;
      const res = this.isCopy ? await batchCopy(params) : await batchMove(params);
      this.loading = false;
      if (res.success) {
        this.$tip.success(res.msg);
        this.closeDialog();
      } else {
        this.$tip.error({ message: res.msg, duration: 5000 });
      }
    } else {
      this.$tip.warning(
        this.$t('pa.pleaseSelect') +
          (this.isCopy ? this.$t('pa.flow.copy') : this.$t('pa.flow.move')) +
          this.$t('pa.someProject')
      );
    }
  }
  closeDialog() {
    this.$emit('close');
    this.display = false;
  }
}
</script>

<style lang="scss" scoped>
.batch-copy {
  /* 弹窗 */
  &-dialog {
    top: 50%;
    left: 50%;
    right: unset;
    bottom: unset;
    transform: translate(-50%, -50%);

    ::v-deep .el-dialog {
      margin: auto !important;

      &__body {
        padding: 0;
      }
    }
  }
  &-head {
    width: 100%;
    height: 100%;
    max-height: 200px;
    overflow: auto;
    background: #fff5ea;
    font-size: 12px;
    color: #ff9e2b;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 10px 5px 10px;
  }
  &-content {
    padding: 0 30px;
    ::v-deep .bs-transfer-frame {
      width: 289px;
      .bs-search {
        width: 267px !important;
      }
    }
  }
}
</style>
