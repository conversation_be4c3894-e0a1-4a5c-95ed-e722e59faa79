import { SESSION_STORAGE_KEY_FOR_ERRORFLOWS } from '@/constant';
import i18n from '@/i18n';
// 获取报错流程信息
export const getErrorInfos = () => {
  return JSON.parse(sessionStorage.getItem(SESSION_STORAGE_KEY_FOR_ERRORFLOWS) || '{}');
};

// 设置流程报错信息
export const setErrorInfo = (flowId, msg) => {
  const errorInfos = getErrorInfos();
  sessionStorage.setItem(SESSION_STORAGE_KEY_FOR_ERRORFLOWS, JSON.stringify(Object.assign(errorInfos, { [flowId]: msg })));
  return errorInfos;
};

// 移除流程报错信息
export const removeErrorInfo = (flowId) => {
  const errorInfos = getErrorInfos();
  delete errorInfos[flowId];
  sessionStorage.setItem(SESSION_STORAGE_KEY_FOR_ERRORFLOWS, JSON.stringify(errorInfos));
  return errorInfos;
};

// 获取流程类型的显示名称
export const FLOW_STATUS_MAP = {
  PROCESSFLOW: 'DS',
  FLINK_SQL: 'SQL',
  UDJ: 'JAR'
};

export const FLOW_ERROR_MAP = {
  PROD: i18n.t('pa.flow.msg35'),
  PUB: i18n.t('pa.flow.msg36'),
  DEV: i18n.t('pa.flow.msg37')
};
