<template>
  <flow-drawer
    :size="600"
    :title="title"
    :show.sync="display"
    :before-close="close"
    :is-full-screen="isFullScreen"
    @submit="handleSubmit"
  >
    <config-form
      v-if="display"
      ref="configRef"
      :title.sync="title"
      :flow-id="flowId"
      :project-id="projectId"
    />
  </flow-drawer>
</template>

<script lang="ts">
import { Component, PropSync, Prop, Ref, Vue } from 'vue-property-decorator';

@Component({
  components: {
    FlowDrawer: () => import('../../components/flow-drawer.vue'),
    ConfigForm: () => import('@/components/single-flow-config/index.vue')
  }
})
export default class ResourceConfig extends Vue {
  @PropSync('show', { default: false }) display!: boolean;
  @Prop({ default: '' }) flowId!: string;
  @Prop({ default: '' }) projectId!: string;
  @Prop({ default: false }) isFullScreen!: boolean;
  @Ref('configRef') readonly configRef!: any;

  private title = '流程配置';

  async handleSubmit() {
    await this.configRef.handleSubmit();
    this.$emit('update', { id: this.configRef.flowId });
    this.display = false;
  }

  close() {
    this.display = false;
  }
}
</script>
<style lang="scss" scoped>
.resource {
  &__container {
    display: block;
    padding: 20px 8px 100px 0;
    width: 100%;
    height: 100%;
    ::v-deep .el-collapse {
      border-top: unset;
    }

    ::v-deep .resource-title {
      margin-left: 16px;
      padding-left: 6px;
      width: 100%;
      font-size: 14px;
      font-weight: 500;
      color: #444444;
      line-height: 20px;
      &::before {
        content: '';
        position: relative;
        left: 0;
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 8px;
        background: #ff9e2b;
        border-radius: 2px;
      }
    }
    ::v-deep .el-form-item {
      &__content {
        display: flex;
        align-items: center;

        .resource-item {
          display: inline-block;
          width: calc(100% - 30px);
          .el-select,
          .el-input-number,
          .el-date-editor {
            width: 100%;
          }
          .el-form-item {
            display: inline-block;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>
