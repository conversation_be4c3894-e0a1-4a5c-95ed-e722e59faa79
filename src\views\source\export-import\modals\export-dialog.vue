<template>
  <bs-dialog
    :visible.sync="show"
    size="small"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
  >
    <div slot="title" class="bs-dialog-title">
      {{ $t('pa.action.export') }}
      <bs-icon v-if="enableJar" class="bs-icon-wenti bs-pro-form-item__icon" tooltip="自定义JAR包流程暂不支持导出" />
    </div>
    <div class="export-main">
      <bs-search
        v-model="search"
        style="width: 200px; margin-right: 10px"
        :placeholder="$t('pa.resource.importExport.keyplaceholder')"
        @change="handleSearch"
      />
      <el-button type="primary" @click="allChecked">{{ $t('pa.resource.importExport.selectAll') }}</el-button>
      <el-button type="primary" @click="resetChecked">{{ $t('pa.resource.importExport.selectNone') }}</el-button>
      <bs-tree
        ref="treeRef"
        v-loading="treeLoading"
        node-key="id"
        show-checkbox
        :item-size="32"
        :data="treeData"
        :props="defaultProps"
        :filter-node-method="filterNode"
        @change="handleChange"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">{{ $t('pa.action.close') }}</el-button>
      <el-button :disabled="disabled" type="primary" :loading="previewLoading" @click="preview">
        {{ $t('pa.action.preview') }}
      </el-button>
      <el-button type="primary" :disabled="disabled" :loading="submitLoading" @click="submit">
        {{ $t('pa.action.export') }}
      </el-button>
    </span>
    <PreviewData v-if="previewVisible" :visible.sync="previewVisible" :data="previewData" :is-dialog="true" />
  </bs-dialog>
</template>

<script lang="ts">
import { Component, PropSync, Ref, Inject, Vue } from 'vue-property-decorator';
import { getExportTree, exportData, previewData } from '@/apis/impExpApi';
import { groupBy } from 'lodash';
import Tree from 'bs-ui-pro/lib/tree';
import { download } from '@/utils';
@Component({
  components: {
    PreviewData: () => import('./preview-data.vue')
  }
})
export default class Export extends Vue {
  @PropSync('visible', { default: false }) show!: boolean;
  @Inject('enableSql') enableSql;
  @Inject('enableJar') enableJar;
  @Ref('treeRef') readonly tree!: Tree;
  search = '';
  submitLoading = false;
  treeLoading = false;
  previewLoading = false;
  defaultProps: any = {
    children: 'children',
    label: 'label'
  };
  treeData: any = [];
  disabled = true;
  previewVisible = false;
  previewData = {};

  async created() {
    try {
      this.treeLoading = true;
      const {
        success,
        data: { job, udf, jobSuf, jobPre, preFix, sufFix, filterTemplate, routeTemplate, table, catalog, sqlFragmentCode },
        error
      } = await getExportTree();
      if (success) {
        // 处理选项：流程前缀、流程后缀，若有sql licence，展示表前缀、表后缀
        const getItem = () => {
          // 选项的子项一个都没有的时候，父节点置灰
          const count =
            (jobSuf?.length || 0) +
            (jobPre?.length || 0) +
            (this.enableSql ? (sufFix?.length || 0) + (preFix?.length || 0) : 0);
          const itemResult = [
            {
              id: 'item',
              label: this.$t('pa.resource.importExport.option'),
              disabled: !count,
              children: [
                {
                  id: 'jobPre',
                  label: this.$t('pa.data.text29'),
                  disabled: !jobPre.length,
                  children: this.formatArray(jobPre)
                },
                {
                  id: 'jobSuf',
                  label: this.$t('pa.data.text30'),
                  disabled: !jobSuf.length,
                  children: this.formatArray(jobSuf)
                }
              ]
                .concat(
                  this.enableSql && [
                    {
                      id: 'preFix',
                      label: this.$t('pa.resource.importExport.tablePrefix'),
                      disabled: !preFix.length,
                      children: this.formatArray(preFix)
                    },
                    {
                      id: 'sufFix',
                      label: this.$t('pa.resource.importExport.tableSuffix'),
                      disabled: !sufFix.length,
                      children: this.formatArray(sufFix)
                    }
                  ]
                )
                .filter(Boolean)
            }
          ];
          return itemResult;
        };

        const getSqlTree = () => {
          return this.enableSql
            ? [
                { id: 'udf', label: 'UDF', disabled: !udf.length, children: this.formatArray(udf) },
                { id: 'table', label: this.$t('pa.flow.table'), disabled: !table.length, children: this.formatArray(table) },
                {
                  id: 'catalog',
                  label: 'Catalog',
                  disabled: !catalog?.length,
                  children: this.formatArray(catalog)
                },
                {
                  id: 'sqlFragmentCode',
                  label: this.$t('pa.menu.sqlClip'),
                  disabled: !sqlFragmentCode?.length,
                  children: this.formatArray(sqlFragmentCode)
                }
              ]
            : [];
        };
        const getTemplate = () => {
          const count = filterTemplate?.length + routeTemplate?.length;
          const templateResult = [
            {
              id: 'template',
              label: this.$t('pa.data.table.detail.template'),
              disabled: !count,
              children: [
                {
                  id: 'filter',
                  label: this.$t('pa.menu.filterTemplate'),
                  disabled: !filterTemplate.length,
                  children: this.formatArray(filterTemplate)
                },
                {
                  id: 'route',
                  label: this.$t('pa.menu.routeTemplate'),
                  disabled: !routeTemplate?.length,
                  children: this.formatArray(routeTemplate)
                }
              ]
            }
          ];
          return templateResult;
        };
        this.treeData = [
          { id: 'job', label: this.$t('pa.flowName'), disabled: !job.length, children: this.formatArray(job) },
          ...getItem(),
          ...getSqlTree(),
          ...getTemplate()
        ];
        this.treeLoading = false;
        return;
      }
      this.$message.error(error);
      this.treeLoading = false;
    } catch {
      this.treeLoading = false;
    }
  }

  handleSearch(val) {
    this.tree.filter(val);
  }

  closeDialog() {
    this.show = false;
  }

  formatArray(raw: any) {
    return Array.isArray(raw) ? raw : [];
  }
  filterNode(value, data) {
    return !value ? true : data.label.includes(value);
  }
  allChecked() {
    this.tree.setAllChecked(true);
    this.disabled = this.tree.getCheckedNodes(true).length === 0;
  }
  resetChecked() {
    this.tree.setAllChecked(false);
    this.disabled = true;
  }
  async preview() {
    try {
      this.previewLoading = true;
      const checkedNodes = this.tree.getCheckedNodes(true).map(({ raw }) => raw);
      const result: any = groupBy(checkedNodes, 'type');
      const { success, msg, error, data } = await previewData(result);
      this.previewLoading = false;
      if (!success) return this.$message.error(msg || error);
      this.previewData = data;
      this.previewVisible = true;
    } catch {
      this.previewLoading = false;
    }
  }
  async submit() {
    try {
      this.submitLoading = true;
      const checkedNodes = this.tree.getCheckedNodes(true).map(({ raw }) => raw);
      const result: any = groupBy(checkedNodes, 'type');
      const data = await exportData(result);
      if (data.type === 'application/json') {
        data.text().then((text) => {
          const { success, msg, error } = JSON.parse(text);
          if (!success) this.$message.error(msg || error);
        });
      } else {
        download(data);
      }
      this.submitLoading = false;
    } catch {
      this.submitLoading = false;
    }
  }

  handleChange() {
    this.disabled = !this.tree.getCheckedNodes(true).length;
  }
}
</script>
<style scoped lang="scss">
.export-main {
  margin: 0 auto;
  width: fit-content;
  height: 330px;
}
::v-deep.bs-tree__container {
  padding: 10px 0 !important;
  height: unset;
}
</style>
