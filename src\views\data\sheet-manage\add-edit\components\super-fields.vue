<template>
  <div>
    <div class="head-info">
      <div class="name">{{ $t('pa.data.seniorFields') }}</div>
      <div>
        <el-button v-if="!disabled" size="small" type="primary" style="margin-left: 10px" @click="chartAdd">
          {{ $t('pa.data.table.detail.addField') }}
        </el-button>
      </div>
    </div>
    <el-table
      :data="list"
      style="width: 100%; max-height: 400px; overflow: auto; border: 1px solid #f1f1f1; border-bottom: none"
      size="mini"
    >
      <el-table-column prop="type" :label="$t('pa.data.table.detail.fieldClassify')" width="220">
        <template slot-scope="{ $index, row }">
          <el-select
            v-model="row.advanceFieldType"
            :disabled="disabled"
            :placeholder="$t('pa.placeholder.select')"
            style="width: 100%"
            @change="typeChange($index)"
          >
            <el-option
              v-for="item in typeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="type" :label="$t('pa.data.table.detail.fieldName')" min-width="130">
        <template slot-scope="{ row }">
          <div v-if="row.advanceFieldType === 'WATERMARK'" style="display: flex">
            <el-select
              v-model="row.field"
              :title="$t('pa.data.table.detail.placeholder.fieldPlaceholder2')"
              placeholder="$t('pa.data.table.detail.placeholder.fieldPlaceholder2')"
              style="flex: 1"
              :disabled="disabled"
            >
              <el-option v-for="item in baseField" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-input-number
              v-model="row.column1"
              :disabled="disabled"
              controls-position="right"
              style="width: 100px"
              :placeholder="$t('pa.placeholder.input')"
              :precision="0"
              :min="0"
            />
            <el-select
              v-model="row.column2"
              :disabled="disabled"
              :placeholder="$t('pa.placeholder.select')"
              style="width: 90px"
            >
              <el-option v-for="item in timeType" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div v-else>
            <el-input
              v-model="row.field"
              :disabled="disabled"
              style="width: 100%"
              :placeholder="$t('pa.data.table.detail.placeholder.fieldPlaceholder1')"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="!disabled" :label="$t('pa.action.action')" :width="isEn ? 120 : 80">
        <template slot-scope="{ $index, row }">
          <i class="el-icon-delete" style="font-size: 18px" @click="deleteChartList($index, row.type)"></i>
        </template>
      </el-table-column>
    </el-table>
    <div class="tabTip">
      {{ error }}
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
@Component({})
export default class SuperField extends PaBase {
  @Prop({ default: false }) disabled!: boolean;
  @Prop() fields!: any[];
  @Prop() table!: any[];
  @Prop() error!: '';
  typeList: any = [
    {
      label: this.$t('pa.waterLevelLine'),
      value: 'WATERMARK'
    },
    {
      label: this.$t('pa.handleTime'),
      value: 'PROCTIME'
    },
    {
      label: this.$t('pa.customFields'),
      value: 'OTHER'
    }
  ];
  timeType: any = [
    {
      label: this.$t('pa.unit.hour'),
      value: 'HOUR'
    },
    {
      label: this.$t('pa.unit.minute'),
      value: 'MINUTE'
    },
    {
      label: this.$t('pa.unit.second'),
      value: 'SECOND'
    }
  ];
  list: any = [];
  // 水平线字段选择
  baseField: any = [];
  index = 0;

  created() {
    this.list = this.fields || [];
    this.fieldsChange(this.list);
  }

  fieldsChange(array) {
    if (Array.isArray(array)) {
      const line = array.find((item) => item.advanceFieldType === 'WATERMARK') ? true : false;
      const time = array.find((item) => item.advanceFieldType === 'PROCTIME') ? true : false;
      this.typeList = this.typeList.map((item) => {
        return {
          ...item,
          disabled: (item.value === 'PROCTIME' && time) || (item.value === 'WATERMARK' && line)
        };
      });
    }
  }
  @Watch('fields', { immediate: true, deep: true })
  fieldChange(val) {
    this.list = val || [];
    this.fieldsChange(this.list);
  }

  //水平线字段名选项
  @Watch('table', { immediate: true, deep: true })
  change(list) {
    this.index++;
    const res = list.filter((item) => item.fieldType === 'TIMESTAMP(3)' && item.fieldName);
    this.baseField = res.map((tar) => {
      return {
        label: tar.fieldName,
        value: tar.fieldName
      };
    });
    const water = this.list.find((item) => item.advanceFieldType === 'WATERMARK' && item.field);
    if (water && !this.baseField.find((item) => item.value === water.field) && this.index > 2) {
      this.list = this.list.map((tar) => {
        return {
          ...tar,
          field: tar.advanceFieldType === 'WATERMARK' ? '' : tar.field
        };
      });
    }
  }

  // 增加高级字段
  chartAdd() {
    const line = this.fields.find((item) => item.advanceFieldType === 'WATERMARK') ? true : false;
    const time = this.fields.find((item) => item.advanceFieldType === 'PROCTIME') ? true : false;
    this.fields.push({
      advanceFieldType: line && time ? 'OTHER' : '',
      field: '',
      value: ''
    });
  }
  // 删除高级表字段
  deleteChartList(i) {
    this.fields.splice(i, 1);
  }
  // 选择字段分类
  typeChange(index) {
    this.list = this.fields.map((item, it) => {
      if (index === it) {
        return {
          advanceFieldType: item.advanceFieldType,
          field: '',
          value: ''
        };
      }
      return item;
    });
    this.fieldsChange(this.list);
  }
}
</script>
<style lang="scss" scoped>
.head-info {
  line-height: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
  .name {
    font-size: 14px;
    font-weight: 500;
  }
}
.tabTip {
  color: red;
}
</style>
