<template>
  <bs-dialog
    :title="$t('pa.flow.title5', [caseName])"
    width="80%"
    :footer-visible="false"
    :visible.sync="testLogDialogVisible"
    :before-close="closeLogDialog"
  >
    <pre class="logContentDiv" v-html="highlightedContent"></pre>
  </bs-dialog>
</template>

<script lang="ts">
import { Vue, Component, Prop, PropSync } from 'vue-property-decorator';

@Component
export default class TestLogDialog extends Vue {
  @PropSync('visible', { default: false }) testLogDialogVisible!: boolean;
  // 用例名称
  @Prop({ default: '' }) caseName!: string;
  @Prop({ default: '' }) logContent!: string;
  closeLogDialog() {
    this.testLogDialogVisible = false;
  }

  get highlightedContent() {
    const reg = /(warn)|(error)|(caused by)|(exception)/gi;
    return this.logContent.replace(reg, (a) => `<span style="background:yellow">${a}</span>`);
  }
}
</script>

<style lang="scss" scoped>
.logContentDiv {
  height: calc(70vh - 172px);
  overflow: auto;
  color: #000000;
  font-size: 14px;
  text-align: left;
  line-height: 20px;
}
</style>
