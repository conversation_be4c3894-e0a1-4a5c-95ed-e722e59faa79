<template>
  <el-popover ref="popover" v-model="showPopover" placement="bottom" width="300" trigger="click" class="filter-popover">
    <div v-for="item in filterList" :key="item.label" class="filter-popover__selections">
      <span class="filter-popover__selections--label">{{ item.label }}</span>
      <el-select
        v-model="filterParams[item.value]"
        class="filter-popover__selections--select"
        filterable
        clearable
        :placeholder="item.value === 'jobRunTimeStatus' ? $t('pa.placeholder.select') : $t('pa.all')"
      >
        <el-option v-for="el in item.options" :key="el.value" :label="el.label" :value="el.value" />
      </el-select>
    </div>
    <div class="filter-popover__popover--header">
      <el-button @click="handleSearch('clear')">{{ $t('pa.flow.reset') }}</el-button>
      <el-button type="primary" @click="handleSearch('confirm')">{{ $t('pa.flow.search') }}</el-button>
    </div>
    <el-tooltip
      v-if="icon"
      slot="reference"
      :content="$t('pa.flow.filter1')"
      effect="light"
      :open-delay="500"
      placement="bottom"
    >
      <i class="action-bar__icon" :class="[icon, filtered ? 'filtered' : '']"></i>
    </el-tooltip>
    <el-button
      v-else
      slot="reference"
      v-access="'PA.FLOW.FLOW_MGR.FILTER'"
      size="small"
      icon="iconfont icon-guolv"
      :class="[filtered ? 'filtered' : '']"
    >
      {{ $t('pa.flow.filter') }}
    </el-button>
  </el-popover>
</template>

<script lang="ts">
import { Vue, Component, PropSync, Prop } from 'vue-property-decorator';
import { filterPopoverList } from './config';
@Component
export default class FilterPopover extends Vue {
  @PropSync('visible') showPopover!: boolean;
  @Prop() icon!: any;
  filterParams: any = {
    jobStatus: '',
    jobRunTimeStatus: '',
    mode: '',
    clusterType: '',
    jobType: ''
  };
  filterList: any = filterPopoverList;
  get filtered() {
    const { filterParams } = this;
    return Object.keys(filterParams).some((k) => filterParams[k] && filterParams[k] !== '');
  }
  handleSearch(type) {
    if (type === 'clear') {
      Object.keys(this.filterParams).forEach((el: any) => {
        this.filterParams[el] = '';
      });
      // 清空后也要触发search事件，发送重置后的参数
    }

    type === 'confirm' && (this.showPopover = false);

    // 将空值转换为 'ALL' 发送给后端
    const searchParams = { ...this.filterParams };
    Object.keys(searchParams).forEach((key) => {
      if (!searchParams[key] || searchParams[key] === '') {
        searchParams[key] = 'ALL';
      }
    });

    this.$emit('search', searchParams);
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-icon-sort {
  transform: rotate(90deg);
}
::v-deep .el-popover {
  padding-top: 0px;
}
.filter-popover {
  &__selections {
    display: flex;
    flex-direction: column;
    &--label {
      font-size: 14px;
      font-weight: 400;
      color: #444444;
      line-height: 20px;
      margin-bottom: 10px;
      margin-top: 14px;
    }
    &--select {
      margin-bottom: 10px;
    }
    & > div {
      display: flex;
    }
  }
  &__popover {
    &--header {
      text-align: right;
      margin-top: 14px;
    }
  }
}
.action-bar__icon.filtered {
  color: $--bs-color-primary;
}
.filtered ::v-deep .iconfont,
.filtered ::v-deep span {
  color: $--bs-color-primary;
}
</style>
