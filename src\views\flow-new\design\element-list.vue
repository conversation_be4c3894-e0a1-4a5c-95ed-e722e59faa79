<template>
  <div class="element-list">
    <div class="element-list--search">
      <bs-search v-model="search" placeholder="查询" @search="getComponentList" />
    </div>
    <bs-drag :editor="editor" :options="dragOptions">
      <bs-collapse :value="activeGroups">
        <bs-collapse-item
          v-for="group in componentList"
          :key="group.operateType"
          :name="group.operateType"
        >
          <div slot="title" class="group-title">
            {{ mapOperateType(group.operateType) }}（{{ group.paJobComponentList.length }}）
          </div>
          <bs-drag-node
            v-for="element in group.paJobComponentList"
            :id="element.id"
            :key="element.id"
            :options="element.options"
          >
            <div class="element-item" :class="{ 'element-img-box--disabled': element.disabled }">
              <div class="element-img">
                <img :src="element.iconBase64" style="width: 24px; height: 24px" />
              </div>
              <div class="element-img-title" :title="element.componentName">
                {{ element.componentName }}
              </div>
            </div>
          </bs-drag-node>
        </bs-collapse-item>
      </bs-collapse>
    </bs-drag>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { URL_COMPONENT_SEARCH } from '@/apis/commonApi';
import { get } from '@/apis/utils/net';
import { JobType } from './interface';
import { Element } from './interface/element';
import cloneDeep from 'lodash/cloneDeep';
import { UPDATE_COMPONENT_LIST } from '@/store/event-names/mutations';
import { getMaxNodeCount } from './page-content/canvas/service';
import { guid } from '@/utils';
interface ComponentGroup {
  paJobComponentList: Element[];
  operateType: 'SOURCE' | 'PROCESS' | 'SINK';
}
@Component
export default class ElementList extends Vue {
  @Prop({ default: JobType.PROCESSFLOW }) jobType!: JobType;
  @Prop() editor!: string;
  // 搜索关键字
  search = '';
  jobData: any = {};
  // 所有的组件类型
  allComponentList: ComponentGroup[] = [];
  activeGroups = ['SOURCE', 'PROCESS', 'SINK'];
  dragOptions = {};
  get isSql() {
    return this.jobType === JobType.FLINK_SQL;
  }
  // 最终展示的组件列表
  get componentList(): ComponentGroup[] {
    const { allComponentList = [], isSql } = this;
    const allComList = cloneDeep(allComponentList);
    return allComList.map((item) => {
      item.paJobComponentList = (item.paJobComponentList || []).filter(({ type = '' }) =>
        isSql ? (type as string).includes('_SQL') : !(type as string).includes('_SQL')
      );
      return item;
    });
  }
  @Watch('editor')
  handleEditorChange(editor) {
    this.dragOptions = {
      target: editor.graph,
      getDragNode: (options) => {
        return editor.getDragNode(options || {});
      },
      getDropNode: (options) => {
        return editor.createNode(options, false, (nodeData, nodes) => {
          const max = getMaxNodeCount(nodes);
          nodeData.nodeName = nodeData.nodeName + '_' + (max + 1);
          // 节点id生成
          nodeData.id = guid();
        });
      },
      validateNode: () => {
        // if (editor.getNodes().length > 100) {
        //   this.$tip.warning('流程最多支持100个节点');
        //   return false;
        // } else {
        return !editor.disabled;
        // }
      }
    };
  }
  async created() {
    await this.getComponentList();
    // 父组件更新组件列表
    this.$emit('load', this.allComponentList);
  }

  // 获取组件列表
  async getComponentList() {
    const { success, data, msg } = await get(URL_COMPONENT_SEARCH, {
      type: this.jobType,
      name: this.search
    });
    if (success) {
      !this.search && this.$store.commit(UPDATE_COMPONENT_LIST, cloneDeep(data));
      this.allComponentList = this.componentListAdapter(data);
      return;
    }
    this.$message.error(msg);
  }

  // 映射操作类型
  mapOperateType(type) {
    return {
      SOURCE: '输入组件',
      PROCESS: '处理组件',
      SINK: '输出组件'
    }[type];
  }
  // 组件列表处理
  componentListAdapter(list: ComponentGroup[]) {
    return list.map((item: any) => {
      item.paJobComponentList = item.paJobComponentList.map((item) => {
        const {
          id,
          orgId,
          jarId,
          type,
          version,
          className,
          componentName,
          connectorType,
          iconBase64,
          inEndPoint,
          outEndPoint,
          properties,
          ioProperties,
          operateType,
          printLog
        } = item;
        return {
          id,
          type,
          className,
          componentName,
          connectorType,
          iconBase64,
          options: {
            // dag节点渲染需要的数据
            nodeName: componentName,
            inPort: JSON.parse(inEndPoint).map((i) => ({ description: i.label })),
            outPort: JSON.parse(outEndPoint).map((i) => ({ description: i.label })),
            status: item.status || 0,
            selected: false,
            position: [0, 0],
            parallelism: 1,
            options: {
              // 原流程设计新创建一个组件时需要的数据
              componentId: id,
              componentVersion: version,
              type,
              componentName,
              orgId,
              properties: JSON.parse(properties),
              className,
              jarId,
              inEndPoint,
              outEndPoint,
              ioProperties,
              operateType,
              position: [0, 0],
              parallelism: 1,
              printLog
            }
          }
        };
      });
      return item;
    });
  }
}
</script>
<style lang="scss" scoped>
.element-list {
  width: 300px;
  height: 100%;
  background-color: #fff;
  &--search {
    height: 50px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    border-bottom: 1px solid #f1f1f1;
  }
  ::v-deep .bs-search {
    width: 100% !important;
  }
  .group-title {
    display: flex;
    align-items: center;
    > span {
      padding-right: 8px;
    }
    > ::v-deep .bs-tag.bs-tag--round {
      min-width: 16px;
      height: 16px;
      line-height: 16px;
      padding-left: 2px;
      padding-right: 2px;
    }
  }
  ::v-deep .bs-drag {
    height: calc(100vh - 246px);
    overflow: auto;
  }
  ::v-deep .el-collapse {
    width: 100%;
    padding: 0 20px;
    border-top: 0;
    border-bottom: 0;
    .el-collapse-item__header {
      height: 50px;
      line-height: 50px;
      font-size: 14px;
      color: #000;
      border-bottom: 1px solid #f1f1f1;
    }
    .el-collapse-item__content {
      padding: 10px 0;
      ::v-deep .bs-drag-node {
        width: 100%;
      }
    }
  }
  .element-item {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    margin: 10px 0;
    border: 1px solid #f1f1f1;
  }
  .element-img {
    width: 38px;
    height: 38px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    margin-right: 12px;
    background: #f9f9f9;
  }
  .element-img-title {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
