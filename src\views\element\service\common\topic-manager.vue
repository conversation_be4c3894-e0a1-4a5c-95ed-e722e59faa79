<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">Topics</div>
      <div class="bs-page__header-operation">
        <el-input
          v-if="this.type !== 'PULSAR'"
          v-model="topicKeyWord"
          placeholder="请输入关键字搜索"
          style="width: 200px; margin-right: 5px"
          @input="searchInfo"
        />
        <bs-search
          v-if="type === 'PULSAR'"
          v-model="search.topic"
          class="marR12"
          placeholder="topicName"
          @change="searchList"
        />
        <bs-select
          v-if="type === 'PULSAR'"
          v-model="search.tenant"
          class="marR12"
          :options="tenantList"
          placeholder="Tenant"
          clearable
          @change="tenantChange"
        />
        <bs-select
          v-if="type === 'PULSAR'"
          v-model="search.namespace"
          class="marR12"
          :options="namespaceList"
          placeholder="Namespace"
          clearable
          @focus="getNamespaceList(search.tenant)"
        />
        <el-button
          v-if="hasCreateAuth || type === 'PULSAR'"
          style="margin: 0 5px"
          size="small"
          @click="handleCreateTopic"
        >
          创建
        </el-button>
        <el-button v-if="hasShareAuth" type="primary" size="small" @click="multipleShare">
          分享
        </el-button>
      </div>
    </div>
    <div class="tab-content" :style="{ minHeight: '180px' }">
      <bs-table
        v-loading="tableLoading"
        selection
        :max-height="560"
        :data="topicData"
        :column-settings="false"
        :column-data="columns"
        :page-data="hasPageData"
        @page-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
      >
        <template v-slot:topic="{ row }">
          <del v-if="!row.available && type === 'KAFKA'" style="color: red">{{ row.topic }}</del>
          <span v-else>{{ row.topic }}</span>
        </template>
        <template v-slot:operator="{ row }">
          <el-tooltip
            v-if="!row.available && type === 'KAFKA'"
            effect="light"
            placement="bottom"
            content="删除"
          >
            <i
              v-if="hasDeleteAuth"
              class="iconfont icon-shanchu"
              style="margin-left: 10px; cursor: pointer"
              @click="handleDeleteTopic(row)"
            ></i>
          </el-tooltip>
          <div v-else>
            <el-tooltip effect="light" placement="bottom" content="数据预览">
              <i
                class="iconfont icon-chakan"
                style="margin-left: 10px; cursor: pointer"
                @click="handlePreview(row)"
              ></i>
            </el-tooltip>
            <el-tooltip effect="light" placement="bottom" content="分享">
              <i
                v-if="hasShareAuth"
                class="iconfont icon-fenxiang"
                style="margin-left: 10px; cursor: pointer"
                @click="shareTopic(row)"
              ></i>
            </el-tooltip>
            <el-tooltip effect="light" placement="bottom" content="引用关系">
              <i
                v-if="type !== 'ROCKETMQ'"
                class="iconfont icon-yinyongguanxi"
                style="margin-left: 10px; cursor: pointer"
                @click="previewRelation(row)"
              ></i>
            </el-tooltip>
          </div>
        </template>
      </bs-table>
      <!-- kafka新增 -->
      <topic-add
        :visible="dialogVisible"
        :data="recordData"
        :form-loading="formLoading"
        @close="closeDialog"
      />
      <!-- pulsar新增 -->
      <topic-add-pulsar
        v-if="addVisible"
        :visible.sync="addVisible"
        :res-id="resId"
        :tenant-list="tenantList"
        :namespace-list="namespaceList"
        @close="fetchList"
      />
      <!-- rocket -->
      <topic-add-rocket
        :visible="addRocket"
        :data="recordData"
        :form-loading="formLoading"
        @close="closeDialog"
      />
      <topic-preview
        v-if="previewDialogVisible"
        :topic="previewRecordData"
        :res-id="resId"
        :visible="previewDialogVisible"
        @close="previewCloseDialog"
      />
      <dept-role
        v-if="showDeptRoleDialog"
        ref="DeptRole"
        :visible.sync="showDeptRoleDialog"
        :data="kafkaData"
        :topic-data="rowTopic"
      />
      <!-- 查看引用关系弹窗 -->
      <view-relation
        v-if="relationDialogShow"
        :visible.sync="relationDialogShow"
        :res-id="resId"
        :topic="rowTopic"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject, Prop } from 'vue-property-decorator';
import {
  URL_RES_DETAIL_KAFKA_TOPIC,
  URL_RES_DELETE_KAFKA_TOPIC,
  URL_RES_DETAIL_ROCKETMQ_ALLTOPICS
} from '@/apis/commonApi';
import { getTopicList, getPulsarTenantList, getPulsarNamespaceList } from '@/apis/serviceApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
import { get as getAll, del } from '@/apis/utils/net';
@Component({
  components: {
    'topic-add': () => import('./modals/topic-add-kafka.vue'),
    'topic-add-pulsar': () => import('./modals/topic-add-pulsar.vue'),
    'topic-add-rocket': () => import('./modals/topic-add-rocket.vue'),
    'topic-preview': () => import('./topic-preview.vue'),
    'dept-role': () => import('@/components/dept-role-1.vue'),
    'view-relation': () => import('./view-relation.vue')
  }
})
export default class TopicManager extends PaBase {
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  @Inject('comListConf') comListConf;
  @Prop({ default: () => {} }) kafkaData;
  search = { topic: '', tenant: '', namespace: '' };
  tenantList: any = [];
  namespaceList: any = [];
  formLoading = false;
  dialogVisible = false;
  recordData: any = {};
  tableLoading = false;
  previewDialogVisible = false;
  previewRecordData: any = {};
  topicKeyWord = '';
  topicData: any[] = [];
  columns: any = [
    {
      label: '名称',
      value: 'topic'
    },
    {
      label: '操作',
      value: 'operator',
      width: 150
    }
  ];
  rowTopic: any = {};
  listConf: any = {};
  data: any = {};
  selectedTopic: any = [];
  showDeptRoleDialog = false;
  pageData: IPageData = {
    currentPage: 1,
    pageSize: 10,
    total: 0
  };
  allTableData: any = [];
  relationDialogShow = false;
  resId: any = '';
  searchList = _.debounce(this.fetchList, 500);
  // pulsar 新增
  addVisible = false;
  //rocket 新增
  addRocket = false;
  // 引用关系类型
  get hasShareAuth() {
    return this.hasFeatureAuthority(this.listConf.shareTopicAuthCode, this.data.dataLevelType);
  }
  get hasDeleteAuth() {
    return this.hasFeatureAuthority(this.listConf.deleteTopicAuthCode, this.data.dataLevelType);
  }
  get hasCreateAuth() {
    return this.hasFeatureAuthority(this.listConf.addTopicAuthCode, this.data.dataLevelType);
  }
  sourceData: any = [];

  // 服务类型
  get type() {
    return this.$route.query.resType;
  }

  // 是否真是topic分页
  get hasPageData() {
    return this.type === 'ROCKETMQ' ? null : this.pageData;
  }

  created() {
    this.resId = this.$route.query.id;
    this.loadData(this.comDetailRecord.val || {}, this.comListConf.val || {});
  }

  handleCreateTopic() {
    this.recordData.resId = this.$route.query.id;
    this.recordData.resType = this.$route.query.resType;
    if (this.type === 'PULSAR') {
      this.addVisible = true;
    } else if (this.type === 'KAFKA') {
      this.dialogVisible = true;
    } else {
      this.addRocket = true;
    }
  }

  handlePreview(row: any) {
    this.previewDialogVisible = true;
    this.previewRecordData = row;
  }

  shareTopic(row) {
    this.rowTopic = row;
    this.showDeptRoleDialog = true;
  }

  async handleSearchTopic() {
    this.tableLoading = true;
    const resp = await getAll(URL_RES_DETAIL_KAFKA_TOPIC, {
      id: this.$route.query.id
    });
    this.parseResponse(resp, () => {
      if (!resp.data) {
        resp.data = [];
      }
      // 初始化
      this.topicData = [];
      this.allTableData = resp.data;
      this.sourceData = _.cloneDeep(this.allTableData);
      this.pageData.total = resp.data.length;
      this.topicData = this.allTableData.slice(0, 10);
    });
    this.tableLoading = false;
  }
  closeDialog(needFresh: any) {
    if (needFresh === true) {
      this.type === 'KAFKA' ? this.handleSearchTopic() : this.handleRocketTopic();
    }
    this.dialogVisible = false;
    this.addRocket = false;
  }
  previewCloseDialog() {
    this.previewDialogVisible = false;
  }

  async handleRocketTopic() {
    this.tableLoading = true;
    this.doGet(URL_RES_DETAIL_ROCKETMQ_ALLTOPICS, {
      params: { id: this.$route.query.id }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        // 初始化
        this.topicData = [];
        resp.data.forEach((item) => this.topicData.push({ topic: item }));
        this.sourceData = _.cloneDeep(this.topicData);
      });
      this.tableLoading = false;
    });
  }
  async loadData(data: any, listConf: any) {
    this.listConf = listConf;
    this.data = data;
    if (this.type === 'PULSAR') {
      this.getTenantList();
      await this.fetchList();
    } else if (this.type === 'ROCKETMQ') {
      await this.handleRocketTopic();
    } else {
      await this.handleSearchTopic();
    }
  }

  searchInfo() {
    const filterData = this.sourceData.filter(
      (data) =>
        !this.topicKeyWord || data.topic.toLowerCase().includes(this.topicKeyWord.toLowerCase())
    );
    this.allTableData = _.clone(filterData);
    this.topicData = this.type === 'ROCKETMQ' ? this.allTableData : this.allTableData.slice(0, 10);
    this.pageData.currentPage = 1;
    this.pageData.total = this.allTableData.length;
  }

  handleCurrentChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    if (this.type === 'PULSAR') {
      this.fetchList();
    } else {
      this.topicData = this.allTableData.slice(10 * (currentPage - 1), 10 * currentPage);
    }
  }

  handleDeleteTopic(row) {
    this.$confirm('确定删除该条数据吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(async () => {
      const resp = await del(URL_RES_DELETE_KAFKA_TOPIC, { topicId: row.id });
      if (resp.success) {
        this.$message.success(resp.msg);
        this.loadData(this.comDetailRecord.val || {}, this.comListConf.val || {});
        this.pageData.currentPage = 1;
      } else {
        this.$message.error(resp.msg);
      }
      this.topicKeyWord = '';
    });
  }

  multipleShare() {
    if (!this.selectedTopic.length) {
      this.$message.error('请至少选择一项topic');
      return;
    }
    this.showDeptRoleDialog = true;
  }

  handleSelectionChange(selectedData) {
    this.selectedTopic = selectedData;
  }

  previewRelation(row) {
    this.rowTopic = row;
    this.relationDialogShow = true;
  }

  // 获取Topic列表
  async fetchList() {
    this.tableLoading = true;
    const {
      data: { tableData, columnData, pageData },
      success,
      msg
    } = await getTopicList(this.resId, {
      pageData: this.pageData,
      search: this.search
    });
    this.tableLoading = false;
    if (success) {
      this.topicData = tableData;
      this.columns = columnData;
      this.pageData = pageData;
      return;
    }
    this.$message.error(msg);
  }

  // type === 'PULSAR'
  tenantChange() {
    this.search.namespace = '';
    this.namespaceList = [];
    this.fetchList();
  }

  async getTenantList() {
    const { data = [], success, msg } = await getPulsarTenantList(this.resId);
    if (success) {
      data.forEach((el) => {
        this.tenantList.push({ label: el, value: el });
      });
      return;
    }
    this.$message.error(msg);
  }

  async getNamespaceList(tenant) {
    this.namespaceList = [];
    const { data, success, msg } = await getPulsarNamespaceList(this.resId, tenant);
    if (success) {
      data.forEach((el) => {
        this.namespaceList.push({ label: el, value: el });
      });
      return;
    }
    this.$message.error(msg);
  }
}
</script>
<style scoped lang="scss">
.my-card_body ::v-deep .el-card__body {
  padding: 0px 0px 0px 0px;
}

.my-wrap-row {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
}
.my-wrap-col {
  border: 1px solid #000;
}
.my-wrap-tag {
  width: 350px;
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
