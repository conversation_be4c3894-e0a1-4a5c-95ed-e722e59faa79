<template>
  <el-dropdown placement="bottom" :disabled="data.disabled" @command="handler">
    <i v-show="data.show" :class="data.icon"></i>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-for="el in data.options" :key="el.command" :command="el.command" :disabled="el.disabled">
        <span class="drop-span">{{ el.text }}</span>
        <el-tooltip v-if="el.content" effect="light" placement="top" :content="el.content">
          <i class="drop-icon" :class="el.icon"></i>
        </el-tooltip>
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import type { ActionItem } from './type';
@Component
export default class DropDown extends Vue {
  @Prop() data!: ActionItem;

  handler(val) {
    this.$emit('click', ...[this.data.name, val]);
  }
}
</script>
<style lang="scss" scoped>
.drop {
  &-span {
    margin-right: 10px;
  }
  &-icon {
    font-size: 15px;
  }
}
</style>
