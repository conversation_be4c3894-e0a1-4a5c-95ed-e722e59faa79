import { Component, Inject, Prop } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';

@Component
export default class VirtualTreeItemPrefix extends PaBase {
  @Prop({}) node!: any;
  @Inject('sourceTreeMaps') sourceTreeMaps;
  sourceNode: any = {};
  created() {
    this.sourceNode = this.sourceTreeMaps.get(this.node.id);
  }
  render() {
    const parent: any = this.$parent;
    return parent.$scopedSlots['prefix']({ id: this.node.id, data: this.sourceNode });
  }
}
