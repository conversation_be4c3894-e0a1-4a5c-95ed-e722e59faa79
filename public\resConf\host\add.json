{"forms": [{"type": "input", "prop": "title", "label": "名称", "componentProps": {"maxlength": 30, "placeholder": "请输入名称", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入名称", "trigger": "blur"}, {"min": 2, "max": 30, "message": "长度在 2 到 30 个字符", "trigger": "blur"}]}, {"type": "input", "prop": "ip", "label": "IP地址", "componentProps": {"maxlength": 50, "placeholder": "请输入IP地址", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入IP地址", "trigger": "blur"}]}, {"type": "input-number", "prop": "port", "label": "端口", "componentProps": {"min": 1, "max": 65535, "placeholder": "请输入端口"}, "rules": [{"required": true, "message": "请输入端口", "trigger": "blur"}, {"type": "number", "min": 1, "max": 65535, "message": "选择范围在1-65535之间", "trigger": "blur"}], "defaultVal": 22}, {"type": "input", "prop": "username", "label": "用户名", "componentProps": {"maxlength": 40, "placeholder": "请输入用户名", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入用户名", "trigger": "blur"}, {"min": 2, "max": 30, "message": "长度在 2 到 40 个字符", "trigger": "blur"}]}, {"type": "select", "prop": "authType", "label": "验证类型", "componentProps": {"options": [{"label": "密码", "value": "password"}, {"label": "秘钥", "value": "priKey"}], "placeholder": "请选择"}, "defaultVal": "password"}, {"type": "password", "prop": "password", "label": "密码", "componentProps": {"maxlength": 100, "placeholder": "请输入密码"}, "rules": [{"required": true, "message": "请输入密码", "trigger": "blur"}]}, {"type": "textarea", "prop": "priKey", "label": "秘钥", "deps": ["authType"], "visible": "(scope) => scope.authType === 'priKey'", "componentProps": {"rows": 5, "maxlength": 4096, "placeholder": "请输入秘钥"}, "rules": [{"required": true, "message": "请输入秘钥", "trigger": "blur"}]}, {"type": "textarea", "prop": "memo", "label": "备注", "componentProps": {"rows": 5, "maxlength": 255, "placeholder": "请输入备注"}}]}