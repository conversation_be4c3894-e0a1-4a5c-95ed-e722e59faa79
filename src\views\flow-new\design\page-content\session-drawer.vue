<template>
  <el-drawer
    class="session"
    :class="{ 'outer-full-screen': fullScreen, 'session--us': isEn }"
    :modal="false"
    :size="isEn ? 550 : 400"
    :append-to-body="false"
    :visible.sync="sessionVisible"
    :before-close="handleDrawerClose"
  >
    <!-- 头部信息 -->
    <div slot="title" class="session__header">
      <span class="session__title marR10">{{ $t('pa.flow.sessionConfig') }}</span>
      <bs-tag v-if="statusText" :class="['bs-tag', ' bs-tag--light', statusClass]">{{ statusText }}</bs-tag>
    </div>
    <!-- 内容区域 -->
    <div v-loading="loading" class="session__content">
      <el-form
        ref="sessionFormRef"
        :model="sessionForm"
        :rules="sessionFormRules"
        :disabled="disabled || readonly"
        :label-width="isEn ? '190px' : '110px'"
      >
        <el-form-item :label="$t('pa.flow.clusterTest')" prop="resId">
          <bs-select v-model="sessionForm.resId" :options="clusterOptions" clearable :label-map="clusterLabelMap" />
        </el-form-item>
        <el-form-item :label="$t('pa.flow.bingxingdu')" prop="parallelism">
          <el-input-number
            v-model="sessionForm.parallelism"
            style="width: 100%"
            number
            :min="1"
            :max="100"
            :step="1"
            :placeholder="$t('pa.flow.placeholder11')"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item :label="$t('pa.flow.maxRunTime')" prop="maxTime">
          <el-input-number
            v-model="sessionForm.maxTime"
            number
            :min="1"
            :max="MAX_TIME"
            :step="1"
            :placeholder="$t('pa.flow.placeholder12')"
            controls-position="right"
          />
          <span class="session__content-memo">{{ $t('pa.flow.s') }}</span>
          <el-tooltip effect="light" :content="timeoutTip" placement="bottom">
            <i class="iconfont icon-wenhao base-icon"></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item :label="$t('pa.flow.maxRow')" prop="maxCount">
          <el-input-number
            v-model="sessionForm.maxCount"
            number
            :min="1"
            :max="MAX_COUNT"
            :step="1"
            style="width: 198px"
            :placeholder="$t('pa.flow.placeholder13')"
            controls-position="right"
          />
          <el-tooltip effect="light" :content="maxCountTip" placement="bottom">
            <i class="iconfont icon-wenhao base-icon"></i>
          </el-tooltip>
        </el-form-item>
      </el-form>
      <!-- 底部按钮区域 -->
      <div v-if="!readonly" class="session__footer">
        <el-button
          v-for="item in buttonList"
          v-show="item.show"
          :key="item.label"
          :style="btnWidth"
          :type="item.type"
          :loading="item.type === 'primary' && confirmLoading"
          @click="operateHandler(item.event)"
        >
          {{ item.label }}
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts">
import { Vue, Component, Prop, PropSync } from 'vue-property-decorator';

import { getFlinkList, getRealTestconfig, getThreshold, updateRealTestconfig } from '@/apis/flowNewApi';
import { cloneDeep } from 'lodash';
import i18n from '@/i18n';
interface ISessionForm {
  resId: string;
  resName: string;
  maxTime: number;
  maxCount: number;
  parallelism: number;
  jobId: string | unknown;
  sessionStatus: 'NOT_CONFIGURED' | 'NOT_RUNNING' | 'RUNNING';
}
const initSessionForm = (): ISessionForm => ({
  resId: '',
  resName: '',
  maxTime: 1,
  maxCount: 1,
  parallelism: 1,
  jobId: '',
  sessionStatus: 'NOT_CONFIGURED'
});
const statusTextMap = {
  NOT_CONFIGURED: '',
  NOT_RUNNING: i18n.t('pa.flow.waitOnline'),
  RUNNING: i18n.t('pa.flow.onlining')
} as const;
const statusClassMap = {
  NOT_CONFIGURED: '',
  NOT_RUNNING: 'session-status--info',
  RUNNING: 'bs-tag--orange'
} as const;

@Component
export default class Session extends Vue {
  @PropSync('show', { default: false }) sessionVisible!: boolean;
  @Prop({ type: Boolean, default: false }) fullScreen!: boolean;
  @Prop({ type: Boolean, default: false }) readonly!: boolean;
  MAX_TIME = 1;
  MAX_COUNT = 1;

  clusterOptions: any = [];
  clusterLabelMap: any = new Map();
  loading = true;
  confirmLoading = false;
  sessionForm: ISessionForm = initSessionForm();
  originSessionForm: ISessionForm = initSessionForm();

  // 表单校验规则
  sessionFormRules = {
    resId: [{ required: true, message: this.$t('pa.flow.msg47'), trigger: 'change' }],
    parallelism: [{ required: true, message: this.$t('pa.flow.placeholder11'), trigger: 'blur' }]
  };
  disabled = false;

  get flowId() {
    return this.$route.query.flowId as string;
  }
  get statusText() {
    return statusTextMap[this.sessionForm.sessionStatus];
  }
  get statusClass() {
    return statusClassMap[this.sessionForm.sessionStatus];
  }
  get btnWidth() {
    const buttonList = this.buttonList.filter((el) => el.show);
    return { width: 100 / buttonList.length + '%' };
  }
  get timeoutTip() {
    return this.$t('pa.flow.msg48', [this.MAX_TIME]);
  }
  get maxCountTip() {
    return this.$t('pa.flow.msg49', [this.MAX_COUNT]);
  }
  get buttonList() {
    return [
      {
        show: !this.disabled,
        event: 'handleCancle',
        label: this.$t('pa.flow.cancel')
      },
      {
        show: !this.disabled,
        event: 'handleSave',
        label: this.$t('pa.flow.save'),
        type: 'primary'
      },
      {
        show: this.sessionForm.sessionStatus === 'NOT_RUNNING' && this.disabled,
        event: 'handleEdit',
        label: this.$t('pa.flow.edit'),
        type: 'primary'
      },
      {
        show: this.sessionForm.sessionStatus === 'NOT_RUNNING' && this.disabled,
        event: 'handleConn',
        label: this.$t('pa.flow.startSession'),
        type: 'primary'
      },
      {
        show: this.sessionForm.sessionStatus === 'RUNNING',
        event: 'handleConnStop',
        label: this.$t('pa.flow.endSession'),
        type: 'primary'
      }
    ];
  }

  async created() {
    this.sessionForm.jobId = this.flowId;
    this.loading = true;
    await this.getClusterList();
    await this.getThreshold();
    await this.getRealTestconfig();
  }
  async getClusterList() {
    const { success, data, error } = await getFlinkList({
      orgId: this.$store.getters.orgId
    });
    if (success) {
      this.clusterOptions = data
        .filter((el) => el.clusterType === 'STANDALONE')
        .map((el) => {
          return { label: el.title, value: el.id };
        });
      this.clusterLabelMap.set(data.resId, data.resName);
      return;
    }
    this.$message.error(error);
  }
  async getThreshold() {
    const {
      success,
      data: { timeOut, maxCount },
      error
    } = await getThreshold();
    if (success) {
      this.MAX_TIME = timeOut;
      this.MAX_COUNT = maxCount;
      return;
    }
    this.$message.error(error);
  }
  async getRealTestconfig() {
    const { success, data, error } = await getRealTestconfig(this.flowId);
    this.loading = false;
    if (success) {
      this.originSessionForm = cloneDeep(data);
      this.sessionForm = data;
      this.disabled = !!data.resId;
      return;
    }
    this.$message.error(error);
  }

  operateHandler(event: string) {
    event && this[event]();
  }
  handleCancle() {
    if (this.sessionForm.sessionStatus === 'NOT_RUNNING') {
      this.disabled = true;
      this.sessionForm = cloneDeep(this.originSessionForm);
    } else {
      this.sessionVisible = false;
    }
  }
  handleSave() {
    (this.$refs.sessionFormRef as any).validate(async (valid: any) => {
      if (valid) {
        this.confirmLoading = true;
        const { msg, success } = await updateRealTestconfig(this.sessionForm);
        this.confirmLoading = false;
        if (success) {
          this.$message.success(msg);
          this.getRealTestconfig();
          return;
        }
        this.$message.error(msg);
      }
    });
  }
  handleEdit() {
    this.disabled = false;
  }
  handleConn() {
    this.$emit('sessionConn');
    this.handleDrawerClose();
  }
  handleConnStop() {
    this.$emit('sessionStopConn');
    this.handleDrawerClose();
  }
  // 抽屉关闭回调
  handleDrawerClose() {
    this.sessionVisible = false;
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__header {
  border-bottom: 1px solid #f1f1f1;
  padding-bottom: 20px;
  margin-bottom: 0;
  height: 60px;
}
.session {
  top: 95px;
  bottom: 11px;
  width: 435px;
  left: calc(100% - 505px);
  z-index: 1999 !important;
  &--us {
    width: 585px;
    left: calc(100% - 655px);
  }
  &__header {
    display: flex;
    align-items: center;
    .session-status--info {
      color: $--bs-color-text-placeholder;
      background-color: $--bs-color-background-base;
      border: 1px solid $--bs-color-border-lighter;
    }
  }
  &__title {
    color: #444;
    font-weight: 500;
  }
  &__content {
    padding: 0 20px;
    height: 100%;
    padding: 17px 35px;
    ::v-deep .el-select {
      width: 100%;
      margin-right: 24px;
    }
    &-memo {
      padding-left: 5px;
    }
    .base-icon {
      margin-left: 5px;
    }
    ::v-deep .el-form {
      height: calc(100% - 48px);
      overflow-x: hidden;
      overflow-y: auto;
    }
  }
  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
  }
}
.outer-full-screen {
  top: 0;
  left: calc(100% - 630px);
}
</style>
