<template>
  <bs-dialog
    :title="title"
    :visible.sync="visible"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
  >
    <el-form
      ref="ruleForm"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm warn"
      element-loading-text="数据正在加载中..."
    >
      <el-form-item label="名称" prop="title">
        <el-input
          v-model="formData.title"
          placeholder="请输入名称"
          maxlength="30"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="规则类型">{{ formData.ruleType }}</el-form-item>
      <el-form-item v-if="!isBatchJob" label="执行周期" prop="cron">
        <el-input
          v-model="formData.cron"
          placeholder="请输入执行周期"
          maxlength="30"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="预警模板" prop="noticeMsg" style="text-align: left">
        <el-dropdown @command="handleDropdownClick">
          <span class="el-dropdown-link">
            下拉选择模板
            <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in noticMsgList" :key="item.id" :command="item.value1">
              {{ item.value1 }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-input
          v-model="formData.noticeMsg"
          type="textarea"
          :row="5"
          placeholder="请输入预警模板"
          maxlength="255"
        />
      </el-form-item>
      <el-form-item label="预警通知发送" prop="sendEnable" style="text-align: left">
        <div class="warn-notify">
          <el-switch
            v-model="formData.sendEnable"
            active-value="Y"
            inactive-value="N"
            active-text="发送"
            inactive-text="不发送"
          />
          <el-tooltip class="item" effect="light" placement="bottom">
            <span slot="content">
              控制是否发送预警通知，如短信通知、微信通知等，设置不发送后真实的预警记录任然会保留
            </span>
            <span class="iconfont icon-wenhao" style="margin-left: 5px"></span>
          </el-tooltip>
        </div>
      </el-form-item>
      <el-form-item label="通知人" prop="noticeUser" style="text-align: left">
        <el-select
          v-model="formData.noticeUser"
          style="width: 100%"
          multiple
          placeholder="请选择通知人"
          @change="noticeUserChange"
        >
          <el-option
            v-for="item in userList"
            :key="item.username"
            :label="item.username"
            :value="item.username"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="!isBatchJob" label="静默周期" prop="silent" style="text-align: left">
        <el-input-number
          v-model="formData.silent"
          placeholder="请输入静默周期"
          style="width: 200px; margin-right: 10px"
          :min="1"
          :max="99999"
          number
        />
        <el-select v-model="formData.silentUnit" style="width: 100px">
          <el-option
            v-for="item in [
              { value: 1000, label: '秒' },
              { value: 60000, label: '分' },
              { value: 3600000, label: '小时' },
              { value: 86400000, label: '天' }
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="!isBatchJob" label="生效时间" required style="text-align: left">
        <div style="display: flex; align-items: center">
          <el-form-item prop="effectiveStartTime">
            <el-time-select
              v-model="formData.effectiveStartTime"
              style="width: 200px"
              :picker-options="{
                start: '00:00',
                step: '00:15',
                end: '23:59',
                maxTime: formData.effectiveEndTime
              }"
              placeholder="起始时间"
              @change="effectiveStartTimeChange"
            />
          </el-form-item>
          <el-form-item prop="effectiveEndTime">
            <el-time-select
              v-model="formData.effectiveEndTime"
              style="width: 200px; margin-left: 10px"
              :picker-options="{
                start: '00:00',
                step: '00:15',
                end: '24:00',
                minTime: formData.effectiveStartTime
              }"
              placeholder="结束时间"
              @change="effectiveEndTimeChange"
            />
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item
        v-if="formData.ruleType === 'KAFKA_LAG'"
        label="迟滞阈值"
        prop="threshold"
        style="text-align: left"
      >
        <el-input
          v-model="formData.threshold"
          placeholder="请输入阈值"
          type="number"
          style="width: 200px"
        />
        <el-tooltip class="item" effect="light" placement="bottom">
          <div slot="content" style="width: 400px">设置在消费kafka消息时数据积压量的预警阈值</div>
          <span class="iconfont icon-wenhao" style="margin-left: 5px"></span>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="通知号码" prop="sendName" style="text-align: left">
        <el-select v-model="formData.sendName" style="width: 100%">
          <el-option
            v-for="item in sendNameList"
            :key="item.code"
            :label="item.value1"
            :value="item.value1"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" :loading="loading" @click="submit('ruleForm')">保存</el-button>
      <el-button
        v-if="hasFeatureAuthority('PA.MONITOR.WARN.ON')"
        type="primary"
        :loading="loading"
        @click="submit('ruleForm', true)"
      >
        保存并重启
      </el-button>
    </span>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Watch, Emit } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import {
  URL_DIC_GETSUBBYPARENT,
  URL_WARNRULE_UPDATE,
  URL_PORTAL_LISTUSERBYORGID,
  URL_WARNRULE_UPDATESTATE
} from '@/apis/commonApi';
import * as _ from 'lodash';

@Component
export default class Edit extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: '新建' }) title!: string;
  @Prop() data: any;

  formData: any = {};
  loading = false;

  rules: any = {
    title: [
      { required: true, message: '请输入名称', trigger: 'blur' },
      { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
    ],
    cron: [{ required: true, message: '请输入执行周期', trigger: 'blur' }],
    noticeMsg: [{ required: true, message: '请输入预警模板', trigger: 'blur' }],
    threshold: [{ required: true, message: '请输入阈值', trigger: 'blur' }],
    noticeUser: [{ required: true, message: '请选择通知人', trigger: 'blur' }],
    silent: [{ required: true, message: '请输入静默周期', trigger: 'blur' }],
    effectiveStartTime: [{ required: true, message: '请选择开始时间', trigger: 'blur' }],
    effectiveEndTime: [{ required: true, message: '请选择结束时间', trigger: 'blur' }]
  };
  noticMsgList: any = [];
  userList: any = [];
  sendNameList: any = [];

  get isBatchJob() {
    return this.formData.resType === 'BATCH_JOB';
  }
  @Emit('close')
  private closeDialog() {
    this.formData = {};
    this.loading = false;
  }

  mounted() {
    this.getNoticMsgList();
    this.getSendNameList();
  }

  handleDropdownClick(val: any) {
    this.formData.noticeMsg = val;
  }
  effectiveStartTimeChange(val: any) {
    if (val !== null && val.length === 5) {
      this.formData.effectiveStartTime = this.formData.effectiveStartTime + ':00';
    }
  }
  effectiveEndTimeChange(val: any) {
    if (val !== null && val.length === 5) {
      this.formData.effectiveEndTime = this.formData.effectiveEndTime + ':00';
    }
    if (val !== null && val === '24:00') {
      this.formData.effectiveEndTime = '23:59:59';
    }
  }
  submit(formName: string, isRestart = false) {
    if (this.formData.noticeUser.length === 0) {
      this.$message.warning('至少需要配置一个通知人');
      return;
    }
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        this.loading = true;
        this.doPut(URL_WARNRULE_UPDATE, this.formData).then((resp: any) => {
          if (!isRestart) {
            this.parseResp(resp);
          } else {
            this.doPut(URL_WARNRULE_UPDATESTATE, [
              {
                id: this.data.id,
                stateType: 'ON'
              }
            ]).then((res: any) => {
              this.parseResp(res);
            });
          }
        });
      } else {
        this.$message.error('请检查输入内容');
        return false;
      }
    });
  }

  parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.closeDialog();
    });
  }

  getNoticMsgList() {
    this.doGet(URL_DIC_GETSUBBYPARENT, {
      params: {
        parentCode: 'notice'
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.noticMsgList = resp.data;
      });
    });
  }

  getSendNameList() {
    this.doGet(URL_DIC_GETSUBBYPARENT, {
      params: {
        parentCode: 'sendName'
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.sendNameList = resp.data;
      });
    });
  }

  getUserList() {
    this.doGet(URL_PORTAL_LISTUSERBYORGID, {
      params: {
        orgId: this.data.orgId
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.userList = resp.data;
      });
    });
  }

  noticeUserChange(val) {
    if (val.length === 0) {
      this.$message.warning('至少需要配置一个通知人');
      return;
    }
    if (val.length > 5) {
      this.formData.noticeUser.splice(0, 1);
      this.$message.warning('通知人最多选择5个');
      return;
    }
  }

  @Watch('visible')
  onVisibleChange(val) {
    if (val) {
      this.getUserList();
      this.formData = _.cloneDeep(this.data);
      this.formData.noticeUser = JSON.parse(this.data.noticeUser);
    }
  }
}
</script>

<style lang="scss" scoped>
.warn {
  &-notify {
    display: flex;
    align-items: center;
  }
}
</style>
