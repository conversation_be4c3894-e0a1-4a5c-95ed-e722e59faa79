const name = (type) => {
  switch (type) {
    case 'JDBC':
      return '数据库';
    case 'STREAMCUBE':
      return '计算引擎';
    case 'FLINK':
      return '加工引擎';
    case 'HOST':
      return '主机';
    case 'HTTP':
      return '外部http接口';
    default:
      return type.toLowerCase();
  }
};
const router = [
  {
    path: '/element',
    name: 'element',
    meta: {
      access: 'PA.ELE', // 权限信息
      title: '元件管理', // 权限信息
      icon: 'iconfont icon-yuanjianguanli'
    },
    redirect: '/element/service',
    component: () => import('../views/element/index.vue'),
    children: [
      {
        path: '/element/service',
        name: 'elementService',
        meta: {
          access: 'PA.ELE.SERVICE.MENU', // 权限信息
          title: '服务管理'
        },
        component: () => import('../views/element/service/index.vue')
      },
      {
        path: '/element/clusters/:type',
        name: 'elementClusters',
        beforeEnter: (to, from, next) => {
          // 获取动态路由标题
          to.meta.title = to.query.title;
          to.meta.resType = to.query.type;
          next();
        },
        component: () => import('@/views/element/service/custom/index.vue')
      },
      {
        path: '/element/clusters/:type/detail',
        name: 'clustersDetail',
        beforeEnter: (to, from, next) => {
          const { resType, title } = to.query;
          // 获取动态路由标题
          to.meta.title = name(resType) + '：' + title;
          next();
        },
        component: () => import('@/views/element/service/custom/detail.vue')
      },
      {
        path: '/element/service/:type/detail',
        name: 'ServiceDetail',
        meta: {
          access: ''
          // title: ''
        },
        beforeEnter: (to, from, next) => {
          // 获取动态路由标题
          to.meta.title = to.query.title;
          next();
        },
        component: () => import('@/views/element/service/custom/detail.vue')
      },
      {
        path: '/element/warehouse',
        name: 'elementWarehouse',
        meta: {
          access: 'PA.ELE.WAREHOUSE.MENU', // 权限信息
          title: '组件库管理'
        },
        component: () => import('../views/element/warehouse/index.vue')
      },
      {
        path: '/element/installPkg',
        name: 'elementInstallPkg',
        meta: {
          access: 'PA.ELE.PKG.MENU', // 权限信息
          title: '安装包'
        },
        component: () => import('../views/element/install-pkg/index.vue')
      }
    ]
  }
];

export { router };
