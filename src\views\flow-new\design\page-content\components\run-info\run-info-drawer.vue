<template>
  <div v-if="display" v-loading="loading" class="run-info__container">
    <!-- header -->
    <div class="run-info__header bs-pro-grid--info">
      <div class="bs-pro-grid__header-title">{{ $t('pa.flow.flowRunInfo') }}</div>
      <!-- button -->
      <div>
        <el-button type="primary" size="small" @click="handleRefresh(true)"> {{ $t('pa.action.refresh') }} </el-button>
        <el-button type="text" size="small" @click="display = false">
          <i class="iconfont icon-close-small"></i>
        </el-button>
      </div>
    </div>
    <!-- main -->
    <info-desc :data="data" />
  </div>
</template>
<script lang="ts">
import { Component, PropSync, Vue } from 'vue-property-decorator';
import { getJobRunInfo } from '@/apis/flowNewApi';

@Component({ components: { InfoDesc: () => import('./info-desc.vue') } })
export default class RunInfoDialog extends Vue {
  @PropSync('show', { default: false }) display!: boolean;

  loading = false;
  jobId = '';
  data: any = {};

  created() {
    this.jobId = (this.$route.query.flowId as string) || '';
    this.handleRefresh();
  }

  async handleRefresh(isRefresh = false) {
    try {
      this.loading = true;
      const { success, data, msg } = await getJobRunInfo(this.jobId);
      if (!success) return this.$message.error(msg);
      this.data = { ...data };
      isRefresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    } finally {
      this.loading = false;
    }
  }
}
</script>
<style lang="scss" scoped>
.run-info {
  &__container {
    position: absolute;
    bottom: 0px;
    z-index: 10;
    padding: 0 20px;
    height: 300px;
    width: 100%;
    background: #fff;
    border-top: 1px solid $--bs-color-border-lighter;
  }
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
  }
}
</style>
