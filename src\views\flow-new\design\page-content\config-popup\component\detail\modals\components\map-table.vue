<template>
  <el-table border stripe :height="height" :data="data" class="map-table">
    <el-table-column
      v-for="el in thead"
      :key="el.prop"
      :prop="el.prop"
      :label="el.label"
      :min-width="el.minWidth"
      align="center"
    >
      <template slot-scope="scope">
        <el-form-item v-if="el.isSelect" class="map-table__item">
          <el-select v-model="form[getProp(scope)]" clearable size="mini" filterable>
            <el-option v-for="item in getOptions(scope)" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <span v-else>{{ scope.row[el.prop] }}</span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { cloneDeep, isEmpty } from 'lodash';

@Component
export default class MapTable extends Vue {
  @Prop() data!: any;
  @Prop({ type: Number }) height!: number;
  @Prop({ type: Array, default: () => [] }) thead!: any[];

  private form: any = {}; // 表单

  @Watch('data', { immediate: true, deep: true })
  handleDataChange(val: any) {
    this.form = {};
    val.forEach(({ name, defaultValue }: any) => {
      this.$set(this.form, name, defaultValue);
    });
  }

  @Watch('form', { deep: true })
  handleFormChange(val: any) {
    if (!isEmpty(val)) {
      this.$emit('change', cloneDeep(val));
    }
  }

  getProp(data: any) {
    return data.row.name;
  }

  getOptions(data: any) {
    return data.row.options || [];
  }
}
</script>
<style lang="scss" scoped>
.map-table {
  &__item {
    margin: 0 !important;

    ::v-deep .el-form-item__content {
      margin: 0 45px !important;
    }
  }
}
</style>
