<template>
  <pro-page :title="$t('pa.menu.sheetManage')" :fixed-header="false">
    <div slot="operation" :class="['operate-box', isEn ? 'is-en' : '']">
      <bs-cascader
        v-model="selectedType"
        class="operate-box__cascader"
        collapse-tags
        :placeholder="$t('pa.data.table.placeholder.select')"
        :options="typeList"
        :props="cascaderProps"
        @change="handleCascaderChange"
      />
      <bs-search
        v-model="searchObj.search.tableName"
        :placeholder="$t('pa.data.table.placeholder.inputPlaceholder1')"
        maxlength="128"
        size="small"
        style="margin-left: 10px"
        @input="handleSearch"
      />
      <bs-search
        v-model="searchObj.search.fieldName"
        :placeholder="$t('pa.data.table.placeholder.inputPlaceholder2')"
        maxlength="20"
        size="small"
        style="margin-left: 10px"
        @input="handleSearch"
      />
      <el-button v-if="hasFeatureAuthority('PA.DATA.TABLE.ADD')" type="primary" style="margin-left: 10px" @click="editChart">
        {{ $t('pa.data.table.addTable') }}
      </el-button>
    </div>
    <bs-table
      v-loading="tableLoading"
      selection
      :height="selectedList.length ? 'calc(100vh - 347px)' : 'calc(100vh - 290px)'"
      :data="tableData.tableData"
      :column-data="tableData.columnData"
      :page-data="tableData.pageData"
      @row-dblclick="dblclick"
      @page-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      @refresh="getListData(true)"
    >
      <el-button
        v-if="hasFeatureAuthority('PA.DATA.TABLE.DELETE')"
        slot="headerOperator"
        size="small"
        @click="delChart(null)"
      >
        {{ $t('pa.action.del') }}
      </el-button>
      <div slot="tableName" slot-scope="{ row }" class="viewName-slot">
        <el-tooltip :content="row.tableName" effect="light" placement="top">
          <div class="viewName">{{ row.tableName }}</div>
        </el-tooltip>
        <el-tooltip
          v-if="row.isUpdate && (row.resType === 'HBASE' || row.resType === 'MYSQL')"
          :content="$t('pa.data.table.tooltip.tooltip1')"
          effect="light"
        >
          <el-tag size="mini" style="margin-left: 8px" type="danger"> {{ $t('pa.data.table.change') }} </el-tag>
        </el-tooltip>
        <el-tag v-if="row.state === 1" size="mini" style="margin-left: 8px"> {{ $t('pa.data.table.incomplete') }} </el-tag>
        <el-tag v-if="row.shareFlag" size="mini" style="margin-left: 8px"> {{ $t('pa.action.share') }} </el-tag>
      </div>
      <div slot="shareFlag" slot-scope="{ row }">
        {{ row.shareFlag ? $t('pa.action.share') : $t('pa.action.buildOneself') }}
      </div>
      <template slot="header-relationNum">
        <div class="sheet-manage__relations">
          <span class="sheet-manage__relations-text" :title="$t('pa.data.text1')"> {{ $t('pa.data.text1') }} </span>
          <el-tooltip effect="light" :content="$t('pa.data.text2')">
            <i class="iconfont icon-wenhao"></i>
          </el-tooltip>
        </div>
      </template>

      <template slot="operator" slot-scope="{ row }">
        <el-tooltip
          v-if="hasAuthority(hasFeatureAuthority('PA.DATA.TABLE.EDIT'), row) && !row.shareFlag"
          :content="$t('pa.action.edit')"
          effect="light"
        >
          <i class="iconfont icon-bianji" @click="editChart(row)"></i>
        </el-tooltip>
        <el-tooltip
          v-if="hasAuthority(hasFeatureAuthority('PA.DATA.TABLE.COPY'), row) && !row.shareFlag"
          :content="$t('pa.action.copy')"
          effect="light"
        >
          <i class="iconfont icon-fuzhi1" @click="copy(row)"></i>
        </el-tooltip>
        <el-tooltip v-access="'PA.DATA.TABLE.VIEW_DETAIL'" :content="$t('pa.action.view')" effect="light">
          <i class="iconfont icon-chakan" @click="detail(row)"></i>
        </el-tooltip>
        <el-tooltip
          v-if="hasAuthority(hasFeatureAuthority('PA.DATA.TABLE.DELETE'), row) && !row.shareFlag"
          :content="$t('pa.action.edit')"
          effect="light"
        >
          <i class="iconfont icon-shanchu" @click="delChart(row)"></i>
        </el-tooltip>
        <el-tooltip v-if="row.state !== 1" :content="$t('pa.action.share')" effect="light">
          <i
            v-show="hasAuthority(hasFeatureAuthority('PA.DATA.TABLE.SHARE'), row) && !row.shareFlag"
            class="iconfont icon-fenxiang"
            @click="deptRole(row)"
          ></i>
        </el-tooltip>
      </template>
    </bs-table>
    <share-dialog
      v-if="showDeptRoleDialog"
      ref="ShareDialog"
      :visible.sync="showDeptRoleDialog"
      :data="deptData"
      type="table"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import * as _ from 'lodash';
import dayjs from 'dayjs';
import CommonDelete from '@/utils/mixins/common-delete';
import { getChartList, getSheetManageResList, delCharts } from '@/apis/dataApi';
import { hasPermission } from '@/utils';
interface CascaderOption {
  label: string;
  value: string;
  disabled?: boolean;
  children?: CascaderOption[];
}
@Component({
  components: {
    ShareDialog: () => import('@/components/share-dialog/index.vue')
  }
})
export default class SheetManage extends Mixins(CommonDelete) {
  tableLoading = false;
  searchObj = {
    pageData: { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 },
    search: {
      fieldName: '', // 字段名称
      resType: '', // 服务类型
      tableName: '',
      label: '',
      connectorId: '' // 连接器ID
    }
  };
  cascaderProps = {
    checkStrictly: true,
    multiple: true,
    emitPath: false
  };
  // 表格配置
  tableData: ITableData = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  // 服务列表
  resType = '';
  // 批量删除选择的数据
  selectedList: string[] = [];
  typeList: CascaderOption[] = [];

  // 分享弹窗相关
  showDeptRoleDialog = false;
  deptData: any = {};
  selectedType: string[] = [];
  created() {
    this.getListData();
    this.getResTypeList();
  }
  // 表格页码更改回调
  handleCurrentChange(val, size) {
    if (this.searchObj.pageData.pageSize === size) {
      this.searchObj.pageData.currentPage = val;
    } else {
      this.searchObj.pageData.pageSize = size;
      this.searchObj.pageData.currentPage = 1;
    }
    this.getListData();
  }
  show(row) {
    return (row.isUpdate && (row.resType === 'HBASE' || row.resType === 'MYSQL')) || row.shareFlag;
  }
  handleCascaderChange() {
    // 父级value集合
    const parentNames = this.typeList.map((el) => el.value);
    // 父级子级disabled属性置为false
    const setOptionDisabledFalse = (index) => {
      this.$set(this.typeList[index], 'disabled', false);
      this.typeList[index].children && (this.typeList[index].children || []).forEach((ele) => (ele.disabled = false));
    };
    this.typeList.forEach((el, index) => {
      // 取消选中，设置所有option的disabled为false
      if (!this.selectedType.length) {
        setOptionDisabledFalse(index);
      } else {
        // 取消选中父级（包含子级）
        if (!this.selectedType.some((ele) => parentNames.includes(ele)) && this.selectedType.length > 1) {
          // 清空选中项，将所有选项的disabled置为false
          this.$set(this, 'selectedType', []);
          return setOptionDisabledFalse(index);
        }
        // 将未选中项禁用
        if (el.value !== this.selectedType[0]) {
          this.$set(this.typeList[index], 'disabled', true);
          el.children && (el.children || []).forEach((ele) => (ele.disabled = !(ele.value === this.selectedType[0])));
        } else {
          // 选中父级时，将子级禁用
          this.$set(this.typeList[index], 'disabled', false);
          el.children &&
            (el.children || []).forEach((ele) => {
              ele.disabled = true;
              this.selectedType.push(ele.value);
            });
        }
      }
    });
    // 组装搜索项
    Object.assign(this.searchObj.search, {
      connectorId: !parentNames.includes(this.selectedType[0]) ? this.selectedType[0] : '', // 选中子级时，connectorId为子级ID
      resType: parentNames.includes(this.selectedType[0]) ? this.selectedType[0] : '' // 选中父级时，resType为选中的type
    });
    this.handleSearch();
  }
  showName(title, num) {
    return title.length > num ? title.slice(0, num) + '...' : title;
  }
  // 打开分享弹窗
  deptRole(row) {
    this.deptData = row;
    this.showDeptRoleDialog = true;
  }
  handleSearch() {
    this.searchObj.pageData.currentPage = 1;
    this.getListData();
  }
  // 复制
  copy(row: any) {
    this.$router.push({
      path: '/data/sheetEdit',
      query: { id: row.id, status: 'copy', title: row.tableName, memo: row.memo }
    });
  }

  // 跳转详情
  detail(row: any) {
    this.$router.push({
      path: '/data/sheetDetail',
      query: { id: row.id, title: row.tableName }
    });
  }
  // 跳转新建编辑
  editChart(row: any) {
    if (row.id) {
      this.$router.push({
        path: '/data/sheetEdit',
        query: {
          id: row.id,
          status: 'edit',
          title: row.tableName,
          update: row.isUpdate,
          state: row.state
        }
      });
    } else {
      this.$router.push({
        path: '/data/sheetEdit',
        query: { status: 'add', title: this.$t('pa.data.table.addTable') }
      });
    }
  }
  // 删除视图 TODO:
  async delChart(row: any) {
    const ids = !row
      ? this.selectedList.map((e: any) => ({
          id: e.id,
          name: e.tableName,
          reference: e.relationNum
        }))
      : {
          id: row.id,
          name: row.tableName,
          reference: row.relationNum
        };
    this.commonDel(ids, async (delIds) => {
      const { success, msg } = await delCharts(delIds);
      if (success) {
        this.getListData();
        this.$message.success(msg);
      } else {
        this.$message.error(msg);
      }
    });
  }
  // 获取服务类型列表
  async getResTypeList() {
    const { data, success, msg, error } = await getSheetManageResList();
    if (success) {
      this.typeList = Object.keys(data).map((el) => ({
        label: el,
        value: el,
        ...(Object.keys(data[el]).length > 1 && {
          children: Object.keys(data[el]).map((ele) => ({ label: ele, value: data[el][ele] }))
        })
      }));
      return;
    }
    this.$message.error(error || msg);
  }
  // 获取列表数据
  getListData(refresh = false) {
    this.tableLoading = true;
    const searchObj = _.cloneDeep(this.searchObj);
    searchObj.search.fieldName = searchObj.search.fieldName.trim();
    searchObj.search.tableName = searchObj.search.tableName.trim();
    const obj = {
      pageData: searchObj.pageData,
      search: searchObj.search,
      sortData: { updateTime: 'DESC' }
    };
    getChartList(obj).then((resp: any) => {
      this.tableLoading = false;
      if (!resp.success) return this.$message.error(resp.msg);
      const { columnData, tableData, pageData } = resp.data;
      columnData.forEach((el) => {
        if (el.prop === 'tableName') {
          el.width = 200;
          el.showOverflowTooltip = false;
        }
        if (el.prop != 'tableName' && el.prop != 'updateTime') {
          el.width = 85;
        }
        el.prop === 'relationNum' && (el.width = 110);
        el.value = el.prop;
      });
      columnData.push({ label: this.$t('pa.action.action'), value: 'operator', width: 180, fixed: 'right' });
      tableData.forEach((el) => {
        el.updateTime = dayjs(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
      });
      this.tableData = {
        columnData,
        tableData,
        pageData
      };
      refresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    });
  }
  // 表格勾选回调
  handleSelectionChange(sel: any) {
    this.selectedList = sel;
  }
  // 双击跳转详情
  dblclick(row) {
    if (!hasPermission('PA.DATA.TABLE.VIEW_DETAIL')) return;
    this.$router.push({
      path: '/data/sheetDetail',
      query: { id: row.id, title: row.tableName }
    });
  }
  hasAuthority(hasRole: any, row: any) {
    return (
      !_.isEmpty(_.toString(row.dataLevelType)) && row.dataLevelType !== 'PARENT' && row.dataLevelType !== 'NONE' && hasRole
    );
  }
  /**
   * 按钮功能权限控制（不能操作上级数据）
   */
  hasFeatureAuthority(code: any, dataLevel?: string) {
    if (code === undefined) {
      return false;
    }
    // 不能修改上级数据
    let isNotParent = false;
    if (dataLevel !== undefined) {
      isNotParent = dataLevel !== 'PARENT';
    } else {
      isNotParent = true;
    }
    return isNotParent && this.$store.getters.authorities.includes(code);
  }
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // 通过 `vm` 访问组件实例
      if (from.name === 'sheetEdit') {
        vm.getListData();
      }
    });
  }
}
</script>

<style scoped lang="scss">
.viewName-slot {
  display: flex;
  align-items: center;
}
.viewName {
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.iconfont {
  cursor: pointer;
}
.iconfont + .iconfont {
  margin-left: 10px;
}
.operate-box {
  &__cascader {
    ::v-deep .el-input__inner {
      max-height: 32px;
    }
  }
}
.operate-box.is-en {
  .operate-box__cascader > ::v-deep .el-input {
    width: 160px;
  }
  .bs-search,
  .bs-search > ::v-deep .el-input {
    width: 260px !important;
  }
}
.sheet-manage {
  &__relations {
    display: flex;
    align-items: center;
    .icon-wenhao {
      margin-left: 5px;
    }
    &-text {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
