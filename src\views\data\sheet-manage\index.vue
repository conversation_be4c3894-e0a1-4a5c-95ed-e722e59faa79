<template>
  <pro-page title="表管理" :fixed-header="false">
    <div slot="operation" class="operate-box">
      <el-select
        v-model="searchObj.search.resType"
        clearable
        placeholder="请选择服务类型"
        size="small"
        @change="handleSearch"
      >
        <el-option
          v-for="item in resTypeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <bs-search
        v-model="searchObj.search.tableName"
        placeholder="请输入表名称，中文名称"
        maxlength="128"
        size="small"
        style="margin-left: 10px"
        @input="handleSearch"
      />
      <bs-search
        v-model="searchObj.search.fieldName"
        placeholder="请输入字段名，中文名"
        maxlength="20"
        size="small"
        style="margin-left: 10px"
        @input="handleSearch"
      />
      <el-button
        v-if="hasFeatureAuthority('PA.DATA.TABLE.ADD')"
        type="primary"
        style="margin-left: 10px"
        @click="editChart"
      >
        新建表
      </el-button>
    </div>
    <bs-table
      v-loading="tableLoading"
      selection
      :height="selectedList.length ? 'calc(100vh - 347px)' : 'calc(100vh - 290px)'"
      :data="tableData.tableData"
      :column-data="tableData.columnData"
      :page-data="tableData.pageData"
      @row-dblclick="dblclick"
      @page-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      @refresh="getListData"
    >
      <el-button
        v-if="hasFeatureAuthority('PA.DATA.TABLE.DELETE')"
        slot="headerOperator"
        size="small"
        @click="delChart(null)"
      >
        删除
      </el-button>
      <div slot="tableName" slot-scope="{ row }" class="viewName-slot">
        <el-tooltip :content="row.tableName" effect="light" placement="top">
          <div class="viewName">{{ row.tableName }}</div>
        </el-tooltip>
        <el-tooltip content="表结构变更，请及时同步" effect="light">
          <el-tag
            v-if="row.isUpdate && (row.resType === 'HBASE' || row.resType === 'MYSQL')"
            size="mini"
            style="margin-left: 8px"
            type="danger"
          >
            变更
          </el-tag>
        </el-tooltip>
        <el-tag v-if="row.state === 1" size="mini" style="margin-left: 8px"> 待完善 </el-tag>
        <el-tag v-if="row.shareFlag === '分享'" size="mini" style="margin-left: 8px"> 分享 </el-tag>
      </div>
      <template slot="operator" slot-scope="{ row }">
        <el-tooltip content="编辑" effect="light">
          <i
            v-if="hasAuthority(hasFeatureAuthority('PA.DATA.TABLE.EDIT'), row) && !row.share"
            class="iconfont icon-bianji"
            @click="editChart(row)"
          ></i>
        </el-tooltip>
        <el-tooltip
          v-if="hasAuthority.bind(this, hasFeatureAuthority('PA.DATA.TABLE.COPY'))"
          content="复制"
          effect="light"
        >
          <i class="iconfont icon-fuzhi1" @click="copy(row)"></i>
        </el-tooltip>
        <el-tooltip content="查看" effect="light">
          <i class="iconfont icon-chakan" @click="detail(row)"></i>
        </el-tooltip>
        <el-tooltip content="删除" effect="light">
          <i
            v-if="hasAuthority(hasFeatureAuthority('PA.DATA.TABLE.DELETE'), row) && !row.share"
            class="iconfont icon-shanchu"
            @click="delChart(row)"
          ></i>
        </el-tooltip>
        <el-tooltip v-if="row.state !== 1" content="分享" effect="light">
          <i
            v-if="hasAuthority(hasFeatureAuthority('PA.DATA.TABLE.SHARE'), row) && !row.share"
            class="iconfont icon-fenxiang"
            @click="deptRole(row)"
          ></i>
        </el-tooltip>
      </template>
    </bs-table>
    <ShareTree
      v-if="showShareTreeDialog"
      ref="ShareTree"
      :visible.sync="showShareTreeDialog"
      :data="deptData"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import * as _ from 'lodash';
import ShareTree from '@/components/share-tree.vue';
import moment from 'moment';
import CommonDelete from '@/utils/mixins/common-delete';
import { getChartList, getResList, delCharts } from '@/apis/dataApi';
@Component({
  components: {
    ShareTree
  }
})
export default class SheetManage extends Mixins(CommonDelete) {
  tableLoading = false;
  searchObj = {
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    search: {
      fieldName: '', // 字段名称
      resType: '', // 服务类型
      tableName: '',
      label: ''
    }
  };
  // 表格配置
  tableData: ITableData = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  // 服务列表
  resType = '';
  resTypeList = [
    {
      value: '',
      label: ''
    }
  ];

  // 批量删除选择的数据
  selectedList: string[] = [];

  // 分享弹窗相关
  showShareTreeDialog = false;
  deptData: any = {};
  created() {
    this.getListData();
    this.getResTypeList();
    //  (this.searchObj.search as any) = this.$route.query.resTitle;
  }
  // 表格页码更改回调
  handleCurrentChange(val, size) {
    if (this.searchObj.pageData.pageSize === size) {
      this.searchObj.pageData.currentPage = val;
    } else {
      this.searchObj.pageData.pageSize = size;
      this.searchObj.pageData.currentPage = 1;
    }
    this.getListData();
  }
  show(row) {
    return (
      (row.isUpdate && (row.resType === 'HBASE' || row.resType === 'MYSQL')) ||
      row.shareFlag === '分享'
    );
  }

  showName(title, num) {
    return title.length > num ? title.slice(0, num) + '...' : title;
  }
  // 打开分享弹窗
  deptRole(row) {
    this.deptData = row;
    this.showShareTreeDialog = true;
  }
  handleSearch() {
    this.searchObj.pageData.currentPage = 1;
    this.getListData();
  }
  // 复制
  copy(row: any) {
    this.$router.push({
      path: '/data/sheetEdit',
      query: { id: row.id, status: '2', title: row.tableName, copy: 'copy', memo: row.memo }
    });
  }

  // 跳转详情
  detail(row: any) {
    this.$router.push({
      path: '/data/sheetDetail',
      query: { id: row.id, status: '1', title: row.tableName, showEdit: row.share }
    });
  }
  // 跳转新建编辑
  editChart(row: any) {
    if (row.id) {
      this.$router.push({
        path: '/data/sheetEdit',
        query: {
          id: row.id,
          status: '2',
          title: row.tableName,
          update: row.isUpdate,
          state: row.state
        }
      });
    } else {
      this.$router.push({
        path: '/data/sheetEdit',
        query: { status: '0', title: '新建表' }
      });
    }
  }
  // 删除视图 TODO:
  async delChart(row: any) {
    const ids = !row
      ? this.selectedList.map((e: any) => ({
          id: e.id,
          name: e.tableName,
          reference: e.relationNum
        }))
      : {
          id: row.id,
          name: row.tableName,
          reference: row.relationNum
        };
    this.commonDel(ids, async (delIds) => {
      const { success, msg } = await delCharts(delIds);
      if (success) {
        this.getListData();
        this.$message.success(msg);
      } else {
        this.$message.error(msg);
      }
    });
  }
  getResTypeList() {
    getResList().then((resp: any) => {
      this.resTypeList = (resp.data || []).map((item) => {
        return {
          value: item,
          label: item
        };
      });
    });
  }
  // 获取列表数据
  getListData() {
    this.tableLoading = true;
    const searchObj = _.cloneDeep(this.searchObj);
    searchObj.search.fieldName = searchObj.search.fieldName.trim();
    searchObj.search.tableName = searchObj.search.tableName.trim();
    const obj = {
      pageData: searchObj.pageData,
      search: searchObj.search,
      sortData: { updateTime: 'DESC' }
    };
    getChartList(obj).then((resp: any) => {
      const { columnData, tableData, pageData } = resp.data;
      columnData.forEach((el) => {
        if (el.prop === 'tableName') {
          el.width = 200;
          el.showOverflowTooltip = false;
        }
        if (el.prop != 'tableName' && el.prop != 'updateTime') {
          el.width = 85;
        }
        el.value = el.prop;
      });
      columnData.push({ label: '操作', value: 'operator', width: 180, fixed: 'right' });
      tableData.forEach((el) => {
        el.share = el.shareFlag;
        el.shareFlag = el.shareFlag ? '分享' : '自建';
        el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
      });
      this.tableData = {
        columnData,
        tableData,
        pageData
      };
      this.tableLoading = false;
    });
  }
  // 表格勾选回调
  handleSelectionChange(sel: any) {
    this.selectedList = sel;
  }
  // 双击跳转详情
  dblclick(row) {
    this.$router.push({
      path: '/data/sheetDetail',
      query: { id: row.id, status: '1', title: row.tableName, showEdit: row.share }
    });
  }
  hasAuthority(hasRole: any, row: any) {
    return (
      !_.isEmpty(_.toString(row.dataLevelType)) &&
      row.dataLevelType !== 'PARENT' &&
      row.dataLevelType !== 'NONE' &&
      hasRole
    );
  }
  /**
   * 按钮功能权限控制（不能操作上级数据）
   */
  hasFeatureAuthority(code: any, dataLevel?: string) {
    if (code === undefined) {
      return false;
    }
    // 不能修改上级数据
    let isNotParent = false;
    if (dataLevel !== undefined) {
      isNotParent = dataLevel !== 'PARENT';
    } else {
      isNotParent = true;
    }
    return isNotParent && this.$store.state.userInfo.authorities.includes(code);
  }
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // 通过 `vm` 访问组件实例
      if (from.name === 'sheetEdit') {
        vm.getListData();
      }
    });
  }
}
</script>

<style scoped lang="scss">
.viewName-slot {
  display: flex;
  align-items: center;
}
.viewName {
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.iconfont {
  cursor: pointer;
}
.iconfont + .iconfont {
  margin-left: 10px;
}
</style>
