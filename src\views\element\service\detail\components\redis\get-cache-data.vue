<template>
  <query-block
    :loading="loading"
    :title="$t('pa.cacheQuery')"
    :result="result"
    label-width="100px"
    :form-data="formData"
    :form-item="formItem"
    :form-rule="formRule"
    @submit="handleSubmit"
  />
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getServerCacheData } from '@/apis/serviceApi';

@Component({
  components: { QueryBlock: () => import('../components/query-block.vue') }
})
export default class RedisCacheData extends Vue {
  @Prop({ default: () => ({}) }) data!: any;

  loading = false;
  result = '';
  formData: any = { id: this.data.id, database: 0, key: '' };
  formItem: any[] = [
    { label: 'database', value: 'database', type: 'number', props: { min: 0, max: 15 } },
    { label: 'key', value: 'key', type: 'input', props: {} }
  ];
  formRule: any = {
    database: [{ required: true, message: this.$t('pa.placeholder.database'), trigger: 'blur' }],
    key: [{ required: true, message: this.$t('pa.placeholder.key'), trigger: 'blur' }]
  };

  async handleSubmit() {
    try {
      this.loading = true;
      const { success, data, error } = await getServerCacheData('redis', this.formData);
      if (!success) return this.$message.error(error);
      this.result = data || this.$t('pa.noData');
    } finally {
      this.loading = false;
    }
  }
}
</script>
