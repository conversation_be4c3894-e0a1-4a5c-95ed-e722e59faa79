<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>rocketMQ</title>
    <defs>
        <linearGradient x1="100%" y1="8.28163186%" x2="17.2321999%" y2="71.4818937%" id="linearGradient-1">
            <stop stop-color="#F25962" offset="0%"></stop>
            <stop stop-color="#CB0319" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="95.5459757%" y1="17.676126%" x2="0%" y2="88.809259%" id="linearGradient-2">
            <stop stop-color="#4C4BE8" offset="0%"></stop>
            <stop stop-color="#2F2576" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="2-1服务-全局视图" transform="translate(-1303.000000, -383.000000)">
            <g id="rocketMQ" transform="translate(1303.000000, 383.000000)">
                <rect id="矩形" x="0" y="0" width="40" height="40"></rect>
                <path d="M27.6862161,25.5528953 C27.638282,26.6202612 27.3049247,27.6315517 26.6861443,28.5867668 C25.7579738,30.0195894 22.6543397,32.1330697 21.6928868,32.8938803 C21.666048,32.8865611 22.4616074,30.8935981 22.7458046,29.4158565 C23.015044,28.0158909 24.1812271,27.3637213 24.5304316,27.1310312 L24.5807738,27.0960772 C24.7589087,26.964418 25.7940562,26.450024 27.6862161,25.5528953 Z M31.8092755,16.1348854 C32.5134377,16.2861675 33.118714,16.7784033 33.6251043,17.6115927 C29.7313651,22.4223141 24.7645009,25.8834474 18.7245115,27.9949925 C18.7367826,27.9584769 17.4396965,25.6886099 18.4454105,24.5618039 C19.4511244,23.4349979 20.0456097,24.587283 24.9697328,21.4218594 C29.8938559,18.2564359 30.0288879,15.7524003 31.8092755,16.1348854 Z M20.1171761,18.7826464 C21.0798774,18.7826464 21.8603009,19.5714364 21.8603009,20.5444582 C21.8603009,21.51748 21.0798774,22.3062699 20.1171761,22.3062699 C19.1544749,22.3062699 18.3740514,21.51748 18.3740514,20.5444582 C18.3740514,19.5714364 19.1544749,18.7826464 20.1171761,18.7826464 Z M25.3095698,7.30898897 C25.30926,7.3116301 25.3172269,7.32335279 25.3320836,7.34332271 L25.3959364,7.42713981 C25.7365565,7.87251529 26.9370296,9.47615553 26.2888008,10.6085037 C25.5368554,11.9220276 24.7183285,10.9198482 20.5529573,15.0508578 C16.3875861,19.1818674 16.7706011,21.6595595 14.9504396,21.6595595 C14.2305453,21.6595595 13.5372397,21.305275 12.8705228,20.5967059 C15.6895781,15.0728777 19.8359271,10.6436387 25.3095698,7.30898897 Z M24.2882247,15.2590229 C25.2509259,15.2590229 26.0313494,16.0478129 26.0313494,17.0208347 C26.0313494,17.9938564 25.2509259,18.7826464 24.2882247,18.7826464 C23.3255234,18.7826464 22.5450999,17.9938564 22.5450999,17.0208347 C22.5450999,16.0478129 23.3255234,15.2590229 24.2882247,15.2590229 Z M13.3919646,12.7393578 C14.437753,12.3076981 15.4835415,12.1699208 16.5293299,12.3260259 C15.2969835,14.0321533 14.5998808,14.9610245 14.4380216,15.1126394 C14.1952328,15.3400618 13.3243202,16.4905705 11.8348876,16.4905705 C10.345455,16.4905705 8.25805871,16.89961 8.25607132,16.8715853 C9.17843617,16.0628487 11.8232819,13.3868473 13.3919646,12.7393578 Z M37.9577212,5.40699868 C38.2286053,8.50282343 37.1978051,12.1200069 34.8653207,16.2585491 C33.3193298,15.5687316 32.5463344,14.5406113 32.5463344,13.1741882 C32.5463344,11.1245536 31.9819174,9.77470377 29.691161,9.49120688 C28.1639901,9.30220896 27.2379646,8.34780491 26.9130845,6.62799474 C31.1956086,5 34.8771542,4.59300132 37.9577212,5.40699868 Z M28.4592732,11.7353994 C29.4219744,11.7353994 30.202398,12.5241894 30.202398,13.4972112 C30.202398,14.4702329 29.4219744,15.2590229 28.4592732,15.2590229 C27.496572,15.2590229 26.7161484,14.4702329 26.7161484,13.4972112 C26.7161484,12.5241894 27.496572,11.7353994 28.4592732,11.7353994 Z" id="形状结合" fill="url(#linearGradient-1)"></path>
                <path d="M2,36 C6.26052829,29.5829122 9.42557357,25.5885669 11.4951358,24.0169639 C14.5994792,21.6595595 18.5035756,25.6895988 15.0682206,28.6323664 C12.7779839,30.5942115 8.42191038,33.0500894 2,36 Z" id="路径-6" fill="url(#linearGradient-2)"></path>
            </g>
        </g>
    </g>
</svg>
