import i18n from '@/i18n';
export const flowActionList = [
  {
    type: 'ElDropdown',
    value: 'add',
    icon: 'iconfont icon-xinjianxiangmu',
    access: ['PA.FLOW.FLOW_MGR.ADD'],
    options: [
      {
        access: 'PA.FLOW.FLOW_MGR.ADD',
        command: 'addFlow',
        text: i18n.t('pa.flow.creatFlow')
      },
      {
        access: 'PA.FLOW.CATALOGUE_MGR.ADD',
        command: 'addDir',
        text: i18n.t('pa.flow.createDir')
      }
    ]
  },
  {
    type: 'ElTooltip',
    value: 'batchFlow',
    content: i18n.t('pa.flow.batch'),
    icon: 'iconfont icon-piliang'
  },
  {
    type: 'ElPopover',
    value: 'filter',
    content: i18n.t('pa.flow.filter'),
    icon: 'iconfont icon-shaixuan',
    access: 'PA.FLOW.FLOW_MGR.FILTER'
  },
  {
    type: 'ElTooltip',
    value: 'search',
    content: i18n.t('pa.flow.seach1'),
    icon: 'iconfont icon-sousuo',
    access: 'PA.FLOW.FLOW_MGR.FILTER'
  },
  {
    type: 'ElTooltip',
    value: 'refresh',
    content: i18n.t('pa.flow.refresh'),
    icon: 'iconfont icon-shuaxin'
  }
];

export const batchActionList = [
  // {
  //   type: 'ElTooltip',
  //   value: 'publish',
  //   content: i18n.t('pa.flow.publish'),
  //   icon: 'iconfont icon-fabu',
  //   access: 'PA.FLOW.FLOW_MGR.PUBLISH'
  // },
  // {
  //   type: 'ElTooltip',
  //   value: 'cancelPublish',
  //   content: i18n.t('pa.flow.cancelPublish'),
  //   icon: 'iconfont icon-quxiaofabu',
  //   access: 'PA.FLOW.FLOW_MGR.CANCEL_PUBLISH'
  // },
  // {
  //   type: 'ElDropdown',
  //   value: 'online',
  //   icon: 'iconfont icon-qidong1',
  //   access: 'PA.FLOW.FLOW_MGR.ONLINE',
  //   options: [
  //     {
  //       content: i18n.t('pa.flow.msg10'),
  //       icon: 'el-icon-warning-outline',
  //       command: false,
  //       text: i18n.t('pa.flow.noStatusRun')
  //     },
  //     {
  //       content: i18n.t('pa.flow.msg11'),
  //       icon: 'el-icon-warning-outline',
  //       command: true,
  //       text: i18n.t('pa.flow.statusRun')
  //     }
  //   ]
  // },
  // {
  //   type: 'ElDropdown',
  //   value: 'offline',
  //   icon: 'iconfont icon-tingzhi',
  //   access: 'PA.FLOW.FLOW_MGR.OFFLINE',
  //   options: [
  //     {
  //       command: 'stop',
  //       text: i18n.t('pa.flow.stop')
  //     },
  //     {
  //       content: i18n.t('pa.flow.msg12'),
  //       icon: 'el-icon-warning-outline',
  //       command: 'retain',
  //       text: i18n.t('pa.flow.stopWithStatus')
  //     },
  //     {
  //       command: 'force',
  //       text: i18n.t('pa.flow.forceStop')
  //     }
  //   ]
  // },
  // {
  //   type: 'splitTag',
  //   value: '|'
  // },
  {
    type: 'ElTooltip',
    value: 'copyFlow',
    content: i18n.t('pa.flow.copyFlow'),
    icon: 'iconfont icon-fuzhi',
    access: 'PA.FLOW.FLOW_MGR.COPY'
  },
  {
    type: 'splitTag',
    value: '|'
  },
  {
    type: 'ElTooltip',
    value: 'moveFlow',
    content: i18n.t('pa.flow.moveFlow'),
    icon: 'iconfont icon-yidong',
    access: 'PA.FLOW.FLOW_MGR.MOVE'
  },
  {
    type: 'splitTag',
    value: '|'
  },
  {
    type: 'ElTooltip',
    value: 'delFlow',
    content: i18n.t('pa.flow.delFlow'),
    icon: 'iconfont icon-shanchu',
    access: 'PA.FLOW.FLOW_MGR.DELETE'
  }
];
