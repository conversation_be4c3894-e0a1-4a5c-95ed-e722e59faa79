<template>
  <pro-page :title="$t('pa.menu.mapTemplate')" :fixed-header="false" class="map">
    <div slot="operation">
      <bs-search
        v-model="searchObj.search"
        :placeholder="$t('pa.params.template.name')"
        style="width: 210px; margin-right: 10px"
        size="small"
        @input="debounceSearch"
      />
      <el-button v-access="'PA.SETTING.MODEL.MAPPING.ADD'" type="primary" size="small" @click="add">
        {{ $t('pa.action.add') }}
      </el-button>
    </div>
    <pro-table
      ref="proTable"
      :columns="columnData"
      :request="request"
      :actions="actions"
      :options="options"
      @action-click="handleActionClick"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { post, del } from '@/apis/utils/net';
import { cloneDeep, debounce } from 'lodash';
@Component({
  name: 'ElementMapTemplate'
})
export default class ElementMapTemplate extends PaBase {
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 }
  };
  columnData = [];
  actions = [
    {
      label: this.$t('pa.action.edit'),
      icon: 'iconfont icon-bianji',
      value: 'edit',
      access: 'PA.SETTING.MODEL.MAPPING.EDIT'
    },
    {
      label: this.$t('pa.action.del'),
      icon: 'iconfont icon-shanchu',
      value: 'del',
      access: 'PA.SETTING.MODEL.MAPPING.DELETE'
    },
    {
      label: this.$t('pa.action.viewVersion'),
      icon: 'iconfont icon-lishi',
      value: 'viewHistory',
      access: 'PA.SETTING.MODEL.MAPPING.MENU'
    }
  ];
  options = {
    actionHandle: ({ actions }) => {
      return actions.filter(({ access }) => this.$store.getters.authorities.includes(access));
    },
    pageOptions: {
      pageSize: this.$store.getters.pageSize,
      currentPage: 1,
      total: 1
    }
  };
  debounceSearch = debounce(this.search, 500);
  activated() {
    this.$refs.proTable!['loadData']();
  }

  handleActionClick(event, { row }) {
    this[event](row);
  }

  // 新建
  add() {
    this.$router.push({
      path: 'mapTemplateEdit',
      query: { id: '', title: this.$t('pa.params.template.addTemplate') as string }
    });
  }

  // 编辑
  edit(row) {
    this.$router.push({
      path: 'mapTemplateEdit',
      query: {
        id: row.id,
        title: `${this.$t('pa.menu.mapTemplate')}：${row.mappingTemplateName || this.$t('pa.params.template.editTemplate')}`
      }
    });
  }

  // 删除
  del(row) {
    this.$confirm(this.$t('pa.params.delConfirm') as string, this.$t('pa.prompt') as string).then(async () => {
      const { error, msg, success } = await del('/rs/pa/mappingGroup/deleteById', { id: row.id });
      if (success) {
        this.$message.success(this.$t('pa.params.template.delSuccess'));
        this.$refs.proTable!['loadData']();
      } else {
        this.$message({ message: msg || error, type: 'error' });
      }
    });
  }

  // 搜索
  search() {
    this.$refs.proTable!['loadDataAndReset']();
  }

  // 查看历史版本
  viewHistory(row) {
    this.$router.push({
      path: 'mapTemplateHistory',
      query: {
        id: row.id,
        title: `${this.$t('pa.params.template.historicalMapTemplate')}：${
          row.mappingTemplateName || this.$t('pa.params.template.historicalTemplate')
        }`
      }
    });
  }

  async request(page) {
    // 获取模板列表
    const searchObj = cloneDeep(this.searchObj);
    searchObj.search = searchObj.search.trim();
    const { data, success, msg } = await post('/rs/pa/mappingGroup/list', {
      search: this.searchObj.search,
      pageData: page
    });
    if (success) {
      data.columnData.forEach((el) => {
        if (el.prop) {
          el.value = el.prop;
          if (el.value === 'updateTime') el.valueProps = { type: 'datetime' };
        }
      });
      this.columnData = data.columnData;
      return { data: data.tableData, total: data.pageData.total };
    } else {
      this.$message.error(msg);
    }
  }
}
</script>

<style lang="scss" scoped>
.map {
  height: calc(100vh - 107px);
  &-search {
    width: 210px;
    margin-right: 10px;
  }
  &-tooltip {
    cursor: pointer;
  }
  &-icon {
    margin: 0 5px;
  }
  &-header {
    height: 50px;
    background: #ffffff;
    border-left: none;
    padding: 0 20px;
    font-size: 16px;
    display: flex;
    align-items: center;
  }
  &-header-operate {
    flex: 1;
    text-align: right;
  }
  &-content {
    height: calc(100% - 58px);
    padding-bottom: 20px;
    overflow: hidden;
    background: #fff;
  }
}
</style>
