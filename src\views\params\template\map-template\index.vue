<template>
  <pro-page title="映射模板" :fixed-header="false" class="map">
    <div slot="operation">
      <bs-search
        v-model="searchObj.search"
        placeholder="请输入模板名称"
        style="width: 210px; margin-right: 10px"
        size="small"
        @input="debounceSearch"
      />
      <el-button v-access="'PA.SETTING.MODEL.MAPPING.ADD'" type="primary" size="small" @click="add">
        新建
      </el-button>
    </div>
    <pro-table
      ref="proTable"
      v-loading="tableLoading"
      :columns="columnData"
      :request="request"
      :actions="actions"
      :options="options"
      @action-click="handleActionClick"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { post, del } from '@/apis/utils/net';
import { cloneDeep, debounce } from 'lodash';
@Component({
  name: 'ElementMapTemplate'
})
export default class ElementMapTemplate extends PaBase {
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 }
  };
  columnData = [];
  actions = [
    {
      label: '编辑',
      icon: 'iconfont icon-bianji',
      value: 'edit',
      access: 'PA.SETTING.MODEL.MAPPING.EDIT'
    },
    {
      label: '删除',
      icon: 'iconfont icon-shanchu',
      value: 'del',
      access: 'PA.SETTING.MODEL.MAPPING.DELETE'
    },
    {
      label: '查看历史版本',
      icon: 'iconfont icon-lishi',
      value: 'viewHistory',
      access: 'PA.SETTING.MODEL.MAPPING.MENU'
    }
  ];
  options = {
    actionHandle: ({ actions }) => {
      return actions.filter(({ access }) =>
        this.$store.state.userInfo.authorities.includes(access)
      );
    }
  };
  debounceSearch = debounce(this.search, 500);
  activated() {
    this.$refs.proTable!['loadData']();
  }

  handleActionClick(event, { row }) {
    this[event](row);
  }

  // 新建
  add() {
    this.$router.push({
      path: 'mapTemplateEdit',
      query: { id: '', title: '新建模板' }
    });
  }

  // 编辑
  edit(row) {
    this.$router.push({
      path: 'mapTemplateEdit',
      query: { id: row.id, title: `映射模板：${row.mappingTemplateName || '编辑模板'}` }
    });
  }

  // 删除
  del(row) {
    this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const { error, msg, success } = await del('/rs/pa/mappingGroup/deleteById', { id: row.id });
      if (success) {
        this.$message({ message: '删除模板成功', type: 'success' });
        this.$refs.proTable!['loadData']();
      } else {
        this.$message({ message: msg || error, type: 'error' });
      }
    });
  }

  // 搜索
  search() {
    this.$refs.proTable!['loadDataAndReset']();
  }

  // 查看历史版本
  viewHistory(row) {
    this.$router.push({
      path: 'mapTemplateHistory',
      query: {
        id: row.id,
        title: `历史映射模板：${row.mappingTemplateName || '编辑模板'}`
      }
    });
  }

  async request(page) {
    this.tableLoading = true;
    // 获取模板列表
    const searchObj = cloneDeep(this.searchObj);
    searchObj.search = searchObj.search.trim();
    try {
      const { data, success, msg } = await post('/rs/pa/mappingGroup/list', {
        search: this.searchObj.search,
        pageData: page
      });
      if (success) {
        data.columnData.forEach((el) => {
          if (el.prop) {
            el.value = el.prop;
            if (el.value === 'updateTime') el.valueProps = { type: 'datetime' };
          }
        });
        this.columnData = data.columnData;
        this.tableLoading = false;
        return { data: data.tableData, total: data.pageData.total };
      } else {
        this.$message.error(msg);
      }
      this.tableLoading = false;
    } catch {
      this.tableLoading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.map {
  height: calc(100vh - 107px);
  &-search {
    width: 210px;
    margin-right: 10px;
  }
  &-tooltip {
    cursor: pointer;
  }
  &-icon {
    margin: 0 5px;
  }
  &-header {
    height: 50px;
    background: #ffffff;
    border-left: none;
    padding: 0 20px;
    font-size: 16px;
    display: flex;
    align-items: center;
  }
  &-header-operate {
    flex: 1;
    text-align: right;
  }
  &-content {
    height: calc(100% - 58px);
    padding-bottom: 20px;
    overflow: hidden;
    background: #fff;
  }
}
</style>
