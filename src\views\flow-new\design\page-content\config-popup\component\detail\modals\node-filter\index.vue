<template>
  <bs-dialog
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="title"
    :visible.sync="visible"
    class="filter-dialog"
    size="large"
    append-to-body
    @confirm="handleConfirm"
  >
    <div v-loading="loading" :element-loading-text="$t('pa.flow.comLoading')" class="filter-container">
      <!-- 逻辑关系 -->
      <bar
        :logic-expression.sync="logicExpression"
        :logic-type.sync="logicType"
        :expression-error.sync="expressionError"
        :conditions="conditions"
        :itle="$t('pa.flow.relation1')"
        :disabled="disabled"
        @change="generateExpression"
        @click="generateConfigTemplate"
      />
      <!-- 结果表达式 -->
      <bar :result-expression="resultExpression" show-result :title="$t('pa.flow.resultStr') + '：'" />
      <!-- tab栏 -->
      <action-bar :active-name.sync="activeTabName" :disabled="disabled" @click="generateConfigTemplate" />
      <!-- 内容 -->
      <div class="filter-content">
        <!-- 配置明细 -->
        <logic-config
          v-show="activeTabName === 'configDetails'"
          ref="config"
          :data.sync="conditions"
          :expression-mapping="expressionMapping"
          :method-list="methodList"
          :method-code="methodCode"
          :upstream-field="upstreamField"
          :disabled="disabled"
          @change="handleConfigChange"
          @copy="handleCopyConfig"
          @delete="handleDeleteConfig"
          @getCode="getMethodCode"
        />
        <!-- 关系图 -->

        <logic-relation
          v-if="activeTabName === 'relationChart'"
          :logic-expression="logicExpression"
          :expression-mapping="expressionMapping"
        />
      </div>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, PropSync, Watch, Vue } from 'vue-property-decorator';
import { get } from '@/apis/utils/net';
import Bar from './bar.vue';
import ActionBar from './action-bar.vue';
import LogicConfig from './logic-config.vue';
import LogicRelation from './logic-relation.vue';
import { cloneDeep, isEmpty } from 'lodash';
import { handleFuncArgs, handleTableData, handleTableHead, isArray, isObject, isString, isValidType } from './utils';
import { URL_GET_FUNC_LIST, URL_GET_FUNC_BODY } from '@/apis/commonApi';

@Component({
  components: {
    Bar,
    ActionBar,
    LogicConfig,
    LogicRelation
  }
})
export default class NodeFilter extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: () => ({}) }) data!: any;
  @Prop() jobData!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: false }) disabled!: boolean;
  @PropSync('sourceCode', { default: () => ({}) }) methodCode!: any;
  private loading = false;
  private funcMapping: any = {}; // 方法类型映射
  private activeTabName = 'configDetails';
  private methodList: any[] = []; // 方法列表
  private logicType = '&&'; // 逻辑关系
  private logicExpression = ''; // 逻辑表达式
  private resultExpression = ''; // 逻辑结构表达式
  private upstreamField: any[] = []; //上游字段数据
  private conditions: any = []; // 条件配置详情
  private expressionMapping: any = {}; // 模块对应表达式映射
  private expressionError = '';

  get title() {
    const { nodeName = '', componentName = '' } = this.data || {};
    return this.$t('pa.flow.msg288', [nodeName, componentName]);
  }

  @Watch('conditions', { immediate: true, deep: true })
  generateExpression() {
    const { conditions, logicType, funcMapping } = this;
    /* 逻辑表达式 */
    if (logicType !== 'CUSTOM') {
      this.logicExpression = conditions
        .map(({ name, funcName }) => (funcName ? name : null))
        .filter(Boolean)
        .join(` ${logicType} `);
    }
    let str = this.logicExpression.replace(/([A-Z])/g, '@@_$1_@@');
    /* 模块对应表达式 */
    this.expressionMapping = conditions.reduce((pre, next) => {
      const { name, funcName, funcType, funcArgs } = next;
      const params = funcArgs
        .map(({ key, value }) => {
          if (funcType === 'DEFAULT' && !isObject(funcMapping[funcName]).isOneParams) {
            return `${key},${value}`;
          }
          return key;
        })
        .filter(Boolean);
      if (funcName) {
        pre[name] = `${funcName}(<span class="code-green">${params.join(',')}</span>)`;
        str = str.replace(new RegExp(`@@_${name}_@@`, 'g'), pre[name]);
      }
      return pre;
    }, {});
    /* 结果表达式 */
    this.resultExpression = str.replace(/(\&\&|\|\|)/g, '<span class="code-red"> $1 </span>').replace(/[\@\_]/g, '');
  }

  async created() {
    try {
      this.loading = true;
      await this.getMethodList();
      this.upstreamField = isArray(this.data.inputFields);
      const { expr, logicType, conditions } = isObject(this.data.properties);
      this.logicExpression = isString(expr);
      this.logicType = isString(logicType, '&&');
      this.conditions = this.handleConditions(isArray(conditions));
      this.loading = false;
    } catch (e) {
      this.loading = false;
      console.log(e);
    }
  }

  async getMethodList() {
    try {
      const { success, data, error } = await get(URL_GET_FUNC_LIST, { orgId: this.orgId });
      if (success) {
        const [mapping, ...list]: any[] = this.handleMethodList(isArray(data), {
          SHARE: [1, this.$t('pa.flow.publicMethod')],
          DEFAULT: [2, this.$t('pa.flow.defaultMethod')]
        });
        this.funcMapping = mapping;
        this.methodList = list;
        return;
      }
      this.$tip.error(error);
    } catch (e) {
      console.log(e);
    }
  }

  handleMethodList(list: any[], mapping: any = {}) {
    return list
      .reduce((pre: any, { funcType, funcName, funcArgs, paramsType }) => {
        if (funcType in mapping) {
          const [index, label] = mapping[funcType];
          if (!pre[index]) pre[index] = { label, children: [] };
          pre[index].children.push({
            label: funcName,
            value: funcName,
            type: funcType,
            code: this.methodCode[funcName]
          });
        }
        if (!pre[0]) pre[0] = {};
        pre[0][funcName] = {
          funcType,
          funcName,
          funcArgs,
          paramsType,
          isOneParams: funcArgs.length < 1
        };
        return pre;
      }, [])
      .filter(Boolean);
  }

  async getMethodCode(params: any = {}) {
    try {
      const { success, data, error } = await get(URL_GET_FUNC_BODY, params);
      if (success) {
        this.$set(this.methodCode, params.name, data);
        return;
      }
      this.$tip.error(error);
    } catch (e) {
      console.log(e);
    }
  }

  handleConditions(list) {
    if (list.length < 1) {
      return [this.generateConfigTemplate(false)];
    }
    return list.map((el) => this.handleConditionsItem(el));
  }

  generateConfigTemplate(needPush = true, config: any = {}) {
    const { length } = this.conditions;
    if (length > 25) {
      this.$tip.warning(this.$t('pa.flow.msg182'));
      return;
    }
    const name = String.fromCharCode(length + 65);
    const base = isEmpty(config)
      ? {
          funcName: '',
          funcType: '',
          funcArgs: [],
          tableHead: [],
          tableData: []
        }
      : config;
    return needPush ? this.conditions.push({ ...base, name }) : { ...base, name };
  }

  handleConditionsItem(data, isChange = false) {
    const { funcType, funcArgs, paramsType } = isObject(this.funcMapping[data.funcName]);
    const result: any = {
      name: data.name,
      funcName: data.funcName,
      funcType: isValidType(funcType) ? funcType : data.funcType
    };
    result.funcArgs = handleFuncArgs(funcType, funcArgs, data.funcArgs, isChange);
    result.tableHead = handleTableHead(funcType, data.funcType, funcArgs, result.funcArgs);
    result.tableData = handleTableData(
      isValidType(funcType) ? funcType : data.funcType,
      funcArgs,
      result.funcArgs,
      paramsType
    );
    return result;
  }

  handleConfigChange(index) {
    this.$set(this.conditions, index, this.handleConditionsItem(this.conditions[index], true));
  }

  handleCopyConfig(index: number) {
    this.generateConfigTemplate(true, cloneDeep(this.conditions[index]));
  }

  async handleDeleteConfig(index: number) {
    const { name } = this.conditions[index];
    await this.$confirm(this.$t('pa.flow.msg183', [name]), this.$t('pa.flow.tip'), { type: 'warning' });
    this.conditions.splice(index, 1);
    this.conditions.forEach((el, order) => {
      this.$set(this.conditions[order], 'name', String.fromCharCode(order + 65));
    });
  }

  async handleConfirm() {
    try {
      if (this.activeTabName === 'relationChart') {
        this.activeTabName = 'configDetails';
        await this.handleConfirm();
        return;
      }
      if (this.expressionError) {
        this.$tip.error(this.$t('pa.flow.msg184'));
        return;
      }
      const legalData = this.conditions.filter(({ funcArgs, funcName, funcType, name }) => {
        return funcArgs && funcName && funcType && name;
      });
      if (legalData.length < 1) {
        this.$tip.error(this.$t('pa.tip.configureAtOne'));
        return;
      }
      await (this.$refs.config as any).validate();
      /* 自定义表达式时，条件校验 */
      if (this.logicType === 'CUSTOM') {
        const result = this.conditions
          .map(({ name }: any) => (this.logicExpression.includes(name) ? null : this.$t('pa.flow.msg185', [name])))
          .filter(Boolean);
        if (result.length > 0) {
          this.$tip.error(this.$t('pa.flow.msg186', [result.join('、')]));
          return;
        }
      }
      this.closeDialog(true);
    } catch (e) {
      const error = Object.keys(e).join('_');
      if (error.includes('funcName') || error.includes('funcArgs')) {
        this.$tip.error(this.$t('pa.flow.msg187'));
      }
      console.log(e);
    }
  }

  @Emit('close')
  private closeDialog(needUpdate = false) {
    const jobNode = cloneDeep(this.data);
    const result: any[] = [];
    for (const { name, type } of this.upstreamField) {
      if (name && type) {
        result.push({ name, type, outputable: true, targetable: true });
      }
    }
    jobNode.outputFields = result;
    jobNode.properties = {
      expr: this.logicExpression,
      logicType: this.logicType,
      conditions: this.conditions.map(({ funcArgs, funcName, funcType, name }: any) => ({
        funcArgs,
        funcName,
        funcType,
        name
      }))
    };
    return { needUpdate, jobNode };
  }
}
</script>
<style lang="scss" scoped>
$marginTop: 15px;
$padding: 20px;
$tabHeight: 48px;
$borderColor: #f1f1f1;

.filter {
  &-dialog {
    ::v-deep .el-dialog {
      &__body {
        padding: 0;
      }
    }
  }

  &-container {
    margin-top: 20px;
  }

  &-tab {
    position: relative;
    height: $tabHeight;

    ::v-deep .el-tabs {
      border-top: 1px solid $borderColor;

      &__nav-wrap {
        padding-left: $padding;
      }

      &__item {
        padding: 0 $marginTop !important;
        height: $tabHeight;
        line-height: $tabHeight;
      }
    }

    &__button {
      position: absolute;
      top: 50%;
      right: $padding;
      z-index: 15;
      transform: translateY(-50%);
    }
  }

  &-content {
    margin-top: $marginTop;
    height: calc(70vh - 288px);
    overflow-x: hidden;
    overflow-y: auto;
  }
}
</style>
