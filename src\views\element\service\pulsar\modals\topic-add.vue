<template>
  <bs-dialog title="创建" :visible.sync="visible" :before-close="closeDialog">
    <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="Tenant" prop="tenant" style="width: 100%">
        <bs-select v-model="formData.tenant" style="width: 100%" :options="tenantList" />
      </el-form-item>
      <el-form-item label="Namespace" prop="namespace">
        <bs-select
          v-model="formData.namespace"
          style="width: 100%"
          :options="namespaceList"
          @focus="getNamespaceList"
        />
      </el-form-item>
      <el-form-item label="Topic name" prop="topic">
        <el-input v-model="formData.topic" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="Persistent" prop="persistent">
        <el-radio-group v-model="formData.persistent">
          <el-radio label="persistent">Persistent</el-radio>
          <el-radio label="non-persisitent">Non-persisitent</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="Partitions" prop="partitions">
        <el-input-number
          v-model="formData.partitions"
          :min="0"
          :max="2147483647"
          :precision="0"
          controls-position="right"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog()">关 闭</el-button>
      <el-button type="primary" @click="submit('ruleForm')">确 定</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { ElForm } from 'bs-ui-pro/types/form';
import { Component, Prop, Emit, Vue, Ref } from 'vue-property-decorator';
import { createTopic } from '@/apis/serviceApi';
@Component
export default class TopicAdd extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: '' }) resId!: string;
  @Prop() tenantList!: any;
  @Prop() namespaceList!: any;
  @Ref('ruleForm') readonly ruleForm!: ElForm;

  formData: any = {
    tenant: '',
    namespace: '',
    topic: '',
    persistent: '',
    partitions: ''
  };

  rules: any = {
    tenant: [{ required: true, message: '请选择Tenant', trigger: 'change' }],
    namespace: [{ required: true, message: '请选择Namespace', trigger: 'change' }],
    topic: [{ required: true, message: '请填写Topic name', trigger: 'blur' }],
    persistent: [{ required: true, message: '请选择Persisitent', trigger: 'change' }],
    partitions: [{ required: true, message: '请填写Partition', trigger: 'blur' }]
  };

  @Emit('close')
  closeDialog() {
    this.ruleForm.resetFields();
    this.$emit('update:visible', false);
  }

  submit() {
    this.ruleForm.validate(async (valid: any) => {
      if (valid) {
        const { success, msg } = await createTopic(this.formData, this.resId);
        if (success) {
          this.$message.success(msg);
          return;
        }
        this.$message.error(msg);
      } else {
        this.$message.error('请检查输入内容');
        return false;
      }
    });
  }

  getNamespaceList() {
    (this.$parent as any).getNamespaceList(this.formData.tenant);
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-select {
  width: 100%;
}
</style>
