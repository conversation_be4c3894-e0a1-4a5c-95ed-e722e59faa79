<!--
 * @Description: 右键菜单
 * @Author: ranran
 * @Date: 2020-02-06 18:26:00
 * @LastEditTime: 2020-04-28 10:56:38
 * @LastEditors: ranran
 -->
<template>
  <span
    v-if="showContextMenu"
    class="context-menu-container"
    :style="{
      left: contextMenu.position[0] + 'px',
      top: contextMenu.position[1] + 'px'
    }"
  >
    <span v-for="item in contextMenu.list" :key="item.name">
      <span class="context-menu-item" @click="cilckTheItem(item)">{{ item.name }}</span>
    </span>
  </span>
</template>
<script lang="ts">
import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import $ from 'jquery';

@Component
export default class ContextMenu extends Vue {
  @Prop() contextMenu!: any;
  showContextMenu = false;
  private position: any = [0, 0];

  @Emit('cilckTheItem')
  cilckTheItem(item) {
    return item;
  }

  mounted() {
    this.$nextTick(() => {
      // 点击空白处关闭右键菜单
      $(document).bind('click', '#app', () => {
        this.showContextMenu = false;
      });
    });
  }
}
</script>
<style lang="scss" scoped>
.context-menu-container {
  position: absolute;
  z-index: 1100;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  width: 140px;
  border: 1px solid #d4dce2;
  box-shadow: 0px 0px 8px #bbbaba;
}
.context-menu-item {
  font-size: 12px;
  height: 30px;
  line-height: 30px;
  color: #333;
  padding: 0 14px;
  display: block;
}
.context-menu-item:hover {
  background-color: rgba(239, 239, 239, 1);
  cursor: pointer;
  color: #333;
}
.item-disabled {
  cursor: not-allowed !important;
  color: #aaa !important;
}
.dividing-line {
  display: block;
  border: 0.5px solid #e5e5e5;
  margin: 0 8px;
}
</style>
