<template>
  <table-block
    :title="$t('pa.consume')"
    :loading="loading"
    :table-data="tableData"
    :column-data="columnData"
    :height="height"
  >
    <div slot="operation">
      <!-- topic -->
      <bs-select
        v-model="topic"
        clearable
        class="inline-input"
        :placeholder="$t('pa.placeholder.topicName')"
        :options="allTopics"
        virtual-loading
        filterable
        @change="handleTopicSelect"
      />
      <!-- group -->
      <el-autocomplete
        v-model="group"
        clearable
        class="inline-input inline-input--group"
        :placeholder="$t('pa.placeholder.consumer')"
        :fetch-suggestions="handleSearch(allGroups)"
      />
      <el-button size="small" type="primary" @click="handleQuery">{{ $t('pa.action.search') }}</el-button>
    </div>
    <div slot="footer" class="group-footer">
      <span>{{ $t('pa.lagTotal') }}</span>
      <span>：{{ lagTotal }}</span>
    </div>
  </table-block>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getKafkaTopic, getKafkaGroup, getKafkaDetail } from '@/apis/serviceApi';
import { safeArray, includesPro } from '@/utils';

@Component({
  components: { TableBlock: () => import('../components/table-block.vue') }
})
export default class ConsumerManager extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: () => ({}) }) params!: any;

  loading = false;
  id = '';
  columnData: any[] = [
    { width: '200', value: 'topic', label: this.$t('pa.name') },
    { value: 'host', label: this.$t('pa.clusterAddress') },
    { value: 'group', label: 'group' },
    { value: 'partition', label: 'partition', sortable: true },
    { value: 'offset', label: 'offset', sortable: true },
    { value: 'logEndSize', label: 'logEndSize', sortable: true },
    { value: 'lag', label: 'lag', sortable: true }
  ];
  tableData: any[] = [];
  topic = '';
  group = '';
  allTopics: any[] = [];
  allGroups: any[] = [];

  get height() {
    return this.tableData.length ? this.params?.height || '300px' : '180px';
  }
  get lagTotal() {
    return this.tableData.reduce((pre, { lag }) => {
      pre += parseInt(lag, 10);
      return pre;
    }, 0);
  }

  created() {
    this.id = this.$route.query.id as string;
    this.getAllTopic();
  }

  async getAllTopic() {
    try {
      this.loading = true;
      const { success, data, error } = await getKafkaTopic(this.id);
      if (!success) return this.$message.error(error);
      this.allTopics = safeArray(data).map((value) => ({ value, label: value }));
    } finally {
      this.loading = false;
    }
  }
  handleTopicSelect(value) {
    this.getAllGroup(value);
  }
  async getAllGroup(topic: string) {
    try {
      this.loading = true;
      const { success, data, error } = await getKafkaGroup(this.id, topic);
      if (!success) return this.$message.error(error);
      this.allGroups = safeArray(data).map((value) => ({ value }));
    } finally {
      this.loading = false;
    }
  }
  /* 搜索事件 */
  handleSearch(data) {
    return (queryStr, cb) => {
      const result = queryStr ? data.filter(({ value }) => includesPro(value, queryStr)) : data;
      cb(result);
    };
  }
  async handleQuery() {
    try {
      if (!this.topic) return this.$message.error(this.$t('pa.placeholder.topicName'));
      if (!this.group) return this.$message.error(this.$t('pa.placeholder.consumerName'));
      this.loading = true;
      const { success, data, error } = await getKafkaDetail({
        brokers: this.data.url,
        group: this.group,
        topic: this.topic,
        id: this.id
      });
      if (!success) return this.$message.error(error);
      this.tableData = safeArray(data);
    } finally {
      this.loading = false;
    }
  }
}
</script>
<style lang="scss" scoped>
.inline-input {
  width: 200px;
  &--group {
    margin: 0 10px;
  }
}
.group-footer {
  padding-right: 8px;
  width: 100%;
  text-align: right;
  line-height: 32px;
  font-weight: bold;
  box-sizing: border-box;
}
</style>
