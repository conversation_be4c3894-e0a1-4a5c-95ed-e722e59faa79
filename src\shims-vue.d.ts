import Vue from 'vue';
import type { ElNotificationOptions } from 'bs-ui-pro/types/notification';

declare module '*.vue' {
  export default Vue;
  declare module '*.vue';
}
declare module '*.png';
declare module 'vue/types/vue' {
  interface Tip {
    (config: any): (config: any) => void;
    success: (msg: any, title?: string = '') => void;
    warning: (msg: any, title?: string = '') => void;
    info: (msg: any, title?: string = '') => void;
    error: (msg: any, title?: string = '') => void;
    errorPro: (title?: string = '', detail: any = {}) => void;
  }
  interface Vue {
    $tip: Tip;
    $saveConfirm: any;
  }
}
