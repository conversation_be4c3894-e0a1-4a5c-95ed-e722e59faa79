<template>
  <div class="warning">
    <div class="bs-detail-block warning">
      <div class="bs-detail__header">
        <div class="bs-detail__header-title">待处理预警数</div>
        <el-button icon="el-icon-refresh" @click="debounceRefresh">刷新</el-button>
      </div>
      <data-handler ref="dataHandler" :request="request" @get-data="getData">
        <template v-slot:content>
          <div
            class="bs-detail__content warning-content"
            :class="{
              'sm-screen': isSmScreenAndNotCollapse,
              'sm-screen__collapse': isSmScreenAndCollapse
            }"
          >
            <div
              v-for="item in warningContentList"
              :key="item.title"
              class="warning-content__item"
              :class="{
                'warning-content__sm-item': isSmScreenAndCollapse,
                'warning-content__separator': item.isSeparator,
                'warning-content__item--service': item.title === '服务'
              }"
              @click="goToWarningRule(item.title)"
            >
              <img
                v-if="item.imageSrc"
                :src="item.imageSrc"
                :alt="item.title"
                :class="{
                  'warning-content__img': !$store.state.others.isCollapse
                }"
              />
              <div v-if="item.imageSrc" class="warning-content__count">
                <span class="warning-content__item--count">{{ item.count }}</span>
                <span class="warning-content__item--title">{{ item.title }}</span>
              </div>
            </div>
          </div>
        </template>
      </data-handler>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { getWarningData } from '@/apis/homeApi';
import { debounce } from 'lodash';
import DataHandler from './data-handler.vue';
import { AxiosPromise } from 'axios';
@Component({
  components: {
    DataHandler
  }
})
export default class Warning extends Vue {
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: 0 }) screenWidth!: number;

  private warningContentList = [
    {
      count: 0,
      title: '服务',
      imageSrc: require('@/assets/flow.png')
    },
    {
      isSeparator: true,
      title: 'separator'
    },
    {
      count: 0,
      title: '流程',
      imageSrc: require('@/assets/service.png')
    }
  ];
  private debounceRefresh = debounce(this.refresh, 1000);
  private request: AxiosPromise[] = [];
  private isResize = false;
  // 小屏非折叠
  get isSmScreenAndNotCollapse() {
    return !this.$store.state.others.isCollapse && this.screenWidth < 1600;
  }
  // 小屏折叠
  get isSmScreenAndCollapse() {
    return this.$store.state.others.isCollapse && this.screenWidth < 1600;
  }
  @Watch('orgId')
  handleOrgIdChange(ids) {
    this.request = [getWarningData(ids)];
  }
  @Watch('$store.state.others.isResize')
  handleResizeChange(val) {
    this.isResize = val;
  }

  refresh() {
    this.request = [getWarningData(this.orgId), true];
  }
  getData({ data }) {
    if (data) {
      this.warningContentList.forEach((el) => {
        el.count = el.title === '服务' ? data.warnForServer : data.warnForFlow;
      });
    }
  }

  goToWarningRule(title) {
    this.$router.push({
      path: 'monitor/warningRule',
      query: {
        type: title === '服务' ? 'service' : 'flow'
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.warning {
  .sm-screen {
    justify-content: space-around;
    margin-left: 30px;
    &__collapse {
      margin-left: 20px;
    }
  }
  &-content {
    width: 100%;
    height: 85%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
    &__img {
      @media screen and (max-width: 1600px) {
        width: 55px;
      }
    }
    &__count {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      margin-left: 21px;
    }
    &__separator {
      width: 1px;
      height: 64px;
      border: 1px solid #f1f1f1;
    }
    &__item {
      display: flex;
      flex-shrink: 0;
      cursor: pointer;
      @media screen and (max-width: 1600px) {
        margin-right: 10px;
      }
      &--service {
        display: flex;
        @media screen and (max-width: 1600px) {
          margin-left: -40px;
        }
      }
      &--title {
        font-weight: 400;
        color: #777777;
      }
      &--count {
        font-size: 1.67vw;
        font-weight: bold;
        color: #444444;
        line-height: 37px;
      }
    }
  }
}
</style>
