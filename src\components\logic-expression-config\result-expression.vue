<template>
  <!-- 结果表达式 -->
  <div :key="key" class="config-expression">
    <span :class="isEn ? 'config-expression__us' : ''">{{ $t('pa.flow.resultStr') }}<span>：</span></span>
    <el-tooltip v-hide effect="light" placement="top">
      <div slot="content" v-html="expression"></div>
      <div class="config-expression__main" v-html="expression"></div>
    </el-tooltip>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { sha256 } from './utils';

@Component
export default class ResultExpression extends Vue {
  @Prop({ default: '' }) expression!: string;

  get key() {
    return sha256(this.expression);
  }
}
</script>
<style lang="scss" scoped>
.config {
  &-expression {
    display: flex;
    align-items: center;
    margin: 0 auto 20px;
    width: calc(100% - 70px);
    font-size: 14px;
    font-weight: 400;
    color: #444444;
    &__us {
      display: inline-block;
      width: 150px;
    }
    &__main {
      width: calc(100% - 90px);
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
</style>
