<template>
  <el-form-item
    :prop="`groupCepPatternConditionList[${scopeIndex}][${data.prop}]`"
    :rules="handlerRules(data)"
  >
    <el-select
      v-model="formData.groupCepPatternConditionList[scopeIndex][data.prop]"
      clearable
      filterable
      size="small"
      style="width: 100%"
      :placeholder="`请选择${data.label}`"
      :disabled="disabled"
      @change="$emit('change', scopeIndex)"
    >
      <el-option
        v-for="item in handleConnectionOptions(data.options, scopeIndex)"
        :key="item.value"
        :label="item.label"
        :value="item.value"
        :disabled="item.disabled"
      />
    </el-select>
  </el-form-item>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class ConnectionType extends Vue {
  @Prop({ type: Number }) index!: number;
  @Prop({ type: Number }) scopeIndex!: number;
  @Prop({ type: Object, default: () => ({}) }) data!: any;
  @Prop({ type: Object, default: () => ({}) }) formData!: any;
  get disabled() {
    return this.scopeIndex === this.length - 1;
  }
  get length() {
    return this.formData.groupCepPatternConditionList.length;
  }
  handlerRules({ required = false, label }: any) {
    return {
      required,
      message: `请选择${label}`,
      trigger: 'change'
    };
  }
  /* 处理连接方式下拉框数据 */
  handleConnectionOptions(options: any[], index: number) {
    options[4].disabled = index === this.length - 2;
    options[5].disabled = !this.disabled;
    return options;
  }
}
</script>
