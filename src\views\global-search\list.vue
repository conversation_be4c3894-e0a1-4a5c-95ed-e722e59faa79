<template>
  <div class="list-content">
    <div v-for="item in data" :key="item.id" class="list-row">
      <div>
        <div class="type">{{ types[item.dataType] }}</div>
        <span class="name" @click="toItem(item)" v-html="item.longName || ''"></span>
        <span v-if="item.jobStatus" :class="`state state--${item.jobStatus}`">
          {{ jobStatus[item.jobStatus] }}
        </span>
      </div>
      <div class="from">
        <span v-if="item.fromProjectName">{{ $t('pa.form', [item.fromProjectName]) }}</span>
      </div>
      <div>
        <span class="label">{{ $t('pa.location') }}</span>
        <span class="location"> {{ item.location }}</span> <span class="label">{{ $t('pa.creator') }}</span>
        <span class="createdBy" :class="{ 'createdBy--us': isEn }"> {{ item.createdBy }}</span>
        <span class="label">{{ $t('pa.flow.updateTime') }}</span>
        <span> {{ timeFormat(item.updateTime) }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { getFlowList } from '@/apis/flowNewApi';
import toLower from 'lodash';
import { dayjs, hasServiceDetailAccess } from '@/utils';
@Component
export default class List extends Vue {
  @Prop() data!: object | any;
  types = {
    PROJECT: this.$t('pa.flow.project'),
    JOB: this.$t('pa.process'),
    ASSETS: this.$t('pa.thirdPartyLibs'),
    FUNCTION: this.$t('pa.method'),
    DATA_DEFINITION: this.$t('pa.menu.dataDefine'),
    TABLE: this.$t('pa.table'),
    UDF: 'UDF',
    SERVICE: this.$t('pa.service'),
    DIRECTORY: this.$t('pa.data.text31'),
    CATALOG: 'CATALOG'
  };
  jobStatus = {
    DEV: this.$t('pa.status.develop'),
    PROD: this.$t('pa.status.online'),
    PUB: this.$t('pa.flow.pub'),
    OTHER: this.$t('pa.flow.other')
  };

  // 时间格式化
  timeFormat(time: number) {
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
  }

  toItem(item: any) {
    const options = {
      ASSETS: { path: '/assets/assets/jar' },
      FUNCTION: { path: `/assets/assets/function/detail?funcId=${item.funcId}` },
      DATA_DEFINITION: { path: `/assets/assets/dd/detail?ddid=${item.ddid}` },
      TABLE: { path: '/data/sheetDetail', query: { id: item.id, status: '1', title: item.name } },
      SERVICE: {
        path: `/element/service/${toLower(item.resType)}/detail`,
        query: { id: item.id, resType: item.resType, title: item.name, clusterType: '' }
      },
      UDF: {
        path: '/data/udfEdit',
        query: { id: item.id, status: '1', title: `UDF：${item.name}` }
      },
      CATALOG: {
        path: 'data/catalogDetail',
        query: { title: this.$t('pa.cataLog.test6'), id: item.id }
      }
    };
    if (item.dataType === 'SERVICE' && !hasServiceDetailAccess()) return;
    switch (item.dataType) {
      case 'PROJECT':
        this.toProject(item);
        break;
      case 'JOB':
        this.toFlowCanvas(item);
        break;
      case 'DIRECTORY':
        this.toDir(item);
        break;
      default:
        this.$router.push(options[item.dataType]);
    }
  }

  //跳转项目
  async toProject({ id, name }) {
    const { success, data } = await getFlowList({
      id,
      name: '',
      jobStatus: 'ALL',
      jobType: 'ALL',
      mode: 'ALL',
      clusterType: 'ALL',
      jobRunTimeStatus: 'ALL'
    });
    if (success) {
      const flowId = data.children[0]?.nodeId || '';
      const title = data.children[0]?.nodeName || name;
      const nodeType = data.children[0]?.nodeType;
      if (
        (this as any).$tabNav.getAllTabs().find((item) => item.title === title && item.value.split('flowId=')[1] === flowId)
      ) {
        const value = (this as any).$tabNav
          .getAllTabs()
          .find((item) => item.title === title && item.value.split('flowId=')[1] === flowId).value;
        localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId }));
        this.$router.push({
          path: value
        });
      } else {
        this.$router.push({
          path: '/flow',
          query: {
            id,
            name,
            title,
            state: 'ALL',
            flowId: nodeType === 'JOB' ? flowId : ''
          }
        });
      }
    }
  }

  //跳转流程
  toFlowCanvas({ id, name, projectRootId, fromProjectName }) {
    if ((this as any).$tabNav.getAllTabs().find((item) => item.title === name && item.value.split('flowId=')[1] === id)) {
      const value = (this as any).$tabNav
        .getAllTabs()
        .find((item) => item.title === name && item.value.split('flowId=')[1] === id).value;
      localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId: id }));
      this.$router.push({
        path: value
      });
    } else {
      this.$router.push({
        path: 'flow',
        query: {
          id: projectRootId,
          name: fromProjectName,
          title: name,
          state: 'ALL',
          flowId: id
        }
      });
    }
  }

  // 跳转到目录
  toDir({ id, projectRootId, fromProjectName, name }) {
    this.$router.push({
      path: 'flow',
      query: {
        id: projectRootId,
        name: fromProjectName,
        title: name,
        state: 'ALL',
        dirId: id
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.list-row {
  border-bottom: 1px solid #e4e7ed;
  padding: 26px 32px 30px 32px;
  .type {
    display: inline-block;
    padding: 0 3px;
    margin: 0 8px 18px 0;
    font-size: 12px;
    border: 1px solid #057eff;
    color: $--bs-color-primary;
    border-radius: 4px;
  }
  .name {
    font-size: 16px;
    margin-right: 8px;
    cursor: pointer;
    font-weight: 500;
  }
  .state {
    display: inline-block;
    text-align: center;
    box-sizing: border-box;
    width: max-content;
    padding: 1px 8px;
    font-size: 12px;
    border-radius: 4px;
    &--DEV {
      background-color: #f4f0fb;
      border-color: #e8e1f7;
      color: #8c6bd6;
    }

    &--PROD {
      background-color: #eefaee;
      border-color: #ddf4de;
      color: #54c958;
    }

    &--PUB {
      background-color: #ebf2ff;
      color: #377cff;
      border: 1px solid #d7e5ff;
    }

    &--OTHER {
      background: #f1f1f1;
      color: #908f94;
    }
  }
  .from {
    margin-bottom: 16px;
  }
  .label {
    margin-right: 12px;
  }
  .location {
    margin-right: 255px;
  }
  .createdBy {
    margin-right: 226px;
    &--us {
      margin-right: 150px;
    }
  }
}
</style>
