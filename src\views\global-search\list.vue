<template>
  <div class="list-content">
    <div v-for="item in data" :key="item.id" class="list-row">
      <div>
        <div class="type">{{ types[item.dataType] }}</div>
        <span class="name" @click="toItem(item)" v-html="item.longName || ''"></span>
        <span v-if="item.jobStatus" :class="`state state--${item.jobStatus}`">
          {{ jobStatus[item.jobStatus] }}
        </span>
      </div>
      <div class="from">
        <span v-if="item.fromProjectName">来自：{{ item.fromProjectName }}</span>
      </div>
      <div>
        <span class="label">所属位置</span><span class="location"> {{ item.location }}</span>
        <span class="label">创建人</span><span class="createdBy"> {{ item.createdBy }}</span>
        <span class="label">更新时间</span><span> {{ timeFormat(item.updateTime) }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { getFlowList } from '@/apis/flowNewApi';
import toLower from 'lodash';
import dayjs from 'dayjs';
@Component
export default class List extends Vue {
  @Prop() data!: object | any;
  types = {
    PROJECT: '项',
    JOB: '流',
    ASSETS: '类',
    FUNCTION: '方法',
    TABLE: '表',
    VIEW: '视图',
    UDF: 'udf',
    SERVICE: '服务'
  };
  jobStatus = {
    DEV: '开发',
    PROD: '已上线',
    PUB: '已发布',
    OTHER: '其他'
  };

  // 时间格式化
  timeFormat(time: number) {
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
  }

  toItem(item: any) {
    const options = {
      ASSETS: { path: '/element/assets/jar', query: { keywords: item.name } },
      FUNCTION: { path: '/element/assets/function', query: { keywords: item.name } },
      TABLE: { path: '/data/sheetDetail', query: { id: item.id, status: '1', title: item.name } },
      VIEW: { path: '/data/viewDetail', query: { id: item.id, status: '2', title: item.name } },
      SERVICE: {
        path: `/element/clusters/${toLower(item.resType)}/detail`,
        query: { id: item.id, resType: item.resType, title: item.name }
      },
      UDF: {
        path: '/data/udfEdit',
        query: { id: item.id, status: '1', title: `UDF：${item.name}` }
      }
    };
    switch (item.dataType) {
      case 'PROJECT':
        this.toProject(item);
        break;
      case 'JOB':
        this.toFlowCanvas(item);
        break;
      default:
        this.$router.push(options[item.dataType]);
    }
  }

  //跳转项目
  async toProject({ id, name }) {
    const { success, data } = await getFlowList({
      id,
      name: '',
      jobStatus: 'ALL',
      jobType: 'ALL',
      mode: 'ALL'
    });
    if (success) {
      const flowId = data.children[0]?.id || '';
      const title = data.children[0]?.jobName || name;
      if (
        (this as any).$tabsNav
          .getAllTabs()
          .find((item) => item.title === title && item.value.split('flowId=')[1] === flowId)
      ) {
        const value = (this as any).$tabsNav
          .getAllTabs()
          .find((item) => item.title === title && item.value.split('flowId=')[1] === flowId).value;
        localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId }));
        this.$router.push({
          path: value
        });
      } else {
        this.$router.push({
          path: '/flow',
          query: {
            id,
            name,
            title,
            state: 'ALL',
            flowId
          }
        });
      }
    }
  }

  //跳转流程
  toFlowCanvas({ id, name, fromProjectId, fromProjectName }) {
    if (
      (this as any).$tabsNav
        .getAllTabs()
        .find((item) => item.title === name && item.value.split('flowId=')[1] === id)
    ) {
      const value = (this as any).$tabsNav
        .getAllTabs()
        .find((item) => item.title === name && item.value.split('flowId=')[1] === id).value;
      localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId: id }));
      this.$router.push({
        path: value
      });
    } else {
      this.$router.push({
        path: 'flow',
        query: {
          id: fromProjectId,
          name: fromProjectName,
          title: name,
          state: 'ALL',
          flowId: id
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.list-row {
  border-bottom: 1px solid #e4e7ed;
  padding: 26px 0 30px;
  .type {
    display: inline-block;
    padding: 0 3px;
    margin: 0 8px 18px 0;
    font-size: 12px;
    border: 1px solid #057eff;
    color: $--bs-color-primary;
    border-radius: 4px;
  }
  .name {
    font-size: 16px;
    margin-right: 8px;
    cursor: pointer;
    font-weight: 500;
  }
  .state {
    display: inline-block;
    text-align: center;
    box-sizing: border-box;
    width: max-content;
    padding: 1px 8px;
    font-size: 12px;
    border-radius: 4px;
    &--DEV {
      background-color: #f4f0fb;
      border-color: #e8e1f7;
      color: #8c6bd6;
    }

    &--PROD {
      background-color: #eefaee;
      border-color: #ddf4de;
      color: #54c958;
    }

    &--PUB {
      background-color: #ebf2ff;
      color: #377cff;
      border: 1px solid #d7e5ff;
    }

    &--OTHER {
      background: #f1f1f1;
      color: #908f94;
    }
  }
  .from {
    margin-bottom: 16px;
  }
  .label {
    margin-right: 12px;
  }
  .location {
    margin-right: 255px;
  }
  .createdBy {
    margin-right: 226px;
  }
}
</style>
