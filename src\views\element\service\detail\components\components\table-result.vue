<template>
  <div class="table-result__container">
    <div class="table-result-title">{{ $t('pa.searchResult') }}</div>
    <div v-if="isString">
      <bs-code
        v-if="data[0]"
        :value="data[0]"
        language="json"
        :read-only="true"
        :extra-style="{ height: '360px' }"
        :formatter="formatter"
      />
      <div v-else class="table-result-noData">{{ $t('pa.noData') }}</div>
    </div>
    <bs-table v-else height="412" border :data="data" :column-data="columnData" :column-settings="false" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class TableResult extends Vue {
  @Prop({ default: '' }) id!: string;
  @Prop({ default: '' }) type!: string;
  @Prop({ default: () => [] }) data!: any[];
  @Prop({ default: false }) isString!: boolean;
  @Prop({ default: false }) formatter!: boolean;
  @Prop({ default: () => [] }) columnData!: any[];
}
</script>
<style lang="scss" scoped>
.table-result {
  &__container {
    height: 452px;
    overflow: hidden;
  }
  &-title {
    margin: 10px 0;
    &:before {
      content: ' ';
      position: relative;
      left: 0;
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 6px;
      background: #ff9c00;
    }
  }
  &-noData {
    height: 411px;
    line-height: 411px;
    text-align: center;
    color: #909399;
    border: 1px solid $--bs-color-border-lighter;
  }
}
</style>
