<template>
  <div style="width: 100%">
    <div v-show="pieData.length > 0" ref="divRef" class="pie-chart-body"></div>
    <div v-if="pieData.length < 1" class="pie-chart-body pie-chart-body--noData">{{ $t('pa.noData') }}</div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator';
import echarts from '@/plugins/use-echarts';
const resizer = require('element-resize-detector')();

@Component
export default class FlinkPieChart extends Vue {
  @Prop({ default: '' }) title!: string;
  @Prop({ default: '' }) name!: string;
  @Prop({ default: () => [] }) pieData!: any[];
  @Prop({ default: () => [] }) legendData!: any[];

  @Ref('divRef') readonly div!: HTMLDivElement;

  chart: any = null;

  @Watch('pieData', { deep: true })
  onPieDataChange() {
    this.renderChart();
  }

  mounted() {
    this.chart = echarts.init(this.div);
    this.renderChart();
    resizer.listenTo(this.div, this.resizeChart);
  }
  beforeDestroy() {
    resizer.removeListener(this.div, this.resizeChart);
  }

  renderChart() {
    if (!this.chart) return;
    const options = {
      title: {
        text: this.title,
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)'
      },
      legend: {
        type: 'scroll',
        orient: 'horizontal',
        left: 'center',
        top: 30,
        data: this.legendData
      },
      series: [
        {
          name: this.name,
          type: 'pie',
          radius: '53%',
          center: ['40%', '50%'],
          data: this.pieData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    this.chart.setOption(options);
  }
  resizeChart() {
    this.chart && this.chart.resize();
  }
}
</script>
<style lang="scss" scoped>
.pie-chart-body {
  width: 100%;
  height: 400px;
  color: #aaa;
  &--noData {
    text-align: center;
    line-height: 400px;
  }
}
</style>
