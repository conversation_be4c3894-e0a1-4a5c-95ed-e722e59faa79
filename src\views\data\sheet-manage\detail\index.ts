import { Message } from 'bs-ui-pro';
// 校验连接器属性 必填项
const validateConnect = (connectList) => {
  const params: any = {};
  let conFlag = true;
  connectList.forEach((item) => {
    if (item.required && item.defaultValue === '') {
      conFlag = false;
    }
    if (item.defaultValue !== '' && item.defaultValue !== 'null') {
      params[item.name] = item.defaultValue;
    }
  });
  if (!conFlag) {
    Message.error('请填写连接器值');
  }
  return { validate: conFlag, params: params };
};

// 校验字段信息
const validateField = (cloneTableData, resType) => {
  const length = cloneTableData.length;
  if (resType === 'REDIS' && (length < 2 || length > 3)) {
    const ls = length > 3 ? '多于3' : length < 2 ? '少于2' : '';
    Message.error(`字段不允许${ls}个`);
    return { validate: false };
  }
  if (resType === 'HBASE') {
    const hasRowKey = cloneTableData.filter((item) => item.primaryKey === '1');
    if (hasRowKey.length && hasRowKey.length !== 1) {
      Message.error(`HBase服务类型：表字段只能设置1个主键`);
      return { validate: false };
    } else if (hasRowKey.length === 0) {
      Message.error(`HBase服务类型：表字段必须要设置1个主键`);
      return { validate: false };
    }
    const noRowKey = cloneTableData.filter((item) => item.primaryKey !== '1' && !item.columnFamily);
    if (noRowKey.length) {
      Message.error(`HBase服务类型：表字段除主键行字段外，其他字段必须设置列簇`);
      return { validate: false };
    }
  }
  const check = cloneTableData.every((item) => {
    item.fieldName = item.fieldName.replace(/\s*/g, '');
    return item.fieldName && item.fieldType;
  });
  if (check) {
    const list = cloneTableData.map((item) => {
      delete item.id;
      return item;
    });
    return { validate: true, param: list };
  } else {
    Message.error('字段信息为必填项，请完善字段信息后重试');
    return { validate: false };
  }
};
// 校验高级连接器属性
const validateAdvancedConnect = (advancedConnect) => {
  const checkArry: any = [];
  const validate = advancedConnect.every((item) => {
    checkArry.push(item.key);
    return item.key !== '' && item.value !== '';
  });
  const keySet = new Set(checkArry);
  if (!validate) {
    Message.error('请填写高级连接器属性字段');
    return { flag: false };
  } else if (keySet.size !== advancedConnect.length) {
    Message.error('高级连接器属性字段属性值不能重复，请重试');
    return { flag: false };
  } else {
    const params: any = [];
    advancedConnect.forEach((element) => {
      const param = {};
      param[element.key] = element.value;
      params.push(param);
    });
    return { flag: true, param: params };
  }
};

// 校验高级表字段
const valideFieldInfo = (advanceChartList) => {
  // 水位线校验
  const line = advanceChartList.find((item) => item.advanceFieldType === 'WATERMARK');
  if (line) {
    if (!line.field) return { flag: false, error: '请选择水位线字段名' };
    if (!line.column1) return { flag: false, error: '请选择水位线数值' };
    if (!line.column2) return { flag: false, error: '请选择水位线单位' };
  }
  // 处理时间校验
  const time = advanceChartList.find((item) => item.advanceFieldType === 'PROCTIME');
  if (time) {
    if (!time.field) return { flag: false, error: '请输入处理时间字段名' };
  }
  // 空值校验
  const validate = advanceChartList.every((item) => {
    return item.field !== '';
  });
  if (!validate) {
    return { flag: false, error: '高级字段不能为空' };
  } else {
    return { flag: true, param: advanceChartList };
  }
};

// 判断字段中是否有重复字段
const getrepeat = (value, family, cloneTableData, resType) => {
  let result = '';
  if (resType !== 'HBASE') {
    result = cloneTableData.filter((item) => item.fieldName !== '' && item.fieldName === value);
  } else {
    result = cloneTableData.filter(
      (item) => item.fieldName !== '' && item.fieldName === value && item.columnFamily === family
    );
  }

  if (result && result.length >= 2) {
    return '存在重复字段，请修改';
  } else {
    return '';
  }
};
// 重复的字段数据
const repeatNum = (cloneTableData) => {
  const name = new Map();
  const res: any = [];
  cloneTableData.forEach((item) => {
    if (item.fieldName === '') {
    } else {
      const ele =
        item.columnFamily !== undefined ? item.fieldName + item.columnFamily : item.fieldName;
      if (name.has(ele)) {
        name.set(ele, name.get(ele) + 1);
      } else {
        name.set(ele, 1);
      }
    }
  });
  for (const value of name.values()) {
    if (value >= 2) {
      res.push(value);
    }
  }
  return res.length;
};
const relationTypeList = [
  {
    label: '流程',
    value: 'JOB'
  },
  {
    label: '视图',
    value: 'VIEW'
  }
];
const tabs = [
  {
    value: '1',
    label: '基本信息'
  },
  {
    value: '2',
    label: '引用关系'
  },
  {
    value: '3',
    label: '历史版本'
  },
  {
    value: '4',
    label: '源码'
  }
];

const isShowFieldColumn = (table, list) => {
  return list.map((item) => {
    if (item.name === 'field-column') {
      return {
        ...item,
        needHide: table.length === 3 ? false : true,
        required: table.length === 3 ? true : false
      };
    }
    return item;
  });
};

export {
  validateConnect,
  validateField,
  validateAdvancedConnect,
  valideFieldInfo,
  getrepeat,
  repeatNum,
  isShowFieldColumn,
  relationTypeList,
  tabs
};
