import { Message } from 'bs-ui-pro';
import i18n from '@/i18n';

// 校验连接器属性 必填项
const validateConnect = (connectList) => {
  const params: any = {};
  let conFlag = true;
  connectList.forEach((item) => {
    if (item.required && item.defaultValue === '') {
      conFlag = false;
    }
    if (item.defaultValue !== '' && item.defaultValue !== 'null') {
      params[item.name] = item.defaultValue;
    }
  });
  if (!conFlag) {
    Message.error(i18n.t('pa.data.table.detail.tips.linkerTip') as string);
  }
  return { validate: conFlag, params: params };
};

// 校验字段信息
const validateField = (cloneTableData, resType) => {
  const length = cloneTableData.length;
  if (resType === 'REDIS' && (length < 2 || length > 3)) {
    const ls =
      length > 3 ? i18n.t('pa.data.table.detail.excess3') : length < 2 ? i18n.t('pa.data.table.detail.lessThan2') : '';
    Message.error(i18n.t('pa.data.table.detail.tips.notAllowedTip', [ls]) as string);
    return { validate: false };
  }
  if (resType === 'HBASE') {
    const hasRowKey = cloneTableData.filter((item) => item.primaryKey === '1');
    if (hasRowKey.length && hasRowKey.length !== 1) {
      Message.error(i18n.t('pa.data.table.detail.tips.hbaseTip1') as string);
      return { validate: false };
    } else if (hasRowKey.length === 0) {
      Message.error(i18n.t('pa.data.table.detail.tips.hbaseTip2') as string);
      return { validate: false };
    }
    const noRowKey = cloneTableData.filter((item) => item.primaryKey !== '1' && !item.columnFamily);
    if (noRowKey.length) {
      Message.error(i18n.t('pa.data.table.detail.tips.hbaseTip3') as string);
      return { validate: false };
    }
  }
  const check = cloneTableData.every((item) => {
    item.fieldName = item.fieldName.replace(/\s*/g, '');
    return item.fieldName && item.fieldType;
  });
  if (check) {
    const list = cloneTableData.map((item) => {
      delete item.id;
      return item;
    });
    return { validate: true, param: list };
  } else {
    Message.error(i18n.t('pa.data.table.detail.tips.hbaseTip4') as string);
    return { validate: false };
  }
};
// 校验高级连接器属性
const validateAdvancedConnect = (advancedConnect) => {
  const checkArry: any = [];
  const validate = advancedConnect.every((item) => {
    checkArry.push(item.key);
    return item.key !== '' && item.value !== '';
  });
  const keySet = new Set(checkArry);
  if (!validate) {
    Message.error(i18n.t('pa.data.table.detail.tips.seniorLinkerTip1') as string);
    return { flag: false };
  } else if (keySet.size !== advancedConnect.length) {
    Message.error(i18n.t('pa.data.table.detail.tips.seniorLinkerTip2') as string);
    return { flag: false };
  } else {
    const params: any = [];
    advancedConnect.forEach((element) => {
      const param = {};
      param[element.key] = element.value;
      params.push(param);
    });
    return { flag: true, param: params };
  }
};

// 校验高级表字段
const valideFieldInfo = (advanceChartList) => {
  // 水位线校验
  const line = advanceChartList.find((item) => item.advanceFieldType === 'WATERMARK');
  if (line) {
    if (!line.field) return { flag: false, error: i18n.t('pa.data.table.placeholder.inputPlaceholder3') };
    if (!line.column1) return { flag: false, error: i18n.t('pa.data.table.placeholder.inputPlaceholder4') };
    if (!line.column2) return { flag: false, error: i18n.t('pa.data.table.placeholder.inputPlaceholder5') };
  }
  // 处理时间校验
  const time = advanceChartList.find((item) => item.advanceFieldType === 'PROCTIME');
  if (time) {
    if (!time.field) return { flag: false, error: i18n.t('pa.data.table.placeholder.inputPlaceholder6') };
  }
  // 空值校验
  const validate = advanceChartList.every((item) => {
    return item.field !== '';
  });
  if (!validate) {
    return { flag: false, error: i18n.t('pa.data.table.placeholder.inputPlaceholder8') };
  } else {
    return { flag: true, param: advanceChartList };
  }
};

// 判断字段中是否有重复字段
const getrepeat = (value, family, cloneTableData, resType) => {
  let result = '';
  if (resType !== 'HBASE') {
    result = cloneTableData.filter((item) => item.fieldName !== '' && item.fieldName === value);
  } else {
    result = cloneTableData.filter(
      (item) => item.fieldName !== '' && item.fieldName === value && item.columnFamily === family
    );
  }

  if (result && result.length >= 2) {
    return i18n.t('pa.data.table.detail.tips.repeatTips');
  } else {
    return '';
  }
};
// 重复的字段数据
const repeatNum = (cloneTableData) => {
  const name = new Map();
  const res: any = [];
  cloneTableData.forEach((item) => {
    if (item.fieldName === '') {
    } else {
      const ele = item.columnFamily !== undefined ? item.fieldName + item.columnFamily : item.fieldName;
      if (name.has(ele)) {
        name.set(ele, name.get(ele) + 1);
      } else {
        name.set(ele, 1);
      }
    }
  });
  for (const value of name.values()) {
    if (value >= 2) {
      res.push(value);
    }
  }
  return res.length;
};
const tabs = [
  {
    value: '1',
    label: i18n.t('pa.baseInformation')
  },
  {
    value: '2',
    label: i18n.t('pa.citationRelation')
  },
  {
    value: '3',
    label: i18n.t('pa.historyVersion')
  },
  {
    value: '4',
    label: i18n.t('pa.sourceCode')
  }
];

const isShowFieldColumn = (table, list) => {
  return list.map((item) => {
    if (item.name === 'field-column') {
      return {
        ...item,
        needHide: table.length === 3 ? false : true,
        required: table.length === 3 ? true : false
      };
    }
    return item;
  });
};

export {
  validateConnect,
  validateField,
  validateAdvancedConnect,
  valideFieldInfo,
  getrepeat,
  repeatNum,
  isShowFieldColumn,
  tabs
};
