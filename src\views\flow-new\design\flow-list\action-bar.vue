<template>
  <div class="action-bar">
    <div class="action-bar__left">
      <span v-show="!showBatchActionBar && !showSelectInput">
        <!--遍历一级操作栏：新建、批量操作、筛选（级联）、搜索-->
        <span v-for="el in flowActionList" :key="el.value" class="action-bar__icon">
          <el-tooltip
            v-if="el.type === 'ElTooltip'"
            :key="el.value"
            :content="el.content"
            effect="light"
            :open-delay="500"
            placement="bottom"
          >
            <i :class="el.icon" @click="handler(el.value)"></i>
          </el-tooltip>

          <el-popover
            v-if="el.type === 'ElPopover'"
            v-model="showPopover"
            popper-class="flow-action-bar"
            trigger="click"
          >
            <div v-for="item in el.options" :key="item.label" class="action-bar__left__selections">
              <span class="action-bar__left__selections--label">{{ item.label }}</span>
              <el-select
                v-model="filterParams[item.value]"
                class="action-bar__left__selections--select"
              >
                <el-option
                  v-for="status in item.children"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </div>
            <div class="action-bar__left__popover--action">
              <el-button size="small" @click="handleFilter('clear')">重置</el-button>
              <el-button size="small" type="primary" @click="handleFilter('confirm')">
                查询
              </el-button>
            </div>
            <i slot="reference" class="action-bar__icon" :class="el.icon"></i>
          </el-popover>
        </span>
      </span>
    </div>
    <!--遍历批量操作栏：发布、取消发布、启动、停止（停止&保留状态停止）、复制、移动、导出-->
    <div v-if="showBatchActionBar" style="padding-bottom: 15px">
      <div class="action-bar-batch">
        <el-checkbox v-model="isInnerChecked" :indeterminate="indeterminate" @change="handleChange">
          全选
        </el-checkbox>
        <span class="action-bar-batch__memo" @click="rollback">退出批量</span>
      </div>
      <div class="action-bar-oper">
        <template v-for="el in batchActionList">
          <el-tooltip
            v-if="el.type === 'ElTooltip'"
            :key="el.value"
            :content="el.content"
            effect="light"
            :open-delay="500"
            placement="bottom"
          >
            <i :class="el.icon" @click="batchHandler('', el.value)"></i>
          </el-tooltip>
          <span v-if="el.type === 'splitTag'" :key="el.content" class="action-bar__split">|</span>
          <el-dropdown
            v-if="el.type === 'ElDropdown'"
            :key="el.name"
            @command="batchHandler($event, el.name)"
          >
            <i class="action-bar__icon" :class="el.icon"></i>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="item in el.options"
                :key="item.command"
                :command="item.command"
              >
                {{ item.text }}
                <el-tooltip effect="light" placement="top" :content="item.content">
                  <i :class="item.icon"></i>
                </el-tooltip>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </div>
    </div>
    <!--显示搜索栏-->
    <bs-search
      v-if="showSelectInput"
      v-model="name"
      placeholder="查询"
      :maxlength="20"
      size="small"
      @search="handleSearch"
    />
    <!--搜索时，右侧图标不显示-->
    <span v-if="!showBatchActionBar" class="action-bar--right">
      <span v-if="showSelectInput" class="action-bar-batch__memo" @click="cancel">取消</span>
      <el-tooltip v-else content="刷新" effect="light" :open-delay="500">
        <i class="action-bar__icon iconfont icon-shuaxin" @click="handler('refresh')"></i>
      </el-tooltip>
    </span>
  </div>
</template>

<script lang="ts">
import { Component, Prop, PropSync } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { debounce } from 'lodash';
import { hasPermission } from '@/utils';
import { flowActionList, batchActionList } from './config';

@Component
export default class FlowListHeader extends PaBase {
  @PropSync('isBatch', { default: false }) isInnerBatch!: boolean;
  @PropSync('isCheckedAll', { default: false }) isInnerChecked!: boolean;
  @Prop({ default: false }) indeterminate!: boolean;
  showPopover = false;
  showBatchActionBar = false;
  showSelectInput = false;
  name = '';
  filterParams: any = {
    jobType: 'ALL',
    jobStatus: 'ALL',
    jobRunTimeStatus: 'ALL',
    mode: 'ALL'
  };
  batchHandler: any = debounce(this.debounceBatchHandler, 500);

  get flowActionList() {
    return flowActionList.filter((el) => hasPermission(el.access));
  }

  get batchActionList() {
    return batchActionList.filter((el) => hasPermission(el.access));
  }

  activated() {
    if (this.$store.state.job.flowSearchObj) {
      this.filterParams = { ...this.$store.state.job.flowSearchObj };
      this.name = this.$store.state.job.flowSearchObj.name;
      this.showSelectInput = !!this.name;
    } else {
      this.filterParams.jobStatus = this.$route.query?.state || 'ALL';
    }
  }

  // value: 方法名【add、batch、filter、search、refresh】
  handler(value: string) {
    value === 'refresh' ? debounce(this[value], 500)(value) : this[value](value);
  }

  // value: 批量方法
  debounceBatchHandler(state: string, value: string) {
    this.$emit('click', { value, state }); // 流程的批量操作
  }

  add(value: string) {
    this.$emit('click', { value, state: '' }); // 新建流程
  }

  batch() {
    // 1. 展示批量操作栏
    this.showBatchActionBar = true;
    // 2. flowlist前面出现复选框
    this.isInnerBatch = true;
  }

  // 点击搜索图标，显示搜索栏
  search() {
    this.showSelectInput = true;
  }

  //  触发搜索：1.筛选项变化
  handleFilter(type) {
    if (type === 'clear') {
      Object.keys(this.filterParams).forEach((el: any) => {
        if (el === 'jobStatus') {
          this.filterParams[el] = this.$route.query?.state || 'ALL';
        } else {
          this.filterParams[el] = 'ALL';
        }
      });
      return;
    }
    this.showPopover = false;
    this.$emit('search', { ...this.filterParams, name: this.name });
  }

  // 触发搜索：2.搜索栏值变化
  handleSearch(value: string) {
    this.$emit('search', { ...this.filterParams, name: value });
  }

  refresh(value: string) {
    this.$emit('click', { value, state: '' }); // 刷新流程
  }

  // 退出批量
  rollback() {
    this.showBatchActionBar = false;
    this.isInnerChecked = false;
    this.isInnerBatch = false;
  }

  // 取消搜索
  cancel() {
    this.showSelectInput = false;
    this.name = '';
    this.handleSearch('');
  }

  // 是否全选，全选
  handleChange(isChecked: boolean) {
    this.isInnerChecked = isChecked;
    this.$emit('change', isChecked);
  }
}
</script>
<style scoped lang="scss">
.action-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  border-bottom: 1px solid #f1f1f1;
  &__left {
    height: 50px;
    display: flex;
    align-items: center;
    &__selections {
      flex-direction: column;
      &--label {
        font-size: 14px;
        font-weight: 400;
        color: #444444;
        line-height: 20px;
        margin-bottom: 10px;
        margin-top: 14px;
      }
      &--select {
        margin-bottom: 10px;
      }
      & > div {
        display: flex;
      }
    }
    &__popover {
      &--action {
        text-align: center;
        margin-top: 14px;
      }
    }
  }

  &__icon {
    color: #757d86;
    cursor: pointer;
    font-size: 16px;
    margin-left: 20px;
    &:first-child {
      margin-left: unset;
    }
  }
  &__split {
    color: $--bs-color-border-lighter;
  }
  &-batch {
    width: 260px;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &__memo {
      font-size: 14px;
      color: $--bs-color-text-placeholder;
      cursor: pointer;
    }
  }
  &-oper {
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    i {
      cursor: pointer;
    }
  }
}
</style>
