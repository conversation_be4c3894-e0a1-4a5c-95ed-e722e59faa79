<template>
  <div class="service-table__container bs-page1 bs-layout-flex-column1">
    <!-- header -->
    <div class="service-table-header" :class="{ monitor }">
      <!-- button -->
      <el-button
        v-if="!monitor"
        type="text"
        icon="el-icon-arrow-right"
        class="service-mgr-button"
        :style="{ visibility: !collapse ? 'visible' : 'hidden' }"
        @click="collapse = !collapse"
      />
      <!-- bar -->
      <div class="service-table-bar">
        <div class="service-table-title">
          <span v-if="monitor" class="service-table-circular"></span>
          <span v-else class="service-table-square"></span>
          <span class="service-table-text">{{ title }}</span>
        </div>
        <div class="">
          <bs-search
            v-model="params.search"
            :class="{ isEn }"
            :placeholder="$t('pa.placeholder.nameOrUrl')"
            @search="getListData(false)"
          />
          <el-button v-if="!monitor" v-access="'PA.ELE.SERVICE.REG'" type="primary" @click="handleReg('')">
            {{ $t('pa.action.register') }}
          </el-button>
        </div>
      </div>
    </div>
    <!-- tableData -->
    <bs-table
      v-loading="loading"
      :height="height"
      :data="tableData"
      :column-data="columnData"
      :page-data="params.pageData"
      @page-change="handlePageChange"
      @row-dblclick="handleRowDblclick"
      @refresh="getListData(true)"
    >
      <!-- header-checkResult -->
      <template slot="header-checkResult" slot-scope="{ row }">
        <span class="service-table__label">{{ row.label }}</span>
        <bs-icon class="bs-icon-wenti" :tooltip="$t('pa.tip.checkResult')" />
      </template>
      <!-- status -->
      <template slot="status" slot-scope="{ row }">
        <el-tag size="mini" :type="tag.enumColor[row.status]" @click="handleTagClick(row)">
          {{ tag.enumData[row.status] }}
        </el-tag>
      </template>
      <!-- title -->
      <div slot="title" slot-scope="{ row }" class="service-table__title">
        <span class="service-table__title__text" :title="row.title">
          {{ row.title }}
        </span>
        <el-tag v-if="row.shareFlag" size="mini">{{ $t('pa.action.share') }}</el-tag>
      </div>
      <!-- operator -->
      <template slot="operator" slot-scope="{ row }">
        <template v-for="it in actionList">
          <el-tooltip v-if="getAccess(it, row)" :key="it.label" :content="it.label" effect="light">
            <i :class="it.icon" @click="it.handler(row)"></i>
          </el-tooltip>
        </template>
      </template>
    </bs-table>
    <!-- 终端 -->
    <term-dialog v-if="showTermDialog" :data="curRow" :show.sync="showTermDialog" />
    <!-- 上传弹窗 -->
    <scp-dialog v-if="showScpDialog" :data="curRow" :show.sync="showScpDialog" />
    <!-- 分享弹窗 -->
    <share-dialog v-if="showShareDialog" :data="curRow" :type="shareType" :visible.sync="showShareDialog" />
    <!-- 服务注册编辑弹窗 -->
    <reg-service-dialog
      v-if="showRegDialog"
      :id="resId"
      :show.sync="showRegDialog"
      :type="resType"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Watch, Vue } from 'vue-property-decorator';
import debounce from 'lodash/debounce';
import { delRes, getResList, kafkaSyncTopics, testConnect } from '@/apis/serviceApi';
import { safeArray, safeParse, timeFormat, hasPermission, hasServiceDetailAccess } from '@/utils';
import { EN_COLUMN_WIDTH_MAP } from './utils';
@Component({
  components: {
    TermDialog: () => import('./components/term-dialog.vue'),
    ScpDialog: () => import('./components/scp-dialog.vue'),
    RegServiceDialog: () => import('./components/reg-service-dialog/index.vue'),
    ShareDialog: () => import('@/components/share-dialog/index.vue')
  }
})
export default class ServiceTable extends Vue {
  @Prop({ default: '' }) title!: string;
  @Prop({ default: '' }) resType!: string;
  @PropSync('isCollapse', { type: Boolean }) collapse!: boolean;
  @Prop({ type: Boolean }) monitor!: boolean;

  loading = true;
  tableData: any[] = [];
  columnData: any[] = [];
  params: any = {
    search: '',
    pageData: { pageSize: this.$store.getters.pageSize || 25, currentPage: 1, total: 0 },
    sortData: { updateTime: 'DESC' }
  };
  getListData: any = debounce(this.handleGetListData, 500);

  filePath = '';
  resId = '';
  curRow: any = {};
  showRegDialog = false;
  showShareDialog = false;
  showTermDialog = false;
  showScpDialog = false;
  tag: any = {
    enumColor: {},
    enumData: {}
  };

  get actionList() {
    return [
      !this.monitor && {
        label: this.$t('pa.action.edit'),
        icon: 'iconfont icon-bianji',
        access: 'PA.ELE.SERVICE.EDIT',
        handler: (row: any) => this.handleReg(row.id)
      },
      !this.monitor && {
        label: this.$t('pa.action.del'),
        icon: 'iconfont icon-shanchu',
        access: 'PA.ELE.SERVICE.DELETE',
        handler: (row: any) => this.handleDelete(row)
      },
      {
        label: this.$t('pa.action.detail'),
        icon: 'iconfont icon-chakan',
        access: 'PA.ELE.SERVICE.VIEW_DETAIL',
        handler: (row: any) => this.handleRowDblclick(row)
      },
      !this.monitor && {
        label: this.$t('pa.action.share'),
        icon: 'iconfont icon-fenxiang',
        access: 'PA.ELE.SERVICE.SHARE',
        handler: (row) => this.handleShare(row)
      },
      !this.monitor &&
        this.resType === 'KAFKA' && {
          label: this.$t('pa.action.syncTopic'),
          icon: 'iconfont icon-jiquntopic',
          access: 'PA.ELE.SERVICE.EDIT',
          handler: (row) => this.handleSyncTopic(row)
        },
      !this.monitor &&
        this.resType === 'HOST' && {
          label: this.$t('pa.action.terminal'),
          icon: 'iconfont icon-zhongduan',
          access: 'PA.ELE.SERVICE.EDIT',
          handler: (row) => this.handleTerm(row)
        },
      !this.monitor &&
        this.resType === 'HOST' && {
          label: this.$t('pa.action.upload'),
          icon: 'iconfont icon-shangchuan',
          access: 'PA.ELE.SERVICE.EDIT',
          handler: (row) => this.handleUpload(row)
        }
    ].filter(Boolean);
  }
  get height() {
    return this.monitor ? 'calc(100vh - 288px)' : 'calc(100vh - 333px)';
  }
  get shareType() {
    return String(this.resType).toLowerCase() === 'kafka' ? 'kafka' : 'server';
  }
  @Watch('resType', { immediate: true })
  handleEesTypeChange() {
    if (!this.resType) return;
    this.params.search = '';
    this.params.pageData.currentPage = 1;
    this.handleGetListData();
  }

  // 获取集群列表
  async handleGetListData(refresh = false) {
    try {
      this.loading = true;
      const { success, data, error } = await getResList(this.resType, this.params);
      if (!success) return this.$message.error(error);
      this.columnData = safeArray(data?.columnData)
        .map((item) => {
          if (!this.isEn) return item;
          return {
            ...item,
            width: EN_COLUMN_WIDTH_MAP[item.value] || item.width
          };
        })
        .concat({
          label: this.$t('pa.flow.operator'),
          value: 'operator',
          fixed: 'right',
          width: this.monitor ? 100 : 180
        });
      const target = this.columnData.find((it) => it.value === 'status');
      target && (this.tag = target);
      this.tableData = safeArray(data?.tableData).map((it) => {
        it.updateTime = timeFormat(it.updateTime);
        it.zkRes = safeParse(it?.resProperty).zkRes;
        return it;
      });
      this.params.pageData.total = data?.pageData?.total || 0;
      refresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    } finally {
      this.loading = false;
    }
  }
  /* 注册服务 */
  handleReg(id = '') {
    this.resId = id;
    this.showRegDialog = true;
  }
  handleConfirm() {
    this.getListData(false);
    this.$emit('refresh');
  }
  /* 分页 */
  handlePageChange(currentPage: number, pageSize: number) {
    this.params.pageData.currentPage = currentPage;
    this.params.pageData.pageSize = pageSize;
    this.getListData(false);
  }
  /* 双击 */
  handleRowDblclick(row) {
    if (!hasServiceDetailAccess()) return;
    const path = `/element/service/${row.resType}/detail`;
    const { clusterType = '' } = safeParse(row?.resProperty);
    this.$router.push({ path, query: { id: row.id, resType: row.resType, title: row.title, clusterType } });
  }
  /* 状态tag点击 */
  async handleTagClick(row) {
    this.$set(row, 'status', 2);
    const { success, data, error } = await testConnect({ id: row.id });
    if (!success) return this.$message.error(error);
    const all = data.fail + data.success;
    this.$set(row, 'status', data.success ? 1 : 0);
    this.$set(row, 'checkResult', `${data.success}/${all}`);
  }
  getAccess({ access, label }, { dataLevelType = '', shareFlag }: any) {
    return label === this.$t('pa.action.detail')
      ? hasPermission(access)
      : !this.monitor && hasPermission(access) && dataLevelType && dataLevelType !== 'PARENT' && !shareFlag;
  }
  /* 删除 */
  async handleDelete(row: any) {
    try {
      await this.$confirm(this.$t('pa.flow.delMsg2'), this.$t('pa.action.tip'), { type: 'warning' });
      this.loading = true;
      const { success, error } = await delRes([row.id]);
      if (!success) return this.$message.error(error);
      this.getListData(false);
      this.$emit('refresh');
    } finally {
      this.loading = false;
    }
  }
  /* 同步topic */
  async handleSyncTopic(row: any) {
    const { success, msg, error } = await kafkaSyncTopics(row.id);
    if (!success) return this.$message.error(error);
    this.$message.success(msg);
  }
  /* 分享 */
  handleShare(row) {
    this.curRow = row;
    this.showShareDialog = true;
  }

  /* 终端 */
  async handleTerm(row: any) {
    this.curRow = row;
    this.showTermDialog = true;
  }
  /* 上传 */
  async handleUpload(row: any) {
    this.curRow = row;
    this.showScpDialog = true;
  }
}
</script>

<style lang="scss" scoped>
.service-table {
  &__container {
    background-color: #fff;
    ::v-deep .el-tag {
      cursor: pointer;
    }
    .iconfont {
      margin-right: 10px;
      cursor: pointer;
      &:last-of-type {
        margin-right: 0;
      }
    }
    .bs-search {
      margin-right: 10px;
      &.isEn {
        width: 309px !important;
      }
    }
  }
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 20px;
    height: 60px;
    border-bottom: 1px solid rgba(241, 241, 241, 1);
    &.monitor {
      padding: 0 20px;
      .service-table-bar {
        margin-left: 0;
      }
    }
  }
  &-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 7px;
    width: 100%;
    color: #444444;
    font-weight: bold;
  }
  &-title {
    display: flex;
    align-items: center;
  }
  &-circular {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 3px solid #ff9c00;
    background-color: #fff;
    border-radius: 50%;
  }
  &-square {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #ff9e2b;
    border-radius: 2px;
  }
  &-text {
    margin-left: 8px;
    vertical-align: middle;
  }
  &__label {
    margin-right: 4px;
    white-space: nowrap;
  }
  &__title {
    display: flex;
    align-items: center;
    &__text {
      display: inline-block;
      margin-right: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
