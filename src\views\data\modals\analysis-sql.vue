<template>
  <bs-dialog v-loading="loading" :title="$t('pa.data.table.detail.sqlParsing')" :visible.sync="visible" size="medium">
    <bs-code
      ref="codeRef"
      :value="sqlCode"
      language="sql"
      :operatable="false"
      :read-only="false"
      :extra-style="extraCodeStyle"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">{{ $t('pa.action.cancel') }}</el-button>
      <el-button type="primary" @click="handelAnalysis">{{ $t('pa.action.analysis') }}</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Vue, Prop, PropSync, Ref } from 'vue-property-decorator';
import { parsingSql } from '@/apis/sqlApi';
@Component
export default class AnalysisSql extends Vue {
  @Ref('codeRef') readonly codeRef!: any;
  @PropSync('show', { type: Boolean }) visible!: boolean;
  @Prop({ default: '' }) title;
  loading = false;
  sqlCode = '';
  extraCodeStyle = {
    height: '100%',
    minHeight: '300px'
  };

  async handelAnalysis() {
    this.sqlCode = this.codeRef.getValue();
    if (!this.sqlCode) return this.$message.error(this.$t('pa.placeholder.sqlPlaceholder'));
    this.loading = true;
    try {
      const { data, success, msg, error } = await parsingSql({ sql: this.sqlCode });
      this.loading = false;
      if (!success) return this.$tip.error({ message: error || msg, duration: 5000 });
      this.$tip.success(msg);
      this.$emit('save', data);
    } catch (err) {
      this.loading = false;
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 23px 20px;
}
</style>
