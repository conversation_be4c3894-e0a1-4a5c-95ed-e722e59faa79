import { getClustersTitle } from './utils';
import i18n from '@/i18n';
export default [
  {
    path: '/',
    meta: {
      access: 'PA.HOME',
      title: i18n.t('pa.homePage'),
      icon: 'iconfont icon-zhuye'
    },
    name: 'homepage',
    component: () => import('@/views/home/<USER>')
  },
  {
    path: '/monitor/service',
    name: 'monitorService',
    meta: {
      access: 'PA.MONITOR.SERVICE.MENU',
      title: i18n.t('pa.menu.serviceMonitor')
    },
    component: () => import('@/views/monitor/service/index.vue')
  },
  {
    path: '/monitor/clusters/:type',
    name: 'monitorClusters',
    beforeEnter: (to, from, next) => {
      // 获取动态路由标题
      to.meta.title = to.query.title;
      to.meta.resType = to.query.type;
      next();
    },
    component: () => import('@/views/monitor/service/table.vue')
  },
  {
    path: '/monitor/flow',
    name: 'flowMonitor',
    meta: {
      access: 'PA.MONITOR.FLOW.MENU',
      title: i18n.t('pa.menu.flowMonitor')
    },
    component: () => import('@/views/monitor/flow/index.vue')
  },
  {
    path: '/monitor/warningRule',
    name: 'monitorWarningRule',
    meta: {
      access: 'PA.MONITOR.WARN.MENU',
      title: i18n.t('pa.menu.monitorWarn')
    },
    component: () => import('@/views/monitor/warning-rule/index.vue')
  },
  {
    path: '/monitor/flow/detail',
    name: 'flowMonitorDetail',
    beforeEnter: (to, from, next) => {
      to.meta.title = i18n.t('pa.flowDetail', [to.query.title]);
      next();
    },
    component: () => import('@/views/flow-new/design/index.vue')
  },
  {
    path: '/data/sheetManage',
    name: 'sheetManage',
    meta: {
      access: 'PA.DATA.TABLE.MENU',
      title: i18n.t('pa.menu.sheetManage'),
      customAccess: 'enableSql'
    },
    component: () => import('@/views/data/sheet-manage/index.vue')
  },
  {
    path: '/data/catalogManage',
    name: 'catalogManage',
    meta: {
      access: 'PA.DATA.CATALOG.MENU',
      title: i18n.t('pa.cataLog.text5'),
      customAccess: 'enableSql'
    },
    component: () => import('@/views/data/catalog-manage/index.vue')
  },
  {
    path: '/data/catalogDetail',
    name: 'catalogDetail',
    beforeEnter: (to, from, next) => {
      to.meta.title = to.query.title;
      next();
    },
    meta: {
      customAccess: 'enableSql'
    },
    component: () => import('@/views/data/catalog-manage/detail/index.vue')
  },
  {
    path: '/data/sheetEdit',
    name: 'sheetEdit',
    beforeEnter: (to, from, next) => {
      to.meta.isReplaced = from.meta.isReplaced;
      to.meta.title = i18n.t('pa.sheetEdit', [to.query.title]);
      next();
    },
    meta: {
      customAccess: 'enableSql'
    },
    component: () => import('@/views/data/sheet-manage/add-edit/index.vue')
  },
  {
    path: '/data/sheetDetail',
    name: 'sheetDetail',
    beforeEnter: (to, from, next) => {
      to.meta.isReplaced = from.meta.isReplaced;
      to.meta.title = i18n.t('pa.sheetEdit', [to.query.title]);
      next();
    },
    meta: {
      customAccess: 'enableSql'
    },
    component: () => import('@/views/data/sheet-manage/detail/index.vue')
  },
  {
    path: '/data/UDF',
    name: 'UDF',
    meta: {
      access: 'PA.DATA.UDF.MENU',
      title: i18n.t('pa.menu.udf'),
      customAccess: 'enableSql'
    },
    component: () => import('@/views/data/udf/index.vue')
  },
  {
    path: '/data/udfEdit',
    name: 'udfEdit',
    beforeEnter: (to, from, next) => {
      to.meta.title = to.query.title;
      next();
    },
    meta: {
      customAccess: 'enableSql'
    },
    component: () => import('@/views/data/udf/add-edit/index.vue')
  },
  {
    path: '/data/udfHistory',
    name: 'udfHistory',
    beforeEnter: (to, from, next) => {
      to.meta.title = to.query.title;
      next();
    },
    meta: {
      customAccess: 'enableSql'
    },
    component: () => import('@/views/data/udf/version/index.vue')
  },
  {
    path: '/flowProject',
    name: 'flowProject',
    meta: {
      title: i18n.t('pa.flow.design'),
      access: 'PA.FLOW',
      icon: 'iconfont icon-liuchengsheji'
    },
    component: () => import('@/views/flow-new/project/index.vue')
  },
  {
    path: '/flowProject/batchOperationInfo',
    name: 'batchOperationInfo',
    meta: {
      title: i18n.t('pa.flow.batchOperationInfo'),
      access: 'PA.FLOW.FLOW_MGR.VIEW'
    },
    component: () => import('@/views/flow-new/project/batch-operation-info/index.vue')
  },
  {
    path: '/flow',
    name: 'flowNew',
    meta: {
      title: i18n.t('pa.flow.mgr'),
      access: 'PA.FLOW.FLOW_MGR.VIEW'
    },
    beforeEnter(to, from, next) {
      to.meta.title = to.query.title;
      next();
    },
    component: () => import('@/views/flow-new/design/index.vue')
  },
  {
    path: '/flowVersion',
    name: 'flowVersion',
    component: () => import('@/views/flow-new/design/version-detail.vue')
  },
  {
    path: '/flowFullScreen',
    name: 'flowFullScreen',
    component: () => import('@/views/flow-new/sql-code-fullscreen.vue')
  },
  {
    path: '/blood-relation',
    name: 'bloodRelation',
    meta: {
      title: i18n.t('pa.menu.bloodRelation'),
      access: 'PA.BLOOD_RELATION.VIEW',
      icon: 'iconfont icon-xieyuanguanxi'
    },
    component: () => import('@/views/blood-relation/index.vue')
  },
  {
    path: '/element/service',
    name: 'elementService',
    meta: {
      access: 'PA.ELE.SERVICE.MENU',
      title: i18n.t('pa.serviceMgr')
    },
    component: () => import('@/views/element/service/index.vue')
  },
  {
    path: '/element/service/:type/detail',
    name: 'serviceDetail',
    beforeEnter: (to, from, next) => {
      to.meta.title = `${getClustersTitle(to.query.resType)}：${to.query.title}`;
      next();
    },
    meta: {
      access: 'PA.ELE.SERVICE.VIEW_DETAIL'
    },
    component: () => import('@/views/element/service/detail/index.vue')
  },
  {
    path: '/element/warehouse',
    name: 'elementWarehouse',
    meta: {
      access: 'PA.ELE.WAREHOUSE.MENU',
      title: i18n.t('pa.componentLibMgr')
    },
    component: () => import('@/views/element/warehouse/index.vue')
  },
  {
    path: '/element/dictionary',
    name: 'templateDictionary',
    meta: {
      access: 'PA.SETTING.DIC.MENU', // 权限信息
      title: i18n.t('pa.dataDict')
    },
    component: () => import('../views/params/dictionary/index.vue')
  },
  {
    path: '/element/template/routeTemplate',
    name: 'routeTemplate',
    meta: {
      title: i18n.t('pa.menu.routeTemplate'),
      access: 'PA.SETTING.MODEL.ROUTE.MENU'
    },
    component: () => import('@/views/params/template/rule-template/index.vue')
  },
  {
    path: '/element/template/filterTemplate',
    name: 'templateFilterTemplate',
    meta: {
      title: i18n.t('pa.menu.filterTemplate'),
      access: 'PA.SETTING.MODEL.FILTER.MENU'
    },
    component: () => import('@/views/params/template/filter-template/index.vue')
  },
  {
    path: '/element/template/mapLibrary',
    name: 'templateMapLibrary',
    meta: {
      title: i18n.t('pa.menu.mapLibrary'),
      access: 'PA.SETTING.MODEL.MAPPING_FIELD.MENU'
    },
    component: () => import('@/views/params/template/map-library/index.vue')
  },
  {
    path: '/element/template/mapTemplate',
    name: 'templateMapTemplate',
    meta: {
      title: i18n.t('pa.menu.mapTemplate'),
      access: 'PA.SETTING.MODEL.MAPPING.MENU'
    },
    component: () => import('@/views/params/template/map-template/index.vue')
  },
  {
    path: '/element/template/routeTemplateDetail',
    name: 'elementRuletTemplateDetail',
    beforeEnter(to, from, next) {
      to.meta.title = to.query.title;
      next();
    },
    component: () => import('@/views/params/template/rule-template/detail.vue')
  },
  {
    path: '/element/template/routeTemplateHistory',
    name: 'templateRouteTemplateHistory',
    beforeEnter(to, from, next) {
      to.meta.title = to.query.title;
      next();
    },
    component: () => import('@/views/params/template/rule-template/history.vue')
  },
  {
    path: '/element/template/filterTemplateDetail',
    name: 'templateFilterTemplateDetail',
    beforeEnter(to, from, next) {
      to.meta.title = to.query.title;
      next();
    },
    component: () => import('@/views/params/template/filter-template/detail.vue')
  },
  {
    path: '/element/template/filterTemplateHistory',
    name: 'templateFilterTemplateHistory',
    beforeEnter(to, from, next) {
      to.meta.title = to.query.title;
      next();
    },
    component: () => import('@/views/params/template/filter-template/history.vue')
  },
  {
    path: '/element/template/mapTemplateEdit',
    name: 'templateMapTemplateEdit',
    beforeEnter(to, from, next) {
      to.meta.title = to.query.title;
      next();
    },
    component: () => import('@/views/params/template/map-template/edit.vue')
  },
  {
    path: '/element/template/mapTemplateHistory',
    name: 'templateMapTemplateHistory',
    beforeEnter(to, from, next) {
      to.meta.title = to.query.title;
      next();
    },
    component: () => import('@/views/params/template/map-template/history.vue')
  },
  {
    path: '/data/option',
    name: 'dataOption',
    meta: {
      title: i18n.t('pa.menu.optionManage'),
      access: 'PA.RES.ITEM.MENU'
    },
    component: () => import('@/views/source/option/index.vue')
  },
  {
    path: '/source/importExport',
    name: 'importExport',
    meta: {
      access: 'PA.RES.PRIVATE.TRANSPORT.MENU', // 权限信息
      title: i18n.t('pa.data.text15')
    },
    component: () => import('@/views/source/export-import/index.vue')
  },
  {
    name: 'sqlClip',
    path: '/clip/sql',
    meta: {
      title: i18n.t('pa.menu.sqlClipLib'),
      access: 'PA.ASSETS.SQL_CODE.MENU',
      customAccess: 'enableSql'
    },
    component: () => import('@/views/source/sql-clip/index.vue')
  },
  {
    name: 'sqlEdit',
    path: '/clip/sqlEdit',
    beforeEnter: (to, from, next) => {
      to.meta.title = i18n.t('pa.sqlFragment', [to.query.title]);
      next();
    },
    component: () => import('@/views/source/sql-clip/modals/add-edit.vue')
  },
  {
    path: '/globalSearch',
    name: 'globalSearch',
    meta: {
      access: 'PA.HOME',
      title: i18n.t('pa.globalSearch')
    },
    component: () => import('@/views/global-search/index.vue')
  },
  {
    // 用于当前tab页无感知刷新
    path: '/refresh',
    name: 'refresh',
    meta: {
      isReplaced: true
    },
    component: () => import(/* webpackChunkName: "refresh" */ '../components/refresh.vue')
  }
];
