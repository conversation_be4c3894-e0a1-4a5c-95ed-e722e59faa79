import i18n from '@/i18n';
export const REDIS_TYPES = Object.freeze({
  '0': i18n.t('pa.singlePoint'),
  '1': i18n.t('pa.cluster'),
  '2': i18n.t('pa.sentry')
});

export const STATE_TYPE = Object.freeze({
  CLOSING: i18n.t('pa.closing'),
  OFF: i18n.t('pa.action.close'),
  ON: i18n.t('pa.action.enable'),
  OPENING: i18n.t('pa.opening')
});
/* 显示组件地址映射 */
export const FILE_MAPPING = Object.freeze({
  '0': i18n.t('pa.singlePoint'),
  '1': i18n.t('pa.cluster'),
  '2': i18n.t('pa.sentry')
});

export const getCodeTitle = (type: string) => {
  const tip = {
    JDBC: i18n.t('pa.tableCode'),
    HIVE: i18n.t('pa.hiveCode'),
    HBASE: i18n.t('pa.hbaseCode'),
    ELASTICSEARCH: i18n.t('pa.indexCode'),
    CLICKHOUSE: i18n.t('pa.tableCode'),
    DORIS: i18n.t('pa.tableCode')
  }[type];
  return i18n.t('pa.defaultCode', [tip]);
};
export const TOPIC_MANAGER_COLUMN_DATA = [
  {
    label: i18n.t('pa.name'),
    value: 'topic',
    minWidth: '450px'
  },
  {
    label: i18n.t('pa.partCount'),
    value: 'partitions',
    minWidth: '245px'
  },
  {
    label: i18n.t('pa.copies'),
    value: 'replicas',
    minWidth: '245px'
  },
  {
    label: i18n.t('pa.action.action'),
    value: 'operator',
    minWidth: '150px',
    showOverflowTooltip: false
  }
];
export const getComponentName = (filePath: string) => {
  return (
    {
      /* base */
      'common/depend-table.vue': 'DependRelation',
      'common/node-table.vue': 'NodeInfo',
      'common/table-view.vue': 'TableView',
      'common/warn-rule-table.vue': 'WarnRules',
      /* flink */
      'flink/components/jobmanager-info.vue': 'FlinkJobManagerInfo',
      'flink/components/taskmanager-info.vue': 'FlinkTaskManagerInfo',
      'flink/components/use-info.vue': 'FlinkUseInfo',
      'flink/components/res-allocate.vue': 'FlinkResAllocate',
      'flink/components/per-job-res-used.vue': 'QueueResourceInfo',
      /* aerospike */
      'aerospike/components/cache-info.vue': 'CacheInfo',
      'aerospike/components/get-cache-data.vue': 'CacheData',
      /* kafka */
      'kafka/components/topic-manager.vue': 'TopicManager',
      'kafka/components/comsumer-manager.vue': 'ConsumerManager',
      /* redis */
      'redis/components/cache-info.vue': 'RedisCacheInfo',
      'redis/components/get-cache-data.vue': 'RedisCacheData',
      /* stream-cube */
      'stream-cube/components/script-table.vue': 'ScriptTable',
      'stream-cube/components/udf-table.vue': 'UdfTable',
      'stream-cube/components/udj-table.vue': 'UdjTable'
    }[filePath] || ''
  );
};
