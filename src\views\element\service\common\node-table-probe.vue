<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">节点</div>
      <div class="bs-page__header-operation">
        <el-button
          v-show="hasAuthority()"
          v-if="hasAuth"
          style="margin-right: 10px"
          @click="handleDeploy"
        >
          部署
        </el-button>
        <el-button v-show="hasAuthority()" @click="getNodeListData">刷新</el-button>
      </div>
    </div>
    <div class="tab-content">
      <base-table
        v-loading="nodeTableLoading"
        :height="height"
        :table-data="nodeTableData"
        :table-config="nodeTableConfig"
      />
      <term ref="Term" :visible="showTermDialog" @close="closeTermDialog" />
      <scp ref="Scp" :visible="showScpDialog" :record-id="recordId" @close="closeScpDialog" />
      <deploy
        ref="Deploy"
        :visible="showDeployDialog"
        :show-port="false"
        :show-start-cmd="false"
        :show-stop-cmd="false"
        @close="closeDeployDialog"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import {
  URL_HOST_FIND_BY_TITLE,
  URL_RES_FINDBYID,
  URL_RES_NODE_LIST,
  URL_RES_NODE_DELETE
} from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {
    'base-table': () => import('@/components/base-table.vue'),
    term: () => import('@/components/term.vue'),
    scp: () => import('@/components/scp.vue'),
    deploy: () => import('../common/deploy.vue')
  }
})
export default class NodeTable extends PaBase {
  detailRecord: any = {};
  @Prop({ default: '200' })
  height!: string;

  private nodeTableData: ITableData = {
    columnData: [],
    tableData: []
  };
  private nodeTableConfig: ITableConfig = {
    width: 200,
    columnsExtend: {
      edit: []
    }
  };
  private nodeTableLoading = false;

  private showTermDialog = false;
  private showScpDialog = false;
  private recordId = '';
  private showDeptRoleDialog = false;
  private showDeployDialog = false;
  get hasAuth() {
    return (
      this.nodeTableData.tableData.length === 0 &&
      this.hasFeatureAuthority('PA.ELE.SERVICE.RES.NODE_DEPLOY')
    );
  }
  mounted() {
    this.handleDetail(this.$route.query.id);
  }

  handleDetail(id: any) {
    this.nodeTableConfig.columnsExtend = {
      edit: []
    };
    if (!id) {
      return;
    }
    this.doGet(URL_RES_FINDBYID, {
      params: {
        id
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.detailRecord = resp.data;
        this.getNodeListData();
        if (this.detailRecord.belongType === 'SELF') {
          (this.nodeTableConfig.columnsExtend as any) = {
            edit: [
              {
                tipMessage: '编辑',
                handler: this.edit.bind(this),
                iconfont: 'icon-bianji',
                hasAuthority:
                  this.hasAuthority() && this.hasFeatureAuthority('PA.ELE.SERVICE.HOST.EDIT')
              },
              {
                tipMessage: '终端',
                handler: this.term.bind(this),
                iconfont: 'icon-zhongduan',
                hasAuthority:
                  this.hasAuthority() && this.hasFeatureAuthority('PA.ELE.SERVICE.HOST.TERM')
              },
              {
                tipMessage: '上传',
                handler: this.scp.bind(this),
                iconfont: 'icon-shangchuan',
                hasAuthority:
                  this.hasAuthority() && this.hasFeatureAuthority('PA.ELE.SERVICE.HOST.SCP')
              },
              {
                tipMessage: '删除',
                handler: this.delete.bind(this),
                iconfont: 'icon-shanchu',
                hasAuthority:
                  this.hasAuthority() && this.hasFeatureAuthority('PA.ELE.SERVICE.HOST.DELETE')
              }
            ]
          };
        }
      });
    });
  }

  getNodeListData() {
    if (!this.$route.query.id) {
      return;
    }
    this.nodeTableLoading = true;
    this.doGet(URL_RES_NODE_LIST, {
      params: {
        resId: this.$route.query.id
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.nodeTableData = {
          ...resp.data
        };
      });
      this.nodeTableLoading = false;
    });
  }
  handleDeploy() {
    (this.$refs.Deploy as any).loadData({
      resId: this.$route.query.id,
      resType: this.$route.query.resType,
      orgId: this.detailRecord.orgId
    });
    this.showDeployDialog = true;
  }
  hasAuthority() {
    return this.detailRecord.dataLevelType !== 'PARENT' && this.detailRecord.belongType === 'SELF';
  }
  delete(row: any) {
    this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        this.nodeTableLoading = true;
        this.doDelete(URL_RES_NODE_DELETE, { data: row.id }).then((resp: any) => {
          this.parseResponse(resp, () => {
            this.getNodeListData();
          });
          this.nodeTableLoading = false;
        });
      })
      .catch(() => {
        return true;
      });
  }
  edit(row: any) {
    (this.$refs.Deploy as any).loadData(row);
    this.showDeployDialog = true;
  }

  term(row: any) {
    this.doGet(URL_HOST_FIND_BY_TITLE, {
      params: { title: row.hostTitle }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        (this.$refs.Term as any).open(resp.data.id, resp.data.ip, row.rootDir);
        this.showTermDialog = true;
      });
    });
  }
  scp(row: any) {
    this.doGet(URL_HOST_FIND_BY_TITLE, {
      params: { title: row.hostTitle }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.showScpDialog = true;
        this.recordId = resp.data.id;
      });
    });
  }
  closeTermDialog() {
    this.showTermDialog = false;
  }
  closeScpDialog() {
    this.showScpDialog = false;
  }
  closeDeployDialog(needFresh: any) {
    if (needFresh === true) {
      this.getNodeListData();
    }
    this.showDeployDialog = false;
  }

  get nodeDataLen() {
    let success = 0;
    this.nodeTableData.tableData.forEach((n: any) => {
      if (n.status === 1) {
        success++;
      }
    });
    return success + '/' + this.nodeTableData.tableData.length;
  }
}
</script>
<style scoped>
.my-card_body ::v-deep .el-card__body {
  padding: 0px 0px 0px 0px;
}
.my-wrap-row {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
}
.my-wrap-col {
  border: 1px solid #000;
}
.my-wrap-tag {
  width: 350px;
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
