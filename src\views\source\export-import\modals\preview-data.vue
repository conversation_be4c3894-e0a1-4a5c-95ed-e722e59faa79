<script lang="tsx">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { IItem, IPreviewData, IPreviewItem } from '../interface';
import { getOverviewTips } from './utils';
import cookies from 'js-cookie';
import i18n from '@/i18n';

@Component({
  name: 'PreviewData'
})
export default class PreviewData extends Vue {
  @PropSync('visible') show!: boolean;
  @Prop({ default: false }) isDialog!: boolean;
  @Prop() data!: IPreviewData;
  @Prop({ default: 'calc(70vh - 150px)' }) tableHeight!: string | number;
  columnData = [
    { label: this.$t('pa.resource.importExport.assetId'), value: 'assetId' },
    { label: this.$t('pa.resource.importExport.assetName'), value: 'assetName' },
    { label: this.$t('pa.resource.importExport.assetType'), value: 'assetType' },
    { label: this.$t('pa.addOrg'), value: 'orgName' }
  ];
  overviewMsg = '';
  tableData!: IPreviewItem[];
  assetsOptions!: IItem[];
  pageData = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 };
  showTableData: IPreviewItem[] = [];
  search = {
    type: '',
    idOrName: ''
  };

  created() {
    this.initData();
    this.handleSearch();
  }

  initData() {
    const { dataPreviews, statisticsDetails } = this.data;
    this.overviewMsg = getOverviewTips(this.data);
    this.assetsOptions = statisticsDetails
      .filter((el) => el.count)
      .map((el) => {
        return { label: el.label, value: el.label };
      });
    this.tableData = dataPreviews;
  }

  handleSearch() {
    this.showTableData = this.tableData
      .filter(({ assetType }) => (this.search.type ? assetType === this.search.type : true))
      .filter(
        ({ assetId, assetName }) => assetId.includes(this.search.idOrName) || assetName.includes(this.search.idOrName)
      );
    this.pageData.total = this.showTableData.length;
    this.pageData.currentPage = 1;
  }
  // 表格页码修改回调
  handlePageChange(page: number, size: number) {
    this.pageData.currentPage = page;
    this.pageData.pageSize = size;
  }
  closeDialog() {
    this.show = false;
  }

  render() {
    const {
      overviewMsg,
      assetsOptions,
      search,
      tableHeight,
      columnData,
      pageData,
      showTableData,
      handleSearch,
      handlePageChange,
      isDialog,
      show,
      closeDialog
    } = this;
    const selectWidth = {
      width: cookies.get('Language') === 'en-US' ? '225px' : '150px'
    };
    console.log(selectWidth);
    const coreTemplate = (
      <div>
        <header class="header">
          <div class="header-info" title={overviewMsg}>
            {overviewMsg}
          </div>
          <div class="header-oper">
            <bs-select
              v-model={search.type}
              size="small"
              style={selectWidth}
              options={assetsOptions}
              placeholder={i18n.t('pa.data.text32')}
              clearable
              onChange={handleSearch}
            />
            <bs-search
              v-model={search.idOrName}
              size="small"
              class="header-oper--search"
              placeholder={i18n.t('pa.data.text33')}
              onChange={handleSearch}
            />
          </div>
        </header>
        <bs-table
          size="small"
          height={tableHeight}
          column-settings={false}
          data={showTableData}
          column-data={columnData}
          page-data={pageData}
          paging-front
          on-page-change={handlePageChange}
        />
      </div>
    );
    if (!isDialog) return coreTemplate;
    return (
      <bs-dialog
        title={this.$t('pa.resource.importExport.exportDataPreview')}
        size="large"
        append-to-body
        visible={show}
        class="preview-export"
        footer-visible={false}
        beforeClose={closeDialog}
      >
        {coreTemplate}
      </bs-dialog>
    );
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0;
}
.preview-export .header {
  height: 50px;
  padding: 20px 34px;
}
.header {
  display: flex;
  height: 38px;
  justify-content: space-between;
  align-items: center;
  padding: 0 34px 10px;

  &-info {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &-oper {
    margin-left: 10px;
    &--search {
      width: 185px !important;
      margin-left: 10px;
    }
  }
}
::v-deep .el-dialog__body,
::v-deep .bs-dialog .el-dialog__body {
  padding: 0;
  max-height: 70vh;
}
</style>
