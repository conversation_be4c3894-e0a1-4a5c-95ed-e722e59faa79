<template>
  <bs-dialog
    :title="id ? $t('pa.params.template.detail.editField') : $t('pa.params.template.detail.newBuiltField')"
    :visible="visible"
    width="700px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    class="my-dialog_body"
  >
    <el-form ref="form" :model="formData" :rules="rules" label-width="100px">
      <el-form-item :label="$t('pa.params.template.detail.systemNumber')" prop="systemId" class="form-item">
        <el-input v-model="formData.systemId" class="form-item-input" />
      </el-form-item>
      <el-form-item :label="$t('pa.params.template.detail.systemName')" prop="systemName" class="form-item">
        <el-input v-model="formData.systemName" class="form-item-input" />
      </el-form-item>
      <el-form-item :label="$t('pa.params.template.detail.mapField')" prop="mappingFieldName" class="form-item">
        <el-input v-model="formData.mappingFieldName" class="form-item-input" />
      </el-form-item>
      <el-form-item :label="$t('pa.params.template.detail.fieldType')" prop="mappingFieldType" class="form-item">
        <el-select v-model="formData.mappingFieldType" :placeholder="$t('pa.placeholder.select')" style="width: 230px">
          <el-option v-for="fItem in fieldTypes" :key="fItem" :label="fItem" :value="fItem" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('pa.params.template.detail.cnName')" prop="cnName" class="form-item">
        <el-input v-model="formData.cnName" class="form-item-input" />
      </el-form-item>
      <el-form-item :label="$t('pa.params.template.detail.sourceCode')" prop="sourceCode" class="form-item">
        <el-input v-model="formData.sourceCode" class="form-item-input" />
      </el-form-item>
      <el-form-item :label="$t('pa.params.template.detail.standardCode')" prop="standardCode" class="form-item">
        <el-input v-model="formData.standardCode" class="form-item-input" />
      </el-form-item>
      <el-form-item :label="$t('pa.params.template.detail.sourceCodeComment')" prop="sourceCodeComment" class="form-item">
        <el-input v-model="formData.sourceCodeComment" class="form-item-input" />
      </el-form-item>
      <el-form-item
        :label="$t('pa.params.template.detail.standardCodeComment')"
        prop="standardCodeComment"
        class="form-item"
      >
        <el-input v-model="formData.standardCodeComment" class="form-item-input" />
      </el-form-item>
      <el-form-item :label="$t('pa.params.template.detail.serialNumber')" prop="serialNumber" class="form-item">
        <el-input v-model="formData.serialNumber" class="form-item-input" />
      </el-form-item>
      <el-form-item
        :label="$t('pa.params.template.detail.serialName')"
        prop="serialName"
        class="form-item"
        style="float: left"
      >
        <el-input v-model="formData.serialName" class="form-item-input" />
      </el-form-item>
      <el-form-item :label="$t('pa.notes')" prop="memo" class="form-item" style="width: 100%">
        <el-input v-model="formData.memo" type="textarea" row="4" class="form-item-textarea" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">{{ $t('pa.action.close') }}</el-button>
      <el-button type="primary" :loading="loading" @click="comfirm()">{{ $t('pa.action.confirm') }}</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { get, post } from '@/apis/utils/net';
@Component({
  name: 'AddTopicModal'
})
export default class AddTopicModal extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({}) id!: any;
  loading = false;
  formData = {
    systemName: '',
    // 系统编号
    systemId: '',
    cnName: '',
    dataLevelType: 'PARENT',
    id: '',
    mappingFieldName: '',
    mappingFieldType: 'String',
    memo: '',
    orgName: '',
    serialName: '',
    serialNumber: '',
    sourceCode: '',
    sourceCodeComment: '',
    standardCode: '',
    standardCodeComment: ''
  };
  // 表单校验规则
  rules = {
    systemId: [
      {
        required: true,
        message: `${this.$t('pa.placeholder.input')}${this.$t('pa.params.template.detail.systemNumber')}`,
        trigger: 'blur'
      }
    ],
    mappingFieldName: [
      {
        required: true,
        message: `${this.$t('pa.placeholder.input')}${this.$t('pa.params.template.detail.mapField')}`,
        trigger: 'blur'
      }
    ],
    mappingFieldType: [
      {
        required: true,
        message: `${this.$t('pa.placeholder.input')}${this.$t('pa.params.template.detail.fieldType')}`,
        trigger: 'change'
      }
    ],
    sourceCode: [
      {
        required: true,
        message: `${this.$t('pa.placeholder.input')}${this.$t('pa.params.template.detail.sourceCode')}`,
        trigger: 'blur'
      }
    ],
    standardCode: [
      {
        required: true,
        message: `${this.$t('pa.placeholder.input')}${this.$t('pa.params.template.detail.standardCode')}`,
        trigger: 'blur'
      }
    ]
  };
  // 字段类型
  fieldTypes = [];
  async created() {
    this.getFieldTypes();
    if (this.id) {
      this.getDetail();
    }
  }
  async getDetail() {
    const { data, success } = await get('/rs/pa/mappingWarehouse/details', { id: this.id });
    if (success) {
      this.formData = data;
    }
  }
  async getFieldTypes() {
    const { data = [] } = await get('/rs/pa/mappingWarehouse/listFieldType');
    this.fieldTypes = data;
  }
  comfirm() {
    // 确认
    (this.$refs['form'] as any).validate(async (valid) => {
      if (valid) {
        const url = this.formData.id ? '/rs/pa/mappingWarehouse/update' : '/rs/pa/mappingWarehouse/add';
        const { msg, success } = await post(url, this.formData);
        this.$message({ message: msg, type: success ? 'success' : 'error' });
        if (success) {
          this.$emit('close', true);
        }
      }
    });
  }
  closeDialog() {
    this.$emit('close');
  }
}
</script>
<style scoped lang="scss">
.my-dialog_body ::v-deep .el-dialog__body {
  text-align: left;
}
.form-item {
  display: inline-block;
  width: 50%;
}
.form-item-input {
  width: 230px;
}
.form-item-textarea {
  width: 545px;
  float: left;
}
</style>
