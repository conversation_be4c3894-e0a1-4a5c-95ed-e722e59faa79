.empty-canvas {
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: calc(100vh - 156px);
  background: #fafcff;
  z-index: 100;
}
.dag-style {
  width: 100%;
  height: calc(100vh - 156px);
}
.dag-node {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #c2d0f0;
  &.is-active {
    border-color: $--bs-color-primary;
    box-shadow: 0 0 3px 3px rgba($color: $--bs-color-primary, $alpha: 0.2);
  }
  &.is-error {
    border-color: $--bs-color-red;
    box-shadow: 0 0 3px 3px rgba($color: $--bs-color-red, $alpha: 0.2);
  }
  &__left {
    width: calc(100% - 26px);
    height: 100%;
    display: flex;
    align-items: center;
  }
  &__name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: $--bs-color-text-primary;
  }
  &__icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-right: 10px;
    color: #fff;
    background: $--bs-color-primary;
    border-radius: 3px 0 0 3px;
  }
  &__num {
    flex-shrink: 0;
    min-width: 20px;
    height: 18px;
    padding: 0px 5px;
    margin: 0 4px;
    line-height: 18px;
    font-size: 12px;
    color: #377cff;
    text-align: center;
    background: #ebf2ff;
    border-radius: 9px;
  }
  &__status {
    flex-shrink: 0;
    margin-right: 10px;
    cursor: default;
  }
  &__con {
    width: 142px;
    display: flex;
  }
}

.canvas {
  &-error-info {
    position: absolute;
    top: 20px;
    left: 68px;
    right: 192px;
    display: flex;
    align-items: center;
    height: 36px;
    padding: 0 14px;
    background: $--bs-color-red-light-2;
    border-radius: 4px;
    border: 1px solid $--bs-color-red;
    color: $--bs-color-red;
    &__title {
      flex: 1;
      padding: 0 8px;
    }
    &__count {
      padding-right: 20px;
    }
    .el-icon-caret-top {
      margin-right: 8px;
    }
    .el-icon-caret-top,
    .el-icon-caret-bottom {
      cursor: pointer;
      font-size: 20px;
    }
    .el-icon-caret-top.is-disabled,
    .el-icon-caret-bottom.is-disabled {
      cursor: not-allowed;
      color: $--bs-color-red-light-1;
    }
  }
  &-log-set {
    &__title {
      font-weight: bolder;
    }
    &__content {
      margin: 10px 0;
    }
    &__button {
      position: absolute;
      top: 22px;
      left: 18px;
    }
  }
}
