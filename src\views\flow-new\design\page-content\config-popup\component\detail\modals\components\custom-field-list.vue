<template>
  <div class="custom-field">
    <div class="custom-field__btn">
      <el-button v-if="!disabled" style="margin-bottom: 16px" @click="addRow">{{ $t('pa.flow.createRow') }}</el-button>
    </div>
    <pro-edit-table
      ref="editTable"
      class="custom-field__table"
      :data="tableData"
      :columns="columns"
      :edit-props="editProps"
      :show-operate-column="!disabled"
      @change="handleChange"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
interface Data {
  name: string;
  value: string;
}
@Component
export default class CustomFieldList extends Vue {
  [x: string]: any;
  @Prop() data!: Data[];
  @Prop({ default: false }) disabled!: boolean;
  tableData: Data[] = [];
  columns = [
    {
      label: this.$t('pa.flow.key'),
      value: 'name',
      formProps: {
        type: 'input',
        rules: [{ pattern: /^[A-Za-z0-9-_!@#￥%&*().]+$/, message: this.$t('pa.flow.msg82') }]
      }
    },
    {
      label: this.$t('pa.flow.val'),
      value: 'value',
      formProps: {
        type: 'input'
      }
    }
  ];
  editProps = {
    multiple: true,
    operateColumnWidth: this.isEn ? 150 : 120,
    defaultActions: ['edit', 'del']
  };
  @Watch('data', { immediate: true })
  handleDataChange(newVal) {
    this.tableData = [...(newVal || [])];
  }
  // 添加一行
  addRow() {
    (this.$refs.editTable as any).action.create();
  }
  // 数据变更
  handleChange(val) {
    const data = (val || [])
      .map((item) => {
        const { name, value } = item;
        return { name, value };
      })
      .filter((item) => item.name !== '' && item.value !== '');
    this.$emit('change', data);
  }
}
</script>
<style lang="scss" scoped>
.custom-field {
  &__btn {
    text-align: right;
  }
  &__table {
    border: 1px solid #ebeef5;
    border-bottom: none;
    ::v-deep .cell > div > .el-button {
      margin-right: 10px;
    }
    ::v-deep .cell .el-button--text + .el-button--text {
      margin-left: 0;
    }
  }
}
</style>
