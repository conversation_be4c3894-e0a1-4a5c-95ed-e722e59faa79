<template>
  <pro-grid v-loading="loading" type="info" :title="title">
    <!-- operation -->
    <slot slot="operation" name="operation"></slot>
    <!-- main -->
    <bs-table
      :height="height"
      :data="tableData"
      :page-data="pageData"
      :selection="selection"
      :column-settings="false"
      :column-data="columnData"
      :paging-front="pagingFront"
      @page-change="handlePageChange"
      @selection-change="handlesSelectionChange"
    >
      <template v-for="it in columnData" :slot="it.value" slot-scope="item">
        <slot :name="it.value" :item="item"></slot>
      </template>
    </bs-table>
    <!-- footer -->
    <slot name="footer"></slot>
  </pro-grid>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class TableBlock extends Vue {
  @Prop({ default: '' }) title!: string;
  @Prop({ default: undefined }) height!: string;
  @Prop({ default: false }) loading!: boolean;
  @Prop({ default: false }) selection!: boolean;
  @Prop({ default: false }) pagingFront!: boolean;
  @Prop({ default: () => null }) pageData!: any;
  @Prop({ default: () => [] }) tableData!: any[];
  @Prop({ default: () => [] }) columnData!: any[];

  handlePageChange(currentPage: number, pageSize: number) {
    this.$emit('page-change', currentPage, pageSize);
  }
  handlesSelectionChange(selection: any[]) {
    this.$emit('selection-change', selection);
  }
}
</script>
