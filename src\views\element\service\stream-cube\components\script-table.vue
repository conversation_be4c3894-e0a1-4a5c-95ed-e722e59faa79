<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">脚本信息</div>
      <div class="bs-page__header-operation">
        <el-button size="small" type="primary" @click="getListData">刷新</el-button>
      </div>
    </div>
    <div class="tab-content">
      <base-table
        v-loading="tableLoading"
        :height="'calc(100% - 34px)'"
        :table-data="tableData"
        :table-config="tableConfig"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { detailScScript } from '@/apis/serviceApi';
@Component({
  components: {
    'base-table': () => import('@/components/base-table.vue')
  }
})
export default class ScriptTable extends Vue {
  tableLoading = false;
  tableData: ITableData = {
    columnData: [],
    tableData: [],
    pageData: {
      pageSize: 10,
      currentPage: 1,
      total: 0
    }
  };
  allTableData: any = [];
  tableConfig: ITableConfig = {
    width: 150,
    columnsExtend: {}
  };
  created() {
    this.getListData();
  }

  async getListData() {
    this.tableLoading = true;
    const { data, success, msg } = await detailScScript(this.$route.query.id);
    if (success) {
      if (!data.tableData) {
        data.tableData = [];
      }
      this.allTableData = data.tableData;
      this.tableData = {
        columnData: data.columnData,
        tableData: data.tableData.slice(0, 10),
        pageData: {
          currentPage: 1,
          pageSize: 10,
          total: data.tableData.length
        }
      };
      this.tableLoading = false;
    } else {
      this.$message.error(msg);
      this.tableLoading = false;
    }
  }
  handleCurrentChange(val) {
    if (this.tableData.pageData) {
      this.tableData.pageData.currentPage = val;
    }
    this.tableData.tableData = this.allTableData.slice(10 * (val - 1), 10 * val);
  }
}
</script>
<style scoped>
.my-card_body ::v-deep .el-card__body {
  padding: 0px 0px 0px 0px;
}
.my-wrap-row {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
}
.my-wrap-col {
  border: 1px solid #000;
}
.my-wrap-tag {
  width: 350px;
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tab-content {
  /* height: calc(100% - 51px); */
  font-size: 12px;
  color: #666666;
  border-top: 1px solid #d4dce2;
}
</style>
