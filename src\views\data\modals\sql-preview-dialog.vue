<!-- >保存提示弹窗<-->
<template>
  <bs-dialog :title="title" :visible.sync="display" :footer-visible="false" size="medium" class="preview-dialog">
    <bs-code :value="data" language="sql" :read-only="true" :operatable="false" :extra-style="{ height: '400px' }" />
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Vue, Prop, PropSync } from 'vue-property-decorator';
import i18n from '@/i18n';
@Component
export default class SqlPreviewDialog extends Vue {
  @PropSync('show', { type: Boolean }) display!: boolean;
  @Prop({ default: '' }) data!: string;
  @Prop({ default: i18n.t('pa.data.table.detail.sqlPreview') }) title!: string;
}
</script>
<style lang="scss" scoped>
.preview-dialog {
  ::v-deep .el-dialog__body {
    padding: 0;
    overflow: hidden;
  }
}
</style>
