<template>
  <pro-grid type="info" :title="title">
    <el-descriptions
      v-for="it in list"
      :key="getKey(it)"
      :column="getColumn(it)"
      label-class-name="info-block__label"
      content-class-name="info-block__content"
    >
      <el-descriptions-item v-for="el in it" :key="el.value" :label="el.label">
        <el-tooltip v-hide effect="light" :content="el.value">
          <span class="info-block__content">{{ el.value }}</span>
        </el-tooltip>
      </el-descriptions-item>
    </el-descriptions>
  </pro-grid>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { sha256 } from '@/utils';

@Component
export default class InfoBlock extends Vue {
  @Prop({ default: '' }) title!: string;
  @Prop({ default: 4 }) column!: number;
  @Prop({ default: () => ({}) }) list!: [];

  getKey(it: any) {
    return sha256(JSON.stringify(it));
  }
  getColumn(it) {
    return it.length > 1 ? this.column : 1;
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-descriptions {
  margin: 0 20px;
  &-item {
    padding-bottom: 0 !important;
    height: 47px;
  }
}
::v-deep .info-block {
  &__label {
    display: inline-block;
    width: fit-content;
    font-size: 14px;
  }
  &__content {
    display: inline-block;
    width: 100%;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
