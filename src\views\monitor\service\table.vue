<template>
  <service-table monitor :title="title" :res-type="resType" />
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';

@Component({
  components: { ServiceTable: () => import('@/views/element/service/service-table.vue') }
})
export default class MonitorServiceTable extends Vue {
  title = '';
  resType = '';

  created() {
    this.title = this.$route.query.title as string;
    this.resType = this.$route.query.type as string;
  }
}
</script>
