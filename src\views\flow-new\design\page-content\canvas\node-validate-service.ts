import i18n from '@/i18n';
// 特殊节点配置是否为空的校验逻辑
class ValidateInfo {
  isError = false;
  errorMsg = '';
  constructor(isError = false, errorMsg = '') {
    this.isError = isError;
    this.errorMsg = errorMsg;
  }
  setDefaultErrorMsg(nodeName) {
    return i18n.t('pa.flow.msg67', [nodeName]);
  }
}
const isValidArray = (t: any) => Array.isArray(t) && t.length > 0;

// [base] 【JOIN】 【FILTER】
const baseValidateMethod = (node) => {
  const info = new ValidateInfo();
  info.isError = !node.properties;
  info.isError && info.setDefaultErrorMsg(node.nodeName);
  return info;
};

// 【MAP】
const mapValidateMethod = (node) => {
  const info = new ValidateInfo();
  info.isError =
    (!node.outputFields || node.inputFields.length === 0) && (!node.inputFields || node.inputFields.length === 0);
  info.isError && info.setDefaultErrorMsg(node.nodeName);
  return info;
};
// 【DD-MAP】
const DdMapValidateMethod = (node) => {
  const info = new ValidateInfo();
  /* 输入 输出字段 */
  if (!isValidArray(node.outputFields) || !isValidArray(node?.inputFields)) {
    info.isError = true;
    info.errorMsg = i18n.t('pa.flow.msg279', [node.nodeName]) as string;
    return info;
  }
  if (!node.properties?.className) {
    info.isError = true;
    info.errorMsg = i18n.t('pa.flow.msg280', [node.nodeName]) as string;
    return info;
  }
  if (!isValidArray(node.properties?.filedList)) {
    info.isError = true;
    info.errorMsg = i18n.t('pa.flow.msg281', [node.nodeName]) as string;
    return info;
  }
  const input = node.inputFields.map((it) => it.name);
  for (const { chooseFieldFromParent } of node.properties.filedList) {
    const flag = chooseFieldFromParent && !input.includes(chooseFieldFromParent);
    if (flag) {
      info.isError = true;
      info.errorMsg = i18n.t('pa.flow.msg282', [node.nodeName, chooseFieldFromParent]) as string;
      return info;
    }
  }
  return info;
};

// 【SOURCE_SQL】
const sourceSqlValidateMethod = (node) => {
  const info = new ValidateInfo();
  info.isError = !node.properties || !node.properties.sourceTable;
  info.isError && (info.errorMsg = i18n.t('pa.flow.msg68', [node.nodeName]) as string);
  return info;
};

// 【PROCESS_SQL】
const processSqlValidateMethod = (node) => {
  const info = new ValidateInfo();
  info.isError = !node.properties || node.outputFields === 0;
  info.isError && (info.errorMsg = i18n.t('pa.flow.msg69', [node.nodeName]) as string);
  return info;
};

// 【SINK_SQL】
const sinkSqlValidateMethod = (node) => {
  if (!node?.properties || !isValidArray(node?.outputFields)) {
    return new ValidateInfo(true, i18n.t('pa.flow.msg70', [node.nodeName]) as string);
  }
  if (!node?.properties?.sourceTable) {
    return new ValidateInfo(true, i18n.t('pa.flow.msg71', [node.nodeName]) as string);
  }
  if (!node?.properties?.sinkTable || !node?.properties?.sinkTableId || !node?.properties?.sinkTableType) {
    return new ValidateInfo(true, i18n.t('pa.flow.msg72', [node.nodeName]) as string);
  }
  if (!isValidArray(Object.keys(node?.properties?.outputFieldsMap || {}))) {
    return new ValidateInfo(true, i18n.t('pa.flow.msg73', [node.nodeName]) as string);
  }
  return new ValidateInfo();
};

const nodeValidate = {
  MAP: mapValidateMethod,
  'DD-MAP': DdMapValidateMethod,
  JOIN: baseValidateMethod,
  FILTER: baseValidateMethod,
  SOURCE_SQL: sourceSqlValidateMethod,
  PROCESS_SQL: processSqlValidateMethod,
  SINK_SQL: sinkSqlValidateMethod,
  // TODO:目前以下组件是按照基础校验来处理的，后端也会进行校验，如果有问题再来补相应逻辑
  CUBE_BATCH_PROCESS: baseValidateMethod,
  DYNAMIC_FILTER: baseValidateMethod,
  'JSON-EXTRACT': baseValidateMethod,
  ROUTETEMPLATE: baseValidateMethod,
  'Dynamic-MAP': baseValidateMethod,
  DELAY_PROCESSING: baseValidateMethod,
  DATA_QUALITY_MONITOR: baseValidateMethod,
  JDBC_COLLECTION: baseValidateMethod
};
export default nodeValidate;
