// 特殊节点配置是否为空的校验逻辑
class ValidateInfo {
  isError = false;
  errorMsg = '';
  constructor(isError = false, errorMsg = '') {
    this.isError = isError;
    this.errorMsg = errorMsg;
  }
  setDefaultErrorMsg(nodeName) {
    return `组件${nodeName}的配置未完成！`;
  }
}
const isValidArray = (t: any) => Array.isArray(t) && t.length > 0;

// [base] 【JOIN】 【FILTER】
const baseValidateMethod = (node) => {
  const info = new ValidateInfo();
  info.isError = !node.properties;
  info.isError && info.setDefaultErrorMsg(node.nodeName);
  return info;
};

// 【MAP】
const mapValidateMethod = (node) => {
  const info = new ValidateInfo();
  info.isError =
    (!node.outputFields || node.inputFields.length === 0) &&
    (!node.inputFields || node.inputFields.length === 0);
  info.isError && info.setDefaultErrorMsg(node.nodeName);
  return info;
};

// 【SOURCE_SQL】
const sourceSqlValidateMethod = (node) => {
  const info = new ValidateInfo();
  info.isError = !node.properties || !node.properties.sourceTable;
  info.isError && (info.errorMsg = `组件${node.nodeName}'的【表/视图】字段未配置！`);
  return info;
};

// 【PROCESS_SQL】
const processSqlValidateMethod = (node) => {
  const info = new ValidateInfo();
  info.isError = !node.properties || node.outputFields === 0;
  info.isError && (info.errorMsg = `组件${node.nodeName}'的【视图】未选择！`);
  return info;
};

// 【SINK_SQL】
const sinkSqlValidateMethod = (node) => {
  if (!node?.properties || !isValidArray(node?.outputFields)) {
    return new ValidateInfo(true, `组件${node.nodeName}'未配置完成`);
  }
  if (!node?.properties?.sourceTable) {
    return new ValidateInfo(true, `组件${node.nodeName}'【源表】未选择！`);
  }
  if (
    !node?.properties?.sinkTable ||
    !node?.properties?.sinkTableId ||
    !node?.properties?.sinkTableType
  ) {
    return new ValidateInfo(true, `组件${node.nodeName}'【输出表】未选择！`);
  }
  if (!isValidArray(Object.keys(node?.properties?.outputFieldsMap || {}))) {
    return new ValidateInfo(true, `组件${node.nodeName}'【输出表字段】未选择！`);
  }
  return new ValidateInfo();
};
const nodeValidate = {
  MAP: mapValidateMethod,
  JOIN: baseValidateMethod,
  FILTER: baseValidateMethod,
  SOURCE_SQL: sourceSqlValidateMethod,
  PROCESS_SQL: processSqlValidateMethod,
  SINK_SQL: sinkSqlValidateMethod,
  // TODO:目前以下组件是按照基础校验来处理的，后端也会进行校验，如果有问题再来补相应逻辑
  CUBE_BATCH_PROCESS: baseValidateMethod,
  DYNAMIC_FILTER: baseValidateMethod,
  'JSON-EXTRACT': baseValidateMethod,
  ROUTETEMPLATE: baseValidateMethod,
  'Dynamic-MAP': baseValidateMethod,
  DELAY_PROCESSING: baseValidateMethod,
  DATA_QUALITY_MONITOR: baseValidateMethod
};
export default nodeValidate;
