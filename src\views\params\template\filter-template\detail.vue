<template>
  <pro-page :title="filterTemplateTitle" fixed-header class="filter">
    <div slot="operation">
      <el-button type="primary" size="small" @click="save">{{ $t('pa.action.save') }}</el-button>
    </div>
    <pro-grid class="filter-grid" direction="column">
      <pro-grid type="info" :title="$t('pa.params.template.detail.baseInfo')">
        <el-form
          ref="form"
          class="filter-grid--form"
          :model="form"
          :rules="formRules"
          :label-width="isEn ? '130px' : '100px'"
        >
          <el-form-item :label="$t('pa.params.template.detail.templateName')" prop="filterTemplateName">
            <el-input
              v-model="form.filterTemplateName"
              maxlength="100"
              :placeholder="$t('pa.params.template.name')"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item :label="$t('pa.notes')">
            <el-input
              v-model="form.memo"
              type="textarea"
              :rows="2"
              :placeholder="$t('pa.placeholder.notesPlaceholder')"
              maxlength="255"
            />
          </el-form-item>
        </el-form>
      </pro-grid>
      <pro-grid type="info" :title="$t('pa.filterConditionList')" class="filter-grid--list">
        <el-button slot="operation" type="primary" size="small" @click="addFilter">
          {{ $t('pa.params.template.detail.addFilter') }}
        </el-button>
        <div v-if="filterInfo.length === 0" class="no-data">{{ $t('pa.noData') }}</div>
        <el-collapse v-if="filterInfo.length" v-model="showCollapses">
          <el-collapse-item v-for="(item, index) in filterInfo" :key="item.rule + index" :name="index">
            <div slot="title" class="title-slot">
              <span class="title-slot-content">{{ $t('pa.params.template.detail.filterRule') }}{{ index + 1 }}</span>
              <el-button type="text" @click="delFilter(index)">{{ $t('pa.action.del') }}</el-button>
            </div>
            <div style="padding: 10px 40px">
              <el-alert
                v-if="routeError[index]"
                style="margin-bottom: 10px"
                :title="$t('pa.params.template.detail.errorTip')"
                type="error"
                show-icon
              >
                <div slot="title">
                  <p v-if="routeError[index].rule">
                    {{ routeError[index].rule[0] }}
                  </p>
                  <p v-for="msg in routeError[index].conditions || []" :key="msg">
                    {{ msg }}
                  </p>
                </div>
              </el-alert>
              <div class="add-condition">
                <el-button style="float: right" icon="el-icon-plus" type="text" @click="addCondition(item.conditions)">
                  {{ $t('pa.params.template.detail.addRule') }}
                </el-button>
              </div>
              <el-table class="title-slot-table" :data="item.conditions" style="width: 100%">
                <el-table-column prop="condition" :label="$t('pa.params.template.detail.rule')" :width="isEn ? 120 : 100">
                  <div slot-scope="scope">{{ $t('pa.params.template.detail.rule') }}{{ scope.$index + 1 }}</div>
                </el-table-column>
                <el-table-column prop="fieldName" :label="$t('pa.params.template.detail.fieldName')">
                  <div slot-scope="scope">
                    <el-input v-model="item.conditions[scope.$index].fieldName" style="width: 100%" />
                  </div>
                </el-table-column>
                <el-table-column prop="fieldType" :label="$t('pa.params.template.detail.fieldType')">
                  <div slot-scope="scope">
                    <el-select
                      v-model="item.conditions[scope.$index].fieldType"
                      style="width: 100%"
                      :placeholder="$t('pa.placeholder.select')"
                    >
                      <el-option v-for="fItem in fieldTypes" :key="fItem" :label="fItem" :value="fItem" />
                    </el-select>
                  </div>
                </el-table-column>
                <el-table-column prop="operator" :label="$t('pa.params.template.detail.operator')">
                  <div slot-scope="scope">
                    <el-select v-model="item.conditions[scope.$index].operator" style="width: 100%">
                      <el-option v-for="sItem in symbolLists" :key="sItem" :label="sItem" :value="sItem" />
                    </el-select>
                  </div>
                </el-table-column>
                <el-table-column prop="fieldValue" :label="$t('pa.params.template.detail.fieldValue')">
                  <div slot-scope="scope">
                    <el-input v-model="item.conditions[scope.$index].fieldValue" :placeholder="$t('pa.placeholder.input')" />
                  </div>
                </el-table-column>
                <el-table-column prop="action" :label="$t('pa.action.action')" width="100">
                  <div slot-scope="scope">
                    <el-button type="text" @click="delCondition(item.conditions, scope.$index)">
                      {{ $t('pa.action.del') }}
                    </el-button>
                  </div>
                </el-table-column>
              </el-table>
            </div>
          </el-collapse-item>
        </el-collapse>
      </pro-grid>
    </pro-grid>
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { Rule, Condition } from './index';
import { get, post } from '@/apis/utils/net';
interface RouteError {
  target?: string;
  conditions?: string[];
}
@Component({
  name: 'ElementFilterTemplateDetail'
})
export default class ElementFilterTemplateDetail extends Vue {
  id = '';
  orgId = this.$store.getters.orgId;
  userName = this.$store.getters.userName;
  form: any = {
    filterTemplateName: '',
    memo: ''
  };
  // 表单校验规则
  formRules = {
    filterTemplateName: [{ required: true, message: this.$t('pa.params.template.name'), trigger: 'change' }]
  };
  // 模板详情
  detail = null;
  // 操作符
  symbolLists = ['=', '<', '>', '>=', '<=', 'in'];
  // 字段类型
  fieldTypes = ['String', 'Integer', 'Float', 'Long', 'Double'];
  // 路由信息
  filterInfo: any = [];
  // 展开过滤条件
  showCollapses: any = [0];
  routeError: RouteError[] = [];
  get filterTemplateTitle() {
    return this.id ? this.$t('pa.params.template.editTemplate') : this.$t('pa.params.template.addTemplate');
  }
  async created() {
    this.id = this.$route.query.id as string;
    if (this.id) {
      this.getDetail(this.id);
    }
  }
  // 获取详情
  async getDetail(id) {
    const { data } = await get('/rs/pa/filter/findById', { id });
    this.detail = data;
    // 表单信息获取
    this.form = {
      filterTemplateName: data.filterTemplateName,
      memo: data.memo
    };
    // 获取路由信息
    this.filterInfo = JSON.parse(data.filterInfo);
  }
  // 校验信息
  validateRoute() {
    const errors: RouteError[] = [];
    const pushError = (index, key, msg) => {
      if (!errors[index]) {
        errors[index] = {};
      }
      if (Array.isArray(errors[index][key])) {
        errors[index][key].push(msg);
      } else if (errors[index][key]) {
        errors[index][key] = [errors[index][key], msg];
      } else {
        errors[index][key] = [msg];
      }
    };
    const checkTypeAndValue = ({ fieldType, fieldValue }) => {
      const type = fieldType.toLowerCase();
      if (type !== 'string') {
        return /\d*.?\d*/.test(fieldValue);
      }
      return true;
    };
    this.filterInfo.forEach((item: any, index) => {
      if (item.conditions.length === 0) {
        pushError(
          index,
          'rule',
          `${this.$t('pa.params.template.detail.filterRule')}${index + 1}${this.$t('pa.params.template.detail.lackRule')}`
        );
      } else if (item.conditions.length > 0) {
        item.conditions.forEach((rItem, rIndex) => {
          if (!rItem.fieldName || !rItem.operator || !rItem.fieldValue) {
            pushError(
              index,
              'conditions',
              `${this.$t('pa.params.template.detail.rule')}${rIndex + 1}${this.$t(
                'pa.params.template.detail.notFullyConfigured'
              )}`
            );
          } else if (!checkTypeAndValue(rItem)) {
            pushError(
              index,
              'conditions',
              `${this.$t('pa.params.template.detail.rule')}${rIndex + 1}${this.$t(
                'pa.params.template.detail.notMismatching'
              )}`
            );
          }
        });
      }
      // 展开规则
      if (errors[index]) {
        this.showCollapses.push(index);
      }
    });
    return errors;
  }
  // 保存过滤模板
  save() {
    (this.$refs['form'] as any).validate(async (valid) => {
      if (valid) {
        if (!this.filterInfo.length) {
          this.$message.error(this.$t('pa.params.template.detail.filterRuleRequired'));
          return;
        }
        this.routeError = this.validateRoute();
        if (this.routeError.length > 0) {
          return false;
        }
        this.filterInfo.forEach((item, index) => {
          item.rule = this.$t('pa.params.template.detail.filterRule') + (index + 1);
          item.conditions.forEach((rItem, rindex) => {
            rItem.condition = this.$t('pa.params.template.detail.rule') + (rindex + 1);
          });
        });
        const params = {
          id: this.id,
          orgName: this.userName,
          resType: 'Kafka',
          filterInfo: this.filterInfo,
          ...this.form
        };
        // 调用新建保存接口
        const url = this.id ? '/rs/pa/filter/update' : '/rs/pa/filter/add';
        const { data, msg, success } = await post(url, params);
        if (success) {
          this.$message({ message: msg, type: 'success' });
          if (url === '/rs/pa/filter/add') {
            const oldFullPath = this.$route.fullPath;
            this.$nextTick(() => {
              try {
                const tabsNavInstance = (this as any).$tabNav;
                tabsNavInstance.handleTabsDelete(oldFullPath);
                // 跳转至新的详情页
                this.$router.replace({
                  path: 'filterTemplateDetail',
                  query: {
                    id: data,
                    title: this.form.filterTemplateName
                  }
                });
              } catch (err) {}
            });
          }
        } else {
          this.$message({ message: msg, type: 'error' });
        }
      }
    });
  }
  // 添加过滤条件
  addFilter() {
    this.filterInfo.push(new Rule());
  }
  // 删除过滤条件
  delFilter(index: number) {
    this.filterInfo.splice(index, 1);
  }
  // 添加条件
  addCondition(item) {
    item.push(new Condition());
  }
  // 删除条件
  delCondition(item, index: number) {
    item.splice(index, 1);
  }
}
</script>

<style lang="scss" scoped>
.title-slot {
  margin-left: 20px;
  &-content {
    margin-right: 10px;
  }
}
.filter {
  height: calc(100vh - 114px);
}
.filter-grid {
  margin-top: 18px;
  height: calc(100vh - 198px);
  overflow: auto;
  &--list {
    margin-top: 18px;
    height: calc(100vh - 424px);
    background: #fff;
  }
  &--form {
    padding: 15px 0;
    width: 60%;
    .el-input,
    .el-select {
      width: 100%;
    }
  }
}
.filter-detail {
  height: calc(100vh - 107px);
  &-content {
    height: calc(100% - 50px);
    overflow: auto;
    padding: 20px;
    background: #fff;
    border-top: 1px solid #f1f1f1;
  }
  .title-slot {
    width: 100%;
    padding: 0 20px;
    display: flex;
    align-items: center;
    .title-slot-content {
      font-size: 14px;
      font-weight: bold;
      flex: 1;
    }
  }
  .title-slot-table {
    border: 1px solid #ebeef5;
    border-bottom: none;
  }
  .add-condition {
    margin-bottom: 10px;
    > span {
      padding-right: 10px;
      color: #606266;
    }
  }
}
.no-data {
  line-height: calc(100vh - 482px);
  text-align: center;
  font-size: 15px;
  color: #606266;
}
.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  &-title {
    flex: auto;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 700;
    font-size: 15px;
  }
}

.filter-header {
  height: 50px;
  background: #ffffff;
  border-left: none;
  border-right: none;
  padding: 0 20px;
  font-size: 16px;
  display: flex;
  align-items: center;
}
.filter-header-operate {
  flex: 1;
  text-align: right;
}
</style>
