<template>
  <pro-page :title="filterTemplateTitle" fixed-header class="filter">
    <div slot="operation">
      <el-button type="primary" size="small" @click="save">保存</el-button>
    </div>
    <pro-grid class="filter-grid" direction="column">
      <pro-grid type="info" title="基本配置">
        <el-form
          ref="form"
          class="filter-grid--form"
          :model="form"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="模板名称" prop="filterTemplateName">
            <el-input
              v-model="form.filterTemplateName"
              maxlength="100"
              placeholder="请输入模板名称"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="form.memo"
              type="textarea"
              :rows="2"
              style="width: 60%"
              placeholder="请输入备注"
              maxlength="1024"
            />
          </el-form-item>
        </el-form>
      </pro-grid>
      <pro-grid type="info" title="过滤条件列表" class="filter-grid--list">
        <el-button slot="operation" type="primary" size="small" @click="addFilter">
          添加过滤条件
        </el-button>
        <div v-if="filterInfo.length === 0" class="no-data">暂无数据</div>
        <el-collapse v-if="filterInfo.length" v-model="showCollapses">
          <el-collapse-item
            v-for="(item, index) in filterInfo"
            :key="item.rule + index"
            :name="index"
          >
            <div slot="title" class="title-slot">
              <span class="title-slot-content">规则{{ index + 1 }}</span>
              <el-button type="text" @click="delFilter(index)">删除</el-button>
            </div>
            <div style="padding: 10px 40px">
              <el-alert
                v-if="routeError[index]"
                style="margin-bottom: 10px"
                title="错误提示的文案"
                type="error"
                show-icon
              >
                <div slot="title">
                  <p v-if="routeError[index].rule">
                    {{ routeError[index].rule[0] }}
                  </p>
                  <p v-for="msg in routeError[index].conditions || []" :key="msg">
                    {{ msg }}
                  </p>
                </div>
              </el-alert>
              <div class="add-condition">
                <el-button
                  style="float: right"
                  icon="el-icon-plus"
                  type="text"
                  @click="addCondition(item.conditions)"
                >
                  添加条件
                </el-button>
              </div>
              <el-table class="title-slot-table" :data="item.conditions" style="width: 100%">
                <el-table-column prop="condition" label="条件" width="100">
                  <div slot-scope="scope">条件{{ scope.$index + 1 }}</div>
                </el-table-column>
                <el-table-column prop="fieldName" label="判断字段">
                  <div slot-scope="scope">
                    <el-input
                      v-model="item.conditions[scope.$index].fieldName"
                      style="width: 200px"
                    />
                  </div>
                </el-table-column>
                <el-table-column prop="fieldType" label="字段类型">
                  <div slot-scope="scope">
                    <el-select
                      v-model="item.conditions[scope.$index].fieldType"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="fItem in fieldTypes"
                        :key="fItem"
                        :label="fItem"
                        :value="fItem"
                      />
                    </el-select>
                  </div>
                </el-table-column>
                <el-table-column prop="operator" label="操作符">
                  <div slot-scope="scope">
                    <el-select v-model="item.conditions[scope.$index].operator">
                      <el-option
                        v-for="sItem in symbolLists"
                        :key="sItem"
                        :label="sItem"
                        :value="sItem"
                      />
                    </el-select>
                  </div>
                </el-table-column>
                <el-table-column prop="fieldValue" label="值">
                  <div slot-scope="scope">
                    <el-input
                      v-model="item.conditions[scope.$index].fieldValue"
                      placeholder="请输入"
                    />
                  </div>
                </el-table-column>
                <el-table-column prop="action" label="操作" width="100">
                  <div slot-scope="scope">
                    <el-button type="text" @click="delCondition(item.conditions, scope.$index)">
                      删除
                    </el-button>
                  </div>
                </el-table-column>
              </el-table>
            </div>
          </el-collapse-item>
        </el-collapse>
      </pro-grid>
    </pro-grid>
  </pro-page>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { Rule, Condition } from './index';
import { get, post } from '@/apis/utils/net';
import { vm } from '@/main';
interface RouteError {
  target?: string;
  conditions?: string[];
}
@Component({
  name: 'ElementFilterTemplateDetail'
})
export default class ElementFilterTemplateDetail extends PaBase {
  id = '';
  orgId = this.$store.state.userInfo.orgId;
  userName = this.$store.state.userInfo.userName;
  form: any = {
    filterTemplateName: '',
    memo: ''
  };
  // 表单校验规则
  formRules = {
    filterTemplateName: [{ required: true, message: '请输入模板名称', trigger: 'change' }]
  };
  // 模板详情
  detail = null;
  // 操作符
  symbolLists = ['=', '<', '>', '>=', '<=', 'in'];
  // 字段类型
  fieldTypes = ['String', 'Integer', 'Float', 'Long', 'Double'];
  // 路由信息
  filterInfo: any = [];
  // 展开过滤条件
  showCollapses: any = [0];
  routeError: RouteError[] = [];
  get filterTemplateTitle() {
    return this.id ? '编辑模板' : '新建模板';
  }
  async created() {
    this.id = this.$route.query.id as string;
    if (this.id) {
      this.getDetail(this.id);
    }
  }
  // 获取详情
  async getDetail(id) {
    const { data } = await get('/rs/pa/filter/findById', { id });
    this.detail = data;
    // 表单信息获取
    this.form = {
      filterTemplateName: data.filterTemplateName,
      memo: data.memo
    };
    // 获取路由信息
    this.filterInfo = JSON.parse(data.filterInfo);
  }
  // 校验信息
  validateRoute() {
    const errors: RouteError[] = [];
    const pushError = (index, key, msg) => {
      if (!errors[index]) {
        errors[index] = {};
      }
      if (Array.isArray(errors[index][key])) {
        errors[index][key].push(msg);
      } else if (errors[index][key]) {
        errors[index][key] = [errors[index][key], msg];
      } else {
        errors[index][key] = [msg];
      }
    };
    const checkTypeAndValue = ({ fieldType, fieldValue }) => {
      const type = fieldType.toLowerCase();
      if (type !== 'string') {
        return /\d*.?\d*/.test(fieldValue);
      }
      return true;
    };
    this.filterInfo.forEach((item: any, index) => {
      if (item.conditions.length === 0) {
        pushError(index, 'rule', `规则${index + 1}未配置条件`);
      } else if (item.conditions.length > 0) {
        item.conditions.forEach((rItem, rIndex) => {
          if (!rItem.fieldName || !rItem.operator || !rItem.fieldValue) {
            pushError(index, 'conditions', `条件${rIndex + 1}未配置完整`);
          } else if (!checkTypeAndValue(rItem)) {
            pushError(index, 'conditions', `条件${rIndex + 1}字段类型与字段值不匹配`);
          }
        });
      }
      // 展开规则
      if (errors[index]) {
        this.showCollapses.push(index);
      }
    });
    return errors;
  }
  // 保存过滤模板
  save() {
    (this.$refs['form'] as any).validate(async (valid) => {
      if (valid) {
        if (!this.filterInfo.length) {
          this.$message.error('过滤条件不能为空');
          return;
        }
        this.routeError = this.validateRoute();
        if (this.routeError.length > 0) {
          return false;
        }
        this.filterInfo.forEach((item, index) => {
          item.rule = '规则' + (index + 1);
          item.conditions.forEach((rItem, rindex) => {
            rItem.condition = '条件' + (rindex + 1);
          });
        });
        const params = {
          id: this.id,
          orgName: this.userName,
          resType: 'Kafka',
          filterInfo: this.filterInfo,
          ...this.form
        };
        // 调用新建保存接口
        const url = this.id ? '/rs/pa/filter/update' : '/rs/pa/filter/add';
        const { data, msg, success } = await post(url, params);
        if (success) {
          this.$message({ message: msg, type: 'success' });
          if (url === '/rs/pa/filter/add') {
            const oldFullPath = this.$route.fullPath;
            this.$nextTick(() => {
              try {
                const tabsNavInstance = vm.$children[0].$refs['tabsNav'];
                tabsNavInstance.handleTabsDelete(oldFullPath);
                // 跳转至新的详情页
                this.$router.replace({
                  path: 'filterTemplateDetail',
                  query: {
                    id: data,
                    title: this.form.filterTemplateName
                  }
                });
              } catch (err) {}
            });
          }
        } else {
          this.$message({ message: msg, type: 'error' });
        }
      }
    });
  }
  // 添加过滤条件
  addFilter() {
    this.filterInfo.push(new Rule());
  }
  // 删除过滤条件
  delFilter(index: number) {
    this.filterInfo.splice(index, 1);
  }
  // 添加条件
  addCondition(item) {
    item.push(new Condition());
  }
  // 删除条件
  delCondition(item, index: number) {
    item.splice(index, 1);
  }
}
</script>

<style lang="scss" scoped>
.title-slot {
  margin-left: 20px;
  &-content {
    margin-right: 10px;
  }
}
.filter {
  height: calc(100vh - 114px);
}
.filter-grid {
  margin-top: 18px;
  &--list {
    height: 400px;
    margin-top: 20px;
  }
  &--form {
    padding: 15px 0;
  }
}
.filter-detail {
  height: calc(100vh - 107px);
  &-content {
    height: calc(100% - 50px);
    overflow: auto;
    padding: 20px;
    background: #fff;
    border-top: 1px solid #f1f1f1;
  }
  .title-slot {
    width: 100%;
    padding: 0 20px;
    display: flex;
    align-items: center;
    .title-slot-content {
      font-size: 14px;
      font-weight: bold;
      flex: 1;
    }
  }
  .title-slot-table {
    border: 1px solid #ebeef5;
    border-bottom: none;
  }
  .add-condition {
    > span {
      padding-right: 10px;
      color: #606266;
    }
    margin-bottom: 10px;
  }
}
.no-data {
  line-height: 400px;
  height: 400px;
  text-align: center;
  font-size: 15px;
  color: #606266;
}
.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  &-title {
    flex: auto;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 700;
    font-size: 15px;
  }
}

.filter-header {
  height: 50px;
  background: #ffffff;
  border-left: none;
  border-right: none;
  padding: 0 20px;
  font-size: 16px;
  display: flex;
  align-items: center;
}
.filter-header-operate {
  flex: 1;
  text-align: right;
}
</style>
