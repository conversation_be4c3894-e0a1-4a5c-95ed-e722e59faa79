<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">JobManager信息</div>
    </div>
    <div class="tab-content">
      <div style="padding: 0 20px">
        <el-row :gutter="20">
          <el-col :span="6">
            <span class="label">Jobmanager 堆内存:</span>
            <span class="my-wrap-tag"> {{ getValue('jobmanager.heap.size') || '无' }}</span>
          </el-col>
          <el-col :span="6">
            <span>默认并行度:</span>
            <span class="my-wrap-tag"> {{ getValue('parallelism.default') || '无' }}</span>
          </el-col>
          <el-col :span="6">
            <span>状态的后端存储:</span>
            <span class="my-wrap-tag"> {{ getValue('state.backend') || '无' }}</span>
          </el-col>
          <el-col :span="6">
            <span>Taskmanager 堆内存:</span>
            <span class="my-wrap-tag"> {{ getValue('taskmanager.heap.size') || '无' }}</span>
          </el-col>
          <el-col :span="6">
            <span>Taskmanager slot数量:</span>
            <span class="my-wrap-tag">
              {{ getValue('taskmanager.numberOfTaskSlots') || '无' }}
            </span>
          </el-col>
        </el-row>
      </div>
      <el-row style="padding: 0px 10px">
        <el-form :inline="true" />
      </el-row>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject } from 'vue-property-decorator';
import { URL_RES_DETAIL_FINK_JOBMANAGERCONF } from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {}
})
export default class FlinkJobManagerInfo extends PaBase {
  height = '300px';
  resRecord: any = {};
  jobmanagerConf: any = [];
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  created() {
    this.loadData(this.comDetailRecord.val || {}, this.comParams.FlinkJobManagerConf || {});
  }
  getJobManagerConf() {
    this.doGet(URL_RES_DETAIL_FINK_JOBMANAGERCONF, {
      params: { id: this.$route.query.id }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.jobmanagerConf = resp.data;
      });
    });
  }

  getValue(key: string) {
    const rec: any = _.find(this.jobmanagerConf, { key: key });
    if (rec) {
      return rec.value;
    }
  }

  async loadData(data: any, params: any) {
    this.resRecord = data;
    this.height = params.height;
    this.getJobManagerConf();
  }
}
</script>
<style scoped>
.my-wrap-row {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
}
.label {
  margin-right: 10px;
  color: #777;
}
.my-wrap-tag {
  padding: 0 10px;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
  color: #444;
  line-height: 40px;
  height: 32px;
}
</style>
