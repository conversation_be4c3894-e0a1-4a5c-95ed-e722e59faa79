<template>
  <bs-dialog v-loading="loading" :title="title" :width="width" :append-to-body="true" :visible.sync="display">
    <!-- header -->
    <header-item
      ref="headerRef"
      :cpu="cpu"
      :queue="queue"
      :slosts="slosts"
      :memory="memory"
      :is-per-job="isPerJob"
      :table-data="tableData"
    />
    <!-- bar -->
    <bar-item v-model="search" :is-per-job="isPerJob" :table-data="tableData" @search="handleSearch" />
    <!-- table -->
    <bs-table :data="tableData" size="mini" height="350" :column-settings="false" :column-data="columnData">
      <!-- afterMemory -->
      <template slot="afterMemory" slot-scope="{ row }">
        <el-input-number
          v-model="row.afterMemory"
          :min="1"
          :precision="0"
          :placeholder="$t('pa.placeholder.afterMemory')"
          controls-position="right"
        />
      </template>
      <!-- afterCpu -->
      <template slot="afterCpu" slot-scope="{ row }">
        <el-input-number
          v-model="row.afterCpu"
          :min="1"
          :precision="0"
          :placeholder="$t('pa.placeholder.afterCpu')"
          controls-position="right"
        />
      </template>
      <!-- afterSlots -->
      <template slot="afterSlots" slot-scope="{ row }">
        <el-input-number
          v-model="row.afterSlots"
          :min="1"
          :precision="0"
          controls-position="right"
          :placeholder="$t('pa.placeholder.afterSlots')"
        />
      </template>
    </bs-table>
    <!-- footer -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="display = false">{{ $t('pa.action.close') }}</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">{{ $t('pa.action.save') }}</el-button>
    </span>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, PropSync, Prop, Ref, Vue } from 'vue-property-decorator';
import { getUseInfo, allocatedRes } from '@/apis/serviceApi';
import HeaderItem from './header-item.vue';
import { includesPro } from '@/utils';

@Component({ components: { HeaderItem, BarItem: () => import('./bar-item.vue') } })
export default class AllocateAssetDialog extends Vue {
  @PropSync('show', { type: Boolean }) display!: boolean;
  @Prop({ default: '' }) id!: string;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: '' }) queue!: string;
  @Prop({ default: () => [] }) orgList!: any[];
  @Prop({ default: false }) isEdit!: boolean;
  @Prop({ default: false }) isPerJob!: boolean;
  @Ref('headerRef') readonly header!: HeaderItem;

  finalSlosts = 0;
  finalMemory = 0;
  finalCpu = 0;
  unifiedSlots = 0;
  unifiedMemory = 0;
  unifiedCpu = 0;

  loading = false;
  search = '';
  slosts = 0;
  memory = 0;
  cpu = 0;
  tableData: any[] = [];

  get columnData() {
    return [
      {
        label: this.$t('pa.orgId'),
        value: 'orgId',
        fixed: true
      },
      {
        label: this.$t('pa.orgName'),
        value: 'orgName',
        fixed: true,
        minWidth: 100
      },
      this.isPerJobAdd && {
        label: this.$t('pa.totalMemoryMB'),
        value: 'parentMemory',
        width: 110
      },
      this.isPerJobAdd && {
        label: this.$t('pa.remainMemoryMB'),
        value: 'residualMemory',
        width: 120
      },
      this.isPerJobAdd && {
        label: this.$t('pa.totalCpu'),
        value: 'parentCpu',
        width: 100
      },
      this.isPerJobAdd && {
        label: this.$t('pa.remainCpu'),
        value: 'residualCpu',
        width: 100
      },
      this.isSlotAdd && {
        label: this.$t('pa.totalSlots'),
        value: 'parentSlots',
        width: 100
      },
      this.isSlotAdd && {
        label: this.$t('pa.remainSlot'),
        value: 'residualSlots',
        width: 100
      },
      this.isPerJob && {
        label: this.$t('pa.adjustMemoryMB'),
        value: 'afterMemory',
        fixed: 'right',
        minWidth: 140,
        showTooltip: false
      },
      this.isPerJob && {
        label: this.$t('pa.adjustCpu'),
        value: 'afterCpu',
        fixed: 'right',
        minWidth: 140,
        showTooltip: false
      },
      !this.isPerJob && {
        label: this.$t('pa.adjustSlots'),
        value: 'afterSlots',
        fixed: 'right',
        minWidth: 140,
        showTooltip: false
      }
    ].filter(Boolean);
  }
  get title() {
    return this.isEdit ? this.$t('pa.action.batchAdjust') : this.$t('pa.action.resourceAllocate');
  }
  get width() {
    return this.isPerJob ? '1200px' : '835px';
  }
  get isPerJobAdd() {
    return this.isPerJob && !this.isEdit; // ???
  }
  get isSlotAdd() {
    return !this.isPerJob && !this.isEdit; // ???
  }

  created() {
    this.handleSearch();
    this.getUseData();
  }

  async getUseData() {
    try {
      this.loading = true;
      const { success, data, error } = await getUseInfo(this.id, this.orgId, this.queue);
      if (!success) return this.$message.error(error);
      this.slosts = data?.residualSlots || 0;
      this.memory = data?.residualMemory || 0;
      this.cpu = data?.residualCpu || 0;
    } finally {
      this.loading = false;
    }
  }
  handleSearch() {
    this.tableData = this.orgList.filter(({ orgName }) => includesPro(orgName, this.search));
  }
  async handleSubmit() {
    const emptyMsg = await this.notEmptyValidate();
    if (emptyMsg) return this.$message.error(emptyMsg);
    const restMsg = await this.header.validate();
    if (restMsg) return this.$message.warning(restMsg);
    const data = this.tableData.map((it) => {
      return {
        allocationCpu: it.afterCpu,
        allocationMemory: it.afterMemory,
        allocationSlots: it.afterSlots,
        childrenOrgId: it.orgId,
        orgName: it.orgName,
        queue: it.queue
      };
    });
    const { success, msg, error } = await allocatedRes(this.id, data, this.orgId);
    if (!success) return this.$message.error(error);
    this.$message.success(msg);
    this.display = false;
    this.$emit('refresh');
  }
  notEmptyValidate() {
    const keys = this.isPerJob ? ['afterMemory', 'afterCpu'] : ['afterSlots'];
    const msg = this.isPerJob ? this.$t('pa.memoryCpuSetting') : this.$t('pa.slotsSetting');
    for (const i of this.tableData) {
      const list = keys.map((it) => i[it]);
      const flag = list.every(Boolean);
      if (!flag) return this.$t('pa.tip.orgConfig', [msg]);
    }
    return '';
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-input-number {
  width: 100%;
}
</style>
