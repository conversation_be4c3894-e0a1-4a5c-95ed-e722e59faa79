<template>
  <el-form
    ref="formRef"
    :rules="rules"
    :model="formData"
    label-width="95px"
    :disabled="disabled"
    class="index-form__container"
  >
    <!-- 计算引擎 -->
    <el-form-item :label="$t('pa.flow.key10')" prop="computationalEngine">
      <el-select
        v-model="formData.computationalEngine"
        filterable
        :placeholder="$t('pa.flow.placeholder21')"
        @change="handleComputationalEngineChange"
      >
        <el-option v-for="el in engineOptions" :key="el.id" :label="el.title" :value="el.title" />
      </el-select>
    </el-form-item>
    <!-- 服务地址 -->
    <el-form-item :label="$t('pa.flow.serveAddress')" prop="serviceAddress">
      <el-input v-model="formData.serviceAddress" disabled readonly />
    </el-form-item>
    <!-- 策略 -->
    <el-form-item :label="$t('pa.flow.key11')" prop="pushOrQueryKey">
      <el-select v-model="formData.pushOrQueryKey" filterable :placeholder="$t('pa.flow.placeholder21')">
        <el-option v-for="el in wayOptions" :key="el.value" :label="el.label" :value="el.value" />
      </el-select>
    </el-form-item>
    <!-- 查询配置 -->
    <el-divider v-if="hasQueryLogic">{{ $t('pa.flow.searchConfig') }}</el-divider>
    <!-- 命名空间 -->
    <el-form-item v-if="hasQueryLogic" :label="$t('pa.flow.nameSpace')" prop="namespace">
      <el-select
        v-model="formData.namespace"
        filterable
        :placeholder="$t('pa.flow.placeholder21')"
        @change="handleNamespaceChange"
      >
        <el-option v-for="item in namespaceOptions" :key="item" :label="item" :value="item" />
      </el-select>
    </el-form-item>
    <!-- 维度 -->
    <el-form-item v-if="hasQueryLogic" :label="$t('pa.flow.level')" prop="dimensionality">
      <el-select
        v-model="formData.dimensionality"
        filterable
        :placeholder="$t('pa.flow.placeholder21')"
        @change="handleDimensionalityChange"
      >
        <el-option v-for="item in dimensionalityOptions" :key="item" :label="item" :value="item" />
      </el-select>
    </el-form-item>
    <!-- 维度值 -->
    <el-form-item v-if="hasQueryLogic" :label="$t('pa.flow.key12')" prop="dimensionalValue" class="index-form-flex">
      <el-select v-model="formData.dimensionalValue" :placeholder="$t('pa.flow.placeholder21')" filterable>
        <el-option v-for="el in fieldList" :key="el.value" :label="el.label" :value="el.value" />
      </el-select>
      <el-tooltip :content="$t('pa.flow.msg110')" effect="light" placement="top">
        <i class="iconfont icon-wenhao index-form-icon"></i>
      </el-tooltip>
    </el-form-item>
    <!-- 截止时间 -->
    <el-form-item v-if="hasQueryLogic" :label="$t('pa.flow.deadline')" prop="deadline" class="index-form-flex">
      <el-select v-model="formData.deadline" :placeholder="$t('pa.flow.placeholder21')" filterable clearable>
        <el-option v-for="el in fieldList" :key="el.value" :label="el.label" :value="el.value" />
      </el-select>
      <el-tooltip :content="$t('pa.flow.msg111')" effect="light" placement="top">
        <i class="iconfont icon-wenhao index-form-icon"></i>
      </el-tooltip>
    </el-form-item>
    <!-- 时间前滚 -->
    <el-form-item
      v-if="hasQueryLogic"
      :label="$t('pa.flow.timeToRollForward')"
      prop="timeToRollForward"
      class="index-form-flex"
    >
      <el-input-number v-model="formData.timeToRollForward" number :min="1" clearable controls-position="right" />
      <el-select v-model="formData.unit" filterable class="index-form-unit">
        <el-option v-for="el in unitOptions" :key="el" :label="el" :value="el" />
      </el-select>
    </el-form-item>
    <!-- 指标配置 -->
    <el-form-item
      v-if="hasQueryLogic"
      :label="$t('pa.flow.logicConfig1')"
      class="index-form-flex"
      prop="indicatorConfiguration"
    >
      <el-input v-model="indicator" disabled readonly :placeholder="$t('pa.flow.msg112')" />
      <el-button type="text" size="mini" class="index-form-button" @click="$emit('config')">
        {{ $t('pa.flow.config1') }}
      </el-button>
    </el-form-item>
    <el-divider v-if="hasPushLogic">{{ $t('pa.flow.pushLogic') }}</el-divider>
    <!-- 推送字段 -->
    <el-form-item v-if="hasPushLogic" :label="$t('pa.flow.key13')" prop="pushFields" class="index-form-flex">
      <el-select v-model="formData.pushFields" :placeholder="$t('pa.flow.placeholder23')" filterable>
        <el-option v-for="el in fieldList" :key="el.value" :label="el.label" :value="el.value" />
      </el-select>
      <el-tooltip :content="$t('pa.flow.msg113')" effect="light" placement="top">
        <i class="iconfont icon-wenhao index-form-icon"></i>
      </el-tooltip>
    </el-form-item>
    <!-- 脚本列表 -->
    <el-form-item v-if="hasPushLogic" :label="$t('pa.flow.scriptList')" prop="scriptList" class="index-form-flex">
      <el-select v-model="formData.scriptList" :placeholder="$t('pa.flow.placeholder21')" multiple filterable clearable>
        <el-option v-for="item in scriptOptions" :key="item" :label="item" :value="item" />
      </el-select>
      <el-tooltip :content="$t('pa.flow.msg114')" effect="light" placement="top">
        <i class="iconfont icon-wenhao index-form-icon"></i>
      </el-tooltip>
    </el-form-item>
    <el-divider>{{ $t('pa.flow.commonConfig') }}</el-divider>
    <!-- 批处理数量 -->
    <el-form-item :label="$t('pa.flow.batchSize')" prop="batchSize">
      <el-input-number v-model="formData.batchSize" number :min="1" clearable controls-position="right" />
    </el-form-item>
    <!-- 超时时间 -->
    <el-form-item :label="$t('pa.flow.timeout')" prop="timeout" class="index-form-flex">
      <el-input-number v-model="formData.timeout" number :min="1" clearable controls-position="right" />
      <span class="el-tooltip">{{ $t('pa.flow.s') }}</span>
    </el-form-item>
    <!-- keyBy字段 -->
    <el-form-item :label="$t('pa.flow.keyBy')" prop="keyByFields">
      <el-select v-model="formData.keyByFields" multiple clearable filterable :placeholder="$t('pa.flow.placeholder24')">
        <el-option v-for="el in fieldList" :key="el.value" :label="el.label" :value="el.value" />
      </el-select>
    </el-form-item>
    <!-- 结果字段 -->
    <el-form-item :label="$t('pa.flow.resultFields')" prop="resultFields">
      <el-input v-model="formData.resultFields" :placeholder="$t('pa.flow.placeholder25')" :maxlength="1000" />
    </el-form-item>
  </el-form>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { FormData } from './type';
import { getEngineList } from '@/apis/portalApi';
import { getSubscribedData } from '@/apis/streamCubeApi';
import { WAY_OPTIONDS, PUSH_ARRAY, QUERY_ARRAY, extraction, getScriptOptions } from './utils';
@Component
export default class BatchProcess extends Vue {
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => [] }) fieldList!: any[];
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: '' }) indicator!: string;
  @Prop({ default: () => ({}) }) formData!: FormData;

  scripts: any = {}; // 流立方脚本
  engineOptions: any = []; //计算引擎
  namespaceOptions: any[] = [];
  dimensionalityOptions: any[] = [];
  scriptOptions: any[] = [];
  unitOptions: string[] = [
    this.$t('pa.flow.s'),
    this.$t('pa.flow.m'),
    this.$t('pa.flow.h'),
    this.$t('pa.flow.d'),
    this.$t('pa.flow.mm'),
    this.$t('pa.flow.y')
  ];
  wayOptions: any[] = WAY_OPTIONDS;

  get hasPushLogic() {
    return !this.formData.pushOrQueryKey ? true : PUSH_ARRAY.includes(this.formData.pushOrQueryKey);
  }
  get hasQueryLogic() {
    return !this.formData.pushOrQueryKey ? true : QUERY_ARRAY.includes(this.formData.pushOrQueryKey);
  }
  get rules() {
    return {
      computationalEngine: {
        required: true,
        message: this.$t('pa.flow.msg103'),
        trigger: 'change'
      },
      serviceAddress: {
        required: true,
        message: this.$t('pa.flow.msg115'),
        trigger: 'change'
      },
      pushOrQueryKey: {
        required: true,
        message: this.$t('pa.flow.msg116'),
        trigger: 'change'
      },
      namespace: {
        required: this.hasQueryLogic,
        message: this.$t('pa.flow.msg104'),
        trigger: 'change'
      },
      dimensionality: {
        required: this.hasQueryLogic,
        message: this.$t('pa.flow.msg105'),
        trigger: 'change'
      },
      dimensionalValue: {
        required: this.hasQueryLogic,
        message: this.$t('pa.flow.msg117'),
        trigger: 'change'
      },
      indicatorConfiguration: {
        required: this.hasQueryLogic,
        message: this.$t('pa.flow.msg118'),
        trigger: 'change'
      },
      batchSize: {
        required: true,
        message: this.$t('pa.flow.msg119'),
        trigger: 'change'
      },
      timeout: {
        required: true,
        message: this.$t('pa.flow.msg120'),
        trigger: 'blur'
      },
      pushFields: {
        required: this.hasPushLogic,
        message: this.$t('pa.flow.placeholder23'),
        trigger: 'change'
      },
      resultFields: {
        required: true,
        validator: (rule, value, callback) => {
          if (!rule.required) return callback();
          if (!value) return callback(new Error(this.$t('pa.flow.msg121')));
          if (extraction(value, this.fieldList)) return callback(new Error(this.$t('pa.flow.msg122')));
          callback();
        },
        trigger: 'blur'
      }
    };
  }
  async created() {
    await this.getEngineOptions();
    await this.getNamespaceOptions();
  }

  // 获取主机列表
  async getEngineOptions() {
    const { success, msg, data } = await getEngineList(this.orgId);
    if (!success) return this.$message.error(msg);
    this.engineOptions = Array.isArray(data) ? [...data] : [];
  }
  async getNamespaceOptions() {
    if (!this.formData.serviceAddress) return;
    const { success, msg, data } = await getSubscribedData(this.formData.serviceAddress);
    if (!success) return this.$message.error(msg);
    const { scripts } = JSON.parse(data);
    this.scripts = scripts;
    this.scriptOptions = getScriptOptions(this.scripts);
    this.namespaceOptions = Object.keys(this.scripts);
    if (!this.formData.namespace) return;
    this.dimensionalityOptions = Object.keys(this.scripts[this.formData.namespace]);
  }
  async handleComputationalEngineChange(engine) {
    const target = this.engineOptions.find(({ title }) => title === engine);
    this.formData.serviceAddress = target?.address || '';
    this.formData.serviceId = target?.id || '';
    this.formData.namespace = '';
    this.formData.dimensionality = '';
    if (target?.address) {
      const { success, msg, data } = await getSubscribedData(target.address);
      if (!success) return this.$message.error(msg);
      this.resetScripts(JSON.parse(data).scripts);
    } else {
      this.resetScripts();
    }
    this.$emit('empty');
  }
  resetScripts(data: any = {}) {
    this.scripts = data;
    this.scriptOptions = getScriptOptions(this.scripts);
    this.namespaceOptions = Object.keys(this.scripts);
    this.dimensionalityOptions = [];
  }
  handleNamespaceChange(namespace: string) {
    this.formData.dimensionality = '';
    this.$emit('empty');
    this.dimensionalityOptions = Object.keys(this.scripts[namespace]);
  }
  handleDimensionalityChange() {
    this.$emit('empty');
  }
  public validate() {
    return (this.$refs['formRef'] as any).validate();
  }
}
</script>
<style lang="scss" scoped>
.index-form {
  &__container {
    padding-right: 20px;
    ::v-deep .el-select,
    .el-input-number {
      width: 100%;
    }
  }
  &-flex {
    ::v-deep .el-form-item__content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    ::v-deep .el-tooltip {
      margin-left: 8px;
    }
  }
  &-unit {
    margin-left: 12px;
    width: 72px !important;
  }
  &-button {
    margin-left: 8px;
    padding-top: unset !important;
    padding-bottom: unset !important;
  }
  &-icon {
    cursor: pointer;
  }
}
</style>
