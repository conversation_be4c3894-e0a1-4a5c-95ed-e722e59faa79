<template>
  <bs-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    width="40%"
  >
    <el-form
      ref="ruleForm"
      class="flow-form"
      :model="formData"
      :rules="rules"
      :label-width="formLabelWidth"
      @submit.native.prevent
    >
      <el-form-item label="名称" prop="jobName">
        <el-input
          v-model="formData.jobName"
          autocomplete="off"
          maxlength="200"
          show-word-limit
          clearable
          placeholder="请输入流程名称，长度不超过200字符"
        />
      </el-form-item>
      <el-form-item label="类型" prop="jobType">
        <el-select
          v-model="formData.jobType"
          filterable
          clearable
          placeholder="请选择流程类型"
          :disabled="!enableSql"
          :popper-append-to-body="false"
        >
          <el-option
            v-for="el in jobTypeList"
            :key="el.value"
            :label="el.label"
            :value="el.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="memo">
        <el-input
          v-model="formData.memo"
          type="textarea"
          clearable
          rows="5"
          placeholder="请输入流程备注"
          autocomplete="off"
          maxlength="255"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取消</el-button>
      <el-button :loading="confirmButtonLoading" type="primary" @click="submit">确定</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue, Inject } from 'vue-property-decorator';
import { addFlow } from '@/apis/flowNewApi';

@Component
export default class AddAndEditFlow extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() projectId!: string;
  @Inject('enableSql') enableSql;
  orgId = '';
  title = '新建流程';
  formData: any = { jobName: '', jobType: 'PROCESSFLOW', memo: '' };
  confirmButtonLoading = false;
  rules: any = {
    jobName: [{ required: true, message: '请输入流程名称', trigger: 'blur' }],
    jobType: [{ required: true, message: '请选择流程类型', trigger: 'change' }]
  };
  formLabelWidth = '80px';
  projectList: any = [];
  jobTypeList: any[] = [
    {
      label: 'DataStream',
      value: 'PROCESSFLOW'
    },
    {
      label: 'SQL',
      value: 'FLINK_SQL'
    }
  ];

  closeDialog() {
    this.$emit('update:visible', false);
  }

  submit() {
    const form: any = this.$refs.ruleForm;
    form.validate(async (valid: any) => {
      if (valid) {
        this.addFlow();
      } else {
        return false;
      }
    });
  }

  async addFlow() {
    this.confirmButtonLoading = true;
    this.formData.projectId = this.projectId;
    const { success, msg } = await addFlow(this.formData);
    this.confirmButtonLoading = false;
    this.handleResponse(success, msg);
  }

  // 处理请求后的回调
  handleResponse(success, msg) {
    if (success) {
      this.closeDialog();
      this.$emit('close'); // 更新项目列表
      this.$message.success(msg);
      return;
    }
    this.$message.error(msg);
  }
}
</script>
<style lang="scss" scoped>
.flow-form {
  .el-select {
    width: 100%;
    text-align: left;
  }
}
</style>
