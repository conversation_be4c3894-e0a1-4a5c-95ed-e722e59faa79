<!--
 * @Description:
 * @Autor: magicyang
 * @Date: 2020-02-26 17:47:41
 * @LastEditors: ranran
 * @LastEditTime: 2020-07-10 14:40:38
 -->
<template>
  <bs-dialog
    id="filter-dialog"
    :title="data.nodeName + '组件配置'"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    width="80%"
    append-to-body
  >
    <div style="margin-bottom: 50px">
      <div><b>表达式信息:</b>{{ exprResult }}</div>
      <div v-if="!disabled" style="width: 100%">
        <el-form :inline="true" style="height: 40px">
          <el-form-item label="操作:">
            <el-switch
              v-model="mode"
              active-text="配置"
              inactive-text="选择"
              style="margin: 0px 10px"
            />
            <el-button @click="addConditon">添加条件</el-button>
            <el-button :disabled="mode" @click="deleteNode">删除条件</el-button>
            <el-button @click="clearAll">清空</el-button>
          </el-form-item>
          <el-form-item label="逻辑:">
            <el-button :disabled="mode" @click="addSymbol('and', '且')">且</el-button>
            <el-button :disabled="mode" @click="addSymbol('or', '或')">或</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div
        id="svg-div"
        v-loading="graphLoading"
        style="width: 100%; height: 370px; border: 1px dashed #000"
      >
        <svg id="filter-svg-canvas" style="width: 100%; height: 100%" />
      </div>
      <node-filter-cond
        :item="clickNode"
        :input="input"
        :visible="condCfgVisible"
        :job-data="jobData"
        @close="closeCondDialog"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <div style="display: flex; justify-content: space-between">
        <div style="width: 33%">
          <el-switch
            v-model="printLog"
            style="display: block; float: left"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="开启日志"
            inactive-text="关闭日志"
            :disabled="disabled"
          />
        </div>
        <div style="width: 33%">
          <el-button @click="closeDialog(false)">取消</el-button>
          <el-button v-if="!disabled" type="primary" @click="submit('ruleForm')">确定</el-button>
        </div>
      </div>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import * as _ from 'lodash';
import * as d3 from 'd3';
import dagreD3 from 'dagre-d3';

@Component({
  components: {
    'node-filter-cond': () => import('./node-filter-cond.vue')
  }
})
export default class NodeFilter extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: false }) disabled!: boolean;
  g: any;
  condNodes: any = [];
  selectNodes: any = [];
  mode = true;
  exprResult = '';
  condCfgVisible = false;
  clickNode: any = {};
  input: any = [];
  inputFields: any = [];
  graphLoading = false;
  printLog = false;

  submit() {
    // 不是结束节点
    const notEnd: any = [];
    this.g.nodes().forEach((n) => {
      const index = _.findIndex(this.g.edges(), { w: n });
      if (index < 0) {
        notEnd.push(n);
      }
    });
    if (notEnd.length > 1) {
      this.$tip.error('有条件未设置逻辑');
      return;
    }
    if (this.exprResult === '' || this.exprResult.indexOf('未配置') >= 0) {
      this.$tip.error('有条件未配置');
      return;
    }
    const nodeDto = _.cloneDeep(this.data);
    nodeDto.printLog = this.printLog;
    nodeDto.outputFields = [];
    this.inputFields.forEach((n) => {
      nodeDto.outputFields.push({
        name: n.name,
        type: n.type,
        outputable: true
      });
    });
    nodeDto.inputFields = this.inputFields;
    nodeDto.properties = {
      condNodes: this.condNodes,
      edges: this.g.edges()
    };
    // emit更新dag
    this.closeDialog(true, nodeDto);
  }

  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    this.condNodes = [];
    this.selectNodes = [];
    this.mode = true;
    this.exprResult = '';
    this.condCfgVisible = false;
    this.clickNode = {};
    this.input = [];
    this.graphLoading = false;
    return { needUpdate, jobNode };
  }

  private initGraph() {
    this.g = new dagreD3.graphlib.Graph({ compound: true })
      .setGraph({
        rankdir: 'LR'
      })
      .setDefaultEdgeLabel(function () {
        return {};
      });
  }

  closeCondDialog(cond: any) {
    this.condCfgVisible = false;
    if (cond && cond.id) {
      const node: any = _.find(this.condNodes, { id: cond.id });
      if (!node.expr) {
        node.expr = '';
      }
      node.expr = cond.method + '(';
      for (const i of Object.keys(cond.params)) {
        if (_.toNumber(i) > 0) {
          node.expr = node.expr + ',';
        }
        node.expr = node.expr + cond.params[i].value;
      }
      this.$set(node, 'method', cond.method);
      this.$set(node, 'params', cond.params);
      node.expr = node.expr + ')';
      node.html = '<span style="font-size:14px">' + node.expr + '</span>';
      this.draw();
    }
  }
  openModal(item: any) {
    if (this.mode && !this.disabled) {
      this.condCfgVisible = true;
      this.clickNode = item;
      this.input = [];
      this.inputFields.forEach((n) => {
        this.input.push({
          value: '#' + n.name + '#'
        });
      });
    }
  }

  addSymbol(logic: string, prefix: string) {
    if (this.selectNodes.length === 0) {
      this.$tip.error('没有选择节点');
      return;
    }
    for (const i of Object.keys(this.selectNodes)) {
      const index = _.findIndex(this.g.edges(), { w: this.selectNodes[i] });
      if (index >= 0) {
        this.$tip.error('节点已配置逻辑');
        return;
      }
    }
    const nodeId = logic + _.random(0, 100000);
    this.selectNodes.forEach((n) => {
      this.g.setEdge(nodeId, n, {
        arrowhead: 'undirected'
      });
    });
    this.condNodes.push({
      id: nodeId,
      label: prefix,
      symbol: true,
      width: 30,
      height: 30,
      logic: logic,
      subNode: this.selectNodes
    });
    this.draw();
    this.selectNodes = [];
  }
  /**
   * 获取表达式
   */
  getExpr(node: any) {
    if (node !== undefined && node.subNode !== undefined && node.subNode.length > 0) {
      const list: any = [];
      node.subNode.forEach((subNodeId) => {
        const rec: any = _.find(this.condNodes, { id: subNodeId });
        const result = this.getExpr(rec);
        if (result !== '') {
          list.push(result);
        }
      });
      if (node.symbol) {
        const expr = node.logic === 'and' ? _.join(list, '&&') : _.join(list, '||');
        return '(' + expr + ')';
      }
    } else {
      return node !== undefined ? node.expr : '';
    }
  }
  createItem(item) {
    /* eslint-disable-next-line */
    const that = this;
    this.g.setNode(item.id, {
      label: function () {
        const div = document.createElement('div');
        const container = d3.select(div);
        container.style('width', item.width + 'px');
        container.style('height', item.height + 'px').style('overflow', 'scroll');
        if (!item.symbol) {
          const div2 = container.append('div');
          div2
            .style('white-space', 'normal')
            .style('word-break', 'break-all')
            .style('word-wrap', 'break-word');
          if (_.toString(item.html) !== '') {
            div2.html(item.html);
          } else {
            div2.html('未配置' + item.id);
          }
        }
        const div3 = container.append('div');
        div3.style('text-align', 'center').style('font-family', 'Microsoft YaHei');
        div3.text(item.label);
        container
          .on('click', function () {
            if (!that.mode) {
              const index = that.selectNodes.indexOf(item.id);
              if (index < 0) {
                container.style('background', 'lightskyblue');
                that.selectNodes.push(item.id);
              } else {
                container.style('background', 'white');
                that.selectNodes.splice(index, 1);
              }
            }
          })
          .on('mouseover', function () {
            div.style.cursor = 'pointer';
          })
          .on('dblclick', function () {
            that.openModal(item);
          });
        return div;
      },
      id: item.id,
      padding: 3,
      rx: 5,
      ry: 5,
      expr: item.expr
    });
  }
  addConditon() {
    const nodeId = 'c' + _.random(0, 100000);
    this.condNodes.push({
      id: nodeId,
      symbol: false,
      width: 220,
      height: 40,
      expr: '未配置'
    });
    if (this.condNodes.length > 20) {
      this.condNodes.pop();
      this.$tip.warning('条件个数过多，添加失败');
      return;
    }
    this.draw();
  }
  deleteNode() {
    if (this.selectNodes.length === 0) {
      this.$tip.error('没有选择节点');
      return;
    }
    this.selectNodes.forEach((n) => {
      const index = _.findIndex(this.condNodes, { id: n });
      this.condNodes.splice(index, 1);
      this.condNodes.forEach((node) => {
        if (node.symbol && node.subNode && Array.isArray(node.subNode)) {
          const subIndex = node.subNode.indexOf(n);
          if (subIndex > -1) {
            node.subNode.splice(subIndex, 1);
          }
        }
      });
      this.g.removeNode(n);
    });
    this.draw();
    this.selectNodes = [];
  }
  clearAll() {
    this.selectNodes = [];
    const evens: any = _.remove(this.condNodes, { symbol: true });
    evens.forEach((n) => {
      this.g.removeNode(n.id);
    });
    const tmp = this.condNodes;
    this.condNodes = _.cloneDeep(tmp);
    this.draw();
  }
  draw() {
    this.condNodes.forEach((n) => {
      this.createItem(n);
    });
    const svg = d3.select('#filter-svg-canvas');
    svg.append('g');

    const inner = svg.select('g');
    const zoom = d3.zoom().on('zoom', function () {
      inner.attr('transform', d3.event.transform);
    });
    svg.call(zoom);
    svg.on('dblclick.zoom', null);
    const render = new dagreD3.render();
    render(d3.select('#filter-svg-canvas g'), this.g);

    const initialScale = 1.0;
    const tWidth = (svg._groups[0][0].clientWidth - this.g.graph().width * initialScale) / 2;
    const tHeight = (svg._groups[0][0].clientHeight - this.g.graph().height * initialScale) / 2;
    svg.call(zoom.transform, d3.zoomIdentity.translate(tWidth, tHeight).scale(initialScale));
  }
  init() {
    if (this.data.properties !== undefined && this.data.properties.condNodes !== undefined) {
      this.condNodes = _.cloneDeep(this.data.properties.condNodes);
      this.data.properties.edges.forEach((n) => {
        this.g.setEdge(n.v, n.w, {
          arrowhead: 'undirected'
        });
      });
    }
    this.draw();
  }
  loadData(jobData: any, data: any) {
    if (_.toString(this.jobData.content) !== '') {
      // 获取上一个节点的输出字段作为该节点的输入字段
      // 由于组件有多个上游组件进行连线时，字段需要一一对应，但是字段顺序可以不同。
      // 且连线时，后连的输入顺序会覆盖前一条连线的顺序，所以此处去获取最后一条连线信息
      const edges = this.jobData.content.edges.filter((e) => e.endNode === data.nodeId);
      let preOutputFields: any = [];
      if (edges && edges.length > 0) {
        const preNode: any = _.find(this.jobData.content.nodes, {
          nodeId: edges[edges.length - 1].startNode
        });
        preOutputFields = preNode.outputFields;
      }
      this.inputFields = [];
      const filterResult: any = _.filter(preOutputFields, { outputable: true });
      filterResult.forEach((n) => {
        this.inputFields.push({
          name: n.name,
          type: n.type,
          targetable: n.targetable
        });
      });
    }
  }
  created() {
    this.printLog = this.data.printLog;
  }
  mounted() {
    this.graphLoading = true;
    this.$nextTick(() => {
      const filter: any = document.getElementById('filter-dialog');
      (document.getElementById('svg-div') as any).style.height =
        filter.childNodes[0].offsetHeight - 170 + 'px';
      this.initGraph();
      this.loadData(this.jobData, this.data);
      this.init();
      this.graphLoading = false;
    });
  }
  @Watch('condNodes')
  watchCondNodes() {
    this.exprResult = '';
    this.condNodes.forEach((nn) => {
      if (nn.symbol) {
        const index = _.findIndex(this.g.edges(), { w: nn.id });
        if (index < 0) {
          this.exprResult = this.getExpr(nn);
        }
      }
    });
  }
}
</script>
<style>
.node rect {
  stroke: #999;
  fill: #fff;
  stroke-width: 1.5px;
}

.edgePath path {
  stroke: #333;
  stroke-width: 1.5px;
}
</style>
