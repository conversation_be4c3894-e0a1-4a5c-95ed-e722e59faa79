<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">消费查看</div>
      <div class="bs-page__header-operation">
        <el-autocomplete
          v-model="topic"
          class="inline-input"
          :fetch-suggestions="querySearch"
          placeholder="请输入topic名称"
          style="width: 300px"
          clearable
          @select="handleSelect"
        />
        <el-autocomplete
          v-model="group"
          class="inline-input"
          :fetch-suggestions="querySearch2"
          placeholder="请输入消费者"
          style="width: 300px; margin: 0 10px"
          clearable
        />
        <el-button size="small" type="primary" @click="handleProgressManage">查询</el-button>
      </div>
    </div>
    <div class="tab-content">
      <bs-table
        v-loading="paDataLoading"
        :height="pmTableData.length ? height : '195px'"
        :column-settings="false"
        :data="pmTableData"
        :column-data="columnsData"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Inject } from 'vue-property-decorator';
import {
  kafkaDescribeGroup,
  kafkaAllTopic,
  kafkaGroup,
  rocketDescribeGroup,
  rocketAllTopic,
  rocketGroup
} from '@/apis/serviceApi';
import * as _ from 'lodash';
@Component({
  components: {}
})
export default class ConsumerManager extends Vue {
  height = '300px';
  resRecord: any = {};
  pmTableData: any[] = [];
  paDataLoading = false;
  topic = '';
  group = '';
  allTopics: any[] = [];
  allGroups: any[] = [];
  columns = [
    {
      label: '名称',
      value: 'topic'
    },
    {
      label: '集群地址',
      value: 'host'
    },
    {
      label: 'partition',
      value: 'partition',
      sortable: true
    },
    {
      label: 'group',
      value: 'group',
      sortable: true
    },
    {
      label: 'offset',
      value: 'offset',
      sortable: true
    },
    {
      label: 'logEndSize',
      value: 'logEndSize',
      sortable: true
    },
    {
      label: 'lag',
      value: 'lag',
      sortable: true
    }
  ];
  @Inject('comDetailRecord') comDetailRecord;
  created() {
    this.resRecord = this.comDetailRecord.val || {};
    this.getAllTopic();
  }

  get type() {
    return this.$route.query.resType;
  }

  get columnsData() {
    return this.type === 'KAFKA'
      ? this.columns
      : this.columns.filter((item) => item.value !== 'host');
  }
  querySearch2(queryString, cb) {
    const t = this.allGroups;
    const results = queryString ? t.filter(this.createFilter(queryString)) : t;
    cb(results);
  }
  querySearch(queryString, cb) {
    const t = this.allTopics;
    const results = queryString ? t.filter(this.createFilter(queryString)) : t;
    cb(results);
  }
  createFilter(queryString) {
    return (restaurant) => {
      return _.toLower(restaurant.value).indexOf(_.toLower(queryString)) >= 0;
    };
  }
  handleSelect(item) {
    this.getAllGroup(item.value);
  }
  async handleProgressManage() {
    if (this.topic === '') {
      this.$message.error('请输入topic名称');
      return;
    }
    if (this.group === '') {
      this.$message.error('请输入消费者名称');
      return;
    }
    this.paDataLoading = true;
    const params = {
      brokers: this.resRecord.url,
      group: this.group,
      topic: this.topic,
      id: this.$route.query.id
    };
    const { data, success, msg } =
      this.type === 'KAFKA' ? await kafkaDescribeGroup(params) : await rocketDescribeGroup(params);
    if (success) {
      this.pmTableData = [];
      // 初始化
      this.pmTableData = data;
    } else {
      this.$message.error(msg);
    }
    this.paDataLoading = false;
  }

  async getAllTopic() {
    const id = this.$route.query.id;
    const { data, success, msg } =
      this.type === 'KAFKA' ? await kafkaAllTopic({ id }) : await rocketAllTopic({ id });
    if (success) {
      this.allTopics = data.map((item) => {
        return {
          value: item
        };
      });
    } else {
      this.$message.error(msg);
    }
  }

  async getAllGroup(topic: string) {
    const params = { id: this.$route.query.id, topic };
    const { data, success, msg } =
      this.type === 'KAFKA' ? await kafkaGroup(params) : await rocketGroup(params);
    if (success) {
      this.allGroups = data.map((item) => {
        return {
          value: item
        };
      });
    } else {
      this.$message.error(msg);
    }
  }
}
</script>
<style scoped></style>
