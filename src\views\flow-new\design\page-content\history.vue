<template>
  <div class="history__container">
    <!-- 基本信息 -->
    <div class="tab-title"><div class="title-text">基本信息</div></div>
    <div class="history-baseInfo">
      <el-row v-for="(el, index) in baseInfoList" :key="index">
        <template v-for="item in el">
          <el-col :key="item.value" :span="8" class="history-baseInfo__item">
            <el-tooltip
              v-if="item.showTooltip && jobData[item.value]"
              effect="light"
              placement="bottom"
              :content="`${item.label}：${handleValue(item)}`"
            >
              <span> {{ handleValue(item) }} </span>
            </el-tooltip>
            <span v-else> {{ item.label }}： {{ handleValue(item) }} </span>
          </el-col>
        </template>
      </el-row>
    </div>
    <!-- 历史运行情况 -->
    <div class="tab-title"><div class="title-text">历史运行情况</div></div>
    <div class="history-run">
      <base-table :table-data="tableData" @handleCurrentChange="handleCurrentChange">
        <template slot="fromLastCheckPoint" slot-scope="{ row }">
          <el-tag :type="row.fromLastCheckPoint ? 'success' : 'danger'">
            {{ row.fromLastCheckPoint ? '是' : '否' }}
          </el-tag>
        </template>
        <template slot="offlineTime" slot-scope="{ row }">
          <span v-if="row.offlineTime">{{ row.offlineTime | dateFormat }}</span>
          <span v-else>-</span>
        </template>
        <template slot="onlineTime" slot-scope="{ row }">
          <span v-if="row.onlineTime">{{ row.onlineTime | dateFormat }}</span>
          <span v-else>-</span>
        </template>
        <template slot="lastCheckpointTime" slot-scope="{ row }">
          <span v-if="row.lastCheckpointTime">{{ row.lastCheckpointTime | dateFormat }}</span>
          <span v-else>-</span>
        </template>
        <template slot="jobSubmitStatus" slot-scope="{ row }">
          <el-tag
            v-if="row.jobSubmitStatus"
            :type="row.jobSubmitStatus === '成功' ? 'success' : 'danger'"
          >
            {{ row.jobSubmitStatus }}
          </el-tag>
        </template>
      </base-table>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { get, post } from '@/apis/utils/net';
import { dateFormat } from '@/utils/format';
import { URL_HISTORY_LIST, URL_JOB_FINDBYID } from '@/apis/commonApi';

@Component({
  components: { BaseTable: () => import('@/components/base-table.vue') }
})
export default class FlowHistory extends Vue {
  @Prop({ default: '' }) flowId!: string;
  private jobData: any = { content: { nodes: [] } };
  private tableData: ITableData = { columnData: [], tableData: [] };
  private pageData = { pageSize: 20, currentPage: 1, total: 1 };

  get baseInfoList() {
    return [
      [
        {
          label: '流程名称',
          value: 'jobName'
        },
        {
          label: '流程备注',
          value: 'memo',
          showTooltip: true
        },
        {
          label: '版本信息',
          value: 'jobVersion'
        }
      ],
      [
        {
          label: '流程状态',
          value: 'jobStatus',
          format: (val: string) =>
            ({
              DEV: '开发',
              INPUB: '发布中',
              PUB: '发布',
              INPROD: '启动中',
              PROD: '运行'
            }[val])
        },
        {
          label: '描述',
          value: 'comments',
          showTooltip: true
        },
        {
          label: '最新启动时间',
          value: 'onlineTime',
          format: (val: any) => (val ? dateFormat(val) : '-')
        }
      ],
      [
        {
          label: '创建人',
          value: 'createdBy'
        },
        {
          label: '修改人',
          value: 'updatedBy'
        },
        {
          label: '最新停止时间',
          value: 'offlineTime',
          format: (val: any) => (val ? dateFormat(val) : '-')
        }
      ],
      [
        {
          label: '创建时间',
          value: 'createTime',
          format: (val: any) => (val ? dateFormat(val) : '-')
        },
        {
          label: '修改时间',
          value: 'updateTime',
          format: (val: any) => (val ? dateFormat(val) : '-')
        },
        {
          label: '组件数',
          value: 'length',
          format: () => this.jobData.content.nodes.length
        }
      ]
    ];
  }

  async created() {
    if (this.flowId) {
      await this.getFlowDetail();
      await this.getFlowList();
    }
  }

  handleValue({ format, value }) {
    return typeof format === 'function' ? format(this.jobData[value]) : this.jobData[value] || '-';
  }

  async getFlowDetail() {
    try {
      const { success, data, error } = await get(URL_JOB_FINDBYID, { id: this.flowId });
      if (success) {
        this.jobData = data;
        try {
          this.jobData.content = JSON.parse(this.jobData.content);
        } catch {
          this.jobData.content = { nodes: [], edges: [] };
        }
        return;
      }
      this.$tip.error(error);
    } catch {}
  }
  async getFlowList() {
    const { success, data, error } = await post(URL_HISTORY_LIST, {
      search: this.flowId,
      pageData: this.pageData
    });
    if (success) {
      if (Array.isArray(data.tableData)) {
        data.tableData.forEach((el) => {
          !el.applicationId && (el.applicationId = '-');
          !el.jobRuntimeId && (el.jobRuntimeId = '-');
          !el.dataCount && (el.dataCount = '-');
          !el.tps && (el.tps = '-');
          !el.jobDelay && (el.jobDelay = '-');
          !el.offlinedBy && (el.offlinedBy = '-');
          !el.lastCheckpointPath && (el.lastCheckpointPath = '-');
          // !el.lastCheckpointTime && (el.lastCheckpointTime = '-');
        });
      }
      this.tableData = { ...data };
      return;
    }
    this.$tip.error(error);
  }
  handleCurrentChange(val) {
    this.pageData.currentPage = val;
    this.getFlowList();
  }
}
</script>
<style lang="scss" scoped>
.history {
  &__container {
    height: calc(100% - 40px);
  }
  &-baseInfo {
    padding: 8px 0;
    border-top: 1px solid #d4dce2;
    border-bottom: 1px solid #d4dce2;
    font-size: 12px;
    color: #666666;
    &__item {
      padding: 8px 16px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  &-run {
    padding-bottom: 8px;
    height: calc(100% - 260px);
  }
}
</style>
