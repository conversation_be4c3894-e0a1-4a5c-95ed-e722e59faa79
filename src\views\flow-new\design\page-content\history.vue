<template>
  <div class="history__container">
    <!-- 基本信息 -->
    <div class="tab-title">
      <div class="title-text">{{ $t('pa.flow.baseInfo1') }}</div>
    </div>
    <div class="history-baseInfo">
      <el-row v-for="(el, index) in baseInfoList" :key="index">
        <template v-for="item in el">
          <el-col :key="item.value" :span="8" class="history-baseInfo__item">
            <el-tooltip
              v-if="item.showTooltip && jobData[item.value]"
              effect="light"
              placement="bottom"
              :content="`${item.label}：${handleValue(item)}`"
            >
              <span> {{ item.label }}：{{ handleValue(item) }} </span>
            </el-tooltip>
            <span v-else> {{ item.label }}： {{ handleValue(item) }} </span>
          </el-col>
        </template>
      </el-row>
    </div>
    <!-- 历史运行情况 -->
    <div class="tab-title">
      <div class="title-text">{{ $t('pa.flow.historyRun') }}</div>
    </div>
    <div class="history-run">
      <bs-table
        :height="'calc(100vh - 460px)'"
        :data="tableData.tableData"
        :column-data="tableData.columnData"
        :column-settings="false"
        :page-data="pageData"
        @page-change="pageChange"
      >
        <!-- 自定义表头 -->
        <template slot="header-dataCount" slot-scope="{ row }">
          <span class="service-table__label">{{ row.label }}</span>
          <bs-icon class="bs-icon-wenti" :tooltip="$t('pa.flow.msg274')" />
        </template>
        <template slot="header-tps" slot-scope="{ row }">
          <span class="service-table__label">{{ row.label }}</span>
          <bs-icon class="bs-icon-wenti" :tooltip="$t('pa.flow.msg275')" />
        </template>
        <template slot="header-jobDelay" slot-scope="{ row }">
          <span class="service-table__label">{{ row.label }}</span>
          <bs-icon class="bs-icon-wenti" :tooltip="$t('pa.flow.msg276')" />
        </template>
        <!-- 自定义列 -->
        <template slot="fromLastCheckPoint" slot-scope="{ row }">
          <el-tag :type="row.fromLastCheckPoint ? 'success' : 'danger'">
            {{ row.fromLastCheckPoint ? $t('pa.flow.yes') : $t('pa.flow.no') }}
          </el-tag>
        </template>
        <template slot="offlineTime" slot-scope="{ row }">
          <span v-if="row.offlineTime">{{ row.offlineTime | dateFormat }}</span>
          <span v-else>-</span>
        </template>
        <template slot="onlineTime" slot-scope="{ row }">
          <span v-if="row.onlineTime">{{ row.onlineTime | dateFormat }}</span>
          <span v-else>-</span>
        </template>
        <template slot="lastCheckpointTime" slot-scope="{ row }">
          <span v-if="row.lastCheckpointTime">{{ row.lastCheckpointTime | dateFormat }}</span>
          <span v-else>-</span>
        </template>
        <template slot="jobSubmitStatus" slot-scope="{ row }">
          <el-tag v-if="row.jobSubmitStatus" :type="row.jobSubmitStatus === $t('pa.home.success') ? 'success' : 'danger'">
            {{ row.jobSubmitStatus }}
          </el-tag>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <el-button type="text" @click="handleJobMonitoring(row)">{{ $t('pa.flow.view') }}</el-button>
        </template>
      </bs-table>
    </div>
    <!-- 运行信息弹窗 -->
    <run-info-dialog v-if="showRunInfoDialog" :show.sync="showRunInfoDialog" :history-id="historyId" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { get, post } from '@/apis/utils/net';
import { dateFormat } from '@/utils/format';
import { URL_HISTORY_LIST, URL_JOB_FINDBYID } from '@/apis/commonApi';
const EN_COLUMN_WIDTH_MAP = {
  dataCount: 260,
  tps: 260,
  onlinedBy: 160,
  offlinedBy: 160,
  fromLastCheckPoint: 260,
  lastCheckpointPath: 200,
  lastCheckpointTime: 200
};
@Component({
  components: {
    RunInfoDialog: () => import('./components/run-info/run-info-dialog.vue')
  }
})
export default class FlowHistory extends Vue {
  @Prop({ default: '' }) flowId!: string;
  private jobData: any = { content: { nodes: [] } };
  private tableData: ITableData = { columnData: [], tableData: [] };
  private pageData = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 };
  showRunInfoDialog = false;
  historyId = '';

  get notJarFlow() {
    return this.jobData?.jobType !== 'UDJ';
  }
  get baseInfoList() {
    return [
      [
        {
          label: this.$t('pa.flow.flowName'),
          value: 'jobName'
        },
        {
          label: this.$t('pa.flow.flowMemo'),
          value: 'memo',
          showTooltip: true
        },
        {
          label: this.$t('pa.flow.version'),
          value: 'jobVersion'
        }
      ],
      [
        {
          label: this.$t('pa.flow.status'),
          value: 'jobStatus',
          format: (val: string) =>
            ({
              DEV: this.$t('pa.flow.dev'),
              INPUB: this.$t('pa.flow.publishing'),
              PUB: this.$t('pa.flow.publish'),
              INPROD: this.$t('pa.flow.onlining'),
              PROD: this.$t('pa.flow.online')
            }[val])
        },
        {
          label: this.$t('pa.flow.memo'),
          value: 'comments',
          showTooltip: true
        },
        {
          label: this.$t('pa.flow.onlineTime'),
          value: 'onlineTime',
          format: (val: any) => (val ? dateFormat(val) : '-')
        }
      ],
      [
        {
          label: this.$t('pa.flow.creater'),
          value: 'createdBy'
        },
        {
          label: this.$t('pa.flow.editor'),
          value: 'updatedBy'
        },
        {
          label: this.$t('pa.flow.stopTime'),
          value: 'offlineTime',
          format: (val: any) => (val ? dateFormat(val) : '-')
        }
      ],
      [
        {
          label: this.$t('pa.flow.createTime'),
          value: 'createTime',
          format: (val: any) => (val ? dateFormat(val) : '-')
        },
        {
          label: this.$t('pa.flow.editTime'),
          value: 'updateTime',
          format: (val: any) => (val ? dateFormat(val) : '-')
        },
        {
          label: this.$t('pa.flow.componentNum'),
          value: 'length',
          format: () => this.jobData.content.nodes?.length
        }
      ]
    ];
  }

  async created() {
    if (this.flowId) {
      await this.getFlowDetail();
      await this.getFlowList();
    }
  }

  handleValue({ format, value }) {
    return typeof format === 'function' ? format(this.jobData[value]) : this.jobData[value] || '-';
  }

  async getFlowDetail() {
    try {
      const { success, data, error } = await get(URL_JOB_FINDBYID, { id: this.flowId });
      if (success) {
        this.jobData = data;
        try {
          this.jobData.content = JSON.parse(this.jobData.content);
        } catch {
          this.jobData.content = { nodes: [], edges: [] };
        }
        return;
      }
      this.$tip.error(error);
    } catch {}
  }
  async getFlowList() {
    const { success, data, error } = await post(URL_HISTORY_LIST, {
      search: this.flowId,
      pageData: this.pageData
    });
    if (success) {
      data.columnData.forEach((el) => {
        el.value = el.prop;
        this.isEn && (el.width = EN_COLUMN_WIDTH_MAP[el.value] || el.width);
      });
      if (Array.isArray(data.tableData)) {
        data.tableData.forEach((el) => {
          !el.applicationId && (el.applicationId = '-');
          !el.jobRuntimeId && (el.jobRuntimeId = '-');
          !el.dataCount && (el.dataCount = '-');
          !el.tps && (el.tps = '-');
          !el.jobDelay && (el.jobDelay = '-');
          !el.offlinedBy && (el.offlinedBy = '-');
          !el.lastCheckpointPath && (el.lastCheckpointPath = '-');
          // !el.lastCheckpointTime && (el.lastCheckpointTime = '-');
        });
      }
      this.notJarFlow &&
        data.columnData.push({
          label: this.$t('pa.flow.flowRunInfo'),
          value: 'operate',
          fixed: 'right',
          width: this.isEn ? 220 : 120,
          showOverflowTooltip: false
        });
      this.tableData.columnData = data.columnData;
      this.tableData = { ...data };
      this.pageData.total = data.pageData.total;
      return;
    }
    this.$tip.error(error);
  }
  pageChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    this.getFlowList();
  }
  handleJobMonitoring(row: any = {}) {
    this.showRunInfoDialog = true;
    this.historyId = row.id;
  }
}
</script>
<style lang="scss" scoped>
.history {
  &__container {
    height: calc(100% - 40px);
  }
  &-baseInfo {
    padding: 8px 0;
    border-top: 1px solid #d4dce2;
    border-bottom: 1px solid #d4dce2;
    font-size: 12px;
    color: #666666;
    &__item {
      padding: 8px 16px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  &-run {
    padding-bottom: 8px;
    height: calc(100% - 260px);
    .bs-icon-wenti {
      font-size: 14px;
      margin-left: 3px;
    }
  }
}
</style>
