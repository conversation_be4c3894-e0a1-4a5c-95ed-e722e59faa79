<template>
  <bs-dialog
    v-loading="dataLoading"
    title="数据预览"
    :visible.sync="visible"
    width="60%"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-tabs v-model="activeName">
      <el-tab-pane label="原始" name="first">
        <div style="height: 500px; overflow-y: auto">
          <div
            v-for="(item, index) in topicData"
            :key="index"
            style="width: 100%; text-align: left; line-height: 30px"
          >
            {{ item }}
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="格式化" name="second">
        <div align="right">
          <el-input
            v-model="sep"
            placeholder="请输入列分隔符"
            style="width: 150px; margin-right: 10px"
          /><el-button type="primary" @click="dataformat"> 转换 </el-button>
        </div>
        <base-table :table-data="tableData" :table-config="tableConfig" height="500" />
      </el-tab-pane>
    </el-tabs>

    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Watch, Emit } from 'vue-property-decorator';
import { URL_RES_DETAIL_ROCKETMQ_PREVIEW } from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {
    'base-table': () => import('@/components/base-table.vue')
  }
})
export default class TopicPreview extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: {} }) data!: any;

  topicData: any = [];
  dataLoading = false;
  activeName = 'first';
  lines: any = [];

  tableData: ITableData = {
    columnData: [],
    tableData: []
  };
  tableConfig: ITableConfig = {
    width: 100,
    columnsExtend: {}
  };
  sep = '';

  dataformat() {
    let cols: any = [];
    const datas: any = [];
    this.lines.forEach((n) => {
      let obj;
      try {
        obj = JSON.parse(n);
      } catch (e) {
        obj = n;
      }
      const isArray = _.isArray(obj);
      const isObject = _.isObject(obj);
      const isString = _.isString(obj);
      if (isString) {
        // string
        const values = _.split(obj, this.sep);
        if (cols.length < values.length) {
          cols = [];
          for (let i = 0; i < values.length; i++) {
            cols.push({
              label: '值' + i,
              prop: 'v' + i
            });
          }
          this.tableData.columnData = cols;
        }
        const val = {};
        for (let i = 0; i < values.length; i++) {
          val['v' + i] = values[i];
        }
        datas.push(val);
      } else {
        if (isObject && isArray) {
          // json array
          if (cols.length === 0) {
            // 生成表头
            for (const key of Object.keys(obj[0])) {
              cols.push({
                label: key,
                prop: key
              });
            }
            this.tableData.columnData = cols;
          }
          obj.forEach((m) => {
            datas.push(m);
          });
        } else {
          // json object
          if (cols.length === 0) {
            // 生成表头
            for (const key of Object.keys(obj)) {
              cols.push({
                label: key,
                prop: key
              });
            }
            this.tableData.columnData = cols;
          }
          // 生成数据
          datas.push(obj);
        }
      }
    });
    this.tableData.tableData = datas;
  }

  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  closeDialog(needFresh: boolean) {
    this.lines = [];
    this.topicData = [];
    this.activeName = 'first';
  }

  @Watch('visible')
  loadData() {
    if (this.visible) {
      this.tableData = {
        columnData: [],
        tableData: []
      };
      this.tableConfig = {
        width: 100,
        columnsExtend: {}
      };
      this.dataLoading = true;
      this.doGet(URL_RES_DETAIL_ROCKETMQ_PREVIEW, {
        params: { id: this.$route.query.id, topic: this.data.topicName }
      }).then((resp: any) => {
        this.parseResponse(resp, () => {
          resp.data.forEach((n) => {
            this.topicData.push(n);
            this.lines.push(n);
          });
        });
        this.dataLoading = false;
      });
    }
  }
}
</script>

<style scoped></style>
