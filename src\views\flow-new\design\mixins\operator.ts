import {
  batchOnlineFlow,
  cancelPublishFlow,
  compileFlow,
  offlineFlow,
  onlineFlow,
  preCompileFlow,
  preOfflineFlow,
  preOnlineFlow,
  prePublishFlow,
  publishFlow,
  getDdStatus,
  validateComponentById,
  compileCodeById
} from '@/apis/flowNewApi';
import { DEL_LOAING_PROJECT_IDS } from '@/store/event-name';
import { Vue, Component } from 'vue-property-decorator';
import { CheckType, JobStatus, JobType, MsgType } from '../interface';
import {
  CancelPublishParams,
  ErrorInfo,
  ErrorType,
  ErrorProInfo,
  LOADING_TEXT_MAP,
  OfflineParams,
  OnlineParams,
  OperatorType,
  PreTaskResult,
  PublishParams,
  Task
} from '../interface/operator';
import { compileFlowStore, getLatestFlowListRef, transformLineErrorInfo } from '../utils';
import { hasDdMap } from '@/utils';

class ErrorReturn {
  type: ErrorType;
  errorInfo: ErrorInfo | ErrorProInfo;
  constructor(error: string | ErrorInfo | ErrorProInfo, type: ErrorType = 'error') {
    this.type = type;
    this.errorInfo = typeof error === 'string' ? { message: error } : error;
    type === 'error' && ((this.errorInfo as ErrorInfo).duration = 5000);
  }
}
class SuccessReturn {
  msg: string;
  constructor(msg) {
    this.msg = msg;
  }
}
@Component
export default class Operator extends Vue {
  compile;
  flowId;
  flowType;
  flowStatus;
  isChange;
  jobContent;
  handleCompile!: () => void;
  publish!: ({ flowIds, isBatch }: PublishParams) => void;
  cancelPublish!: ({ flowIds }: CancelPublishParams) => void;
  online!: ({ flowIds, state }: OnlineParams) => void;
  offline!: ({ flowIds, state }: OfflineParams) => void;
  showDirEmpty;
  get isFlinkSql() {
    return this.flowType === JobType.FLINK_SQL;
  }
  get isDS() {
    return this.flowType === 'PROCESSFLOW';
  }
  created() {
    // 编译
    this.handleCompile = this.operator('complier', [
      () => !this.isFlinkSql && this.vaildate('complier'), // DS流程需要调用
      this.preCompile as Task<unknown>,
      (data, result) => {
        this.jobContent = result;
        compileFlowStore.add(this.flowId);
      },
      this.realCompile as Task<unknown>,
      this.afterCompile as Task<unknown>
    ]);
    // 发布
    this.publish = this.operator<PublishParams>('publish', [
      (data) => {
        console.log(data);
        if (!data) data = { flowIds: [], isBatch: false };
        if (!data.flows) data.flows = [{ id: this.flowId, jobType: this.flowType }];
        data.flowIds = (data.flows || []).map((i) => i.id);
        this.verifyDdStatus();
      },
      this.vaildForPublish as Task<PublishParams>,
      this.prePublish as Task<PublishParams>,
      this.beforPublish as Task<PublishParams>,
      this.realPublish as Task<PublishParams>,
      this.afterPublish as Task<PublishParams>
    ]);
    // 取消发布
    this.cancelPublish = this.operator<CancelPublishParams>('cancelPublish', [
      (data) => {
        if (!data) data = { flowIds: [], isBatch: false };
        data.flowIds = data.flowIds || [this.flowId];
      },
      this.vaildForCancelPublish as Task<CancelPublishParams>,
      this.realCancelPublish as Task<CancelPublishParams>,
      this.afterPublish as Task<CancelPublishParams>
    ]);
    // 上线
    this.online = this.operator<OnlineParams>(
      'online',
      [
        (data) => {
          if (!data) data = { flowIds: [], state: false };
          data!.flowIds = data?.flowIds || [{ id: this.flowId, fromLastCheckpoint: data!.state }];
          this.verifyDdStatus();
        },
        this.vaildForOnline as Task<OnlineParams>,
        this.preOnline as Task<OnlineParams>,
        this.beforeOnline as Task<OnlineParams>,
        this.realonline as Task<OnlineParams>,
        this.afterOnline as Task<OnlineParams>
      ],
      {
        before: this.beforeBeforeVaildForOnline as Task<OnlineParams>
      }
    );
    // 下线
    this.offline = this.operator<OfflineParams>(
      'offline',
      [
        this.vaildForOffline as Task<OfflineParams>,
        this.preOffline as Task<OfflineParams>,
        this.beforeOffline as Task<OfflineParams>,
        this.realOffline as Task<OfflineParams>,
        this.afterOffline as Task<OfflineParams>
      ],
      {
        before: this.beforeBeforeVaildForOffline as Task<OfflineParams>
      }
    );
  }
  /* 获取当前页面的实例 */
  getVm(flowId) {
    if (!this.$parent || !Array.isArray(this.$parent.$children)) return this;
    return this.$parent.$children.find((vm: any) => vm.flowId === flowId) || this;
  }
  /* 获取当前页面的的flowList实例 */
  getFlowListVm() {
    const currentFlowId = this.$route.query.flowId;
    return getLatestFlowListRef(this.$parent.$children || [], currentFlowId);
  }
  /* 流程列表刷新重置勾选 */
  resetAndRefreshFlowList() {
    const flowListRef = this.getFlowListVm();
    flowListRef.fetchList();
    flowListRef.selectedFlowIds = [];
    flowListRef.isCheckedAll = false;
  }
  // 设置左侧列表的状态错误信息
  setError(ids, errMsg) {
    const flowListRef = this.getFlowListVm();
    ids = Array.isArray(ids) ? ids : [ids];
    errMsg = Array.isArray(errMsg) ? errMsg : [errMsg];
    ids.forEach((id, idx) => {
      flowListRef.setFlowError(id, errMsg[idx]);
    });
  }
  // 设置左侧列表的错误信息
  removeError(ids) {
    const flowListRef = this.getFlowListVm();
    ids = Array.isArray(ids) ? ids : [ids];
    ids.forEach((id) => {
      flowListRef.removeFlowError(id);
    });
  }
  /* 批量设置左侧流程列表的状态 */
  batchSetStatus(ids, status) {
    const flowListRef = this.getFlowListVm();
    ids.forEach((id) => {
      flowListRef.setFlowStatus(id, status);
      flowListRef.removeFlowError(id);
    });
  }
  // 流程是否为批模式
  isBatchFlow() {
    try {
      return JSON.parse((this as any).getNewestFlow()?.properties)?.mode === 'batch';
    } catch {
      return;
    }
  }
  // ===========================编译==========================
  /* 流程预编译 */
  async preCompile() {
    const params = (this as any).getNewestFlow();
    if (this.isFlinkSql) {
      params.content = this.$store.getters.encrypt(params.content);
    }
    delete params.properties;
    const { success, data, error } = await preCompileFlow(params);
    if (success) return this.isFlinkSql ? this.$store.getters.decrypt(data.content) : JSON.parse(data.content);
    throw new ErrorReturn(error);
  }
  /* 流程编译 */
  async realCompile() {
    const params = (this as any).getNewestFlow();
    if (this.isFlinkSql) {
      params.content = this.$store.getters.encrypt(params.content);
    }
    return await compileFlow(params);
  }
  /* 流程编译之后处理 */
  afterCompile(data, result) {
    const { success, data: _data, error, msgType } = result || {};
    compileFlowStore.remove(_data.jobId || this.flowId);
    const currentFlowId = this.$route.query.flowId;
    if (!success && msgType === MsgType.SIMPLE) {
      throw new ErrorReturn(error);
    }
    if (!success && msgType === MsgType.LINE_MESSAGE && currentFlowId === _data.jobId) {
      const lineErrors = JSON.parse(error || '[]');
      (this as any).flowSqlCode.setCodeError(transformLineErrorInfo(lineErrors));
      throw new ErrorReturn(this.$t('pa.flow.msg39'));
    }
    return result;
  }
  // ===========================发布==========================
  /* 流程发布校验 */
  async vaildForPublish(data) {
    const { flows, flowIds, isBatch } = data;
    /* 增加发布二次校验 */
    await this.$verifyDd.show(flowIds);
    // 发布流程包含当前流程 且 发生了变更
    if (this.isChange && flowIds.includes(this.flowId)) {
      throw new ErrorReturn(this.$t('pa.flow.msg265', [(this as any).baseInfo.jobName]), 'warning');
    }
    if ((!isBatch && this.flowStatus !== JobStatus.DEV) || flowIds.length === 0) {
      throw new ErrorReturn(this.$t('pa.flow.msg41'), 'warning');
    }
    // 非批量操作 DS流程前端错误检查
    if (!isBatch && flows[0] && flows[0].jobType === JobType.PROCESSFLOW) {
      const result = (this as any).flowCanvas?.validteFlow('publish');
      if (!result) {
        throw new ErrorReturn(this.$t('pa.flow.msg37'));
      }
    }
    const requests = flows
      .filter(({ jobType }) => jobType !== JobType.UDJ)
      .map(({ id, jobType }) => ({ id, req: jobType === JobType.PROCESSFLOW ? this.vaildateDs : this.vaildateSql }));
    const values: { id: string; success: boolean }[] = await Promise.all(requests.map(({ id, req }) => req(id)));
    // 过滤流程类型未jar 或者 校验通过的流程
    data.flows = data.flows.filter(
      ({ id, jobType }) => jobType === JobType.UDJ || (values.find((i) => i.id === id) || {}).success
    );
    data.flowIds = data.flows.map((i) => i.id);
    // 校验通过的流程数量为空 阻止后续请求
    if (data.flowIds.length === 0) throw new ErrorReturn(this.$t('pa.flow.msg37'));
  }
  /* sql流程发布校验 */
  async vaildateSql(flowId) {
    const { success, msgType, error } = await compileCodeById(flowId);
    if (!success && msgType === MsgType.LINE_MESSAGE) {
      if (this.flowId === flowId) {
        this.removeError(flowId);
        const lineErrors = JSON.parse(error || '[]');
        (this as any).flowSqlCode.setCodeError(transformLineErrorInfo(lineErrors));
      }
      // 不是当前流程或者画布被遮盖（选中目录时）
      if (this.flowId !== flowId || this.showDirEmpty) {
        console.log(this.showDirEmpty);
        this.setError(flowId, {
          type: JobStatus.DEV,
          msgType: MsgType.LINE_MESSAGE,
          errorInfo: error
        });
      }
    }
    return { success, id: flowId };
  }
  /* ds流程发布校验 */
  async vaildateDs(flowId) {
    const { success, data = {}, msg } = await validateComponentById(flowId);
    this.removeError(flowId);
    if (!success) {
      const updateInfos = Object.keys(data).map((key) => ({
        id: key,
        status: 2,
        msg: data[key]
      }));
      // 当前流程
      if (this.flowId === flowId) {
        // 节点报错 直接画布区域中报错 反之存储流程的错误
        updateInfos.length
          ? (this as any).flowCanvas?.updateNodeStatus(updateInfos)
          : this.setError(flowId, {
              type: JobStatus.DEV,
              msgType: MsgType.SIMPLE,
              errorInfo: msg
            });
      }
      // 不是当前流程或者画布被遮盖（选中目录时）
      if (this.flowId !== flowId || this.showDirEmpty) {
        this.setError(flowId, {
          type: JobStatus.DEV,
          msgType: updateInfos.length ? MsgType.NODE_MESSAGE : MsgType.SIMPLE,
          errorInfo: updateInfos.length ? JSON.stringify(updateInfos) : msg
        });
      }
    }
    return { success, id: flowId };
  }
  /* 流程预发布 */
  async prePublish({ flowIds }) {
    const { success, error } = await prePublishFlow(flowIds);
    if (success) return;
    throw new ErrorReturn(error);
  }
  /* 流程发布之前 */
  beforPublish({ flowIds }) {
    // 包含当前流程 更改状态
    if (flowIds.includes(this.flowId)) {
      this.flowStatus = JobStatus.INPUB;
    }
    // 批量更改流程状态
    this.batchSetStatus(flowIds, JobStatus.INPUB);
  }
  /* 流程发布 */
  async realPublish({ flowIds }) {
    return await publishFlow(flowIds);
  }
  /* 流程发布/取消发布之后 */
  async afterPublish({ flowIds, isBatch }, { success, msg, error, data = [] }) {
    const { jobId = flowIds[0], errorInfo = error, msgType } = data[0] || {};
    this.flowStatus = success ? JobStatus.PUB : JobStatus.DEV;
    const currentFlowId = this.$route.query.flowId;
    this.resetAndRefreshFlowList();
    if (success) {
      return new SuccessReturn(msg);
    }
    // 非批量模式 且 当前流程不是操作流程
    if (!isBatch) {
      // 当前流程不是操作流程, 设置错误信息
      if (currentFlowId !== jobId) {
        this.setError(jobId, { type: JobStatus.DEV, errorInfo, msgType });
      } else {
        this.removeError(jobId);
        if (msgType === MsgType.LINE_MESSAGE) {
          const lineErrors = JSON.parse(errorInfo || '[]');
          (this as any).flowSqlCode.setCodeError(transformLineErrorInfo(lineErrors));
          throw new ErrorReturn(this.$t('pa.flow.msg37'));
        }
        throw new ErrorReturn(errorInfo);
      }
    } else {
      data.forEach(({ jobId: _jobId, success: _success, errorInfo: _errorInfo, msgType: _msgType }) => {
        _success
          ? this.removeError(_jobId)
          : this.setError(_jobId, {
              type: JobStatus.DEV,
              errorInfo: _errorInfo,
              msgType: _msgType
            });
      });
      throw new ErrorReturn(error);
    }
  }
  // ===========================取消发布==========================
  /* 流程取消发布校验 */
  vaildForCancelPublish({ flowIds, isBatch }) {
    if ((!isBatch && this.flowStatus !== JobStatus.PUB) || flowIds.length === 0) {
      throw new ErrorReturn(this.$t('pa.flow.msg42'), 'warning');
    }
  }
  /* 流程取消发布 */
  async realCancelPublish({ flowIds }) {
    return await cancelPublishFlow(flowIds);
  }
  // ===========================上线==========================
  /* 流程上线前执行 */
  async beforeBeforeVaildForOnline(data: OnlineParams) {
    if (!data.state) {
      await this.$confirm(this.$t('pa.flow.msg17'), this.$t('pa.flow.tip'), { type: 'warning' });
    }
  }
  /* 流程上线校验 */
  async vaildForOnline({ flowIds }: OnlineParams) {
    if (flowIds.length === 0) {
      throw new ErrorReturn(this.$t('pa.flow.msg43'), 'warning');
    }
    const resultForBack = await (this as any).validateFlowByBack();
    if (!resultForBack) throw '';
  }
  /* 流程预上线 */
  async preOnline({ flowIds }: OnlineParams) {
    const params = flowIds.map(({ id }) => id);
    const result = await preOnlineFlow(params);
    const { success, data, msg, msgType } = result;
    if (success) return result;
    throw new ErrorReturn({ msgType, msg, data }, 'errorPro');
  }
  /* 流程上线前处理 */
  beforeOnline({ flowIds }: OnlineParams) {
    // 批量上线此处不需要处理，只考虑单个流程上线
    if (flowIds[0].id === this.flowId) {
      this.flowStatus = JobStatus.INPROD;
    }
    // 批量更改流程状态
    this.batchSetStatus(
      flowIds.map((i) => i.id),
      JobStatus.INPROD
    );
  }
  /* 流程上线 */
  async realonline({ flowIds }: OnlineParams) {
    const runFn = this.isBatchFlow() ? batchOnlineFlow : onlineFlow;
    return await runFn(flowIds);
  }
  /* 流程上线之后处理 */
  async afterOnline({ flowIds }: OnlineParams, { success, msg, data = [] }: any) {
    const { jobId = flowIds[0].id, errorInfo, msgType } = data[0] || {};
    this.flowStatus = success ? JobStatus.PROD : JobStatus.PUB;
    const currentFlowId = this.$route.query.flowId;
    this.resetAndRefreshFlowList();
    (this as any).onlineSuccess = success;
    const _msg = msgType === MsgType.DETAIL ? this.$t('pa.flow.msg34') : errorInfo;
    if (success) {
      return new SuccessReturn(msg);
    }
    // 已离开当前流程，在流程左侧报错
    if (data.length === 1 && currentFlowId !== jobId) {
      this.setError(jobId, { type: JobStatus.PUB, msgType, msg: _msg, data });
    } else {
      this.removeError(jobId);
      if (msgType === MsgType.LINE_MESSAGE) {
        const lineErrors = JSON.parse(data[0].errorInfo);
        (this as any).flowSqlCode.setCodeError(transformLineErrorInfo(lineErrors));
      } else {
        throw new ErrorReturn({ msgType, msg: _msg, data }, 'errorPro');
      }
    }
  }
  // ===========================下线==========================
  /* 流程上下线之前 */
  async beforeBeforeVaildForOffline({ flowIds, state }: OfflineParams) {
    if (!flowIds && state !== 'retain') {
      const type = { stop: this.$t('pa.flow.stop'), force: this.$t('pa.flow.forceStop') }[state];
      const msg = this.$t('pa.flow.msg44', [type, (this as any).baseInfo.jobName]);
      await this.$confirm(msg, this.$t('pa.flow.tip'), {
        type: 'warning',
        customClass: 'flow-list__confirm'
      });
      // 隐藏组件信息和流程运行信息，为特殊处理
      (this as any).showNodeInfoDrawer = false;
      (this as any).showRunInfoDrawer = false;
    } else {
      (this as any).showNodeInfoDrawer = false;
      (this as any).showRunInfoDrawer = false;
    }
  }
  /* 流程上下线校验 */
  async vaildForOffline({ flowIds }: OfflineParams) {
    if ((!flowIds && this.flowStatus !== JobStatus.PROD) || (flowIds && flowIds.length === 0)) {
      throw new ErrorReturn(this.$t('pa.flow.msg45'), 'warning');
    }
  }
  /* 流程预下线 */
  async preOffline({ flowIds }: OfflineParams) {
    const params = flowIds ? flowIds.map((i) => ({ jobId: i.jobId })) : [{ jobId: this.flowId }];
    const result = await preOfflineFlow(params);
    if (result.success) return result;
    throw new ErrorReturn(result.error);
  }
  /* 流程下线之前 */
  beforeOffline({ flowIds }: OfflineParams) {
    const ids = flowIds ? flowIds.map((i) => i.jobId) : [this.flowId];
    // 包含当前流程 更改状态
    if (ids.includes(this.flowId)) {
      this.flowStatus = JobStatus.INOFF;
    }
    // 批量更改流程状态
    this.batchSetStatus(ids, JobStatus.INOFF);
  }
  /* 流程下线 */
  async realOffline({ flowIds, state }: OfflineParams) {
    const params = flowIds || [{ jobId: this.flowId, savepoint: state === 'retain' }];
    return await offlineFlow(state === 'force', params);
  }
  /* 流程下线之后 */
  afterOffline({ flowIds }, { data, error, msg, success, msgType }) {
    const { jobId, errorInfo = error } = data[0] || {};
    const currentFlowId = this.$route.query.flowId;
    this.flowStatus = success ? JobStatus.PUB : JobStatus.PROD;
    this.resetAndRefreshFlowList();
    if (success) {
      return new SuccessReturn(msg);
    }
    if (!flowIds) {
      // 当前流程不是操作流程, 设置错误信息
      currentFlowId !== jobId ? this.setError(jobId, { type: JobStatus.PROD, errorInfo }) : this.removeError(jobId);
    } else {
      data.forEach(({ jobId: _jobId, success: _success, errorInfo: _errorInfo }) => {
        _success ? this.removeError(_jobId) : this.setError(_jobId, { type: JobStatus.PROD, errorInfo: _errorInfo });
      });
    }
    throw new ErrorReturn({ msgType, msg: !flowIds ? errorInfo || msg : msg, data }, 'errorPro');
  }
  /* 检验 */
  async vaildate(type: CheckType | OperatorType) {
    (this as any).componentConfigPreTreated(type);
    // 1. 前端错误检查
    const result = (this as any).flowCanvas?.validteFlow(type);
    if (!result) throw '';
    // 2. 后端错误校验
    const resultForBack = await (this as any).validateFlowByBack();
    if (!resultForBack) throw '';
    return true;
  }
  operator<T>(type: OperatorType, tasks: Task<T>[], options?: { before: Task<T> }) {
    return async (data?: T) => {
      let operatorResult = true;
      const { showLoading, closeLoading } = this as any;
      options && options.before && (await options.before(data));
      showLoading(this.$t('pa.flow.msg27', [LOADING_TEXT_MAP[type]]));
      // 上一次任务执行结束的返回值
      let result: PreTaskResult = null;
      try {
        for (let i = 0; i < tasks.length; i++) {
          result = await tasks[i](data || undefined, result);
        }
        closeLoading();
        result && result.msg && this.$tip.success(result.msg);
      } catch (error) {
        operatorResult = false;
        console.log('operator catch error: ', error);
        closeLoading();
        if (!error) return;
        this.$tip[error.type] && this.$tip[error.type](error.errorInfo);
      } finally {
        const currentVm: any = this.getVm(this.$route.query.flowId);
        if (operatorResult && this.$route.query.flowId && type !== 'complier') {
          currentVm.isRender = false; // 用于后续更新画布
          currentVm.getCurFlow({ id: this.$route.query.flowId });
        }
        this.$store.commit(DEL_LOAING_PROJECT_IDS, currentVm.baseInfo.projectId);
      }
    };
  }
  async verifyDdStatus() {
    if (!hasDdMap((this as any).flowStore.content)) return;
    const { success, data = {}, error = '' } = await getDdStatus((this as any).flowStore.baseInfo.id);
    if (!success) return this.$tip.error(error);
    Object.entries(data).forEach(([id, msg]) => {
      msg && (this as any).flowCanvas?.updateNodeStatus({ id, status: 3, msg });
    });
  }
}
