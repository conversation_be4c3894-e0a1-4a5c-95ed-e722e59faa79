export interface TableData {
  name: string;
  cnName: string;
  type: string;
  isAdvField?: boolean;
}

export interface FieldInfo {
  height?: string | number;
  tableData: TableData[];
  columnData: any[];
}
export interface TableInfo {
  tableName: string; // 表名
  tableNameCn: string; // 中文名
  businessExplain: string; // 业务口径
  serviceType: string; // 服务类型
  serviceName: string; // 服务
  serviceAddress: string; // 服务地址
  serviceLabel: string; // 数据表、topic、''
  serviceValue: string; // label 对应的值
  connectorName: string; //  连接器名称
  baseConnector: any[]; // 基础连接器
  advancedConnector: any[]; // 高级连接器
  fieldInfo: any[]; // 字段信息
}
