const router = [
  {
    path: '/element/data',
    name: 'elementData',
    meta: {
      title: '参数管理',
      icon: 'iconfont icon-mobanguanli',
      access: 'PA.SETTING'
    },
    component: () => import('../views/element/index.vue'),
    children: [
      {
        path: '/element/dictionary',
        name: 'templateDictionary',
        meta: {
          access: 'PA.SETTING.DIC.MENU', // 权限信息
          title: '数据字典'
        },
        component: () => import('../views/params/dictionary/index.vue')
      },
      {
        path: '/element/template',
        name: 'templateTemplate',
        meta: {
          title: '模板管理',
          access: 'PA.SETTING.MODEL.MENU'
        },
        component: () => import('../views/params/index.vue'),
        children: [
          {
            path: '/template/cep',
            name: 'templateCep',
            meta: {
              noMenu: true,
              access: 'CEP', // 权限信息
              title: 'CEP模板'
            },
            component: () => import('../views/params/cep-template/index.vue')
          },
          {
            path: '/template/cep/add-edit',
            name: 'templateCepAddEdit',
            meta: {
              title: 'CEP编辑/新建',
              noMenu: true,
              access: 'CEP' // 权限信息
            },
            component: () => import('../views/params/cep-template/add-edit/index.vue')
          },
          {
            path: '/template/cep/detail',
            name: 'templateCepDetail',
            meta: {
              title: 'CEP详情',
              noMenu: true,
              access: 'CEP' // 权限信息
            },
            beforeEnter(to, from, next) {
              to.meta.title = to.query.title;
              next();
            },
            component: () => import('../views/params/cep-template/detail/index.vue')
          },
          {
            path: '/element/template/routeTemplate',
            name: 'routeTemplate',
            meta: {
              title: '路由模板',
              access: 'PA.SETTING.MODEL.ROUTE.MENU'
            },
            component: () => import('../views/params/template/rule-template/index.vue')
          },
          {
            path: '/element/template/filterTemplate',
            name: 'templateFilterTemplate',
            meta: {
              title: '过滤模板',
              access: 'PA.SETTING.MODEL.FILTER.MENU'
            },
            component: () => import('../views/params/template/filter-template/index.vue')
          },
          {
            path: '/element/template/mapLibrary',
            name: 'templateMapLibrary',
            meta: {
              title: '映射字段库',
              access: 'PA.SETTING.MODEL.MAPPING_FIELD.MENU'
            },
            component: () => import('../views/params/template/map-library/index.vue')
          },
          {
            path: '/element/template/mapTemplate',
            name: 'templateMapTemplate',
            meta: {
              title: '映射模板',
              access: 'PA.SETTING.MODEL.MAPPING.MENU'
            },
            component: () => import('../views/params/template/map-template/index.vue')
          },
          {
            path: 'routeTemplateDetail',
            name: 'elementRuletTemplateDetail',
            beforeEnter(to, from, next) {
              to.meta.title = to.query.title;
              next();
            },
            component: () => import('../views/params/template/rule-template/detail.vue')
          },
          {
            path: '/element/template/routeTemplateHistory',
            name: 'templateRouteTemplateHistory',
            beforeEnter(to, from, next) {
              to.meta.title = to.query.title;
              next();
            },
            component: () => import('../views/params/template/rule-template/history.vue')
          },
          {
            path: '/element/template/filterTemplateDetail',
            name: 'templateFilterTemplateDetail',
            beforeEnter(to, from, next) {
              to.meta.title = to.query.title;
              next();
            },
            component: () => import('../views/params/template/filter-template/detail.vue')
          },
          {
            path: '/element/template/filterTemplateHistory',
            name: 'templateFilterTemplateHistory',
            beforeEnter(to, from, next) {
              to.meta.title = to.query.title;
              next();
            },
            component: () => import('../views/params/template/filter-template/history.vue')
          },
          {
            path: '/element/template/mapTemplateEdit',
            name: 'templateMapTemplateEdit',
            beforeEnter(to, from, next) {
              to.meta.title = to.query.title;
              next();
            },
            component: () => import('../views/params/template/map-template/edit.vue')
          },
          {
            path: '/element/template/mapTemplateHistory',
            name: 'templateMapTemplateHistory',
            beforeEnter(to, from, next) {
              to.meta.title = to.query.title;
              next();
            },
            component: () => import('../views/params/template/map-template/history.vue')
          }
        ]
      }
    ]
  }
];

export { router };
