<template>
  <header class="header__container">
    <!-- 查询节点 -->
    <search-config :is-full-screen="isFullScreen" @submit="search" />
    <!-- 显示配置 -->
    <display-config
      :is-full-screen="isFullScreen"
      :data.sync="formData"
      @submit="$emit('submit')"
    />
    <!-- 下载 -->
    <el-tooltip
      v-access="'PA.BLOOD_RELATION.DOWNLOAD'"
      content="下载"
      effect="light"
      placement="top"
    >
      <i class="iconfont icon-xiazai1" @click="debounceClick('download')"></i>
    </el-tooltip>
    <!-- 全屏 -->
    <el-tooltip v-if="!isFullScreen" content="全屏" effect="light" placement="top">
      <i class="iconfont icon-quanping" @click="click('fullScreen')"></i>
    </el-tooltip>
    <!-- 退出全屏 -->
    <el-tooltip v-else content="退出全屏" effect="light" placement="top">
      <i class="iconfont icon-tuichuquanping" @click="click('exitFullScreen')"></i>
    </el-tooltip>
  </header>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import debounce from 'lodash/debounce';
import DisplayConfig from './dispaly-config.vue';
import SearchConfig from './search-config.vue';

@Component({ components: { DisplayConfig, SearchConfig } })
export default class TopHeader extends Vue {
  @Prop({ default: false }) isFullScreen!: boolean;
  @PropSync('displayConfig', { default: () => ({}) }) formData!: any;

  private debounceClick = debounce(this.click, 1200);

  click(type: string) {
    this.$emit('click', type);
  }

  search(data) {
    this.$emit('search', data);
  }
}
</script>
<style lang="scss" scoped>
$headerHeight: 56px;

.header {
  &__container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 10px;
    width: 100%;
    height: 64px;
    background: #ffffff;
    box-shadow: 0px 1px 8px 0px rgba(0, 0, 0, 0.05);
    border: 1px solid #f6f6f6;
    box-sizing: border-box;
    overflow: hidden;
    i {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      margin-left: 16px;
      width: 32px;
      height: 32px;
      font-size: 14px;
      cursor: pointer;
      border-radius: 4px;
      border: 1px solid #dbd9d9;
    }
  }
  &-display {
    width: 200px;
    overflow: hidden;
  }
}
</style>
