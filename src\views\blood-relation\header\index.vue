<template>
  <div class="search" :style="{ width: isExpand ? '300px' : '0px' }">
    <!-- 查询节点 -->
    <div v-show="isExpand" class="search__label">
      <span class="search__label-title">{{ $t('pa.blood.queryNode') }}</span>
      <el-tooltip effect="light" placement="bottom-start">
        <div slot="content">
          <p style="margin-bottom: 8px">{{ tipContent }}</p>
          <p class="content-point"><span class="bs-circle"></span>{{ $t('pa.blood.tip4') }}</p>
          <p class="content-point"><span class="bs-circle"></span>{{ $t('pa.blood.tip5') }}</p>
          <p class="content-point"><span class="bs-circle"></span>{{ $t('pa.blood.tip6') }}</p>
        </div>
        <i class="bs-pro-form-item__icon bs-icon-wenti" style="cursor: pointer"></i>
      </el-tooltip>
    </div>
    <!-- 查询表单 -->
    <search-config v-show="isExpand" class="search__form" @search="(data) => $emit('search', data)" />
    <!-- 展开收起按钮 -->
    <div :class="[isExpand ? 'search__close' : 'search__expand', isEn ? 'isEn' : '']" @click="toggleExpand">
      <span v-if="!isExpand">{{ $t('pa.blood.queryNode') }}</span>
      <span :class="['iconfont', isExpand ? 'icon-close' : 'icon-expand']"></span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import SearchConfig from './search-config.vue';

@Component({ components: { SearchConfig } })
export default class LeftSearch extends Vue {
  @Prop({ default: false }) isFullScreen!: boolean;
  isExpand = true;
  get tipContent() {
    return this.$store.getters.enableSql ? this.$t('pa.blood.tip7') : this.$t('pa.blood.tip8');
  }
  search(data) {
    this.$emit('search', data);
  }
  toggleExpand() {
    this.isExpand = !this.isExpand;
    this.$emit('expand-change', this.isExpand);
  }
}
</script>
<style lang="scss" scoped>
.search {
  position: relative;
  flex-shrink: 0;
  width: 300px;
  height: 100%;
  background: #ffffff;
  box-shadow: 0px 0px 5px 0px rgba(2, 13, 41, 0.07);
  &__label {
    width: 100%;
    position: relative;
    z-index: 2;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    height: 64px;
    padding: 0 20px;
    border-bottom: 1px solid #ebeef5;
    background: #fff;
  }
  &__label-title {
    position: relative;
    padding-left: 16px;
    font-weight: 500;
  }
  &__label-title::before {
    content: '';
    position: absolute;
    top: 6px;
    left: 0px;
    display: block;
    width: 8px;
    height: 8px;
    background: #ff9c00;
    border-radius: 2px;
  }
  &__expand,
  &__close {
    position: absolute;
    top: 6px;
    right: -50px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 50px;
    min-height: 50px;
    padding: 16px 0;
    background: #fff;
    box-shadow: 0px 0px 5px 0px rgba(2, 13, 41, 0.07);
    border-radius: 0 10px 10px 0;
    cursor: pointer;
    z-index: 1;
    & .iconfont {
      color: #aaa;
    }
  }
  &__expand > span {
    width: 20px;
    text-align: center;
    line-height: 18px;
    color: #777777;
    margin-bottom: 12px;
  }
  &__expand {
    &.isEn {
      > span {
        writing-mode: vertical-rl;
        text-orientation: sideways;
      }
    }
  }
  &__form {
    height: calc(100% - 64px);
  }
}

.content-point {
  display: flex;
  .bs-circle {
    flex-shrink: 0;
    background: #444;
    margin-top: 6px;
    margin-right: 10px;
  }
}
</style>
