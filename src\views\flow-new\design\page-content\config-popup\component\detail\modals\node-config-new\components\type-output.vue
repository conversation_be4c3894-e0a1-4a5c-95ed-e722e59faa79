<template>
  <bs-tag-input
    v-if="multiple"
    ref="tagInput"
    v-model="innerValue"
    separator=","
    :mincollapsed-num="4"
    draggable
    :placeholder="$t('pa.flow.placeholder27')"
    :disabled="disabled"
    :validate-method="(val) => validateMethod(val)"
    @change="handleChange"
  />
  <el-input
    v-else
    v-model="innerValue"
    :disabled="disabled"
    :placeholder="placeholder"
    @input="handleChange"
    @change="handleChange"
  />
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { FIELD_REG } from '../validator';
// 存储流程报错信息
@Component({ name: 'TypeOutput' })
export default class TypeOutput extends Vue {
  // 表单配置信息
  @Prop() config!: any;
  @Prop() disabled!: boolean;
  @Prop() value!: string | string[];
  unit = '';
  innerValue: undefined | string | string[] = [];

  get componentProps() {
    return this.config.componentProps || { multiple: false };
  }
  get placeholder() {
    return this.componentProps.placeholder;
  }
  get multiple() {
    return this.componentProps.multiple;
  }
  @Watch('value', { immediate: true })
  handleValueChage() {
    this.innerValue = this.value || (this.multiple ? [] : '');
  }
  // 单个tag输入校验
  validateMethod(val) {
    // 是否包含特殊字符
    return val.match(FIELD_REG);
  }
  handleChange() {
    this.$refs.tagInput && (this.$refs.tagInput as any).validate();
    // 是否需要拼接单位
    this.$emit('change', this.innerValue);
  }
}
</script>
<style lang="scss" scoped></style>
