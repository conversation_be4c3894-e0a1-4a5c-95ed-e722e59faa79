<template>
  <pro-page fixed-header :active-tab="activeTab" :tabs="tabPaneList" @tab-click="handleTabClick">
    <!-- Tab切换（基本信息、历史版本、引用关系） -->
    <bs-download v-if="activeTab === 'relation'" slot="operation" :url="url">
      <el-button size="small" type="primary"> 下载引用关系 </el-button>
    </bs-download>

    <!-- tab：基本信息 -->
    <info v-if="activeTab === 'info'" :id="id" :version="version" />
    <!-- tab：历史版本 -->
    <history v-if="showHistory" :id="id" />
    <!-- tab：引用关系 -->
    <relation v-if="showRelation" :id="id" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import Info from './components/info.vue';
import History from './components/history.vue';
import Relation from './components/relation.vue';

@Component({ components: { Info, History, Relation } })
export default class TemplateCepDetail extends Vue {
  private activeTab = 'info';
  private tabPaneListInit = [
    { label: '基本信息', value: 'info' },
    { label: '历史版本', value: 'history' },
    { label: '引用关系', value: 'relation' }
  ];
  private tabPaneList = this.tabPaneListInit;
  columnData: any = [];
  url = 'rs/pa/flink/cep/downloadRef?id=' + this.id;

  get id() {
    return this.$route.query ? this.$route.query.id : '';
  }

  get version() {
    const version = this.$route.query.version;
    if (version) {
      this.tabPaneList = [{ label: '基本信息', value: 'info' }];
    } else {
      this.tabPaneList = this.tabPaneListInit;
    }
    return this.$route.query.version;
  }

  get showHistory() {
    return !this.version && this.activeTab === 'history';
  }

  get showRelation() {
    return !this.version && this.activeTab === 'relation';
  }

  handleTabClick(val: string) {
    this.activeTab = val;
  }
}
</script>

<style lang="scss" scoped>
.event {
  &-container {
    position: relative;
    width: 100%;
    height: 100%;
    //overflow: auto;
  }
}
</style>
