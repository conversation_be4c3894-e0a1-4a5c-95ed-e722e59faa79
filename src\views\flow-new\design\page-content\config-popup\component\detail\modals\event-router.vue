<template>
  <bs-dialog
    append-to-body
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    :confirm-button="{ disabled }"
    @close="closeDialog(false)"
    @confirm="submit('ruleForm')"
  >
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-position="left"
      :label-width="isEn ? '150px' : '80px'"
      class="form-content"
    >
      <el-form-item :label="$t('pa.flow.label22')" prop="resId">
        <el-tooltip effect="light" :disabled="!routeInfo">
          <div slot="content">
            <div v-for="(route, index) in routeInfo" :key="route.route + index">
              <div class="route-title">{{ $t('pa.params.template.detail.route') }}{{ index + 1 }}</div>
              <div class="route-body">
                <div>
                  <span>{{ $t('pa.flow.target') }}：</span>{{ route.target.toString() }}
                </div>
                <div v-for="(rule, index1) in route.rules" :key="rule.fieldName + index1">
                  {{ $t('pa.flow.condition') }}{{ index1 + 1 }}：{{ rule.fieldName }} ({{ $t('pa.flow.category') }}：{{
                    rule.fieldType
                  }})
                  {{ rule.operator }}
                  {{ rule.fieldValue }}
                </div>
              </div>
            </div>
          </div>
          <el-select
            v-model="ruleForm.id"
            :placeholder="$t('pa.flow.placeholder33')"
            clearable
            filterable
            :disabled="disabled"
            style="width: 100%"
          >
            <el-option v-for="rule in ruleList" :key="rule.id" :label="rule.routeTemplateName" :value="rule.id" />
          </el-select>
        </el-tooltip>
      </el-form-item>
      <el-form-item :label="$t('pa.flow.label13')" prop="updateInterval">
        <el-input v-model="ruleForm.updateInterval" type="number" :disabled="disabled">
          <template slot="append">{{ $t('pa.flow.s') }}</template>
        </el-input>
      </el-form-item>
    </el-form>
    <print-log slot="footer-left" v-model="printLog" :disabled="disabled" />
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { get } from '@/apis/utils/net';
import { cloneDeep } from 'lodash';
import PrintLog from './components/print-log.vue';

@Component({
  components: {
    PrintLog
  }
})
export default class EventRouter extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop({ default: false }) disabled!: boolean;

  printLog = false;
  ruleForm: any = {
    id: '',
    resId: '',
    updateInterval: 600
  };
  rules: any = {
    id: [{ required: true, message: this.$t('pa.flow.placeholder33'), trigger: 'change' }],
    updateInterval: [{ required: true, message: this.$t('pa.flow.msg202'), trigger: 'blur' }]
  };

  ruleList: any = [];
  get title() {
    const { nodeName = '', componentName = '' } = this.data || {};
    return this.$t('pa.flow.msg288', [nodeName, componentName]);
  }
  async created() {
    await this.getList();
    const { properties } = this.data;
    this.printLog = this.data.printLog || false;
    if (properties) {
      this.ruleForm = properties;
    }
  }

  async getList() {
    // 获取模板列表
    const { data, success } = await get('/rs/pa/route/findAll');
    if (success) {
      this.ruleList = data;
    }
  }

  get routeInfo() {
    if (this.ruleList.length > 0 && this.ruleForm.id) {
      let { routeInfo = '' } = this.ruleList.find((item) => item.id === this.ruleForm.id);
      /* eslint-disable-next-line */
      routeInfo = eval(routeInfo);
      return Array.isArray(routeInfo) && routeInfo.length === 0 ? '' : routeInfo;
    } else {
      return '';
    }
  }

  submit(formName: any) {
    const nodeDot = cloneDeep(this.data);
    const formRef: any = this.$refs[formName];
    formRef.validate((valid) => {
      if (valid) {
        nodeDot.outputFields = nodeDot.inputFields;
        nodeDot.outputFields.forEach((item) => {
          item.outputable = true;
        });
        nodeDot.properties = this.ruleForm;
        nodeDot.properties.resId = this.getRuleResId();
        nodeDot.printLog = this.printLog;
        this.closeDialog(true, nodeDot);
      }
    });
  }
  getRuleResId() {
    const target = this.ruleList.find(({ id }) => id === this.ruleForm.id);
    return target && target.resId ? target.resId : null;
  }

  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    return { needUpdate, jobNode };
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__content .el-input-group {
  vertical-align: baseline;
}
.route-title {
  font-size: 14px;
  font-weight: bolder;
  padding-bottom: 10px;
}
.route-body {
  padding-bottom: 10px;
  div {
    padding: 3px;
  }
}
</style>
