<template>
  <bs-dialog
    :title="$t('pa.action.add')"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
  >
    <el-form ref="formData" :model="formData" :rules="rules" label-position="left" label-width="80px" class="form-content">
      <el-form-item :label="$t('pa.params.template.detail.file')" prop="file">
        <el-upload
          ref="upload"
          action
          :on-change="handleFile"
          :limit="1"
          accept=".xlsx"
          :auto-upload="false"
          :on-exceed="exceed"
          :before-remove="removeFile"
        >
          <div class="form-content--upload">
            <el-button type="primary">{{ $t('pa.params.template.detail.selectFile') }}</el-button>
            <div slot="tip" class="el-upload__tip">
              {{ $t('pa.params.template.detail.onlyExcel') }}
              <a @click.prevent="downloadTemplate">（ {{ $t('pa.params.template.detail.downloadTemplate') }} ）</a>
            </div>
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">{{ $t('pa.action.cancel') }}</el-button>
      <el-button type="primary" @click="submit('formData')">{{ $t('pa.action.confirm') }}</el-button>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { file } from '@/apis/utils/net';

@Component
export default class ImportModal extends Vue {
  @Prop({ default: false }) visible!: boolean;

  formData: any = {
    file: null
  };

  rules: any = {
    file: [{ required: true, validator: this.validateFile }]
  };

  validateFile(rule, value, callback) {
    if (value === null) {
      callback(new Error(this.$t('pa.tip.uploadFiles', ['']) as string));
    } else {
      callback();
    }
  }
  downloadTemplate() {
    window.open((process.env.NODE_ENV === 'development' ? '' : '.') + '/rs/pa/mappingWarehouse/download');
  }

  handleFile(data: any) {
    this.formData.file = data.raw;
    (this.$refs['formData'] as any).validate();
  }

  removeFile() {
    this.formData.file = null;
  }

  exceed() {
    this.$message.warning(this.$t('pa.params.template.detail.onlyOneExcel'));
  }

  closeDialog(needFresh = false) {
    this.$emit('close', needFresh);
  }
  submit(formName: string) {
    (this.$refs[formName] as any).validate(async (valid: boolean) => {
      if (valid) {
        const form: any = Object.assign({}, this.formData);
        const { msg, success } = await file('/rs/pa/mappingWarehouse/upload', form, {
          method: 'post',
          headers: { ContentType: 'multipart/form-data' }
        });
        if (success) {
          this.$message.success(msg);
          this.closeDialog(true);
        } else {
          this.formData.file = null;
          (this.$refs.upload as any).clearFiles();
          this.$message.error(msg);
        }
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.form-content {
  &--upload {
    display: flex;
    align-items: center;
  }
  .el-upload__tip {
    display: inline-block;
    margin-left: 10px;
    margin-top: unset;
  }
  .add-btn {
    display: flex;
    padding: 7px 12px;
    margin-top: 5px;
    width: 416px;
    justify-content: center;
  }
  .contact {
    text-align: left;
    &-delete {
      font-size: 16px;
      cursor: pointer;
    }
    .form-item-input {
      margin-right: 16px;
      width: 200px;
    }
  }
}
</style>
