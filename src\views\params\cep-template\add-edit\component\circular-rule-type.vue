<template>
  <div class="circular__container">
    <!-- 单选规则 -->
    <el-form-item :prop="`${prefix}.circularRuleTimes`" :rules="circularRuleTimesRule">
      <el-select
        v-model="formData.groupCepPatternConditionList[scopeIndex].circularRuleTimes"
        clearable
        filterable
        size="small"
        placeholder="请选择"
        :disabled="disabled"
        class="circular__radio"
        @change="$emit('change', scopeIndex)"
      >
        <el-option
          v-for="item in circularRule"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <!-- 多选规则 -->
    <el-form-item :prop="`${prefix}.excessRuleTimesList`">
      <el-select
        v-model="formData.groupCepPatternConditionList[scopeIndex].excessRuleTimesList"
        clearable
        filterable
        multiple
        collapse-tags
        size="small"
        placeholder="请选择"
        :disabled="disabled"
        class="circular__checkbox"
      >
        <el-option
          v-for="item in excessRule"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class CircularRuleType extends Vue {
  @Prop({ type: Number }) scopeIndex!: number;
  @Prop({ type: Object, default: () => ({}) }) formData!: any;

  private circularRule: any[] = [
    {
      label: 'times',
      value: 'TIMES'
    },
    {
      label: 'timesOrMore',
      value: 'TIMES_OR_MORE'
    },
    {
      label: 'oneOrMore',
      value: 'ONE_OR_MORE'
    }
  ];
  private excessRule: any[] = [
    {
      label: 'optional',
      value: 'OPTIONAL'
    },
    {
      label: 'greedy',
      value: 'GREEDY'
    }
  ];

  get prefix() {
    return `groupCepPatternConditionList[${this.scopeIndex}]`;
  }
  get circularRuleTimesRule() {
    return {
      required: !this.disabled,
      message: '请选择',
      trigger: 'change'
    };
  }
  get disabled() {
    return this.formData.groupCepPatternConditionList[this.scopeIndex].isNotConneModeType;
  }
}
</script>

<style lang="scss" scoped>
.circular {
  &__container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  /* 单选规则 */
  &__radio {
    width: 138px;
  }
  /* 多选规则 */
  &__checkbox {
    width: 158px;
    ::v-deep .el-input__inner {
      height: 28px !important;
    }
  }
}
</style>
