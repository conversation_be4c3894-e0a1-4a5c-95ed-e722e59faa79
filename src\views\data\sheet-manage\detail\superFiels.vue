<template>
  <div>
    <div class="head-info">
      <div class="name">高级字段</div>
      <div>
        <el-button v-if="status !== '1'" type="primary" style="marginleft: 10px" @click="chartAdd">
          添加字段
        </el-button>
      </div>
    </div>
    <el-table
      :data="fields"
      style="
        width: 100%;
        max-height: 400px;
        overflow: auto;
        border: 1px solid #f1f1f1;
        border-bottom: none;
      "
      size="mini"
    >
      <el-table-column prop="type" label="字段分类" width="280">
        <template slot-scope="{ row }">
          {{ getType(row.advanceFieldType, typeList) }}
        </template>
      </el-table-column>
      <el-table-column prop="type" label="字段名" min-width="130">
        <template slot-scope="{ row }">
          <div v-if="row.advanceFieldType === 'WATERMARK'">
            <span style="margin-right: 100px">{{ row.field }}</span>
            {{ row.column1 }} {{ getType(row.column2, timeType) }}
          </div>
          <div v-else>
            {{ row.field }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="tabTip">
      {{ error }}
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
@Component({})
export default class SuperField extends PaBase {
  @Prop({
    default() {
      return [];
    }
  })
  fields!: any[];
  @Prop({
    default() {
      return [];
    }
  })
  table!: any[];
  @Prop() status!: '0' | '1' | '2';
  @Prop() error!: '';
  typeList: any = [
    {
      label: '水位线',
      value: 'WATERMARK'
    },
    {
      label: '处理时间',
      value: 'PROCTIME'
    },
    {
      label: '自定义字段',
      value: 'OTHER'
    }
  ];
  timeType: any = [
    {
      label: '时',
      value: 'HOUR'
    },
    {
      label: '分',
      value: 'MINUTE'
    },
    {
      label: '秒',
      value: 'SECOND'
    }
  ];

  // 水平线字段选择
  baseField: any = [];

  @Watch('fields', { immediate: true, deep: true })
  fieldsChange(array) {
    if (Array.isArray(array)) {
      const line = array.find((item) => item.advanceFieldType === 'WATERMARK') ? true : false;
      const time = array.find((item) => item.advanceFieldType === 'PROCTIME') ? true : false;
      this.typeList = this.typeList.map((item) => {
        return {
          ...item,
          disabled: (item.value === 'PROCTIME' && time) || (item.value === 'WATERMARK' && line)
        };
      });
    }
  }

  //水平线字段名选项
  @Watch('table', { immediate: true, deep: true })
  change(list) {
    const res = list.filter((item) => item.fieldType === 'TIMESTAMP(3)');
    this.baseField = res.map((tar) => {
      return {
        label: tar.fieldName,
        value: tar.fieldName
      };
    });
  }

  getType(type, typeList) {
    const value = typeList.find((item) => item.value === type);
    return value && value['label'];
  }
}
</script>
<style lang="scss" scoped>
.head-info {
  line-height: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
  .name {
    font-size: 14px;
    font-weight: 500;
  }
}
.tabTip {
  color: red;
}
</style>
