<template>
  <div>
    <div class="head-info">
      <div class="name">{{ $t('pa.data.seniorFields') }}</div>
    </div>
    <el-table
      :data="fields"
      style="width: 100%; max-height: 400px; overflow: auto; border: 1px solid #f1f1f1; border-bottom: none"
      size="mini"
    >
      <el-table-column prop="type" :label="$t('pa.data.table.detail.fieldClassify')" width="280">
        <template slot-scope="{ row }">
          {{ getType(row.advanceFieldType, typeList) }}
        </template>
      </el-table-column>
      <el-table-column prop="type" :label="$t('pa.data.table.detail.fieldName')" min-width="130">
        <template slot-scope="{ row }">
          <div v-if="row.advanceFieldType === 'WATERMARK'">
            <span style="margin-right: 100px">{{ row.field }}</span>
            {{ row.column1 }} {{ getType(row.column2, timeType) }}
          </div>
          <div v-else>
            {{ row.field }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="tabTip">
      {{ error }}
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Watch, Vue } from 'vue-property-decorator';
@Component({})
export default class SuperField extends Vue {
  @Prop({
    default() {
      return [];
    }
  })
  fields!: any[];
  @Prop({
    default() {
      return [];
    }
  })
  table!: any[];
  @Prop() error!: '';
  typeList: any = [
    {
      label: this.$t('pa.waterLevelLine'),
      value: 'WATERMARK'
    },
    {
      label: this.$t('pa.handleTime'),
      value: 'PROCTIME'
    },
    {
      label: this.$t('pa.customFields'),
      value: 'OTHER'
    }
  ];
  timeType: any = [
    {
      label: this.$t('pa.unit.hour'),
      value: 'HOUR'
    },
    {
      label: this.$t('pa.unit.minute'),
      value: 'MINUTE'
    },
    {
      label: this.$t('pa.unit.second'),
      value: 'SECOND'
    }
  ];

  // 水平线字段选择
  baseField: any = [];

  @Watch('fields', { immediate: true, deep: true })
  fieldsChange(array) {
    if (Array.isArray(array)) {
      const line = array.find((item) => item.advanceFieldType === 'WATERMARK') ? true : false;
      const time = array.find((item) => item.advanceFieldType === 'PROCTIME') ? true : false;
      this.typeList = this.typeList.map((item) => {
        return {
          ...item,
          disabled: (item.value === 'PROCTIME' && time) || (item.value === 'WATERMARK' && line)
        };
      });
    }
  }

  //水平线字段名选项
  @Watch('table', { immediate: true, deep: true })
  change(list) {
    const res = list.filter((item) => item.fieldType === 'TIMESTAMP(3)');
    this.baseField = res.map((tar) => {
      return {
        label: tar.fieldName,
        value: tar.fieldName
      };
    });
  }

  getType(type, typeList) {
    const value = typeList.find((item) => item.value === type);
    return value && value['label'];
  }
}
</script>
<style lang="scss" scoped>
.head-info {
  line-height: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
  .name {
    font-size: 14px;
    font-weight: 500;
  }
}
.tabTip {
  color: red;
}
</style>
