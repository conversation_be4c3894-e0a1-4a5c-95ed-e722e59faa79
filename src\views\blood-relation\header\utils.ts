import i18n from '@/i18n';
export const FORM_RULES = {
  resType: [{ required: true, message: i18n.t('pa.resType'), trigger: 'blur' }],
  serviceType: [{ required: true, message: i18n.t('pa.serviceTypeRule'), trigger: 'blur' }],
  serviceName: [{ required: true, message: i18n.t('pa.serviceName'), trigger: 'blur' }],
  flowType: [{ required: true, message: i18n.t('pa.flowType'), trigger: 'blur' }],
  serviceId: [{ required: true, message: i18n.t('pa.serviceId'), trigger: 'blur' }],
  resourceList: [{ required: true, message: i18n.t('pa.resourceList'), trigger: 'blur' }]
};

export const FLOW_TYPE_OPTION = [
  {
    value: 'SQL',
    label: i18n.t('pa.sqlFlow')
  },
  {
    value: 'PROCESSFLOW',
    label: i18n.t('pa.dsFlow')
  }
];

export const GET_FORM_DEFAULT_VALUE = () => {
  return {
    serviceType: '',
    serviceName: '',
    flowType: '',
    serviceId: '',
    resourceList: ''
  };
};

export const LABLE_MAPPING = Object.freeze({
  SERVICE: i18n.t('pa.flow.serveName'),
  TABLE: i18n.t('pa.tableName'),
  CATALOG: i18n.t('pa.catalogName'),
  JOB: i18n.t('pa.process')
});
