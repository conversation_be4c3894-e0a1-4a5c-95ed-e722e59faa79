<template>
  <div>
    <el-form-item
      v-for="el in renderList"
      v-show="!el.hidden"
      :key="el.name"
      :prop="el.name"
      :label="el.label"
      :rules="rules[el.name]"
    >
      <div :class="['resource-item', el.tip ? 'resource-item--tip' : '']">
        <!-- select -->
        <el-select
          v-if="el.type === 'select'"
          :key="selectKey"
          v-model="form[el.name]"
          filterable
          clearable
          :placeholder="el.placeholder"
          :popper-append-to-body="true"
          @change="handleSelectChange"
        >
          <el-option-group v-for="group in clusterGroup" :key="group.label" :label="group.label">
            <el-option
              v-for="item in group.options"
              :key="item[clusterModelKey]"
              :value="item[clusterModelKey]"
              :label="`${item.title}${item.url ? '（' + item.url + '）' : ''}`"
            >
              <el-popover v-if="(item.title + item.url).length >= 50" placement="top-start" trigger="hover">
                <p>{{ `${item.title}${item.url ? '（' + item.url + '）' : ''}` }}</p>
                <span slot="reference">{{
                  `${item.title}${item.url ? '（' + item.url + '）' : ''}`.slice(0, 45) + '...'
                }}</span>
              </el-popover>
            </el-option>
          </el-option-group>
        </el-select>
        <!-- number -->
        <el-input-number
          v-if="el.type === 'number'"
          v-model="form[el.name]"
          number
          :min="el.min"
          :max="el.max"
          :step="el.step ? el.step : 1"
          :placeholder="el.placeholder"
          :disabled="el.disabled"
        />
        <el-checkbox v-if="el.type === 'checkbox'" v-model="form[el.name]" :disabled="el.disabled">
          {{ el.desc }}
        </el-checkbox>
        <div v-if="el.name === 'log4jFile'" style="position: relative">
          <el-link v-if="log4jFile || log4jFileInCluster" :underline="false" type="primary">
            {{ log4jFile || log4jFileInCluster }}
          </el-link>
          <bs-upload
            ref="bsUpload"
            action=""
            accept=".properties"
            :limit="1"
            :auto-upload="false"
            :upload-text="form.log4jFile ? '重新上传' : '选择文件'"
            :on-change="handleFieldChange"
            :on-remove="handleFieldRemove"
            :file-list="fileList"
            tip-text="只支持properties格式"
          />
          <div v-if="!enableDFS" class="bs-upload-mask" @click="handleUploadClick"></div>
        </div>
      </div>
      <el-tooltip v-if="el.tip" effect="light" :content="el.tip" placement="bottom">
        <i class="iconfont icon-wenhao base-icon"></i>
      </el-tooltip>
    </el-form-item>
  </div>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Watch, Vue } from 'vue-property-decorator';
import { cloneDeep } from 'lodash';
import { getFlinkList } from '@/apis/flowNewApi';
import { FLOW_DEFAULT_CONFIG } from '../config';
import ClearEnter from '@/common/clear-enter';
interface SelectedCluster {
  clusterType: string;
  id: string;
  title: string;
  url: string;
  clusterDetail?: string;
  custom?: string;
  deployTimeout?: number;
  log4jFilePath?: string;
}
@Component({
  mixins: [ClearEnter]
})
export default class BaseConfig extends Vue {
  @Prop({ required: true, default: false }) isCloud!: boolean;
  @Prop({ required: true, default: false }) isFlinkSql!: boolean;
  @PropSync('data', { required: true, default: FLOW_DEFAULT_CONFIG() }) form!: any;
  // 集群列表-分组
  private clusterGroup: any[] = [
    { label: 'YARN_PER_JOB', options: [] },
    { label: 'STANDALONE', options: [] },
    { label: 'YARN_APPLICATION', options: [] },
    { label: 'YARN_SESSION', options: [] },
    { label: this.$t('pa.cloudService'), options: [] }
  ];
  private clusterList: any[] = []; // 集群列表：用于处理集群id是否在集群列表中
  private hideTaskMemoryAndSlotNum = false;
  private isRequested = false; // 判断接口是否已经调用过，调用过再进行集群信息校验
  private selectKey = 0;
  fileList = [];
  log4jFile = '';
  // 集群信息中带的log4j地址
  log4jFileInCluster = '';
  // 校验规则：当集群类型是CLOUD时，【taskManagerRequestCpu】必填
  get rules() {
    return {
      [this.clusterRenderKey]: [
        {
          required: true,
          validator: this.validatorCluster,
          trigger: 'change'
        }
      ],
      taskManagerMemory: [{ required: true, message: this.$t('pa.tip.taskManagerMemory'), trigger: 'blur' }],
      taskManagerSlotNumber: [{ required: true, message: this.$t('pa.tip.taskManagerSlotNumber'), trigger: 'blur' }],
      taskManagerRequestCpu: [{ required: this.isCloud, message: this.$t('pa.tip.taskManagerRequestCpu'), trigger: 'blur' }],
      parallelism: [{ required: true, message: this.$t('pa.flow.placeholder11'), trigger: 'blur' }]
    };
  }

  // 判断已选集群是否在集群列表中
  get isInClusterList() {
    const clusterOptions = cloneDeep(this.clusterGroup)
      .map((el) => el.options)
      .flat(Infinity);
    return clusterOptions.some((el) => el.id === this.form.clusterId);
  }

  get hasClusterOption() {
    return cloneDeep(this.clusterGroup)
      .map((el) => el.options)
      .flat(Infinity).length;
  }

  // form数据中对应的key
  get clusterRenderKey(): string {
    return (this.form.clusterId && !this.isInClusterList) || !this.hasClusterOption ? 'clusterDetail' : 'clusterId';
  }

  // select-option组件绑定的key
  get clusterModelKey(): string {
    return (this.form.clusterId && !this.isInClusterList) || !this.hasClusterOption ? 'clusterDetail' : 'id';
  }

  // 渲染列表
  get renderList() {
    return [
      {
        name: this.clusterRenderKey,
        label: this.$t('pa.clusterName'),
        type: 'select'
      },
      {
        name: 'taskManagerMemory',
        label: this.$t('pa.taskManagerMemory'),
        type: 'number',
        min: 0,
        max: 1048576,
        tip: this.$t('pa.tip.taskMgrMemory'),
        hidden: this.hideTaskMemoryAndSlotNum
      },
      {
        name: 'taskManagerSlotNumber',
        label: this.$t('pa.taskManagerSlotNumber'),
        placeholder: this.$t('pa.placeholder.taskManagerSlotNumber'),
        type: 'number',
        min: 0,
        max: 99,
        tip: this.$t('pa.tip.taskMgrSlotNumber'),
        hidden: this.hideTaskMemoryAndSlotNum
      },
      {
        name: 'taskManagerRequestCpu',
        label: this.$t('pa.taskManagerRequestCpu'),
        placeholder: this.$t('pa.placeholder.taskManagerRequestCpu'),
        type: 'number',
        min: 0,
        max: 99,
        step: 0.1,
        tip: this.$t('pa.tip.taskMgrRequestCpu'),
        hidden: !this.isCloud
      },
      {
        name: 'parallelism',
        label: this.$t('pa.flow.bingxingdu'),
        placeholder: this.$t('pa.flow.placeholder11'),
        type: 'number',
        min: 1,
        max: 100,
        disabled: this.parallelismDisabled
      },
      {
        name: 'isApplyParallelism',
        label: '',
        desc: this.$t('pa.tip.isApplyParallelism'),
        type: 'checkbox',
        disabled: this.parallelismDisabled,
        hidden: this.isFlinkSql || this.isJar
      },
      {
        name: 'log4jFile',
        label: this.$t('pa.log4jFile'),
        type: 'custom',
        hidden: this.showLog4jFileDisabled
      }
    ];
  }

  // 非开发状态不可修改并行度
  get parallelismDisabled() {
    return this.form?.jobStatus !== 'DEV';
  }

  // 是否为jar类型的流程
  get isJar() {
    return this.form?.jobType === 'jar';
  }
  // 是否配置分布式文件系统
  get enableDFS() {
    return this.$store.getters.enableDFS;
  }

  // 服务类型为'YARN_PER_JOB'且jar类型的流程显示log4jFile表单项
  get showLog4jFileDisabled() {
    return this.form?.clusterType !== 'YARN_PER_JOB' || !this.isJar;
  }

  /* 监听orgId变换，获取集群列表 */
  @Watch('form.orgId', { immediate: true })
  async getClusterList(val: string) {
    if (!val || (this as any)._isDestroyed) return;
    try {
      this.isRequested = false;
      const { success, data, error } = await getFlinkList({
        orgId: this.$store.getters.orgId
      });
      if (success) {
        this.clusterList = data;
        this.isRequested = true;
        this.handleClusterGroup(data);
        this.handleSelectChange(this.form.clusterId, true);
        return;
      }
      this.$message.error(error);
    } catch (e) {
      this.clusterGroup.forEach((el, index) => {
        this.$set(this.clusterGroup[index], 'options', []);
      });
    }
  }
  @Watch('form.log4jFile', { immediate: true })
  handleLog4jFileChange(val: string) {
    if (typeof val === 'string') {
      // 上传过log4jFile后，返回的url进行回显
      this.log4jFile = this.form?.log4jFile;
    }
  }

  validatorCluster(rule, value, callback) {
    if (!this.isRequested) {
      callback();
      return;
    }
    if (this.form.clusterDetail || this.form.clusterId) {
      callback();
    } else {
      callback(new Error(this.$t('pa.params.template.detail.servicePlaceholder')));
    }
    const isExist = this.clusterList.some((el) => el.id === this.form.clusterId);
    if (!isExist) {
      callback(new Error(this.$t('pa.tip.clusterNotExist')));
      return;
    }
    callback();
  }
  // 按照集群类型进行分类，构造分组下拉列表
  handleClusterGroup(data: any[] = []) {
    let clusterList: any[] = Array.isArray(data) ? [...data] : [];
    clusterList = clusterList.map((item) => {
      const { id, title, url, namespace, queue, deployTimeout, clusterType = '', log4jFilePath } = item;
      return { id, title, url, clusterType, deployTimeout, custom: namespace || queue || '', log4jFilePath };
    });
    const clusterMap = {
      STANDALONE: this.clusterGroup[1].options,
      YARN_APPLICATION: this.clusterGroup[2].options,
      CLOUD: this.clusterGroup[4].options
    };
    clusterList.forEach((item) => clusterMap[item.clusterType].push(item));
    // 已经选择过集群，但该集群已不在列表中
    if ((this.form.clusterId && !this.isInClusterList) || !this.hasClusterOption) {
      this.form.resTitle &&
        (this.form.clusterDetail = `${this.form.resTitle}${this.form.url ? '（' + this.form.url + '）' : ''}`);
      this.clusterGroup.forEach((el) => {
        el.options.forEach((ele) => (ele.clusterDetail = `${ele.title}${ele.url ? '（' + ele.url + '）' : ''}`));
      });
    }
  }

  /* 集群变化*/
  handleSelectChange(id: string, auto = false) {
    const [{ options: n1 }, { options: n2 }, { options: n3 }, { options: n4 }, { options: n5 }] = this.clusterGroup;

    // 选中项在集群列表中
    const clusterItem = [...n1, ...n2, ...n3, ...n4, ...n5]
      .filter(Boolean)
      .find((el) => id && el[this.clusterModelKey] === id);
    const { clusterType, clusterId, resTitle, url, namespace, queue } = this.form;
    let selectedCluster: SelectedCluster | null = null;
    if (clusterItem) {
      selectedCluster = cloneDeep(clusterItem);
    } else if (!clusterItem || (!this.isInClusterList && clusterId !== clusterItem.id) || !this.hasClusterOption) {
      // 选中项不在集群列表中，重新组装option数据
      selectedCluster = {
        clusterType,
        id: clusterId,
        title: resTitle,
        url,
        clusterDetail: `${resTitle}${url ? '（' + url + '）' : ''}`,
        custom: namespace || queue || '',
        deployTimeout: 90
      };
    }
    if (selectedCluster) {
      const { clusterType, title, url, deployTimeout } = selectedCluster;
      // 当选中Standalone、yarn session模式，隐藏task manager内存、task manager slot个数;
      this.hideTaskMemoryAndSlotNum = ['STANDALONE', 'YARN_SESSION'].includes(clusterType);
      this.$set(this.form, 'url', url);
      this.$set(this.form, 'resTitle', title);
      this.$set(this.form, 'clusterType', clusterType);
      this.$set(this.form, 'clusterId', selectedCluster.id);
      this.$set(this.form, 'property', cloneDeep(selectedCluster));
      selectedCluster.id && this.$set(this.form, this.clusterModelKey, selectedCluster.clusterDetail);
      if (!auto) {
        this.$set(this.form, 'deployTimeout', deployTimeout);
        this.$set(this.form, 'namespace', '');
        this.$set(this.form, 'queue', '');
      }
      this.selectKey = Date.now();
      this.log4jFileInCluster = selectedCluster.log4jFilePath || '';
      return;
    }
    this.$set(this.form, 'url', '');
    this.$set(this.form, 'property', {});
    this.$set(this.form, 'resTitle', '');
    this.$set(this.form, 'clusterType', '');
  }

  handleUploadClick() {
    this.$message.warning(this.$t('pa.tip.dfs'));
  }
  handleFieldChange(data) {
    if (data.size > Math.pow(1024, 3)) {
      this.$message.warning(this.$t('pa.tip.size1GB'));
      return (this.fileList = []);
    }
    this.form.log4jFile = data.raw;
  }
  handleFieldRemove(file, fileList) {
    this.fileList = fileList;
    fileList.length === 0 && (this.form.log4jFile = undefined);
  }
}
</script>

<style scoped lang="scss">
.base {
  &-icon {
    margin-left: 10px;
  }
}
::v-deep .bs-upload {
  line-height: 1;
  .el-upload__tip {
    display: inline;
    margin-top: 0px;
    margin-left: 10px;
  }
}
.bs-upload-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 102px;
  height: 28px;
  background: transparent;
  cursor: pointer;
}
</style>
