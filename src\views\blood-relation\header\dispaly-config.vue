<template>
  <el-popover
    v-model="dispaly"
    trigger="click"
    placement="bottom"
    :append-to-body="false"
    popper-class="display-popover"
  >
    <!-- 表单 -->
    <el-form ref="formRef" :model="formData">
      <!-- 表单内容 -->
      <template v-for="(el, index) in formConfig">
        <div
          :key="el.label"
          class="display-item"
          :class="{ 'display-item--noborder': index === formConfig.length - 1 }"
        >
          <!-- 标题 -->
          <div class="display-item__title display-gap">{{ el.label }}</div>
          <!-- 内容 -->
          <template v-for="item in el.children">
            <div
              v-if="item.name === 'B2'"
              :key="item.name"
              class="display-item__resource display-gap"
            >
              <el-checkbox v-model="formData[item.name]">
                {{ item.label }}
              </el-checkbox>
              <el-select v-model="formData.C1" size="small" multiple collapse-tags>
                <el-option
                  v-for="op in options"
                  :key="op.value"
                  :label="op.label"
                  :value="op.value"
                />
              </el-select>
            </div>
            <el-checkbox v-else :key="item.name" v-model="formData[item.name]" class="display-gap">
              {{ item.label }}
            </el-checkbox>
          </template>
        </div>
      </template>
      <!-- 按钮 -->
      <div style="text-align: right; padding: 0 14px">
        <el-button size="small" @click="reset">重置</el-button>
        <el-button type="primary" size="small" @click="click">确定</el-button>
      </div>
    </el-form>
    <!-- 触发者 -->
    <el-tooltip slot="reference" content="显示信息" effect="light" placement="top">
      <i class="iconfont icon-xianshixinxi display-icon"></i>
    </el-tooltip>
  </el-popover>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import ElForm from 'bs-ui-pro/packages/form';

@Component
export default class DisplayConfig extends Vue {
  @Prop({ default: false }) isFullScreen!: boolean;
  @PropSync('data', { default: () => ({}) }) formData!: any;
  @Ref('formRef') readonly form!: ElForm;

  private dispaly = false;
  private options: any[] = [
    {
      label: 'aerospike',
      value: 'aerospike'
    },
    {
      label: 'dts',
      value: 'dts'
    },
    {
      label: 'elasticsearch',
      value: 'elasticsearch'
    },
    {
      label: 'ftp',
      value: 'ftp'
    },
    {
      label: 'hbase',
      value: 'hbase'
    },
    {
      label: 'hdfs',
      value: 'hdfs'
    },
    {
      label: 'hive',
      value: 'hive'
    },
    {
      label: '主机',
      value: 'host'
    },
    {
      label: '外部http接口',
      value: 'externalInterface'
    },
    {
      label: 'kafka',
      value: 'kafka'
    },
    {
      label: '数据库',
      value: 'database'
    },
    {
      label: 'pulsar',
      value: 'pulsar'
    },
    {
      label: 'rabittMq',
      value: 'rabittMq'
    },
    {
      label: 'redis',
      value: 'redis'
    },
    {
      label: 'rocketMQ',
      value: 'rocketMQ'
    },
    {
      label: 'socket',
      value: 'socket'
    },
    {
      label: '计算引擎',
      value: 'computingEngine'
    }
  ];
  private formConfig: any[] = [
    {
      label: '点边明细',
      children: [
        {
          type: 'checkbox',
          name: 'showNodeLabel',
          label: '显示节点值'
        },
        {
          type: 'checkbox',
          name: 'showLineLabel',
          label: '显示边信息'
        },
        {
          type: 'checkbox',
          name: 'showHomologyNodes',
          label: '显示同源信息'
        }
      ]
    }
    // {
    //   label: '节点类型',
    //   children: [
    //     {
    //       type: 'checkbox',
    //       name: 'B1',
    //       label: '显示流程'
    //     },
    //     {
    //       type: 'checkbox',
    //       name: 'B2',
    //       label: '显示资源'
    //     },
    //     {
    //       type: 'checkbox',
    //       name: 'B3',
    //       label: '显示表'
    //     },
    //     {
    //       type: 'checkbox',
    //       name: 'B4',
    //       label: '显示视图'
    //     }
    //   ]
    // }
  ];

  reset() {
    this.form.resetFields();
    this.formData = { showNodeLabel: true, showLineLabel: true, showHomologyNodes: true };
    this.$message.success({
      message: '重置成功',
      el: this.isFullScreen ? '#bloodRelation' : ''
    });
  }

  click() {
    this.dispaly = false;
    this.$emit('submit');
  }
}
</script>
<style lang="scss">
.display {
  &-popover {
    padding: 16px 0 !important;
    width: 274px;
    background: #ffffff;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    box-sizing: border-box;
  }
}
</style>
<style lang="scss" scoped>
.display {
  &-gap {
    display: block;
    margin-bottom: 16px;
  }
  &-item {
    padding: 0 14px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
    font-weight: 400;
    color: #444444;
    line-height: 20px;
    &--noborder {
      border: 0;
    }
    &__title {
      font-weight: 500;
    }
    &__resource {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .el-select {
        width: 160px;
      }
    }
  }
  &-icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin-left: 16px;
    width: 32px;
    height: 32px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 4px;
    border: 1px solid #dbd9d9;
  }
}
</style>
