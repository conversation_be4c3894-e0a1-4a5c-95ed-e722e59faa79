{"pa": {"AdvancedConnectorAttr": "高級連接器屬性", "action": {"action": "操作", "add": "新建", "addNewRow": "添加一行", "addRow": "添加行", "allSelect": "全選", "analysis": "解析", "applyAll": "應用全部", "batchAdjust": "批量調整", "batchDel": "批量刪除", "batchUAdd": "批量新增", "buildOneself": "自建", "cancel": "取消", "cancelAll": "全部取消", "close": "關閉", "confirm": "確認", "copy": "複製", "cover": "覆蓋", "create": "創建", "dataClear": "清除無效數據", "del": "刪除", "detail": "詳情", "download": "下載", "downloadRelation": "下載引用關係", "edit": "編輯", "editCustom": "編輯[{0}]", "editIcon": "編輯圖標", "editIconCustom": "編輯圖標[{0}]", "enable": "啓用", "execute": "執行", "export": "導出", "import": "導入", "makeSure": "確定", "mockSubmit": "模擬提交", "offline": "停止", "online": "啓動", "output": "輸出", "preview": "預覽", "recycle": "回收", "refresh": "刷新", "register": "註冊", "resourceAllocate": "資源分配", "rollBack": "回滾", "run": "運行", "save": "保存", "search": "查詢", "selectAll": "全部選擇", "share": "分享", "skip": "跳過", "staging": "暫存", "submit": "提交", "syncTopic": "同步集羣Topic", "terminal": "終端", "test": "測試", "testConnect": "測試連接", "tip": "提示", "transfer": "轉換", "update": "更新", "upload": "上傳", "uploadFiles": "上傳文件", "versionContrast": "版本比對", "view": "查看", "viewSourceCode": "查看源碼", "viewVersion": "查看歷史版本"}, "addOrg": "創建機構", "adjustCpu": "調整CPU", "adjustCpuTo": "調整CPU為:", "adjustMemoryMB": "調整內存(MB)", "adjustMemoryTo": "調整內存為:", "adjustSlots": "調整Slots", "adjustSlotsTo": "調整Slots為:", "advancedParams": "高級參數", "all": "全部", "allOrg": "全部機構", "allQueue": "全部隊列", "argument": "參數", "argumentType": "參數類型", "assets": {"title1": "數據管理", "title2": "數據定義", "title3": "第三方類庫", "title4": "方法", "title5": "導入導出"}, "attempts": "嘗試次數", "backoffMultiplier": "增長乘數", "baseConnectorAttr": "基礎連接器屬性", "baseInfo": "基本信息", "baseInformation": "基本信息", "basicParams": "基本參數", "basisChange": "以下任一情況作為變更依據：1、配置的數據定義在平台內不存在；2、配置的數據定義與平台內相同全類名數據定義的內容不一致", "batch": "批量", "batchText0": "您確定要刪除選中的 [${name}] ${num}條記錄嗎？", "batchText1": "您選中的 [${name}] ${num}條記錄被關聯，無法刪除！[${name}] ${num}條記錄沒有關聯，您確定要刪除嗎？", "batchText2": "您選中的 [${name}] ${num}條記錄被關聯，無法刪除！", "blood": {"center": "居中", "dep": "依賴上游", "dynamic": "動態", "exitFullScreen": "退出全屏", "fit": "適應畫布", "fullScreen": "全屏", "homologyInfo": "同源信息", "homologyNode": "同源節點", "nodeName": "節點名稱", "nodeType": "節點類型", "queryNode": "查詢節點", "relatedInfo": "關聯信息", "searchblood": "查詢血緣", "serviceType": "服務類型", "size": "實際尺寸", "tip1": "請在左側選擇查詢節點，將以查詢節點為中心點進行血緣探索", "tip10": "血緣關係中節點不同，但是其實際代表的物理集羣服務地址+資源（例如kafka服務ip:port+topic）相同或有交集", "tip11": "暫無數據權限，不支持查看", "tip12": "當前節點已與查詢節點形成循環關係，不支持收縮", "tip13": "單擊查看節點詳情", "tip14": "，雙擊將該節點作為查詢節點", "tip2": "暫無數據權限，無法查詢該節點血緣", "tip3": "畫布中存在節點與查詢節點形成循環，已自動展開環上節點的直接關聯節點", "tip4": "靜態服務（配置服務時指定具體資源）：請在資源輸入框輸入名稱查詢；", "tip5": "依賴上游服務（配置服務時使用上游字段值來指定具體資源）：請在資源輸入框輸入“依賴上游數據”關鍵詞；", "tip6": "無資源服務（配置服務時未指定具體資源）：請在資源輸入框輸入“無“關鍵詞", "tip7": "支持以流程（已發佈）、表、Catalog、部分服務資源（靜態、依賴上游、無資源）作為查詢節點，暫不支持動態服務作為查詢。服務查詢提示：", "tip8": "支持以流程（已發佈）、部分服務資源（靜態、依賴上游、無資源）作為查詢節點，暫不支持動態服務作為查詢。服務查詢提示：", "tip9": "查詢條件不完整，請檢查"}, "cacheInfo": "緩存信息", "cacheQuery": "緩存查詢", "canNotSync": "無法同步，", "cataLog": {"test6": "Catalog詳情", "text1": "catalog名稱", "text2": "請輸入catalog名稱", "text3": "catalog名稱需要以字母開頭且只包含字母、數字與下劃線", "text5": "Catalog管理"}, "catalogName": "Catalog名稱", "checkLoading": "正在進行數據校驗，請稍等...", "checkpointInterval": "checkpoint週期", "choseFile": "選擇文件", "citationRelation": "引用關係", "closing": "關閉中", "cloudService": "雲服務", "cluster": "集羣", "clusterAddress": "集羣地址", "clusterMemory": "集羣總內存", "clusterName": "集羣名稱", "clusterRemainingMemory": "集羣剩餘內存", "clusterResidualCpu": "集羣剩餘CPU", "clusterResidualMemory": "集羣總CPU", "clusterResidualSlots": "集羣剩餘slots", "clusterResourceUsage": "集羣資源使用情況", "clusterSlots": "集羣總slots", "clusterUsedCpu": "集羣已使用CPU", "clusterUsedMemory": "集羣已使用內存", "clusterUsedSlots": "集羣已使用slots", "codeCompare": "源碼比對", "codeOrName": "請輸入編碼或名稱", "comments": "請輸入描述", "company": "邦盛科技", "compareVersion": "對比版本：{0}", "componentLibMgr": "組件庫管理", "confirmDialog": "字段【{0}】有對應的輸入字段但未選中。點擊確定將丟棄未選中配置，是否要繼續提交？", "consume": "消費查看", "copies": "副本數", "cpFailed": "可容忍的檢查點故障", "cpMinPause": "checkpoint最小停頓時間", "cpTimeout": "checkpoint超時時間", "cpUnaligned": "是否開啓未對齊檢查點", "cpuCores": "CPU核數", "creator": "創建人", "cronExpression": "cron表達式", "currentCpu": "當前剩餘CPU", "currentMemory": "當前剩餘內存（MB）", "currentSlots": "當前剩餘slots", "customFields": "自定義字段", "customParams": "自定義參數", "data": {"baseFields": "基礎字段", "codeView": "源碼查看", "copyError": "複製失敗", "copySuccess": "複製成功", "option": {"addOption": "新建選項", "optionName": "選項名稱", "optionType": "選項類型", "placeholder": {"optionNamePlaceholder": "請輸入選項名稱", "optionTypePlaceholder": "請選擇選項類型"}, "validateTip": "不能以數字開頭"}, "seniorFields": "高級字段", "table": {"addTable": "新建表", "change": "變更", "copyTable": "複製表", "detail": {"addAttribute": "添加屬性", "addField": "添加字段", "attribute": "屬 性", "baseConnector": "基礎連接器", "businessCaliber": "業務口徑", "chineseName": "中文名", "columnCluster": "列簇", "connector": "連接器", "connectorNature": "連接器屬性", "creatorPhone": "創建人電話", "databaseType": "數據庫類型", "excess3": "多於3", "fieldClassify": "字段分類", "fieldName": "字段名", "fieldType": "字段類型", "lessThan2": "少於2", "partition": "分區", "placeholder": {"fieldPlaceholder1": "請輸入字段名", "fieldPlaceholder2": "請在基礎字段表內添加字段類型=“TIMESTAMP(3)”字段", "servicePlaceholder1": "請選擇服務", "servicePlaceholder2": "請選擇數據庫類型", "tablePlaceholder1": "請選擇前綴名", "tablePlaceholder2": "請填寫表名", "tablePlaceholder3": "請選擇後綴名", "tablePlaceholder4": "請輸入業務口徑"}, "primaryKey": "主鍵", "seniorConnector": "高級連接器", "serviceAddress": "服務地址", "serviceInfo": "服務信息", "serviceType": "服務類型", "sqlParsing": "SQL解析", "sqlPreview": "SQL預覽", "sync": "同步", "tableFields": "表字段", "tableInfo": "表信息", "tableName": "表名", "template": "模板", "tips": {"delConfirm": "您確定要刪除{0}字段嗎？", "fieldRFepeat": "列表內存在{0}個重複字段，請刪除不必要的字段", "hbaseTip1": "HBase服務類型：表字段只能設置1個主鍵", "hbaseTip2": "HBase服務類型：表字段必須要設置1個主鍵", "hbaseTip3": "HBase服務類型：表字段除主鍵行字段外，其他字段必須設置列簇", "hbaseTip4": "字段信息為必填項，請完善字段信息後重試", "leastOne": "字段信息或高級表字段需要至少填寫一項", "linkerTip": "請填寫連接器值", "needToDel": "請選擇需要刪除的字段", "noPermissions": "暫無當前{0}數據權限，請重新選擇", "noServePermissions": "暫無當前服務數據權限，請重新選擇", "notAllowedTip": "字段不允許{0}個", "notEmpty": "{0}不能為空", "repeat": "存在重複字段，請修改後提交", "repeatTips": "存在重複字段，請修改", "seniorLinkerTip1": "請填寫高級連接器屬性字段", "seniorLinkerTip2": "高級連接器屬性字段屬性值不能重複，請重試", "syncSuccess": "共同步{0}個字段", "templateTooltip": "不同{0}類型的模板會有差異，請按照{1}類型進行下載使用。", "type2": "上傳文件不大於10MB"}, "value": "值", "zipPackage": "zip包"}, "editTable": "編輯表", "incomplete": "待完善", "placeholder": {"inputPlaceholder1": "請輸入表名稱，中文名稱", "inputPlaceholder2": "請輸入字段名，中文名", "inputPlaceholder3": "請選擇水位線字段名", "inputPlaceholder4": "請選擇水位線數值", "inputPlaceholder5": "請選擇水位線單位", "inputPlaceholder6": "請輸入處理時間字段名", "inputPlaceholder7": "請輸入自定義字段名", "inputPlaceholder8": "高級字段不能為空", "select": "請選擇服務類型"}, "tooltip": {"tooltip1": "表結構變更，請及時同步", "tooltip2": "flink內置字段，處理時間、水位線等", "tooltip3": "連接器高級屬性配置"}}, "text1": "引用數量", "text10": "只能上傳excel類型的文件!", "text11": "不可導入原因", "text12": "請上傳zip格式文件", "text13": "點擊導入", "text14": "資源導出", "text15": "資源導入導出", "text16": "資源導入", "text17": "將導出的資源文件中的數據增量導入到平台中。", "text18": "下列資產請在執行導入操作前完成準備，以提升導入效率：", "text19": "1、機構、用户、角色等數據請通過SQL方式導入導出 或 在導入環境重新創建", "text2": "引用當前資產的下游資產數量", "text20": "2、服務請確保導入前在導入環境完成註冊（推薦服務類型、名稱保持一致，以便系統自動匹配服務信息）", "text21": "點擊導出", "text22": "3、流程組件請通過【", "text23": "】中的上傳和下載功能", "text24": "4、方法、第三方類庫、數據定義等底層資產請通過【", "text25": "數據管理-導入導出", "text26": "】功能", "text27": "將平台中的流程（含所屬項目和目錄）、選項、UDF、表、Catalog、SQL片段、模板導出。", "text28": "將平台中的流程（含所屬項目和目錄）、選項、模板導出。", "text29": "流程前綴", "text3": "你確定要刪除{0}字段嗎？", "text30": "流程後綴", "text31": "目錄", "text32": "請選擇資產類型", "text33": "請輸入ID或名稱搜索", "text34": "不可導入校驗", "text35": "重複性校驗", "text36": "下一步", "text37": "上一步", "text4": "不能上傳超過10MB的文件!", "text5": "第二步：上傳模板文件", "text7": "不同服務類型的模板會有差異，請按照服務類型進行下載使用。", "text8": "1.文件格式ZIP\n           2.大小不能超過10MB", "text9": "請上傳文件", "udf": {"addUdf": "新建UDF", "detail": {"SyncAll": "全部同步", "addMode": "創建方式", "addPhone": "創建電話", "check": "校驗", "correlationType": "關聯方法", "downloadFile": "下載模板文件", "explain": "功能説明", "fieldSync": "字段同步", "individualTest": "單獨方法測試", "manualWriting": "手動編寫", "methodName": "方法名", "mockTest": "模擬環境測試", "param1": "參數1", "param2": "參數2", "placeholder": {"addTypePlaceholder": "請選擇創建方式", "domainPlaceholder": "請填寫域名", "explainPlaceholder": "1.入參描述  2.描述 3.功能説明", "methodNamePlaceholder": "請輸入方法名搜索", "name": "請填寫UDF名", "typeInputPlaceholder": "請填寫類型", "typePlaceholder": "請選擇類型", "updataPlaceholder": "請填寫更新內容"}, "returnType": "返回類型", "savePrompt": "保存提示", "selectFile": "選擇文件", "testData": "測試數據", "tips": {"StagingSuccess": "暫存成功", "addSuccess": "新建成功", "codeTip": "源碼不能為空", "delConfirm": "確定刪除此行？", "dominTip": "請輸入英文", "downloadSteps1": "第一步：下載模板文件", "importCoverTip": "導入將覆蓋之前的數據，是否覆蓋？", "importTip": "注意:導入後原數據將被清空，請謹慎操作！", "nameTip": "UDF名稱需要以字母開頭且只包含字母、數字與下劃線", "numberOfTests": "測試數據不允許超過5個", "onTests": "必須保留一行測試數據", "sourceTip": "關聯方法作為靜態方法添加在DefaultGlobalFunction類中。用户進行關聯後可在UDF源碼中以 DefaultGlobalFunction.關聯方法名稱(參數) 的方式使用。", "zipTip": "上傳zip包的UDF需要提交後才能測試!"}, "type": "類型", "udfDomainName": "UDF主類全域名", "udfName": "UDF名", "uploadZip": "上傳zip包"}, "editUdf": "編輯UDF", "placeholder": {"udfExplainPlaceholder": "請輸入功能説明", "udfNamePlaceholder": "請輸入UDF名，中文名", "udfPlaceholder": "請選擇UDF類型"}, "submit": "已提交", "viewUdf": "查看UDF"}}, "dataDict": "數據字典", "dataPreview": "數據預覽", "dataViewMode": "查看數據方式", "databaseName": "database名稱", "defaultCode": "查詢語句（{0}）", "delData": "請選擇需要刪除的數據", "delay": "固定重啓延遲時間", "deployTimeout": "上線超時時間", "differentEncoding": "編碼和上級編碼不能相同", "disableOperatorChain": "是否打斷算子鏈", "docs": "文檔", "downloadFailed": "下載失敗", "dsFlow": "Datestream流程", "editCategory": "編輯分類", "elementLoading": "配置加載中", "enableCheckPoint": "是否開啓checkpoint", "encoding": "編碼", "encodingPlaceholder": "請輸入編碼", "errorDetail": "錯誤信息詳情", "evictedObject": "淘汰對象", "executionLog": "執行日誌", "expiredObject": "過期對象", "failureRateDelay": "失敗重啓延遲時間", "failureRateInterval": "時間間隔", "failuresPerInterval": "重啓次數", "fieldConfig": "字段配置", "fieldInfo": "字段信息", "fieldName": "字段名稱", "fieldParams": "字段參數", "fieldType": "字段類型", "fields": "字段", "file": "文件", "filterConditionList": "過濾條件列表", "flink": "加工引擎", "flow": {"3mm": "最近三個月", "add": "增加", "addCondition": "增加條件", "addData": "添加數據", "addNextRow": "添加下一行", "addTest": "添加測試用例", "address": "地址", "all": "全部", "allClose": "全部關閉", "allOpen": "全部開啓", "attr": "屬性", "autoSaved": "已自動備份", "autoSaving": "自動備份中", "autoUpdateField": "可自動更新字段", "baseConnector": "基礎連接器", "baseInfo": "​基礎信息", "baseInfo1": "基本信息", "baseParam": "基本參數", "batch": "批量操作", "batchCancel": "取消批任務", "batchCancelConfirm": "確定要取消該批量操作嗎？", "batchCancelFailed": "取消失敗，任務已開始運行", "batchCancelPublish": "批量取消發佈", "batchCancelSuccess": "取消成功", "batchKill": "強制停止", "batchKillConfirm": "確定要強制停止該批量操作嗎？", "batchKillFailed": "強制停止失敗", "batchKillSuccess": "強制停止成功", "batchMode": "批模式", "batchOffline": "批量停止", "batchOnline": "批量啟動", "batchOperation": "批量操作", "batchOperationDetail": "批量操作詳情", "batchOperationFailed": "批量操作添加失敗", "batchOperationInfo": "批量任務", "batchOperationName": "批操作名稱", "batchOperationNameLength": "長度在 1 到 50 個字符", "batchOperationNamePlaceholder": "請輸入批操作名稱", "batchOperationNameRequired": "請輸入批操作名稱", "batchOperationSuccess": "批量操作添加成功，可點擊批量任務按鈕查看", "batchPublish": "批量發佈", "batchRestart": "批量重啟", "batchSize": "批處理數量", "bingxingdu": "並行度", "bloodRelation": "血緣關係", "business": "業務口徑", "cFlow": "流程複製", "cancel": "取消", "cancelAll": "全部取消", "cancelPublish": "取消發佈", "canclePublish": "取消發佈中", "canvas": "畫布", "category": "類型", "chineseName": "中文名", "className": "全類名", "close": "關閉", "clusterTest": "測試集羣", "clusterType": "集羣類型", "code": "源碼", "codeKey": "片段縮略詞", "codeLibrary": "代碼片段庫", "codeName": "片段名稱", "comConfig": "組件配置", "comInfo": "組件信息", "comLoading": "組件加載中", "commonConfig": "通用配置", "compile": "編譯", "complier": "編譯中", "component": "流程組件", "component1": "組件", "componentName": "組件名稱", "componentNum": "組件數", "componentType": "組件類型", "condition": "條件", "config1": "配置", "config2": "條件配置", "config3": "配置條件", "config4": "其他配置", "configDetail": "配置明細", "configErr": "配置錯誤", "configMsg1": "的配置未完成！", "configMsg2": "的【", "configMsg3": "】字段未配置！", "configured": "已配置", "confirm": "確定", "connector": "連接器", "connectorAttr": "連接器屬性", "connectorVal": "屬性值", "copy": "複製", "copyFlow": "複製流程", "copyed": "已複製", "creatFlow": "新建流程", "createDir": "新建目錄", "createOrg": "創建機構", "createProject": "新建項目", "createRow": "添加行", "createSubDir": "新建子目錄", "createTime": "創建時間", "creater": "創建人", "csvTrans": "csv解析", "currentVersion": "當前版本", "customCron": "自定義：{0}", "customField": "自定義字段", "customSize": "自定義JAR包", "d": "日", "dDir": "刪除目錄", "dataMap": "數據集", "deadline": "截止時間", "defaultMethod": "默認方法", "del": "刪除", "delFlow": "刪除流程", "delMsg": "確認刪除嗎", "delMsg1": "您確定要刪除該行數據嗎?", "delMsg2": "您確定要刪除選中的數據嗎", "delProject": "刪除項目", "deng": "等", "design": "流程設計", "detail": "查看詳情", "dev": "開發", "eDir": "編輯目錄", "eFlow": "編輯流程", "edit": "編輯", "editContent": "編輯區", "editProject": "編輯項目", "editSql": "編輯SQL片段", "editSuccess": "修改成功", "editTime": "修改時間", "editor": "修改人", "endDate": "結束日期", "endSession": "終止會話", "errorMessage": "錯誤信息", "exit": "退出批量", "export": "導出", "exportLog": "導出日誌", "fDir": "父級目錄", "fenqu": "分區", "field": "字段", "fieldConfig": "字段配置", "fieldInfo": "字段信息", "fieldMap": "字段映射", "fieldMapTitle": "字段映射配置", "fieldName": "字段名", "fieldName1": "字段名稱", "fieldParam": "字段參數", "fieldTrans": "字段轉換", "fieldTransMethod": "字段轉換方式", "fieldType": "字段類型", "fieldTypeMap": "字段類型映射", "fieldUpdateTip": "字段自動更新提示", "file": "文件採集", "filter": "篩選", "filter1": "流程篩選", "flow": "流程", "flowMemo": "流程備註", "flowName": "流程名稱", "flowRelation": "引用流程：", "flowRunInfo": "流程運行信息", "flowTest": "流程測試", "forceStop": "強制停止", "format": "YYYY年MM月DD日 HH:mm", "format1": "YYYY年MM月DD日", "from": "來自", "gen": "生成", "genSql": "表字段生成sql", "getBatchDetailFailed": "獲取批量操作詳情失敗", "getBatchInfoFailed": "獲取批量操作信息失敗", "h": "時", "h1": "小時", "highParam": "高級參數", "hignConnector": "高級連接器", "hignLevel": "高級", "hignMode": "高級模式", "historyRun": "歷史運行情況", "historyRun1": "歷史運行", "hiveConfig": "HIVE配置參數", "http": "HTTP 輸入", "indexField": "索引字段", "indicatorId": "指標ID", "indicatorName": "指標名稱", "inputCompent": "輸入組件", "inputField": "輸入字段", "jarPkg": "流程jar包", "jdbcType": "數據庫類型", "jsonTtrans": "json轉換", "key": "鍵", "key1": "左流key字段", "key10": "計算引擎", "key11": "策略", "key12": "維度值", "key13": "推送字段", "key14": "參數名", "key15": "參數值", "key16": "定時模式", "key17": "表匹配方式", "key18": "指定唯一表", "key19": "動態表", "key2": "左流輸出字段", "key20": "動態表名", "key21": "是否UPSERT", "key22": "更新字段", "key23": "批量大小", "key3": "右流key字段", "key4": "右流輸出字段", "key5": "分區字段", "key6": "HIVE字段", "key7": "HIVE字段類型", "key8": "上游輸入字段類型", "key9": "上游輸入字段", "keyBy": "keyBy字段", "label1": "全‘&&’關係", "label10": "事件時間", "label11": "亂序時間", "label12": "過濾模板", "label13": "更新頻率", "label14": "映射模板", "label15": "系統編號", "label16": "映射字段", "label17": "源代碼值", "label18": "源代碼含義", "label19": "標準代碼值", "label2": "全‘||’關係", "label20": "標準代碼含義", "label21": "系統編號字段", "label22": "路由模板", "label23": "左流配置", "label24": "key字段", "label25": "輸出字段前綴", "label26": "右流配置", "label27": "join方式", "label28": "內連接", "label29": "時間下界", "label3": "自定義關係", "label30": "時間上界", "label31": "允許亂序時間", "label32": "時間類型", "label33": "左連接", "label34": "右連接", "label35": "窗口類型", "label36": "滑動窗口", "label37": "滾動窗口", "label38": "窗口大小", "label39": "滑動步長", "label4": "方法入參", "label40": "生成方式", "label41": "枚舉值", "label42": "小數位", "label43": "數據範圍", "label44": "字符類型", "label45": "時間範圍", "label46": "輸出方式", "label47": "模擬輸出", "label48": "真實輸出", "label49": "用例名稱", "label5": "上游組件字段", "label50": "用例數據", "label51": "手動添加", "label52": "csv輸入", "label53": "隨機造數", "label54": "數據預覽", "label55": "數據修改", "label56": "最大反壓比", "label57": "checkpoint失敗次數", "label58": "checkpoint最大時間", "label59": "checkpoint平均時間", "label6": "閾值", "label60": "taskManager最大使用內存", "label61": "jobManager最大使用內存", "label62": "GC次數", "label63": "流程運行時間", "label64": "最大輸入流量", "label65": "最大輸出流量", "label66": "數據定義全類名輸出", "label67": "數據定義字段", "label68": "是否本地分區", "label69": "更新主鍵", "label7": "延時時間", "label70": "週期模式", "label71": "基於主鍵查詢", "label72": "基於更新時間查詢", "label73": "基於索引字段查詢", "label74": "普通分頁查詢", "label75": "基於JDBC流式查詢", "label76": "禁用", "label77": "跳過NULL值", "label78": "請選擇是否啓用跳過NULL值功能", "label79": "請選擇跳過NULL值設置", "label8": "單位秒", "label80": "啓用後，當輸入字段值為NULL時，將跳過更新該字段，保持數據庫中的原有值不變。僅在UPSERT模式下生效", "leaveMsg": "確定離開當前頁面嗎？", "level": "維度", "loadingTip": "拼命加載中...", "log": "日誌", "logInfo": "日誌信息", "logText": "輸入輸出日誌", "logicConfig": "指標配置( {0} )", "logicConfig1": "指標配置", "lowData": "低質量數據", "m": "分", "m1": "分鐘", "mFlow": "流程移動", "mainKey": "主鍵", "maxRow": "最大行數", "maxRunTime": "最大運行時間", "mei": "每", "meiD": "每日", "meiH": "每小時", "meiM": "每分鐘", "meiS": "每秒", "memo": "描述", "memoPlaceholder": "請輸入備註信息（可選）", "mgr": "流程管理", "mm": "月", "mm1": "本月", "mockTest": "模擬輸入測試", "mode": "流程模式", "monitor": "流程監控", "monitorMgr": "監控管理", "monitorRule": "監控規則名稱", "move": "移動", "moveFlow": "移動流程", "msg1": "請先保存再啓動會話！", "msg10": "流程首次啓動，或者修改kafka consumer group等信息後需要從頭開始消費數據。", "msg100": "第({0})條記錄的名稱與其他字段名有重複", "msg101": "第({0})條記錄未配置完整", "msg102": "第({0})條記錄方法的參數未配置完整", "msg103": "請選擇計算引擎", "msg104": "請選擇命名空間", "msg105": "請選擇維度", "msg106": "請選擇指標名", "msg107": "請輸入指標ID", "msg108": "指標ID不可重複", "msg109": "請選擇需要刪除的指標", "msg11": "流程重啓，需要接着上次checkpoint記錄的位置，如kafka上次的offset位置繼續消費。", "msg110": "選擇上游字段的某個字段作為維度值進行查詢", "msg111": "選擇上游字段的某個字段作為維度值進行查詢，若是不選擇則使用默認值", "msg112": "請配置指標", "msg113": "指定上游的某個字段作為報送的值，該值需是一個包含@type的JSONString字符串", "msg114": "指定推送生效的腳本列表，若是不指定則默認所有腳本都生效", "msg115": "請輸入服務地址", "msg116": "請選擇策略", "msg117": "請選則維度值", "msg118": "請進行指標配置", "msg119": "請輸入批處理數量", "msg12": "即flink savepoint，用於暫停流程，流程重新啓動，保證精準一次語義。", "msg120": "請輸入超時時間", "msg121": "請輸入存放指標查詢結果的對應字段", "msg122": "結果字段不可重複", "msg123": "（已選擇{0}項）", "msg124": "請配置規則表達式", "msg125": "第{0}條記錄未配置完整", "msg126": "第{0}條記錄的名稱與其他字段名有重複", "msg127": "請選擇數據庫類型", "msg128": "請選擇服務名稱", "msg129": "請選擇要查詢的表名，若是基於正則匹配多個表名進行查詢，可以寫為${正則表達式}的格式，如${table[0-9]}，正則地查詢對匹配的表不會動態發現", "msg130": "選擇一些數據庫的字段, 這些字段將會被查詢並輸出到下游", "msg131": "請選擇輸出字段", "msg132": "請輸入where條件，可以為空", "msg133": "where條件不能輸入?", "msg134": "請選擇查詢方式", "msg135": "請選擇時間字段", "msg137": "單位毫秒，默認100 * 1000", "msg138": "請輸入分頁大小", "msg139": "指定查詢的起始時間，非必填。合法的時間格式將會失去焦點後自動轉換成時間戳。", "msg14": "確認刪除這些流程嗎?", "msg140": "請輸入區間大小", "msg141": "請輸入起始時間", "msg142": "請輸入合法時間表達式", "msg143": "請選擇索引字段", "msg144": "即每次查詢的最大記錄條數，默認100條", "msg145": "當數據庫中沒有新數據後，下一次查詢的間隔時間.單位毫秒，默認為60000。", "msg146": "請輸入間隔時間", "msg147": "遊標重置模式", "msg148": "請選擇表匹配方式", "msg149": "請選擇表，並配置映射字段信息", "msg150": "請輸入動態表名", "msg151": "表名支持支持時間宏，如使用時間宏則表名錶達式必須用table_{yyyyMMdd}形式，匹配到的表的字段應完全一致", "msg152": "請選擇是否UPSERT", "msg153": "若選擇`否`，則為`insert`語句; 若選擇`是`，則會根據主鍵進行`insert`或`update`，主鍵是自動獲取的", "msg154": "指定update時更新的字段，未選擇的字段將不會更新，默認更新所有字段。", "msg155": "每次寫入數據庫時的記錄條數，默認100條", "msg156": "適當的增加批量大小可以增加數據的寫入速度", "msg157": "超過該時間，若是數據量還未積攢到指定的批量大小，則會輸出，單位為`秒`", "msg158": "文本內容有空格，請確認輸入是否有誤", "msg159": "請配置表對應的映射字段", "msg160": "注意：處理規則引用的方法中，如需輸入固定參數，需要對特殊字符串進行轉義，如\"需寫為\\\\\"。", "msg161": "第{0}條記錄方法的參數未配置完整", "msg162": "組件的輸出字段=自定義字段+選中的上游輸入字段", "msg163": "順序:自定義字段>上游輸入字段", "msg164": "請輸入自定義字段", "msg165": "請填寫自定義字段", "msg166": "標籤不可以包含特殊字符，僅可以包含字母，數字，_-!@#￥%&*()", "msg167": "只能輸入一個輸出字段", "msg168": "不能輸入重複字段", "msg169": "自定義字段不能與上游輸入字段重複", "msg17": "確定無狀態啓動嗎？", "msg170": "請輸入全路徑表名", "msg171": "請至少選擇一項上游輸入字段", "msg172": "請處理錯誤提示信息，再提交", "msg173": "基本參數中[{0}]所填字段名與字段配置中的字段有重複", "msg174": "基本參數中[數據庫名/表名]與[全路徑表名]，至少填寫一個。", "msg175": "基本參數中[數據庫名]和[表名]要麼都填，要麼都不填。", "msg176": "基本參數中[參數列名]、[自定義參數列名]，至少填寫一個。", "msg177": "基本參數中所填字段名與字段配置中的字段有重複", "msg178": "請檢查輸入內容", "msg179": "請輸入輸出字段", "msg18": "您確定要{0}{1}{2}{3}個流程嗎？", "msg180": "存在重複輸出數據,請重新輸入", "msg181": "請輸入合法的邏輯關係！", "msg182": "條件數量已經達到上限", "msg183": "您確定要刪除配置條件{0}嗎?", "msg184": "請輸入合法的邏輯表達式", "msg185": "條件{0}", "msg186": "邏輯關係不完整：{0}沒有被寫入表達式！", "msg187": "條件配置有誤請檢查條件配置項！", "msg188": "請選擇上游組件字段", "msg189": "請輸入閾值", "msg190": "長度在 1 到 50 個字符", "msg191": "{0}已經被刪除", "msg192": "指定一個字段作為keyBy", "msg193": "時間語義", "msg194": "指定一個字段作為時間字段", "msg195": "最大允許亂序時間，單位秒", "msg196": "條件配置A", "msg197": "條件配置B", "msg198": "請輸入延時時間", "msg199": "請選擇延時時間", "msg2": "流程並未改變，不需要保存", "msg20": "流程最多支持100個節點", "msg200": "請選擇時間語義", "msg201": "請輸入亂序時間", "msg202": "請填寫更新頻率", "msg203": "選擇上個節點輸出的字段，作為json字段提取組件的輸出", "msg204": "請填寫輸出字段", "msg205": "請在左側輸入您要轉換的json", "msg206": "請填寫左側流的join字段", "msg207": "請填寫左側流的時間字段", "msg208": "請填寫左側流的輸出字段的前綴", "msg209": "請填寫左側流的輸出字段", "msg21": "流程{0}還沒保存，確定離開嗎？", "msg210": "請填寫右側流的join字段", "msg211": "請填寫右側流的時間字段", "msg212": "請填寫右側流的輸出字段的前綴", "msg213": "請填寫右側流的輸出字段", "msg214": "請選擇流的join方式", "msg215": "請輸入時間上界大小，單位秒", "msg216": "允許亂序的時間", "msg217": "允許亂序的時間,單位秒", "msg218": "請輸入時間窗口大小，單位秒", "msg219": "請選擇時間類型", "msg22": "請先保存再配置資源！", "msg220": "請輸入名稱，長度不超過150字符", "msg221": "名稱中存在特殊字符{0}", "msg222": "請輸入導出字段，以逗號分隔", "msg223": "請先選擇日誌類型", "msg224": "請先選擇時間範圍再進行下載", "msg225": "沒有查詢到任何信息，請點擊繼續查詢獲取更多", "msg226": "請選擇用例", "msg227": "請輸入小數位數", "msg228": "請輸入時間範圍", "msg229": "請選擇字符類型", "msg23": "上游輸入字段", "msg230": "請輸入枚舉值", "msg231": "請選擇枚舉值", "msg232": "請輸入枚舉值，如有多個請用英文逗號分隔", "msg233": "請輸入數據範圍", "msg234": "開始範圍不能大於結束範圍", "msg235": "不能為空", "msg236": "{0}測試數據不能為空", "msg237": "{0}不能為空", "msg238": "{0}用例數據不能為空", "msg239": "{0}造數邏輯不能為空", "msg24": "流程解析失敗", "msg240": "請輸入用例名稱", "msg241": "請輸入用例數據", "msg242": "請選擇造數邏輯", "msg243": "請輸入行數", "msg244": "執行中，請稍等...", "msg245": "用例{0}__測試結果", "msg246": "請先設置流程", "msg247": "確定刪除【{0}】該條數據嗎?", "msg248": "注：若項目內存在流程將無法刪除，項目內無流程將會把項目和項目內所有目錄一起刪除", "msg249": "項目+目錄最多支持{0}級。", "msg25": "字段信息格式錯誤", "msg250": "注：非開發狀態的流程不能移動，系統已為您自動過濾", "msg251": "將{0}共{1}個流程移動至目標項目目錄", "msg252": "將{0}共{1}個流程複製至目標項目目錄，若選擇多個項目目錄，則每個項目目錄下都會新增所選流程", "msg253": "{0}個流程移動成功", "msg254": "{0}個流程移動成功，{1}個流程移動失敗，失敗原因如下：", "msg255": "流程名稱在目標項目中已存在：", "msg256": "流程狀態為非開發：", "msg257": "{0}個流程移動失敗，失敗原因如下：", "msg258": "查看目錄詳情", "msg259": "流程發佈失敗!詳細原因請點擊畫布行進行查看。", "msg26": "{0}保存提示", "msg260": "流程狀態非開發，不可編輯", "msg261": "所選流程狀態均為非開發，不可移動", "msg262": "確認刪除該目錄嗎？", "msg263": "注：若目錄下存在流程將無法刪除，目錄下無流程將會把目錄和所有子目錄一起刪除", "msg264": "僅支持已發佈的流程查看血緣關係", "msg265": "流程{0}有修改，請先保存再發布！", "msg266": "請在左側流程管理選擇一個流程查看，若無流程請先新建", "msg267": "只支持jar格式", "msg268": "請輸入全類名", "msg269": "請上傳流程jar包", "msg27": "{0}，請稍等...", "msg270": "該模式需要配置分佈式文件系統", "msg271": "只能上傳一個文件", "msg272": "只能上傳jar類型文件", "msg273": "文件大小限制1GB", "msg274": "所有采集組件的處理數據量總和", "msg275": "所有采集組件的處理數據量總和/運行時長（秒）", "msg276": "所有組件（平均傳輸延時+平均處理延時）總和", "msg277": "批量設置組件輸入輸出日誌開關", "msg278": "輸入輸出日誌開關用於控制流程發佈後是否在運行日誌中打印流程節點的輸入和輸出數據信息，開啓代表打印，關閉代表不打印。", "msg279": "組件{0}的【輸出字段】和【輸入字段】未配置完成！", "msg28": "正在努力生成sql腳本", "msg280": "組件{0}的【數據定義】未配置完成！", "msg281": "組件{0}的【字段映射】未配置完成！", "msg282": "組件{0}的【輸入字段】中，{1}字段不存在！", "msg283": "表別名僅支持字母、數字和下劃線", "msg284": "系統將獲取所有目標表的字段並按規範生成sql。例如：畫布已存在", "msg285": "配置表和別名（user別名為u，area別名為a）後點擊生成，則會生成下列sql：", "msg286": "注意：建議對生成sql進行檢查修改，以符合實際需求", "msg287": "快速獲取所有目標表的字段並生成sql", "msg288": "{0}節點配置（組件：{1}）", "msg289": "組件的輸出字段=選中的上游輸入字段", "msg29": "請選擇連接器和表用途", "msg290": "順序:上游輸入字段", "msg291": "表單項【{0}】解析validator函數報錯: {1}", "msg292": "請選擇上游輸入字段", "msg293": "字段【{0}】有對應的輸入字段但未選中。點擊確定將丟棄未選中配置，是否要繼續提交？", "msg294": "請選擇數據定義", "msg295": "請輸入字段搜索", "msg296": "開啓後，將會把數據定義全類名作為“@type”字段的值一起輸出；關閉則不輸出", "msg297": "將上方選中的數據定義字段以JSON格式組裝後放入該字段（全類名根據配置決定是否輸出）", "msg298": "不可以包含特殊字符，僅可以包含字母、數字、中劃線及下劃線", "msg299": "（已選擇{0}項作為輸出內容）", "msg3": "保存中，請稍等...", "msg30": "編譯中，請稍等...", "msg300": "請選擇關聯字段【{0}】的輸入字段", "msg301": "衍生字段表達式：{0}，無需配置輸入字段", "msg302": "請確保輸出的衍生字段【{0}】的關聯字段【{1}】選中", "msg303": "請選擇數據定義字段【{0}】對應的輸入字段", "msg304": "指定update時更新的字段，未選擇的字段將不會更新，默認更新所有字段", "msg305": "使用本地分區方式會將數據直接寫入分區表中", "msg306": "分區字段中存儲了本地分區後的分區表名例如table_202407", "msg307": "請選擇分區字段", "msg308": "請選擇更新主鍵", "msg309": "請先配置該字段的映射關係", "msg31": "請輸入流程名稱", "msg310": "根據數據表的主鍵進行增量查詢", "msg311": "需在業務側保證修改的數據的更新時間字段為最新，能夠讀取到包括更新在內的增量數據；時間字段需建立索引才能加快查詢效率，否則每次都會進行全表掃描", "msg312": "對於沒有主鍵的表，基於索引字段查詢，能夠加快查詢效率", "msg313": "使用分頁查詢，對於大數據量的表，越到後面查詢速率越慢", "msg314": "使用JDBC流式查詢，不提供狀態保存的能力，適合用於全表遷移；對於數據量越大的表，所需內存越大。建議使用多並行度進行查詢", "msg315": "請輸入字段", "msg316": "該開關用於控制流程發佈後是否在運行日誌中打印流程節點的輸入和輸出數據信息，開啓代表打印，關閉代表不打印。", "msg317": "灰色區域為上游輸入字段，不可修改字段名稱和類型", "msg318": "的名稱或者類型被修改", "msg319": "第", "msg32": "請選擇要操作的流程", "msg320": "條記錄的參數類型與處理規則不符", "msg321": "條記錄的處理規則不存在", "msg33": "請選擇要啓動的流程", "msg34": "流程上線失敗！", "msg35": "流程停止失敗", "msg36": "流程上線失敗", "msg37": "流程發佈失敗", "msg38": "流程發佈失敗!詳細原因請點擊畫布中異常SQL代碼行進行查看。", "msg39": "流程編譯失敗", "msg4": "請先保存再測試！", "msg41": "沒有需要發佈的流程", "msg42": "沒有需要取消發佈的流程", "msg43": "沒有需要上線的流程", "msg44": "您確定要{0}【{1}】個流程嗎？", "msg45": "沒有需要停止的流程", "msg46": "請先啓動會話", "msg47": "請選擇測試集羣", "msg48": "設置測試任務運行時間上限，當執行時間超過閾值時，自動終止測試任務；運行時間最大可設置為{0}秒。", "msg49": "設置測試任務執行結果的閾值上限，當測試任務產生的結果數據已達到閾值，則自動終止測試任務；行數最大可設置為{0}行。", "msg50": "是否確認回滾?", "msg51": "因上游字段的增刪導致以下組件字段出現變更，請確定是否進行自動更新", "msg52": "紅色字段：被刪除的輸出字段", "msg53": "綠色字段：增加的輸出字段", "msg54": "紅色字段：被刪除的字段", "msg55": "綠色字段：增加的字段", "msg56": "當前組件使用到的上游輸出字段，不包括可選字段", "msg57": "請輸入大於0小於100的整數", "msg58": "名稱中存在特殊字符", "msg59": "名稱已存在", "msg60": "點擊左側“流程組件”開始流程開發", "msg61": "存在配置異常組件", "msg62": "超過最大連接數：【{0}】最多支持{1}個輸入連接", "msg63": "組件【{0}】的輸出字段和組件【{1}】輸入字段不匹配。 {2} {3}", "msg64": "組件{0}{1}", "msg65": "{0}：【{1}】組件更新完將刪除{2}個字段，請檢查更新。", "msg66": "{0}為空：【{1}】組件更新完將刪除{2}個字段，導致當前組件的{3}為空，請檢查更新。", "msg67": "組件{0}的配置未完成！", "msg68": "組件{0}'的【表/視圖】字段未配置！", "msg69": "組件{0}'的【視圖】未選擇！", "msg7": "確認刪除該項目嗎?", "msg70": "組件{0}'未配置完成", "msg71": "組件{0}'【源表】未選擇！", "msg72": "組件{0}'【輸出表】未選擇！", "msg73": "組件{0}'【輸出表字段】未選擇！", "msg74": "請設置片段縮略詞，以便於在編輯區編寫代碼時通過 縮略詞+回車 快速使用SQL片段", "msg75": "請輸入非空格字符", "msg76": "確定刪除SQL片段？", "msg77": "首字符僅可輸入字母/數字/./", "msg78": "代碼片段庫用於展示具有複用性的公用/個人SQL片段", "msg79": "是由系統管理員維護，提供的SQL片段，", "msg80": "系統所有用户可用。", "msg81": "系統用户維護、僅供個人使用的SQL片段。", "msg82": "請輸入正確的鍵", "msg83": "至少選擇一條數據", "msg84": "請選擇字段【{0}】對應的輸入字段", "msg85": "請選擇主鍵和分區字段", "msg87": "僅支持如下格式的json語句：{\"key1\":\"value\",\"key2\":[1,2],\"key3\":{\"key4\":\"xx\"}}", "msg88": "請輸入json", "msg89": "請輸入解析內容", "msg90": "採用英文逗號分隔", "msg91": "字段{0}不存在", "msg92": "字段{0}重複", "msg93": "字段{0}已配置為{1}類型，再次配置將更新字段類型", "msg94": "至少保留一個類型映射行", "msg95": "最大支持新增20個類型映射行", "msg96": "請設置字段類型及數據精準度", "msg97": "請設置字段類型及字段信息", "msg98": "注意：處理規則引用的方法中，如需輸入固定參數，需要對特殊字符串進行轉義，如'需寫為\\'。", "msg99": "請完善信息", "name": "名稱", "nameSpace": "命名空間", "new": "新建", "new1": "新增", "newSql": "新建SQL片段", "no": "否", "noData": "暫無更多數據", "noStatusRun": "無狀態啓動", "nodeDetail": "節點詳情", "nodeLabel1": "輸入數據量", "nodeLabel10": "近一天數據處理量", "nodeLabel2": "輸出數據量", "nodeLabel3": "失敗數據量", "nodeLabel4": "平均傳輸延時", "nodeLabel5": "平均處理延時", "nodeLabel6": "預警數量", "nodeLabel7": "近一分鐘數據處理量", "nodeLabel8": "近十分鐘數據處理量", "nodeLabel9": "近一小時數據處理量", "notConfig": "未配置", "notInInputs": "不存在輸入字段中。", "notInOutputs": "不存在輸出字段中。", "numTotal": "數字總位數", "numTotal1": "小數點後位數", "online": "上線", "onlineMode": "間隔週期模式：{0}上線，每隔{1}小時{2}分執行", "onlineMode1": "時間週期模式：{0}上線，每天{1}運行", "onlineTime": "最新啓動時間", "onlineTip": "一次性上線：{0}上線", "onlining": "啓動中", "open": "開啓", "operationStatus": "操作狀態", "operationTarget": "操作對象", "operationTime": "操作時間", "operationType": "操作類型", "operator": "操作", "other": "其他", "output": "輸出", "outputChange": "上游輸出變更自動", "outputComponent": "輸出組件", "outputFields": "輸出字段", "pageSize": "分頁大小", "paramMap": "參數映射", "paramType": "參數類型", "params": "參數", "params1": "參數{0}", "personCodeLibrary": "個人SQL片段庫", "placeholder0": "請輸入", "placeholder1": "請輸入項目名稱模糊搜索", "placeholder10": "請輸入流程備註", "placeholder11": "請輸入並行度", "placeholder12": "請輸入最大運行時間", "placeholder13": "請輸入最大行數", "placeholder14": "請輸入標籤、備註", "placeholder15": "請輸入名稱，長度不超過200字符", "placeholder16": "請輸入片段縮略詞", "placeholder17": "請選擇輸入字段", "placeholder18": "不輸入默認使用英文逗號(,)", "placeholder19": "請選擇字段", "placeholder2": "請輸入流程名稱、集羣名稱", "placeholder20": "請輸入字段名稱", "placeholder21": "請選擇", "placeholder22": "請輸入內容", "placeholder23": "請選擇推送字段", "placeholder24": "請選擇keyBy字段", "placeholder25": "存放指標查詢結果的字段", "placeholder26": "指定table匹配方式，可指定唯一table或者通過時間表達式配置動態table", "placeholder27": "支持黏貼多個字段，回車後自動輸入，默認採用英文逗號分隔", "placeholder28": "請選擇方法", "placeholder29": "請選擇過濾模板", "placeholder3": "請輸入項目或目錄", "placeholder30": "請選擇映射模板", "placeholder31": "請選擇系統編號字段", "placeholder32": "請選擇映射字段", "placeholder33": "請選擇路由模板", "placeholder34": "請填寫輸入字段", "placeholder35": "請輸入時間下界大小，單位秒", "placeholder36": "請選擇窗口類型", "placeholder37": "請輸入滑動步長，單位秒", "placeholder38": "搜索多個關鍵字用逗號分隔", "placeholder39": "請選擇日誌類型", "placeholder4": "請輸入查詢關鍵字", "placeholder40": "請選擇數據輸出方式", "placeholder41": "請輸入用例名稱，長度不超過30字符", "placeholder42": "請輸入流程名稱查詢", "placeholder43": "長度不超過30字，超過則不支持輸入", "placeholder44": "請選擇父級目錄", "placeholder45": "請選擇前綴", "placeholder46": "請選擇後綴", "placeholder47": "請選擇目標項目目錄", "placeholder48": "請選擇，可選擇多個", "placeholder49": "表別名（選填）", "placeholder5": "請輸入名稱，長度不超過30字符", "placeholder50": "請輸入任務名稱模糊搜索", "placeholder51": "請輸入作業名稱模糊搜索", "placeholder6": "請輸入備註", "placeholder7": "請輸入名稱", "placeholder8": "請輸入流程名稱，長度不超過30字符", "placeholder9": "請選擇流程類型", "processComponent": "處理組件", "processed": "已處理", "prod": "已上線", "project": "項目", "projectInfo": "全部項目信息", "projectName": "項目名稱", "pub": "已發佈", "publicCodeLibrary": "公用SQL片段庫", "publicMethod": "共享方法", "publish": "發佈", "publishing": "發佈中", "pushLogic": "推送配置", "qkey": "快捷鍵", "qt": "嵌套", "quanping": "全屏", "queryInterval": "區間大小", "queryStrategy": "查詢方式", "rDir": "根目錄", "random": "隨機生成", "randomEnum": "隨機生成-枚舉值", "realTest": "真實輸入測試", "refresh": "刷新", "relation": "引用關係", "relation1": "邏輯關係", "relationImg": "關係圖", "reload": "重新上傳", "remark": "備註", "reset": "重置", "resourceName": "資源名稱", "result": "結果", "resultFields": "結果字段", "resultStr": "結果表達式", "resultView": "結果查看", "rollBack": "回滾", "rowNum": "行數", "rule": "處理規則", "ruleStr": "規則表達式", "run1": "執行", "runInfo": "運行信息", "runLog": "執行日誌", "runStatus": "執行狀態", "runTest": "運行測試", "s": "秒", "save": "保存", "scriptList": "腳本列表", "scroll": "滾動", "seach1": "搜索", "search": "查詢", "search1": "繼續查詢", "searchAll": "全量搜索", "searchConfig": "查詢配置", "searchResult": "個搜索結果", "select": "重新選擇", "selectAll": "全選", "selectAll1": "選擇全部", "selectAll2": "全部選擇", "selectFile": "選擇文件", "selectTable": "請選擇表", "selectTimeRange": "選擇時間範圍", "selectableProject": "可選項目", "selected": "已選擇", "selectedProject": "已選項目", "serve": "服務", "serveAddress": "服務地址", "serveInfo": "​服務信息", "serveName": "服務名稱", "serveType": "服務類型", "serviceInfo": "全部服務信息", "session": "會話", "sessionConfig": "會話配置", "showAll": "顯示全部", "showSelected": "只顯示已選中", "showUnselected": "只顯示未選中", "sortByProjectName": "按照項目名稱首字母排序", "sortByUpdateTime": "按照更新時間倒敍排序", "sourseTable": "原始表", "splitStr": "分隔符", "sqlCode": "SQL代碼", "sqlDetail": "SQL片段詳情", "startDate": "開始日期", "startSession": "啓動會話", "startTime": "起始時間", "status": "流程狀態", "statusRun": "基於上次狀態啓動", "steam": "流", "stop": "停止", "stopTime": "最新停止時間", "stopWithStatus": "停止並保留狀態", "stopping": "停止中", "str": "表達式", "streamingMode": "流模式", "tDir": "目標項目目錄", "table": "表", "tableMgr": "表管理", "tableName": "表名", "tag": "標籤", "target": "輸出目標", "test": "測試", "testData": "測試數據", "testEp": "測試用例", "testResult": "測試結果", "tiao": "條", "timeField": "時間字段", "timeToRollForward": "時間前滾", "timeout": "超時時間", "tip": "提示", "title1": "資源配置({0})", "title2": "生成個人SQL片段", "title3": "造數邏輯", "title4": "執行提示信息", "title5": "【{0}】用例的日誌", "total": "共", "trans": "轉換", "type": "流程類型", "unknownError": "未知錯誤", "updateTime": "更新時間", "updater": "更新人", "use": "啓用", "useUtil": "一鍵調用智能工具", "util": "智能工具", "val": "值", "version": "版本信息", "version1": "版本", "versionCompare": "版本比對", "view": "查看", "view1": "概覽視圖", "view2": "項目視圖", "view3": "查看組件監控數據", "viewCondition": "查看條件", "waitOnline": "待啓動", "waitTime": "間隔時間", "warnning": "預警", "waykey1": "先推再查", "waykey2": "先查詢再推送", "waykey3": "僅查詢", "waykey4": "僅推送", "week": "星期", "where": "where條件", "wu": "無", "xiang": "項", "y": "年", "yes": "是", "ys": "衍生", "zhi": "至"}, "flowDataSetChange": "流程{0}配置的數據定義發生變更，請確認是否繼續？", "flowDetail": "流程詳情：{0}", "flowInfo": "流程信息", "flowList": "流程列表", "flowName": "流程", "flowOnlineException": "流程上線異常，您可以在流程監控模塊執行強制停止操作", "flowType": "服務資源類型未選擇", "form": "來自：{0}", "formDesign": "表單設計", "format": "格式化", "freeMemory": "JVM堆內存", "freeSlots": "可用slot總數", "getConfFailed": "獲取配置失敗", "getMenuFailed": "獲取菜單失敗", "globalSearch": "全局搜索", "graph": "圖形", "groupError": "表單項【{0}】缺少必要字段[group]，表單項無法顯示", "groupInfoError": "表單項【{0}】group字段值[{1}]不存在分組信息中，表單項無法顯示", "handleLoading": "正在{0}數據，請稍等...", "handleRule": "處理規則", "handleTime": "處理時間", "hbaseCode": "請點擊左側表名查詢，最多查詢20條數據", "historyVersion": "歷史版本", "hiveCode": "請點擊左側表名或表字段，最多查詢20條數據", "home": {"count": "數量", "data": "數據", "dataCount": "數據量", "distribution": "流程狀態/數量分佈", "engineMatter": "引擎資源使用情況", "flowTotal": "流程總量", "hdMillion": "億", "individual": "個", "memory": "內存", "noPage": "無指定的服務列表頁面", "regist": "服務註冊量", "resource": "資源", "restart": "重啓中", "running": "運行中", "service": "服務", "success": "成功", "text1": "當日流程數據處理情況", "text10": "重啓中：流程正在重啓", "text11": "未知：流程狀態未知（因平台或加工引擎異常，無法獲取到流程運行信息）", "text12": "已完成", "text13": "已失敗", "text14": "已取消", "text15": "佔比（%）", "text2": "當日數據處理總量", "text3": "當日流程數據處理量", "text4": "已完成發佈的流程（流程狀態為已發佈或已上線）的運行狀態分佈情況，運行狀態定義如下：", "text5": "未運行：流程已發佈，但是未上線", "text6": "運行中：流程正在運行", "text7": "已完成：流程已完成（批模式或者有界流程完成後，會顯示為已完成）", "text8": "已失敗：流程運行失敗", "text9": "已取消：流程被取消", "thousand": "萬", "timeout": "查詢超時，請", "title": "PA監控雲盤", "tryAgain": "重試", "unused": "未使用", "updateTime": "數據更新時間", "used": "已使用", "warning": "待處理預警數"}, "homePage": "主頁", "host": "主機", "hostName": "主機名", "iconFile": "圖標文件", "indexCode": "請點擊左側index查詢，最多查詢20條數據", "initialBackoff": "初始間隔時間", "intervalPeriod": "間隔週期", "jdbc": "數據庫", "jitterFactor": "振動因子", "jobManagerLimitCpu": "job manager CPU爆發倍數", "jobManagerLimitMemory": "job manager 內存爆發倍數", "jobManagerMemory": "job manager內存(MB)", "jobManagerRequestCpu": "job manager CPU", "jobMgrInfo": "JobManager信息", "jobMgrMemory": "Jobmanager 堆內存", "jobRunningRule": "任務運行規則", "jobTag": "請輸入標籤，長度不超過30字符", "key": "鍵", "labelError": "表單項【{0}】缺少必要字段[label]", "lagTotal": "lag總計", "length2to30": "長度在 2 到 30 個字符", "listView": "列表展示", "loading": "拼命加載中...", "loadingText": "數據正在加載中...", "location": "所屬位置", "log": "日誌", "log4jFile": "log4j配置", "logBack": "用户會話過期請重新登錄", "logExpired": "登錄過期，請重新登錄", "logOutputKafka": "是否輸出日誌到Kafka", "logout": "退出登錄", "managedMemory": "Flink管理的內存", "maxBackoff": "最大間隔時間", "memoryCpuSetting": "內存、CPU設置", "memoryRemain": "內存剩餘", "memoryUsed": "內存使用", "menu": {"bloodRelation": "血緣關係", "dataDefine": "數據定義", "filterTemplate": "過濾模板", "flowMonitor": "流程監控", "mapLibrary": "映射字段庫", "mapTemplate": "映射模板", "monitorWarn": "監控預警", "optionManage": "選項管理", "routeTemplate": "路由模板", "serviceMonitor": "服務監控", "sheetManage": "表管理", "sqlClip": "SQL片段", "sqlClipLib": "SQL片段庫", "udf": "UDF管理"}, "method": "方法", "modeSelection": "模式選擇", "monitor": {"flow": {"action": {"basedLastOnline": "基於上次狀態啓動", "basedLastTooltip": "流程重啓，需要接着上次checkpoint記錄的位置，如kafka上次的offset位置繼續消費。", "batchOperationSuccess": "批量操作添加成功", "forceOffline": "強制停止", "leastOne": "請選擇記錄", "onlineLeast": "請選擇要啓動的流程", "restartForce": "一鍵重啓", "retainOffline": "停止並保留狀態", "retainTooltip": "即flink savepoint，用於暫停流程，流程重新啓動，保證精準一次語義。", "statelessConfirm": "確定無狀態啓動嗎？", "statelessOnline": "無狀態啓動", "statelessTooltip": "流程首次啓動，或者修改kafka consumer group等信息後需要從頭開始消費數據。"}}, "service": {"connect": "連接", "unConnect": "未連接"}, "text1": "控制觸發規則預警後是否發送通知，設置不發送後仍然會觸發預警並生成記錄（平台內查看）。", "text10": "當預警規則狀態為啓用，則會在配置的生效時間內按照執行週期進行定時檢查。執行週期通過cron表達式配置，cron表達式6位分別對應：秒/分/時/日/月/周，例如：‘0 */1 * * * ?’表示每分鐘執行1次；‘30 30 8 * * ?’表示每天8點30分30秒執行1次", "text11": "生效時間用於設置系統對資源進行監控的時間範圍，僅在每天該時間段內按執行週期對資源進行檢查", "text12": "預警通知內容", "text13": "狀態", "text14": "請輸入生效時間", "text15": "請輸入預警通知內容", "text16": "請選擇處理狀態", "text17": "未處理", "text18": "流程監控針對已完成發佈的流程（流程狀態為已發佈或已上線），支持查看運行情況以及運維管理。運行狀態定義如下：", "text19": "總計", "text2": "注意：通知功能需要系統對接，以實現短信、電話、羣機器人等方式告知相關負責人", "text20": "流程運行監控", "text21": "請選擇運行狀態", "text22": "請輸入項目目錄、流程名稱", "text23": "請選擇預警狀態", "text24": "請選擇預警規則", "text25": "請選擇資源類型", "text26": "當預警規則狀態為啓用，則會在配置的生效時間內按照執行週期進行定時檢查。", "text27": "執行週期通過cron表達式配置，cron表達式6位分別對應：秒/分/時/日/月/周，例如：‘0", "text28": "*/1 * * * ?’表示每分鐘執行1次；‘30 30 8 * * ?’表示每天8點30分30秒執行1次", "text29": "僅看未處理預警>0", "text3": "當資源檢查觸發預警規則時，通知下游系統的消息內容。可自定義內容文案，下列參數可作為動態變量使用：", "text30": "暫無數據權限", "text4": "title：資源名稱；", "text5": "resType：資源類型；", "text6": "ruleType：預警規則；", "text7": "problem：發生問題的內容。", "text8": "例如kafka服務：testkafka連通失敗，則會提示“資源名稱：testkafka；資源類型：Kakfa；預警規則：連通性檢查；發生問題：失敗連接的節點:[10.100.1.23:6311]", "text9": "請輸入預警規則", "warningRule": {"detail": {"allHandle": "全部處理", "detailInfo": "詳細信息", "handle": "處理", "lastMonth": "最近一個月", "lastThreeMonths": "最近三個月", "lastWeek": "最近一週", "noNeedToBeProcessed": "沒有需要處理的記錄", "warningRecord": "預警記錄"}, "edit": {"cron": "執行週期", "cronPlaceholder": "請輸入執行週期", "effectiveTime": "生效時間", "endTime": "結束時間", "noticeUser": "通知人", "noticeUserPlaceholder": "請選擇通知人", "ruleType": "規則類型", "saveAndOnline": "保存並重啓", "sendEnable": "預警通知發送", "silent": "靜默週期", "silentPlaceholder": "請輸入靜默週期", "startTime": "起始時間"}, "resNamePlaceholder": "請輸入資源名稱"}}, "monitorRuleName": "監控規則名稱", "name": "名稱", "nameTip": "{0}等{1}個流程{2}", "nameTip2": "{0}{1}個流程{2}", "namespace": "命名空間", "namespaceInfo": "namespace信息", "newCategory": "新建分類", "noData": "暫無數據", "node": "節點", "nodeInfo": "節點信息", "notPermission": "暫無操作權限，請聯繫管理員", "notUpdateModeJobName": "的基本參數、高級參數和自定義參數將被更新，", "notes": "備註", "objectCount": "對象數量(主)", "once": "一次性", "onlineDate": "上線日期", "onlineTime": "上線時間", "onlyValue": "僅Value", "opening": "開啓中", "orgAllocated": "機構已分配", "orgAvailableSlots": "機構可用slots", "orgCPU": "機構CPU", "orgChildrenCpu": "機構已分配CPU", "orgChildrenMemory": "機構已分配內存", "orgChildrenSlots": "機構已分配slots", "orgCpu": "機構可用CPU", "orgField": "組織機構字段", "orgId": "機構ID", "orgMemory": "機構可用內存", "orgName": "機構名稱", "orgOrQueueSetting": "機構/隊列設置", "orgResidualCpu": "機構剩餘CPU", "orgResidualMemory": "機構剩餘內存", "orgResidualSlots": "機構剩餘slots", "orgSlots": "機構Slots", "orgSurplus": "機構剩餘", "orgUsed": "機構已使用", "orgUsedCpu": "機構已使用CPU", "orgUsedMemory": "機構已使用內存", "orgUsedSlots": "機構已使用slots", "outputFields": "輸出字段", "pageDesign": "頁面設計", "parallelism": "默認並行度", "params": {"delConfirm": "此操作將永久刪除該記錄, 是否繼續?", "template": {"addTemplate": "新建模板", "delFieldSuccess": "刪除字段成功", "delSuccess": "刪除模板成功", "detail": {"addField": "添加字段", "addFilter": "添加過濾條件", "addRoute": "添加路由", "addRule": "添加條件", "baseInfo": "基本配置", "cnName": "中文名稱", "downloadTemplate": "模板下載", "editField": "編輯字段", "errorTip": "錯誤提示的文案", "fieldList": "字段列表", "fieldName": "判斷字段", "fieldType": "字段類型", "fieldValue": "值", "file": "文件", "filterRule": "規則", "filterRuleRequired": "過濾條件不能為空", "lackRule": "未配置條件", "mapField": "映射字段", "newBuiltField": "新建字段", "notFullyConfigured": "未配置完整", "notMismatching": "字段類型與字段值不匹配", "onlyExcel": "只能上傳excel文件", "onlyOneExcel": "只能上傳一個excel文件", "operator": "操作符", "route": "路由", "routeList": "路由列表", "rule": "條件", "selectFile": "選擇文件", "serialName": "標準名稱", "serialNumber": "標準編碼", "service": "集羣", "servicePlaceholder": "請選擇集羣", "serviceType": "服務類型", "sourceCode": "源代碼值", "sourceCodeComment": "源代碼含義", "standardCode": "標準代碼值", "standardCodeComment": "標準代碼含義", "systemName": "系統名稱", "systemNumber": "系統編號", "target": "輸出目標", "targetRequired": "輸出目標不能為空", "templateName": "模板名稱"}, "editTemplate": "編輯模板", "historicalFilterTemplate": "歷史過濾模板", "historicalMapTemplate": "歷史映射模板", "historicalRouteTemplate": "歷史路由模板", "historicalTemplate": "歷史模板", "name": "請輸入模板名稱"}}, "parentEncoding": "上級編碼", "parentEncodingNotExist": "上級編碼不存在請重新選擇分類", "parentEncodingPlaceholder": "請輸入上級編碼", "partCount": "分區數", "patternError": "表單項【{0}】解析pattern報錯: {1}", "physicalMemory": "物理內存", "placeholder": {"afterCpu": "請輸入調整後的CPU", "afterMemory": "請輸入調整後的內存", "afterSlots": "請輸入調整後的Slots", "attempts": "請輸入嘗試次數", "checkpointInterval": "請輸入checkpoint週期", "complete": "請完善信息", "consumeMode": "請選擇消費策略", "consumer": "請輸入消費者", "consumerName": "請輸入消費者名稱", "copies": "請輸入副本數", "cpFailed": "請輸入托管內存大小因子", "cpMinPause": "請輸入最小停頓時間", "cronExpression": "請輸cron表達式", "database": "請輸入database", "delay": "請輸入固定重啓延遲時間", "failureRateDelay": "請輸入失敗重啓延遲時間", "failureRateInterval": "請輸入時間間隔", "failuresPerInterval": "請輸入重啓次數", "fieldName": "請輸入字段名稱", "groupId": "請輸入group-id", "input": "請輸入", "jobManagerMemory": "請輸入job manager內存(MB)", "jobManagerRequestCpu": "請輸入job manager CPU", "jobRunningRule": "請選擇任務運行規則", "key": "請輸入key", "keyPlaceholder": "請輸入查詢關鍵字", "keyWord": "請輸入搜索關鍵字", "keyword": "請輸入關鍵字搜索", "name": "請輸入名稱", "nameOrUrl": "請輸入名稱或服務地址", "namespace": "請輸入namespace", "newKey": "請輸入鍵", "notesPlaceholder": "請輸入備註", "onlineTime": "請選擇上線時間", "orgIds": "請選擇分配機構", "orgName": "請輸入機構名稱", "orgNameFilter": "請輸入機構名稱過濾", "orgNameOrId": "請輸入機構名稱/ID", "partCount": "請輸入分區數", "partitionOffset": "請輸入partition:offset;partition:offset", "queue": "請選擇隊列", "queueList": "請選擇可分配的隊列", "queueName": "輸入隊列名稱", "restartStrategy": "請選擇重啓策略", "search": "請輸入內容", "select": "請選擇", "set": "請輸入set", "sqlPlaceholder": "請輸入SQL代碼", "stateBackend": "請選擇狀態後端", "tableName": "請輸入表名", "taskManagerFrac": "請輸入taskmanager托管內存因子", "taskManagerLimitCpu": "請輸入task manager CPU 爆發倍數", "taskManagerLimitMemory": "請輸入task manager 內存爆發倍數", "taskManagerRequestCpu": "請輸入task manager CPU個數", "taskManagerSlotNumber": "請輸入task manager slot個數", "time": "請輸入時間", "topicName": "請選擇topic名稱", "updatePlaceholder": "請輸入更新內容", "useCasesPlaceholder": "請輸入用例數據", "vaguePlaceholder": "輸入字段名模糊搜索", "value": "請輸入值"}, "pleaseSelect": "請選擇要", "port": "端口", "portal": {"title1": "用户權限", "title2": "機構管理", "title3": "用户管理", "title4": "角色管理", "title5": "系統配置", "title6": "安全配置", "title7": "內容配置", "title8": "操作日誌", "title9": "個人中心"}, "preLackCpu": "預計剩餘Cpu不足", "preLackMemory": "預計剩餘內存不足", "preLackSlots": "預計剩餘slots不足", "preRemainCpu": "預計剩餘CPU", "preRemainMemory": "預計剩餘內存（MB）", "preferenceRelation": "引用關係({0})", "prescribed": "自定義", "process": "流程", "prompt": "提示", "propError": "表單項【{0}】缺少必要字段[prop]", "queryTimeout": "查詢超時，請刷新重試", "queue": "隊列", "queueResource": "隊列資源", "queueResourceDetail": "隊列資源詳情", "rawData": "原始數據", "read": "讀", "readError": "讀錯誤", "realTestResult": "查看真實輸入測試結果", "refreshSource": "刷新資源使用情況", "remainCpu": "剩餘CPU", "remainMemoryMB": "剩餘內存(MB)", "remainSlot": "剩餘Slots", "remainSlots": "預計剩餘slots", "repeatProp": "存在重複prop[{0}]", "repeatTime": "重複時間", "requestTimeout": "請求超時！", "resType": "節點類型未選擇", "resetBackoffThreshold": "最小穩定運行時間", "resource": {"importExport": {"assetId": "資產ID", "assetName": "資產名稱", "assetType": "資產類型", "diffContent": "差異內容", "exportDataPreview": "導出數據預覽", "keyplaceholder": "輸入關鍵字進行過濾", "option": "選項", "project": "項目", "selectAll": "全選", "selectNone": "全不選", "tablePrefix": "表前綴", "tableSuffix": "表後綴"}, "sql": {"dataTip": "請選擇數據", "delTip": "確認要刪除選中數據?", "detail": {"alreadyExists": "以下SQL片段縮略詞在平台已存在", "duplicateData": "重複數據", "file": "文件", "partAcronym": "片段縮略詞", "partName": "片段名稱", "placeholder": {"acronymPlaceholder": "請輸入片段縮略詞", "name": "請輸入片段名稱", "notesPlaceholder": "請輸入備註信息"}, "sqlCode": "SQL代碼", "tips": {"inputAcronymTips": "首字符僅可輸入字母/數字/./"}, "tooltip": {"tooltip1": "請設置片段縮略詞，以便於在編輯區編寫代碼時通過 縮略詞+回車 快速使用SQL片段"}}}, "tip1": "打斷算子鏈，可用於分析流程性能瓶頸，會對流程性能產生影響", "tip2": "進行週期性狀態快照，保證數據一致性，會對流程性能產生影響"}, "resourceConf": "資源配置({0})", "resourceConfig": "資源配置", "resourceList": "請填寫資源名稱", "resourceNotFound": "{0} 資源未被發現", "restartStrategy": "重啓策略", "result": "結果", "reupload": "重新上傳", "ruleExpression": "規則表達式", "scriptInfo": "腳本信息", "searchDoc": "文檔查詢", "searchResult": "查詢結果", "selectVersion": "選中版本：{0}", "sentry": "哨兵", "serialNumber": "序號", "service": "服務", "serviceDetail": "服務詳情", "serviceId": "節點名稱未選擇", "serviceMgr": "服務管理", "serviceName": "流程類型未選擇", "serviceType": "服務類型", "serviceTypeRule": "請選擇服務資源類型", "sessionExpired": "可能您的會話已過期，是否要跳轉到登錄頁面？", "sharedOrg": "已分享機構", "sharingMethod": "共享方法", "sheetEdit": "表管理：{0}", "showHomologyGraph": "點擊【查詢血緣】後，將以該同源節點作為查詢節點展示血緣關係圖", "single": "單個", "singlePoint": "單點", "singleText0": "您確定刪除選中的數據嗎？", "singleText1": "${name}已經被關聯，無法刪除！", "slotsNumber": "slot總數", "slotsSetting": "Slots設置", "someProject": "到的項目", "sourceCode": "源碼", "sqlFlow": "SQL流程", "sqlFragment": "SQL片段{0}", "stateBackend": "狀態後端", "status": {"develop": "開發", "none": "未運行", "online": "已上線", "published": "已發佈", "unKnow": "未知", "underDdevelopment": "開發中"}, "statusStorage": "狀態的後端存儲", "streamcube": "計算引擎", "syncMig": "將【{0}】流程的配置同步到{1}。執行同步後，{2}{3}{4}您確定要同步嗎？", "syncSetting": "同步配置", "syncSuccess": "同步成功，請保存或啓動", "systemPath": "安裝路徑的佔位符${install_dir_key},端口的佔位符${port_key}", "systemPlaceholder": "系統內置佔位符", "table": "表", "tableCode": "請選擇左側表名或表字段，最多查詢20條數據", "tableInfo": "表信息", "tableName": "表名稱", "targetOrg": "目標機構", "taskManage": "task managers", "taskManagerFrac": "taskmanager託管內存因子", "taskManagerLimitCpu": "task manager CPU 爆發倍數", "taskManagerLimitMemory": "task manager 內存爆發倍數", "taskManagerMemory": "task manager內存(MB)", "taskManagerRequestCpu": "task manager CPU", "taskManagerSlotNumber": "task manager slot個數", "taskMgrInfo": "TaskManager信息", "taskMgrMemory": "Taskmanager 堆內存", "taskMgrSlots": "Taskmanager slot數量", "testResult": "測試結果", "thirdPartyLibs": "第三方類庫", "timePeriod": "時間週期", "timeSinceLastHeartbeat": "最後心跳時間", "tip": {"addFile": "請添加文件", "checkMessage": "請檢查輸入內容", "checkResult": "統計節點連接情況，連接節點數/總節點數", "choseData": "請選擇數據", "choseOne": "請選擇一條數據", "clusterNotExist": "該集羣不存在", "codeLoading": "源碼加載中...", "color": "紅色代表刪除，黃色代表更新，藍色代表新增", "compareVersion": "請選擇需要對比的版本", "configureAtOne": "請至少配置一個條件", "connectError": "{0}連接失敗", "connectSuccess": "連接成功", "cpFailed": "checkpoint最大失敗次數", "cpUnaligned": "開啓後可以提高checkpoint的速度，但會增加內存使用", "cron": "請再次輸入正確cron表達式", "dataQualityMonitoring": "數據質量監控組件提示\n            \n            該組件配置的監控規則採用 mvel 表達式實現，遵循mvel表達式的語法。\n            表達式的結果必須是一個boolean類型的值。下列是一些示例：\n  \n            假設上游傳遞的字段為 ： id,name,counta,msg,countb,countc\n            1.字段的使用：以 # 開頭 ， 以 # 結尾。 例如: #counta# > 10\n            2.符號的使用：&&、||、>、&lt;、>=、&lt;=、==、%、*、+、-等， null 或者\n            nil 表示空值。\n            例如：(#countc# + #counta# &lt; 200) && #countb# &lt; 990\n            例如：#name# == null || #id# == null\n            \n            3.自定義方法的使用：需要先在【元件管理】-【資源管理】-【方法】裏面先定義一個方法，然後才能使用。\n            例如先定義了方法：\n  \n            public static boolean getBoolean(Integer id, String name)\n              { return name == null || id == null || id &lt; 9999;\n            }\n  \n            在此處編輯的表達式則可為：\n            getBoolean(#id#,#name#)\n            或者\n            #counta # * #countb# &lt; 99999 && getBoolean(#id#,#name#)", "delConfirm": "確定刪除該條數據嗎?", "delSuccess": "刪除成功", "deleteConfirm": "此操作將永久刪除該記錄, 是否繼續?", "deleted": "已經被刪除", "deployTimeout": "請輸入上線超時時間", "dfs": "該模式需要配置分布式文件系統", "fieldRepeat": "基本參數中[ {0} ]所填字段名與字段配置中的字段有重複", "fileCount": "最多上傳 {0} 個文件", "greaterStartTime": "請選擇大於當前時間的上線時間", "intervalHour": "請輸入小時", "intervalMinute": "請輸入分鐘", "isApplyParallelism": "同步到每個組件", "jobManagerLimitCpu": "請輸入job manager CPU爆發倍數", "jobManagerLimitMemory": "請輸入job manager 內存爆發倍數", "jobManagerMemory": "請輸入job manager內存", "jobManagerRequestCpu": "請輸入job manager CPU", "jobMgrLimitCpu": "該配置項用於開啓JobManager CPU爆發能力，指定了JobManager運行時能夠使用的CPU上限", "jobMgrLimitMemory": "該配置項用於開啓JobManager內存爆發能力，指定了1個JobManager運行時能夠使用的內存上限。", "jobMgrMemory": "JobManager負責整個job的生命週期管理，包括資源申請，狀態監控，協調、控制的執行過程如處理調度任務、保存checkpoint、容錯等；該配置項指定 了JVM 堆內存。", "jobMgrRequestCpu": "該配置項指定了JobManager運行分配的最小CPU", "jobRunningRule": "請選擇任務運行規則", "loading": "數據正在加載中...", "logging": "登錄中...", "map": "注意：處理規則引用的方法中，如需輸入固定參數，需要對特殊字符串進行轉義，如\"需寫為\\\"。", "maxLength1000": "單次最大可選擇1000條數據", "maximumInput": "最大支持輸入{0}字符", "mode": "請選擇流程模式", "namespace": "yarn通過命名空間實現用户和資源的管理，配置該項指定提交到集羣的某個命名空間中。", "nonSpaceChar": "請輸入非空格字符", "notEndWithComma": "內容結尾不能是逗號", "notHasChinese": "內容不能包含中文", "notHasChineseComma": "內容不能包含中文逗號", "notHasSpaceBreak": "內容不能包含空格或換行", "notStartWithComma": "內容開頭不能是逗號", "onlyJar": "只能上傳jar類型的文件!", "onlyJar250MB": "只能上傳jar文件，且不超過250MB", "onlyPngSvg": "只能上傳png、svg類型的文件!", "onlyPngSvg20KB": "只能上傳png、svg類型的文件，且不超過20KB", "orgConfig": "部分機構未完成{0}，請完成配置", "queue": "yarn通過隊列實現用户和資源的管理，配置該項指定提交到集羣的某個隊列中。", "recordConfig": "第 {0} 條記錄未配置完整", "recordName": "第 {0} 條記錄的名稱與其他字段名有重複", "recordParams": "第 {0} 條記錄方法的參數未配置完整", "recycle": "確定回收機構名下已分配的資源？", "refreshSuccess": "刷新成功", "related": "當前資源存在引用關係，修改後可能會受到影響", "repeatTime": "請選擇重複時間", "rollBackConfirm": "是否確認回滾?", "sameCode": "版本號{0}與選中版本號{1}源碼一致", "sameQueueOrgData": "請選擇同一隊列下的機構數據", "scp": "提示:文件默認上傳到用户根目錄", "selectFile": "請選擇文件", "selectedOrg": "已選擇機構（{0}/1000）：", "shareAccess": "默認分享的權限：", "shareTopic": "請選擇分享的Topic：", "size1GB": "文件大小限制1GB", "size20KB": "不能上傳超過20KB的文件!", "size250MB": "不能上傳超過250MB的文件!", "someKey": "存在相同鍵", "startConnectTest": "開始連接測試...", "startDate": "請選擇上線日期", "startTime": "請選擇上線時間", "taskManagerFrac": "作用於RocksDB狀態後端，批流流程中的排序，哈希表以及緩存中間結果。", "taskManagerLimitCpu": "請輸入task manager CPU 爆發倍數", "taskManagerLimitMemory": "請輸入task manager 內存爆發倍數", "taskManagerMemory": "請輸入task manager內存", "taskManagerRequestCpu": "請輸入task manager CPU個數", "taskManagerSlotNumber": "請輸入task manager slot數量", "taskMgrLimitCpu": "該配置用於開啓TaskManager CPU爆發能力，指定了TaskManager運行時能夠使用的CPU上限", "taskMgrLimitMemory": "該配置項用於開啓TaskManager內存爆發能力，指定了TaskManager運行時能夠使用的內存上限。", "taskMgrMemory": "taskManager負責具體的任務執行，設置該項為對應任務在每個節點上的內存數量。", "taskMgrRequestCpu": "該配置項指定了TaskManager運行分配的最小CPU", "taskMgrSlotNumber": "配置一個TaskManager有多少個併發的slot數，類似於每個TaskManager內有多少個線程。", "terminal": "終端[{0}]", "timeFormat": "日期格式：{0}", "typeJudge": "只能上傳{0}類型的文件!", "updateSuccess": "更新成功", "uploadFiles": "請上傳{0}文件"}, "totalCpu": "總CPU", "totalMemory": "總內存", "totalMemoryMB": "總內存(MB)", "totalSlots": "總Slots", "udfInfo": "UDF信息", "udjInfo": "UDJ信息", "underExecution": "執行中，請稍等...", "unit": {"day": "天", "hour": "時", "hours": "小時", "minute": "分", "second": "秒"}, "unknownError": "未知錯誤：{0}", "unknownErrorCode": "未知錯誤，錯誤代碼：{0}", "unknownFile": "未知文件", "unused": "未使用", "updateModeJobName": "的基本參數、高級參數、自定義參數和模式選擇將被更新，", "uploadBatch": "批量上傳(目前只針對不存在的組件)", "uploadError": "（上傳失敗）{0}", "uploadSingle": "單個上傳", "used": "已使用", "userCancel": "用户取消", "userCenter": "個人中心", "value": "值", "value1": "值1", "value1Placeholder": "請輸入值1內容", "value2": "值2", "value2Placeholder": "請輸入值2內容", "value3": "值3", "value3Placeholder": "請輸入值3內容", "value4": "值4", "value4Placeholder": "請輸入值4內容", "viewField": "查看字段", "visibleError": "表單項【{0}】解析visible函數報錯: {1}", "warehouse": "組件庫", "warnRule": "預警規則", "waterLevelLine": "水位線", "will": "將", "write": "寫", "writeError": "寫錯誤", "wu": "無"}}