<template>
  <div class="bs-detail-block">
    <data-handler class="flow-processing" :request="request" @get-data="getData">
      <template v-slot:content>
        <div class="flow-processing__handle">
          <div class="bs-detail__header">
            <div class="bs-detail__header-title">{{ $t('pa.home.text1') }}</div>
          </div>
          <div class="flow-processing__handle--item">
            <img src="@/assets/data.png" :alt="$t('pa.home.data')" />
            <div class="flow-processing__count">
              <span class="flow-processing__handle--count">
                {{ dataCount }}
                <span class="unit">{{ displayUnit }}</span>
              </span>
              <span class="flow-processing__handle--title" :class="{ 'flow-processing__handle--us': isEn }">{{
                $t('pa.home.text2')
              }}</span>
            </div>
            <div v-if="showSeparator" class="flow-processing__separator"></div>
          </div>
        </div>
        <div class="flow-processing__rank">
          <div class="bs-detail__header">
            <span class="flow-processing__rank--title">
              {{ $t('pa.home.text3') }}
              <el-select v-model="processCount" @change="handleProcessCountChange">
                <el-option v-for="{ value, label } in processCountList" :key="value" :value="value" :label="label" />
              </el-select>
            </span>
          </div>
          <chart v-show="hasData" :option="option" name="process" class="flow-processing__chart" />
          <div v-show="!hasData"><bs-empty class="flow-processing__chart" image-size="150px" /></div>
        </div>
      </template>
    </data-handler>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from 'vue-property-decorator';
import { FlowProcessingChart } from '../charts/charts';
import Chart from '../components/chart.vue';
import { getFlowDataCount } from '@/apis/homeApi';
import { AxiosPromise } from 'axios';
import DataHandler from './data-handler.vue';
@Component({
  components: {
    Chart,
    DataHandler
  }
})
export default class FlowProcessing extends Vue {
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: 0 }) screenWidth!: number;
  private processCount = 10;
  private option: any = FlowProcessingChart.option;
  private processCountList: object[] = [
    { value: 5, label: 'Top 5' },
    { value: 10, label: 'Top 10' },
    { value: 15, label: 'Top 15' }
  ];
  private request: AxiosPromise[] = [];
  private dataCount = 0;
  private unit = '';
  private displayUnit = '';
  private scWidth = 0;
  private topFlows: any = null; // 流程数据
  get showSeparator() {
    if ((this.scWidth < 1600 && this.$store.getters.isCollapse) || this.scWidth > 1600) return true;
    return false;
  }
  get hasData() {
    return this.option.series[0].data.length > 0;
  }

  @Watch('screenWidth', { immediate: true })
  handleScreenWidthChange(val) {
    this.scWidth = val;
  }
  @Watch('orgId', { immediate: true })
  handleOrgIdChange(ids) {
    this.request = [getFlowDataCount(ids)];
  }
  // 流程量变化
  handleProcessCountChange(count) {
    this.option = FlowProcessingChart.setCount(count);
    this.dataCount = this.setTotalCount(
      this.option.xAxis.data.reduce((pre, cur) => pre + this.topFlows[cur], 0),
      2
    );
    this.displayUnit = this.unit;
  }
  setTotalCount(number, decimalDigit) {
    this.unit = '';
    const integer = Math.floor(number);
    const addUnit = (integer, number, mutiple, decimalDigit) => {
      const digit = getDigit(integer);
      if (digit > 3) {
        let remainder = digit % 8;
        if (remainder >= 5) {
          remainder = 4;
        }
        this.unit = this.$t('pa.home.thousand') as string;
        return Math.round(number / Math.pow(10, remainder + mutiple - decimalDigit)) / Math.pow(10, decimalDigit);
      } else {
        return Math.round(number / Math.pow(10, mutiple - decimalDigit)) / Math.pow(10, decimalDigit);
      }
    };
    const getDigit = (integer) => {
      let digit = -1;
      while (integer >= 1) {
        digit++;
        integer = integer / 10;
      }
      return digit;
    };
    const digit = getDigit(integer);
    if (digit > 3) {
      const multiple = Math.floor(digit / 8);
      if (multiple >= 1) {
        const tmp = Math.round(integer / Math.pow(10, 8 * multiple));
        const count = addUnit(tmp, number, 8 * multiple, decimalDigit);
        for (let i = 0; i < multiple; i++) {
          this.unit += this.$t('pa.home.hdMillion');
        }
        return count;
      } else {
        return addUnit(integer, number, 0, decimalDigit);
      }
    } else {
      return number;
    }
  }
  getData({ data }) {
    if (data) {
      const { topFlows } = data;
      this.topFlows = topFlows;
      this.unit = '';
      const map: object[] = [];
      Object.keys(topFlows).forEach((el) => {
        const displayValue = this.setTotalCount(topFlows[el], 2) + '';
        map.push({
          value: topFlows[el],
          displayValue,
          label: el,
          unit: this.unit
        });
        this.unit = '';
      });
      const tempData = {
        data: [Object.keys(topFlows)],
        series: [
          {
            name: this.$t('pa.home.dataCount'),
            data: map,
            type: 'bar',
            barWidth: 20,
            color: '#54C958'
          }
        ]
      };
      FlowProcessingChart.setOption(tempData);
      this.handleProcessCountChange(10);
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-processing {
  width: 100%;
  display: flex;
  justify-content: space-between;
  ::v-deep .bs-empty {
    height: 100%;
  }
  &__chart {
    ::v-deep .bs-empty__img {
      margin-top: 10px;
    }
  }
  &__handle {
    flex: 1;
    &--item {
      display: flex;
      height: 100%;
      justify-content: space-around;
      align-items: center;
      margin-left: 20px;
    }
    &--title {
      font-size: 14px;
      font-weight: 400;
      color: #777777;
      line-height: 20px;
    }
    &--us {
      width: 240px;
    }
  }
  &__rank {
    flex: 5;
    &--title {
      margin-left: 60px;
      font-size: 14px;
      margin-top: 5px;
      font-weight: 500;
      color: #444444;
    }
    &--flow {
      margin-right: 11px;
    }
  }
  &__separator {
    width: 1px;
    height: 64px;
    border: 1px solid #f1f1f1;
  }
  &__count {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    font-weight: bold;
    font-size: 1.67vw;
    color: #444444;
    line-height: 37px;
    flex-shrink: 0;
    margin-right: 25px;
    .unit {
      font-size: 14px;
      font-weight: 400;
      color: #777777;
      line-height: 20px;
    }
  }
  .bs-detail__header {
    display: flex;
    justify-content: space-between;
    ::v-deep .el-input__inner {
      width: 95px;
    }
    &-title {
      width: 175px;
      font-weight: 500;
    }
  }
}
.bs-detail__content {
  padding-right: 0;
}
</style>
