const router = [
  {
    path: '/data',
    name: 'data',
    meta: {
      access: 'PA.DATA',
      title: '数据管理',
      icon: 'iconfont icon-data-source',
      customAccess: 'enableSql'
    },
    component: () => import('../views/data/index.vue'),
    children: [
      {
        path: '/data/sheetManage',
        name: 'sheetManage',
        meta: {
          access: 'PA.DATA.TABLE.MENU',
          title: '表管理',
          customAccess: 'enableSql'
        },
        component: () => import('../views/data/sheet-manage/index.vue')
      },
      {
        path: '/data/viewManage',
        name: 'viewManage',
        meta: {
          access: 'PA.DATA.VIEW.MENU',
          title: '视图管理',
          customAccess: 'enableSql'
        },
        component: () => import('../views/data/view/index.vue')
      },
      {
        path: '/data/option',
        name: 'dataOption',
        meta: {
          access: 'PA.DATA.ITEM.MENU',
          title: '选项管理',
          customAccess: 'enableSql'
        },
        component: () => import('../views/data/option/index.vue')
      },
      {
        path: '/data/viewEdit',
        name: 'viewEdit',
        beforeEnter: (to, from, next) => {
          to.meta.title = (to.query.title === '新建视图' ? '' : '视图管理：') + to.query.title;
          next();
        },
        meta: {
          noMenu: true,
          customAccess: 'enableSql'
        },
        component: () => import('../views/data/view/add-edit/index.vue')
      },
      {
        path: '/data/viewDetail',
        name: 'viewHistory',
        beforeEnter: (to, from, next) => {
          to.meta.title = '视图管理：' + to.query.title;
          next();
        },
        meta: {
          noMenu: true,
          customAccess: 'enableSql'
        },
        component: () => import('../views/data/view/detail/index.vue')
      },
      {
        path: '/data/sheetEdit',
        name: 'sheetEdit',
        beforeEnter: (to, from, next) => {
          to.meta.title = '表管理：' + to.query.title;
          next();
        },
        meta: {
          noMenu: true,
          customAccess: 'enableSql'
        },
        component: () => import('../views/data/sheet-manage/add-edit/index.vue')
      },
      {
        path: '/data/sheetDetail',
        name: 'sheetDetail',
        beforeEnter: (to, from, next) => {
          to.meta.title = '表管理：' + to.query.title;
          next();
        },
        meta: {
          noMenu: true,
          customAccess: 'enableSql'
        },
        component: () => import('../views/data/sheet-manage/detail/index.vue')
      },
      {
        path: '/data/UDF',
        name: 'UDF',
        meta: {
          access: 'PA.DATA.UDF.MENU',
          title: 'UDF管理',
          customAccess: 'enableSql'
          // 不打开新tab
          // noJump: true
        },
        component: () => import('../views/data/udf/index.vue')
      },
      {
        path: '/data/udfEdit',
        name: 'udfEdit',
        beforeEnter: (to, from, next) => {
          to.meta.title = to.query.title;
          next();
        },
        meta: {
          noMenu: true,
          customAccess: 'enableSql'
        },
        component: () => import('../views/data/udf/add-edit/index.vue')
      },
      {
        path: '/data/udfHistory',
        name: 'udfHistory',
        beforeEnter: (to, from, next) => {
          to.meta.title = to.query.title;
          next();
        },
        meta: {
          noMenu: true,
          customAccess: 'enableSql'
        },
        component: () => import('../views/data/udf/version/index.vue')
      }
    ]
  }
];
export { router };
