import Vue from 'vue';
import i18n from '@/i18n';
import installBsUiPro from './common/install-bs-ui-pro';
import App from './App.vue';
import store from './store';
import router from './router';
import '@/apis/utils/http';
import { installFilterAndDirective } from '@/filters-directives';
import { Component } from 'vue-property-decorator';
import VueClipboard from 'vue-clipboard2';
import initWuJie from '@bs/wujie';
import '@/components/svg-icon';
import { autoLogin, microAppCfg } from '@/utils';
import { invalidRequestHandler } from '@/apis/utils/http-base';
import { GET_I18N_MAP, GET_PA_CONFIG, GET_PA_MENU, GET_PORTAL_CONFIG, GET_USER_INFO } from '@/store/event-name';

Vue.config.productionTip = false;
Component.registerHooks(['beforeRouteEnter', 'beforeRouteLeave', 'beforeRouteUpdate']);

Vue.use(VueClipboard);
// 判断当前URl是否包含/pipe/pipeace.html，如果包含说明是融合环境，不需要加载微前端资源
// 如果不包含说明是独立环境，需要加载微前端资源
if (!window.location.href.includes('/pipe/pipeace.html')) {
  initWuJie(Vue, microAppCfg, { router, toLogin: invalidRequestHandler, runToLoginNames: ['portal', 'assets'] });
}
// 按需引入bs-ui-pro 注意和bsview中引用的element样式的冲突
installBsUiPro();
// 注册全局过滤器和自定义指令
installFilterAndDirective();

(async () => {
  try {
    await autoLogin();
    await store.dispatch(GET_PA_CONFIG);
    await store.dispatch(GET_PA_MENU);
    await store.dispatch(GET_PORTAL_CONFIG);
    await store.dispatch(GET_USER_INFO);
    await store.dispatch(GET_I18N_MAP);
  } finally {
    new Vue({
      el: '#app',
      store,
      router,
      i18n,
      render: (h) => h(App)
    });
  }
})();
