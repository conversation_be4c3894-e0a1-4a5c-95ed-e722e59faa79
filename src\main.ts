import Vue from 'vue';
import i18n from '@/locale';
import installOtherProjects from './common/install-other-projects';
import installBsUiPro from './common/install-bs-ui-pro';
import InstallFuseData, { handleFuseToekn } from './common/install-fuse-data';
import axios from 'axios';
import VueAxios from 'vue-axios';
import App from './App.vue';
import store from './store';
import router from './router';
import { AxiosInterceptor } from '@/apis/utils/http';
import { installFilterAndDirective } from '@/filters-directives';
import { Component } from 'vue-property-decorator';
import { GET_USER_INFO, GET_CONFIG_DATA } from '@/store/event-names/actions';
// import { autoLogin } from './utils';
import $ from 'jquery';
import '@/components/svg-icon';
import cookie from 'js-cookie';
cookie.set(
  'access_token',
  decodeURIComponent(
    'Bearer eyJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************************************************************************************************.a6JOHSRSJ1ZbM2vghwwpvv86UxpI_SSXOcieIHIlbMNiE0plR7bH_4Wm5EHz6sNZUHiI6UzTZv68QvZwzBdwYg'
  )
);
(window as any).$ = (window as any).jQuery = $;
Vue.config.productionTip = false;
Component.registerHooks(['beforeRouteEnter', 'beforeRouteLeave', 'beforeRouteUpdate']);

Vue.use(VueAxios, axios);
// 安装其他项目 流立方 && assets
installOtherProjects(Vue, i18n);
// 按需引入bs-ui-pro 注意和bsview中引用的element样式的冲突
installBsUiPro();
AxiosInterceptor();
// 注册全局过滤器和自定义指令
installFilterAndDirective();

(async () => {
  try {
    InstallFuseData(store);
    await handleFuseToekn(store);
    // await autoLogin();
    await store.dispatch(GET_CONFIG_DATA); // 获取sql、融合开关
    await store.dispatch(GET_USER_INFO);
    initVue();
  } catch (err) {
    initVue();
  }
})();
// 存放vue实例
let vm;

function initVue() {
  vm = new Vue({
    store,
    router,
    i18n,
    render: (h) => h(App)
  }).$mount('#app');
}

export { vm };
