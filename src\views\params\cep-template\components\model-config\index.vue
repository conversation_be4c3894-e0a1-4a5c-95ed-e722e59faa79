<template>
  <bs-dialog
    :title="title"
    width="1000px"
    :visible.sync="display"
    class="config-dialog"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @confirm="handleConfirm"
  >
    <!--zhong -->
    <div v-loading="loading" element-loading-text="配置加载中" class="config__container">
      <!-- 逻辑关系 -->
      <logic-bar
        ref="bar"
        :data.sync="modelData"
        :disabled="disabled"
        @change="generateExpression"
        @increase="generateConfigTemplate"
      />

      <!-- 结果表达式 -->
      <div :key="key" class="config-resultExpression">
        <span>结果表达式：</span>
        <el-tooltip v-hide="resultExpression" effect="light" placement="top">
          <div slot="content" v-html="resultExpression"></div>
          <div class="config-resultExpression__main" v-html="resultExpression"></div>
        </el-tooltip>
      </div>
      <!-- 配置明细 -->
      <logic-config
        ref="config"
        :disabled="disabled"
        :field-list="fieldList"
        :method-list="methodList"
        :data.sync="singletonCondition"
        :field-type-dict="fieldTypeDict"
        :method-source-code.sync="methodCode"
        :expression-mapping="expressionMapping"
        @change="handleConfigChange"
        @copy="handleCopyConfig"
      />
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Watch, Vue } from 'vue-property-decorator';
import { get } from '@/apis/utils/net';
import LogicBar from './logic-bar.vue';
import LogicConfig from './config/index.vue';
import { cloneDeep, isEmpty } from 'lodash';
import {
  handleFuncArgs,
  handleTableData,
  handleTableHead,
  isArray,
  isObject,
  // isString,
  isValidType,
  hide,
  generateName,
  sha256
} from '../../util';
import { URL_GET_FUNC_LIST } from '@/apis/commonApi';

@Component({ components: { LogicBar, LogicConfig }, directives: { hide } })
export default class ModelConfig extends Vue {
  @Prop({ default: 0 }) index!: number;
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => [] }) fieldList!: any[];
  @Prop({ default: () => ({}) }) fieldTypeDict!: any[];
  @PropSync('show', { default: false }) display!: boolean;
  @PropSync('data', { default: () => ({}) }) modelData!: any;
  @PropSync('methodSourceCode', { default: () => ({}) }) methodCode!: any;
  @Ref('bar') readonly logicBar!: LogicBar;
  @Ref('config') readonly logicConfig!: LogicConfig;

  private loading = false;
  private funcMapping: any = {}; // 方法类型映射
  private methodList: any[] = []; // 方法列表
  private conditions: any = []; // 条件配置详情
  private expressionMapping: any = {}; // 模块对应表达式映射
  private rawData: any = {};
  get orgId() {
    return this.$store.state.userInfo.orgId;
  }
  get title() {
    return `${this.modelData.singletonModeName || ''} 条件配置`;
  }
  get logicalRelationship() {
    return this.modelData.logicalRelationship;
  }
  get logicRelation() {
    return this.modelData.logicRelation;
  }
  get resultExpression() {
    return this.modelData.resultExpression;
  }
  get singletonCondition() {
    return this.modelData.singletonCondition;
  }
  get key() {
    return sha256(this.resultExpression);
  }

  @Watch('singletonCondition', { immediate: true, deep: true })
  generateExpression() {
    /* 逻辑表达式 */
    this.handleLogicalRelation();
    this.handleResultExpression();
  }

  async created() {
    try {
      this.loading = true;
      this.initData();
      await this.getMethodList();
      const result = await this.parseConditions(isArray(this.singletonCondition));
      this.$set(this.modelData, 'singletonCondition', result);
      this.loading = false;
    } catch (e) {
      this.loading = false;
      console.log(e);
    }
  }
  /* 处理逻辑表达式 */
  handleLogicalRelation() {
    if (this.logicalRelationship === 'CUSTOM') return;
    const result = this.singletonCondition
      .map(({ name, funcName }) => (funcName ? name : null))
      .filter(Boolean)
      .join(` ${this.logicalRelationship} `);
    this.$set(this.modelData, 'logicRelation', result);
  }
  /* 处理结果表达式 */
  handleResultExpression() {
    let str = this.logicRelation.replace(/([A-Z])/g, '@@_$1_@@');
    /* 模块对应表达式 */
    const expressionMapping = this.singletonCondition.reduce((pre, next) => {
      const { name, funcName, funcType, funcArgs } = next;
      const params = funcArgs
        .map(({ key, value }) => {
          if (funcType === 'DEFAULT' && !isObject(this.funcMapping[funcName]).isOneParams) {
            return `${key},${value}`;
          }
          return key;
        })
        .filter(Boolean);
      if (funcName) {
        pre[name] = `${funcName}(<span class="code-green">${params.join(',')}</span>)`;
        str = str.replace(new RegExp(`@@_${name}_@@`, 'g'), pre[name]);
      }
      return pre;
    }, {});
    this.$set(this, 'expressionMapping', expressionMapping);
    /* 结果表达式 */
    const result = str
      .replace(/(\&\&|\|\|)/g, '<span class="code-red"> $1 </span>')
      .replace(/[\@\_]/g, '');
    this.$set(this.modelData, 'resultExpression', result);
  }
  initData() {
    this.rawData = cloneDeep(this.modelData);
    this.$set(this.modelData, 'logicalRelationship', this.logicalRelationship || '&&');
  }
  async getMethodList() {
    try {
      const { success, data, error } = await get(URL_GET_FUNC_LIST, {
        orgId: this.orgId,
        name: this.$store.state.userInfo.username
      });
      if (success) {
        const [mapping, ...list]: any[] = this.handleMethodList(isArray(data), {
          SHARE: [1, '共享方法'],
          DEFAULT: [2, '默认方法']
        });
        this.funcMapping = mapping;
        this.methodList = list;
        return;
      }
      this.$message.error(error);
    } catch (e) {
      console.log(e);
    }
  }

  handleMethodList(list: any[], mapping: any = {}) {
    return list
      .reduce((pre: any, { funcId, funcType, funcName, funcArgs, paramsType }) => {
        if (funcType in mapping) {
          const [index, label] = mapping[funcType];
          if (!pre[index]) pre[index] = { label, children: [] };
          pre[index].children.push({
            label: funcName,
            value: funcName,
            type: funcType
          });
        }
        if (!pre[0]) pre[0] = {};
        pre[0][funcName] = {
          funcType,
          funcId,
          funcName,
          funcArgs,
          paramsType,
          isOneParams: funcArgs.length < 1
        };
        return pre;
      }, [])
      .filter(Boolean);
  }
  /* 解析All条件 */
  parseConditions(list: any[] = []) {
    if (list.length < 1) {
      return [this.generateConfigTemplate(false)];
    }
    return list.map((el) => this.parseCondition(el));
  }
  /* 解析条件 */
  parseCondition(data: any, isChange = false) {
    const { funcId, funcType, funcArgs, paramsType } = isObject(this.funcMapping[data.funcName]);
    const result: any = {
      name: data.name,
      funcName: data.funcName,
      funcId,
      funcType: isValidType(funcType) ? funcType : data.funcType
    };
    result.funcArgs = handleFuncArgs(funcType, funcArgs, data.funcArgs, isChange);
    result.tableHead = handleTableHead(funcType, data.funcType, funcArgs, result.funcArgs);
    result.tableData = handleTableData(
      isValidType(funcType) ? funcType : data.funcType,
      funcArgs,
      result.funcArgs,
      paramsType
    );
    return result;
  }

  generateConfigTemplate(needPush = true, config: any = {}) {
    const name = generateName(this.singletonCondition.length + 1);
    const base = isEmpty(config)
      ? {
          funcName: '',
          funcType: '',
          funcArgs: [],
          tableHead: [],
          tableData: []
        }
      : config;
    return needPush ? this.singletonCondition.push({ ...base, name }) : { ...base, name };
  }

  handleConfigChange(index) {
    this.$set(
      this.singletonCondition,
      index,
      this.parseCondition(this.singletonCondition[index], true)
    );
  }

  handleCopyConfig(index: number) {
    this.generateConfigTemplate(true, cloneDeep(this.singletonCondition[index]));
  }

  async handleConfirm() {
    try {
      const legalData = this.singletonCondition.filter(({ funcArgs, funcName, funcType, name }) => {
        return funcArgs && funcName && funcType && name;
      });
      if (legalData.length < 1) {
        this.$message.error('请至少配置一个条件');
        return;
      }
      await this.logicBar.validate();
      await this.logicConfig.validate();

      this.closeDialog(false);
    } catch (e) {
      console.log(e);
    }
  }
  closeDialog(resetData = true) {
    let data: any = this.rawData;
    if (!resetData) {
      const {
        singletonModeName,
        logicalRelationship,
        resultExpression,
        logicRelation,
        singletonCondition
      } = this.modelData;
      data = {
        singletonModeName,
        logicalRelationship,
        resultExpression,
        logicRelation,
        singletonCondition: singletonCondition.map(
          ({ funcArgs, funcName, funcId, funcType, name }: any) => ({
            funcArgs,
            funcName,
            funcId,
            funcType,
            name
          })
        )
      };
    }
    this.$emit('change', ...[data, this.index]);
    this.display = false;
  }
}
</script>
<style lang="scss" scoped>
$marginTop: 15px;
$padding: 20px;
$tabHeight: 48px;
$borderColor: #f1f1f1;

.config {
  &-dialog {
    ::v-deep .el-dialog {
      &__body {
        padding: 0;
      }
    }
  }

  &__container {
    margin-top: 20px;
  }
  &-resultExpression {
    display: flex;
    align-items: center;
    margin: 0 auto 20px;
    width: calc(100% - 70px);
    font-size: 14px;
    font-weight: 400;
    color: #444444;
    &__main {
      width: calc(100% - 90px);
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  &-content {
    margin-top: $marginTop;
    height: 400px;
    overflow-x: hidden;
    overflow-y: auto;
  }
}
</style>
