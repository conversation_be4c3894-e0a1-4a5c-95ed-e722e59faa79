<template>
  <pro-grid type="info" :title="$t('pa.data.table.detail.tableFields')" style="width: 100%">
    <!-- operation -->
    <div slot="operation" class="info-bar">
      <el-checkbox v-model="isSuperField">{{ $t('pa.data.seniorFields') }}</el-checkbox>
      <el-tooltip effect="light" placement="top" :content="$t('pa.data.table.tooltip.tooltip2')">
        <i class="iconfont icon-wenhao" style="margin-left: 10px"></i>
      </el-tooltip>
    </div>
    <!-- body -->
    <div class="table-field-body">
      <!-- header -->
      <div class="table-field-header">
        <!-- left -->
        <div class="table-field-header__left">
          <span class="table-field-header__title">{{ $t('pa.data.baseFields') }}</span>
          <bs-search
            v-model="keyword"
            :class="{ 'table-field-header__search': isEn }"
            :placeholder="$t('pa.placeholder.vaguePlaceholder')"
            @change="handleSearch"
          />
        </div>
        <!-- right -->
        <div class="table-field-header__right">
          <!-- 删除 -->
          <el-button type="primary" size="small" @click="handleDelete">{{ $t('pa.action.del') }}</el-button>
          <!-- 导出 -->
          <el-button v-if="status !== 'add'" type="primary" size="small" @click="handleExport">
            {{ $t('pa.action.export') }}
          </el-button>
          <!-- 导入 -->
          <el-button type="primary" size="small" @click="showUploadDialog = true">{{ $t('pa.action.import') }}</el-button>
          <!-- showSync -->
          <!-- 同步 -->
          <el-button v-if="showSync && !isUpdate" type="primary" size="small" @click="handleSync">
            {{ $t('pa.data.table.detail.sync') }}
          </el-button>
          <!-- 同步 -->
          <el-tooltip
            v-if="showSync && isUpdate"
            effect="light"
            placement="top"
            :value="isUpdate"
            :content="$t('pa.data.table.tooltip.tooltip1')"
          >
            <el-button type="primary" size="small" @click="handleSync">{{ $t('pa.data.table.detail.sync') }}</el-button>
          </el-tooltip>
          <!-- 添加字段 -->
          <el-button type="primary" size="small" @click="handleAdd">{{ $t('pa.data.table.detail.addField') }}</el-button>
        </div>
      </div>
      <bs-table
        ref="bsTableRef"
        v-loading="loading"
        selection
        crossing
        paging-front
        size="mini"
        height="290px"
        :data="tableData"
        :page-data="pageData"
        :column-settings="false"
        :column-data="columnData"
        :show-multiple-selection="false"
        :selectable="(row) => !row.disabled"
        @selection-change="handleSelectionChange"
        @page-change="handlePageChange"
      >
        <template slot="header-fieldName" slot-scope="{ row }">
          <span style="color: red; margin-right: 4px">*</span>
          <span>{{ row.label }}</span>
        </template>
        <template slot="header-fieldType" slot-scope="{ row }">
          <span style="color: red; margin-right: 4px">*</span>
          <span>{{ row.label }}</span>
        </template>
        <template slot="fieldName" slot-scope="{ row }">
          <el-input
            :id="`table-field--${row.id}`"
            v-model="row.fieldName"
            maxlength="50"
            :class="{ 'is-repeat': !!hasRepeat(row) }"
            @input="handleInput($event, 'fieldName', row)"
          />
        </template>
        <template slot="fieldNameCn" slot-scope="{ row }">
          <el-input v-model="row.fieldNameCn" maxlength="20" />
        </template>
        <!-- fieldType -->
        <template slot="fieldType" slot-scope="{ row }">
          <bs-select
            v-model="row.fieldType"
            filterable
            allow-create
            :options="typeList"
            :placeholder="$t('pa.placeholder.select')"
            default-first-option
          />
        </template>
        <!-- columnFamily -->
        <template slot="columnFamily" slot-scope="{ row }">
          <el-input v-model="row.columnFamily" />
        </template>
        <!-- primaryKey -->
        <template slot="primaryKey" slot-scope="{ row }">
          <el-switch v-model="row.primaryKey" active-value="1" inactive-value="0" />
        </template>
        <!-- partition -->
        <template slot="partition" slot-scope="{ row }">
          <el-switch v-model="row.partition" active-value="1" inactive-value="0" />
        </template>
        <!-- businessExplain -->
        <template slot="businessExplain" slot-scope="{ row }">
          <el-input v-model="row.businessExplain" maxlength="100" name="bus" />
        </template>
        <!-- operator -->
        <template slot="operator" slot-scope="{ row }">
          <i class="el-icon-delete" style="font-size: 18px; cursor: pointer" @click="handelFieldDelete(row)"></i>
        </template>
        <!-- footer-expand -->
        <div v-if="repeatCount" slot="footer-expand" class="tabTip">
          {{ $t('pa.data.table.detail.tips.fieldRFepeat', [repeatCount]) }}
        </div>
      </bs-table>
      <!-- 高级字段 -->
      <super-field
        v-show="isSuperField"
        ref="superFields"
        style="padding: 0 20px"
        :fields="advanceFieldList"
        :table="rawTableData"
        :error="advanceError"
      />
      <!-- 导入弹窗 -->
      <upload-dialog v-if="showUploadDialog" :show.sync="showUploadDialog" :res-type="resType" @confirm="handleConfirm" />
      <!-- 同步弹窗 -->
      <sync-dialog v-if="showSyncDialog" :show.sync="showSyncDialog" :data="syncData" @sync="handleFieldSync" />
    </div>
  </pro-grid>
</template>
<script lang="ts">
import { Vue, Component, Prop, Ref, Watch } from 'vue-property-decorator';
import { getTypeEnum, getColumnData, tableExport } from '@/apis/dataApi';
import { cloneDeep, uniqueId, includesPro, safeArray, safeParse } from '@/utils';
import { BsTable } from 'bs-ui-pro';
import { validSe } from '../utils';
@Component({
  components: {
    SuperField: () => import('./super-fields.vue'),
    UploadDialog: () => import('./upload-dialog.vue'),
    SyncDialog: () => import('./sync-dialog.vue')
  }
})
export default class DataTableField extends Vue {
  @Prop({ default: '' }) status!: string;
  @Prop({ default: () => null }) data!: any;
  @Prop({ default: () => null }) autoSyncConfig!: any;
  @Ref('bsTableRef') readonly bsTable!: BsTable;

  loading = false;
  id = '';
  isUpdate = false;
  resType = '';
  isSuperField = false;
  tableData: any[] = [];
  columnData: any[] = [];
  checkedRows: any[] = [];
  rawTableData: any[] = [];
  advanceError = '';

  keyword = '';
  typeList: any[] = [];
  pageData: any = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 0 };

  showSyncDialog = false;
  syncData: any = null;

  showUploadDialog = false;
  advanceFieldList: any[] = [];

  get isEdit() {
    return this.status === 'edit';
  }
  get showSync() {
    return this.autoSyncConfig?.show && !this.autoSyncConfig?.disable;
  }
  get repeatCount() {
    const rawList = this.rawTableData.map(({ fieldName }) => fieldName).filter(Boolean);
    const list = [...new Set(rawList)];
    return rawList.length - list.length;
  }
  @Watch('rawTableData')
  handleSourceChange() {
    (this.bsTable as any).doLayout();
    if (this.resType === 'REDIS') {
      this.$emit('change', this.rawTableData.length);
    }
  }

  created() {
    this.id = this.$route.query.id as string;
    this.isUpdate = this.$route.query.update === 'true';
    this.getTypeList();
  }

  /* 获取字段类型 */
  async getTypeList() {
    const { success, data, error } = await getTypeEnum();
    if (!success) return this.$message.error(error);
    this.typeList = safeArray(safeParse(data?.value1, [])).map(({ value, label }) => ({ value, label }));
  }
  /* 获取表头 */
  async getColumnData() {
    try {
      this.loading = true;
      const { success, data, error } = await getColumnData(this.resType);
      if (!success) return this.$message.error(error);
      this.columnData = safeArray(data);
    } finally {
      this.loading = false;
    }
  }
  /* 搜索 */
  handleSearch(handler = () => 1) {
    this.tableData = this.rawTableData.filter(({ fieldName }) => includesPro(fieldName, this.keyword));
    this.pageData.total = this.tableData.length;
    this.pageData.currentPage = typeof handler === 'function' ? handler() : 1;
  }
  /* 删除 */
  async handleDelete() {
    this.keyword = '';
    if (!this.checkedRows.length) return this.$message.warning(this.$t('pa.data.table.detail.tips.needToDel'));
    let name = this.checkedRows.map((it: any) => it.fieldName).join(',');
    if (name.length > 25) name = `${name.slice(0, 25)}...`;
    name = name.replace(/,$/gi, '');
    await this.$confirm(this.$t('pa.data.text3', [name]), this.$t('pa.prompt'), { type: 'warning' });
    const idList = this.checkedRows.map((it: any) => it.id);
    this.rawTableData = this.rawTableData.filter(({ id }) => !idList.includes(id));
    (this.bsTable as any).clearSelection();
    this.handleSearch(() => {
      const lastPage = Math.ceil(this.pageData.total / this.pageData.pageSize) || 1;
      return this.pageData.currentPage > lastPage ? lastPage : this.pageData.currentPage;
    });
  }
  /* 导出 */
  handleExport() {
    tableExport(this.resType, this.id);
  }
  /* 同步 */
  handleSync() {
    this.keyword = '';
    this.$emit('auto-sync');
  }
  /* 字段同步 */
  handleFieldSync(list: any[] = []) {
    const saveList = list.map((it, index) => {
      delete it.available;
      return { ...it, id: uniqueId() };
    });
    this.rawTableData = [...this.rawTableData, ...saveList];
    this.$message.success(this.$t('pa.data.table.detail.tips.syncSuccess', [saveList.length]));
    this.handleSearch(() => {
      const lastPage = Math.ceil(this.pageData.total / this.pageData.pageSize) || 1;
      return this.pageData.currentPage > lastPage ? lastPage : this.pageData.currentPage;
    });
  }
  /* 字段导入 */
  handleConfirm(data: any[]) {
    this.keyword = '';
    this.rawTableData = safeArray(data).map((it, index) => {
      const temp: any = {
        id: uniqueId(),
        fieldName: it.fieldName,
        fieldNameCn: it.fieldNameCn,
        fieldType: it.fieldType,
        primaryKey: it.primaryKey,
        partition: it.partition ? it.partition : '0',
        businessExplain: it.businessExplain
      };
      if (this.resType === 'HBASE') temp.columnFamily = it.columnFamily;
      return temp;
    });
    this.handleSearch(() => {
      const lastPage = Math.ceil(this.pageData.total / this.pageData.pageSize) || 1;
      return this.pageData.currentPage > lastPage ? lastPage : this.pageData.currentPage;
    });
  }
  /* 添加字段 */
  async handleAdd() {
    this.keyword = '';
    const param: any = {
      fieldName: '',
      fieldNameCn: '',
      fieldType: '', // 字段类型
      id: uniqueId(),
      primaryKey: '0', // 主键
      partition: '0', // 分区
      businessExplain: '' // 业务口径
    };
    if (this.resType === 'HBASE') param.columnFamily = '';
    this.rawTableData.push(param);
    this.handleSearch(() => {
      return Math.ceil(this.pageData.total / this.pageData.pageSize);
    });
    await this.$nextTick;
    const el: any = document.querySelector(`#table-field--${param.id}`);
    el && el.focus();
  }
  /* 选中 */
  handleSelectionChange(rows: any[]) {
    this.checkedRows = rows;
  }
  /* 分页 */
  handlePageChange(currentPage: number, pageSize: number) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
  }
  handleInput(value: string, key: string, row: any) {
    row[key] = validSe(value);
  }
  /* 删除字段 */
  async handelFieldDelete({ id, fieldName }: any) {
    await this.$confirm(this.$t('pa.data.table.detail.tips.delConfirm', [fieldName || '']), this.$t('pa.prompt'));
    this.rawTableData = this.rawTableData.filter((el) => el.id !== id);
    this.handleSearch(() => {
      const lastPage = Math.ceil(this.pageData.total / this.pageData.pageSize) || 1;
      return this.pageData.currentPage > lastPage ? lastPage : this.pageData.currentPage;
    });
  }
  // 判断是否是重复字段
  hasRepeat({ fieldName, columnFamily }) {
    const result: any[] = this.rawTableData.filter((it) => {
      if (this.resType !== 'HBASE') return it.fieldName && it.fieldName === fieldName;
      return it.fieldName && it.fieldName === fieldName && it.columnFamily === columnFamily;
    });
    return result.length >= 2;
  }

  public async init(data: any) {
    this.resType = data?.resType;
    await this.getColumnData();
    this.rawTableData = safeArray(this.data?.baseFieldInfo).map((it, index) => {
      const temp: any = {
        id: uniqueId(),
        fieldName: it.fieldName,
        fieldNameCn: it.fieldNameCn,
        fieldType: it.fieldType,
        primaryKey: it.primaryKey,
        partition: it.partition ? it.partition : '0',
        businessExplain: it.businessExplain
      };
      if (this.resType === 'HBASE') temp.columnFamily = it.columnFamily;
      return temp;
    });
    this.handleSearch();
  }
  public initAdvancedField() {
    this.advanceFieldList = this.data?.advanceFieldInfo;
    this.isSuperField = !!this.advanceFieldList.length;
  }
  /* 同步弹窗 */
  public openSyncDialog(params: any) {
    this.syncData = params;
    this.showSyncDialog = true;
  }
  public async validate() {
    if (!this.rawTableData.length && !this.advanceFieldList.length) {
      throw this.$message.error(this.$t('pa.data.table.detail.tips.leastOne'));
    }
    if (this.repeatCount) throw this.$message.error(this.$t('pa.data.table.detail.tips.repeat'));
    const baseFieldInfo = await this.baseFieldValidate();
    const advanceFieldInfo = await this.advanceFieldValidate();
    return { baseFieldInfo, advanceFieldInfo };
  }
  baseFieldValidate() {
    const result = cloneDeep(this.rawTableData);
    /* REDIS */
    if (this.resType === 'REDIS' && (result.length < 2 || result.length > 3)) {
      const msg =
        result.length > 3
          ? this.$t('pa.data.table.detail.excess3')
          : result.length < 2
          ? this.$t('pa.data.table.detail.lessThan2')
          : '';
      throw this.$message.error(this.$t('pa.data.table.detail.tips.notAllowedTip', [msg]));
    }
    /* HBASE */
    if (this.resType === 'HBASE') {
      const list = this.rawTableData.filter((it) => it.primaryKey === '1');
      if (list.length && list.length !== 1) {
        throw this.$message.error(this.$t('pa.data.table.detail.tips.hbaseTip1'));
      } else if (list.length === 0) {
        throw this.$message.error(this.$t('pa.data.table.detail.tips.hbaseTip2'));
      }
    }
    for (const it of result) {
      if (!it.fieldName || !it.fieldType) {
        throw this.$message.error(this.$t('pa.data.table.detail.tips.hbaseTip4'));
      }
      it.fieldName = it.fieldName.replace(/\s*/g, '');
    }
    return result;
  }
  advanceFieldValidate() {
    const result = cloneDeep(this.advanceFieldList);
    for (const it of result) {
      /* 水位线校验 */
      if (it.advanceFieldType === 'WATERMARK') {
        if (!it.field) {
          throw this.tip(this.$t('pa.data.table.placeholder.inputPlaceholder3'));
        }
        if (!it.column1) {
          throw this.tip(this.$t('pa.data.table.placeholder.inputPlaceholder4'));
        }
        if (!it.column2) {
          throw this.tip(this.$t('pa.data.table.placeholder.inputPlaceholder5'));
        }
      }
      /* 处理时间校验 */
      if (it.advanceFieldType === 'PROCTIME' && !it.field) {
        throw this.tip(this.$t('pa.data.table.placeholder.inputPlaceholder6'));
      }
      /* 处理时间校验 */
      if (it.advanceFieldType === 'OTHER' && !it.field) {
        throw this.tip(this.$t('pa.data.table.placeholder.inputPlaceholder7'));
      }
    }
    return result;
  }
  tip(msg: string) {
    this.advanceError = msg;
    return this.$message.error(this.advanceError);
  }
}
</script>
<style lang="scss" scoped>
.is-repeat {
  ::v-deep .el-input__inner {
    border-color: red;
  }
}
.table-field {
  width: 100%;
  border-top: 1px solid #f1f1f1;
  &-body {
    width: 100%;
    .tabTip {
      color: red;
    }
  }
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-top: 1px solid #f1f1f1;
    &__search {
      width: 260px !important;
    }
    &__title {
      margin-right: 10px;
      font-weight: 500;
    }
  }
}
</style>
