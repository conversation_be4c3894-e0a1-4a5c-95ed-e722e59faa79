<template>
  <div class="action__container">
    <div v-for="el in renderList" :key="el.name" class="action-item">
      <drop-down v-if="el.is === 'DropDown'" :data="el" @click="emitClick" />
      <el-tooltip v-else v-show="el.show" effect="light" placement="bottom" :content="el.content">
        <i
          class="iconfont"
          :class="`${el.icon} action-item__${el.type}`"
          @click="handler(el.type, el.name, el.icon.includes('is-active'))"
        ></i>
      </el-tooltip>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { debounce } from 'lodash';
import { hasPermission } from '@/utils';
import DropDown from './drop-down.vue';

@Component({ components: { DropDown } })
export default class HeaderActionBar extends Vue {
  @Prop() jobStatus!: any;
  @Prop() jobRuntimeId!: any;
  @Prop({ default: false }) fromMonitorFlow!: boolean;
  @Prop() flowType!: string;
  @PropSync('currentTab', { default: 'CANVAS' }) tabName!: string;

  private emitClick = debounce((...args) => this.$emit('click', ...args), 500);

  get typeIsJar() {
    return this.flowType === 'UDJ';
  }
  get showCanvasAction() {
    return !this.fromMonitorFlow && this.tabName === 'CANVAS';
  }
  get isDev() {
    return this.jobStatus === 'DEV';
  }
  get isPub() {
    return this.jobStatus === 'PUB';
  }
  get noProdNoRuntimeId() {
    return !this.isPub || !this.jobRuntimeId;
  }
  get noProd() {
    return this.jobStatus !== 'PROD';
  }
  get renderList() {
    return [
      {
        name: 'save',
        content: this.$t('pa.flow.save'),
        access: 'PA.FLOW.FLOW_MGR.ADD',
        type: 'button',
        show: this.showCanvasAction,
        icon: `iconfont icon-baocun ${this.isDev ? 'is-active' : ''}`
      },
      {
        name: 'handleCompile',
        content: this.$t('pa.flow.compile'),
        access: 'PA.FLOW.FLOW_MGR.COMPILE',
        type: 'button',
        show: this.showCanvasAction,
        icon: `iconfont icon-bianyi ${this.isDev && !this.typeIsJar ? 'is-active' : ''}`
      },
      {
        name: 'handleTest',
        content: this.$t('pa.flow.mockTest'),
        access: 'PA.FLOW.FLOW_MGR.TEST',
        type: 'button',
        show: this.showCanvasAction,
        icon: `iconfont icon-ceshi ${this.isDev && !this.typeIsJar ? 'is-active' : ''}`
      },
      {
        name: 'publish',
        content: this.$t('pa.flow.publish'),
        access: 'PA.FLOW.FLOW_MGR.PUBLISH',
        type: 'button',
        show: this.showCanvasAction,
        icon: `iconfont icon-fabu ${this.isDev ? 'is-active' : ''}`
      },
      {
        name: 'cancelPublish',
        content: this.$t('pa.flow.cancelPublish'),
        access: 'PA.FLOW.FLOW_MGR.CANCEL_PUBLISH',
        type: 'button',
        show: this.showCanvasAction,
        icon: `iconfont icon-quxiaofabu ${this.isPub ? 'is-active' : ''}`
      },
      {
        is: 'DropDown',
        name: 'online',
        show: this.showCanvasAction,
        access: 'PA.FLOW.FLOW_MGR.ONLINE',
        icon: 'iconfont icon-qidong1',
        disabled: !this.isPub && this.noProdNoRuntimeId,
        options: [
          {
            command: false,
            content: this.$t('pa.flow.msg10'),
            icon: 'el-icon-warning-outline',
            text: this.$t('pa.flow.noStatusRun'),
            disabled: !this.isPub
          },
          {
            command: true,
            content: this.$t('pa.flow.msg11'),
            icon: 'el-icon-warning-outline',
            text: this.$t('pa.flow.statusRun'),
            disabled: this.noProdNoRuntimeId
          }
        ]
      },
      {
        is: 'DropDown',
        name: 'offline',
        show: this.showCanvasAction,
        access: 'PA.FLOW.FLOW_MGR.OFFLINE',
        icon: 'iconfont icon-tingzhi',
        disabled: this.noProd,
        options: [
          {
            command: 'stop',
            text: this.$t('pa.flow.stop'),
            disabled: this.noProd
          },
          {
            command: 'retain',
            content: this.$t('pa.flow.msg12'),
            icon: 'el-icon-warning-outline',
            text: this.$t('pa.flow.stopWithStatus'),
            disabled: this.noProd
          },
          {
            command: 'force',
            text: this.$t('pa.flow.forceStop'),
            disabled: this.noProd
          }
        ]
      },
      {
        name: 'blood',
        content: this.isDev ? this.$t('pa.flow.msg264') : this.$t('pa.flow.bloodRelation'),
        access: 'PA.FLOW.FLOW_MGR.BLOOD_RELATION',
        type: 'button',
        show: this.showCanvasAction,
        icon: `iconfont icon-xieyuanguanxi2 ${!this.typeIsJar && !this.isDev ? 'is-active' : ''}`
      }
    ].filter((el) => hasPermission(el.access));
  }

  handler(type: string, name: string, canClick: boolean) {
    if (type === 'action') {
      this.$emit('tab-change', name);
      this.tabName = name;
      return;
    }
    if (!canClick) return;
    this.emitClick(name);
  }
}
</script>
<style lang="scss" scoped>
.action {
  &__container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 15px;
  }
  &-item {
    margin-left: 20px;
    &__action {
      color: #666666;
      background: #ffffff;
      border-radius: 0;
      padding: 0;
      cursor: pointer;
      &.is-active {
        color: #2196f3;
        background: #f2f5f9;
        border-radius: '4px';
        padding: '3px';
      }
    }
    &__button {
      color: #c8c8c8;
      cursor: not-allowed;
      &.is-active {
        color: #666666;
        cursor: pointer;
      }
    }
  }
}
</style>
