<template>
  <div class="action__container">
    <div v-for="el in renderList" :key="el.name" class="action-item">
      <drop-down v-if="el.is === 'DropDown'" v-access="el.access" :data="el" @click="emitClick" />
      <el-tooltip
        v-else
        v-show="el.show"
        v-access="el.access"
        effect="light"
        placement="bottom"
        :content="el.content"
      >
        <i
          class="iconfont"
          :class="`${el.icon} action-item__${el.type}`"
          @click="handler(el.type, el.name, el.icon.includes('is-active'))"
        ></i>
      </el-tooltip>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { debounce } from 'lodash';
import DropDown from './drop-down.vue';

@Component({ components: { DropDown } })
export default class HeaderActionBar extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: false }) fromMonitorFlow!: boolean;
  @PropSync('currentTab', { default: 'CANVAS' }) tabName!: string;

  private emitClick = debounce((...args) => this.$emit('click', ...args), 500);

  get showCanvasAction() {
    return !this.fromMonitorFlow && this.tabName === 'CANVAS';
  }
  get isDev() {
    return this.data.jobStatus === 'DEV';
  }
  get isPub() {
    return this.data.jobStatus === 'PUB';
  }
  get noProdNoRuntimeId() {
    return !this.isPub || !this.data.jobRuntimeId;
  }
  get noProd() {
    return this.data.jobStatus !== 'PROD';
  }
  get renderList() {
    return [
      {
        name: 'save',
        content: '保存',
        access: 'PA.FLOW.FLOW_MGR.ADD',
        type: 'button',
        show: this.showCanvasAction,
        icon: `iconfont icon-baocun ${this.isDev ? 'is-active' : ''}`
      },
      {
        name: 'handleCompile',
        content: '编译',
        access: 'PA.FLOW.FLOW_MGR.COMPILE',
        type: 'button',
        show: this.showCanvasAction,
        icon: `iconfont icon-bianyi ${this.isDev ? 'is-active' : ''}`
      },
      {
        name: 'handleTest',
        content: '测试',
        access: 'PA.FLOW.FLOW_MGR.TEST',
        type: 'button',
        show: this.showCanvasAction,
        icon: `iconfont icon-ceshi ${this.isDev ? 'is-active' : ''}`
      },
      {
        name: 'publish',
        content: '发布',
        access: 'PA.FLOW.FLOW_MGR.PUBLISH',
        type: 'button',
        show: this.showCanvasAction,
        icon: `iconfont icon-fabu ${this.isDev ? 'is-active' : ''}`
      },
      {
        name: 'cancelPublish',
        content: '取消发布',
        access: 'PA.FLOW.FLOW_MGR.CANCEL_PUBLISH',
        type: 'button',
        show: this.showCanvasAction,
        icon: `iconfont icon-quxiaofabu ${this.isPub ? 'is-active' : ''}`
      },
      {
        is: 'DropDown',
        name: 'online',
        show: this.showCanvasAction,
        access: 'PA.FLOW.FLOW_MGR.ONLINE',
        icon: 'iconfont icon-qidong1',
        disabled: !this.isPub && this.noProdNoRuntimeId,
        options: [
          {
            command: false,
            content: '流程首次启动，或者修改kafka consumer group等信息后需要从头开始消费数据。',
            icon: 'el-icon-warning-outline',
            text: '无状态启动',
            disabled: !this.isPub
          },
          {
            command: true,
            content:
              '流程重启，需要接着上次checkpoint记录的位置，如kafka上次的offset位置继续消费。',
            icon: 'el-icon-warning-outline',
            text: '基于上次状态启动',
            disabled: this.noProdNoRuntimeId
          }
        ]
      },
      {
        is: 'DropDown',
        name: 'offline',
        show: this.showCanvasAction,
        access: 'PA.FLOW.FLOW_MGR.OFFLINE',
        icon: 'iconfont icon-tingzhi',
        disabled: this.noProd,
        options: [
          {
            command: false,
            text: '停止',
            disabled: this.noProd
          },
          {
            command: true,
            text: '停止并保留状态',
            disabled: this.noProd
          }
        ]
      },
      {
        name: 'blood',
        content: '血缘关系',
        access: 'PA.FLOW.FLOW_MGR.BLOOD_RELATION',
        type: 'button',
        show: this.showCanvasAction,
        icon: `iconfont icon-xieyuanguanxi2 is-active`
      }
    ];
  }

  handler(type: string, name: string, canClick: boolean) {
    if (type === 'action') {
      this.$emit('tab-change', name);
      this.tabName = name;
      return;
    }
    if (!canClick) return;
    this.emitClick(name);
  }
}
</script>
<style lang="scss" scoped>
.action {
  &__container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 15px;
  }
  &-item {
    margin-left: 20px;
    &__action {
      color: #666666;
      background: #ffffff;
      border-radius: 0;
      padding: 0;
      cursor: pointer;
      &.is-active {
        color: #2196f3;
        background: #f2f5f9;
        border-radius: '4px';
        padding: '3px';
      }
    }
    &__button {
      color: #c8c8c8;
      cursor: not-allowed;
      &.is-active {
        color: #666666;
        cursor: pointer;
      }
    }
  }
}
</style>
