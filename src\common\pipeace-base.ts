import { Vue } from 'vue-property-decorator';
import moment from 'moment';
import { webSocket } from 'rxjs/webSocket';
import * as _ from 'lodash';
import { URL_RESCONF_GETRESTYPELIST } from '@/apis/commonApi';
import getWebConsole from '@/utils/get-web-console';
export class PaBase extends Vue {
  public ws: any;
  public resEnums: any = [];
  /**
   * 时间格式化
   */
  timeFormatter(data: string) {
    return data === undefined ? '' : moment(data).format('YYYY-MM-DD HH:mm:ss');
  }
  parsePro(text: string) {
    try {
      return JSON.parse(text);
    } catch (e) {
      // do nothing
    }
    return {};
  }
  /**
   * 解析字符串
   */
  parseResProperty(text: string, name: string) {
    try {
      return JSON.parse(text)[name];
    } catch (e) {
      // do nothing
    }
  }
  initWs(callback: any) {
    const userId = 'test' + _.now();
    this.ws = webSocket(this.getWsUrl() + '/statusWs/' + userId);
    this.ws.subscribe((res) => {
      callback(res);
    });
  }
  doPost(api: string, params?: any) {
    return params === undefined ? Vue.axios.post(api) : Vue.axios.post(api, params);
  }
  doPostDownload(api: string, params?: any) {
    return params === undefined
      ? Vue.axios.post(api, {}, { responseType: 'blob' })
      : Vue.axios.post(api, params, { responseType: 'blob' });
  }
  doGet(api: string, params?: any) {
    return params === undefined ? Vue.axios.get(api) : Vue.axios.get(api, params);
  }
  doPut(api: string, params?: any) {
    return params === undefined ? Vue.axios.put(api) : Vue.axios.put(api, params);
  }
  doDelete(api: string, params?: any) {
    return params === undefined ? Vue.axios.delete(api) : Vue.axios.delete(api, params);
  }
  parseResponse(resp: any, callback?: any) {
    const msg = _.toString(resp.msg);
    if (resp.success) {
      if (msg !== '') {
        this.$message.success(resp.msg);
      }
    } else {
      if (msg !== '') {
        this.$message.error(resp.msg);
      }
    }
    if (resp.success && callback) {
      callback();
    }
  }
  getWsUrl() {
    if (process.env.NODE_ENV === 'development') {
      return (
        'ws://' + window.location.hostname + ':' + window.location.port + Vue.axios.defaults.baseURL
      );
    } else {
      return (
        'ws://' +
        window.location.hostname +
        ':' +
        window.location.port +
        window.location.pathname.replace('/pipeace.html', '')
      );
    }
  }
  getEndpointOyzc() {
    if (process.env.NODE_ENV === 'development') {
      return (
        'http://' +
        window.location.hostname +
        ':' +
        window.location.port +
        Vue.axios.defaults.baseURL +
        '/endpointOyzc'
      );
    } else {
      return (
        'http://' +
        window.location.hostname +
        ':' +
        window.location.port +
        window.location.pathname.replace('/pipeace.html', '') +
        '/endpointOyzc'
      );
    }
  }
  getWebConsole(key: string) {
    return getWebConsole(key);
  }
  /**
   * 按钮功能权限控制（不能操作上级数据）
   */
  hasFeatureAuthority(code: any, dataLevel?: string) {
    if (code === undefined) {
      return false;
    }
    // 不能修改上级数据
    let isNotParent = false;
    if (dataLevel !== undefined) {
      isNotParent = dataLevel !== 'PARENT';
    } else {
      isNotParent = true;
    }
    return isNotParent && this.$store.state.userInfo.authorities.includes(code);
  }
  /**
   * 按钮功能权限控制（可以操作上级数据）
   */
  hasFeatureAuthorityWithParent(code: any, dataLevel?: string) {
    if (code === undefined) {
      return false;
    }
    return this.$store.state.userInfo.authorities.includes(code);
  }
  /**
   * 校验url上是否有中文
   * @param rule
   * @param value
   * @param callback
   */
  validZhongWenOnUrl(rule: any, value: any, callback: any) {
    // 校验中文的正则：/^[\u4e00-\u9fa5]{0,}$/
    // console.log(/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.test(value))
    // if (/^[\u4e00-\u9fa5]{0,}$/.test(value) === true) {
    //   callback(new Error('服务地址不允许有中文，包括中文符号'));
    // } else {
    //   // 校验通过
    //   callback();
    // }
    callback();
  }
  getResType(resType: string) {
    return _.find(this.resEnums, { type: resType });
  }
  getResLabel(resType: string, key: string) {
    const rec = this.getResType(resType);
    if (rec === undefined) {
      return '';
    }
    return rec[key];
  }
  getComponent(templateName: string) {
    return this.$refs[templateName]![0];
  }
  async getResEnums() {
    return await Vue.axios.get(URL_RESCONF_GETRESTYPELIST);
  }
  isUndefined(val, defaultVal) {
    if (val === undefined) {
      return defaultVal;
    }
    return val;
  }
}
