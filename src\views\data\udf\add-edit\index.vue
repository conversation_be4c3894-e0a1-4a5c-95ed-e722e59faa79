<template>
  <!-- UDF详情页 -->
  <pro-page
    v-loading="loading"
    :title="udfPageTitle"
    fixed-header
    :element-loading-text="$t('pa.loading')"
    :class="{ 'is-view': isViewPage }"
  >
    <!-- 头部操作按钮 -->
    <div slot="operation" class="udf-detail__header">
      <el-button
        v-for="item in udfDetailHeaderButtonList"
        v-show="item.display"
        :key="item.label"
        size="small"
        type="primary"
        @click="operateHandler(item.event, item.tempSave)"
      >
        {{ item.label }}
      </el-button>
    </div>
    <!-- Tab切换（基本信息、历史版本、引用关系） -->
    <el-tabs v-show="showTabs" v-model="activeTab" class="udf-detail__tab" @tab-click="handleTabClick">
      <el-tab-pane v-for="{ label, name } in tabPaneList" :key="name" :label="label" :name="name" />
      <el-button
        v-for="item in udfDetailHeaderButtonList"
        v-show="item.display"
        :key="item.label"
        size="small"
        type="primary"
        @click="operateHandler(item.event, item.tempSave)"
      >
        {{ item.label }}
      </el-button>
    </el-tabs>
    <pro-grid direction="column" :gutter="18">
      <!-- tab：历史版本 -->
      <pro-grid v-if="showVersionTab" type="info" :class="{ 'udf-detail__content': !isViewPage }">
        <roll-back
          v-if="showVersionTab"
          :data="detailSource"
          :share-flag="shareFlag"
          @versionChangeCallback="versionChange"
        />
      </pro-grid>
      <!-- tab：基本信息 -->
      <pro-grid
        v-if="detailVisible"
        :title="$t('pa.baseInformation')"
        type="info"
        :class="{ 'udf-detail__content': !isViewPage }"
      >
        <!-- 版本 -->
        <div class="tab-content">
          <el-row>
            <el-col :span="20">
              <!-- 表信息查看 -->
              <div v-if="isViewPage" class="chartInfoDetail">
                <div v-for="item in baseInfoContentKeyList" :key="item.label" class="item">
                  {{ item.label }}
                  <el-radio-group v-if="item.value === 'createType'" v-model="chartInfo.createType" disabled>
                    <el-radio label="UPLOAD"> {{ $t('pa.data.udf.detail.uploadZip') }} </el-radio>
                    <el-radio label="WRITE"> {{ $t('pa.data.udf.detail.manualWriting') }} </el-radio>
                  </el-radio-group>
                  <span v-else>{{ udfDetailInfo(item.value) }}</span>
                </div>
              </div>
              <!-- 表信息编辑 -->
              <el-form
                v-if="isAddEditPage"
                ref="udfForm"
                :model="chartInfo"
                class="data-form-inline"
                :rules="chartRules"
                :label-width="isEn ? '180px' : '130px'"
                :label-position="'right'"
              >
                <el-form-item :label="$t('pa.data.udf.detail.udfName')" prop="udf">
                  <el-input v-model="chartInfo.udf" :disabled="isEditPage" style="width: 85%" maxlength="20" />
                </el-form-item>
                <el-form-item :label="$t('pa.data.table.detail.chineseName')" prop="udfCn">
                  <el-input v-model="chartInfo.udfCn" style="width: 85%" maxlength="20" />
                </el-form-item>
                <el-form-item :label="$t('pa.data.udf.detail.type')" prop="type">
                  <el-select
                    v-model="chartInfo.type"
                    :placeholder="$t('pa.data.udf.detail.placeholder.typePlaceholder')"
                    class="tab-content__item"
                    :disabled="udfFormItemDisabled"
                    @change="getUdfTemplateCode"
                  >
                    <el-option v-for="item in udfTypeList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('pa.data.udf.detail.udfDomainName')" prop="domain">
                  <el-input
                    :disabled="udfFormItemDisabled"
                    :value="chartInfo.domain"
                    maxlength="100"
                    class="tab-content__item"
                    @input="handleUdfDomainInput"
                  />
                </el-form-item>
                <el-form-item :label="$t('pa.data.udf.detail.explain')" prop="businessExplain">
                  <el-input
                    v-model="chartInfo.businessExplain"
                    type="textarea"
                    row="5"
                    class="tab-content__item"
                    :placeholder="$t('pa.data.udf.detail.placeholder.explainPlaceholder')"
                    maxlength="100"
                  />
                </el-form-item>
                <el-form-item :label="$t('pa.data.udf.detail.addMode')" prop="createType">
                  <el-radio-group v-model="chartInfo.createType" :disabled="createTypeDisabled" @change="showUploadButton">
                    <el-radio label="UPLOAD"> {{ $t('pa.data.udf.detail.uploadZip') }} </el-radio>
                    <el-radio label="WRITE"> {{ $t('pa.data.udf.detail.manualWriting') }} </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-button v-if="uploadVisible" class="upload-file" @click="showUploadDialog">
                  {{ $t('pa.action.uploadFiles') }}
                </el-button>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </pro-grid>
      <!-- 源码 -->
      <pro-grid v-show="sourceCodeVisible" class="source-code" type="info" :title="$t('pa.sourceCode')">
        <!-- 源码头部按钮组（下载、复制、关联类、关联方法\依赖） -->
        <div slot="operation" class="source-code__buttons">
          <!-- 提示 -->
          <el-tooltip
            v-if="isAddEditPage"
            class="source-code__buttons--tooltip"
            effect="light"
            :content="$t('pa.data.udf.detail.tips.sourceTip')"
            placement="bottom"
          >
            <i class="iconfont icon-wenhao" style="cursor: pointer"></i>
          </el-tooltip>
          <!-- <el-button v-if="isViewPage" size="small" type="primary" @click="downloadUdfAsZip">
            下载
          </el-button> -->
          <el-button
            v-if="isViewPage"
            v-clipboard:copy="sourceCode"
            v-clipboard:success="copySuccess"
            v-clipboard:error="copyError"
            size="small"
            type="primary"
          >
            {{ $t('pa.action.copy') }}
          </el-button>
          <el-button
            v-for="item in sourceCodeButtonList"
            v-show="isAddEditPage"
            :key="item.label"
            :disabled="sourceCodeButtonListDisabled || file"
            size="small"
            type="primary"
            @click="item.event(item.label)"
          >
            {{ item.label }}
          </el-button>
        </div>
        <source-code :key="key" ref="code" :code="sourceCode" :read-only="sourceCodeReadOnly" />
      </pro-grid>
      <!-- tab：引用关系 -->
      <pro-grid v-if="referenceRelationVisible" type="info" :class="{ 'udf-detail__content': !isViewPage }">
        <el-button slot="operation" type="primary" @click="exportRelationList">{{ $t('pa.action.download') }}</el-button>
        <reference-relation :id="this.$route.query.id" relation="udf" />
      </pro-grid>
    </pro-grid>
    <!-- 保存弹窗 -->
    <save-confirm
      v-if="saveConfirmVisible"
      ref="saveConfirm"
      :show.sync="saveConfirmVisible"
      :require="false"
      @saveConfirmCallback="saveConfirmCallback"
    />
    <!-- UDF文件上传及模板下载 -->
    <upload
      type="UDF"
      :visible="uploadDialogVisible"
      :title="uploadDialogTitle"
      :res-type="info.resType"
      :download-type="chartInfo.type"
      @close="closeUploadDialog"
    />
    <!-- 关联方法、类/依赖弹窗 -->
    <relation-dialog
      v-show="relationDialogVisible"
      :visible.sync="relationDialogVisible"
      :checked-data="checkedData"
      @getData="getCheckedData"
    />
    <!-- 测试弹窗 -->
    <test-dialog v-if="testDialogVisible" :visible.sync="testDialogVisible" :data="detailSource" :sql="sqlCode" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Watch, InjectReactive } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import {
  URL_UDF_DOWLOAD,
  URL_UDF_FINDBYID,
  URL_UDF_TEMPLATE_CODE,
  URL_EXPORT_UDF_RELATION_LIST,
  URL_UDF_ASSEMBLE_CODE,
  URL_UDF_ADD_ON_WRITE,
  URL_UDF_ADD_ON_UPLOAD,
  URL_UDF_UPDATE_ON_WRITE,
  URL_UDF_UPDATE_ON_UPLOAD,
  URL_UDF_TESTSQL
} from '@/apis/commonApi';
import { get, post, download } from '@/apis/utils/net';
import VueClipboard from 'vue-clipboard2';
import CommonDelete from '@/utils/mixins/common-delete';
import ReferenceRelation from '@/views/data/components/reference-relation.vue';
import Upload from '../../modals/upload.vue';
import SaveConfirm from '../../modals/save-confirm.vue';
import RollBack from '../version/index.vue';
import SourceCode from '../components/source-code.vue';
import RelationDialog from '../components/relation-dialog.vue';
import TestDialog from '../components/test-dialog.vue';
PaBase.use(VueClipboard);
@Component({
  name: 'ViewAddEdit',
  components: {
    SaveConfirm,
    Upload,
    RollBack,
    SourceCode,
    ReferenceRelation,
    RelationDialog,
    TestDialog
  },
  mixins: [CommonDelete]
})
export default class ViewAddEdit extends PaBase {
  @InjectReactive('isCloud') isCloud!: boolean;
  private showVersionTab = false;
  // 当前UDF数据
  private detailSource: any = {};
  private udfTypeList: any = [
    { value: 'UDF', label: 'UDF' },
    { value: 'UDAF', label: 'UDAF' },
    { value: 'UDTF', label: 'UDTF' },
    { value: 'ASYNCUDF', label: 'ASYNCUDF' }
  ];
  private status: any = 'add';
  private uploadDialogVisible = false;
  private uploadDialogTitle: any = '';
  private chartInfo: any = {
    domain: '', // 域名
    udf: '',
    udfCn: '',
    businessExplain: '',
    type: '',
    createType: ''
  };
  private id = '';
  private loading = false;
  private value: any = [];
  private file: any = null;
  private shareFlag = false;
  private sourceCode = `public static String functionName(){
    return "";
}`;
  private info = {
    resType: '', // 服务类型
    service: '',
    checked: false, // 开启数据权限
    orgCodeFiled: '' // 组织机构字段
  };
  private visible = false;
  private fileName = '';
  private chartRules = {
    udf: [{ required: true, validator: this.validateUdfName, trigger: 'blur' }],
    domain: [{ required: true, message: this.$t('pa.data.udf.detail.placeholder.domainPlaceholder'), trigger: 'blur' }],
    type: [{ required: true, message: this.$t('pa.data.udf.detail.placeholder.typeInputPlaceholder'), trigger: 'change' }],
    createType: [{ required: true, message: this.$t('pa.data.udf.detail.placeholder.addTypePlaceholder'), trigger: 'blur' }]
  };
  private baseInfoContentKeyList = [
    {
      label: `${this.$t('pa.data.udf.detail.udfName')}：`,
      value: 'udf'
    },
    {
      label: `${this.$t('pa.data.table.detail.chineseName')}：`,
      value: 'udfCn'
    },
    {
      label: `${this.$t('pa.data.udf.detail.type')}：`,
      value: 'type'
    },
    {
      label: `${this.$t('pa.data.udf.detail.explain')}：`,
      value: 'businessExplain'
    },
    {
      label: `${this.$t('pa.data.udf.detail.udfDomainName')}：`,
      value: 'domain'
    },
    {
      label: `${this.$t('pa.creator')}：`,
      value: 'createdBy'
    },
    {
      label: `${this.$t('pa.data.udf.detail.addPhone')}：`,
      value: 'createdByMobile'
    },
    {
      label: `${this.$t('pa.data.udf.detail.addMode')}：`,
      value: 'createType'
    }
  ];
  private sourceCodeButtonList = [{ label: this.$t('pa.data.udf.detail.correlationType'), event: this.openRelationDialog }];
  private key = Date.now();
  private relationDialogVisible = false;
  private testDialogVisible = false;
  private relationDialogTitle = '';
  private uploadButtonVisible = false;
  private saveConfirmVisible = false;
  private activeTab = 'info';
  private tabPaneList = [
    { label: this.$t('pa.baseInformation'), name: 'info' },
    { label: this.$t('pa.historyVersion'), name: 'history' },
    { label: this.$t('pa.citationRelation'), name: 'relation' }
  ];
  private tempSave = false;
  private relationParams: any = {};
  private checkedData = {};
  private preFile = null;
  private sqlCode = '';
  // 源码框只读
  get sourceCodeReadOnly() {
    return (this.isViewPage || this.file ? true : false) || this.chartInfo.createType === 'UPLOAD';
  }

  get sourceCodeButtonListDisabled() {
    if (!this.chartInfo.type) return true;
    if ((['UPLOAD', ''].includes(this.chartInfo.createType) && this.status === 'add') || this.file) return true;
    return false;
  }

  // 上传文件按钮显隐
  get uploadVisible() {
    return this.uploadButtonVisible || (this.chartInfo.createType === 'UPLOAD' && this.status === 'edit');
  }

  // 创建方式禁用
  get createTypeDisabled() {
    return this.status !== 'add';
  }

  // 新建/编辑状态下展示tab
  get showTabs() {
    return !['add', 'edit'].includes(this.status);
  }

  get referenceRelationVisible() {
    return this.activeTab === 'relation';
  }

  get detailVisible() {
    return this.activeTab === 'info' || this.status === 'edit';
  }

  get sourceCodeVisible() {
    return (
      ['info', ''].includes(this.activeTab) &&
      (this.chartInfo.createType === 'WRITE' || (this.status === 'detail' && this.chartInfo.createType === 'UPLOAD'))
    );
  }

  get isViewPage() {
    return this.status === 'detail';
  }

  get isEditPage() {
    return this.status === 'edit';
  }

  get udfDetailHeaderButtonList() {
    // （新建、编辑页面）显示暂存、提交按钮
    const showViewButtons = this.isAddEditPage && !this.showVersionTab;
    const showTempSaveButton =
      this.status === 'add' || (showViewButtons && ['WRITE', ''].includes(this.chartInfo.createType));
    // （查看页面）显示、编辑按钮
    const showOperateButtons = this.isViewPage && this.activeTab === 'info' && !this.shareFlag;
    // （查看页面）显示下载按钮
    const showDownloadButton =
      (this.isViewPage && this.activeTab === 'info') || (this.status === 'edit' && this.chartInfo.createType === 'UPLOAD');
    const showRelationButton = this.isViewPage && this.activeTab === 'relation';
    return [
      {
        label: this.$t('pa.action.download'),
        display: showDownloadButton,
        event: 'downloadUdfAsZip',
        tempSave: false
      },
      {
        label: this.$t('pa.action.test'),
        display: this.isAddEditPage,
        event: 'openTestDialog',
        tempSave: false
      },
      {
        label: this.$t('pa.action.staging'),
        display: showTempSaveButton,
        event: 'submit',
        tempSave: true
      },
      {
        label: this.$t('pa.action.submit'),
        display: showViewButtons,
        event: 'submit',
        tempSave: false
      },
      {
        label: this.$t('pa.action.edit'),
        display: showOperateButtons,
        event: 'switchToEdit',
        tempSave: false
      },
      {
        label: this.$t('pa.action.downloadRelation'),
        display: showRelationButton,
        event: 'exportRelationList',
        tempSave: false
      }
    ];
  }

  get udfPageTitle() {
    const statusMap = {
      add: this.$t('pa.data.udf.addUdf'),
      detail: this.$t('pa.data.udf.viewUdf'),
      edit: this.$t('pa.data.udf.editUdf')
    };
    return statusMap[this.status];
  }

  // 当前页面为新建/编辑页
  get isAddEditPage() {
    return ['add', 'edit'].includes(this.status) ? true : false;
  }

  get sourceCodeIsEmpty() {
    if ((this.$refs.code as any).sourceCode.trim() === '') return true;
    return false;
  }

  // UDF类型选择禁用
  get udfFormItemDisabled() {
    return this.status === 'edit' && this.chartInfo.createType === 'UPLOAD';
  }

  @Watch('status')
  handleIsViewPageChange(val) {
    if (val === 'edit') {
      const header: any = document.querySelector('.bs-pro-page__header');
      header.style.display = 'flex';
    }
  }

  created() {
    if (this.$route.query.id) {
      this.id = this.$route.query.id as string;
      this.status = this.$route.query.status;
    }
    this.getUdfDetail();
  }

  mounted() {
    const header: any = document.querySelector('.bs-pro-page__header');
    const layout: any = document.querySelector('.bs-layout');
    if (this.isViewPage) {
      header.style.display = 'none';
      layout.style.overflow = 'hidden';
    }
  }

  // UDF名非空、驼峰校验
  validateUdfName(rule, value, callback) {
    const re = /^[a-zA-Z]{1}\w*$/;
    if (value === '') {
      callback(new Error(this.$t('pa.data.udf.detail.placeholder.name')));
    } else if (value !== '' && !re.test(value)) {
      callback(new Error(this.$t('pa.data.udf.detail.tips.nameTip')));
    } else {
      callback();
    }
  }

  handleUdfDomainInput(e) {
    this.chartInfo.domain = e.replace(/[\u4E00-\u9FA5]/g, '');
  }

  // 导出引用关系
  async exportRelationList() {
    try {
      await download(`${URL_EXPORT_UDF_RELATION_LIST}?id=${this.$route.query.id}`);
    } catch (error) {
      this.$message.error(error);
    }
  }

  private getUdfParams(isEdit = false, memo = '', tempSave = false) {
    /**
     * createType: UDF创建方式
     * file: 上传java文件,
     * id：唯一标识
     * memo：更新备注,
     * udfExplain：功能说明
     * udfName：UDF名称
     * udfNameCn：UDF中文名
     * udfNameMain：全域名
     * udfType：UDF类型
     */
    const [
      { businessExplain: udfExplain, udf: udfName, udfCn: udfNameCn, domain: udfNameMain, type: udfType, createType },
      { id },
      { otherFuncs, imports, jars }
    ] = [this.chartInfo, this, this.relationParams];
    const [params, isWrite]: any = [
      {
        udfExplain,
        udfName,
        udfNameCn,
        udfNameMain,
        udfType,
        createType,
        tempSave,
        otherFuncs,
        imports,
        jars
      },
      createType === 'WRITE'
    ];
    if (isWrite || (!isWrite && !this.file)) {
      params.sourceCode = (this.$refs as any).code.sourceCode;
    }
    if (isEdit) {
      params.id = id;
      params.memo = memo;
    }
    return params;
  }

  handleTabClick({ label }) {
    if (label) {
      this.showVersionTab = label === this.$t('pa.historyVersion') ? true : false;
    }
    this.$forceUpdate();
  }

  udfDetailInfo(value) {
    return this.chartInfo.hasOwnProperty(value) ? this.chartInfo[value] : this.detailSource[value];
  }

  showUploadButton(label) {
    this.uploadButtonVisible = label === 'UPLOAD' ? true : false;
    if (label === 'WRITE' && this.chartInfo.type) {
      this.getUdfTemplateCode();
    }
  }

  // 根据选择的UDF类型展示不同的源码信息
  async getUdfTemplateCode() {
    this.loading = true;
    try {
      const { data, success, msg } = await get(URL_UDF_TEMPLATE_CODE, {
        udfType: this.chartInfo.type
      });
      if (success) {
        this.sourceCode = data;
        this.loading = false;
        return;
      }
      this.$message.error(msg);
    } catch {
      this.loading = false;
    }
  }

  showUploadDialog() {
    this.uploadDialogVisible = true;
    this.uploadDialogTitle = this.$t('pa.data.udf.detail.uploadZip');
  }

  validateDomin(rule, value, callback) {
    const re = /[\u4E00-\u9FA5]/g;
    if (value === '') {
      callback(new Error(this.$t('pa.data.udf.detail.placeholder.domainPlaceholder')));
    } else if (value !== '' && re.test(value)) {
      callback(new Error(this.$t('pa.data.udf.detail.tips.dominTip')));
    } else {
      callback();
    }
  }

  /**
   * 获取弹窗中选中的数据
   * otherFuncs：关联方法
   * imports：第三方类库/依赖
   * preImports：上次选中的第三方类库/依赖
   */
  async getCheckedData({ otherFuncs, imports, preImports }) {
    //
    try {
      this.loading = true;
      const { success, msg, data } = await post(URL_UDF_ASSEMBLE_CODE, {
        createType: this.chartInfo.createType,
        id: this.id,
        otherFuncs: JSON.stringify(otherFuncs),
        sourceCode: (this.$refs as any).code.sourceCode,
        tempSave: this.tempSave,
        udfExplain: this.chartInfo.businessExplain,
        udfName: this.chartInfo.udf,
        udfNameCn: this.chartInfo.udfCn,
        udfNameMain: this.chartInfo.domain,
        udfType: this.chartInfo.type,
        memo: '',
        imports: JSON.stringify(imports),
        preImports: JSON.stringify(preImports)
      });
      if (success) {
        // 保存组装源码的返回值，作为提交时的传参
        this.relationParams = {
          otherFuncs: data.otherFuncs || '',
          imports: data.imports || '',
          jars: data.jars || ''
        };
        this.sourceCode = data.sourceCode;
        this.key = Date.now();
      } else {
        this.$message.error(msg);
      }
      this.loading = false;
    } catch {
      this.loading = false;
    }
  }

  // 下载当前UDF
  async downloadUdfAsZip() {
    try {
      this.loading = true;
      await download(`${URL_UDF_DOWLOAD}?id=${this.id}`, '', null);
    } finally {
      this.loading = false;
    }
  }

  closeUploadDialog(data, needFresh) {
    if (needFresh && data.size) {
      this.fileName = '';
      this.file = data;
      this.fileName = data.name;
    }
    this.uploadDialogVisible = false;
  }

  // 提交修改信息
  submit() {
    if (this.chartInfo.type === 'WRITE' && this.sourceCodeIsEmpty) {
      this.$message.error(this.$t('pa.data.udf.detail.tips.codeTip'));
      return;
    }
    if (this.chartInfo.createType == 'UPLOAD' && !this.file && !this.id) {
      this.$message.error(this.$t('pa.tip.uploadFiles', ['java']));
      return;
    }
    const udfForm: any = this.$refs['udfForm'];
    udfForm.validate(async (valid) => {
      if (valid) {
        const isWrite = this.chartInfo.createType === 'WRITE';
        // 编辑
        if (this.id) {
          this.saveConfirmVisible = true;
        } else {
          this.loading = true;
          const submitData = new FormData();
          let config: Record<string, any> = {};
          if (!isWrite) {
            config = {
              timeout: 1500000,
              headers: {
                'Content-Type': 'multipart/form-data'
              }
            };
            submitData.append('file', this.file);
          }
          try {
            const api = isWrite
              ? URL_UDF_ADD_ON_WRITE
              : `${URL_UDF_ADD_ON_UPLOAD}/?tempSave=${this.tempSave}&createType=${
                  this.chartInfo.createType
                }&udfExplain=${encodeURIComponent(this.chartInfo.businessExplain)}&udfName=${
                  this.chartInfo.udf
                }&udfNameCn=${encodeURIComponent(this.chartInfo.udfCn)}&udfNameMain=${this.chartInfo.domain}&udfType=${
                  this.chartInfo.type
                }`;

            const { data, success, msg } = await post(
              api,
              isWrite ? this.getUdfParams(false, '', this.tempSave) : submitData,
              isWrite ? undefined : config
            );
            if (success) {
              const msg = this.tempSave
                ? this.$t('pa.data.udf.detail.tips.StagingSuccess')
                : this.$t('pa.data.udf.detail.tips.addSuccess');
              this.$message.success(msg);
              this.$router.push({
                name: `refresh`,
                query: {
                  id: data.id,
                  title: `UDF：${data.udfName}`,
                  status: 'detail'
                }
              });
            } else {
              this.$message.error(msg);
            }
            this.loading = false;
          } catch {
            this.loading = false;
          }
        }
      }
    });
  }

  // 编辑状态保存处理
  async saveConfirmCallback(data) {
    if (this.chartInfo.type === 'WRITE' && this.sourceCodeIsEmpty) {
      this.$message.error(this.$t('pa.data.udf.detail.tips.codeTip'));
      return;
    }
    if (data === 0) return;
    const isWrite = this.chartInfo.createType === 'WRITE';
    this.saveConfirmVisible = false;
    this.loading = true;
    const config = {
      timeout: 1500000,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    };
    const submitData = new FormData();
    if (this.file || this.preFile) {
      submitData.append('file', this.file || this.preFile);
    }
    const api = isWrite
      ? URL_UDF_UPDATE_ON_WRITE
      : `${URL_UDF_UPDATE_ON_UPLOAD}/?createType=${this.chartInfo.createType}&id=${this.id}&tempSave=${this.tempSave}&udfExplain=${this.chartInfo.businessExplain}&udfName=${this.chartInfo.udf}&udfNameCn=${this.chartInfo.udfCn}&udfNameMain=${this.chartInfo.domain}&udfType=${this.chartInfo.type}`;
    // if (!isWrite && !this.file) api = URL_UDF_UPDATE_ON_WRITE;
    const res: any = await post(
      api,
      isWrite ? this.getUdfParams(true, data.comments, this.tempSave) : submitData,
      isWrite ? undefined : config
    );
    this.parseResponse(res);
    if (res.success) {
      this.$router.push({
        name: 'refresh',
        query: { id: this.$route.query.id, status: 'detail', title: `UDF：${this.chartInfo.udf}` }
      });
    }
    this.loading = false;
    this.preFile = this.file;
    this.file = null;
  }

  // 版本回滚
  versionChange(e) {
    if (e) {
      this.getUdfDetail();
    }
  }

  async getUdfDetail() {
    this.$forceUpdate();
    // 编辑赋值
    if (this.id) {
      this.loading = true;
      const api = URL_UDF_FINDBYID + '?id=' + this.id;
      let data: any = {};
      await get(api).then((resp: any) => {
        this.parseResponse(resp, () => {
          data = resp!.data;
        });
      });
      this.detailSource = data;
      this.chartInfo.udf = data.udfName;
      this.chartInfo.udfCn = data.udfNameCn;
      this.chartInfo.domain = data.udfNameMain;
      this.chartInfo.type = data.udfType;
      this.chartInfo.businessExplain = data.udfExplain;
      this.chartInfo.createType = data.createType;
      this.shareFlag = data.shareFlag;
      this.sourceCode = data.sourceCode;
      // 获取选中的关联方法、关联类/依赖
      this.checkedData = {
        // 第三方类库
        imports: data.imports ? JSON.parse(data.imports) : [],
        // 依赖
        jars: data.jars ? JSON.parse(data.jars) : [],
        // 关联方法
        otherFuncs: data.otherFuncs ? JSON.parse(data.otherFuncs) : []
      };
      this.relationParams = {
        imports: data.imports ? JSON.parse(data.imports) : [],
        jars: data.jars ? JSON.parse(data.jars) : [],
        otherFuncs: data.otherFuncs ? JSON.parse(data.otherFuncs) : []
      };
      this.loading = false;
    }
  }

  openTestDialog() {
    const udfForm: any = this.$refs['udfForm'];
    udfForm.validate(async (valid) => {
      if (valid) {
        this.detailSource.udfType = this.chartInfo.type;
        if (
          (this.status === 'add' && this.chartInfo.createType === 'UPLOAD') ||
          (this.file && this.status === 'edit' && this.chartInfo.createType === 'UPLOAD') ||
          (this.status === 'edit' && this.chartInfo.createType === 'UPLOAD' && this.detailSource.udfState === 'DEV')
        ) {
          this.$message.error(this.$t('pa.data.udf.detail.tips.zipTip'));
          return;
        }
        this.getUdfTestSql();
      }
    });
  }

  async getUdfTestSql() {
    this.loading = true;
    try {
      const { data, success, msg } = await get(`${URL_UDF_TESTSQL}?id=${this.detailSource.id}&type=${this.chartInfo.type}`);
      if (success) {
        this.loading = false;
        this.sqlCode = data;
        this.testDialogVisible = true;
        this.setDetailSourceData();
      } else {
        this.$message.error(msg);
        this.loading = false;
        return;
      }
    } catch (err) {
      this.loading = false;
      this.testDialogVisible = false;
      return;
    }
  }

  setDetailSourceData() {
    this.detailSource.sourceCode = '';
    this.detailSource.sourceCode = (this.$refs.code as any).sourceCode || this.chartInfo.sourceCode;
    this.detailSource.createType = this.chartInfo.createType;
    this.detailSource.tempSave = this.tempSave;
    this.detailSource.udfExplain = this.chartInfo.businessExplain;
    this.detailSource.udfName = this.chartInfo.udf;
    this.detailSource.udfNameCn = this.chartInfo.udfCn;
    this.detailSource.udfNameMain = this.chartInfo.domain;
    this.detailSource.udfType = this.chartInfo.type;
    this.detailSource.otherFuncs = this.relationParams.otherFuncs || this.detailSource.otherFuncs;
  }

  openRelationDialog(title) {
    this.relationDialogTitle = title;
    this.relationDialogVisible = true;
  }

  // 头部操作按钮触发
  operateHandler(event, tempSave) {
    this.tempSave = tempSave;
    this[event]();
  }
  // 切换至编辑状态
  switchToEdit() {
    this.$router.push({
      name: 'refresh',
      query: { id: this.$route.query.id, status: 'edit', title: `UDF：${this.chartInfo.udf}` }
    });
  }

  copySuccess() {
    this.$message.success(this.$t('pa.data.copySuccess'));
  }

  copyError() {
    this.$message.error(this.$t('pa.data.copyError'));
  }
}
</script>

<style scoped lang="scss">
.is-view {
  height: calc(100vh - 30px);
}
.source-code__buttons {
  &--tooltip {
    margin-right: 10px;
  }
}
.el-tabs {
  background: #fff;
  border-bottom: 1px solid #e2e2e2;
  ::v-deep &__header {
    line-height: 64px;
    margin-left: 20px;
  }
}
.udf-detail {
  &__header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  &__content {
    margin-top: 20px;
  }
  &__tab {
    display: flex;
    justify-content: space-between;
    padding-right: 25px;
    align-items: center;
    height: 64px;
  }
}
.tab-content {
  padding: 20px 25px;
  &__col {
    display: flex;
  }
  &__item {
    width: 85%;
  }
  .upload-file {
    margin-left: 125px;
  }
  .chartInfoDetail {
    .item {
      margin-bottom: 30px;
    }
    .top {
      div {
        width: 261px;
        margin-right: 10px;
      }
    }
  }
}
</style>
