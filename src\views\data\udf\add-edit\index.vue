<template>
  <!-- UDF详情页 -->
  <pro-page
    v-loading="loading"
    :title="udfPageTitle"
    fixed-header
    element-loading-text="拼命加载中..."
    :class="{ 'is-view': isViewPage }"
  >
    <!-- 头部操作按钮 -->
    <div slot="operation" class="udf-detail__header">
      <el-button
        v-for="item in udfDetailHeaderButtonList"
        v-show="item.display"
        :key="item.label"
        size="small"
        type="primary"
        @click="operateHandler(item.event, item.tempSave)"
      >
        {{ item.label }}
      </el-button>
    </div>
    <!-- Tab切换（基本信息、历史版本、引用关系） -->
    <el-tabs
      v-show="showTabs"
      v-model="activeTab"
      class="udf-detail__tab"
      @tab-click="handleTabClick"
    >
      <el-tab-pane v-for="{ label, name } in tabPaneList" :key="name" :label="label" :name="name" />
      <el-button
        v-for="item in udfDetailHeaderButtonList"
        v-show="item.display"
        :key="item.label"
        size="small"
        type="primary"
        @click="operateHandler(item.event, item.tempSave)"
      >
        {{ item.label }}
      </el-button>
    </el-tabs>
    <pro-grid direction="column" :gutter="18">
      <!-- tab：历史版本 -->
      <pro-grid v-if="showVersionTab" type="info" :class="{ 'udf-detail__content': !isViewPage }">
        <roll-back
          v-if="showVersionTab"
          :data="detailSource"
          @versionChangeCallback="versionChange"
        />
      </pro-grid>
      <!-- tab：基本信息 -->
      <pro-grid
        v-if="detailVisible"
        title="基本信息"
        type="info"
        :class="{ 'udf-detail__content': !isViewPage }"
      >
        <!-- 版本 -->
        <div class="tab-content">
          <el-row>
            <el-col :span="20">
              <!-- 表信息查看 -->
              <div v-if="isViewPage" class="chartInfoDetail">
                <div v-for="item in baseInfoContentKeyList" :key="item.label" class="item">
                  {{ item.label }}
                  <el-radio-group
                    v-if="item.value === 'createType'"
                    v-model="chartInfo.createType"
                    disabled
                  >
                    <el-radio label="UPLOAD"> 上传zip包 </el-radio>
                    <el-radio label="WRITE"> 手动编写 </el-radio>
                  </el-radio-group>
                  <span v-else>{{ udfDetailInfo(item.value) }}</span>
                </div>
              </div>
              <!-- 表信息编辑 -->
              <el-form
                v-if="isAddEditPage"
                ref="udfForm"
                :model="chartInfo"
                class="data-form-inline"
                :rules="chartRules"
                label-width="130px"
                :label-position="'right'"
              >
                <el-form-item label="UDF名" prop="udf">
                  <el-input v-model="chartInfo.udf" style="width: 85%" maxlength="20" />
                </el-form-item>
                <el-form-item label="中文名" prop="udfCn">
                  <el-input v-model="chartInfo.udfCn" style="width: 85%" maxlength="20" />
                </el-form-item>
                <el-form-item label="类型" prop="type">
                  <el-select
                    v-model="chartInfo.type"
                    placeholder="请选择类型"
                    class="tab-content__item"
                    :disabled="udfFormItemDisabled"
                    @change="getUdfTemplateCode"
                  >
                    <el-option
                      v-for="item in udfTypeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="UDF主类全域名" prop="domain">
                  <el-input
                    :disabled="udfFormItemDisabled"
                    :value="chartInfo.domain"
                    maxlength="100"
                    class="tab-content__item"
                    @input="handleUdfDomainInput"
                  />
                </el-form-item>
                <el-form-item label="功能说明" prop="businessExplain">
                  <el-input
                    v-model="chartInfo.businessExplain"
                    type="textarea"
                    row="5"
                    class="tab-content__item"
                    placeholder="1.入参描述  2.描述 3.功能说明"
                    maxlength="100"
                  />
                </el-form-item>
                <el-form-item label="创建方式" prop="createType">
                  <el-radio-group
                    v-model="chartInfo.createType"
                    :disabled="createTypeDisabled"
                    @change="showUploadButton"
                  >
                    <el-radio label="UPLOAD"> 上传zip包 </el-radio>
                    <el-radio label="WRITE"> 手动编写 </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-button v-if="uploadVisible" class="upload-file" @click="showUploadDialog">
                  上传文件
                </el-button>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </pro-grid>
      <!-- 源码 -->
      <pro-grid v-show="sourceCodeVisible" class="source-code" type="info" title="源码">
        <!-- 源码头部按钮组（下载、复制、关联类、关联方法\依赖） -->
        <div slot="operation" class="source-code__buttons">
          <!-- 提示 -->
          <el-tooltip
            v-if="isAddEditPage"
            class="source-code__buttons--tooltip"
            effect="light"
            content="关联方法作为静态方法添加在DefaultGlobalFunction类中。用户进行关联后可在UDF源码中以 DefaultGlobalFunction.关联方法名称(参数) 的方式使用。"
            placement="bottom"
          >
            <i class="iconfont icon-wenhao"></i>
          </el-tooltip>
          <!-- <el-button v-if="isViewPage" size="small" type="primary" @click="downloadUdfAsZip">
            下载
          </el-button> -->
          <el-button
            v-if="isViewPage"
            v-clipboard:copy="sourceCode"
            v-clipboard:success="copySuccess"
            v-clipboard:error="copyError"
            size="small"
            type="primary"
          >
            复制
          </el-button>
          <el-button
            v-for="item in sourceCodeButtonList"
            v-show="isAddEditPage"
            :key="item.label"
            :disabled="sourceCodeButtonListDisabled || file"
            size="small"
            type="primary"
            @click="item.event(item.label)"
          >
            {{ item.label }}
          </el-button>
        </div>
        <source-code :key="key" ref="code" :code="sourceCode" :read-only="sourceCodeReadOnly" />
      </pro-grid>
      <!-- tab：引用关系 -->
      <pro-grid
        v-if="referenceRelationVisible"
        type="info"
        :class="{ 'udf-detail__content': !isViewPage }"
      >
        <el-button slot="operation" type="primary" @click="exportRelationList">下载</el-button>
        <reference-relation :id="this.$route.query.id" relation="udf" />
      </pro-grid>
    </pro-grid>
    <!-- 保存弹窗 -->
    <save-confirm ref="saveConfirm" :require="false" @saveConfirmCallback="saveConfirmCallback" />
    <!-- UDF文件上传及模板下载 -->
    <upload
      type="UDF"
      :visible="uploadDialogVisible"
      :title="uploadDialogTitle"
      :res-type="info.resType"
      :download-type="chartInfo.type"
      @close="closeUploadDialog"
    />
    <!-- 关联方法、类/依赖弹窗 -->
    <relation-dialog
      v-show="relationDialogVisible"
      :visible.sync="relationDialogVisible"
      :checked-data="checkedData"
      @getData="getCheckedData"
    />
    <!-- 测试弹窗 -->
    <test-dialog
      v-if="testDialogVisible"
      :visible.sync="testDialogVisible"
      :data="detailSource"
      :sql="sqlCode"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue, Watch, InjectReactive } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import {
  URL_UDF_DOWLOAD,
  URL_UDF_FINDBYID,
  URL_UDF_TEMPLATE_CODE,
  URL_EXPORT_UDF_RELATION_LIST,
  URL_UDF_ASSEMBLE_CODE,
  URL_UDF_ADD_ON_WRITE,
  URL_UDF_ADD_ON_UPLOAD,
  URL_UDF_UPDATE_ON_WRITE,
  URL_UDF_UPDATE_ON_UPLOAD,
  URL_UDF_TESTSQL
} from '@/apis/commonApi';
import { vm } from '@/main';
import { get, post, download } from '@/apis/utils/net';
import VueClipboard from 'vue-clipboard2';
import CommonDelete from '@/utils/mixins/common-delete';
import ReferenceRelation from '@/views/data/components/reference-relation.vue';
import Upload from '../../modals/upload.vue';
import SaveConfirm from '../../modals/save-confirm.vue';
import RollBack from '../version/index.vue';
import CodeMirror from '@/components/codemirror/codemirror.vue';
import SourceCode from '../components/source-code.vue';
import RelationDialog from '../components/relation-dialog.vue';
import TestDialog from '../components/test-dialog.vue';
PaBase.use(VueClipboard);
@Component({
  name: 'ViewAddEdit',
  components: {
    SaveConfirm,
    Upload,
    RollBack,
    CodeMirror,
    SourceCode,
    ReferenceRelation,
    RelationDialog,
    TestDialog
  },
  mixins: [CommonDelete]
})
export default class ViewAddEdit extends PaBase {
  @InjectReactive('isFuseMode') isFuseMode!: boolean;
  private showVersionTab = false;
  // 当前UDF数据
  private detailSource: any = {};
  private udfTypeList: any = [
    { value: 'UDF', label: 'UDF' },
    { value: 'UDAF', label: 'UDAF' },
    { value: 'UDTF', label: 'UDTF' },
    { value: 'ASYNCUDF', label: 'ASYNCUDF' }
  ];
  // 0:新建 1:查看 2:编辑
  private status: any = '0';
  private uploadDialogVisible = false;
  private uploadDialogTitle: any = '';
  private chartInfo: any = {
    domain: '', // 域名
    udf: '',
    udfCn: '',
    businessExplain: '',
    type: '',
    createType: ''
  };
  private id = '';
  private loading = false;
  private value: any = [];
  private file: any = null;
  private sourceCode = `public static String functionName(){
    return "";
}`;
  private info = {
    resType: '', // 服务类型
    service: '',
    checked: false, // 开启数据权限
    orgCodeFiled: '' // 组织机构字段
  };
  private visible = false;
  private fileName = '';
  private chartRules = {
    udf: [{ required: true, validator: this.validateUdfName, trigger: 'blur' }],
    domain: [{ required: true, message: '请填写域名', trigger: 'blur' }],
    type: [{ required: true, message: '请填写类型', trigger: 'change' }],
    createType: [{ required: true, message: '请选择创建方式', trigger: 'blur' }]
  };
  private baseInfoContentKeyList = [
    {
      label: 'UDF名：',
      value: 'udf'
    },
    {
      label: '中文名：',
      value: 'udfCn'
    },
    {
      label: '类型：',
      value: 'type'
    },
    {
      label: '功能说明：',
      value: 'businessExplain'
    },
    {
      label: 'UDF主类全域名：',
      value: 'domain'
    },
    {
      label: '创建人：',
      value: 'createdBy'
    },
    {
      label: '创建电话：',
      value: 'createdByMobile'
    },
    {
      label: '创建方式：',
      value: 'createType'
    }
  ];
  private sourceCodeButtonList = [{ label: '关联方法', event: this.openRelationDialog }];
  private key = Date.now();
  private relationDialogVisible = false;
  private testDialogVisible = false;
  private relationDialogTitle = '';
  private uploadButtonVisible = false;
  private activeTab = 'info';
  private tabPaneList = [
    { label: '基本信息', name: 'info' },
    { label: '历史版本', name: 'history' },
    { label: '引用关系', name: 'relation' }
  ];
  private tempSave = false;
  private relationParams: any = {};
  private checkedData = {};
  private preFile = null;
  private sqlCode = '';
  // 源码框只读
  get sourceCodeReadOnly() {
    return (this.isViewPage || this.file ? true : false) || this.chartInfo.createType === 'UPLOAD';
  }

  get sourceCodeButtonListDisabled() {
    if (!this.chartInfo.type) return true;
    if ((['UPLOAD', ''].includes(this.chartInfo.createType) && this.status === '0') || this.file)
      return true;
    return false;
  }

  // 上传文件按钮显隐
  get uploadVisible() {
    return (
      this.uploadButtonVisible || (this.chartInfo.createType === 'UPLOAD' && this.status === '2')
    );
  }

  // 创建方式禁用
  get createTypeDisabled() {
    return this.status !== '0';
  }

  // 新建/编辑状态下展示tab
  get showTabs() {
    return !['0', '2'].includes(this.status);
  }

  get referenceRelationVisible() {
    return this.activeTab === 'relation';
  }

  get detailVisible() {
    return this.activeTab === 'info' || this.status === '2';
  }

  get sourceCodeVisible() {
    return (
      ['info', ''].includes(this.activeTab) &&
      (this.chartInfo.createType === 'WRITE' ||
        (this.status === '1' && this.chartInfo.createType === 'UPLOAD'))
    );
  }

  get isViewPage() {
    return this.status === '1';
  }

  get udfDetailHeaderButtonList() {
    // （新建、编辑页面）显示暂存、提交按钮
    const showViewButtons = this.isAddEditPage && !this.showVersionTab;
    const showTempSaveButton =
      this.status === '0' || (showViewButtons && ['WRITE', ''].includes(this.chartInfo.createType));
    // （查看页面）显示、编辑按钮
    const showOperateButtons = this.isViewPage && this.activeTab === 'info';
    // （查看页面）显示下载按钮
    const showDownloadButton =
      (this.isViewPage && this.activeTab === 'info') ||
      (this.status === '2' && this.chartInfo.createType === 'UPLOAD');
    const showRelationButton = this.isViewPage && this.activeTab === 'relation';
    return [
      {
        label: '下载',
        display: showDownloadButton,
        event: 'downloadUdfAsZip',
        tempSave: false
      },
      {
        label: '测试',
        display: this.isAddEditPage,
        event: 'openTestDialog',
        tempSave: false
      },
      {
        label: '暂存',
        display: showTempSaveButton,
        event: 'submit',
        tempSave: true
      },
      {
        label: '提交',
        display: showViewButtons,
        event: 'submit',
        tempSave: false
      },
      {
        label: '编辑',
        display: showOperateButtons,
        event: 'switchToEdit',
        tempSave: false
      },
      {
        label: '下载引用关系',
        display: showRelationButton,
        event: 'exportRelationList',
        tempSave: false
      }
    ];
  }

  get udfPageTitle() {
    const statusMap = {
      '0': '新建UDF',
      '1': '查看UDF',
      '2': '编辑UDF'
    };
    return statusMap[this.status];
  }

  // 当前页面为新建/编辑页
  get isAddEditPage() {
    return ['0', '2'].includes(this.status) ? true : false;
  }

  get sourceCodeIsEmpty() {
    if ((this.$refs.code as any).sourceCode.trim() === '') return true;
    return false;
  }

  // UDF类型选择禁用
  get udfFormItemDisabled() {
    return this.status === '2' && this.chartInfo.createType === 'UPLOAD';
  }

  @Watch('status')
  handleIsViewPageChange(val) {
    if (val === '2') {
      const header: any = document.querySelector('.bs-pro-page__header');
      header.style.display = 'flex';
    }
  }

  created() {
    if (this.$route.query.id) {
      this.id = this.$route.query.id as string;
      this.status = this.$route.query.status;
    }
    this.getUdfDetail();
  }

  mounted() {
    const header: any = document.querySelector('.bs-pro-page__header');
    const layout: any = document.querySelector('.bs-layout');
    if (this.isViewPage) {
      header.style.display = 'none';
      layout.style.overflow = 'hidden';
    }
  }

  // UDF名非空、驼峰校验
  validateUdfName(rule, value, callback) {
    const re = /^[a-zA-Z]{1}\w*$/;
    if (value === '') {
      callback(new Error('请填写UDF名'));
    } else if (value !== '' && !re.test(value)) {
      callback(new Error('UDF名称需要以字母开头且只包含字母、数字与下划线'));
    } else {
      callback();
    }
  }

  handleUdfDomainInput(e) {
    this.chartInfo.domain = e.replace(/[\u4E00-\u9FA5]/g, '');
  }

  // 导出引用关系
  async exportRelationList() {
    try {
      await download(`${URL_EXPORT_UDF_RELATION_LIST}?id=${this.$route.query.id}`);
    } catch (error) {
      this.$message.error(error);
    }
  }

  private getUdfParams(isEdit = false, memo = '', tempSave = false) {
    /**
     * createType: UDF创建方式
     * file: 上传java文件,
     * id：唯一标识
     * memo：更新备注,
     * udfExplain：功能说明
     * udfName：UDF名称
     * udfNameCn：UDF中文名
     * udfNameMain：全域名
     * udfType：UDF类型
     */
    const [
      {
        businessExplain: udfExplain,
        udf: udfName,
        udfCn: udfNameCn,
        domain: udfNameMain,
        type: udfType,
        createType
      },
      { id },
      { otherFuncs, imports, jars }
    ] = [this.chartInfo, this, this.relationParams];
    const [params, isWrite]: any = [
      {
        udfExplain,
        udfName,
        udfNameCn,
        udfNameMain,
        udfType,
        createType,
        tempSave,
        otherFuncs,
        imports,
        jars
      },
      createType === 'WRITE'
    ];
    if (isWrite || (!isWrite && !this.file)) {
      params.sourceCode = (this.$refs as any).code.sourceCode;
    }
    if (isEdit) {
      params.id = id;
      params.memo = memo;
    }
    return params;
  }

  handleTabClick({ label }) {
    if (label) {
      this.showVersionTab = label === '历史版本' ? true : false;
    }
    this.$forceUpdate();
  }

  udfDetailInfo(value) {
    return this.chartInfo.hasOwnProperty(value) ? this.chartInfo[value] : this.detailSource[value];
  }

  showUploadButton(label) {
    this.uploadButtonVisible = label === 'UPLOAD' ? true : false;
    if (label === 'WRITE' && this.chartInfo.type) {
      this.getUdfTemplateCode();
    }
  }

  // 根据选择的UDF类型展示不同的源码信息
  async getUdfTemplateCode() {
    this.loading = true;
    try {
      const { data, success, msg } = await get(URL_UDF_TEMPLATE_CODE, {
        udfType: this.chartInfo.type
      });
      if (success) {
        this.sourceCode = data;
        this.loading = false;
        return;
      }
      this.$message.error(msg);
    } catch {
      this.loading = false;
    }
  }

  showUploadDialog() {
    this.uploadDialogVisible = true;
    this.uploadDialogTitle = '上传zip包';
  }

  validateDomin(rule, value, callback) {
    const re = /[\u4E00-\u9FA5]/g;
    if (value === '') {
      callback(new Error('请填写域名'));
    } else if (value !== '' && re.test(value)) {
      callback(new Error('请输入英文'));
    } else {
      callback();
    }
  }

  /**
   * 获取弹窗中选中的数据
   * otherFuncs：关联方法
   * imports：第三方类库/依赖
   * preImports：上次选中的第三方类库/依赖
   */
  async getCheckedData({ otherFuncs, imports, preImports }) {
    //
    try {
      this.loading = true;
      const { success, msg, data } = await post(URL_UDF_ASSEMBLE_CODE, {
        createType: this.chartInfo.createType,
        id: this.id,
        otherFuncs: JSON.stringify(otherFuncs),
        sourceCode: (this.$refs as any).code.sourceCode,
        tempSave: this.tempSave,
        udfExplain: this.chartInfo.businessExplain,
        udfName: this.chartInfo.udf,
        udfNameCn: this.chartInfo.udfCn,
        udfNameMain: this.chartInfo.domain,
        udfType: this.chartInfo.type,
        memo: '',
        imports: JSON.stringify(imports),
        preImports: JSON.stringify(preImports)
      });
      if (success) {
        // 保存组装源码的返回值，作为提交时的传参
        this.relationParams = {
          otherFuncs: data.otherFuncs || '',
          imports: data.imports || '',
          jars: data.jars || ''
        };
        this.sourceCode = data.sourceCode;
        this.key = Date.now();
      } else {
        this.$message.error(msg);
      }
      this.loading = false;
    } catch {
      this.loading = false;
    }
  }

  // 下载当前UDF
  async downloadUdfAsZip() {
    this.loading = true;
    const res: any = await this.doGet(URL_UDF_DOWLOAD + '?id=' + this.id, {
      responseType: 'blob'
    });
    if (res.blob) {
      const { blob, fileName } = res;
      const url = window.URL.createObjectURL(new Blob([blob]));
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      this.loading = false;
      return;
    } else if (!res.success) {
      this.$message.error(res.msg);
      this.loading = false;
    }
  }

  closeUploadDialog(data, needFresh) {
    if (needFresh && data.size) {
      this.fileName = '';
      this.file = data;
      this.fileName = data.name;
    }
    this.uploadDialogVisible = false;
  }

  // 提交修改信息
  submit() {
    if (this.chartInfo.type === 'WRITE' && this.sourceCodeIsEmpty) {
      this.$message.error('源码不能为空');
      return;
    }
    if (this.chartInfo.createType == 'UPLOAD' && !this.file && !this.id) {
      this.$message.error('请上传java文件');
      return;
    }
    const udfForm: any = this.$refs['udfForm'];
    udfForm.validate(async (valid) => {
      if (valid) {
        const isWrite = this.chartInfo.createType === 'WRITE';
        // 编辑
        if (this.id) {
          const saveConfirmRef: any = this.$refs.saveConfirm;
          saveConfirmRef.visible = true;
        } else {
          this.loading = true;
          const submitData = new FormData();
          if (!isWrite) {
            Vue.axios.defaults.timeout = 1500000;
            Vue.axios.defaults.headers.post['Content-Type'] = 'multipart/form-data';
            submitData.append('file', this.file);
          }
          try {
            const api = isWrite
              ? URL_UDF_ADD_ON_WRITE
              : `${URL_UDF_ADD_ON_UPLOAD}/?tempSave=${this.tempSave}&createType=${
                  this.chartInfo.createType
                }&udfExplain=${encodeURIComponent(this.chartInfo.businessExplain)}&udfName=${
                  this.chartInfo.udf
                }&udfNameCn=${encodeURIComponent(this.chartInfo.udfCn)}&udfNameMain=${
                  this.chartInfo.domain
                }&udfType=${this.chartInfo.type}`;
            const { data, success, msg } = await post(
              api,
              isWrite ? this.getUdfParams(false, '', this.tempSave) : submitData
            );
            if (success) {
              const msg = this.tempSave ? '暂存成功' : '新建成功';
              this.$message.success(msg);
              const oidFullPath = this.$route.fullPath;
              const tabsNavInstance = vm.$children[0].$refs['tabsNav'];
              tabsNavInstance.handleTabsDelete(oidFullPath);
              this.$router.push({
                path: `/data/udfEdit/`,
                query: {
                  id: data.id,
                  title: `UDF：${data.udfName}`,
                  status: '2'
                }
              });
            } else {
              this.$message.error(msg);
            }
            this.loading = false;
          } catch {
            this.loading = false;
          }
        }
      }
    });
  }

  // 编辑状态保存处理
  async saveConfirmCallback(data) {
    if (this.chartInfo.type === 'WRITE' && this.sourceCodeIsEmpty) {
      this.$message.error('源码不能为空');
      return;
    }
    if (data === 0) return;
    const isWrite = this.chartInfo.createType === 'WRITE';
    const saveConfirmRef: any = this.$refs.saveConfirm;
    saveConfirmRef.visible = false;
    this.loading = true;
    Vue.axios.defaults.timeout = 1500000;
    Vue.axios.defaults.headers.post['Content-Type'] = 'multipart/form-data';
    const submitData = new FormData();
    if (this.file || this.preFile) {
      submitData.append('file', this.file || this.preFile);
    }
    const api = isWrite
      ? URL_UDF_UPDATE_ON_WRITE
      : `${URL_UDF_UPDATE_ON_UPLOAD}/?createType=${this.chartInfo.createType}&id=${this.id}&tempSave=${this.tempSave}&udfExplain=${this.chartInfo.businessExplain}&udfName=${this.chartInfo.udf}&udfNameCn=${this.chartInfo.udfCn}&udfNameMain=${this.chartInfo.domain}&udfType=${this.chartInfo.type}`;
    // if (!isWrite && !this.file) api = URL_UDF_UPDATE_ON_WRITE;
    const res: any = await this.doPost(
      api,
      isWrite ? this.getUdfParams(true, data.comments, this.tempSave) : submitData
    );
    this.parseResponse(res);
    if (res.success) {
      await this.getUdfDetail();
    }
    this.loading = false;
    this.preFile = this.file;
    this.file = null;
  }

  // 版本回滚
  versionChange(e) {
    if (e) {
      this.getUdfDetail();
    }
  }

  async getUdfDetail() {
    this.$forceUpdate();
    // this.status = '0';
    // 编辑赋值
    if (this.id) {
      this.loading = true;
      const api = URL_UDF_FINDBYID + '?id=' + this.id;
      let data: any = {};
      await this.doGet(api).then((resp: any) => {
        this.parseResponse(resp, () => {
          data = resp!.data;
        });
      });
      this.detailSource = data;
      this.chartInfo.udf = data.udfName;
      this.chartInfo.udfCn = data.udfNameCn;
      this.chartInfo.domain = data.udfNameMain;
      this.chartInfo.type = data.udfType;
      this.chartInfo.businessExplain = data.udfExplain;
      this.chartInfo.createType = data.createType;
      this.sourceCode = data.sourceCode;
      // 获取选中的关联方法、关联类/依赖
      this.checkedData = {
        // 第三方类库
        imports: data.imports ? JSON.parse(data.imports) : [],
        // 依赖
        jars: data.jars ? JSON.parse(data.jars) : [],
        // 关联方法
        otherFuncs: data.otherFuncs ? JSON.parse(data.otherFuncs) : []
      };
      this.relationParams = {
        imports: data.imports ? JSON.parse(data.imports) : [],
        jars: data.jars ? JSON.parse(data.jars) : [],
        otherFuncs: data.otherFuncs ? JSON.parse(data.otherFuncs) : []
      };
      this.loading = false;
    }
  }

  openTestDialog() {
    const udfForm: any = this.$refs['udfForm'];
    udfForm.validate(async (valid) => {
      if (valid) {
        this.detailSource.udfType = this.chartInfo.type;
        if (
          (this.status === '0' && this.chartInfo.createType === 'UPLOAD') ||
          (this.file && this.status === '2' && this.chartInfo.createType === 'UPLOAD') ||
          (this.status === '2' &&
            this.chartInfo.createType === 'UPLOAD' &&
            this.detailSource.udfState === 'DEV')
        ) {
          this.$message.error('上传zip包的UDF需要提交后才能测试!');
          return;
        }
        this.getUdfTestSql();
      }
    });
  }

  async getUdfTestSql() {
    this.loading = true;
    try {
      const { data, success, msg } = await get(
        `${URL_UDF_TESTSQL}?id=${this.detailSource.id}&type=${this.chartInfo.type}`
      );
      if (success) {
        this.loading = false;
        this.sqlCode = data;
        this.testDialogVisible = true;
        this.setDetailSourceData();
      } else {
        this.$message.error(msg);
        this.loading = false;
        return;
      }
    } catch (err) {
      this.loading = false;
      this.testDialogVisible = false;
      return;
    }
  }

  setDetailSourceData() {
    this.detailSource.sourceCode = '';
    this.detailSource.sourceCode = (this.$refs.code as any).sourceCode || this.chartInfo.sourceCode;
    this.detailSource.createType = this.chartInfo.createType;
    this.detailSource.tempSave = this.tempSave;
    this.detailSource.udfExplain = this.chartInfo.businessExplain;
    this.detailSource.udfName = this.chartInfo.udf;
    this.detailSource.udfNameCn = this.chartInfo.udfCn;
    this.detailSource.udfNameMain = this.chartInfo.domain;
    this.detailSource.udfType = this.chartInfo.type;
    this.detailSource.otherFuncs = this.relationParams.otherFuncs || this.detailSource.otherFuncs;
  }

  openRelationDialog(title) {
    this.relationDialogTitle = title;
    this.relationDialogVisible = true;
  }

  // 头部操作按钮触发
  operateHandler(event, tempSave) {
    this.tempSave = tempSave;
    this[event]();
  }
  // 切换至编辑状态
  switchToEdit() {
    this.$router.push({
      path: '/data/udfEdit',
      query: { id: this.$route.query.id, status: '2', title: `UDF：${this.chartInfo.udf}` }
    });
  }

  copySuccess() {
    this.$message.success('复制成功!');
  }

  copyError() {
    this.$message.error('复制失败!');
  }
}
</script>

<style scoped lang="scss">
.is-view {
  height: calc(100vh - 30px);
}
.source-code__buttons {
  &--tooltip {
    margin-right: 10px;
  }
}
.el-tabs {
  background: #fff;
  border-bottom: 1px solid #e2e2e2;
  ::v-deep &__header {
    line-height: 64px;
    margin-left: 20px;
  }
}
.udf-detail {
  &__header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  &__content {
    margin-top: 20px;
  }
  &__tab {
    display: flex;
    justify-content: space-between;
    padding-right: 25px;
    align-items: center;
    height: 64px;
  }
}
.tab-content {
  padding: 20px 25px;
  &__col {
    display: flex;
  }
  &__item {
    width: 85%;
  }
  .upload-file {
    margin-left: 125px;
  }
  .chartInfoDetail {
    .item {
      margin-bottom: 30px;
    }
    .top {
      div {
        width: 261px;
        margin-right: 10px;
      }
    }
  }
}
</style>
