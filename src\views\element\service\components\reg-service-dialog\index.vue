<template>
  <bs-dialog size="medium" :title="title" :visible.sync="display">
    <div v-loading="loading">
      <el-alert v-if="showDependText" type="success" style="margin-bottom: 20px" :title="$t('pa.tip.related')" />
      <pro-form
        v-if="!loading"
        ref="proFormRef"
        :value="formData"
        show-hidden-error
        :form-items="formItems"
        scroll-box=".el-dialog__body"
        :options="{ labelWidth: isEn ? 170 : 110, className: isHost ? 'host-form' : '' }"
        @change="handleChange"
        @item-change="handleItemChange"
      >
        <flink-upload slot="flinkJarFile" :options="getFormItem('flinkJarFile')" :form-data="formData" />
        <flink-upload slot="log4jFile" :options="getFormItem('log4jFile')" :form-data="formData" />
      </pro-form>
    </div>
    <!-- footer -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="display = false">{{ $t('pa.action.close') }}</el-button>
      <el-button type="primary" :loading="saveLoading" @click="handleSubmit">{{ $t('pa.action.makeSure') }}</el-button>
      <el-button type="primary" @click="testConnect">{{ $t('pa.action.testConnect') }}</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, PropSync, Prop, Vue, Ref, Watch } from 'vue-property-decorator';
import { addRes, getServerConfig, getResDetail, getResRelation, testResConnect, updateRes } from '@/apis/serviceApi';
import { convertRules, convertVisible, setEditable } from './utils';
import { safeArray } from '@/utils';
import cloneDeep from 'lodash/cloneDeep';
import FlinkUpload from './flink-upload.vue';
import ClearEnter from '@/common/clear-enter';
import { BsProForm } from 'bs-ui-pro';

@Component({
  mixins: [ClearEnter],
  components: { FlinkUpload }
})
export default class RegServiceDialog extends Vue {
  @PropSync('show', { required: true, default: false }) display!: boolean;
  @Prop({ default: '' }) type!: string;
  @Prop({ default: '' }) id!: string;
  @Ref('proFormRef') readonly proForm!: BsProForm;

  loading = true;
  saveLoading = false;
  formData: Record<string, any> = {};
  formItems: any[] = [];
  // 详情接口返回的原始数据
  sourceData = { resType: this.type };
  // 显示依赖关系提示文案
  showDependText = false;
  flinkConfTemplate = '';
  flinkConfDefaultValue = '';

  get title() {
    return this.id ? this.$t('pa.action.edit') : this.$t('pa.action.register');
  }
  get isHost() {
    return this.type === 'HOST';
  }
  get isFlink() {
    return this.type === 'FLINK';
  }

  @Watch('formData', { deep: true })
  handleFormConfigChange(data) {
    // 特殊处理：服务类型-主机-验证类型为秘钥时，密码项非必填
    if (data.authType && this.isHost) {
      const index = this.formItems.findIndex((el) => el.type === 'password');
      index !== -1 && this.$set(this.formItems[index].rules[0], 'required', data.authType === 'password');
    }
  }
  async created() {
    try {
      this.loading = true;
      await this.setFormItems();
      // 编辑状态获取依赖关系和服务详情
      if (this.id) {
        this.getDepends();
        await this.getDetail();
      }
    } finally {
      this.loading = false;
    }
  }
  // 获取依赖关系
  async getDepends() {
    const { data = 0 } = await getResRelation(this.id);
    this.showDependText = data > 0;
  }
  // 获取详情
  async getDetail() {
    const { data } = await getResDetail(this.id);
    const resProperty = JSON.parse(data.resProperty || '{}');
    this.formItems.forEach(({ type, prop }: any) => {
      if (type === 'password' || prop === 'priKey') {
        this.formData[prop] = this.$store.getters.decrypt(resProperty[prop] || '');
      } else {
        this.formData[prop] = resProperty[prop];
      }
    });
    this.sourceData = data;
  }
  // 获取表单配置
  async setFormItems() {
    // 根据自定义jar来过滤部分字段的显示隐藏
    const filterByUdjStatus = (items) => {
      const needCheckFields = ['flinkJarFile', 'log4jFile'];
      if (!(this.type === 'FLINK' && this.$store.getters.enableJar)) {
        return items.filter((item) => !needCheckFields.includes(item.prop));
      }
      return items;
    };
    const { data } = await getServerConfig(this.type, 'reg');
    let formItems = JSON.parse(data || '{}').forms;
    if (!Array.isArray(formItems)) return;
    formItems = filterByUdjStatus(formItems);
    formItems.forEach((item: any) => {
      // 将visible转化成函数
      convertVisible(item);
      convertRules(item);
      // 编辑状态下处理不能被编辑的表单项
      this.id && setEditable(item);
      // 处理默认值
      if (item.defaultVal || typeof item.defaultVal === 'boolean') {
        this.formData[item.prop] = item.defaultVal;
      }
      if (item.prop === 'priKey' && this.isHost) item.className = 'pri-key';
      /* flinkConf  */
      if (this.isFlink && item.prop === 'flinkConf') {
        const { template, code, message } = item?.extend || {};
        this.flinkConfTemplate = template || '';
        this.flinkConfDefaultValue = item.defaultVal || '';
        if (!Array.isArray(item.rules)) item.rules = [];
        if (code && message) {
          item.rules.push({
            validator: (rule, value, callback) => {
              const flag = value
                .split(/\r?\n/g)
                .reduce((pre, next) => {
                  const tmp = next.trim().startsWith('#');
                  if (!tmp && next) {
                    pre.push(next);
                  }
                  return pre;
                }, [])
                .some((it) => it.includes(code));
              return flag ? callback(new Error(message)) : callback();
            },
            trigger: 'blur'
          });
        }
      }
    });
    this.formItems = formItems;
  }
  // 获取单个表单配置
  getFormItem(prop) {
    return this.formItems.find((item: any) => item.prop === prop);
  }
  // 处理表单数据更新
  handleChange(data) {
    this.formData = data;
  }
  handleItemChange(prop: string, value: string) {
    if (!this.isFlink || prop !== 'clusterType') return;
    const flinkConf = value.toLowerCase().includes('yarn_') ? this.flinkConfTemplate : this.flinkConfDefaultValue;
    (this.proForm as any).updateModel('flinkConf', flinkConf);
  }
  /* 保存 */
  async handleSubmit() {
    try {
      await (this.proForm as any).validate();
      this.saveLoading = true;
      const params = this.getParams(true);
      const { success, msg, error } = await (this.id ? updateRes(params) : addRes(params));
      if (!success) return this.$message.error(error);
      this.$message.success(msg);
      this.$emit('confirm');
      this.display = false;
    } finally {
      this.saveLoading = false;
    }
  }
  /* 获取参数 */
  getParams(toBlob = false) {
    const data: any = cloneDeep(this.sourceData);
    const fileData: any = {};
    // 处理需要加密的字段
    const checkEncrypt = (res, { type, prop }) => {
      if (type === 'password' || prop === 'priKey') {
        res[prop] = this.$store.getters.encrypt(res[prop]);
      }
    };
    // 处理字段为文件流的字段
    const checkFile = (res, { prop }) => {
      if (Object.prototype.toString.call(res[prop]) === '[object File]') {
        fileData[prop] = res[prop];
        delete res[prop];
      }
    };
    // 因为部分服务需要上传文件 所以需要将参数转化成blob 测试连接不需要处理
    const checkParams = (data) => {
      if (toBlob) {
        return {
          [this.id ? 'delta' : 'bean']: new Blob([JSON.stringify(data)], {
            type: 'application/json'
          }),
          ...fileData
        };
      } else {
        return data;
      }
    };
    const resProperty: Record<string, any> = this.formItems.reduce((res: any, { type, prop, visible }) => {
      let isVisible = true;
      if (typeof visible === 'function') {
        try {
          isVisible = visible(this.formData);
        } catch {
          isVisible = false;
        }
      } else {
        isVisible = true;
      }
      if (!isVisible) return res;
      if (this.formData[prop] === undefined) return res;
      res[prop] = this.formData[prop];
      checkEncrypt(res, { type, prop });
      checkFile(res, { prop });
      return res;
    }, {});
    // TODO: 兼容原数据模型 避免接口报错 后续接口优化后去掉
    resProperty.resType = this.type;
    resProperty.belongType = 'REG';
    data.belongType = 'REG';
    data.title = this.formData.title;
    data.url = this.formData.url;
    // 更新外层数据
    data.resProperty = JSON.stringify(resProperty);
    return checkParams(data);
  }
  /* 测试连接 */
  async testConnect() {
    await (this.proForm as any).validate();
    this.$message.info(this.$t('pa.tip.startConnectTest'));
    const { success, data, error } = await testResConnect(this.getParams());
    if (!success) return this.$message.error(error);
    if (data?.success > 0) {
      this.$message.success(this.$t('pa.tip.connectSuccess'));
    } else {
      this.$message.error(this.$t('pa.tip.connectError', [safeArray(data?.failUrls).join(', ')]));
    }
  }
}
</script>
<style lang="scss" scoped>
.el-dialog__body > div {
  min-height: 200px;
}
.host-form {
  ::v-deep [placeholder='请输入秘钥'] {
    -webkit-text-security: disc !important;
  }
}
</style>
