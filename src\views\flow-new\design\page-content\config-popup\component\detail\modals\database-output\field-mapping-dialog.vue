<template>
  <bs-dialog
    width="800px"
    :title="title"
    append-to-body
    class="mapping-dialog"
    :visible.sync="display"
  >
    <!-- header -->
    <div class="mapping-header">
      <!-- title -->
      <span class="mapping-header__title">字段映射</span>
      <!-- search -->
      <bs-search v-model="keyword" placeholder="请输入" @search="handleSearch" />
    </div>
    <bs-table
      border
      stripe
      :height="370"
      paging-front
      row-key="id"
      size="mini"
      align="center"
      :data="tableData"
      :page-data="pageData"
      :selection="!disabled"
      :crossing="!disabled"
      :column-settings="false"
      :column-data="columnData"
      :checked-rows="checkedData"
      :show-multiple-selection="false"
      cell-class-name="mapping-center"
      header-cell-class-name="mapping-center"
      @page-change="handlePageChange"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择全部 -->
      <template slot="footer-expand">
        <el-checkbox v-if="!disabled" v-model="checkAll" @change="handleCheckedAll">
          选择全部
        </el-checkbox>
        <span class="mapping-dialog__total">（已选择{{ currentSelectionData.length }}项）</span>
      </template>
      <!-- key -->
      <template slot="id" slot-scope="{ row }">
        <div class="mapping-key">
          <el-tooltip v-hide effect="light" placement="top" :content="row.id">
            <span class="mapping-key__id">{{ row.id }}</span>
          </el-tooltip>
          <el-tag v-if="row.isPrimarykey" size="mini">主键</el-tag>
        </div>
      </template>
      <!-- value -->
      <template slot="value" slot-scope="{ row }">
        <el-select
          v-model="row.value"
          clearable
          filterable
          size="small"
          :disabled="disabled"
          placeholder="请选择输入字段"
        >
          <el-option
            v-for="el in inputFields"
            :key="el.value"
            :value="el.value"
            :label="el.label"
          />
        </el-select>
      </template>
    </bs-table>
    <div slot="footer">
      <el-button @click="display = false">{{ disabled ? '关闭' : '取消' }}</el-button>
      <el-button v-if="!disabled" type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue, Watch } from 'vue-property-decorator';
import type { TableItem } from './type';
import { getValue, hide, includesPro } from './utils';
import { uniqBy } from 'lodash';

@Component({ directives: { hide } })
export default class FieldMappingDialog extends Vue {
  @Prop() tableName!: string;
  @Prop({ default: '' }) outputColumns!: string;
  @Prop({ default: () => [] }) outputFields!: string[];
  @Prop({ default: () => [] }) inputFields!: any[];
  @Prop({ default: () => [] }) tableFields!: any[];
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @PropSync('show', { type: Boolean, default: false }) display!: boolean;

  private title = '字段映射配置';
  private keyword = '';
  private pageData = {
    pageSize: 10,
    currentPage: 1,
    total: 0,
    layout: 'total, prev, pager, next'
  };
  private checkAll = false;
  private tableData: any[] = [];
  private checkedData: any[] = [];
  private currentSelectionData: TableItem[] = [];
  private isRendered = true;

  get value() {
    const temp = this.outputColumns.split(',').filter(Boolean);
    return temp.reduce((pre: any, next: string, index: number) => {
      pre[next] = this.outputFields[index];
      return pre;
    }, {});
  }
  get rawTableData() {
    if (!this.disabled) {
      return this.tableFields.map(({ id, isPrimarykey }) => {
        return {
          id,
          isPrimarykey,
          value: getValue(id, this.inputFields, this.value)
        };
      });
    }
    return Object.entries(this.value).map(([id, value]) => {
      let isPrimarykey = false;
      const item = this.tableFields.find((el) => el.id == id);
      'isPrimarykey' in item && (isPrimarykey = item.isPrimarykey);
      return { id, value, isPrimarykey };
    });
  }

  get columnData() {
    return [
      {
        label: this.tableName,
        value: 'id',
        width: '296px',
        showOverflowTooltip: false
      },
      {
        label: '输入字段',
        value: 'value',
        width: '296px',
        showOverflowTooltip: false
      }
    ];
  }

  @Watch('tableData', { deep: true })
  handleCurrentSelectionDataChange(data) {
    this.isRendered && this.$set(this, 'checkedData', this.currentSelectionData);
    !this.isRendered && (this.isRendered = true);
    this.checkAll = !data.length
      ? false
      : this.currentSelectionData.filter((el) => this.tableData.map((el) => el.id).includes(el.id))
          .length === this.tableData.length;
  }

  created() {
    this.handleSearch('', !this.disabled);
  }

  /* 处理搜索事件 */
  handleSearch(keyword: string, isFirst = false) {
    this.tableData = !keyword
      ? this.rawTableData
      : (this.rawTableData as any).filter(({ id }) => includesPro(id, keyword));
    this.pageData = { ...this.pageData, currentPage: 1, total: this.tableData.length };
    if (isFirst) {
      this.$set(
        this,
        'checkedData',
        (this.checkedData = Object.keys(this.value)
          .map((key) => this.tableData.find((el) => String(el.id) === key))
          .filter(Boolean))
      );
      this.isRendered = false;
    }
  }
  handlePageChange(currentPage) {
    this.pageData.currentPage = currentPage;
  }
  /* 处理表格选中事件 */
  handleSelectionChange(data: TableItem[]) {
    const tableDataIds = this.tableData.map((el) => el.id);
    const preSelections = this.currentSelectionData.filter((el) => !tableDataIds.includes(el.id));
    if (!data.length && preSelections.length) {
      this.$set(this, 'currentSelectionData', preSelections);
    } else if (data.length && preSelections.length) {
      this.$set(this, 'currentSelectionData', uniqBy([...data, ...preSelections], 'id'));
    } else {
      this.$set(this, 'currentSelectionData', data);
    }
    if (!this.keyword) {
      this.checkAll = this.currentSelectionData.length === this.tableData.length;
    } else {
      this.checkAll =
        this.currentSelectionData.filter((el) => this.tableData.map((el) => el.id).includes(el.id))
          .length === this.tableData.length;
    }
  }
  /* 处理表格全选事件 */
  handleCheckedAll(isChecked) {
    this.checkedData = isChecked ? this.tableData : [];
    this.handleSelectionChange(this.checkedData);
  }
  /* 处理确认事件 */
  async handleConfirm() {
    await this.checkFieids();
    const [fields, columns] = this.currentSelectionData.reduce(
      (pre: any, { id, value }) => {
        pre[0].push(value);
        pre[1].push(id);
        return pre;
      },
      [[], []]
    );
    this.$emit('change', ...[fields, columns.join(',')]);
    this.display = false;
  }
  /* 字段映射校验 */
  checkFieids() {
    return new Promise((reslove: any, reject: any) => {
      if (this.currentSelectionData.length < 1) {
        this.$tip.error('至少选择一条数据');
        return reject(false);
      }
      const target = this.currentSelectionData.find((el) => !el.value);
      if (target) {
        this.$tip.error(`请选择字段【${target.id}】对应的输入字段`);
        return reject(false);
      }
      reslove(true);
    });
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-select {
  width: 100%;
}
::v-deep .mapping-center {
  text-align: center;
}
::v-deep .bs-table-footer-slot {
  padding-left: 0;
  text-align: left;
}

.mapping {
  &-dialog {
    ::v-deep .el-dialog__body {
      padding-bottom: 0;
      min-height: 490px !important;
    }
    &__total {
      font-weight: bolder;
      letter-spacing: 1px;
    }
  }
  &-header {
    display: flex;
    justify-content: space-between;
    align-content: center;
    margin-bottom: 16px;
    height: 32px;
    &__title {
      font-weight: bold;
      font-size: 14px;
    }
  }

  &-key {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    &__id {
      display: inline-block;
      width: calc(100% - 45px);
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}
</style>
