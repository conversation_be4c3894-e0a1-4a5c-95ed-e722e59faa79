import { get, post } from './utils/net';

export const login = (data) => {
  return post('/j_spring_security_check', data);
};

export const getUserInfo = () => {
  return get('/rs/pa/portal/session/user');
};

export const user = (data) => {
  return get('/rs/pa/portal/getUserInfo', data);
};
export const getPortalConfig = () => {
  return get('/rs/generalfunction/get');
};
export default {
  login,
  user,
  getUserInfo,
  getPortalConfig
};
