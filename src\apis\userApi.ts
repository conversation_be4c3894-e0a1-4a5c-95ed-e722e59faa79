import { get, post } from './utils/net';

export const login = (data) => {
  return post('/j_spring_security_check', data, { baseURL: '/portal' });
};

export const user = () => {
  return get('/rs/profile/session/user', null, { baseURL: '/portal' });
};

export const getUserInfo = (data) => {
  return get('/rs/pa/portal/getUserInfo', data, { baseURL: '/portal' });
};

export default {
  login,
  user,
  getUserInfo
};
