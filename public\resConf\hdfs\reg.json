{"forms": [{"type": "input", "prop": "title", "label": "名称", "componentProps": {"maxlength": 30, "placeholder": "请输入名称", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入名称", "trigger": "blur"}, {"min": 2, "max": 30, "message": "长度在 2 到 30 个字符", "trigger": "blur"}]}, {"type": "input", "prop": "shortCode", "label": "nameService", "componentProps": {"maxlength": 30, "showWordLimit": true, "placeholder": "HDFS命名服务的名称,请输入服务简码(仅限6-12位小写字母)"}, "rules": [{"required": true, "message": "请输入标识符", "trigger": "blur"}, {"pattern": "/^[a-z]{6,12}$/", "message": "标识符仅限6-12位小写字母", "trigger": "blur"}]}, {"type": "textarea", "prop": "url", "label": "服务地址", "componentProps": {"rows": 3, "maxlength": 1024, "placeholder": "请输入服务地址，多个地址以逗号分隔，兼容Ipv4和Ipv6"}, "rules": [{"required": true, "message": "请输入服务地址，多个地址以逗号分隔，兼容Ipv4和Ipv6", "trigger": "blur"}]}, {"type": "input", "prop": "defaultFS", "label": "fs.defaultFS", "componentProps": {"maxlength": 255, "showWordLimit": true, "placeholder": "请输入defaultFS, 通常可以在core-site文件中通过fs.defaultFS配置获取"}}, {"type": "textarea", "prop": "coreSite", "label": "coreSite", "componentProps": {"rows": 5, "placeholder": "若此HDFS与FLINK属于不同集群,为保证HDFS高可用,请输入文件core-site.xml内容"}, "rules": [{"message": "请输入文件core-site.xml内容", "required": true, "trigger": "blur"}]}, {"type": "textarea", "prop": "hdfsSite", "label": "hdfsSite", "componentProps": {"rows": 5, "placeholder": "若此HDFS与FLINK属于不同集群,为保证HDFS高可用,请输入文件hdfs-site.xml内容"}, "rules": [{"message": "请输入文件hdfs-site.xml内容", "required": true, "trigger": "blur"}]}, {"type": "radio-group", "prop": "proxyType", "label": "是否代理", "componentProps": {"hideEl": {"NO_PROXY": ["proxyAddress"], "HADOOP_CLI": []}, "placeholder": "请选择", "options": [{"label": "hadoop-cli代理", "value": "HADOOP_CLI"}, {"label": "否", "value": "NO_PROXY"}]}, "rules": [{"message": "请选择是否代理", "required": true, "trigger": "change"}], "defaultVal": "NO_PROXY"}, {"type": "textarea", "prop": "proxyAddress", "label": "代理服务地址", "deps": ["proxyType"], "visible": "(scope) => scope.proxyType === 'HADOOP_CLI'", "componentProps": {"rows": 3, "maxlength": 1024, "placeholder": "请输入代理地址的IP和端口，如nameNode1:8020"}, "rules": [{"required": true, "message": "请输入代理地址的IP和端口，如nameNode1:8020", "trigger": "blur"}]}, {"type": "textarea", "prop": "memo", "label": "备注", "componentProps": {"rows": 5, "maxlength": 255, "placeholder": "请输入备注"}}]}