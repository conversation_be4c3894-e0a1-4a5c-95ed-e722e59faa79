<template>
  <div class="detail__container">
    <node-config
      v-if="dialogVisible"
      :visible="dialogVisible"
      :data="rawData"
      :org-id="orgId"
      :disabled="disabled"
      :job-data="flowData"
      @close="closeConfigDialog"
    />
    <node-map
      v-if="mapDialogVisible"
      :visible="mapDialogVisible"
      :data="rawData"
      :org-id="orgId"
      :disabled="disabled"
      :job-data="flowData"
      @close="closeConfigDialog"
    />
    <node-join
      v-if="joinDialogVisible"
      :visible="joinDialogVisible"
      :data="rawData"
      :org-id="orgId"
      :disabled="disabled"
      :job-data="flowData"
      @close="closeConfigDialog"
    />
    <node-filter
      v-if="filterDialogVisible"
      :visible="filterDialogVisible"
      :org-id="orgId"
      :data="rawData"
      :disabled="disabled"
      :job-data="flowData"
      :source-code="sourceCode"
      @close="closeConfigDialog"
    />
    <json-extract
      v-if="jsonExtractDialogVisible"
      :visible="jsonExtractDialogVisible"
      :data="rawData"
      :disabled="disabled"
      @close="closeConfigDialog"
    />
    <event-router
      v-if="eventRouterDialogVisible"
      :visible="eventRouterDialogVisible"
      :data="rawData"
      :disabled="disabled"
      @close="closeConfigDialog"
    />
    <dynamic-map
      v-if="dynamicMapDialogVisible"
      :visible="dynamicMapDialogVisible"
      :data="rawData"
      :disabled="disabled"
      @close="closeConfigDialog"
    />
    <dynamic-filter
      v-if="dynamicFilterDialogVisible"
      :visible="dynamicFilterDialogVisible"
      :data="rawData"
      :disabled="disabled"
      @close="closeConfigDialog"
    />
    <node-interval-join
      v-if="intervalJoinDialogVisible"
      :visible="intervalJoinDialogVisible"
      :data="rawData"
      :org-id="orgId"
      :disabled="disabled"
      :job-data="flowData"
      @close="closeConfigDialog"
    />
    <!-- 流立方批查批推 -->
    <batch-process
      v-if="showBatchDialog"
      :visible="showBatchDialog"
      :data="rawData"
      :disabled="disabled"
      :job-data="flowData"
      :org-id="orgId"
      @close="closeConfigDialog"
    />
    <!-- 数据质量监控组件 -->
    <data-quality-monitor
      v-if="dataQualityMonitorDialogVisible"
      :show.sync="dataQualityMonitorDialogVisible"
      :data="rawData"
      :disabled="disabled"
      :job-data="flowData"
      :org-id="orgId"
      @close="closeConfigDialog"
    />
    <!-- 延时处理组件 -->
    <process-delay
      v-if="processDelayDialogVisible"
      :visible="processDelayDialogVisible"
      :data="rawData"
      :org-id="orgId"
      :disabled="disabled"
      :job-data="flowData"
      :source-code="sourceCode"
      @close="closeConfigDialog"
    />
    <database-output
      v-if="showDatabaseOutputDialog"
      :show.sync="showDatabaseOutputDialog"
      :data="rawData"
      :org-id="orgId"
      :disabled="disabled"
      :job-data="flowData"
      @close="closeConfigDialog"
    />
    <database-collection
      v-if="showDatabaseCollectionDialog"
      :show.sync="showDatabaseCollectionDialog"
      :data="rawData"
      :org-id="orgId"
      :disabled="disabled"
      :job-data="flowData"
      @close="closeConfigDialog"
    />
    <!-- 数据定义映射组件 -->
    <dd-mapping
      v-if="showDdMappingDialogVisible"
      :visible="showDdMappingDialogVisible"
      :data="rawData"
      :org-id="orgId"
      :disabled="disabled"
      :job-data="flowData"
      @close="closeConfigDialog"
    />
  </div>
</template>

<script lang="ts">
import { Component, PropSync, Prop, Ref, Vue } from 'vue-property-decorator';
import ElForm from 'bs-ui-pro/packages/form';
import { MAP, FILTER, JOIN, INTERVAL_JOIN, JDBC_OUTPUT, JDBC_COLLECTION, MAPPING, DD_MAPPING } from './utils';

@Component({
  components: {
    FlowDrawer: () => import('../../../components/flow-drawer.vue'),
    NodeConfig: () => import('./modals/node-config/index.vue'),
    NodeMap: () => import('./modals/mapping/index.vue'),
    DdMapping: () => import('./modals/dd-mapping/index.vue'),
    NodeJoin: () => import('./modals/node-join.vue'),
    NodeFilter: () => import('./modals/node-filter/index.vue'),
    JsonExtract: () => import('./modals/json-extract.vue'),
    EventRouter: () => import('./modals/event-router.vue'),
    DynamicMap: () => import('./modals/dynamic-map.vue'),
    DynamicFilter: () => import('./modals/dynamic-filter.vue'),
    NodeIntervalJoin: () => import('./modals/node-interval-join.vue'),
    BatchProcess: () => import('./modals/cube-batch-process/index.vue'),
    DataQualityMonitor: () => import('./modals/data-quality-monitor/index.vue'),
    ProcessDelay: () => import('./modals/process-delay/index.vue'),
    DatabaseOutput: () => import('./modals/database-output/index.vue'),
    DatabaseCollection: () => import('./modals/database-collection/new-index.vue')
  }
})
export default class ComponentDetailConfig extends Vue {
  @PropSync('show', { default: false }) display!: boolean;
  @PropSync('data', { default: () => ({}) }) rawData!: any;
  @Prop({ default: () => ({}) }) flowData!: any;
  @Prop({ default: false }) isFullScreen!: boolean;
  @Ref('formRef') readonly form!: ElForm;

  sourceCode: any = {}; // 过滤组件方法源码
  dialogVisible = false;
  mapDialogVisible = false;
  filterDialogVisible = false;
  joinDialogVisible = false;
  intervalJoinDialogVisible = false;
  jsonExtractDialogVisible = false;
  eventRouterDialogVisible = false;
  dynamicMapDialogVisible = false;
  dynamicFilterDialogVisible = false;
  showSqlInput = false;
  showSqlProcessing = false;
  showBatchDialog = false;
  showSqlOutput = false;
  dataQualityMonitorDialogVisible = false;
  processDelayDialogVisible = false;
  showDatabaseOutputDialog = false;
  showDatabaseCollectionDialog = false;
  showDdMappingDialogVisible = false;

  get type() {
    if (this.rawData.className === MAP) return 'MAP';
    if (this.rawData.className === FILTER) return 'FILTER';
    if (this.rawData.className === JOIN) return 'JOIN';
    if (this.rawData.className === INTERVAL_JOIN) return 'INTERVAL_JOIN';
    if (this.rawData.className === JDBC_OUTPUT) return 'JDBC_OUTPUT';
    if (this.rawData.className === JDBC_COLLECTION) return 'JDBC_COLLECTION';
    if (this.rawData.className === DD_MAPPING) return 'DD_MAPPING';
    return this.rawData.type;
  }
  get key() {
    return MAPPING[this.type] || 'dialogVisible';
  }
  get disabled() {
    return this.flowData?.jobStatus !== 'DEV';
  }
  get orgId() {
    return this.$store.getters.orgId;
  }

  created() {
    this[this.key] = true;
  }
  closeConfigDialog({ needUpdate, jobNode }) {
    this.display = false;
    this.rawData = null;
    if (needUpdate === true) this.$emit('update', jobNode);
    this.dialogVisible = false;
    Object.values(MAPPING).forEach((el) => (this[el] = false));
  }
}
</script>
<style lang="scss" scoped>
.detail {
  &__container {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    padding: 20px 0;
    width: 100%;
    height: 100%;
  }
  &-icon {
    position: fixed;
    top: 200px;
    right: 30px;
    padding: 10px;
    cursor: pointer;
  }
}
</style>
