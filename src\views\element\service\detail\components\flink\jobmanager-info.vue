<template>
  <info-block :list="renderList" :title="$t('pa.jobMgrInfo')" />
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getJobMgrInfo } from '@/apis/serviceApi';
import { safeArray } from '@/utils';
@Component({
  components: { InfoBlock: () => import('../components/info-block.vue') }
})
export default class FlinkJobManagerInfo extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: () => ({}) }) params!: any;

  loading = false;
  id = '';
  rawData: any = {};
  renderList: any[] = [
    [
      { label: this.$t('pa.jobMgrMemory'), value: '', key: 'jobmanager.memory.process.size' },
      { label: this.$t('pa.parallelism'), value: '', key: 'parallelism.default' },
      {
        label: this.$t('pa.statusStorage'),
        value: '',
        key: 'state.backend'
      },
      { label: this.$t('pa.taskMgrMemory'), value: '', key: 'taskmanager.memory.process.size' },
      {
        label: this.$t('pa.taskMgrSlots'),
        value: '',
        key: 'taskmanager.numberOfTaskSlots'
      }
    ]
  ];

  get height() {
    return this.params?.height || '400px';
  }

  created() {
    this.id = this.$route.query.id as string;
    this.getJobMgrConf();
  }

  async getJobMgrConf() {
    try {
      this.loading = true;
      const { success, data, error } = await getJobMgrInfo(this.id);
      if (!success) return this.$message.error(error);
      this.rawData = safeArray(data).reduce((pre, next) => {
        pre[next.key] = next.value;
        return pre;
      }, {});
      const list = this.renderList[0].map(({ label, key }) => ({ label, value: this.rawData[key] || this.$t('pa.wu') }));
      this.$set(this.renderList, 0, list);
    } finally {
      this.loading = false;
    }
  }
}
</script>
