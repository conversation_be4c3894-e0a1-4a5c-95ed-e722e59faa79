<!--
 * @Description:
 * @Autor: magicyang
 * @Date: 2020-03-03 13:20:43
 * @LastEditors: magicyang
 * @LastEditTime: 2020-03-04 10:18:17
 -->
<template>
  <el-dialog
    title="条件配置"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    width="30%"
    append-to-body
  >
    <el-form ref="ruleForm" :model="formData" label-width="100px" :rules="rules">
      <el-form-item label="方法" prop="method">
        <el-select
          v-model="formData.method"
          clearable
          filterable
          style="width: 100%"
          @change="methodChange"
        >
          <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item
        v-for="(item, index) in formData.params"
        :key="index"
        :label="'参数' + item.type"
        :prop="'params.' + index + '.value'"
        :rules="{
          required: true,
          message: '参数' + item.type + '不能为空'
        }"
      >
        <el-autocomplete
          v-model="item.value"
          style="width: 100%"
          :fetch-suggestions="querySearchHandle"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">取消</el-button>
      <el-button type="primary" @click="submit('ruleForm')">确定</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { URL_ASSETS_FUNC_BOOLEAN_RETURN_LIST } from '@/apis/commonApi';
import * as _ from 'lodash';

@Component({})
export default class NodeFilterCond extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop() item!: any;
  @Prop() jobData!: any;
  @Prop({ default: [] }) input!: any;
  rules: any = {
    method: [
      {
        required: true,
        message: '请选择方法',
        trigger: 'blur'
      }
    ]
  };
  options: any = [];
  formData: any = {
    method: '',
    params: []
  };

  methodChange(val) {
    const rec: any = _.find(this.options, { name: val });
    this.formData.params = [];
    if (rec !== undefined) {
      /* eslint-disable-next-line */
      let index = 0;
      rec.paramTypes.forEach((n) => {
        index += 1;
        this.formData.params.push({
          type: n,
          value: ''
        });
      });
    }
  }
  submit(formName: string) {
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        this.formData.id = this.item.id;
        this.closeDialog(false, this.formData);
      } else {
        this.$tip.error('请检查输入内容');
        return;
      }
    });
  }

  @Emit('close')
  private closeDialog(flag, formData) {
    const cond = _.cloneDeep(formData);
    this.options = [];
    this.formData = { method: '', params: [] };
    return cond;
  }
  querySearchHandle(queryString, cb) {
    const list = this.input;
    const results = queryString ? list.filter(this.createFilter(queryString)) : list;
    // 调用 callback 返回建议列表的数据
    cb(results);
  }
  createFilter(queryString) {
    return (obj) => {
      return _.toLower(obj.value).indexOf(_.toLower(queryString)) >= 0;
    };
  }
  @Watch('visible')
  visibleChanged(val) {
    if (val) {
      Vue.axios
        .get(URL_ASSETS_FUNC_BOOLEAN_RETURN_LIST, {
          params: {
            orgId: this.jobData.orgId,
            projectName: '',
            jobName: ''
          }
        })
        .then((resp: any) => {
          if (resp.success) {
            this.options = resp.data;
          } else {
            this.$tip.error(resp.msg);
          }
        });
      if (this.item.method !== undefined) {
        this.formData.method = this.item.method;
      }
      if (this.item.params !== undefined) {
        this.formData.params = this.item.params;
      }
    }
  }
}
</script>
<style lang="scss" scoped></style>
