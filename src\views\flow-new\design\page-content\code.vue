<template>
  <div v-loading="loading" element-loading-text="加载中..." class="code__container">
    <codemirror :key="key" :code="sourceCode" :options="options" :origin-code="sourceCode" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import sha256 from 'sha256';
import { isEmpty, cloneDeep } from 'lodash';
import { post } from '@/apis/utils/net';
import { URL_JOB_SOURCECODE } from '@/apis/commonApi';

@Component({
  components: {
    codemirror: () => import('@/components/codemirror/codemirror.vue')
  }
})
export default class FlowCode extends Vue {
  @Prop({ default: () => {} }) flowData!: any;

  private loading = false;
  private sourceCode = '';
  private options: any = {
    mode: 'javascript',
    readOnly: true,
    lineNumbers: true,
    line: true,
    indentUnit: 4,
    lineWrapping: true,
    foldGutter: true,
    gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter']
  };

  get key() {
    return sha256(this.sourceCode);
  }

  created() {
    this.getSourceCode();
  }

  async getSourceCode() {
    try {
      if (isEmpty(this.flowData)) return;
      this.loading = true;
      this.sourceCode = '';
      const { success, data, error } = await post(URL_JOB_SOURCECODE, cloneDeep(this.flowData));
      if (success) {
        this.sourceCode += data;
        this.loading = false;
        return;
      }
      this.$tip.error(error);
      this.loading = false;
    } catch {
      this.loading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.code {
  &__container {
    width: 100%;
    height: calc(100% - 40px);
    ::v-deep .CodeMirror {
      &-sizer {
        padding-bottom: 8px !important;
      }
      &-vscrollbar {
        display: none !important;
      }
    }
  }
}
</style>
