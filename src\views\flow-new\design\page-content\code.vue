<template>
  <div v-loading="loading" :element-loading-text="$t('pa.loading')" class="code__container">
    <bs-code :key="key" :value="sourceCode" language="java" :operatable="false" :extra-style="{ height: '100%' }" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import sha256 from 'sha256';
import { isEmpty, cloneDeep } from 'lodash';
import { post } from '@/apis/utils/net';
import { URL_JOB_SOURCECODE } from '@/apis/commonApi';

@Component
export default class FlowCode extends Vue {
  @Prop({ default: () => {} }) flowData!: any;

  loading = false;
  sourceCode = '';

  get key() {
    return sha256(this.sourceCode);
  }

  created() {
    this.getSourceCode();
  }

  async getSourceCode() {
    try {
      if (isEmpty(this.flowData)) return;
      this.loading = true;
      this.sourceCode = '';
      const { success, data, error } = await post(URL_JOB_SOURCECODE, cloneDeep(this.flowData));
      if (!success) return this.$tip.error(error);
      this.sourceCode += data;
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.code {
  &__container {
    width: 100%;
    height: calc(100% - 40px);
    .bs-code {
      height: calc(100% - 22px);
      border: 0;
    }
  }
}
</style>
