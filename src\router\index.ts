import Vue from 'vue';
import VueRouter, { RawLocation, Route } from 'vue-router';
import { router as monitorRouter } from './monitor';
import { router as bloodRelationRouter } from './blood-relation';
import { router as flowNewRouter } from './flow-new';
import { router as elementRouter } from './element';
import { router as paramsRouter } from './params';
import { router as dataRouter } from './data-manage';
import { router as menuSourceRouter } from './menuSource';
import { router as sourceRouter } from './source';
import { router as homeRouter } from './home';
import { router as globalSearchRouter } from './globalSearch';
import store from '@/store';
import { ErrorHandler } from 'vue-router/types/router';
import { hasPermission, hasCustomPermission } from '@/utils';

const originalPush = VueRouter.prototype.push;
const originalReplace = VueRouter.prototype.replace;

const handlerRouterPushAndReplace = function (
  this: any,
  location: RawLocation,
  onResolve: ((route: Route) => void) | undefined,
  onReject: ErrorHandler | undefined
) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject);
  return (originalReplace.call(this, location) as any).catch((err) => err);
};

(VueRouter.prototype as any).push = handlerRouterPushAndReplace;
(VueRouter.prototype as any).replace = handlerRouterPushAndReplace;
Vue.use(VueRouter);

const routes: any[] = [
  ...homeRouter,
  ...monitorRouter,
  ...dataRouter,
  ...flowNewRouter,
  ...bloodRelationRouter,
  ...elementRouter,
  ...paramsRouter,
  ...sourceRouter,
  ...globalSearchRouter,
  {
    // 用于当前tab页无感知刷新
    path: '/refresh',
    name: 'refresh',
    meta: {
      isReplaced: true
    },
    component: () => import(/* webpackChunkName: "refresh" */ '../components/refresh.vue')
  }
];

export const menuRoutes = [
  ...homeRouter,
  ...monitorRouter,
  ...dataRouter,
  ...flowNewRouter,
  ...bloodRelationRouter,
  ...elementRouter,
  ...menuSourceRouter,
  ...paramsRouter,
  ...globalSearchRouter
];
const router: any = new VueRouter({ routes });
router.beforeEach((to: any, from: any, next: (arg0: boolean | undefined) => void): void => {
  if (to.matched && to.matched.length < 1) return next(false);
  if (!hasCustomPermission(to.meta.customAccess)) return next(false);
  if (store.state.others.isFuseMode && to.path.includes('assets')) return next(false);
  const isAboutSqlRoute = ['DAEMON', 'DTS'].includes(to.query.type);
  if (!store.state.others.enableSql && isAboutSqlRoute) return next(false);
  if (!hasPermission(to.meta.access)) return next(false);
  next(true);
});

export default router;
