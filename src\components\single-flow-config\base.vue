<template>
  <el-collapse v-model="activeList" class="base">
    <el-collapse-item name="baseConfig">
      <div slot="title" class="resource-title">基本参数</div>
      <template v-for="el in renderList">
        <el-form-item
          v-show="!el.hidden"
          :key="el.name"
          :prop="el.name"
          :label="el.label"
          :rules="rules[el.name]"
        >
          <div :class="['resource-item', el.tip ? 'resource-item--tip' : '']">
            <!-- select -->
            <el-select
              v-if="el.type === 'select'"
              v-model="form[el.name]"
              filterable
              clearable
              :placeholder="el.placeholder"
              :popper-append-to-body="true"
              @change="handleSelectChange"
            >
              <el-option-group
                v-for="group in clusterGroup"
                :key="group.label"
                :label="group.label"
              >
                <el-option
                  v-for="item in group.options"
                  :key="item.id"
                  :value="item.id"
                  :label="`${item.title}( ${item.url} )`"
                >
                  <el-popover
                    v-if="(item.title + item.url).length >= 50"
                    placement="top-start"
                    trigger="hover"
                  >
                    <p>{{ `${item.title}( ${item.url} )` }}</p>
                    <span slot="reference">{{ (item.title + item.url).slice(0, 45) + '...' }}</span>
                  </el-popover>
                </el-option>
              </el-option-group>
            </el-select>
            <!-- number -->
            <el-input-number
              v-if="el.type === 'number'"
              v-model="form[el.name]"
              number
              :min="el.min"
              :max="el.max"
              :step="el.step ? el.step : 1"
              :placeholder="el.placeholder"
              :disabled="el.disabled"
            />
            <el-checkbox
              v-if="el.type === 'checkbox'"
              v-model="form[el.name]"
              :disabled="el.disabled"
            >
              {{ el.desc }}
            </el-checkbox>
          </div>
          <el-tooltip v-if="el.tip" effect="light" :content="el.tip" placement="bottom">
            <i class="iconfont icon-wenhao base-icon"></i>
          </el-tooltip>
        </el-form-item>
      </template>
    </el-collapse-item>
  </el-collapse>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Watch, Vue } from 'vue-property-decorator';
import { get } from '@/apis/utils/net';
import { URL_RES_FLINK } from '@/apis/commonApi';
import { cloneDeep } from 'lodash';

@Component
export default class BaseConfig extends Vue {
  @Prop({ type: String, default: '' }) id!: string;
  @Prop({ type: Object, default: () => ({}) }) data!: any;
  @Prop({ type: String, default: '' }) orgId!: string;
  @Prop({ type: Boolean, default: false }) isCloud!: boolean;
  @Prop({ type: Boolean, default: false }) parallelismDisabled!: boolean;
  @PropSync('data', { type: Object, default: () => ({}) }) form!: any;
  @Prop({ default: () => [] }) cluster!: any[]; // 批量模式从外部传入 集群列表
  @Prop({ default: false }) isBatch!: boolean; //批量资源配置
  private activeList = ['baseConfig'];
  private clusterGroup: any[] = [
    { label: 'YARN_PER_JOB', options: [] },
    { label: 'STANDALONE', options: [] },
    { label: 'YARN_APPLICATION', options: [] },
    { label: 'YARN_SESSION', options: [] },
    { label: '云服务', options: [] }
  ]; // 集群列表
  private rules: any = {
    clusterId: [{ required: true, message: '请选择集群', trigger: 'change' }],
    taskManagerMemory: [{ required: true, message: '请输入task manager内存', trigger: 'blur' }],
    taskManagerSlotNumber: [
      { required: true, message: '请输入task manager slot数量', trigger: 'blur' }
    ],
    taskManagerCpuNumber: [
      { required: false, message: '请输入task manager CPU个数', trigger: 'blur' }
    ],
    parallelism: [{ required: true, message: '请输入并行度', trigger: 'blur' }]
  };

  // 渲染列表
  get renderList() {
    return [
      {
        name: 'clusterId',
        label: '集群名称',
        type: 'select'
      },
      {
        name: 'taskManagerMemory',
        label: 'task manager内存(MB)',
        type: 'number',
        hidden: false,
        min: 0,
        max: 1048576,
        tip: 'taskManager负责具体的任务执行，设置该项为对应任务在每个节点上的内存数量。'
      },
      {
        name: 'taskManagerSlotNumber',
        label: 'task manager slot个数',
        placeholder: '请输入task manager slot个数',
        hidden: false,
        type: 'number',
        min: 0,
        max: 99,
        tip: '配置一个TaskManager有多少个并发的slot数，类似于每个TaskManager内有多少个线程。'
      },
      {
        name: 'taskManagerRequestCpu',
        label: 'task manager CPU',
        placeholder: '请输入task manager CPU个数',
        hidden: true,
        type: 'number',
        min: 0,
        max: 99,
        step: 0.1,
        tip: '该配置项指定了TaskManager运行分配的最小CPU'
      },
      {
        name: 'parallelism',
        label: '并行度',
        placeholder: '请输入并行度',
        type: 'number',
        min: 0,
        max: 2147483647,
        disabled: this.parallelismDisabled
      },
      {
        name: 'isApplyParallelism',
        label: '',
        desc: '同步到每个组件',
        type: 'checkbox',
        disabled: this.parallelismDisabled
      }
    ];
  }

  @Watch('isCloud', { immediate: true })
  handleIsCloud(val: boolean) {
    this.$set(this.renderList[3], 'hidden', !val);
    this.$set(this.rules.taskManagerCpuNumber[0], 'required', val);
  }
  @Watch('orgId', { immediate: true })
  hander() {
    this.getClusterList();
  }

  /* 获取集群列表 */
  async getClusterList() {
    try {
      if (!this.orgId) return;
      const {
        success = true,
        data,
        error = ''
      } = this.isBatch ? { data: this.cluster } : await get(URL_RES_FLINK, { orgId: this.orgId });
      if (success) {
        const arr3 = Array.isArray(data) ? [...data] : [];
        const clusterMap = {
          YARN_PER_JOB: this.clusterGroup[0].options,
          STANDALONE: this.clusterGroup[1].options,
          YARN_APPLICATION: this.clusterGroup[2].options,
          YARN_SESSION: this.clusterGroup[3].options,
          CLOUD: this.clusterGroup[4].options
        };
        const arr = arr3.map((item) => {
          const { id, title, url, namespace, queue, clusterType = '' } = item;
          return {
            id,
            title,
            url,
            clusterType,
            custom: namespace || queue || ''
          };
        });
        arr.forEach((item) => clusterMap[item.clusterType].push(item));
        this.handleSelectChange(this.form.clusterId, true);
        return;
      }
      this.$message.error(error);
    } catch (e) {
      this.clusterGroup.forEach((el, index) => {
        this.$set(this.clusterGroup[index], 'options', []);
      });
    }
  }

  /* 集群变化 */
  handleSelectChange(id: string, auto = false) {
    const [{ options: n1 }, { options: n2 }, { options: n3 }, { options: n4 }, { options: n5 }] =
      this.clusterGroup;
    const item = [...n1, ...n2, ...n3, ...n4, , ...n5].filter(Boolean).find((el) => {
      if (id && el.id === id) return el;
    });
    if (item) {
      const { clusterType, title, url } = item;
      // 当选中Standalone、yarn session模式，隐藏task manager内存、task manager slot个数;
      const isHiddenClusterType = ['STANDALONE', 'YARN_SESSION'].includes(clusterType);
      this.setRenderListValue('taskManagerMemory', isHiddenClusterType ? true : false);
      this.setRenderListValue('taskManagerSlotNumber', isHiddenClusterType ? true : false);
      this.$set(this.form, 'url', url);
      this.$set(this.form, 'resTitle', title);
      this.$set(this.form, 'property', cloneDeep(item));
      this.$set(this.form, 'clusterType', clusterType);
      if (!auto) {
        this.$set(this.form, 'namespace', '');
        this.$set(this.form, 'queue', '');
      }
      return;
    }
    this.$set(this.form, 'url', '');
    this.$set(this.form, 'property', {});
    this.$set(this.form, 'resTitle', '');
    this.$set(this.form, 'clusterType', '');
  }

  setRenderListValue(name: string, value: any, key = 'hidden') {
    const index = this.renderList.findIndex((el) => el.name === name);
    if (index > -1) {
      this.$set(this.renderList[index], key, value);
    }
  }
}
</script>

<style scoped lang="scss">
.base {
  &-icon {
    display: inline-block;
    margin: 0 0 0 10px;
    font-size: 16px;
  }
  &-item {
    display: inline-block;
    width: 100%;
    background: pink;
    .el-select,
    .el-input-number {
      width: 100%;
    }
    &--tip {
      width: calc(100% - 20px);
    }
  }
}
</style>
