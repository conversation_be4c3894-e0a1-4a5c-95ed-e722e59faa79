import { get, post, del, file, download } from './utils/net';

// 新建SQL代码片段(单个)
export const addSql = (data: unknown) => {
  return post('/rs/pa/sql/add', data);
};

// 根据id查找SQL代码片段
export const findById = (id: string) => {
  return get(`/rs/pa/sql/findById?id=${id}`);
};

// 根据类型查找SQL代码片段
export const getSqlByType = (data: unknown) => {
  return get(`/rs/pa/sql/findByType`, data);
};

//获取全部sql代码片段(分页)
export const getAllSql = (data: unknown) => {
  return post('/rs/pa/sql/list', data);
};

// 根据全部的sql代码片段（不分页）
export const getAllSqlList = () => {
  return get('/rs/pa/sql/findAllList');
};

//删除sql代码片段
export const delSql = (data: unknown) => {
  return del('/rs/pa/sql/deleteById', {}, { data });
};

//导出sql代码片段
export const exportSql = (data: any) => {
  return download(
    '/rs/pa/sql/exportSqlCode',
    '',
    data,
    { blob: 'blob', fileName: 'fileName' },
    {
      responseType: 'blob',
      timeout: 60000,
      method: 'post'
    }
  );
};

//验证导入sql片段是否重复
export const validateImportSql = (data: any) => {
  return file('/rs/pa/sql/validateFile', data, {
    headers: { ContentType: 'multipart/form-data' }
  });
};

//导入sql片段
export const importSqlList = (data: unknown) => {
  return post('/rs/pa/sql/upload', data);
};

// 数据集-表管理列表
export const getTableMgrList = (search: string) => {
  return get(`/rs/pa/sql/getTableManageType?search=${search}`);
};
// 数据集-表管理-生成sql模板
export const getTableMgrSql = (data: any) => {
  let paramsStr = '';
  if (data.connInfo || data.tableUseFor) {
    paramsStr += `&connInfo=${data.connInfo}&tableUseFor=${data.tableUseFor}`;
  }
  return get(`/rs/pa/sql/formManageTable?tableId=${data.tableId}&jobId=${data.jobId}${paramsStr}`);
};
// 数据集-原始表-生成sql模板
export const getOriginTableSql = (data: any) => {
  return post('/rs/pa/sql/formOriginalTable', data);
};
// 数据集-原始表：只返回2级-类型&服务
export const getOriginTableGroup = () => {
  return get('/rs/pa/sql/getOriginalTableType');
};
// 数据集-原始表：点击服务获取下面的表
export const getOriginTableByResId = (resId: string, resType: string) => {
  return get(`/rs/pa/sql/getTableByResId?id=${resId}&resType=${resType}`);
};
// 数据集-原始表：需要进行配置的获取连接器&用途信息
export const getConnInfoAndTableUseFor = (tableId: string, resType: string) => {
  return get(`/rs/pa/sql/getConnInfoAndTableUseFor?tableId=${tableId}&resType=${resType}`);
};

// 修改SQL代码片段
export const updateSql = (data: unknown) => {
  return post('/rs/pa/sql/update', data);
};
// 缩略词校验
export const validateCode = (shortCode: string) => {
  return get('/rs/pa/sql/validateShortCode', { shortCode });
};

// 解析sql
export const parsingSql = (data: unknown) => {
  return post('/rs/pa/sql/table/parsingSql', data);
};

// 解析sql代码中表对应的字段
export const extractSqlFields = (data: unknown) => {
  return post('/rs/pa/sqlJob/extractSqlFields', data);
};
