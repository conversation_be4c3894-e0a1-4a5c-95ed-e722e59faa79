import i18n from '@/i18n';

export const TEMPLATES_CONFIG = [
  { label: i18n.t('pa.flowInfo') as string, value: 'infoConfig' },
  {
    label: i18n.t('pa.basicParams'),
    value: 'baseConfig'
  },
  {
    label: i18n.t('pa.advancedParams'),
    value: 'advanceConfig'
  },
  {
    label: i18n.t('pa.customParams'),
    value: 'customerConfig'
  }
];

export const FLOW_DEFAULT_CONFIG = () => {
  return {
    jobName: '',
    jobType: '',
    originalJobName: '', // 流程名称
    prefix: '', // 流程名称前缀
    suffix: '', // 流程名称后缀
    memo: '',
    clusterId: '', // 集群id
    clusterType: '', // 集群类型
    deployTimeout: 90, //上线超时时间
    jobManagerMemory: 1024,
    taskManagerMemory: 1024,
    taskManagerSlotNumber: 1,
    taskManagerRequestCpu: 0.5, // task manager Cpu
    taskManagerLimitCpu: 1, // task manager Cpu爆发倍数
    taskManagerLimitMemory: 1, // task manager 内存爆发倍数
    jobManagerRequestCpu: 0.5, // job manager Cpu
    jobManagerLimitCpu: 1, // job manager Cpu爆发倍数
    jobManagerLimitMemory: 1, // job manager 内存爆发倍数
    parallelism: 1, // 并行度
    isApplyParallelism: true, // 同步到每个组件
    queue: '', // 队列
    disableOperatorChain: false, // 是否打断算子链
    enableCheckPoint: true, // 是否开启checkpoint
    delay: 10, // 固定重启延迟时间
    failureRateDelay: 10, // 失败重启延迟时间
    checkpointInterval: 60000,
    cp_timeout: 60000,
    cp_min_pause: 500,
    taskmanager_managed_frac: 0.1,
    cp_failed: 0,
    cp_unaligned: false,
    logOutputKafka: true, // 输出日志到kafka
    stateBackend: 'filesystem', // 状态后端
    restartStrategy: '', // 重启策略
    attempts: 5, // 尝试次数
    failuresPerInterval: 1, // 重启次数
    failureRateInterval: 1, // 时间间隔
    mode: 'stream', // 模式选择
    jobRunningRule: '', // 任务运行规则 一次性、间隔周期、时间周期、自定义
    startTime: '', // 上线时间
    intervalHour: 0, // 间隔小时
    intervalMinute: 0, // 间隔分钟
    repeatTime: '', // 重复时间
    cron: '', // cron表达式
    initialBackoff: 1, // 初始间隔时间
    maxBackoff: 300, // 最大间隔时间
    backoffMultiplier: 2.0, // 增长乘数
    resetBackoffThreshold: 60, // 最小稳定运行时间,
    jitterFactor: 0.1, // 振动因子,
    projectId: ''
  };
};

// 高级参数渲染列表
export const ADVANCE_RENDER_LIST = () => [
  {
    name: 'taskManagerLimitCpu',
    label: i18n.t('pa.taskManagerLimitCpu'),
    placeholder: i18n.t('pa.placeholder.taskManagerLimitCpu'),
    type: 'number',
    min: 1,
    max: 4,
    hidden: true,
    tip: i18n.t('pa.tip.taskMgrLimitCpu')
  },
  {
    name: 'taskManagerLimitMemory',
    label: i18n.t('pa.taskManagerLimitMemory'),
    placeholder: i18n.t('pa.placeholder.taskManagerLimitMemory'),
    type: 'number',
    min: 1,
    max: 4,
    hidden: true,
    tip: i18n.t('pa.tip.taskMgrLimitMemory')
  },
  {
    name: 'jobManagerRequestCpu',
    label: i18n.t('pa.jobManagerRequestCpu'),
    placeholder: i18n.t('pa.placeholder.jobManagerRequestCpu'),
    type: 'number',
    min: 0.1,
    max: 10,
    step: 0.1,
    hidden: true,
    tip: i18n.t('pa.tip.jobMgrRequestCpu')
  },
  {
    name: 'deployTimeout',
    label: i18n.t('pa.deployTimeout'),
    type: 'number',
    min: 30,
    max: 6000,
    unit: 's'
  },
  {
    name: 'jobManagerMemory',
    label: i18n.t('pa.jobManagerMemory'),
    placeholder: i18n.t('pa.placeholder.jobManagerMemory'),
    hidden: false,
    type: 'number',
    min: 1,
    max: 1048576,
    tip: i18n.t('pa.tip.jobMgrMemory')
  },
  {
    name: 'queue',
    label: i18n.t('pa.queue'),
    placeholder: i18n.t('pa.placeholder.queue'),
    type: 'select',
    options: [],
    hidden: true,
    tip: i18n.t('pa.tip.queue')
  },
  {
    name: 'taskmanager_managed_frac',
    label: i18n.t('pa.taskManagerFrac'),
    placeholder: i18n.t('pa.placeholder.taskManagerFrac'),
    hidden: false,
    type: 'number',
    min: 0,
    max: 0.7,
    step: 0.1,
    precision: 1,
    tip: i18n.t('pa.tip.taskManagerFrac')
  },
  {
    name: 'restartStrategy',
    label: i18n.t('pa.restartStrategy'),
    placeholder: i18n.t('pa.placeholder.restartStrategy'),
    type: 'select',
    options: ['fixed-delay', 'failure-rate', 'exponential-delay', 'none']
  },
  {
    name: 'attempts',
    label: i18n.t('pa.attempts'),
    placeholder: i18n.t('pa.placeholder.attempts'),
    type: 'number',
    min: 1,
    max: 2147483647,
    hidden: true
  },
  {
    name: 'delay',
    label: i18n.t('pa.delay'),
    placeholder: i18n.t('pa.placeholder.delay'),
    type: 'number',
    min: 1,
    max: 100,
    hidden: true,
    unit: 's'
  },
  {
    name: 'initialBackoff',
    label: i18n.t('pa.initialBackoff'),
    type: 'number',
    min: 1,
    max: 2147483647,
    hidden: true,
    unit: 's'
  },
  {
    name: 'maxBackoff',
    label: i18n.t('pa.maxBackoff'),
    type: 'number',
    min: 2,
    max: 2147483647,
    hidden: true,
    unit: 's'
  },
  {
    name: 'backoffMultiplier',
    label: i18n.t('pa.backoffMultiplier'),
    type: 'number',
    precision: 1,
    step: 0.1,
    hidden: true
  },
  {
    name: 'resetBackoffThreshold',
    label: i18n.t('pa.resetBackoffThreshold'),
    type: 'number',
    min: 0,
    max: 2147483647,
    unit: 'min',
    hidden: true
  },
  {
    name: 'jitterFactor',
    label: i18n.t('pa.jitterFactor'),
    type: 'number',
    step: 0.1,
    min: 0.1,
    max: 1.0,
    precision: 1,
    hidden: true
  },
  {
    name: 'failureRateDelay',
    label: i18n.t('pa.failureRateDelay'),
    placeholder: i18n.t('pa.placeholder.failureRateDelay'),
    type: 'number',
    min: 1,
    max: 100,
    hidden: true,
    unit: 's'
  },
  {
    name: 'failuresPerInterval',
    label: i18n.t('pa.failuresPerInterval'),
    placeholder: i18n.t('pa.placeholder.failuresPerInterval'),
    type: 'number',
    min: 1,
    max: 2147483647,
    hidden: true
  },
  {
    name: 'failureRateInterval',
    label: i18n.t('pa.failureRateInterval'),
    placeholder: i18n.t('pa.placeholder.failureRateInterval'),
    type: 'number',
    min: 1,
    max: 2147483647,
    hidden: true,
    unit: 'min'
  },
  {
    name: 'disableOperatorChain',
    label: i18n.t('pa.disableOperatorChain'),
    type: 'checkbox',
    tip: i18n.t('pa.resource.tip1'),
    wrap: true
  },
  {
    name: 'logOutputKafka',
    label: i18n.t('pa.logOutputKafka'),
    type: 'checkbox'
  },
  {
    name: 'enableCheckPoint',
    label: i18n.t('pa.enableCheckPoint'),
    type: 'checkbox',
    tip: i18n.t('pa.resource.tip2'),
    wrap: true
  },
  {
    name: 'cp_unaligned',
    label: i18n.t('pa.cpUnaligned'),
    type: 'checkbox',
    tip: i18n.t('pa.tip.cpUnaligned')
  },
  {
    name: 'checkpointInterval',
    label: i18n.t('pa.checkpointInterval'),
    placeholder: i18n.t('pa.placeholder.checkpointInterval'),
    type: 'number',
    min: 1,
    max: 1000000,
    unit: 'ms',
    hidden: false
  },
  {
    name: 'cp_timeout',
    label: i18n.t('pa.cpTimeout'),
    placeholder: i18n.t('pa.flow.msg120'),
    type: 'number',
    min: 1,
    max: 1000000,
    unit: 'ms',
    hidden: false
  },
  {
    name: 'cp_min_pause',
    label: i18n.t('pa.cpMinPause'),
    placeholder: i18n.t('pa.placeholder.cpMinPause'),
    type: 'number',
    min: 1,
    max: 10000,
    unit: 'ms',
    hidden: false
  },
  {
    name: 'cp_failed',
    label: i18n.t('pa.cpFailed'),
    placeholder: i18n.t('pa.placeholder.cpFailed'),
    type: 'number',
    min: 0,
    max: 100,
    tip: i18n.t('pa.tip.cpFailed')
  },
  {
    name: 'stateBackend',
    label: i18n.t('pa.stateBackend'),
    placeholder: i18n.t('pa.placeholder.stateBackend'),
    type: 'select',
    options: ['jobmanager', 'filesystem', 'rocksdb']
  },
  {
    name: 'jobManagerLimitCpu',
    label: i18n.t('pa.jobManagerLimitCpu'),
    placeholder: i18n.t('pa.tip.jobManagerLimitCpu'),
    type: 'number',
    min: 1,
    max: 4,
    hidden: true,
    tip: i18n.t('pa.tip.jobMgrLimitCpu')
  },
  {
    name: 'jobManagerLimitMemory',
    label: i18n.t('pa.jobManagerLimitMemory'),
    placeholder: i18n.t('pa.tip.jobManagerLimitMemory'),
    type: 'number',
    min: 1,
    max: 4,
    hidden: true,
    tip: i18n.t('pa.tip.jobMgrLimitMemory')
  },
  {
    name: 'namespace',
    label: i18n.t('pa.namespace'),
    placeholder: i18n.t('pa.flow.msg104'),
    type: 'select',
    options: [],
    hidden: true,
    tip: i18n.t('pa.tip.namespace')
  }
];

// 不开启checkpoint时删除下列字段
export const NO_CHECK_POINT = [
  'checkpointInterval', // checkpoint周期
  'cp_timeout', // checkpoint超时时间
  'cp_min_pause', // checkpoint最小停顿时间
  'cp_failed', // 可容忍的检查点故障
  'cp_unaligned', // 是否开启未对齐检查点
  'stateBackend' // 状态后端
];
// 重启策略映射，不同的策略对应不同的字段：这里对应的是选中某个策略后，需要删除的字段
export const STRATEGY_MAPPING = {
  'failure-rate': [
    'attempts',
    'delay',
    'initialBackoff',
    'maxBackoff',
    'backoffMultiplier',
    'resetBackoffThreshold',
    'jitterFactor'
  ],
  'fixed-delay': [
    'failureRateDelay',
    'failuresPerInterval',
    'failureRateInterval',
    'initialBackoff',
    'maxBackoff',
    'backoffMultiplier',
    'resetBackoffThreshold',
    'jitterFactor'
  ],
  'exponential-delay': ['failureRateDelay', 'attempts', 'delay', 'failuresPerInterval', 'failureRateInterval'],
  all: [
    'attempts',
    'delay',
    'failuresPerInterval',
    'failuresPerInterval',
    'failureRateDelay',
    'initialBackoff',
    'maxBackoff',
    'backoffMultiplier',
    'resetBackoffThreshold',
    'jitterFactor'
  ]
};
