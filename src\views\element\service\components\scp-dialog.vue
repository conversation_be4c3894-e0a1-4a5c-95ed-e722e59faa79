<template>
  <bs-dialog :title="$t('pa.action.upload')" width="40%" :visible.sync="display">
    <el-alert :title="$t('pa.tip.scp')" type="success" :closable="false" />
    <el-form ref="formRef" :key="key" :model="formData" :rules="formRule" label-width="70px">
      <el-form-item :label="$t('pa.file')" prop="pkg">
        <el-upload action :limit="1" :multiple="false" :http-request="handleFile" :on-exceed="handleExceed">
          <el-button size="small" type="primary">{{ $t('pa.choseFile') }}</el-button>
        </el-upload>
      </el-form-item>
    </el-form>
    <!-- footer -->
    <div slot="footer">
      <el-button @click="display = false">{{ $t('pa.action.close') }}</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">{{ $t('pa.action.upload') }}</el-button>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, PropSync, Prop, Ref, Vue } from 'vue-property-decorator';
import ElForm from 'bs-ui-pro/packages/form/index.js';
import { scpToHost } from '@/apis/serviceApi';

@Component
export default class ScpDialog extends Vue {
  @PropSync('show', { required: true, default: false }) display!: boolean;
  @Prop({ default: () => ({}) }) data!: any;
  @Ref('formRef') readonly form!: ElForm;

  loading = false;
  key = Date.now();
  formData: any = { pkg: null };
  get formRule() {
    return {
      pkg: {
        required: true,
        trigger: 'blur',
        validator: (rule: any, value: any, callback: any) => {
          if (!value) return callback(new Error(this.$t('pa.tip.addFile')));
          callback();
        }
      }
    };
  }

  handleFile(file: any) {
    if (file?.file) {
      this.formData.pkg = file.file;
    }
    this.form.validate();
  }
  handleExceed(files: any[]) {
    this.$message.warning(this.$t('pa.tip.fileCount', [files.length]));
  }
  async handleSubmit() {
    try {
      await this.form.validate();
    } catch {
      this.$message.error(this.$t('pa.tip.checkMessage'));
      return;
    }
    try {
      this.loading = true;
      const params = new FormData();
      params.append('id', this.data.id);
      params.append('file', this.formData.pkg);
      const { success, msg, error } = await scpToHost(params);
      if (!success) return this.$message.error(error);
      this.$message.success(msg);
      this.key = Date.now();
      this.formData.pkg = null;
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-top: 20px;
}
</style>
