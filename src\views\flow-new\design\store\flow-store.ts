// import { job } from '@/store/modules/job';
import { JobBaseInfo, StaticData } from '../interface';
import { dateFormat } from '@/common/utils';
import { OriginEdge, OriginNode } from '../page-content/canvas/interface';
import cloneDeep from 'lodash/cloneDeep';
import { isFlinkSql } from '@/utils';
import i18n from '@/i18n';
class FlowStore {
  // 流程是否发生变更
  private _dataChange: boolean;
  // 流程基础信息
  public baseInfo: JobBaseInfo;
  // 流程资源信息
  // public resourceInfo: JobResourceInfo;
  // 流程画布信息
  public content: { edges: OriginEdge[]; nodes: OriginNode[]; nodeNum: number };

  public staticData: StaticData;

  constructor(data) {
    const rawData = cloneDeep(data);
    this._dataChange = false;
    this.baseInfo = this.setBaseInfo(rawData);
    this.content = isFlinkSql(data.jobType)
      ? data.content
      : JSON.parse(rawData.content || '{"edges":[],"nodes":[],"nodeNum":0}');
    delete rawData.content;
    this.staticData = rawData;
  }
  get dataChange(): boolean {
    return this._dataChange;
  }

  set dataChange(val: boolean) {
    this._dataChange = val;
  }

  // 流程基础信息
  setBaseInfo(data) {
    return {
      id: data.id || '',
      jobStatus: data.jobStatus || 'DEV',
      jobRunTimeStatus: data.jobRunTimeStatus || '-',
      jobName: data.jobName || i18n.t('pa.flow.flowName'),
      memo: data.memo || '-',
      createdBy: data.createdBy || '-',
      createTime: dateFormat(data.createTime),
      orgName: data.orgName || '-',
      urls: this.handleUrls(data),
      jobType: data.jobType || 'PROCESSFLOW',
      jobRuntimeId: data.jobRuntimeId || '',
      properties: data.properties,
      projectId: data.projectId
    };
  }

  handleUrls({ properties, applicationId, cloudJob, webUrl = '-' }: any) {
    let result: string[] = [webUrl];
    try {
      const { url } = JSON.parse(properties);
      if (applicationId && url && !cloudJob) {
        result = url.split(',').map((el) => `http://${el}/proxy/${applicationId}/`);
      }
      return result;
    } catch (e) {
      return result;
    }
  }
  // 流程资源信息
  setResourceInfo() {
    return {};
  }

  // 流程节点信息
  setContent(data: any) {
    this.content = data;
  }

  // 流程保存的接口
  getComplete() {}
}
export default FlowStore;
