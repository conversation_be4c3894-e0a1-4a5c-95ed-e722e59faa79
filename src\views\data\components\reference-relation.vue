<template>
  <div class="reference-relation">
    <el-select
      v-if="relationTypeList"
      v-model="selectType"
      class="reference-relation__select"
      size="small"
      @change="getRelationList"
    >
      <el-option
        v-for="item in relationTypeList"
        :key="item.value"
        :value="item.value"
        :label="item.label"
      />
    </el-select>
    <!-- 页面正常显示 -->
    <div class="reference-relation__content">
      <bs-table
        :key="key"
        v-loading="realtionListLoading"
        height="calc(100vh - 231px)"
        :class="{ 'is-func': isFunc }"
        :data="tableData"
        :column-settings="false"
        :column-data="columnData"
        :page-data="pageData"
        @page-change="handleListPageChange"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import {
  URL_GET_TABLE_RELATION_LIST,
  URL_EXPORT_TABLE_RELATION_LIST,
  URL_GET_VIEW_RELATION_LIST,
  URL_EXPORT_VIEW_RELATION_LIST,
  URL_GET_UDF_RELATION_LIST,
  URL_EXPORT_UDF_RELATION_LIST,
  URL_GET_FUNC_UDF_RELATION_LIST,
  URL_GET_FUNC_JOB_RELATION_LIST,
  URL_GET_FUNC_CEP_RELATION_LIST
} from '@/apis/commonApi';
import { get, post } from '@/apis/utils/net';
import moment from 'moment';
@Component
export default class ReferenceRelation extends Vue {
  // 弹窗topic名称
  @Prop({ default: '' }) topicName!: string;
  // 引用类型列表
  @Prop({ default: () => null }) relationTypeList!: object[];
  // 表/视图/topic id
  @Prop({ default: '' }) id!: string;
  // 引用类型
  @Prop({ default: 'JOB' }) type!: string;
  // 应用场景（视图/表/topic/udf）
  @Prop({ default: '' }) relation!: string;
  @Prop({ default: false }) isFunc!: boolean;
  search = this.id;
  pageData = { pageSize: 20, currentPage: 1, total: 0 };
  selectType = '';
  tableData = [];
  columnData = [];
  realtionListLoading = false;
  // 引用关系列表接口对象映射
  getRelationListApiMap = {
    table: URL_GET_TABLE_RELATION_LIST,
    udf: URL_GET_UDF_RELATION_LIST,
    view: URL_GET_VIEW_RELATION_LIST,
    UDF: URL_GET_FUNC_UDF_RELATION_LIST,
    JOB: URL_GET_FUNC_JOB_RELATION_LIST,
    CEP: URL_GET_FUNC_CEP_RELATION_LIST
  };
  // 引用关系下载接口对象映射
  exportRelationListApiMap = {
    table: URL_EXPORT_TABLE_RELATION_LIST,
    udf: URL_EXPORT_UDF_RELATION_LIST,
    view: URL_EXPORT_VIEW_RELATION_LIST
  };
  key = Date.now();
  get relationParam() {
    return ['udf', 'view', 'UDF', 'JOB', 'CEP'].includes(this.relation)
      ? ''
      : `/${this.selectType}`;
  }

  created() {
    this.selectType = this.relation;
    this.getRelationList();
  }

  // 获取引用关系列表
  async getRelationList() {
    await this.$emit('change', this.selectType);
    this.realtionListLoading = true;
    const { pageSize, currentPage, total } = this.pageData;
    const params = {
      search: this.search,
      pageData: {
        pageSize,
        currentPage,
        total
      }
    };
    const api = this.getRelationListApiMap[this.relation];
    const { data } = await post(`${api}${this.relationParam}`, params);
    data.tableData.sort(this.sortCreateTime('createTime'));
    data.tableData.sort(this.sortCreateTime('updateTime'));
    data.tableData.forEach((el) => {
      el.createTime = moment(el.createTime).format('YYYY-MM-DD HH:mm:ss');
      el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
    });
    this.tableData = data.tableData;
    this.columnData = data.columnData;
    this.pageData.total = data.pageData.total;
    this.key = Date.now();
    this.realtionListLoading = false;
  }

  // 根据创建时间进行排序
  sortCreateTime(property) {
    return (a, b) => {
      const value1 = a[property];
      const value2 = b[property];
      return value2 - value1;
    };
  }

  // 监听分页变换
  handleListPageChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    this.getRelationList();
  }

  // 导出引用关系(excel格式)
  async exportRelationList() {
    const api = this.exportRelationListApiMap[this.relation];
    const res: any = await get(`${api}${this.relationParam}?id=${this.id}`, null, {
      responseType: 'blob'
    });
    if (res.blob) {
      const { blob, fileName } = res;
      const url = window.URL.createObjectURL(new Blob([blob]));
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      return;
    }
  }
}
</script>

<style lang="scss" scoped>
.is-func {
  float: left;
  width: 100%;
  margin-top: -28px;
}
.reference-relation {
  background: #fff;
  &__dialog {
    &--select {
      margin-left: 30px;
    }
  }
  &__content {
    width: 100%;
  }
  &__select {
    float: right;
    position: relative;
    bottom: 45px;
    margin: 0 156px;
    // margin-left: 62%;
  }
  &__icon {
    margin-left: 15px;
    font-size: 18px;
    cursor: pointer;
  }
  &__slot {
    &--select {
      display: inline-block;
      margin-left: 10px;
    }
  }
  .operation {
    display: flex;
    height: 62px;
    align-items: center;
    justify-content: flex-end;
    padding: 20px;
  }
}
</style>
