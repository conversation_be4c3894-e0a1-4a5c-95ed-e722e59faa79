<template>
  <pro-grid>
    <!-- 图形 -->
    <pie-chart
      :title="$t('pa.graph')"
      :loading="loading"
      :pie-data="pieData"
      :name="isAS ? 'AS' : 'CubeBase'"
      @refresh="getCacheInfo"
    />
    <!--节点信息 -->
    <table-block :title="$t('pa.nodeInfo')" :loading="loading" :table-data="nodeInfoData" :column-data="nodeInfoColumns" />
    <!-- namespace信息 -->
    <table-block
      :title="$t('pa.namespaceInfo')"
      :loading="loading"
      :table-data="nameSpaceData"
      :column-data="nameSpaceColumns"
    />
  </pro-grid>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getServerCacheInfo } from '@/apis/serviceApi';
import { safeArray } from '@/utils';

@Component({
  components: {
    PieChart: () => import('../components/pie-chart.vue'),
    TableBlock: () => import('../components/table-block.vue')
  }
})
export default class CacheInfo extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: () => ({}) }) params!: any;

  loading = false;
  id = '';
  resType = '';
  pieData: any[] = [];
  nodeInfoData: any[] = [];
  nameSpaceData: any[] = [];

  get isAS() {
    return this.data?.resType === 'AEROSPIKE';
  }
  get type() {
    return this.isAS ? 'aerospike' : 'cubebase';
  }
  get nodeInfoColumns() {
    return [
      {
        label: this.$t('pa.hostName'),
        value: 'host',
        width: 180
      },
      {
        label: this.$t('pa.port'),
        value: 'port'
      },
      {
        label: this.$t('pa.objectCount'),
        value: 'masterObjects',
        width: 120
      },
      this.isAS && {
        label: this.$t('pa.evictedObject'),
        value: 'evictedObjects'
      },
      {
        label: this.$t('pa.expiredObject'),
        value: 'expiredObjects'
      },
      {
        label: this.$t('pa.totalMemory'),
        value: 'memoryTotalDes'
      },
      {
        label: this.$t('pa.memoryUsed'),
        value: 'memoryUsedDes'
      },
      {
        label: this.$t('pa.memoryRemain'),
        value: 'memoryRemainDes'
      },
      this.isAS && [
        {
          label: this.$t('pa.read'),
          value: 'readCount'
        },
        {
          label: this.$t('pa.readError'),
          value: 'readErrorCount'
        },
        {
          label: this.$t('pa.write'),
          value: 'writeCount'
        },
        {
          label: this.$t('pa.writeError'),
          value: 'writeErrorCount'
        }
      ]
    ]
      .flat(2)
      .filter(Boolean);
  }
  get nameSpaceColumns() {
    return [{ label: this.$t('pa.namespace'), value: 'host' }, ...this.nodeInfoColumns.slice(2)];
  }

  created() {
    this.id = this.$route.query.id as string;
    this.resType = this.data?.resType;
    this.getCacheInfo();
  }
  async getCacheInfo() {
    try {
      this.loading = true;
      const { success, data, error } = await getServerCacheInfo(this.type, this.id);
      if (!success) return this.$message.error(error);
      this.pieData = [
        {
          name: this.$t('pa.used'),
          value: data.memoryUsed,
          des: data.memoryUsedDes,
          total: data.memoryTotalDes
        },
        {
          name: this.$t('pa.unused'),
          value: data.memoryRemain,
          des: data.memoryRemainDes,
          total: data.memoryTotalDes
        }
      ];
      this.nodeInfoData = safeArray(data?.allNodes);
      this.nameSpaceData = safeArray(data?.allNamespace);
    } finally {
      this.loading = false;
    }
  }
}
</script>
