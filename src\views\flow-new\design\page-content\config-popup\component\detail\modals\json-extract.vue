<template>
  <bs-dialog :title="title" :visible.sync="visible" :before-close="closeDialog" size="large" append-to-body>
    <!-- form -->
    <el-form ref="formRef" inline :model="formData" :rules="formRule" :label-width="isEn ? '120px' : '80px'">
      <!-- 输入字段 -->
      <el-form-item :label="$t('pa.flow.inputField')" prop="inputField">
        <bs-select
          v-model="formData.inputField"
          clearable
          size="mini"
          :disabled="disabled"
          :options="fieldOptions"
          :placeholder="$t('pa.flow.placeholder34')"
        />
        <el-tooltip effect="light" placement="bottom" :content="$t('pa.flow.msg203')">
          <i class="iconfont icon-wenhao"></i>
        </el-tooltip>
      </el-form-item>
      <!-- 输出字段 -->
      <el-form-item :label="$t('pa.flow.outputFields')" prop="outField">
        <el-input
          v-model="formData.outField"
          size="mini"
          :disabled="disabled"
          :placeholder="$t('pa.flow.outputFields')"
          @blur="handleOutFieldBlur"
        />
      </el-form-item>
    </el-form>
    <!-- main -->
    <div class="json-extract-main">
      <!-- code -->
      <bs-code
        ref="codeRef"
        language="json"
        :value="tempJsonValue"
        :read-only="readOnly"
        :operatable="false"
        :extra-style="{ height: '100%' }"
        @change="handleCodeChange"
      />
      <!-- 转换 -->
      <div class="json-extract-transform" @click="handleTransform">
        <div v-if="!disabled">{{ $t('pa.flow.trans') }}</div>
        <i v-if="!disabled" class="el-icon-right"></i>
      </div>
      <!-- tree -->
      <el-tree
        show-checkbox
        node-key="label"
        :data="treeData"
        :props="defaultProps"
        :default-checked-keys="selectedKeys"
        :default-expanded-keys="expandedKeys"
        @check="handleCheckChange"
      >
        <span slot-scope="{ node, data }">
          {{ data.label && data.label.slice(data.label.lastIndexOf('.') + 1) }}
        </span>
      </el-tree>
    </div>
    <!-- footer -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">{{ $t('pa.action.cancel') }}</el-button>
      <el-button v-if="!disabled" type="primary" @click="handleFormat">{{ $t('pa.format') }}</el-button>
      <el-button v-if="!disabled" type="primary" @click="handleSubmit">{{ $t('pa.flow.confirm') }}</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Emit, Ref, Vue } from 'vue-property-decorator';
import { cloneDeep, safeArray, safeParse } from '@/utils';
import ElForm from 'bs-ui-pro/packages/form/index.js';
import { extractJson } from '@/apis/flowNewApi';

@Component
export default class JsonExtract extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop({ default: false }) disabled!: boolean;
  @Ref('formRef') readonly form!: ElForm;
  @Ref('codeRef') readonly code!: any;

  readOnly = false;
  fieldOptions: any[] = [];
  formData: any = { inputField: '', outField: '' };
  formRule: any = {
    inputField: [{ required: true, message: this.$t('pa.flow.placeholder34'), trigger: 'change' }],
    outField: [{ required: true, message: this.$t('pa.flow.msg204'), trigger: 'blur' }]
  };
  tempJsonValue = '';
  jsonValue = '';
  defaultProps: any = { children: 'children', label: 'label' };
  treeData: any[] = [];
  selectedKeys: any[] = [];
  expandedKeys: any[] = [];

  get title() {
    const { nodeName = '', componentName = '' } = this.data || {};
    return this.$t('pa.flow.msg288', [nodeName, componentName]);
  }

  created() {
    this.fieldOptions = safeArray(this.data.inputFields).map(({ name }) => ({ label: name, value: name }));
    this.readOnly = !!this.disabled;
    this.tempJsonValue = this.jsonValue = this.data.jsonValue || '';
    this.treeData = safeArray(this.data?.properties?.tree, [{}]);
    this.selectedKeys = safeArray(this.data?.properties?.selected);
    this.expandedKeys = this.selectedKeys.length > 0 ? [this.selectedKeys[0]] : [];
    this.formData.inputField = this.data?.properties?.inputField || '';
    this.formData.outField = this.data?.properties?.outputField || '';
  }

  handleCodeChange() {
    this.jsonValue = this.code.getValue();
  }
  async handleTransform() {
    if (!this.jsonValue) return this.$tip.error(this.$t('pa.flow.msg205'));
    this.selectedKeys = [];
    const { success, data, error } = await extractJson(this.jsonValue);
    if (!success) return this.$tip.error(error);
    this.treeData.splice(0, 1, safeParse(data, []));
  }
  handleCheckChange(currentNode, checkedNodes: any) {
    this.selectedKeys = checkedNodes.checkedKeys;
  }
  handleFormat() {
    this.code.format();
  }
  async handleSubmit() {
    await this.form.validate();
    const nodeDot = cloneDeep(this.data);
    nodeDot.properties = { tree: cloneDeep(this.treeData), selected: cloneDeep(this.selectedKeys) };
    const outputFields = nodeDot.inputFields.filter((it) => it.name === this.formData.inputField);
    if (outputFields.length > 0) {
      outputFields[1] = cloneDeep(outputFields[0]);
      outputFields[1]['name'] = this.formData.outField;
      outputFields[1]['outputable'] = true;
    }
    nodeDot.outputFields = outputFields;
    // 临时方案：前端模拟通用组件，构造ioProperties和properties相关字段
    nodeDot.ioProperties = `{"emitTime":0,"f0":["inputField"],"f1":["outputField"]}`;
    nodeDot.properties['inputField'] = this.formData.inputField;
    nodeDot.properties['outputField'] = this.formData.outField;
    this.closeDialog(true, nodeDot);
  }

  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    return { needUpdate, jobNode };
  }
  // 输出字段去空格
  handleOutFieldBlur() {
    this.formData.outField = (this.formData.outField || '').trim();
  }
}
</script>
<style lang="scss" scoped>
.icon-wenhao {
  margin-left: 10px;
  cursor: pointer;
}
.json-extract {
  &-main {
    display: flex;
    align-items: center;
    height: 456px;

    .bs-code,
    .el-tree {
      padding: 0;
      flex: 1;
      height: 456px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
    }
    .el-tree {
      overflow-y: auto;
    }
  }
  &-transform {
    width: 50px;
    color: #2196f3;
    cursor: pointer;
    text-align: center;
  }
}
</style>
