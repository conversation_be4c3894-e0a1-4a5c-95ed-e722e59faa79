<template>
  <bs-dialog
    :title="data.nodeName + '组件配置'"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    width="60%"
    append-to-body
  >
    <div class="extract-content">
      <el-form
        ref="ruleForm"
        :inline="true"
        :model="ruleForm"
        :rules="rules"
        label-position="left"
        label-width="80px"
        class="form-content"
      >
        <el-form-item label="输入字段" prop="inputField" style="margin-right: 20px">
          <el-select
            v-model="ruleForm.inputField"
            placeholder="请填写输入字段"
            clearable
            size="mini"
            :disabled="disabled"
          >
            <el-option
              v-for="(field, index) in data.inputFields"
              :key="field.name + index"
              :label="field.name"
              :value="field.name"
            />
          </el-select>
          <el-tooltip effect="light" placement="bottom">
            <div slot="content">选择上个节点输出的字段，作为json字段提取组件的输出</div>
            <span class="iconfont icon-wenhao" style="marginleft: 5px"></span>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="输出字段" prop="outField">
          <el-input
            v-model="ruleForm.outField"
            placeholder="输出字段"
            size="mini"
            :disabled="disabled"
            @blur="handleOutFieldBlur"
          />
        </el-form-item>
      </el-form>
      <div class="json-content">
        <div class="code-content">
          <codemirror
            ref="myCm"
            class="code-mirror"
            :code="jsonValue"
            :options="options"
            :origin-code="jsonValue"
            @saveCode="saveCode"
          />
        </div>
        <div class="trans-content" @click="transferData">
          <div v-if="!disabled" class="trans-content__text">转换</div>
          <span v-if="!disabled"></span><i v-if="!disabled">></i>
        </div>
        <div class="tree-content">
          <el-tree
            :data="properties.tree"
            show-checkbox
            node-key="label"
            :props="defaultProps"
            :default-expanded-keys="expandedKeys"
            :default-checked-keys="properties.selected"
            @check="currentChange"
          >
            <span slot-scope="{ node, data }">
              {{ data.label && data.label.slice(data.label.lastIndexOf('.') + 1) }}
            </span>
          </el-tree>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">取消</el-button>
      <el-button v-if="!disabled" type="primary" @click="format">格式化</el-button>
      <el-button v-if="!disabled" type="primary" @click="submit('ruleForm')">确定</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Emit } from 'vue-property-decorator';
import { cloneDeep } from 'lodash';
import { PaBase } from '@/common/pipeace-base';
import { URL_EXTRACT_JSON } from '@/apis/commonApi';
@Component({
  components: {
    codemirror: () => import('@/components/codemirror/codemirror.vue')
  }
})
export default class JsonExtract extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop({ default: false }) disabled!: boolean;

  ruleForm: any = {
    inputField: '',
    outField: ''
  };
  rules: any = {
    inputField: [{ required: true, message: '请填写输入字段', trigger: 'change' }],
    outField: [{ required: true, message: '请填写输出字段', trigger: 'blur' }]
  };
  defaultProps: any = {
    children: 'children',
    label: 'label'
  };
  options: any = {
    mode: { name: 'javascript', json: true },
    readOnly: false,
    lineNumbers: true,
    line: true,
    indentUnit: 4,
    lineWrapping: true,
    foldGutter: true,
    gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter']
  };
  jsonValue = '';
  properties: any = {
    tree: [{}],
    selected: []
  };
  expandedKeys: any = [];

  created() {
    if (this.disabled) {
      this.options.readOnly = true;
      // this.$set(this.options, 'readOnly', true);
    }
    this.jsonValue = this.data.jsonValue || '';
    this.properties = this.data.properties || {
      tree: [{}],
      selected: []
    };
    this.expandedKeys =
      (this.properties.selected &&
        this.properties.selected.length > 0 && [this.properties.selected[0]]) ||
      [];

    this.ruleForm.inputField = (this.data.properties && this.data.properties.inputField) || '';
    this.ruleForm.outField = (this.data.properties && this.data.properties.outputField) || '';
  }

  saveCode(val) {
    this.jsonValue = val;
  }

  format() {
    const cmref: any = this.$refs.myCm;
    cmref.autoFormat();
  }

  async transferData() {
    if (!this.jsonValue) {
      this.$tip.error('请在左侧输入您要转换的json');
      return;
    }
    this.properties.selected = [];
    const param = {
      jsonValue: this.jsonValue
    };
    const result: any = await this.doPost(URL_EXTRACT_JSON, param);
    if (!result.success && result.msg) {
      this.$tip.error(result.msg);
    } else {
      this.properties.tree.splice(0, 1, JSON.parse(result.data));
    }
  }

  currentChange(currentNode, checkedNodes) {
    this.properties.selected = checkedNodes.checkedKeys;
  }

  submit(formName: any) {
    const nodeDot = cloneDeep(this.data);
    const formRef: any = this.$refs[formName];
    formRef.validate((valid) => {
      if (valid) {
        nodeDot.properties = this.properties;
        const outputFields = nodeDot.inputFields.filter(
          (item) => item.name === this.ruleForm.inputField
        );
        if (outputFields.length > 0) {
          outputFields[1] = cloneDeep(outputFields[0]);
          outputFields[1]['name'] = this.ruleForm.outField;
          outputFields[1]['outputable'] = true;
        }
        nodeDot.outputFields = outputFields;
        // 临时方案：前端模拟通用组件，构造ioProperties和properties相关字段
        nodeDot.ioProperties = `{"emitTime":0,"f0":["inputField"],"f1":["outputField"]}`;
        nodeDot.properties['inputField'] = this.ruleForm.inputField;
        nodeDot.properties['outputField'] = this.ruleForm.outField;
        this.closeDialog(true, nodeDot);
      }
    });
  }

  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    return { needUpdate, jobNode };
  }
  // 输出字段去空格
  handleOutFieldBlur() {
    this.ruleForm.outField = (this.ruleForm.outField || '').trim();
  }
}
</script>
<style lang="scss" scoped>
.extract-content {
  height: 520px;
  .form-content {
    text-align: left;
    width: 100%;
  }
  .json-content {
    height: 456px;
    display: flex;
    .code-content {
      height: 100%;
      text-align: left;
      flex: 1;
      max-width: 500px;
      .code-mirror {
        height: 100%;
      }
    }
    .trans-content {
      width: 50px;
      color: #2196f3;
      margin-top: 250px;
      cursor: pointer;
      &__text {
        margin-left: 10px;
      }
      span {
        display: inline-block;
        vertical-align: super;
        width: 25px;
        position: relative;
        height: 1px;
        left: 5px;
        margin-left: 3px;
        background: #2196f3;
      }
      i {
        position: relative;
        top: -2px;
        left: -3px;
      }
    }
    .tree-content {
      flex: 1;
      border: 1px solid #ccc;
      height: 456px;
      overflow: auto;
    }
  }
}
</style>
