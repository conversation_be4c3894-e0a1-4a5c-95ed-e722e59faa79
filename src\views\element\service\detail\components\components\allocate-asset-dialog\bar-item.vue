<template>
  <div class="bar-item__container">
    <el-form :model="formData" inline>
      <el-form-item v-for="it in renderList" :key="it.value" :label="it.label" :prop="it.value">
        <el-input-number v-model="formData[it.value]" :min="1" :precision="0" controls-position="right" />
        <el-button type="primary" @click="handleApply(it.value)">{{ $t('pa.action.applyAll') }}</el-button>
      </el-form-item>
    </el-form>
    <bs-search v-model="search" :placeholder="$t('pa.placeholder.orgNameFilter')" @search="$emit('search', search)" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, ModelSync, Vue } from 'vue-property-decorator';
@Component
export default class BarItem extends Vue {
  @ModelSync('value', 'input', { type: String }) search!: boolean;
  @Prop({ default: false }) isPerJob!: boolean;
  @Prop({ default: () => [] }) tableData!: any[];

  formData: any = { afterSlots: 1, afterMemory: 1, afterCpu: 1 };

  get renderList() {
    return [
      !this.isPerJob && {
        label: this.$t('pa.adjustSlotsTo'),
        value: 'afterSlots'
      },
      this.isPerJob && {
        label: this.$t('pa.adjustMemoryTo'),
        value: 'afterMemory'
      },
      this.isPerJob && {
        label: this.$t('pa.adjustCpuTo'),
        value: 'afterCpu'
      }
    ].filter(Boolean);
  }

  handleApply(type: string) {
    this.tableData.forEach((it) => this.$set(it, type, this.formData[type]));
  }
}
</script>
<style lang="scss" scoped>
.bar-item {
  &__container {
    display: flex;
    justify-content: space-between;
    align-content: center;
    ::v-deep .el-input-number {
      margin-right: 5px;
      width: 100px;
    }
  }
}
</style>
