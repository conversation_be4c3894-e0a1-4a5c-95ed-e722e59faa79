import {
  UPDATE_COLLAPSE,
  UPDATE_SPINSHOW,
  UPDATE_TITLE,
  RESIZE_CHARTS,
  SET_ENABLE_SQL,
  SET_FUSE_MODE
} from '@/store/event-names/mutations';
import { GET_CONFIG_DATA } from '@/store/event-names/actions';
import { URL_DEPLOY_CONFIG } from '@/apis/commonApi';
import { Message } from 'bs-ui-pro';
import Vue from 'vue';

const spinShow: boolean = false;
const isCollapse: boolean = false;
const isResize: boolean = false;
const tabTitles: any = {};
const enableSql: boolean = true;
const isFuseMode: boolean = false;

export default {
  state: () => ({ spinShow, isCollapse, tabTitles, isResize, enableSql, isFuseMode }),
  mutations: {
    [UPDATE_SPINSHOW](state: IState, payload: boolean) {
      state.spinShow = payload;
    },
    [RESIZE_CHARTS](state: IState, payload: boolean) {
      state.isResize = payload;
    },
    [UPDATE_COLLAPSE](state: IState, payload: any) {
      state.isCollapse = payload;
    },
    // 同步SQL开关状态
    [SET_ENABLE_SQL](state: IState, payload: any): void {
      state.enableSql = payload;
    },
    /* 流立方需要的方法，临时容错性处理 */
    [UPDATE_TITLE](state, { route, title, scriptType }) {
      // console.log('new tab');
    },
    [SET_FUSE_MODE](state: IState, payload: any): void {
      state.isFuseMode = payload;
    }
  },
  actions: {
    // 获取配置（SQL流程能力、是否融合）
    async [GET_CONFIG_DATA]({ commit }) {
      try {
        const { success, data, msg, error }: any = await Vue.axios.get(URL_DEPLOY_CONFIG);
        if (success) {
          commit(SET_ENABLE_SQL, !!data.sqlStatus);
          commit(SET_FUSE_MODE, !!data.cloudStatus);
          return;
        }
        Message.error(error || msg);
      } catch {
        Message.error('获取配置失败');
      }
    }
  },
  getters: {
    tabTitles: (state) => state.tabTitles
  }
};
