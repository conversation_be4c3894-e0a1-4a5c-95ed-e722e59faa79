<template>
  <div v-loading="loading">
    <pro-grid type="info" title="基本信息" class="info">
      <el-descriptions :colon="false" style="padding: 0 32px 0 32px">
        <el-descriptions-item label="模板名称">
          {{ data.basicInformation.patternName }}
        </el-descriptions-item>
        <el-descriptions-item label="备注">{{ data.basicInformation.memo }} </el-descriptions-item>
        <el-descriptions-item label="创建人">
          {{ data.basicInformation.createdBy }}
        </el-descriptions-item>
        <el-descriptions-item label="创建人手机号">
          {{ data.basicInformation.createdPhone }}
        </el-descriptions-item>
      </el-descriptions>
    </pro-grid>
    <pro-grid type="info" title="模式" class="info">
      <pro-grid :col-span="12" class="info-item">
        <span class="info-item__label">数据定义</span>
        <span class="info-item__content">{{ ddName }}</span>
      </pro-grid>
      <pro-grid
        v-for="(item, index) in data.singletonCepPatterns"
        :key="item.logicRelation + index"
        class="info-item info-pattern"
      >
        <span class="info-pattern__content">
          <span class="info-item__label">个体模式名称</span>
          <span class="info-item__content">{{ item.singletonModeName }}</span>
        </span>
        <span class="info-item__content">
          结果表达式：
          <el-tooltip v-hide="item.resultExpression" effect="light" placement="top">
            <span slot="content" v-html="item.resultExpression"></span>
            <span
              class="info-item__content__resultExpression"
              v-html="item.resultExpression"
            ></span>
          </el-tooltip>
        </span>
      </pro-grid>
    </pro-grid>
    <pro-grid
      v-for="pattern in data.groupCepPatterns"
      :key="pattern.groupName"
      type="info"
      title="模式组"
      class="info"
    >
      <el-descriptions :colon="false" style="padding: 0 32px 0 32px">
        <el-descriptions-item label="模式组名称"> {{ pattern.groupName }} </el-descriptions-item>
        <el-descriptions-item label="逻辑关系">
          {{ pattern.logicalRelationship }}
        </el-descriptions-item>
      </el-descriptions>
      <div class="info-table">
        <bs-table
          :data="pattern['groupCepPatternConditionList']"
          :column-data="columnData"
          :column-settings="false"
        >
          <template slot="cycles" slot-scope="{ row }">
            <span v-if="row.circularRuleTime === 'ONE_OR_MORE'">
              [ {{ row.leftInterval }} , {{ row.rightInterval }} ]
            </span>
            <span v-else>-</span>
          </template>
        </bs-table>
      </div>
    </pro-grid>
    <pro-grid type="info" title="模式序列" class="info">
      <el-button slot="operation" size="mini" @click="showRelationDialog = true">
        查看序列
      </el-button>
      <el-descriptions :colon="false" style="padding: 0 32px 0 32px">
        <el-descriptions-item label="时间间隔">
          {{ sequenceCondition.timeValue }}
        </el-descriptions-item>
        <el-descriptions-item label="跳过策略">
          {{ sequenceCondition.sequenceSkipStrategy }}
        </el-descriptions-item>
        <el-descriptions-item v-if="sequenceCondition.groupName" label="模式名称">
          {{ sequenceCondition.groupName }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :colon="false" style="padding: 0 32px 0 32px">
        <el-descriptions-item class="info-descriptions">
          <span slot="label" class="info-descriptions__label">逻辑关系</span>
          <div :key="key" class="info-descriptions__container">
            <el-tooltip v-hide effect="light" placement="top">
              <span slot="content" v-html="sequenceCondition.sequenceRelationship"></span>
              <span
                class="info-relationship"
                v-html="sequenceCondition.sequenceRelationship"
              ></span>
            </el-tooltip>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <div class="info-table">
        <bs-table
          :data="sequenceCondition['groupCepPatternConditionList']"
          :column-data="columnData"
          :column-settings="false"
        >
          <template slot="cycles" slot-scope="{ row }">
            [ {{ row.leftInterval }} , {{ row.rightInterval }} ]
          </template>
        </bs-table>
      </div>
    </pro-grid>
    <sequence-relation
      v-if="showRelationDialog"
      :show.sync="showRelationDialog"
      :data="data.sequenceCepPatterns"
      :group-data="data.groupCepPatterns"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { getInfo, getDdById, getHistoryById } from '@/apis/cep-api';
import { Dictionary, hide } from '../../util';
import SequenceRelation from '../../add-edit/model-sequence/sequence-relation/index.vue';
@Component({
  name: 'Info',
  components: {
    SequenceRelation
  },
  directives: { hide }
})
export default class Info extends Vue {
  @Prop({ type: String }) id!: string;
  @Prop({ type: String }) version!: string;
  private showRelationDialog = false;
  loading = true;
  data: any = {
    basicInformation: {},
    groupCepPatterns: [],
    sequenceCepPatterns: [],
    singletonCepPatterns: []
  };
  sequenceCondition: any = {};
  ddName = '';
  key = 0;
  columnData = [
    { label: '模式名称', value: 'modelOrGroupName' },
    { label: '循环规则', value: 'circularRuleTimes' },
    { label: '循环次数', value: 'cycles' },
    { label: '连接方式', value: 'connectionModeType' }
  ];

  mounted() {
    const timer = setTimeout(() => {
      this.key = Date.now();
      clearTimeout(timer);
    }, 500);
  }

  async created() {
    this.loading = true;
    const { data } = this.version ? await getHistoryById(this.id) : await getInfo(this.id);
    this.data = { ...data };
    //模式序列
    this.sequenceCondition = this.data.sequenceCepPatterns[0];
    // 模式序列：时间间隔
    this.sequenceCondition.timeValue =
      this.sequenceCondition.maximumAllowableTimeInterval.timeValue +
      Dictionary[this.sequenceCondition.maximumAllowableTimeInterval.timeValueType];
    //模式序列：跳过策略
    this.sequenceCondition.sequenceSkipStrategy = this.sequenceCondition.sequenceSkipStrategy;
    //模式序列：模式名称
    this.sequenceCondition.groupName = this.sequenceCondition.groupName;
    // 模式序列：逻辑关系
    this.sequenceCondition.sequenceRelationship = this.sequenceCondition.logicalRelationship;

    const {
      data: { name },
      success,
      msg
    } = await getDdById(this.data.ddId);

    if (success) {
      this.ddName = name;
    } else {
      this.$message(msg);
    }
    this.loading = false;
  }
}
</script>

<style lang="scss" scoped>
.info {
  margin-top: 20px;
  &-descriptions {
    &__label {
      width: 56px !important;
      color: #777;
    }
    &__container {
      width: 70vw;
    }
  }
  &-relationship {
    display: inline-block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    vertical-align: middle;
  }
  &-item {
    height: 60px;
    line-height: 60px;
    padding: 0 30px;
    margin-bottom: 10px;

    &__label {
      color: #777777;
      margin-right: 12px;
    }
    &__content {
      display: inline-block;
      width: 69%;
      color: #444444;
      &__resultExpression {
        display: inline-block;
        width: calc(100% - 95px);
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        vertical-align: middle;
      }
    }
  }
  &-pattern {
    ::v-deep .bs-pro-grid__content {
      background: #f9f9f9;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      padding: 0 30px;
    }
    &__content {
      display: inline-block;
      width: 30%;
    }
  }
  &-table {
    margin: 0 30px 10px;
    width: calc(100% - 60px);
  }
}
::v-deep .el-descriptions-item__label {
  color: #777777;
}
::v-deep .el-button--mini {
  padding-top: unset;
  padding-bottom: unset;
}
::v-deep .bs-pro-grid__content {
  padding-bottom: 15px;
}
</style>
