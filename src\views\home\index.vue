<template>
  <pro-page title="PA监控云盘" fixed-header>
    <bs-cascader
      ref="orgCascader"
      slot="operation"
      v-model="selectedOrg"
      :options="orgOptions"
      collapse-tags
      filterable
      :props="{ multiple: true, showAll: true, checkStrictly: true }"
      @change="getNodes"
      @remove-tag="removeTag"
    />
    <!-- 内容区域 -->
    <main class="home__content">
      <el-row
        v-for="list in contentList"
        :key="list.line"
        :gutter="list.gutter"
        :class="{ 'home__content--flow': list.isProcessingContent }"
      >
        <el-col v-for="item in list.content" :key="item.name" :span="item.width">
          <component :is="item.name" :org-id="selectOrgId" :screen-width="screenWidth" />
        </el-col>
      </el-row>
    </main>
    <span class="home__time">数据更新时间：{{ dataUpdateTime }}</span>
  </pro-page>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { getOrgList } from '@/apis/homeApi';
import dayjs from 'dayjs';
import Warning from './components/warning.vue';
import Engine from './components/engine.vue';
import FlowDistribution from './components/flow-distribution.vue';
import FlowProcessing from './components/flow-processing.vue';
import Service from './components/service.vue';
@Component({
  components: {
    Warning,
    Engine,
    FlowDistribution,
    FlowProcessing,
    Service
  },
  beforeRouteLeave(to, from, next) {
    this.$data.homeTop =
      (document.querySelector('.bs-pro-page__content-wrap') as HTMLElement).scrollTop || 0;
    next();
  }
})
export default class Home extends Vue {
  // 机构列表
  private orgList: object[] = [];
  private contentList = [
    {
      line: 1,
      gutter: 20,
      content: [
        {
          name: 'warning',
          width: 8
        },
        {
          name: 'engine',
          width: 8
        },
        {
          name: 'flow-distribution',
          width: 8
        }
      ]
    },
    {
      line: 2,
      isProcessingContent: true,
      content: [
        {
          name: 'flow-processing',
          width: 24
        }
      ]
    },
    {
      line: 3,
      content: [
        {
          name: 'service',
          width: 24
        }
      ]
    }
  ];
  // 选中机构名称
  private selectedOrg = [];
  private skeletonLoading = true;
  private orgOptions: object[] = [];
  private selectOrgId: any[] = [];
  private dataUpdateTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
  private homeTop = 0;
  private screenWidth = document.body.clientWidth;
  private removeTagList: any[] = [];
  mounted() {
    window.addEventListener('resize', this.handleResize);
  }
  activated() {
    (document.querySelector('.bs-pro-page__content-wrap') as HTMLElement).scrollTop =
      this.homeTop || 0;
  }
  created() {
    this.getOrgList();
  }
  handleResize() {
    this.screenWidth = document.body.clientWidth;
  }
  getNodes() {
    this.selectOrgId = (this.$refs['orgCascader'] as HTMLFormElement)
      .getCheckedNodes()
      .map((el) => el.value)
      .filter((el) => {
        if (this.removeTagList.length)
          return el.split('-')[0] !== 'BSUI' && !this.removeTagList.includes(el);
        return el.split('-')[0] !== 'BSUI';
      });
    if (!this.selectedOrg.length) this.selectOrgId = [];
    this.removeTagList = [];
  }
  removeTag(tag) {
    this.removeTagList.push(!Array.isArray(tag) ? tag : tag[tag.length - 1]);
    this.removeTagList = [...new Set(this.removeTagList)];
  }
  setOptions(data) {
    const { orgId, orgName, childrenInfo } = data;
    if (orgId && orgName) {
      data.value = orgId;
      data.label = orgName;
      delete data.orgId, data.orgName;
    }
    if (childrenInfo) {
      data.children = data.childrenInfo;
      delete data.childrenInfo;
      data.children.forEach((el) => {
        this.setOptions(el);
      });
    }
    return data;
  }

  // 获取当前用户所属机构列表
  async getOrgList() {
    const { orgId } = this.$store.state.userInfo;
    try {
      const { data } = await getOrgList(orgId);
      this.orgOptions = [this.setOptions(data)];
      // 初始选中所有option
      this.setChecked(this.selectedOrg, [], this.orgOptions);
      this.selectOrgId = [...new Set(this.selectedOrg.flat(Infinity))];
    } catch {
      this.orgOptions = [];
      this.selectedOrg = [];
      this.selectOrgId = [];
    }
  }

  setChecked(selectData, upArray, options) {
    options.forEach((item) => {
      const array = [...upArray, item.value];
      selectData.push(array);
      if (item.children) {
        this.setChecked(selectData, array, item.children);
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.home {
  &__content {
    padding: 20px 0;
    &--flow {
      padding: 18px 0;
    }
    ::v-deep .bs-detail-block {
      padding: 20px;
      height: 220px;
    }
  }
  &__time {
    float: right;
    padding-bottom: 10px;
  }
}
.page-content .content {
  border: unset;
  background-color: #f6f7f9;
}
.first_content {
  height: calc(100% - 50px);
  overflow: scroll;
}
::v-deep .el-scrollbar__bar.is-horizontal {
  display: none;
}
::v-deep .bs-pro-page__header-operation .el-cascader .el-input--suffix {
  min-width: 361px;
}
</style>
