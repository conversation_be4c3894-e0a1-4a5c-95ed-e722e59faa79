{"pa": {"AdvancedConnectorAttr": "Advanced Connector Attributes", "action": {"action": "Operation", "add": "Add", "addNewRow": "Add Row", "addRow": "Add Row", "allSelect": "Select All", "analysis": "Analyze", "applyAll": "Apply All", "batchAdjust": "<PERSON><PERSON>", "batchDel": "<PERSON><PERSON> Delete", "batchUAdd": "<PERSON><PERSON> Add", "buildOneself": "Self-Built", "cancel": "Cancel", "cancelAll": "Cancel All", "close": "Close", "confirm": "Confirm", "copy": "Copy", "cover": "Overwrite", "create": "Create", "dataClear": "Clear Invalid Data", "del": "Delete", "detail": "Detail", "download": "Download", "downloadRelation": "Download Reference", "edit": "Edit", "editCustom": "Edit [{0}]", "editIcon": "Edit Icon", "editIconCustom": "Edit Icon [{0}]", "enable": "Enable", "execute": "Execute", "export": "Export", "import": "Import", "makeSure": "Confirm", "mockSubmit": "<PERSON><PERSON>mit", "offline": "Stop", "online": "Start", "output": "Output", "preview": "Preview", "recycle": "Recycle", "refresh": "Refresh", "register": "Register", "resourceAllocate": "Resource Allocation", "rollBack": "Rollback", "run": "Run", "save": "Save", "search": "Search", "selectAll": "Select All", "share": "Share", "skip": "<PERSON><PERSON>", "staging": "Temporary Save", "submit": "Submit", "syncTopic": "Sync Cluster Topic", "terminal": "Terminal", "test": "Test", "testConnect": "Test Connection", "tip": "Tip", "transfer": "Transfer", "update": "Update", "upload": "Upload", "uploadFiles": "Upload Files", "versionContrast": "Version Comparison", "view": "View", "viewSourceCode": "View Source Code", "viewVersion": "View History Versions"}, "addOrg": "Create Organization", "adjustCpu": "Adjust CPU", "adjustCpuTo": "Adjust CPU to:", "adjustMemoryMB": "Adjust Memory (MB)", "adjustMemoryTo": "Adjust Memory to:", "adjustSlots": "Adjust Slots", "adjustSlotsTo": "Adjust Slots to:", "advancedParams": "Advanced Parameters", "all": "All", "allOrg": "All Organizations", "allQueue": "All Queues", "argument": "Parameter", "argumentType": "Parameter Type", "assets": {"title1": "Data Management", "title2": "Data Definition", "title3": "Third-Party Libraries", "title4": "Method", "title5": "Import/Export"}, "attempts": "Number of Attempts", "backoffMultiplier": "Growth Multiplier", "baseConnectorAttr": "Basic Connector Attributes", "baseInfo": "Basic Information", "baseInformation": "Basic Information", "basicParams": "Basic Parameters", "basisChange": "Any of the following conditions are considered as changes: 1. The configured data definition does not exist in the platform; 2. The configured data definition has inconsistent content with the same full-class-name data definition in the platform.", "batch": "<PERSON><PERSON>", "batchText0": "Are you sure to delete the selected [{name}] {num} records?", "batchText1": "The selected [{name}] {num} records have been associated and cannot be deleted! [{name}] {num} records are not associated. Are you sure to delete them?", "batchText2": "The selected [{name}] {num} records have been associated and cannot be deleted!", "blood": {"center": "Center", "dep": "Dependent-upstream", "dynamic": "Dynamic", "exitFullScreen": "Exit Full Screen", "fit": "<PERSON><PERSON>", "fullScreen": "Full Screen", "homologyInfo": "Homologous Information", "homologyNode": "Homologous Node", "nodeName": "Node Name", "nodeType": "Node Type", "queryNode": "Query Node", "relatedInfo": "Related Information", "searchblood": "Search Blood Relationship", "serviceType": "server Type", "size": "Actual Size", "tip1": "Please select a query node on the left. The blood relationship will be explored with the query node as the center.", "tip10": "The nodes in the blood relationship are different, but the actual physical cluster service addresses + resources (e.g., Kafka service IP:port+topic) are the same or intersect.", "tip11": "No data permissions are available to view.", "tip12": "The current node has formed a cyclic relationship with the query node and cannot be contracted.", "tip13": "Click to view node details", "tip14": ", double-click to set this node as the query node", "tip2": "No data permissions are available to query the blood relationship of this node.", "tip3": "There is a cycle in the canvas nodes with the query node. The directly related nodes on the cycle have been automatically expanded.", "tip4": "Static service (configure the service with a specific resource): Please enter the name in the resource input box;", "tip5": "Upstream-dependent service (configure the service using upstream field values to specify a specific resource): Please enter the keyword 'dependent-on-upstream-data' in the resource input box;", "tip6": "Resourceless service (configure the service without specifying a specific resource): Please enter the keyword 'none' in the resource input box.", "tip7": "Support querying with published flows, tables, catalogs, and some service resources (static, upstream-dependent, and resourceless). Dynamic services are not supported for query. Service query tips:", "tip8": "Support querying with published flows and some service resources (static, upstream-dependent, and resourceless). Dynamic services are not supported for query. Service query tips:", "tip9": "The query conditions are incomplete. Please check."}, "cacheInfo": "<PERSON><PERSON>", "cacheQuery": "<PERSON><PERSON>", "canNotSync": "Cannot synchronize,", "cataLog": {"test6": "Catalog Details", "text1": "Catalog name", "text2": "Please enter a catalog name", "text3": "Catalog names must start with a letter and contain only letters, numbers, and underscores", "text5": "Catalog Management"}, "catalogName": "Catalog Name", "checkLoading": "Validating data, please wait...", "checkpointInterval": "Checkpoint Interval", "choseFile": "Select File", "citationRelation": "Reference Relationship", "closing": "Closing", "cloudService": "Cloud server", "cluster": "Cluster", "clusterAddress": "Cluster Address", "clusterMemory": "Cluster Total Memory", "clusterName": "Cluster Name", "clusterRemainingMemory": "Cluster Remaining Memory", "clusterResidualCpu": "Cluster Remaining CPU", "clusterResidualMemory": "Cluster Remaining Memory", "clusterResidualSlots": "Cluster Remaining Slots", "clusterResourceUsage": "Cluster Resource Usage", "clusterSlots": "Cluster Total Slots", "clusterUsedCpu": "Cluster Used CPU", "clusterUsedMemory": "Cluster Used Memory", "clusterUsedSlots": "Cluster Used Slots", "codeCompare": "Source Code Comparison", "codeOrName": "Enter code or name", "comments": "Please enter a description", "company": "Bangsheng Technology", "compareVersion": "Comparison Version: {0}", "componentLibMgr": "Component Management", "confirmDialog": "Field [{0}] has a corresponding input field but is not selected. Clicking Confirm will discard the unselected configuration. Do you want to continue submitting?", "consume": "Consumption View", "copies": "Replica Count", "cpFailed": "Tolerable Checkpoint Failures", "cpMinPause": "Minimum Pause Time for Checkpoints", "cpTimeout": "Checkpoint Timeout", "cpUnaligned": "Enable Unaligned Checkpoints", "cpuCores": "CPU Cores", "creator": "Creator", "cronExpression": "Cron Expression", "currentCpu": "Current Remaining CPU", "currentMemory": "Current Remaining Memory (MB)", "currentSlots": "Current Remaining Slots", "customFields": "Custom Fields", "customParams": "Custom Parameters", "data": {"baseFields": "Basic Fields", "codeView": "Source Code View", "copyError": "Co<PERSON> failed", "copySuccess": "Copy successful", "option": {"addOption": "Create Option", "optionName": "Option Name", "optionType": "Option Type", "placeholder": {"optionNamePlaceholder": "Please enter an option name", "optionTypePlaceholder": "Please select an option type"}, "validateTip": "Cannot start with number"}, "seniorFields": "Advanced Fields", "table": {"addTable": "Create Table", "change": "Change", "copyTable": "Copy Table", "detail": {"addAttribute": "Add Attribute", "addField": "Add Field", "attribute": "Attribute", "baseConnector": "Basic Connector", "businessCaliber": "Business Caliber", "chineseName": "Chinese Name", "columnCluster": "Column Family", "connector": "Connector", "connectorNature": "Connector Attributes", "creatorPhone": "Creator's Phone", "databaseType": "Database Type", "excess3": "More than 3", "fieldClassify": "Field Classification", "fieldName": "Field Name", "fieldType": "Field Type", "lessThan2": "Less than 2", "partition": "Partition", "placeholder": {"fieldPlaceholder1": "Please enter a field name", "fieldPlaceholder2": "Please add a field of type 'TIMESTAMP(3)' in the basic fields table", "servicePlaceholder1": "Please select a server", "servicePlaceholder2": "Please select the database type", "tablePlaceholder1": "Please select a prefix", "tablePlaceholder2": "Please enter a table name", "tablePlaceholder3": "Please select a suffix", "tablePlaceholder4": "Please enter a business caliber"}, "primaryKey": "Primary Key", "seniorConnector": "Advanced Connector", "serviceAddress": "Service URL", "serviceInfo": "Service Information", "serviceType": "Service Type", "sqlParsing": "SQL Parsing", "sqlPreview": "SQL Preview", "sync": "Synchronize", "tableFields": "Table Fields", "tableInfo": "Table Information", "tableName": "Table Name", "template": "Template", "tips": {"delConfirm": "Are you sure to delete the {0} field?", "fieldRFepeat": "There are {0} duplicate fields in the list. Please delete unnecessary fields.", "hbaseTip1": "For HBase server type: Only one primary key is allowed in the table fields.", "hbaseTip2": "For HBase server type: A primary key must be set in the table fields.", "hbaseTip3": "For HBase server type: Except for the primary key row fields, other fields must have a column family set.", "hbaseTip4": "Field information is required. Please complete the field information and try again.", "leastOne": "At least one field must be filled in either the field information or the advanced table fields.", "linkerTip": "Please fill in the connector value.", "needToDel": "Please select the field to delete.", "noPermissions": "You do not have permission to access this {0} data. Please select again.", "noServePermissions": "You do not have permission to access this server data. Please select again.", "notAllowedTip": "Fields are not allowed to be {0}.", "notEmpty": "{0} cannot be empty", "repeat": "Duplicate fields exist. Please modify before submission.", "repeatTips": "Duplicate fields exist. Please modify.", "seniorLinkerTip1": "Please fill in the attributes for the advanced connector.", "seniorLinkerTip2": "The attribute values for the advanced connector cannot be repeated. Please try again.", "syncSuccess": "{0} fields synchronized successfully.", "templateTooltip": "Templates for different {0} types may vary. Please download and use according to the {1} type.", "type2": "Upload files cannot exceed 10MB."}, "value": "Value", "zipPackage": "ZIP Package"}, "editTable": "Edit Table", "incomplete": "Incomplete", "placeholder": {"inputPlaceholder1": "Enter table name (Chinese name)", "inputPlaceholder2": "Enter field name (Chinese name)", "inputPlaceholder3": "Please select a watermark field name", "inputPlaceholder4": "Please select a watermark value", "inputPlaceholder5": "Please select a watermark unit", "inputPlaceholder6": "Please enter a processing time field name", "inputPlaceholder7": "Please enter a custom field name", "inputPlaceholder8": "Advanced fields cannot be empty", "select": "Select server type"}, "tooltip": {"tooltip1": "The table structure has changed. Please synchronize in time.", "tooltip2": "Flink built-in fields, such as processing time and watermark.", "tooltip3": "Advanced attributes configuration for connectors."}}, "text1": "Reference Count", "text10": "Only Excel files are allowed!", "text11": "Reason for non-import", "text12": "Please upload a zip file", "text13": "Click to import", "text14": "Resource Export", "text15": "Resource Import/Export", "text16": "Resource Import", "text17": "Incrementally import the data from the exported resource files into the platform.", "text18": "The following assets should be prepared before performing the import operation to improve import efficiency:", "text19": "1. Organization, user, role, and other data should be imported through SQL or recreated in the import environment.", "text2": "Number of downstream assets referencing this asset", "text20": "2. Services should be registered in the import environment before importing (it is recommended to keep the service type and name consistent for automatic matching by the system).", "text21": "Click to export", "text22": "3. Flow components should be uploaded and downloaded through [", "text23": "].", "text24": "4. Methods, third-party libraries, data definitions, and other underlying assets should be imported/exported through [", "text25": "Data Management - Import/Export", "text26": "].", "text27": "Export flows (including associated projects and directories), options, UDFs, tables, catalogs, SQL fragments, and templates from the platform.", "text28": "Export flows (including associated projects and directories), options, and templates from the platform.", "text29": "Flow Prefix", "text3": "Are you sure to delete the {0} field?", "text30": "Flow Suffix", "text31": "Directory", "text32": "Please select an asset type", "text33": "Search by ID or name", "text34": "Non-importable Check", "text35": "Duplicate Check", "text36": "Next Step", "text37": "Previous Step", "text4": "Files larger than 10MB are not allowed!", "text5": "Step 2: Upload Template File", "text7": "Templates for different server types may vary. Please download and use according to the server type.", "text8": "1. File format ZIP\n         2. Size cannot exceed 10MB", "text9": "Please upload a file", "udf": {"addUdf": "Create UDF", "detail": {"SyncAll": "Synchronize All", "addMode": "Creation Method", "addPhone": "Creator's Phone", "check": "Check", "correlationType": "Associated Method", "downloadFile": "Download Template File", "explain": "Function Description", "fieldSync": "Field Synchronization", "individualTest": "Individual Method Test", "manualWriting": "Manual Writing", "methodName": "Method Name", "mockTest": "Mock Environment Test", "param1": "Parameter 1", "param2": "Parameter 2", "placeholder": {"addTypePlaceholder": "Please select a creation method", "domainPlaceholder": "Please enter a domain name", "explainPlaceholder": "1. Input parameter description 2. Description 3. Function description", "methodNamePlaceholder": "Please enter a method name for search", "name": "Please enter a UDF name", "typeInputPlaceholder": "Please enter a type", "typePlaceholder": "Please select a type", "updataPlaceholder": "Please enter update content"}, "returnType": "Return Type", "savePrompt": "Save Prompt", "selectFile": "Select File", "testData": "Test Data", "tips": {"StagingSuccess": "Staging successful", "addSuccess": "Created successfully", "codeTip": "Source code cannot be empty.", "delConfirm": "Are you sure to delete this row?", "dominTip": "Please enter in English.", "downloadSteps1": "Step 1: Download the template file", "importCoverTip": "Importing will overwrite the previous data. Do you want to overwrite it?", "importTip": "Note: Importing will clear the original data. Please be cautious!", "nameTip": "UDF names must start with a letter and contain only letters, numbers, and underscores.", "numberOfTests": "Test data cannot exceed 5 items.", "onTests": "At least one row of test data must be retained.", "sourceTip": "Associated methods are added as static methods in the DefaultGlobalFunction class. Users can use them in the UDF source code in the format DefaultGlobalFunction.associatedMethodName(parameters).", "zipTip": "UDFs uploaded as ZIP packages need to be submitted before testing!"}, "type": "Type", "udfDomainName": "UDF Domain Name", "udfName": "UDF Name", "uploadZip": "Upload ZIP Package"}, "editUdf": "Edit UDF", "placeholder": {"udfExplainPlaceholder": "Please enter a function description", "udfNamePlaceholder": "Please enter a UDF name (Chinese name)", "udfPlaceholder": "Please select a UDF type"}, "submit": "Submitted", "viewUdf": "View UDF"}}, "dataDict": "Data Dictionary", "dataPreview": "Data Preview", "dataViewMode": "Data Viewing Mode", "databaseName": "Database Name", "defaultCode": "Query Statement ({0})", "delData": "Please select the data to delete", "delay": "Fixed Restart Delay", "deployTimeout": "Deployment Timeout", "differentEncoding": "Encoding cannot be the same as the parent encoding", "disableOperatorChain": "Disable Operator Chaining", "docs": "Documentation", "downloadFailed": "Download failed", "dsFlow": "Datestream Flow", "editCategory": "Edit Category", "elementLoading": "Loading configuration...", "enableCheckPoint": "Enable Checkpoints", "encoding": "Encoding", "encodingPlaceholder": "Enter encoding", "errorDetail": "Error details", "evictedObject": "Evicted Objects", "executionLog": "Execution Log", "expiredObject": "Expired Objects", "failureRateDelay": "Failure <PERSON><PERSON>", "failureRateInterval": "Time Interval", "failuresPerInterval": "Number of Restarts", "fieldConfig": "Field Configuration", "fieldInfo": "Field Information", "fieldName": "Field Name", "fieldParams": "Field Parameters", "fieldType": "Field Type", "fields": "Fields", "file": "File", "filterConditionList": "Filter Condition List", "flink": "Processing Engine", "flow": {"3mm": "Last Three Months", "add": "Add", "addCondition": "Add Condition", "addData": "Add Data", "addNextRow": "Add Next Row", "addTest": "Add Test Case", "address": "Address", "all": "All", "allClose": "Close All", "allOpen": "Open All", "attr": "Attribute", "autoSaved": "Auto-saved", "autoSaving": "Auto-saving", "autoUpdateField": "Auto-update Field", "baseConnector": "Basic Connector", "baseInfo": "Basic Information", "baseInfo1": "Basic Information", "baseParam": "Basic Parameters", "batch": "Batch Operation", "batchCancel": "Cancel Batch", "batchCancelConfirm": "Are you sure you want to cancel this batch operation?", "batchCancelFailed": "Cancel failed，The task has started running.", "batchCancelPublish": "Batch Cancel Publish", "batchCancelSuccess": "Cancel successful", "batchKill": "Force Stop", "batchKillConfirm": "Are you sure you want to force stop this batch operation?", "batchKillFailed": "Force stop failed", "batchKillSuccess": "Force stop successful", "batchMode": "Batch Mode", "batchOffline": "Batch Offline", "batchOnline": "Batch Online", "batchOperation": "Batch Operation", "batchOperationDetail": "Batch Operation Detail", "batchOperationFailed": "Batch operation failed to add", "batchOperationInfo": "Batch task", "batchOperationName": "Batch Name", "batchOperationNameLength": "Length should be 1 to 50 characters", "batchOperationNamePlaceholder": "Please enter batch operation name", "batchOperationNameRequired": "Please enter batch operation name", "batchOperationSuccess": "Batch operation added successfully, click the batch task button to view", "batchPublish": "Batch Publish", "batchRestart": "Batch restart", "batchSize": "<PERSON><PERSON> Si<PERSON>", "bingxingdu": "Parallelism", "bloodRelation": "Blood Relationship", "business": "Business Caliber", "cFlow": "Copy Flow", "cancel": "Cancel", "cancelAll": "Cancel All", "cancelPublish": "Cancel Publish", "canclePublish": "Cancelling Publication", "canvas": "<PERSON><PERSON>", "category": "Category", "chineseName": "Chinese Name", "className": "Full Class Name", "close": "Close", "clusterTest": "Test Cluster", "clusterType": "Cluster Type", "code": "Source Code", "codeKey": "Fragment Abbreviation", "codeLibrary": "Code Fragment Library", "codeName": "Fragment Name", "comConfig": "Component Configuration", "comInfo": "Component Information", "comLoading": "Loading Components", "commonConfig": "Common Configuration", "compile": "Compile", "complier": "Compiling", "component": "Flow Component", "component1": "Component", "componentName": "Component Name", "componentNum": "Number of Components", "componentType": "Component Type", "condition": "Condition", "config1": "Configuration", "config2": "Condition Configuration", "config3": "Configure Conditions", "config4": "Other Configuration", "configDetail": "Configuration Details", "configErr": "Configuration Error", "configMsg1": "The configuration of", "configMsg2": "is incomplete!", "configMsg3": "The field", "configMsg4": "is not configured!", "configured": "Already configured", "confirm": "Confirm", "connector": "Connector", "connectorAttr": "Connector Attributes", "connectorVal": "Attribute Value", "copy": "Copy", "copyFlow": "Copy Flow", "copyed": "<PERSON>pied", "creatFlow": "Create Flow", "createDir": "Create Directory", "createOrg": "Create Organization", "createProject": "Create Project", "createRow": "Add Row", "createSubDir": "Create Subdirectory", "createTime": "Creation Time", "creater": "Creator", "csvTrans": "CSV Parsing", "currentVersion": "Current Version", "customCron": "Custom: {0}", "customField": "Custom Fields", "customSize": "Custom JAR Package", "d": "Day", "dDir": "Delete Directory", "dataMap": "Data Map", "deadline": "Deadline", "defaultMethod": "Default Method", "del": "Delete", "delFlow": "Delete Flow", "delMsg": "Confirm deletion?", "delMsg1": "Are you sure to delete this row of data?", "delMsg2": "Are you sure you want to delete the selected data?", "delProject": "Delete Project", "deng": "Wait", "design": "Flow Design", "detail": "View Details", "dev": "Development", "eDir": "Edit Directory", "eFlow": "Edit Flow", "edit": "Edit", "editContent": "Editing Area", "editProject": "Edit Project", "editSql": "Edit SQL Fragment", "editSuccess": "Modified successfully", "editTime": "Modification Time", "editor": "Modifier", "endDate": "End Date", "endSession": "Terminate Session", "errorMessage": "Error Message", "exit": "Exit Batch", "export": "Export", "exportLog": "Export Log", "fDir": "Parent Directory", "fenqu": "Partition", "field": "Field", "fieldConfig": "Field Configuration", "fieldInfo": "Field Information", "fieldMap": "Field Mapping", "fieldMapTitle": "Field Mapping Configuration", "fieldName": "Field Name", "fieldName1": "Field Name", "fieldParam": "Field Parameters", "fieldTrans": "Convert fields", "fieldTransMethod": "Convert fields Method", "fieldType": "Field Type", "fieldTypeMap": "Field Type Mapping", "fieldUpdateTip": "Field Auto-update Prompt", "file": "File Collection", "filter": "Filter", "filter1": "Flow Filter", "flow": "Flow", "flowMemo": "Flow Notes", "flowName": "Flow Name", "flowRelation": "Referenced Flow:", "flowRunInfo": "Flow Runtime Information", "flowTest": "Flow Test", "forceStop": "Force Stop", "format": "YYYY-MM-DD HH:mm", "format1": "YYYY-MM-DD", "from": "From", "gen": "Generate", "genSql": "Generate SQL from Table Fields", "getBatchDetailFailed": "Failed to get batch operation details", "getBatchInfoFailed": "Failed to get batch operation information", "h": "Hour", "h1": "Hour", "highParam": "Advanced Parameters", "hignConnector": "Advanced Connector", "hignLevel": "Advanced", "hignMode": "Advanced Mode", "historyRun": "Historical Runs", "historyRun1": "Historical Run", "hiveConfig": "HIVE Configuration Parameters", "http": "HTTP Input", "indexField": "Index Field", "indicatorId": "Metric ID", "indicatorName": "Metric Name", "inputCompent": "Input Component", "inputField": "Input Field", "jarPkg": "Flow JAR Package", "jdbcType": "Database Type", "jsonTtrans": "JSON Transformation", "key": "Key", "key1": "Left Stream Key Field", "key10": "Computing Engine", "key11": "Policy", "key12": "Dimension Value", "key13": "Push Field", "key14": "Parameter Name", "key15": "Parameter Value", "key16": "Timing Mode", "key17": "Table Matching Method", "key18": "Specify Unique Table", "key19": "Dynamic Table", "key2": "Left Stream Output Field", "key20": "Dynamic Table Name", "key21": "Whether UPSERT", "key22": "Update Fields", "key23": "<PERSON><PERSON> Si<PERSON>", "key3": "Right Stream Key Field", "key4": "Right Stream Output Field", "key5": "Partition Field", "key6": "HIVE Field", "key7": "HIVE Field Type", "key8": "Upstream Input Field Type", "key9": "Upstream Input Field", "keyBy": "KeyBy <PERSON>", "label1": "All '&&' Relationships", "label10": "Event Time", "label11": "Out-of-order Time", "label12": "Filter <PERSON>late", "label13": "Update Frequency", "label14": "Mapping Template", "label15": "System Number", "label16": "Mapping Field", "label17": "Source Code Value", "label18": "Source Code Meaning", "label19": "Standard Code Value", "label2": "All '||' Relationships", "label20": "Standard Code Meaning", "label21": "System Number Field", "label22": "Routing <PERSON><PERSON><PERSON>", "label23": "Left Stream Configuration", "label24": "Key Field", "label25": "Output Field Prefix", "label26": "Right Stream Configuration", "label27": "Join Method", "label28": "Inner Join", "label29": "Time Lower Bound", "label3": "Custom Relationship", "label30": "Time Upper Bound", "label31": "Allow Out-of-order Time", "label32": "Time Type", "label33": "Left Join", "label34": "Right Join", "label35": "Window Type", "label36": "Sliding Window", "label37": "Tumbling Window", "label38": "Window Size", "label39": "Slide Step", "label4": "Method Parameters", "label40": "Generation Method", "label41": "Enumeration Value", "label42": "Decimal Places", "label43": "Data Range", "label44": "Character Type", "label45": "Time Range", "label46": "Output Method", "label47": "Simulated Output", "label48": "Real Output", "label49": "Test Case Name", "label5": "Upstream Component Fields", "label50": "Test Data", "label51": "Manual Addition", "label52": "CSV Input", "label53": "Random Data Generation", "label54": "Data Preview", "label55": "Data Modification", "label56": "Maximum Backpressure Ratio", "label57": "Checkpoint Failure Tolerance", "label58": "Maximum Checkpoint Time", "label59": "Average Checkpoint Time", "label6": "<PERSON><PERSON><PERSON><PERSON>", "label60": "Maximum TaskManager Memory Usage", "label61": "Maximum JobManager Memory Usage", "label62": "GC Count", "label63": "Flow Running Time", "label64": "Maximum Input Throughput", "label65": "Maximum Output Throughput", "label66": "Data Definition Full Class Name Output", "label67": "Data Definition Fields", "label68": "Whether Local Partitioning", "label69": "Update Primary Key", "label7": "Delay Time", "label70": "Periodic Mode", "label71": "Query Based on Primary Key", "label72": "Query Based on Update Time", "label73": "Query Based on Index Field", "label74": "Normal Paging Query", "label75": "Query Based on JDBC Streaming", "label76": "Disable", "label77": "Skip NULL Values", "label78": "Please select whether to enable the NULL value skip function", "label79": "Please select NULL value skip settings", "label8": "Unit: Seconds", "label80": "When enabled, when the input field value is NULL, the update of that field will be skipped, keeping the original value in the database unchanged. Only effective in UPSERT mode", "leaveMsg": "Are you sure to leave the current page?", "level": "Dimension", "loadingTip": "Loading...", "log": "Log", "logInfo": "Log Information", "logText": "Input/Output Log", "logicConfig": "Metric Configuration ({0})", "logicConfig1": "Metric Configuration", "lowData": "Low-quality Data", "m": "Minute", "m1": "This month", "mFlow": "Move Flow", "mainKey": "Primary Key", "maxRow": "Maximum Number of Rows", "maxRunTime": "Maximum Running Time", "mei": "Every", "meiD": "Per Day", "meiH": "Per Hour", "meiM": "Per <PERSON>", "meiS": "Per Second", "memo": "Description", "memoPlaceholder": "Please enter memo information (optional)", "mgr": "Flow Management", "mm": "Month", "mm1": "This Month", "mockTest": "Mock Input Test", "mode": "Flow Mode", "monitor": "Flow Monitoring", "monitorMgr": "Monitoring Management", "monitorRule": "Monitoring Rule Name", "move": "Move", "moveFlow": "Move Flow", "msg1": "Please save before starting a session!", "msg10": "When the flow is started for the first time or the Kafka consumer group information is modified, it needs to start consuming data from the beginning.", "msg100": "The name of the {0}th record conflicts with other field names", "msg101": "The {0}th record is not fully configured", "msg102": "The parameters of the {0}th record method are not fully configured", "msg103": "Please select a calculation engine", "msg104": "Please select a namespace", "msg105": "Please select a dimension", "msg106": "Please select an metric name", "msg107": "Please enter an metric ID", "msg108": "Metric ID cannot be repeated", "msg109": "Please select the metric to delete", "msg11": "When the flow is restarted, it needs to continue consuming data from the last checkpoint position, such as the last Kafka offset.", "msg110": "Select a field from the upstream fields as the dimension value for query", "msg111": "Select a field from the upstream fields as the dimension value for query. If not selected, the default value will be used", "msg112": "Please configure the metric", "msg113": "Specify an upstream field as the value for reporting. This value must be a JSONString string containing @type", "msg114": "Specify the effective script list for reporting. If not specified, all scripts will be effective by default", "msg115": "Please enter the service URL", "msg116": "Please select a strategy", "msg117": "Please select a dimension value", "msg118": "Please configure the metric", "msg119": "Please enter the batch processing quantity", "msg12": "This is a Flink savepoint, used to pause the flow. When the flow is restarted, it ensures exactly-once semantics.", "msg120": "Please enter a timeout time", "msg121": "Please enter the field to store the metric query result", "msg122": "The result field cannot be repeated", "msg123": "({0} items selected)", "msg124": "Please configure the rule expression", "msg125": "The {0}th record is not fully configured", "msg126": "The name of the {0}th record conflicts with other field names", "msg127": "Please select the database type", "msg128": "Please select a server name", "msg129": "Please select the table to query. If querying multiple tables using a regular expression, it can be written in the format ${regular expression}, such as ${table[0-9]}. Regular expressions will not dynamically discover matching tables.", "msg130": "Select some database fields. These fields will be queried and output to the downstream", "msg131": "Please select the output fields", "msg132": "Please enter a WHERE condition (can be empty)", "msg133": "The WHERE condition cannot contain a '?'", "msg134": "Please select a query method", "msg135": "Please select a time field", "msg137": "Unit: Milliseconds. Default is 100 * 1000", "msg138": "Please enter a page size", "msg139": "Specify the start time for the query. If a valid time format is entered, it will automatically convert to a timestamp when the focus is lost.", "msg14": "Are you sure to delete these flows?", "msg140": "Please enter an interval size", "msg141": "Please enter a start time", "msg142": "Please enter a valid time expression", "msg143": "Please select a cursor reset mode", "msg144": "Please select a table matching method", "msg145": "Please select a table and configure the mapping field information", "msg146": "Please enter a dynamic table name", "msg147": "Cursor Reset Mode", "msg148": "Please select whether UPSERT", "msg149": "If 'No', it will be an 'INSERT' statement; if 'Yes', it will perform 'INSERT' or 'UPDATE' based on the primary key, which is automatically obtained", "msg150": "Specify the fields to update during 'UPDATE'. Unselected fields will not be updated. By default, all fields will be updated.", "msg151": "The number of records written to the database each time. Default is 100", "msg152": "Increasing the batch size appropriately can increase the speed of data writing", "msg153": "If the data volume does not reach the specified batch size within this time, the output will occur. Unit is 'seconds'", "msg154": "The text content contains spaces. Please confirm whether the input is correct", "msg155": "Please configure the mapping fields for the table", "msg156": "Note: Special characters in the processing rules need to be escaped, such as ' should be written as \\\"", "msg157": "The parameters of the {0}th record method are not fully configured", "msg158": "The output fields of the component = custom fields + selected upstream input fields", "msg159": "Order: Custom fields > Upstream input fields", "msg160": "Note: Special characters in the processing rules need to be escaped, such as ' should be written as \\\"", "msg161": "Please fill in custom fields", "msg162": "Labels can only contain letters, numbers, _, -, !, @, #, ¥, %, &, *, (, )", "msg163": "Only one output field can be entered", "msg164": "No duplicate fields are allowed", "msg165": "Custom fields cannot conflict with upstream input fields", "msg166": "Please check the input content", "msg167": "Please enter an output field", "msg168": "Duplicate output data exists. Please re-enter", "msg169": "Please enter a valid logical expression", "msg17": "Confirm Stateless Start?", "msg170": "Condition limit has been reached", "msg171": "Are you sure to delete the configuration condition {0}?", "msg172": "Please enter a logical expression", "msg173": "Incomplete logical relationship: {0} is not included in the expression", "msg174": "Condition configuration is incorrect. Please check the configuration items!", "msg175": "Please select an upstream component field", "msg176": "Please enter a threshold", "msg177": "Length should be between 1 and 50 characters", "msg178": "Please check the input content", "msg179": "Please enter an output field", "msg18": "Are you sure to {0} {1} {2} {3} flows?", "msg180": "Duplicate output data exists. Please re-enter", "msg181": "Please enter a valid logical expression", "msg182": "Condition limit has been reached", "msg183": "Are you sure to delete the configuration condition {0}?", "msg184": "Please enter a valid logical expression", "msg185": "Condition {0}", "msg186": "Incomplete logical relationship: {0} is not included in the expression", "msg187": "Condition configuration is incorrect. Please check the configuration items!", "msg188": "Please select an upstream component field", "msg189": "Please enter a threshold", "msg190": "Length should be between 1 and 50 characters", "msg191": "{0} has been deleted", "msg192": "Specify a field as keyBy", "msg193": "Time Semantics", "msg194": "Specify a field as the time field", "msg195": "Maximum allowed out-of-order time, in seconds", "msg196": "Condition Configuration A", "msg197": "Condition Configuration B", "msg198": "Please enter a delay time", "msg199": "Please select a delay time", "msg2": "No changes to the flow. No need to save", "msg20": "The flow supports up to 100 nodes", "msg200": "Please select a time semantics", "msg201": "Please enter an out-of-order time", "msg202": "Please fill in the update frequency", "msg203": "Select the output field of the previous node as the output of the JSON field extraction component", "msg204": "Please fill in the output field", "msg205": "Please enter the JSON to be transformed on the left", "msg206": "Please fill in the join field of the left stream", "msg207": "Please fill in the time field of the left stream", "msg208": "Please fill in the prefix of the output field of the left stream", "msg209": "Please fill in the output field of the left stream", "msg21": "Process {0} has not been saved yet, are you sure to leave?", "msg210": "Please fill in the join field of the right stream", "msg211": "Please fill in the time field of the right stream", "msg212": "Please fill in the prefix of the output field of the right stream", "msg213": "Please fill in the output field of the right stream", "msg214": "Please select the join method for the stream", "msg215": "Please enter the upper bound of the time window, in seconds", "msg216": "Allow out-of-order time", "msg217": "Allow out-of-order time, in seconds", "msg218": "Please enter the size of the time window, in seconds", "msg219": "Please select a time type", "msg22": "Please save before configuring resources!", "msg220": "Please enter export fields, separated by commas", "msg221": "Special characters exist in the name: {0}", "msg222": "Please enter test case names", "msg223": "Please select a log type first", "msg224": "Please select a time range before downloading", "msg225": "No information found. Please click to continue querying for more", "msg226": "Please select a test case", "msg227": "Please enter the number of decimal places", "msg228": "Please enter a time range", "msg229": "Please select a character type", "msg23": "The name or type of the upstream input field", "msg230": "Please enter enumeration values", "msg231": "Please select enumeration values", "msg232": "Please enter enumeration values. If multiple, separate with commas", "msg233": "Please enter a data range", "msg234": "The start range cannot be greater than the end range", "msg235": "Cannot be empty", "msg236": "Test data for {0} cannot be empty", "msg237": "{0} cannot be empty", "msg238": "Test data for {0} cannot be empty", "msg239": "Logic for generating {0} cannot be empty", "msg24": "Flow parsing failed", "msg240": "Please input the use case name", "msg241": "Please enter test case data", "msg242": "Please select the logic for data generation", "msg243": "Please enter the number of rows", "msg244": "Executing, please wait...", "msg245": "Test Results for Use Case {0}", "msg246": "Please set up the process first", "msg247": "Are you sure you want to delete this data for [{0}]?", "msg248": "Note: If there are processes within the project, it cannot be deleted. If there are no processes within the project, the project and all its directories will be deleted together.", "msg249": "The project and its directories support a maximum of {0} levels.", "msg25": "Field information format error", "msg250": "Note: Processes that are not in development status cannot be moved. The system has automatically filtered them for you.", "msg251": "Move {0} flows to the target project directory", "msg252": "Copy {0} flows to the target project directory. If multiple project directories are selected, each directory will receive the selected flows.", "msg253": "{0} flows moved successfully", "msg254": "{0} flows moved successfully, {1} flows failed to move. Reasons for failure are as follows:", "msg255": "Flow names already exist in the target project: ", "msg256": "Flow status is not development", "msg257": "{0} flows failed to move. Reasons for failure are as follows:", "msg258": "View Directory Details", "msg259": "Flow publishing failed! For detailed reasons, please click on the canvas line to view.", "msg26": "{0} save prompt", "msg260": "The flow status is not development and cannot be edited", "msg261": "The selected flows are not in development status and cannot be moved", "msg262": "Are you sure to delete this directory?", "msg263": "Note: If the directory contains flows, it cannot be deleted. If there are no flows in the directory, the directory and all subdirectories will be deleted", "msg264": "Only published flows can view blood relationships", "msg265": "The flow {0} has been modified. Please save before publishing!", "msg266": "Please select a flow in the flow management on the left. If there is no flow, please create one first", "msg267": "Only supports JAR format", "msg268": "Please enter a full class name", "msg269": "Please upload the flow JAR package", "msg27": "{0}, please wait...", "msg270": "This mode requires configuring a distributed file system", "msg271": "Only one file can be uploaded", "msg272": "Only JAR files are supported", "msg273": "File size limit is 1GB", "msg274": "Total processing data volume of all collection components", "msg275": "Total processing data volume of all collection components / runtime duration (seconds)", "msg276": "Total of average transmission delay + average processing delay of all components", "msg277": "Batch set component input/output log switch", "msg278": "The input/output log switch controls whether to print the input and output data information of the flow nodes in the runtime log after the flow is published. On means printing, and off means not printing.", "msg279": "The output fields of component {0} and the input fields are not fully configured!", "msg28": "Generating SQL script...", "msg280": "The data definition of component {0} is not fully configured!", "msg281": "The field mapping of component {0} is not fully configured!", "msg282": "The input field {1} does not exist in the input fields of component {0}!", "msg283": "Table aliases only support letters, numbers, and underscores", "msg284": "The system will obtain all target table fields and generate SQL according to the specifications. For example: if the canvas already has", "msg285": "After configuring the table and alias (user alias u, area alias a) and clicking Generate, the following SQL will be generated:", "msg286": "Note: It is recommended to check and modify the generated SQL to meet actual requirements", "msg287": "Quickly obtain all target table fields and generate SQL", "msg288": "Configuration of node {0} (component: {1})", "msg289": "The output fields of the component = the selected upstream input fields", "msg290": "Order: Upstream input fields", "msg291": "Error parsing the validator function of form item [{0}]: {1}", "msg292": "Please select an upstream input field", "msg293": "Field [{0}] has a corresponding input field but is not selected. Clicking Confirm will discard the unselected configuration. Do you want to continue submitting?", "msg294": "Please select a data definition", "msg295": "Please enter a field search", "msg296": "When enabled, the full class name of the data definition will be output as the value of the '@type' field; when disabled, it will not be output", "msg297": "The selected data definition fields will be assembled into a JSON format and placed in this field (the full class name will be output according to the configuration)", "msg298": "Cannot contain special characters. Only letters, numbers, hyphens, and underscores are allowed", "msg299": "({0} items selected as output content)", "msg3": "Saving, please wait...", "msg30": "Compiling, please wait...", "msg300": "Please select the input field associated with field [{0}]", "msg301": "Derived field expression: {0}, no need to configure input fields", "msg302": "Please ensure that the associated field [{1}] of the derived field [{0}] is selected", "msg303": "Please select the input field corresponding to the data definition field [{0}]", "msg304": "Specify the fields to update during 'UPDATE'. Unselected fields will not be updated. By default, all fields will be updated.", "msg305": "Local partitioning will directly write data to the partition table", "msg306": "The partition field stores the partition table name after local partitioning, such as table_202407", "msg307": "Please select a partition field", "msg308": "Please select an update primary key", "msg309": "Please configure the mapping relationship for this field first", "msg31": "Please enter a flow name", "msg310": "Incremental query based on the primary key of the data table", "msg311": "Ensure that the update time field on the business side is the latest and can read incremental data including updates. Indexing the time field can speed up query efficiency; otherwise, a full table scan will be performed each time", "msg312": "For tables without a primary key, querying based on the index field can speed up query efficiency", "msg313": "Using pagination query, for tables with large data volumes, the query speed will slow down as you get further into the table", "msg314": "Using JDBC streaming query, it does not provide state-saving capabilities and is suitable for full-table migration. For tables with large data volumes, more memory is required. It is recommended to use multiple parallel instances for the query", "msg315": "Please enter a field", "msg316": "This switch controls whether to print the input and output data information of the flow nodes in the runtime log after the flow is published. On means printing, and off means not printing.", "msg317": "Gray area is upstream input fields, cannot modify field names and types", "msg318": "has been modified", "msg319": "The", "msg32": "Please select a flow to operate", "msg320": "record's parameter type does not match the processing rule", "msg321": "record's processing rule does not exist", "msg33": "Please select a flow to start", "msg34": "Flow deployment failed!", "msg35": "Flow stop failed", "msg36": "Flow deployment failed", "msg37": "Flow publishing failed", "msg38": "Flow publishing failed! For detailed reasons, please click on the canvas line with the abnormal SQL code", "msg39": "Flow compilation failed", "msg4": "Please save before testing!", "msg41": "No flows to publish", "msg42": "No flows to unpublish", "msg43": "No flows to deploy", "msg44": "Are you sure to {0} {1} flow?", "msg45": "No flows to stop", "msg46": "Please start a session first", "msg47": "Please select a test cluster", "msg48": "Set the maximum runtime for the test task. When the execution time exceeds the threshold, the test task will be automatically terminated. The maximum runtime can be set to {0} seconds.", "msg49": "Set the maximum number of rows for the test task results. When the test task generates the specified number of rows, it will be automatically terminated. The maximum number of rows can be set to {0}.", "msg50": "Are you sure to rollback?", "msg51": "Due to the addition or deletion of upstream fields, the following component fields have changed. Please confirm whether to perform automatic updates", "msg52": "Red fields: Deleted output fields", "msg53": "Green fields: Added output fields", "msg54": "Red fields: Deleted fields", "msg55": "Green fields: Added fields", "msg56": "The upstream output fields used by the current component, excluding optional fields", "msg57": "Please enter an integer greater than 0 and less than 100", "msg58": "Special characters exist in the name", "msg59": "The name already exists", "msg60": "Click 'Flow Components' on the left to start flow development", "msg61": "There are components with configuration anomalies", "msg62": "Exceeded the maximum number of connections: Component [{0}] supports a maximum of {1} input connections", "msg63": "The output fields of component [{0}] do not match the input fields of component [{1}]. {2} {3}", "msg64": "Component {0} {1}", "msg65": "{0}: Component [{1}] will delete {2} fields after update. Please check the update.", "msg66": "{0} is empty: Component [{1}] will delete {2} fields after update, causing the current component's {3} to be empty. Please check the update.", "msg67": "The configuration of component {0} is incomplete!", "msg68": "Component [{0}] has not configured the [Table/View] field!", "msg69": "Component [{0}] has not selected a [View]!", "msg7": "Are you sure to delete this project?", "msg70": "Component [{0}] is not fully configured", "msg71": "Component [{0}] has not selected a [Source Table]!", "msg72": "Component [{0}] has not selected an [Output Table]!", "msg73": "Component [{0}] has not selected [Output Table Fields]!", "msg74": "Please set a fragment abbreviation so that it can be quickly used in the editing area by typing the abbreviation and pressing Enter.", "msg75": "Please enter non-space characters", "msg76": "Are you sure to delete the SQL fragment?", "msg77": "The first character can only be a letter/number/./", "msg78": "The code fragment library is used to display reusable public/personal SQL fragments", "msg79": "Maintained by system administrators, available to all system users.", "msg80": "Available to all system users.", "msg81": "Maintained by system users, for personal use only.", "msg82": "Please enter a correct key", "msg83": "At least one item must be selected", "msg84": "Please select the input field corresponding to field [{0}]", "msg85": "Please select a primary key and partition field", "msg87": "Only the following JSON format is supported: {\"key1\":\"value\",\"key2\":[1,2],\"key3\":{\"key4\":\"xx\"}}", "msg88": "Please enter JSON", "msg89": "Please enter the parsing content", "msg90": "Use English commas to separate", "msg91": "Field {0} does not exist", "msg92": "Field {0} is duplicated", "msg93": "Field {0} has been configured as {1} type. Reconfiguration will update the field type", "msg94": "At least one type mapping row must be retained", "msg95": "A maximum of 20 type mapping rows can be added", "msg96": "Please set the field type and data precision", "msg97": "Please set the field type and field information", "msg98": "Note: Special characters in the processing rules need to be escaped, such as ' should be written as \\\"", "msg99": "Please complete the information", "name": "Name", "nameSpace": "Namespace", "new": "New", "new1": "Add", "newSql": "Create SQL Fragment", "no": "No", "noData": "No more data", "noStatusRun": "Stateless Start", "nodeDetail": "Node Details", "nodeLabel1": "Input Data Volume", "nodeLabel10": "Data Processing Volume in the Last Day", "nodeLabel2": "Output Data Volume", "nodeLabel3": "Failed Data Volume", "nodeLabel4": "Average Transmission Delay", "nodeLabel5": "Average Processing Delay", "nodeLabel6": "Warning Count", "nodeLabel7": "Data Processing Volume in the Last Minute", "nodeLabel8": "Data Processing Volume in the Last Ten Minutes", "nodeLabel9": "Data Processing Volume in the Last Hour", "notConfig": "Not Configured", "notInInputs": "Not in input fields.", "notInOutputs": "Not in output fields.", "numTotal": "Total Number of Digits", "numTotal1": "Number of Decimal Places", "online": "Online", "onlineMode": "Interval Cycle Mode: {0} online, execute every {1} hours {2} minutes", "onlineMode1": "Time Cycle Mode: {0} online, run daily at {1}", "onlineTime": "Latest Start Time", "onlineTip": "One-time online: {0} online", "onlining": "Starting...", "open": "Open", "operationStatus": "Operation Status", "operationTarget": "Operation Target", "operationTime": "Operation Time", "operationType": "Operation Type", "operator": "Operation", "other": "Other", "output": "Output", "outputChange": "Upstream Output Change Automatic", "outputComponent": "Output Component", "outputFields": "Output Fields", "pageSize": "<PERSON>", "paramMap": "Parameter Mapping", "paramType": "Parameter Type", "params": "Parameter", "params1": "Parameter {0}", "personCodeLibrary": "Personal SQL Fragment Library", "placeholder0": "Please enter", "placeholder1": "Search by project name", "placeholder10": "Enter flow notes", "placeholder11": "Enter parallelism", "placeholder12": "Enter maximum running time", "placeholder13": "Enter maximum number of rows", "placeholder14": "Enter tags, notes", "placeholder15": "Enter a name, up to 200 characters", "placeholder16": "Enter fragment abbreviation", "placeholder17": "Select input fields", "placeholder18": "Enter delimiter (default is comma)", "placeholder19": "Select a field", "placeholder2": "Search by flow or cluster", "placeholder20": "Enter field name", "placeholder21": "Select", "placeholder22": "Enter content", "placeholder23": "Select push fields", "placeholder24": "Select keyBy field", "placeholder25": "Field to store metric query results", "placeholder26": "Specify table matching method, can specify a unique table or configure a dynamic table using a time expression", "placeholder27": "Support multi-field paste, auto-input on Enter, comma-separated by default.", "placeholder28": "Select a method", "placeholder29": "Select a filter template", "placeholder3": "Enter project or directory", "placeholder30": "Select a mapping template", "placeholder31": "Select a system number field", "placeholder32": "Select a mapping field", "placeholder33": "Select a routing template", "placeholder34": "Enter input fields", "placeholder35": "Enter the lower bound size of the time window, in seconds", "placeholder36": "Select window type", "placeholder37": "Enter the slide step, in seconds", "placeholder38": "Enter keywords separated by commas", "placeholder39": "Select a log type", "placeholder4": "Enter search keyword", "placeholder40": "Select data output method", "placeholder41": "Enter test case name, up to 30 characters", "placeholder42": "Enter flow name for search", "placeholder43": "Up to 30 characters, no more", "placeholder44": "Select parent directory", "placeholder45": "Select prefix", "placeholder46": "Select suffix", "placeholder47": "Select target project directory", "placeholder48": "Select (multiple allowed)", "placeholder49": "Table alias (optional)", "placeholder5": "Enter a name, up to 30 characters", "placeholder50": "Enter task name", "placeholder51": "Enter job name for fuzzy search", "placeholder6": "Enter notes", "placeholder7": "Enter a name", "placeholder8": "Enter a flow name, up to 30 characters", "placeholder9": "Select flow type", "processComponent": "Processing Component", "processed": "Processed", "prod": "Online", "project": "Project", "projectInfo": "All Project info", "projectName": "Project Name", "pub": "Published", "publicCodeLibrary": "Public SQL Fragment Library", "publicMethod": "Shared Method", "publish": "Publish", "publishing": "Publishing", "pushLogic": "Push Configuration", "qkey": "Shortcut Key", "qt": "Nested", "quanping": "Full Screen", "queryInterval": "Interval Size", "queryStrategy": "Query Method", "rDir": "Root Directory", "random": "Random Generation", "randomEnum": "Random Generation - Enumeration Value", "realTest": "Real Input Test", "refresh": "Refresh", "relation": "Reference Relationship", "relation1": "Logical Relationship", "relationImg": "Relationship Diagram", "reload": "Re-upload", "remark": "Remark", "reset": "Reset", "resourceName": "Resource Name", "result": "Result", "resultFields": "Result Fields", "resultStr": "Result Expression", "resultView": "Result View", "rollBack": "Rollback", "rowNum": "Row Count", "rule": "Processing Rule", "ruleStr": "Rule Expression", "run1": "Execute", "runInfo": "Runtime Information", "runLog": "Execution Log", "runStatus": "Execute Status", "runTest": "Run Test", "s": "Second", "save": "Save", "scriptList": "Script List", "scroll": "<PERSON><PERSON>", "seach1": "Search", "search": "Search", "search1": "Continue Searching", "searchAll": "Full Search", "searchConfig": "Search Configuration", "searchResult": "Search Results", "select": "Re-select", "selectAll": "Select All", "selectAll1": "Select All", "selectAll2": "Select All", "selectFile": "Select File", "selectTable": "Please select a table", "selectTimeRange": "Select Time Range", "selectableProject": "Selectable Projects", "selected": "Selected", "selectedProject": "Selected Project", "serve": "Server", "serveAddress": "Server Address", "serveInfo": "Server Information", "serveName": "Server Name", "serveType": "Server Type", "serviceInfo": "All Service Info", "session": "Session", "sessionConfig": "Session Configuration", "showAll": "Show All", "showSelected": "Show Selected Only", "showUnselected": "Show Unselected Only", "sortByProjectName": "Sort by project name alphabetically", "sortByUpdateTime": "Sort by update time in descending order", "sourseTable": "Source Table", "splitStr": "Delimiter", "sqlCode": "SQL Code", "sqlDetail": "SQL Fragment Details", "startDate": "Start Date", "startSession": "Start Session", "startTime": "Start Time", "status": "Flow Status", "statusRun": "Start Based on Last State", "steam": "Stream", "stop": "Stop", "stopTime": "Latest Stop Time", "stopWithStatus": "Stop and Retain Status", "stopping": "Stopping", "str": "Expression", "streamingMode": "Streaming Mode", "tDir": "Target Project Directory", "table": "Table", "tableMgr": "Table Management", "tableName": "Table Name", "tag": "Tag", "target": "Output Target", "test": "Test", "testData": "Test Data", "testEp": "Test Case", "testResult": "Test Result", "tiao": "<PERSON><PERSON>", "timeField": "Time Field", "timeToRollForward": "Time Roll Forward", "timeout": "Timeout", "tip": "Tip", "title1": "Resource Configuration ({0})", "title2": "Generate Personal SQL Fragment", "title3": "Data Generation Logic", "title4": "Execution Prompt Information", "title5": "Log for [{0}] Test Case", "total": "Total", "trans": "Transfer", "type": "Flow Type", "unknownError": "Unknown error", "updateTime": "Update Time", "updater": "Updated By", "use": "Enable", "useUtil": "Invoke Smart Tool", "util": "Smart Tool", "val": "Value", "version": "Version Information", "version1": "Version", "versionCompare": "Version Comparison", "view": "View", "view1": "Overview", "view2": "Project View", "view3": "View Component Monitoring Data", "viewCondition": "View Conditions", "waitOnline": "Pending Start", "waitTime": "Interval Time", "warnning": "<PERSON><PERSON>", "waykey1": "Push First", "waykey2": "Query First", "waykey3": "Query Only", "waykey4": "Push Only", "week": "Week", "where": "WHERE Condition", "wu": "None", "xiang": "<PERSON><PERSON>", "y": "Year", "yes": "Yes", "ys": "Derived", "zhi": "To"}, "flowDataSetChange": "The data definition configured for flow {0} has changed. Please confirm whether to continue.", "flowDetail": "Flow Details: {0}", "flowInfo": "Flow Information", "flowList": "Flow List", "flowName": "Flow", "flowOnlineException": "Flow deployment exception. You can perform a forced stop operation in the flow monitoring module", "flowType": "Server resource type not selected", "form": "From: {0}", "formDesign": "Form Design", "format": "Formatting", "freeMemory": "JVM Heap Memory", "freeSlots": "Available Slots", "getConfFailed": "Failed to get configuration", "getMenuFailed": "Failed to get menu", "globalSearch": "Global Search", "graph": "Graph", "groupError": "Form item [{0}] is missing the required field [group]. The form item cannot be displayed", "groupInfoError": "The group field value [{1}] of form item [{0}] does not exist in the group information. The form item cannot be displayed", "handleLoading": "Processing {0} data, please wait...", "handleRule": "Processing Rule", "handleTime": "Processing Time", "hbaseCode": "Please click on the table name on the left to query, up to 20 records", "historyVersion": "History Versions", "hiveCode": "Please click on the table name or table field on the left to query, up to 20 records", "home": {"count": "Count", "data": "Data", "dataCount": "Data Volume", "distribution": "Flow Status/Quantity Distribution", "engineMatter": "Engine Resource Usage", "flowTotal": "Total Flows", "hdMillion": "Billion", "individual": "items", "memory": "Memory", "noPage": "No specified server list page", "regist": "Server Registration Count", "resource": "Resource", "restart": "Restarting", "running": "Running", "service": "Server", "success": "Success", "text1": "Flow data processing for the day", "text10": "Restarting: The flow is restarting", "text11": "Unknown: The flow status is unknown (due to platform or processing engine anomalies, the flow running information cannot be obtained)", "text12": "Completed", "text13": "Failed", "text14": "Cancelled", "text15": "Percentage (%)", "text2": "Total data processed for the day", "text3": "Flow data processing volume for the day", "text4": "Distribution of the running status of published flows (flows with a status of published or online), the definitions of running statuses are as follows:", "text5": "Not Running: The flow is published but not online", "text6": "Running: The flow is currently running", "text7": "Completed: The flow has completed (batch mode or bounded flow will show as completed after completion)", "text8": "Failed: The flow has failed", "text9": "Cancelled: The flow has been cancelled", "thousand": "Ten Thousand", "timeout": "Query timed out, please", "title": "PA Monitoring Cloud Disk", "tryAgain": "Retry", "unused": "Unused", "updateTime": "Data Update Time", "used": "Used", "warning": "Pending <PERSON><PERSON>"}, "homePage": "Home", "host": "Host", "hostName": "Host Name", "iconFile": "Icon File", "indexCode": "Please click on the index on the left to query, up to 20 records", "initialBackoff": "Initial Interval", "intervalPeriod": "Interval Period", "jdbc": "Database", "jitterFactor": "Jitter Factor", "jobManagerLimitCpu": "JobManager CPU Burst Multiplier", "jobManagerLimitMemory": "JobManager Memory Burst Multiplier", "jobManagerMemory": "JobManager Memory (MB)", "jobManagerRequestCpu": "JobManager CPU", "jobMgrInfo": "JobManager Information", "jobMgrMemory": "Job<PERSON><PERSON><PERSON>", "jobRunningRule": "Job Running Rule", "jobTag": "Please enter a tag, up to 30 characters", "key": "Key", "labelError": "Form item [{0}] is missing the required field [label]", "lagTotal": "Total Lag", "length2to30": "Length between 2 to 30 characters", "listView": "List View", "loading": "Loading...", "loadingText": "Loading data...", "location": "Location", "log": "Log", "log4jFile": "Log4j Configuration", "logBack": "User session expired. Please log in again.", "logExpired": "<PERSON><PERSON> expired. Please log in again.", "logOutputKafka": "Log Output to Kafka", "logout": "Log out", "managedMemory": "Flink Managed Memory", "maxBackoff": "Maximum Interval", "memoryCpuSetting": "Memory and CPU Settings", "memoryRemain": "Memory Remaining", "memoryUsed": "Memory Used", "menu": {"bloodRelation": "Blood Relationship", "dataDefine": "Data Definition", "filterTemplate": "Filter <PERSON>late", "flowMonitor": "Flow Monitoring", "mapLibrary": "Mapping Field Library", "mapTemplate": "Mapping Template", "monitorWarn": "Monitoring <PERSON><PERSON>", "optionManage": "Option Management", "routeTemplate": "Route Template", "serviceMonitor": "Service Monitoring", "sheetManage": "Table Management", "sqlClip": "SQL Fragment", "sqlClipLib": "SQL Fragment Library", "udf": "UDF Management"}, "method": "Method", "modeSelection": "Mode Selection", "monitor": {"flow": {"action": {"basedLastOnline": "Start Based on Last State", "basedLastTooltip": "Restart process, it needs to continue consuming data from the last checkpoint position, such as the last Kafka offset.", "batchOperationSuccess": "Batch operation added successfully", "forceOffline": "Force Stop", "leastOne": "Please select a record", "onlineLeast": "Please select the flow to start", "restartForce": "<PERSON><PERSON>", "retainOffline": "Stop and Retain Status", "retainTooltip": "This is a Flink savepoint, used to pause the flow. When the flow is restarted, it ensures exactly-once semantics.", "statelessConfirm": "Confirm Stateless Start?", "statelessOnline": "Stateless Start", "statelessTooltip": "The flow is started for the first time, or the Kafka consumer group information has been modified and needs to start consuming data from the beginning."}}, "service": {"connect": "Connected", "unConnect": "Disconnected"}, "text1": "Control whether to send notifications when triggering alert rules. Even if notifications are disabled, alerts will still be triggered and records will be generated (viewable within the platform).", "text10": "When the alert rule status is enabled, the system will perform timed checks according to the execution cycle within the effective time. The execution cycle is configured using a cron expression, where the 6 positions of the cron expression correspond to: seconds/minutes/hours/days/months/weeks. For example, '0 */1 * * * ?' means executing every minute; '30 30 8 * * ?' means executing at 8:30:30 AM every day.", "text11": "Effective time is used to set the time range for system resource monitoring, and the system will check the resources according to the execution cycle only within this time range every day.", "text12": "Alert notification content", "text13": "Status", "text14": "Please enter the effective time", "text15": "Please enter the alert notification content", "text16": "Please select the processing status", "text17": "Unprocessed", "text18": "Flow monitoring targets published flows (with a status of published or online), supporting the viewing of running conditions and operation & maintenance management. The definitions of running statuses are as follows:", "text19": "Total", "text2": "Note: Notification functionality requires system integration to enable notifications via SMS, phone calls, group bots, etc.", "text20": "Runtime Monitoring", "text21": "Select status", "text22": "Enter project/flow name", "text23": "Select status", "text24": "Select rule", "text25": "Select Type", "text26": "When the alert rule status is enabled, the system will perform timed checks according to the execution cycle within the effective time.", "text27": "The execution cycle is configured using a cron expression, where the 6 positions of the cron expression correspond to: seconds/minutes/hours/days/months/weeks. For example, '0", "text28": "*/1 * * * ?' means executing every minute; '30 30 8 * * ?' means executing at 8:30:30 AM every day", "text29": "View only alerts with >0 unprocessed", "text3": "When a resource check triggers an alert rule, the message content sent to the downstream system can be customized. The following parameters can be used as dynamic variables:", "text30": "No data permissions", "text4": "title: Resource name;", "text5": "resType: Resource type;", "text6": "ruleType: Alert rule;", "text7": "problem: Content of the issue.", "text8": "For example, if the Kafka server 'testkafka' fails to connect, the message will be 'Resource name: testkafka; Resource type: Kafka; Alert rule: Connectivity check; Problem: Failed to connect to node: [10.100.1.23:6311]'", "text9": "Please enter an alert rule", "warningRule": {"detail": {"allHandle": "Handle All", "detailInfo": "Detailed Information", "handle": "<PERSON><PERSON>", "lastMonth": "Last Month", "lastThreeMonths": "Last Three Months", "lastWeek": "Last Week", "noNeedToBeProcessed": "No records need to be processed", "warningRecord": "Alert Records"}, "edit": {"cron": "Execution Cycle", "cronPlaceholder": "Please enter an execution cycle", "effectiveTime": "Effective Time", "endTime": "End Time", "noticeUser": "Notifier", "noticeUserPlaceholder": "Please select notification recipients", "ruleType": "Rule Type", "saveAndOnline": "Save and Restart", "sendEnable": "Alert Notification Sending", "silent": "Silent Period", "silentPlaceholder": "Please enter a silent period", "startTime": "Start Time"}, "resNamePlaceholder": "Enter Name"}}, "monitorRuleName": "Monitoring Rule Name", "name": "Name", "nameTip": "{0} and {1} flows {2}", "nameTip2": "{0} {1} flows {2}", "namespace": "Namespace", "namespaceInfo": "Namespace Information", "newCategory": "Create Category", "noData": "No data", "node": "Node", "nodeInfo": "Node Information", "notPermission": "No operation permissions. Please contact the administrator.", "notUpdateModeJobName": "The basic parameters, advanced parameters, and custom parameters will be updated.", "notes": "Remark", "objectCount": "Object Count (Primary)", "once": "One-time", "onlineDate": "Online Date", "onlineTime": "Online Time", "onlyValue": "Only Value", "opening": "Opening", "orgAllocated": "Organization Allocated", "orgAvailableSlots": "Organization Available Slots", "orgCPU": "Organization CPU", "orgChildrenCpu": "Organization Allocated CPU", "orgChildrenMemory": "Organization Allocated Memory", "orgChildrenSlots": "Organization Allocated Slots", "orgCpu": "Organization Available CPU", "orgField": "Organization Field", "orgId": "Organization ID", "orgMemory": "Organization Available Memory", "orgName": "Organization Name", "orgOrQueueSetting": "Organization/Queue Settings", "orgResidualCpu": "Organization Remaining CPU", "orgResidualMemory": "Organization Remaining Memory", "orgResidualSlots": "Organization Remaining Slots", "orgSlots": "Organization Slots", "orgSurplus": "Organization Remaining", "orgUsed": "Organization Used", "orgUsedCpu": "Organization Used CPU", "orgUsedMemory": "Organization Used Memory", "orgUsedSlots": "Organization Used Slots", "outputFields": "Output Fields", "pageDesign": "Page Design", "parallelism": "De<PERSON><PERSON>", "params": {"delConfirm": "This operation will permanently delete the record. Do you want to continue?", "template": {"addTemplate": "Add Template", "delFieldSuccess": "Field deleted successfully", "delSuccess": "Template deleted successfully", "detail": {"addField": "Add Field", "addFilter": "Add Filter Condition", "addRoute": "Add Route", "addRule": "Add Condition", "baseInfo": "Basic Configuration", "cnName": "Chinese Name", "downloadTemplate": "Download Template", "editField": "Edit Field", "errorTip": "Error message", "fieldList": "Field List", "fieldName": "Field Name", "fieldType": "Field Type", "fieldValue": "Value", "file": "File", "filterRule": "Filter Rule", "filterRuleRequired": "Filter condition cannot be empty", "lackRule": "Condition not configured", "mapField": "Mapping Field", "newBuiltField": "New Field", "notFullyConfigured": "Not fully configured", "notMismatching": "Field type and field value do not match", "onlyExcel": "Only Excel files are allowed", "onlyOneExcel": "Only one Excel file is allowed", "operator": "Operator", "route": "Route", "routeList": "Route List", "rule": "Condition", "selectFile": "Select File", "serialName": "Standard Name", "serialNumber": "Standard Code", "service": "Cluster", "servicePlaceholder": "Select cluster", "serviceType": "Server Type", "sourceCode": "Source Code", "sourceCodeComment": "Source Code Meaning", "standardCode": "Standard Code", "standardCodeComment": "Standard Code Meaning", "systemName": "System Name", "systemNumber": "System Number", "target": "Output Target", "targetRequired": "Output target cannot be empty", "templateName": "Template Name"}, "editTemplate": "Edit Template", "historicalFilterTemplate": "Historical Filter Template", "historicalMapTemplate": "Historical Mapping Template", "historicalRouteTemplate": "Historical Route Template", "historicalTemplate": "Historical Template", "name": "Enter template name"}}, "parentEncoding": "Parent Encoding", "parentEncodingNotExist": "Parent encoding does not exist. Please re-select the category", "parentEncodingPlaceholder": "Enter parent encoding", "partCount": "Partition Count", "patternError": "Error parsing pattern for form item [{0}]: {1}", "physicalMemory": "Physical Memory", "placeholder": {"afterCpu": "Please enter the adjusted CPU", "afterMemory": "Please enter the adjusted memory", "afterSlots": "Please enter the adjusted slots", "attempts": "Please enter the number of attempts", "checkpointInterval": "Please enter the checkpoint interval", "complete": "Please complete the information", "consumeMode": "Please select a consumption strategy", "consumer": "Please enter a consumer", "consumerName": "Please enter a consumer name", "copies": "Please enter the number of replicas", "cpFailed": "Please enter the checkpoint failure tolerance", "cpMinPause": "Please enter the minimum pause time", "cronExpression": "Please enter a cron expression", "database": "Please enter a database", "delay": "Please enter the fixed restart delay", "failureRateDelay": "Please enter the failure restart delay", "failureRateInterval": "Please enter the time interval", "failuresPerInterval": "Please enter the number of restarts", "fieldName": "Please enter a field name", "groupId": "Please enter a group-id", "input": "Please enter", "jobManagerMemory": "Please enter the memory for the job manager (MB)", "jobManagerRequestCpu": "Please enter the CPU for the job manager", "jobRunningRule": "Please select a job running rule", "key": "Please enter a key", "keyPlaceholder": "Please enter search keywords", "keyWord": "Please enter a search keyword", "keyword": "Please enter a keyword for search", "name": "Please enter name", "nameOrUrl": "Please enter name or service address", "namespace": "Please enter a namespace", "newKey": "Please enter a key", "notesPlaceholder": "Please enter notes", "onlineTime": "Please select the online time", "orgIds": "Please select an organization", "orgName": "Please enter an organization name", "orgNameFilter": "Please enter an organization name filter", "orgNameOrId": "Please enter an organization name/ID", "partCount": "Please enter the number of partitions", "partitionOffset": "Please enter partition:offset;partition:offset", "queue": "Please select a queue", "queueList": "Please select a queue for allocation", "queueName": "Enter queue name", "restartStrategy": "Please select a restart strategy", "search": "Please enter content", "select": "Please select", "set": "Please enter a set", "sqlPlaceholder": "Please enter SQL code", "stateBackend": "Please select a state backend", "tableName": "Please enter a table name", "taskManagerFrac": "Please enter the task manager memory fraction", "taskManagerLimitCpu": "Please enter the CPU burst multiplier for the task manager", "taskManagerLimitMemory": "Please enter the memory burst multiplier for the task manager", "taskManagerRequestCpu": "Please enter the number of CPUs for the task manager", "taskManagerSlotNumber": "Please enter the number of task manager slots", "time": "Please enter a time", "topicName": "Please select a topic name", "updatePlaceholder": "Please enter update content", "useCasesPlaceholder": "Please enter test case data", "vaguePlaceholder": "Enter field name for fuzzy search", "value": "Please enter a value"}, "pleaseSelect": "Please select", "port": "Port", "portal": {"title1": "User Permissions", "title2": "Organization Management", "title3": "User Management", "title4": "Role Management", "title5": "System Configuration", "title6": "Security Configuration", "title7": "Content Configuration", "title8": "Operation Log", "title9": "Personal Center"}, "preLackCpu": "Estimated Remaining CPU Insufficient", "preLackMemory": "Estimated Remaining Memory Insufficient", "preLackSlots": "Estimated Remaining Slots Insufficient", "preRemainCpu": "Estimated Remaining CPU", "preRemainMemory": "Estimated Remaining Memory (MB)", "preferenceRelation": "Reference Relationship ({0})", "prescribed": "Custom", "process": "Process", "prompt": "Prompt", "propError": "Form item [{0}] is missing the required field [prop]", "queryTimeout": "Query timed out. Please refresh and try again.", "queue": "Queue", "queueResource": "Queue Resource", "queueResourceDetail": "Queue Resource Details", "rawData": "Raw Data", "read": "Read", "readError": "<PERSON> Error", "realTestResult": "View Real Input Test Results", "refreshSource": "Refresh resource usage", "remainCpu": "Remaining CPU", "remainMemoryMB": "Remaining Memory (MB)", "remainSlot": "Remaining Slots", "remainSlots": "Estimated Remaining Slots", "repeatProp": "Duplicate prop [{0}] exists", "repeatTime": "Repeat Time", "requestTimeout": "Request Timeout!", "resType": "Node type not selected", "resetBackoffThreshold": "Minimum Stable Runtime", "resource": {"importExport": {"assetId": "Asset ID", "assetName": "Asset Name", "assetType": "Asset Type", "diffContent": "Difference Content", "exportDataPreview": "Export Data Preview", "keyplaceholder": "Filter by keyword", "option": "Option", "project": "Project", "selectAll": "Select All", "selectNone": "Select None", "tablePrefix": "Table Prefix", "tableSuffix": "Table Suffix"}, "sql": {"dataTip": "Please select data", "delTip": "Are you sure to delete the selected data?", "detail": {"alreadyExists": "The following SQL fragment abbreviations already exist in the platform", "duplicateData": "Duplicate Data", "file": "File", "partAcronym": "Fragment Abbreviation", "partName": "Fragment Name", "placeholder": {"acronymPlaceholder": "Please enter a fragment abbreviation", "name": "Please enter a fragment name", "notesPlaceholder": "Please enter notes"}, "sqlCode": "SQL Code", "tips": {"inputAcronymTips": "The first character can only be a letter/number/./"}, "tooltip": {"tooltip1": "Please set a fragment abbreviation so that it can be quickly used in the editing area by typing the abbreviation and pressing Enter."}}}, "tip1": "Break the operator chain, which can be used to analyze process performance bottlenecks, but it will have an impact on process performance.", "tip2": "Perform periodic state snapshots to ensure data consistency, but it will have an impact on process performance."}, "resourceConf": "Resource Configuration ({0})", "resourceConfig": "Resource Configuration", "resourceList": "Please enter a resource name", "resourceNotFound": "{0} resource not found", "restartStrategy": "Restart Strategy", "result": "Result", "reupload": "Re-upload", "ruleExpression": "Rule Expression", "scriptInfo": "Script Information", "searchDoc": "Document Search", "searchResult": "Search Results", "selectVersion": "Selected Version: {0}", "sentry": "Sentry", "serialNumber": "Serial Number", "service": "Service", "serviceDetail": "Service Details", "serviceId": "Node name not selected", "serviceMgr": "Service Management", "serviceName": "Flow type not selected", "serviceType": "Server Type", "serviceTypeRule": "Please select a server resource type", "sessionExpired": "Session Expired. Redirect to <PERSON><PERSON>?", "sharedOrg": "Shared Organizations", "sharingMethod": "Shared Method", "sheetEdit": "Table Management: {0}", "showHomologyGraph": "After clicking [Query Blood Relationship], the blood relationship diagram will be displayed with this homologous node as the query node", "single": "Single", "singlePoint": "Single Node", "singleText0": "Are you sure to delete the selected data?", "singleText1": "{name} has been associated and cannot be deleted!", "slotsNumber": "Total Slots", "slotsSetting": "Slots Settings", "someProject": "to the project", "sourceCode": "Source Code", "sqlFlow": "SQL Flow", "sqlFragment": "SQL Fragment {0}", "stateBackend": "State Backend", "status": {"develop": "Development", "none": "Not Running", "online": "Online", "published": "Published", "unKnow": "Unknown", "underDdevelopment": "Under Development"}, "statusStorage": "State Backend Storage", "streamcube": "Computing Engine", "syncMig": "Synchronize the configuration of flow {0} to {1}. After synchronization, the basic parameters, advanced parameters, custom parameters, and mode selection of {2} will be updated. {3} {4} Are you sure to synchronize?", "syncSetting": "Synchronization Settings", "syncSuccess": "Synchronization successful. Please save or start.", "systemPath": "Installation path placeholder ${install_dir_key}, port placeholder ${port_key}", "systemPlaceholder": "System built-in placeholder", "table": "Table", "tableCode": "Please select a table name or table field on the left to query, up to 20 records", "tableInfo": "Table Information", "tableName": "Table Name", "targetOrg": "Target Organization", "taskManage": "Task Managers", "taskManagerFrac": "TaskManager Managed Memory Factor", "taskManagerLimitCpu": "TaskManager CPU Burst Multiplier", "taskManagerLimitMemory": "TaskManager Memory Burst Multiplier", "taskManagerMemory": "TaskManager Memory (MB)", "taskManagerRequestCpu": "TaskManager CPU", "taskManagerSlotNumber": "TaskManager Slots", "taskMgrInfo": "TaskManager Information", "taskMgrMemory": "TaskManager <PERSON>", "taskMgrSlots": "TaskManager Slots", "testResult": "Test Result", "thirdPartyLibs": "Third-party Libraries", "timePeriod": "Time Period", "timeSinceLastHeartbeat": "Last Heartbeat Time", "tip": {"addFile": "Please add a file", "checkMessage": "Please check the input content", "checkResult": "Check the connection status of the statistical node, connected nodes / total nodes", "choseData": "Please select data", "choseOne": "Please select one record", "clusterNotExist": "The cluster does not exist", "codeLoading": "Loading source code...", "color": "Red indicates deletion, yellow indicates update, and blue indicates addition", "compareVersion": "Please select the version to compare", "configureAtOne": "Please configure at least one condition", "connectError": "{0} connection failed", "connectSuccess": "Connection successful", "cpFailed": "Maximum checkpoint failures", "cpUnaligned": "Enabling this can increase the speed of checkpoints but will increase memory usage", "cron": "Please enter a correct cron expression", "dataQualityMonitoring": "Data Quality Monitoring Component Prompt\n\n      The monitoring rules configured in this component are implemented using mvel expressions, following the syntax of mvel expressions.\n      The result of the expression must be a boolean value. Here are some examples:\n\n      Assuming the upstream fields are: id, name, counta, msg, countb, countc\n      1. Using fields: Start with # and end with #. For example: #counta# > 10\n      2. Using symbols: &&, ||, >, <, >=, <=, ==, %, *, +, - etc., null or nil indicates an empty value.\n      For example: (#countc# + #counta# < 200) && #countb# < 990\n      For example: #name# == null || #id# == null\n\n      3. Using custom methods: Define a method in [Component Management] - [Resource Management] - [Methods] first, and then use it.\n      For example, define the following method first:\n\n      public static boolean getBoolean(Integer id, String name)\n        { return name == null || id == null || id < 9999;\n      }\n\n      The expression edited here can be:\n      getBoolean(#id#,#name#)\n      Or\n      #counta# * #countb# < 99999 && getBoolean(#id#,#name#)", "delConfirm": "Are you sure to delete this data?", "delSuccess": "Deleted successfully", "deleteConfirm": "This operation will permanently delete the record. Do you want to continue?", "deleted": "Already deleted", "deployTimeout": "Please enter the deployment timeout", "dfs": "This mode requires configuring a distributed file system", "enableCheckPoint": "Perform periodic state snapshots to ensure data consistency, which may impact process performance", "fieldRepeat": "The field name in the basic parameters conflicts with the fields in the field configuration", "fileCount": "Maximum {0} files can be uploaded", "greaterStartTime": "Please select a start time greater than the current time", "intervalHour": "Please enter hours", "intervalMinute": "Please enter minutes", "isApplyParallelism": "Apply to each component", "jobManagerLimitCpu": "Please enter the CPU burst multiplier for the job manager", "jobManagerLimitMemory": "Please enter the memory burst multiplier for the job manager", "jobManagerMemory": "Please enter the memory for the job manager", "jobManagerRequestCpu": "Please enter the CPU for the job manager", "jobMgrLimitCpu": "This configuration enables the CPU burst capability of the job manager and specifies the maximum CPU that the job manager can use during runtime.", "jobMgrLimitMemory": "This configuration enables the memory burst capability of the job manager and specifies the maximum memory that a job manager can use during runtime.", "jobMgrMemory": "The job manager is responsible for the entire lifecycle management of the job, including resource application, status monitoring, coordination, and control execution, such as handling scheduling tasks, saving checkpoints, and fault tolerance. This configuration item specifies the JVM heap memory.", "jobMgrRequestCpu": "This configuration item specifies the minimum CPU allocated for the job manager to run.", "jobRunningRule": "Please select the job running rule", "loading": "Loading data...", "logging": "Logging in...", "map": "Note: When using methods in the processing rules, special strings need to be escaped, such as \" should be written as \\\"", "maxLength1000": "Maximum 1000 records can be selected at a time", "maximumInput": "Maximum input length is {0} characters", "mode": "Please select the process mode", "namespace": "YARN manages users and resources through namespaces. This item specifies the namespace to which the job is submitted to the cluster.", "nonSpaceChar": "Please enter non-space characters", "notEndWithComma": "Content cannot end with a comma", "notHasChinese": "Content cannot contain Chinese characters", "notHasChineseComma": "Content cannot contain Chinese commas", "notHasSpaceBreak": "Content cannot contain spaces or line breaks", "notStartWithComma": "Content cannot start with a comma", "onlyJar": "Only jar files are allowed!", "onlyJar250MB": "Only jar files are allowed, and the size cannot exceed 250MB", "onlyPngSvg": "Only png and svg files are allowed!", "onlyPngSvg20KB": "Only png and svg files are allowed, and the size cannot exceed 20KB", "orgConfig": "Some organizations have not completed {0}, please complete the configuration", "queue": "YARN manages users and resources through queues. This item specifies the queue to which the job is submitted to the cluster.", "recordConfig": "The {0}th record is not fully configured", "recordName": "The name of the {0}th record conflicts with other field names", "recordParams": "The parameters of the {0}th record method are not fully configured", "recycle": "Are you sure to recycle the resources allocated under the organization?", "refreshSuccess": "Refresh successful", "related": "The current resource has reference relationships, and modifications may affect it", "repeatTime": "Please select the repeat time", "rollBackConfirm": "Are you sure to rollback?", "sameCode": "Version {0} is the same as the selected version {1}", "sameQueueOrgData": "Please select data from the same queue organization", "scp": "Note: Files are uploaded to the user's root directory by default", "selectFile": "Please select a file", "selectedOrg": "Selected organizations ({0}/1000):", "shareAccess": "Default share permissions:", "shareTopic": "Please select the topic to share:", "size1GB": "File size limit is 1GB", "size20KB": "Files larger than 20KB are not allowed!", "size250MB": "Files larger than 250MB are not allowed!", "someKey": "Duplicate keys exist", "startConnectTest": "Starting connection test...", "startDate": "Please select the online date", "startTime": "Please select the online time", "taskManagerFrac": "This item is used for sorting, hash tables, and caching intermediate results in the RocksDB state backend in batch-stream processes.", "taskManagerLimitCpu": "Please enter the CPU burst multiplier for the task manager", "taskManagerLimitMemory": "Please enter the memory burst multiplier for the task manager", "taskManagerMemory": "Please enter the memory for the task manager", "taskManagerRequestCpu": "Please enter the number of CPUs for the task manager", "taskManagerSlotNumber": "Please enter the number of task manager slots", "taskMgrLimitCpu": "This configuration enables the CPU burst capability of the task manager and specifies the maximum CPU that the task manager can use during runtime.", "taskMgrLimitMemory": "This configuration enables the memory burst capability of the task manager and specifies the maximum memory that the task manager can use during runtime.", "taskMgrMemory": "The task manager is responsible for the execution of specific tasks, and this item is set to the memory amount for each task on the node.", "taskMgrRequestCpu": "This configuration item specifies the minimum CPU allocated for the task manager to run.", "taskMgrSlotNumber": "Configure the number of concurrent slots for a task manager, similar to the number of threads in a task manager.", "terminal": "Terminal [{0}]", "timeFormat": "Date format: {0}", "typeJudge": "Only {0} type files are allowed!", "updateSuccess": "Update successful", "uploadFiles": "Please upload {0} file"}, "totalCpu": "Total CPU", "totalMemory": "Total Memory", "totalMemoryMB": "Total Memory (MB)", "totalSlots": "Total Slots", "udfInfo": "UDF Information", "udjInfo": "UDJ Information", "underExecution": "Executing, please wait...", "unit": {"day": "Day", "hour": "Hour", "hours": "Hours", "minute": "Minute", "second": "Second"}, "unknownError": "Unknown error: {0}", "unknownErrorCode": "Unknown error, error code: {0}", "unknownFile": "Unknown File", "unused": "Unused", "updateModeJobName": "The basic parameters, advanced parameters, custom parameters, and mode selection will be updated.", "uploadBatch": "Batch Upload (currently only for non-existent components)", "uploadError": "Upload failed: {0}", "uploadSingle": "Single Upload", "used": "Used", "userCancel": "User canceled", "userCenter": "Personal Center", "value": "Value", "value1": "Value 1", "value1Placeholder": "Enter content for Value 1", "value2": "Value 2", "value2Placeholder": "Enter content for Value 2", "value3": "Value 3", "value3Placeholder": "Enter content for Value 3", "value4": "Value 4", "value4Placeholder": "Enter content for Value 4", "viewField": "View Fields", "visibleError": "Error parsing visible function for form item [{0}]: {1}", "warehouse": "Component Management", "warnRule": "<PERSON><PERSON>", "waterLevelLine": "Watermark", "will": "Will", "write": "Write", "writeError": "Write Error", "wu": "None"}}