<template>
  <bs-dialog
    width="50%"
    :title="title"
    append-to-body
    class="test-dialog"
    :visible.sync="display"
    :confirm-button="{ text: $t('pa.flow.runTest') }"
    @confirm="confirm"
  >
    <el-table :data="tableData" height="360" stripe border>
      <el-table-column prop="key" :label="$t('pa.flow.key14')" />
      <el-table-column prop="value" :label="$t('pa.flow.key15')">
        <template v-slot="scope">
          <el-input v-model="scope.row.value" />
        </template>
      </el-table-column>
    </el-table>
    <div class="test-result">
      {{ $t('pa.flow.testResult') }}：<span class="test-result--red">{{ result }}</span>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { debounce } from 'lodash';
import { post } from '@/apis/utils/net';
import { URL_TEST_MONITOR_RULE } from '@/apis/commonApi';

@Component
export default class MonitorRuleTestDialog extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @PropSync('show', { default: false }) display!: boolean;

  private result = '';
  private tableData: any = [];
  private confirm = debounce(this.handleConfirm, 1200);

  get isOldData() {
    return typeof this.data.mvel === 'string';
  }
  get title() {
    return this.data.name || this.$t('pa.flow.test');
  }

  created() {
    this.isOldData ? this.parseOldData() : this.parseNewData();
  }

  parseOldData() {
    if (typeof this.data.mvel !== 'string') return;
    const matchArr = this.data.mvel.replaceAll(',', ', ').match(/[#\w,_,$]+#/g);
    if (Array.isArray(matchArr)) {
      this.generateTableData(matchArr.map((el) => el.replaceAll('#', '')));
    }
  }
  parseNewData() {
    if (Array.isArray(this.data.mvel.conditions)) {
      const result: any[] = [];
      this.data.mvel.conditions.forEach(({ funcArgs }) => {
        if (Array.isArray(funcArgs)) {
          funcArgs.forEach(({ key }) => result.push(key.replaceAll('#', '')));
        }
      });
      this.generateTableData(result);
    }
  }
  generateTableData(keysArr: any[] = []) {
    if (!keysArr.length) return;
    this.tableData = Array.from(new Set(keysArr)).map((key) => ({ key, value: '' }));
  }
  async handleConfirm() {
    try {
      console.log(JSON.parse(this.handleParams()));
      const { success, data, error } = await post(URL_TEST_MONITOR_RULE, {
        ...(this.isOldData ? { mvel: this.data.mvel } : { newRule: { ...this.data } }),
        params: this.handleParams()
      });
      if (!success) this.$tip.error(error);
      this.result = success ? data : false;
    } catch (e) {
      console.log(e);
    }
  }
  handleParams() {
    return JSON.stringify(
      this.tableData.reduce((pre: any, next: any) => {
        pre[next.key] = next.value;
        return pre;
      }, {})
    );
  }
}
</script>

<style lang="scss" scoped>
$resultHeight: 45px;
.test {
  &-dialog {
    ::v-deep .bs-dialog {
      .el-dialog__body {
        padding: 0 30px;
        overflow: hidden;
      }
      &-footer {
        text-align: center;
      }
    }
  }
  &-result {
    height: $resultHeight;
    line-height: $resultHeight;
    font-weight: bold;
    &--red {
      color: #f56c6c;
    }
  }
}
</style>
