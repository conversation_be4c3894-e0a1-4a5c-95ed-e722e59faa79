<template>
  <div style="height: 100%" class="card_content">
    <div class="tab-title">
      <div class="title-text" style="width: 10%">
        {{ cardTitle }}
      </div>
      <div style="width: 90%">
        <el-button
          type="primary"
          style="float: right"
          icon="el-icon-refresh-right"
          @click="getListData"
        />
        <el-button
          v-if="hasFeatureAuthority('PA.ELE.PKG.ADD')"
          type="primary"
          class="default-btn"
          style="float: right; margin-right: 10px"
          @click="add"
        >
          新建
        </el-button>

        <add-edit
          :visible="dialogVisible"
          :title="dialogTitle"
          :data="recordData"
          :form-loading="formLoading"
          @close="closeDialog"
        />
      </div>
    </div>
    <div style="height: 100%">
      <base-table
        v-loading="tableLoading"
        :height="'78%'"
        :table-data="tableData"
        :table-config="tableConfig"
        @handleCurrentChange="handleCurrentChange"
        @handleSortChange="handleSortChange"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import {
  URL_INSTALLATIONPACKAGE_LIST,
  URL_INSTALLATIONPACKAGE_DELETE,
  URL_INSTALLATIONPACKAGE_DOWNLOAD,
  URL_INSTALLATIONPACKAGE_FIND
} from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
import * as _ from 'lodash';
enum InstallPkgType {
  PROBE,
  ZOOKEEPER,
  KAFKA,
  AEROSPIKE,
  STREAMCUBE,
  SMARTENGINE
}
const typeNameArray = ['探针', 'Zookeeper', 'Kafka', 'Aerospike', '计算引擎', '决策引擎'];
@Component({
  components: {
    'base-table': () => import('@/components/base-table.vue'),
    'add-edit': () => import('../modals/add-edit.vue')
  }
})
export default class InstallationPackage extends PaBase {
  @Prop({ default: '' })
  pkgType!: string;
  formLoading = false;
  dialogVisible = false;
  dialogTitle = '新建';
  recordData: any = {};
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 5, currentPage: 1, total: 1 },
    sortData: {
      createTime: 'DESC'
    }
  };
  tableData: ITableData = {
    columnData: [],
    tableData: []
  };
  tableConfig: ITableConfig = {
    width: 110,
    columnsExtend: {
      edit: [
        {
          tipMessage: '编辑',
          handler: this.edit.bind(this),
          iconfont: 'icon-bianji',
          hasAuthority: this.hasAuthority.bind(this, this.hasFeatureAuthority('PA.ELE.PKG.EDIT'))
        },
        {
          tipMessage: '删除',
          handler: this.delete.bind(this),
          iconfont: 'icon-shanchu',
          hasAuthority: this.hasAuthority.bind(this, this.hasFeatureAuthority('PA.ELE.PKG.DELETE'))
        },
        {
          tipMessage: '下载',
          handler: this.download.bind(this),
          iconfont: 'icon-xiazai',
          hasAuthority: this.hasFeatureAuthority('PA.ELE.PKG.DOWNLOAD')
        }
      ]
    }
  };
  fetchList: any = _.debounce(this.getListData, 500);
  cardTitle = '';

  hasAuthority(hasRole: any, row: any) {
    return !_.isEmpty(_.toString(row.dataLevelType)) && row.dataLevelType !== 'PARENT' && hasRole;
  }
  handleCurrentChange(val) {
    this.searchObj.pageData.currentPage = val;
    this.fetchList();
  }

  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.fetchList();
  }
  add() {
    this.dialogVisible = true;
    this.dialogTitle = '新建';
    this.recordData = {
      type: this.pkgType
    };
  }
  edit(row: any) {
    this.dialogTitle = '编辑';
    this.formLoading = true;
    const params = { id: row.id };
    this.doGet(URL_INSTALLATIONPACKAGE_FIND, { params: params }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.recordData = resp.data;
        this.dialogVisible = true;
      });
      this.formLoading = false;
    });
  }

  delete(row: any) {
    this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        this.tableLoading = true;
        const ids: any[] = [];
        ids.push(row.id);
        this.doDelete(URL_INSTALLATIONPACKAGE_DELETE, { data: ids }).then((resp: any) => {
          this.parseResponse(resp, () => {
            this.getListData();
          });
          this.tableLoading = false;
        });
      })
      .catch(() => {
        return true;
      });
  }
  download(row: any) {
    window.location.href =
      Vue.axios.defaults.baseURL + URL_INSTALLATIONPACKAGE_DOWNLOAD + '?id=' + row.id;
  }
  closeDialog(needFresh: any) {
    if (needFresh === true) {
      this.getListData();
    }
    this.dialogVisible = false;
  }

  getListData() {
    this.tableLoading = true;
    this.doPost(URL_INSTALLATIONPACKAGE_LIST + '/' + this.pkgType, this.searchObj).then(
      (resp: any) => {
        this.parseResponse(resp, () => {
          this.tableData = {
            ...resp.data
          };
        });
        this.tableLoading = false;
      }
    );
  }

  public handleList() {
    this.cardTitle = typeNameArray[InstallPkgType[this.pkgType]];
    this.getListData();
  }
  created() {
    this.handleList();
  }
}
</script>

<style lang="scss" scoped>
.card_content {
  margin: 10px 20px 0;
  border: 1px solid #e2e2e2;
  background: #ffffff;
}
.table_content {
  height: calc(100% - 100px);
}
</style>
