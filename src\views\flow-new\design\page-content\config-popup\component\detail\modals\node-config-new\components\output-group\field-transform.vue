<template>
  <div class="field-transform">
    <div class="field-transform__header">
      <span>{{ $t('pa.flow.fieldTransMethod') }}</span>
      <bs-select v-model="type" :options="options" class="select" @change="handleChange" />
      <span v-if="isCSV">{{ $t('pa.flow.splitStr') }}</span>
      <el-input v-if="isCSV" v-model="separator" :placeholder="$t('pa.flow.placeholder18')" style="width: 220px" />
    </div>
    <el-form ref="form" :model="formData" :rules="rules" class="field-transform__content">
      <el-form-item v-if="isJSON" prop="codeData">
        <bs-code ref="code" :value="formData.codeData" :read-only="false" :extra-style="{ height: '256px' }" />
      </el-form-item>
      <el-form-item v-if="isCSV" prop="csvData">
        <el-input
          v-model="formData.csvData"
          type="textarea"
          :autosize="{ minRows: 14 }"
          :placeholder="$t('pa.placeholder.input')"
          style="width: 100%; height: 100%"
        />
      </el-form-item>
    </el-form>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import _ from 'lodash';
import i18n from '@/i18n';
// 校验是都为json可解析的对象
const validateJsonVaild = (rule, value, callback) => {
  const errorMsg = i18n.t('pa.flow.msg87') as string;
  try {
    const str = JSON.parse(value);
    if (typeof str !== 'object') {
      callback(new Error(errorMsg));
    } else {
      callback();
    }
  } catch (err) {
    callback(new Error(errorMsg));
  }
};
@Component
export default class FieldTransform extends Vue {
  @Prop() customFields!: any;
  // 字段转换方式
  type = 'JSON';
  options = [
    {
      label: this.$t('pa.flow.jsonTtrans'),
      value: 'JSON'
    },
    {
      label: this.$t('pa.flow.csvTrans'),
      value: 'CSV'
    }
  ];
  separator = ''; // 分隔符
  formData: { codeData?: string; csvData: string } = {
    codeData: '{"key1":"value","key2":[1,2],"key3":{"key4":"xx"}}',
    csvData: ''
  };
  rules: { codeData?: Base[]; csvData: Base[] } = {
    codeData: [
      { required: true, message: this.$t('pa.flow.msg88') },
      { validator: validateJsonVaild, trigger: 'blur' }
    ],
    csvData: [
      { required: true, message: this.$t('pa.flow.msg89') },
      { validator: this.validateRepeat, trigger: 'blur' }
    ]
  };
  get isJSON() {
    return this.type === 'JSON';
  }
  get isCSV() {
    return this.type === 'CSV';
  }
  created() {
    if (!Array.isArray(this.customFields)) return;
    this.formData.codeData = JSON.stringify(
      this.customFields.reduce((res, item) => {
        res[item] = '';
        return res;
      }, {})
    );
    this.formData.csvData = this.customFields.join(',');
  }
  // 表单校验字段重复
  validateRepeat(rule, value, callback) {
    const keys = value.split(this.separator || ',') || [];
    const result = _.uniq(_.filter(keys, (v, i, a) => a.indexOf(v) !== i));
    if (result.length) {
      const msg = [...new Set(result)].map((r) => `{${r}}`).join(',');
      callback(new Error(this.$t('pa.flow.msg92', [msg])));
    } else {
      callback();
    }
  }
  // 切换方式时清除原先的校验结果
  handleChange() {
    (this.$refs.form as any).clearValidate();
  }
  // 确认
  public confirm(done) {
    (this.$refs.form as any).validate((valid) => {
      if (!valid) return done(false);
      const { codeData, csvData } = this.formData;
      const saveData = this.isJSON
        ? Object.keys(JSON.parse(codeData || '')) || []
        : csvData.split(this.separator || ',') || [];
      done(true, saveData);
    });
  }
}
</script>
<style lang="scss" scoped>
.field-transform__header {
  margin-bottom: 20px;
  > span {
    margin-right: 10px;
  }
  .select {
    width: 120px;
    margin-right: 20px;
  }
}
.field-transform__content {
  height: 320px;
}
</style>
