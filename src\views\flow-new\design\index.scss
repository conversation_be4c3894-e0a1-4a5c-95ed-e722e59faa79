.flow {
  display: flex;
  position: relative;
  background: #ffffff;
  height: calc(100vh - 106px);

  &-right {
    position: relative;
    flex: 1;
    overflow: hidden;
    height: calc(100vh - 106px);
    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 50px;
      border-bottom: 1px solid $--bs-color-border-lighter;
      padding: 0 20px;
    }
    .showFlowTest {
      height: calc(100% - 380px);
      overflow: hidden;
    }
    .test-reslt--oper {
      position: absolute;
      right: 65px;
      bottom: 15px;
      width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background: #fff;
      box-shadow: 0px 4px 6px 0px rgba(8, 8, 8, 0.1);
      border-radius: 50%;
      cursor: pointer;
    }
    &__empty {
      position: absolute;
      top: 0;
      bottom: 0;
      right: 0;
      left: 0;
      z-index: 101;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      background-color: #fff;
    }
  }
}
.design-project-box {
  height: calc(100vh - 250px);
  overflow: auto;
}
