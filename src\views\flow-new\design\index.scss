.flow {
  display: flex;
  position: relative;
  background: #ffffff;
  height: calc(100vh - 106px);

  &-left {
    width: 300px;
    &-header {
      display: flex;
      align-items: center;
      height: 50px;
      padding: 0 20px;
      border-bottom: 1px solid #f1f1f1;
      &__title {
        display: inline-block;
        width: 150px;
        font-size: 14px;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .el-dropdown-link {
        color: $--bs-color-text-placeholder;
        cursor: pointer;
      }
      &__oper {
        flex: 1;
        text-align: right;
        cursor: pointer;
        i {
          color: #757d86;
        }
      }
    }
    ::v-deep .el-tabs__header {
      margin: unset;
    }
    ::v-deep .el-tabs__nav {
      margin-left: 55px;
    }
    ::v-deep .el-tabs__item {
      padding: 0 43px;
    }
  }
  &-right {
    flex: 1;
    border-left: 1px solid $--bs-color-border-lighter;
    overflow: hidden;

    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 50px;
      border-bottom: 1px solid $--bs-color-border-lighter;
      padding: 0 20px;
    }
    .showFlowTest {
      height: calc(100% - 380px) !important;
    }
    .showNodeInfoDrawer {
      height: calc(100% - 350px) !important;
    }
  }
}
.design-project-box {
  height: calc(100vh - 250px);
  overflow: auto;
}
.hasBorder {
  ::v-deep .el-input__inner {
    border: 1px solid #e5e5e5;
  }
}
.noBorder {
  ::v-deep .el-input__inner {
    border: none;
  }
}
