<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">znode管理</div>
    </div>
    <div class="tab-content">
      <div class="continer">
        <div class="left">
          <div class="name">名称</div>
          <div class="tree">
            <el-input v-model="filterText" placeholder="请输入关键词" @input="filterTree" />
            <el-tree
              ref="deptTree"
              class="filter-tree"
              :data="treeData"
              current-node-key
              :props="defaultProps"
              accordion
              :filter-node-method="filterNode"
              @node-click="handleNodeClick"
            />
          </div>
        </div>
        <div class="right">
          <div class="name-info">数据信息</div>
          <el-form label-width="80px" style="width: 70%">
            <el-form-item label="Key:">
              <el-input v-model="nodePath" placeholder readonly />
            </el-form-item>
            <el-form-item label="Value:">
              <el-input
                v-model="nodeValue"
                type="textarea"
                placeholder
                readonly
                :autosize="{ minRows: 10, maxRows: 10 }"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Inject } from 'vue-property-decorator';
import { zookeeperTree, zookeeperNodeInfo } from '@/apis/serviceApi';
import _ from 'lodash';
import moment from 'moment';
@Component({
  components: {}
})
export default class NodeManager extends Vue {
  height = '300px';
  filterText = '';
  treeData: any = [];
  defaultProps: any = [];
  nodePath = '';
  nodeValue = '';
  @Inject('comParams') comParams;
  filterTree() {
    _.debounce((this.$refs.deptTree as any).filter(this.filterText), 500);
  }
  created() {
    this.loadData(this.comParams.NodeManager || {});
  }
  async loadData(params: any) {
    this.height = params.height;
    this.handleZookeeperTree();
  }
  async handleZookeeperTree() {
    const { data, success, msg } = await zookeeperTree(this.$route.query.id);
    if (success) {
      this.treeData = data;
    } else {
      this.$message.error(msg);
    }
  }
  filterNode(value, data) {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
  }

  async handleNodeClick(treeData) {
    this.nodePath = '';
    this.nodeValue = '';
    const { data, sucess, msg } = await zookeeperNodeInfo({
      id: this.$route.query.id,
      path: treeData.fullPath
    });
    if (sucess) {
      this.nodePath = treeData.label;
      this.nodeValue = data.data + '\n';
      for (const key in data.stat) {
        if (['ctime', 'mtime'].includes(key)) {
          data.stat[key] = moment(data.stat[key]).format('YYYY-MM-DD HH:mm:ss');
        }
        this.nodeValue += `${key} = ${data.stat[key]}\n`;
      }
    } else {
      this.$message.error(msg);
    }
  }
}
</script>
<style scoped>
.my-card_body ::v-deep .el-card__body {
  padding: 0px 0px 0px 0px;
}
.my-wrap-row {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
}
.my-wrap-col {
  border: 1px solid #000;
}
.my-wrap-tag {
  width: 350px;
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.continer {
  display: flex;
  border-top: 1px solid #f1f1f1;
}
.left {
  width: 33%;
}
.tree {
  padding: 18px 10px 0;
  height: 302px;
  overflow-y: auto;
}
.right {
  flex: 1;
  border-left: 1px solid #f1f1f1;
}
.name {
  line-height: 48px;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f1f1f1;
}
.name-info {
  margin-bottom: 12px;
  line-height: 49px;
  padding-left: 20px;
  font-size: 14px;
  font-weight: bold;
  background: #f9fbff;
}
.search {
  border-left: 1px solid #dcdfe6;
  cursor: pointer;
}
</style>
