<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">队列资源详情</div>
      <div class="bs-page__header-operation">
        <el-input v-model="searchObj.search" placeholder="输入队列名称" />
      </div>
    </div>
    <div class="tab-content" :style="{ height: height }">
      <base-table
        v-loading="tableLoading"
        :height="'100%'"
        :table-data="queueResourceData"
        :table-config="nodeTableConfig"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject, Watch } from 'vue-property-decorator';
import { URL_FLINK_QUEUELIST } from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
import baseTable from '@/components/base-table.vue';
@Component({
  components: {
    baseTable
  }
})
export default class QueueResourceInfo extends PaBase {
  height = '300px';
  resRecord: any = {};
  tableLoading = false;
  private searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: {}
  };
  private queueResourceData: ITableData = {
    columnData: [],
    tableData: []
  };
  private nodeQueueResourceData: ITableData = {
    columnData: [],
    tableData: []
  };
  private nodeTableConfig: ITableConfig = {
    width: 80,
    columnsExtend: {
      edit: []
    }
  };
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  created() {
    this.loadData(this.comDetailRecord.val || {}, this.comParams.FlinkResPerJobQueueUseInfo || {});
  }
  getQueueList() {
    this.tableLoading = true;
    this.doPost(URL_FLINK_QUEUELIST + '?id=' + this.resRecord.id, {}).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.queueResourceData = {
          ...resp.data
        };
        this.nodeQueueResourceData = {
          ...resp.data
        };
      });
      this.tableLoading = false;
    });
  }
  async loadData(data: any, params: any) {
    this.resRecord = data;
    this.height = params.height;
    this.getQueueList();
  }
  @Watch('searchObj.search')
  watchSearch(val) {
    if (val !== '') {
      this.queueResourceData.tableData = [];
      this.nodeQueueResourceData.tableData.forEach((n) => {
        if (n.queue.indexOf(val) >= 0) {
          this.queueResourceData.tableData.push(_.cloneDeep(n));
        }
      });
    } else {
      this.queueResourceData = _.cloneDeep(this.nodeQueueResourceData);
    }
  }
}
</script>
<style scoped></style>
