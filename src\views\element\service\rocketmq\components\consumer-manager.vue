<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">消费查看</div>
      <div class="bs-detail__header-operation">
        <el-autocomplete
          v-model="topic"
          class="inline-input"
          :fetch-suggestions="querySearch"
          placeholder="请输入topic名称"
          style="width: 300px"
          clearable
          @select="handleSelect"
        />

        <el-autocomplete
          v-model="group"
          class="inline-input"
          :fetch-suggestions="querySearch2"
          placeholder="请输入消费者"
          style="width: 300px; margin: 0 10px"
          clearable
        />
        <el-button size="mini" type="primary" @click="handleProgressManage">查询</el-button>
      </div>
    </div>
    <div class="tab-content" :style="{ height: height }">
      <el-table v-loading="paDataLoading" :data="pmTableData" size="mini" :height="'100%'">
        <el-table-column prop="topic" label="名称" />
        <el-table-column prop="partition" label="partition" />
        <el-table-column prop="group" label="group" />
        <el-table-column prop="offset" label="offset" />
        <el-table-column prop="logEndSize" label="logEndSize" />
        <el-table-column prop="lag" label="lag" />
      </el-table>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject } from 'vue-property-decorator';
import {
  URL_RES_DETAIL_ROCKETMQ_ALLTOPICS,
  URL_RES_DETAIL_ROCKETMQ_DESCRIBEGROUP,
  URL_RES_DETAIL_ROCKETMQ_GROUPS
} from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {}
})
export default class ConsumerManager extends PaBase {
  height = '300px';
  resRecord: any = {};
  pmTableData: any[] = [];
  paDataLoading = false;
  topic = '';
  group = '';
  allTopics: any[] = [];
  allGroups: any[] = [];
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  created() {
    this.loadData(this.comDetailRecord.val || {}, this.comParams.ComsumerManager || {});
  }
  querySearch2(queryString, cb) {
    const t = this.allGroups;
    const results = queryString ? t.filter(this.createFilter(queryString)) : t;
    cb(results);
  }

  querySearch(queryString, cb) {
    const t = this.allTopics;
    const results = queryString ? t.filter(this.createFilter(queryString)) : t;
    cb(results);
  }
  createFilter(queryString) {
    return (restaurant) => {
      return _.toLower(restaurant.value).indexOf(_.toLower(queryString)) >= 0;
    };
  }
  handleSelect(item) {
    this.getAllGroup(item.value);
  }
  handleProgressManage() {
    if (this.topic === '') {
      this.$message.error('请输入topic名称');
      return;
    }
    if (this.group === '') {
      this.$message.error('请输入消费者名称');
      return;
    }
    this.paDataLoading = true;
    this.doGet(URL_RES_DETAIL_ROCKETMQ_DESCRIBEGROUP, {
      params: {
        brokers: this.resRecord.url,
        group: this.group,
        topic: this.topic
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.pmTableData = [];
        // 初始化
        this.pmTableData = resp.data;
      });
      this.paDataLoading = false;
    });
  }

  getAllTopic() {
    this.doGet(URL_RES_DETAIL_ROCKETMQ_ALLTOPICS, {
      params: {
        id: this.$route.query.id
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        resp.data.forEach((n) => {
          this.allTopics.push({
            value: n
          });
        });
      });
    });
  }

  getAllGroup(topic: string) {
    this.doGet(URL_RES_DETAIL_ROCKETMQ_GROUPS, {
      params: {
        id: this.$route.query.id,
        topic
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        resp.data.forEach((n) => {
          this.allGroups.push({
            value: n
          });
        });
      });
    });
  }
  async loadData(data: any, params: any) {
    this.resRecord = data;
    this.height = params.height;
    this.getAllTopic();
  }
}
</script>
<style scoped>
.my-card_body ::v-deep .el-card__body {
  padding: 0px 0px 0px 0px;
}
.my-wrap-row {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
}
.my-wrap-col {
  border: 1px solid #000;
}
.my-wrap-tag {
  width: 350px;
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
