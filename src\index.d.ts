import type { ElMessage } from 'bs-ui-pro/types/message';
declare module 'vue/types/vue' {
  interface Tip {
    (config: any): (config: any) => void;
    success: (msg: any, title?: string = '') => void;
    warning: (msg: any, title?: string = '') => void;
    info: (msg: any, title?: string = '') => void;
    error: (msg: any, title?: string = '') => void;
    errorPro: (errorInfo: ErrorProInfo) => void;
  }
  interface Vue {
    $tip: Tip;
    $saveConfirm: any;
    $message: ElMessage;
    isEn: boolean;
    $t: (key: string, params: any = []) => string;
    $verifyDd: any;
  }
}
declare global {
  interface Base {
    [propName: string]: unknown;
  }
  interface MenuItem {
    icon?: string;
    index: string;
    title: string;
    children?: MenuItem[];
  }
  interface ErrorProItem extends Base {
    errorInfo: string;
  }
  interface ErrorProInfo {
    msgType: 'SIMPLE' | 'DETAIL';
    msg: string;
    data: ErrorProItem[];
  }
  interface IJobData {
    clusterId: string;
    content: any;
    createTime: number;
    createdBy: string;
    dataLevelType: string;
    id: string;
    jobName: string;
    jobStatus: string;
    jobType: string;
    jobVersion: string;
    memo: string;
    orgId: string;
    orgPath: string;
    projectId: string;
    projectName: string;
    updateTime: number;
    updatedBy: string;
    properties?: string;
  }

  interface IState {
    others: any;
    /** 用户信息 */
    userInfo: IUserInfo;
    /** 页面loading效果 */
    spinShow: boolean;
    /** 流程详情 */
    job: {
      data: IJobData;
      originJobContent: string;
      errorNodes: any[];
    };
    /** 流程DAG信息，用来比较DAG是否更改 */
    originJobContent: string;
    /** 左侧组件栏 */
    componentList: any;
    /** 可用的字段 */
    assetFields: any;
    /** 可用的方法 */
    assetFuncs: any;
    /** 可用的资源 */
    resTypes: any;
    /* 左侧菜单栏是否展开 */
    isCollapse: boolean;
    /* 流程设计列表批量操作后是否需要重新加载右侧画布 */
    reloadJob: boolean;
    // tab对应的路由
    tabTitles: any;
    // 打开的路由数组
    app: { fullPathList: any; environment: string; loginUrl: string; token: string };
    enableSql: boolean; // 根据配置文件中的内容判断是否开放SQL流程能力
    isFuseMode: boolean; // 融合模式
    encryptionMethod: string;
    publicKey: string;
    udjStatus: boolean; // 是否开启自定义JAR
    udjDfsStatus: boolean; // 是否使用了分布式文件系统
  }

  interface AppModule {
    lineageDimensionSplit: string;
    encryptEnable: boolean;
    platformName: string;
    menu: any[];
    loginUrl: string;
    iv: string;
    publicKey: string;
    encryptionMethod: string;
    isCloud: boolean;
    enableSql: boolean;
    enableJar: boolean;
    enableDFS: boolean;
    monitorUrl?: string;
    // eslint-disable-next-line @typescript-eslint/ban-types
    portalConfig?: any | {};
    isCollapse: boolean;
    projectLevelsNumber: number;
    i18nMap: Record<string, string>;
  }

  interface UserModule {
    title?: string;
    username: string;
    realname: string;
    orgPath?: string;
    orgId: string;
    orgName?: string;
    authorities: string[];
    pageSize: number;
    mobile?: string;
    [propName: string]: unknown;
  }
  interface AssetModule {
    assetFields: any[];
    assetFuncs: any[];
  }

  interface IPaginationConfig {
    layout?: string;
  }

  interface ITableConfig {
    type?: string; // 对应列的类型。如果设置了 selection 则显示多选框；如果设置了 index 则显示该行的索引（从 1 开始计算）；如果设置了 expand 则显示为一个可展开的按钮，可不传
    expandProps?: string[];
    width: number;
    columnsExtend?: {
      edit?: Array<{
        iconfont: string;
        tipMessage: string;
        // eslint-disable-next-line @typescript-eslint/ban-types
        hasAuthority?: boolean | Function;
        // eslint-disable-next-line @typescript-eslint/ban-types
        handler: Function;
      }>;
    };
  }

  interface ITableData {
    columnData: IColumn[];
    tableData: any[];
    pageData?: IPageData;
  }

  interface IColumn {
    prop: string;
    label: string;
    value?: string;
    sortable?: boolean; // 是否需要排序，默认为false，可不传， 不传的时候则不排序
    dataType?: string; // 数据的类型，如果是Date类型，前端将后端返回的时间戳转为‘YYYY-MM-DD HH:mm:ss’的形式，可不传
    width?: number; // 列的宽度，可不传，不传时宽度则为动态的
    show?: boolean; // 是否显示列
  }

  interface IPageData {
    pageSize: number;
    currentPage: number;
    total: number;
  }

  interface ISearchObj {
    search: any;
    queueName?: string;
    startTime?: string;
    endTime?: string;
    pageData: IPageData;
    sortData?: any;
  }

  interface IFormArray {
    width?: number;
    rowData?: Array<{
      label: string;
      key?: string;
      value: string;
    }>;
    textAreaData?: Array<{
      label: string;
      value: string;
    }>;
  }

  type IProTable = (Vue | Element) & {
    runRequest: () => void;
  };
}
