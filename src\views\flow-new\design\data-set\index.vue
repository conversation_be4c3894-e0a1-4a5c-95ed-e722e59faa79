<template>
  <div class="data-set">
    <div class="data-set-header" :class="{ 'data-set-header__us': isEn }">
      <el-radio-group v-model="activeTab" size="mini" @change="fetchList()">
        <el-radio-button :label="TABS.TABLE_MGR" />
        <el-radio-button :label="TABS.ORIGIN_TABLE" />
      </el-radio-group>
      <data-set-search
        ref="searchRef"
        :is-table-mgr="isTableMgr"
        :is-expand-origin-res="isExpandOriginRes"
        :select-val.sync="selectVal"
        :table-mgr-search.sync="tableMgrSearch"
        :origin-table-search.sync="originTableSearch"
        :is-select-val-change.sync="isSelectValChange"
        @search="fetchList"
        @refresh="handleRefresh"
      />
    </div>
    <div v-loading="loading" class="data-set-content">
      <bs-collapse v-if="tableList.length" v-model="activeCollapse" accordion @change="handelCollapseChange">
        <data-set-collapse-item
          v-for="data in tableList"
          ref="dataSetCollapseRef"
          :key="data.id"
          :data="data"
          :disabled="disabled"
          :is-table-mgr="isTableMgr"
          :conn-options="connOptions"
          @viewTableInfo="handleView"
          @getSqlByConnInfo="(data) => $emit('click', data)"
          @click="handleClick"
        />
      </bs-collapse>
      <bs-empty v-else-if="!loading" />
    </div>
    <!-- 单击表右侧查看图标（悬浮时展示），展示表信息、字段信息 -->
    <table-info-drawer
      :id="tableId"
      :is-table="isTableMgr"
      :show.sync="showTableInfoDrawer"
      :name="tableName"
      :type="resType"
      :left="drawerLeft"
    />
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { cloneDeep, debounce } from 'lodash';
import {
  getTableMgrList,
  getOriginTableGroup,
  getConnInfoAndTableUseFor,
  getTableMgrSql,
  getOriginTableSql
} from '@/apis/sqlApi';
import DataSetSearch from './components/data-set-search.vue';
import DataSetCollapseItem from './components/data-set-collapse.vue';
import { CollapseItem, ConnInfoOptions } from '../interface/data-set';

@Component({
  name: 'DataSet',
  components: {
    DataSetSearch,
    DataSetCollapseItem,
    TableInfoDrawer: () => import('./components/table-info-drawer/index.vue')
  }
})
export default class DataSet extends Vue {
  @Prop() flowStatus!: string;

  TABS = {
    TABLE_MGR: this.$t('pa.flow.tableMgr'),
    ORIGIN_TABLE: this.$t('pa.flow.sourseTable')
  };
  activeTab = this.TABS.TABLE_MGR;
  activeCollapse = '';
  sonActiveCollapse = ''; // 记录打开的二级菜单，用于原始表前端搜索还原展开状态
  loading = false;
  tableMgrSearch = ''; // 表管理-搜索字段
  originTableSearch = ''; // 原始表-搜索字段
  selectVal: 'SERVICE' | 'TABLE' = 'SERVICE'; // 原始表搜索-选中的搜索类型
  isSelectValChange = false;
  tableList: any = [];
  originTableList: any = []; // 缓存一份【原始表】原始数据，用于原始表-前端搜索
  originTableMap: any = new Map(); // 缓存一份【表管理】原始数据，用于表管理-前端优化，只展示一组展开的数据
  isExpandOriginRes = false; // 原始表是否展开了服务下的所有表
  connOptions: ConnInfoOptions[] = []; // 连接器信息

  // 表信息相关
  showTableInfoDrawer = false;
  tableId = '';
  tableName = '';
  resId = '';
  resType = '';
  fetchList = debounce(this.handleFetchList, 500);
  drawerLeft = '0px';
  get disabled() {
    return this.flowStatus !== 'DEV';
  }

  get isTableMgr() {
    return this.activeTab === this.TABS.TABLE_MGR;
  }

  @Watch('activeTab')
  async handleTabChange(val) {
    if (val === this.TABS.ORIGIN_TABLE) {
      await this.getOriginTableGroup();
      this.resetDataTreeCollapse(); // 重置原始表的展开状态
      this.sonActiveCollapse = '';
      if (this.selectVal === 'SERVICE') {
        this.handleOriginTableFrontSearch();
      } else {
        this.sonActiveCollapse = this.originTableSearch = '';
      }
    }
  }

  async created() {
    this.getTableMgrList();
  }

  async getTableMgrList() {
    this.tableList = [];
    this.loading = true;
    const { data = [], success, msg } = (await getTableMgrList(this.tableMgrSearch)) || [];
    if (success) {
      this.loading = false;
      if (!data.length) return;
      this.handleTableMgrRender(cloneDeep(data));
      this.tableList = data.map((el, index) => {
        if (index === 0) el.loading = true; // 增加交互体感
        el.children = [];
        return el;
      });
      return;
    }
    this.$message.error(msg);
  }

  // 解决从【原始表】切换到【表管理】卡顿问题
  handleTableMgrRender(data) {
    data.forEach((el) => {
      this.originTableMap.set(el.id, el.children); // 构造前端id->children数据
    });
    setTimeout(() => {
      const keys: any = Array.from(this.originTableMap.keys());
      this.activeCollapse = keys[0] || '';
      this.tableList[0].loading = false;
      this.handelCollapseChange(); // 手动展开collapse
    }, 100);
  }

  async getOriginTableGroup() {
    this.tableList = [];
    this.loading = true;
    const { data = [], success, msg } = (await getOriginTableGroup()) || {};
    if (success) {
      this.loading = false;
      this.activeCollapse = data[0]?.id || '';
      this.tableList = cloneDeep(data);
      this.originTableList = cloneDeep(data);
      return;
    }
    this.$message.error(msg);
  }

  // 表管理、原始表搜索
  async handleFetchList() {
    if (this.isTableMgr) {
      this.getTableMgrList();
      return;
    }
    if (this.isSelectValChange) {
      this.resetDataTreeCollapse(); // 重置原始表的展开状态
      this.sonActiveCollapse = '';
      this.isSelectValChange = false;
      await this.getOriginTableGroup();
    }
    this.handleOriginTableFrontSearch();
  }

  async handleRefresh() {
    // 原始表搜索类型为【表】时，刷新则重置原始表搜索条件
    if (!this.isTableMgr && this.selectVal === 'TABLE') {
      this.originTableSearch = '';
      (this.$refs.searchRef as any).originTableSearch = '';
    }
    await this.fetchList();
    this.$message.success(this.$t('pa.tip.refreshSuccess'));
  }

  handelCollapseChange() {
    if (this.isTableMgr && !this.activeCollapse) return;
    if (this.isTableMgr && this.activeCollapse) {
      this.tableList.forEach((el) => {
        el.children = this.activeCollapse === el.id ? this.originTableMap.get(el.id) : [];
      });
      return;
    }
    this.resetDataTreeCollapse();
  }

  // 父组件切换collapse时，重置子组件的展开状态
  resetDataTreeCollapse() {
    this.isExpandOriginRes = false;
    (this.$refs.dataSetCollapseRef as any).forEach((el) => {
      el.activeCollapse = '';
    });
  }

  // 原始表搜索：前端过滤
  handleOriginTableFrontSearch() {
    this.tableList = (this.selectVal === 'SERVICE' ? this.getOriginTableByService() : this.getOriginTableByTable()) || [];
    this.handleCollpseByFrontSearch();
  }

  // 原始表-前端搜索-根据服务名搜索
  getOriginTableByService() {
    const originData = cloneDeep(this.originTableList);
    const typeResMap: any = {}; // 用于存放符合搜索条件的 {类型: 服务[]}
    originData.forEach((el) => {
      const arr: any = [];
      el.children.forEach((item) => {
        if (item.name.toLocaleLowerCase().includes(this.originTableSearch.toLocaleLowerCase())) {
          arr.push(item);
          typeResMap[el.id] = arr;
        }
      });
    });
    const typeIds = Object.keys(typeResMap);
    const result: any = originData
      .filter((el) => typeIds.includes(el.id))
      .map((el) => {
        el.children = typeResMap[el.id];
        return el;
      });
    return result;
  }

  // 原始表-前端搜索-根据表名搜索
  getOriginTableByTable() {
    try {
      const originData = cloneDeep(this.originTableList);
      if (!this.originTableSearch) return originData;
      const openedType = originData.filter(({ id }) => id === this.activeCollapse);
      openedType[0].children = (openedType[0].children || []).filter((el) => el.children.length);
      const nodes = (openedType[0].children[0].children || []).filter((el) =>
        el.name.toLocaleLowerCase().includes(this.originTableSearch.toLocaleLowerCase())
      );
      openedType[0].children[0].children = nodes;
      return nodes.length ? openedType : [];
    } catch (e) {}
  }

  // 前端搜索后，还原展开状态
  async handleCollpseByFrontSearch() {
    if (!this.sonActiveCollapse) return;
    await this.$nextTick();
    ((this.$refs.dataSetCollapseRef as any) || []).forEach((el: any) => {
      el.data.children.forEach((item) => {
        if (item.id === this.sonActiveCollapse) {
          el.activeCollapse = this.sonActiveCollapse;
        }
      });
    });
  }

  // 点击查看图标：展示详情
  handleView(data) {
    this.tableId = this.isTableMgr ? data.id : data.resId;
    this.tableName = data.name;
    this.resType = data.resType; // 原始表需要类型
    const { x, width } = this.$el.getBoundingClientRect();
    this.drawerLeft = x + width + 'px';
    this.showTableInfoDrawer = true;
  }

  // 单击表名：自动生成sql代码
  async handleClick(table: CollapseItem) {
    this.showTableInfoDrawer = false; // 切换表的时候，关闭表信息弹窗
    this.tableId = this.isTableMgr ? table.id : '';
    this.resId = this.isTableMgr ? '' : table.resId || '';
    this.tableName = table.name;
    if (this.disabled) return; // 非开发状态不可以插入代码
    // 选择连接器和表用途
    if (table.needConfig) {
      const { data, success, msg } = await getConnInfoAndTableUseFor(
        this.isTableMgr ? table.id : '',
        this.isTableMgr ? '' : table.resType
      );
      if (success) {
        this.connOptions = data;
        return;
      }
      this.$message.error(msg);
      return;
    }
    this.getAutoSql(table);
  }

  // 直接获取sql模板，不需要进行连接器配置
  async getAutoSql(tableInfo: CollapseItem) {
    const getParams = (data: any) => {
      return this.isTableMgr
        ? { tableId: data.id, jobId: this.$route.query?.flowId }
        : {
            id: data.resId,
            tableName: data.name,
            jobId: this.$route.query?.flowId,
            resType: tableInfo.resType.toUpperCase()
          };
    };
    const params = getParams(tableInfo);
    const loading = this.$loading({ lock: true, text: this.$t('pa.flow.msg28') });
    // 请求后端接口拿到自动生成的sql
    const { data, success, msg } = this.isTableMgr ? await getTableMgrSql(params) : await getOriginTableSql(params);
    loading.close();
    if (success) {
      this.$emit('click', this.$store.getters.decrypt(data));
      return;
    }
    this.$message.error(msg);
  }
}
</script>
<style lang="scss" scoped>
.data-set {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #fff;
  &-header {
    padding: 12px 20px;
    border-bottom: 1px solid #f1f1f1;
    .el-radio-group {
      width: 100%;
      .el-radio-button {
        width: 50%;
      }
    }
    ::v-deep .el-radio-button .el-radio-button__inner {
      display: inline-block;
      width: 100%;
    }
    &__us {
      padding: 12px 10px;
    }
  }
  &-content {
    height: calc(100% - 103px);
    overflow: auto;
  }
  ::v-deep .el-collapse {
    width: 100%;
    border-top: 0;
    border-bottom: 0;
    .el-collapse-item__wrap {
      border-bottom: inherit;
    }
    .el-collapse-item:last-child {
      margin-bottom: 0px;
    }
    .el-collapse-item__header.is-active {
      height: 50px;
      line-height: 50px;
      font-size: 14px;
      color: #000;
      border-bottom: 1px solid $--bs-color-border-lighter;
      background-color: $--bs-color-background-light;
    }
    .data-set-collapse--recursion {
      .el-collapse-item__header {
        border-bottom: inherit;
      }
      .data-set-collapse--group {
        color: $--bs-color-text-secondary;
      }
      .el-collapse-item__header.is-active {
        background-color: inherit;
      }
      .el-collapse-item__title {
        padding-left: inherit;
      }
      .el-collapse-item__arrow {
        margin: inherit;
      }
      .el-collapse-item__content {
        padding: inherit;
      }
    }

    .el-collapse-item__title {
      padding-left: 20px;
    }
    .el-collapse-item__arrow {
      margin: 0 20px 0 10px;
    }
    .el-collapse-item__content {
      padding: 0 20px;
    }
  }
}
</style>
