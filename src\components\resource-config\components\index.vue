<script lang="tsx">
import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator';
import cloneDeep from 'lodash/cloneDeep';
import cookies from 'js-cookie';
import { TEMPLATES_CONFIG, FLOW_DEFAULT_CONFIG, NO_CHECK_POINT, STRATEGY_MAPPING } from '../config';
import ElForm from 'bs-ui-pro/packages/form';

const jobTypeMap: any = {
  PROCESSFLOW: 'DataStream',
  FLINK_SQL: 'SQL',
  UDJ: 'jar'
}; // 流程类型

@Component({
  components: {
    FlowInfo: () => import('./info.vue'),
    BaseConfig: () => import('./base.vue'),
    AdvancedConfig: () => import('./advanced.vue'),
    CustomConfig: () => import('./custom.vue'),
    ModeConfig: () => import('./mode.vue')
  }
})
export default class ResourceConfig extends Vue {
  @Prop({ default: () => FLOW_DEFAULT_CONFIG() }) data!: any;
  @Prop({ default: true }) showInfo!: boolean; // 控制是否显示流程信息
  @Ref('formRef') readonly formRef!: ElForm;
  @Ref('customRef') readonly customRef: any;

  private activeList: string[] = [];
  private formData: any = FLOW_DEFAULT_CONFIG();

  get isCloud() {
    return this.formData?.clusterType === 'CLOUD';
  }

  get isFlinkSql() {
    return this.formData.jobType === 'SQL';
  }

  get disabled() {
    return ['INPROD', 'PROD'].includes(this.data?.jobStatus);
  }

  @Watch('data', { immediate: true })
  handleDataChange() {
    console.log(this.data);
    this.formData = cloneDeep(this.data);
    this.formData.jobType = jobTypeMap[this.formData.jobType];
    this.formData.originalJobName = !this.formData.originalJobName ? this.formData.jobName : this.formData.originalJobName;
  }

  created() {
    // 根据 showInfo 决定默认展开的面板
    this.activeList = this.showInfo ? ['infoConfig', 'baseConfig'] : ['baseConfig'];
  }

  // 获取要渲染的下拉面板模板：流程信息、基本参数、高级参数、自定义参数
  getTemplate(value: string) {
    const { formData, isCloud, isFlinkSql } = this;
    const customConfigData = formData.configs === '[]' ? [] : formData.configs;
    return {
      infoConfig: <flow-info data={formData} />,
      baseConfig: <base-config is-cloud={isCloud} is-flink-sql={isFlinkSql} data={formData} />,
      advanceConfig: <advanced-config property={formData.property} data={formData} type={formData.jobType} />,
      customerConfig: <custom-config ref="customRef" data={customConfigData} type={formData.jobType} />
    }[value];
  }

  handleFormData(noProperties?: boolean) {
    const formData = cloneDeep(this.formData);
    /* 是否开启checkpoint属性：反选后删除checkpoint相关属性 */
    if (!formData.enableCheckPoint) {
      NO_CHECK_POINT.forEach((el) => delete formData[el]);
    }
    // 删除无数据权限时，前端拼接的clusterDetail参数
    formData.clusterDetail && delete formData.clusterDetail;
    formData.property?.clusterDetail && delete formData.property.clusterDetail;
    formData.configs = this.customRef.getData();
    const list = STRATEGY_MAPPING[formData.restartStrategy] || STRATEGY_MAPPING.all;
    list.push(this.isCloud ? 'queue' : 'namespace');
    list.forEach((key) => delete formData[key]);
    formData.jobType = this.data.jobType;
    // 批量操作来回切换流程时，不需要计算最终传给后端的数据
    if (noProperties) return formData;
    return {
      pajob: {
        ...{
          jobName: this.formData.jobName,
          memo: this.formData.memo,
          originalJobName: !this.formData.originalJobName ? this.formData.jobName : this.formData.originalJobName,
          suffix: this.formData.suffix,
          prefix: this.formData.prefix
        },
        properties: JSON.stringify(formData)
      },
      log4jFile: typeof formData.log4jFile === 'string' ? '' : formData.log4jFile
    };
  }

  /**
   * @description: public method：构造表单数据
   * @param {Booelean} NoProperties：当且仅当批量操作切换流程时，为true，此时不需要计算最终要传给后端的数据
   * @return {Promise}
   */
  public async getFormData(noProperties?: boolean) {
    try {
      await this.customRef.validateForm();
      await this.formRef.validate();
      return this.handleFormData(noProperties);
    } catch (e) {
      this.$tip.error('请检查配置是否有误');
      throw new Error();
    }
  }

  render() {
    const { activeList, formData, isFlinkSql, disabled } = this;
    const labelWidth = cookies.get('Language') === 'en-US' ? '260px' : '180px';

    // 根据 showInfo 过滤配置项
    const filteredTemplates = TEMPLATES_CONFIG.filter((item) => {
      if (item.value === 'infoConfig') {
        return this.showInfo;
      }
      return true;
    });

    return (
      <el-form
        ref="formRef"
        props={{ model: formData }}
        disabled={disabled}
        label-width={labelWidth}
        class="resource__container"
      >
        <el-collapse value={activeList}>
          {filteredTemplates.map((item) => {
            return (
              <el-collapse-item name={item.value}>
                <div slot="title" class="resource-title">
                  {item.label}
                </div>
                {this.getTemplate(item.value)}
              </el-collapse-item>
            );
          })}
        </el-collapse>
        <mode-config is-flink-sql={isFlinkSql} data={formData} />
      </el-form>
    );
  }
}
</script>
<style lang="scss" scoped>
.resource {
  &__container {
    ::v-deep .el-collapse {
      border-top: unset;
    }
    ::v-deep .resource-title {
      margin-left: 16px;
      padding-left: 6px;
      width: 200px;
      font-size: 14px;
      font-weight: 500;
      color: #444444;
      line-height: 20px;
      &::before {
        content: '';
        position: relative;
        left: 0;
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 8px;
        background: #ff9e2b;
        border-radius: 2px;
      }
    }
    ::v-deep .el-form-item {
      &__content {
        display: flex;
        align-items: center;

        .resource-item {
          display: inline-block;
          width: calc(100% - 30px);
          &__name {
            display: flex;
            &--prefix {
              margin-right: 10px;
            }
            &--suffix {
              margin-left: 10px;
            }
          }
          .el-select,
          .el-input-number,
          .el-date-editor {
            width: 100%;
          }
          .el-form-item {
            display: inline-block;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>
