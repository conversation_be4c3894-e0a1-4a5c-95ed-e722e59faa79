<template>
  <bs-dialog
    title="造数逻辑"
    :visible="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    width="550px"
  >
    <div class="title">字段信息</div>
    <div class="info">
      <div><span>字段: </span>{{ data.fieldName }}</div>
      <div><span>字段类型: </span>{{ data.fieldType }}</div>
    </div>
    <div class="title">造数逻辑</div>
    <el-form ref="ruleForm" :model="info" :rules="rules">
      <el-form-item :data="['type']" label="生成方式" prop="type" label-width="80px">
        <el-select v-model="info.type" style="width: 100%">
          <el-option
            v-for="item in logicInfo"
            :key="item.label"
            :label="item.label"
            :value="item.value"
          >
            {{ item.label }}
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="getBool" :data="['check']" label="枚举值" prop="check" label-width="80px">
        <el-checkbox-group v-model="info.check">
          <el-checkbox label="0" />
          <el-checkbox label="1" />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item
        v-if="getDouble"
        :data="['accuracy']"
        label="小数位"
        prop="accuracy"
        label-width="80px"
      >
        <el-input-number
          v-model="info.accuracy"
          :max="15"
          :min="0"
          :step="1"
          controls-position="right"
        />
      </el-form-item>
      <el-form-item
        v-if="(getInt || getDouble) && info.type === 'RANDOM'"
        :data="['random']"
        label="数据范围"
        prop="random"
        label-width="80px"
      >
        <div style="display: flex; justify-content: space-between">
          <el-input-number
            v-model="info.random"
            controls-position="right"
            :max="100"
            :min="0"
            :step="1"
          />
          ~
          <el-input-number
            v-model="info.random1"
            controls-position="right"
            :max="100"
            :min="0"
            :step="1"
          />
        </div>
      </el-form-item>
      <el-form-item
        v-if="getString && info.type === 'RANDOM'"
        :data="['rule']"
        label="字符类型"
        prop="rule"
        label-width="80px"
      >
        <el-select v-model="info.rule" style="width: 100%" placeholder="请选择">
          <el-option
            v-for="item in basicType"
            :key="item.label"
            :label="item.label"
            :value="item.value"
          >
            {{ item.label }}
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="(getInt || getDouble || getString) && info.type === 'ENUM'"
        :data="['enumVal']"
        label="枚举值"
        prop="enumVal"
        label-width="80px"
      >
        <el-input
          v-model="info.enumVal"
          type="textarea"
          placeholder="请输入枚举值，如有多个请用英文逗号分隔"
          maxlength="200"
          :rows="3"
          show-word-limit
        />
      </el-form-item>
      <el-form-item
        v-if="getDate"
        :data="['timeStamp']"
        label="时间范围"
        prop="timeStamp"
        label-width="80px"
      >
        <el-date-picker
          v-model="info.timeStamp"
          type="datetimerange"
          range-separator="至"
          :picker-options="pickerOptions"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog()">取消</el-button>
      <el-button type="primary" @click="submit('ruleForm')">确定</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { logicList, rules, pickerOptions, editInfo } from './index';
import { getJobEnums } from '@/apis/flowTestApi';
@Component({
  components: {}
})
export default class NumLogic extends PaBase {
  @Prop() data!: any;
  @Prop() visible!: boolean;
  @Prop() title!: string;
  @Prop() type!: string;
  rules: any = {
    ...rules,
    random: [
      { required: true, message: '请输入数据范围', trigger: 'change' },
      { validator: this.validateRound, trigger: 'change' }
    ]
  };
  info: any = {
    type: 'RANDOM',
    enumVal: '',
    check: [],
    accuracy: 3,
    random: 0,
    random1: 100,
    timeStamp: [new Date(), new Date('2022-02-17 23:59:59')]
  };
  logicList = logicList;
  // 字符类型
  basicType = [];
  pickerOptions = pickerOptions;
  validateRound(rule, value, callback) {
    if (this.info.random < 0 || !this.info.random1) {
      callback(new Error('请输入数据范围'));
    }
    if (this.info.random > this.info.random1) {
      callback(new Error('开始范围不能大于结束范围'));
    }
    callback();
  }

  activeTab = '';
  // 测试用例详细信息
  testDetail: any = { data: [] };
  //流程类型
  processName: any = [];

  get logicInfo() {
    const type = ['date', 'timestamp(3)', 'timestamp', 'bool', 'boolean'].includes(this.data.type);
    return type ? this.logicList.slice(0, 1) : this.logicList;
  }

  get getInt() {
    return ['tinyint', 'smallint', 'mediumint', 'int', 'bigint'].includes(this.data.type);
  }

  get getDouble() {
    return ['float', 'double'].includes(this.data.type);
  }

  get getBool() {
    return ['bool', 'boolean'].includes(this.data.type);
  }

  get getDate() {
    return ['date', 'timestamp(3)', 'timestamp'].includes(this.data.type);
  }

  get getString() {
    return this.data.type === 'string';
  }
  created() {
    this.getEnums();
    const { other } = this.data;
    if (other.type) {
      const { info } = editInfo(this.data, this.info);
      this.info = { ...info, type: other.type };
    }
  }
  async getEnums() {
    await getJobEnums().then((res) => {
      this.basicType = res.data.map((item) => {
        return {
          label: item,
          value: item
        };
      });
    });
  }
  closeDialog() {
    this.$emit('close');
  }

  submit(formName: string) {
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        let list = [];
        form.$children.forEach((item) => {
          list = list.concat(item.$attrs.data);
        });
        list.forEach((name: any) => {
          if (name.indexOf('check') !== -1) {
            this.data.other.rule = this.info.check.join();
          } else if (name.indexOf('random') !== -1) {
            this.data.other.random = `${this.info.random}, ${this.info.random1}`;
          } else if (name.indexOf('timeStamp') !== -1) {
            this.data.other.timeStamp = `${this.info.timeStamp[0].valueOf()}, ${this.info.timeStamp[1].valueOf()}`;
          } else {
            this.data.other[name] = this.info[name];
          }
        });
        const { type, other } = this.data;
        if (type === 'string') {
          other.type === 'ENUM' ? delete other.rule : delete other.enumVal;
        }
        this.$emit('submit', this.data);
        this.$emit('close');
      }
    });
  }
}
</script>
<style lang="scss" scoped>
.title {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: #444;
  position: relative;
  margin-bottom: 30px;
}
.title::before {
  content: '';
  width: 8px;
  height: 8px;
  background: #ff9c00;
  border-radius: 2px;
  position: absolute;
  left: -14px;
  top: 6px;
}
.info {
  display: flex;
  margin-bottom: 20px;
  div {
    width: 50%;
    span {
      color: #777;
      margin-right: 10px;
    }
  }
}
</style>
