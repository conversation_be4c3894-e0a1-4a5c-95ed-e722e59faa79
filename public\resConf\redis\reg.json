{"forms": [{"type": "input", "prop": "title", "label": "名称", "componentProps": {"maxlength": 30, "placeholder": "请输入名称", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入名称", "trigger": "blur"}, {"min": 2, "max": 30, "message": "长度在 2 到 30 个字符", "trigger": "blur"}]}, {"type": "select", "prop": "type", "label": "模式", "componentProps": {"placeholder": "请选择类型", "options": [{"value": "0", "label": "单点"}, {"value": "1", "label": "集群"}, {"value": "2", "label": "哨兵"}]}, "rules": [{"required": true, "message": "请选择类型", "trigger": "blur"}]}, {"type": "textarea", "prop": "url", "label": "服务地址", "componentProps": {"rows": 3, "maxlength": 1024, "placeholder": "请输入服务地址，多个地址以逗号分隔，兼容Ipv4和Ipv6"}, "rules": [{"required": true, "message": "请输入服务地址，多个地址以逗号分隔，兼容Ipv4和Ipv6", "trigger": "blur"}, {"validator": "validateResUrl", "trigger": "blur"}]}, {"type": "password", "prop": "password", "label": "密码", "componentProps": {"maxlength": 40}}, {"type": "input", "prop": "sentry<PERSON><PERSON><PERSON>", "label": "哨兵地址", "deps": ["type"], "visible": "(scope) => scope.type === '2'", "componentProps": {"maxlength": 255, "placeholder": "请输入哨兵地址", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入哨兵地址", "trigger": "blur"}]}, {"type": "input", "prop": "sentry<PERSON><PERSON>", "label": "哨兵名称", "deps": ["type"], "visible": "(scope) => scope.type === '2'", "componentProps": {"maxlength": 40, "placeholder": "请输入哨兵名称", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入哨兵名称", "trigger": "blur"}]}, {"type": "textarea", "prop": "memo", "label": "备注", "componentProps": {"maxlength": 255, "placeholder": "请输入备注", "rows": 5}}]}