<template>
  <pro-form
    ref="proForm"
    class="output-group"
    :value="formData"
    :form-items="formItems"
    :options="{ labelWidth }"
    @change="handleChange"
  >
    <div v-if="customFields.multiple" slot="customFields">
      <div class="output-group__tag">
        <bs-tag-input
          ref="tagInput"
          v-model="formData.customFields"
          separator=","
          :mincollapsed-num="4"
          draggable
          :placeholder="$t('pa.flow.placeholder27')"
          :disabled="disabled"
          :validate-method="(val) => validateMethod(val)"
          @change="handleTagInputChange"
        />
        <span v-if="!disabled" class="output-group__span" @click="handleFieldClick">{{ $t('pa.flow.fieldTrans') }}</span>
      </div>
    </div>
    <el-input
      v-else
      slot="customFields"
      v-model="formData.customFields"
      :disabled="disabled"
      :placeholder="customPlaceholder"
    />
    <bs-page-select
      slot="inputFields"
      v-model="formData.inputFields"
      :placeholder="$t('pa.flow.msg292')"
      filterable
      :disabled="disabled"
      :options="inputOptions"
      :collapse-tags="!disabled"
    >
      <div slot-scope="{ item }" class="output-group__option">
        <span>{{ item.label }}</span>
        <span class="output-group__option-type">{{ item.type }}</span>
      </div>
    </bs-page-select>
  </pro-form>
</template>

<script lang="ts">
import { Component, Prop, Vue, Inject } from 'vue-property-decorator';
import { Group } from '../../interface';
import { FIELD_REG } from '../../validator';
import FieldTransform from './field-transform.vue';
import { createDialog } from '@/utils/index';
import { getOutputField } from '../../utils';
import i18n from '@/i18n';
const openFieldTransDialog = (customFields) => {
  return createDialog({ title: i18n.t('pa.flow.fieldTrans') }, (h) => [
    h(FieldTransform, { props: { customFields }, slot: 'default' })
  ]);
};
@Component
export default class CustomFieldList extends Vue {
  @Prop() config!: Group;
  @Prop() inputOptions!: any[];
  @Prop() outputFields!: any[];
  @Prop({ default: false }) disabled!: boolean;
  @Inject('labelWidth') labelWidth;
  formData: { customFields: string[] | string; inputFields: string[] } = {
    customFields: this.customFields.multiple ? [] : '',
    inputFields: []
  };
  get customFields() {
    return Object.assign(
      { multiple: true, label: this.$t('pa.flow.customField'), prop: 'customData$', defaultVal: [] },
      this.config.customFields || {}
    );
  }
  get customPlaceholder() {
    const { label, placeholder } = this.customFields as any;
    return placeholder || this.$t('pa.placeholder.input') + label;
  }
  get formItems() {
    const { label } = this.customFields;
    return [
      {
        type: 'custom',
        prop: 'customFields',
        label: label,
        rules: [
          { required: true, message: this.$t('pa.placeholder.input') + label, trigger: 'change' },
          { validator: this.validateTag, trigger: 'change' }
        ]
      },
      {
        type: 'custom',
        prop: 'inputFields',
        label: this.$t('pa.flow.key9')
      }
    ];
  }
  get inputNames() {
    return this.inputOptions.map(({ label }) => label);
  }
  get inputMaps() {
    return this.inputOptions.reduce((res, item, index) => {
      res.set(item.value, item);
      return res;
    }, new Map());
  }
  async created() {
    // 数据初始化处理
    const {
      inputNames,
      customFields: { multiple, defaultVal }
    } = this;
    if (Array.isArray(this.outputFields) && this.outputFields.length) {
      this.outputFields.forEach((item) => {
        if (inputNames.includes(item.name)) {
          this.formData.inputFields.push(item.name);
        } else {
          // 自定义多选&单选
          multiple ? (this.formData.customFields as string[]).push(item.name) : (this.formData.customFields = item.name);
        }
      });
    } else if ((multiple && Array.isArray(defaultVal) && defaultVal.length) || (!multiple && defaultVal)) {
      // 默认值处理
      this.formData.customFields = defaultVal;
    }
    // 更新表单数据
    this.handleChange();
    this.$emit('custom-label-change', this.customFields.label);
  }
  // 全部tag输入字段校验
  validateTag(rule, value, callback) {
    if (!this.customFields.multiple && value && !Array.isArray(value)) {
      value = [value];
    }
    if (!value || value.length === 0) callback(new Error(this.$t('pa.flow.msg165')));
    if (Array.isArray(value) && value.length) {
      // 非法字符校验
      if (value.join('') && !value.join('').match(FIELD_REG)) {
        callback(new Error(this.$t('pa.flow.msg166')));
      }
      // 重复字段校验
      if ([...new Set(value)].length < value.length) {
        callback(new Error(this.$t('pa.flow.msg168')));
      }
      // 上游重复字段校验
      if (value.some((el) => this.inputNames.includes(el))) {
        callback(new Error(this.$t('pa.flow.msg169')));
      }
    }
    callback();
  }
  // 单个tag输入校验
  validateMethod(val) {
    // 校验是否与上游字段重复 是否包含特殊字符 是否和其他字段重复
    return !(
      this.inputNames.includes(val) ||
      !val.match(FIELD_REG) ||
      Array.from(new Set(this.formData.customFields)).length !== this.formData.customFields.length
    );
  }
  // 处理点击字段转换
  async handleFieldClick() {
    const { data } = await openFieldTransDialog(this.formData.customFields);
    if (Array.isArray(data)) {
      this.formData.customFields = data.filter(Boolean);
    }
  }
  handleTagInputChange(val) {
    this.$refs.tagInput && (this.$refs.tagInput as any).validate();
  }
  // 处理
  handleChange() {
    const { customFields, inputFields } = this.formData;
    const { inputMaps } = this;
    const outputFields = [
      ...inputFields
        .sort((pre, val) => inputMaps.get(pre).sort - inputMaps.get(val).sort) // 根据输入字段的顺序进行排序
        .map((item) => getOutputField(item, inputMaps.get(item).type)),
      ...(Array.isArray(customFields) ? customFields : [customFields].filter(Boolean)).map((item) => getOutputField(item))
    ];
    this.$refs.proForm && (this.$refs.proForm as any).validate();
    this.$emit('change', outputFields, { [this.customFields.prop]: customFields });
  }
}
</script>
<style lang="scss" scoped>
.output-group.el-form {
  margin-left: 0;
  margin-right: 0;
}
.output-group__tag {
  display: flex;
  .bs-tag-input {
    flex: 1;
    margin-right: 10px;
  }
}
.output-group__span {
  color: $--bs-color-primary;
  cursor: pointer;
  width: 60px;
}
.output-group__option {
  display: inline-flex;
  justify-content: space-between;
  width: calc(100% - 14px);
  padding-left: 10px;
  &-type {
    color: $--bs-color-primary;
    font-weight: 700;
  }
}
</style>
