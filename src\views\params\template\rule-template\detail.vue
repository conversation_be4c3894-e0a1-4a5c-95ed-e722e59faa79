<template>
  <pro-page :title="ruleTemplateTitle" fixed-header class="rule">
    <div slot="operation">
      <el-button type="primary" size="small" @click="save">{{ $t('pa.action.save') }}</el-button>
    </div>
    <pro-grid class="rule-grid" direction="column">
      <pro-grid type="info" :title="$t('pa.params.template.detail.baseInfo')">
        <el-form ref="form" class="rule-grid--form" :model="form" :rules="formRules" :label-width="isEn ? '120px' : '100px'">
          <el-form-item :label="$t('pa.params.template.detail.templateName')" prop="routeTemplateName">
            <el-input
              v-model="form.routeTemplateName"
              maxlength="100"
              :placeholder="$t('pa.params.template.name')"
              style="width: 215px"
            />
          </el-form-item>
          <el-form-item :label="$t('pa.params.template.detail.serviceType')">
            <el-select value="Kafka" disabled />
          </el-form-item>
          <el-form-item :label="$t('pa.params.template.detail.service')" prop="service">
            <el-select
              v-model="form.service"
              :placeholder="$t('pa.params.template.detail.servicePlaceholder')"
              @change="handleServiceChange"
            >
              <el-option v-for="sItem in serviceLists" :key="sItem.id" :label="sItem.title" :value="sItem.id" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('pa.notes')">
            <el-input
              v-model="form.memo"
              type="textarea"
              :rows="2"
              style="width: 60%"
              :placeholder="$t('pa.placeholder.notesPlaceholder')"
              maxlength="255"
            />
          </el-form-item>
        </el-form>
      </pro-grid>
      <pro-grid type="info" :title="$t('pa.params.template.detail.routeList')" class="rule-grid--list">
        <el-button slot="operation" type="primary" style="padding: 7px 12px" @click="addRoute">
          {{ $t('pa.params.template.detail.addRoute') }}
        </el-button>
        <div v-if="routeInfo.length === 0" class="no-data">{{ $t('pa.noData') }}</div>
        <el-collapse v-if="routeInfo.length" v-model="showCollapses">
          <el-collapse-item v-for="(item, index) in routeInfo" :key="item.route + index" :name="index">
            <div slot="title" class="title-slot">
              <span class="title-slot-content">{{ $t('pa.params.template.detail.route') }}{{ index + 1 }}</span>
              <el-button type="text" @click="delRoute(index)">{{ $t('pa.action.del') }}</el-button>
            </div>
            <div style="padding: 10px 40px">
              <el-alert
                v-if="routeError[index]"
                style="margin-bottom: 10px"
                :title="$t('pa.params.template.detail.errorTip')"
                type="error"
                show-icon
              >
                <div slot="title">
                  <p v-if="routeError[index].target">
                    {{ routeError[index].target[0] }}
                  </p>
                  <p v-for="msg in routeError[index].route || []" :key="msg">
                    {{ msg }}
                  </p>
                </div>
              </el-alert>
              <div class="target-select">
                <span>{{ $t('pa.params.template.detail.target') }}</span>
                <bs-select
                  v-model="item.target"
                  class="rule-detail-content__select"
                  multiple
                  collapse-tags
                  filterable
                  remote
                  :remote-method="
                    (query) => {
                      getTopic(query);
                    }
                  "
                  :options="topicLists"
                />
                <el-button style="float: right" icon="el-icon-plus" type="text" @click="addRule(item.rules)">
                  {{ $t('pa.params.template.detail.addRule') }}
                </el-button>
              </div>
              <el-table class="title-slot-table" :data="item.rules" style="width: 100%">
                <el-table-column prop="rule" :label="$t('pa.params.template.detail.rule')" :width="isEn ? 120 : 100">
                  <div slot-scope="scope">{{ $t('pa.params.template.detail.rule') }}{{ scope.$index + 1 }}</div>
                </el-table-column>
                <el-table-column prop="fieldName" :label="$t('pa.params.template.detail.fieldName')">
                  <div slot-scope="scope">
                    <el-input v-model="item.rules[scope.$index].fieldName" />
                  </div>
                </el-table-column>
                <el-table-column prop="fieldType" :label="$t('pa.params.template.detail.fieldType')">
                  <div slot-scope="scope">
                    <el-select v-model="item.rules[scope.$index].fieldType" :placeholder="$t('pa.placeholder.select')">
                      <el-option v-for="fItem in fieldTypes" :key="fItem" :label="fItem" :value="fItem" />
                    </el-select>
                  </div>
                </el-table-column>
                <el-table-column prop="operator" :label="$t('pa.params.template.detail.operator')">
                  <div slot-scope="scope">
                    <el-select v-model="item.rules[scope.$index].operator">
                      <el-option v-for="sItem in symbolLists" :key="sItem" :label="sItem" :value="sItem" />
                    </el-select>
                  </div>
                </el-table-column>
                <el-table-column prop="fieldValue" :label="$t('pa.params.template.detail.fieldValue')">
                  <div slot-scope="scope">
                    <el-input v-model="item.rules[scope.$index].fieldValue" :placeholder="$t('pa.placeholder.input')" />
                  </div>
                </el-table-column>
                <el-table-column prop="action" :label="$t('pa.action.action')" width="100">
                  <div slot-scope="scope">
                    <el-button type="text" @click="delRule(item.rules, scope.$index)"> {{ $t('pa.action.del') }} </el-button>
                  </div>
                </el-table-column>
              </el-table>
            </div>
          </el-collapse-item>
        </el-collapse>
      </pro-grid>
    </pro-grid>
  </pro-page>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { get, post } from '@/apis/utils/net';
import { Rule, Route } from './index';
interface RouteError {
  target?: string;
  route?: string[];
}
interface TemplateDetail {
  serviceName?: string;
  [propName: string]: unknown;
}
@Component({
  name: 'ElementRuleTemplateDetail',
  components: {}
})
export default class ElementRuleTemplateDetail extends PaBase {
  id = '';
  orgId = this.$store.getters.orgId;
  userName = this.$store.getters.userName;
  form: any = {
    routeTemplateName: '',
    service: '',
    memo: ''
  };
  formRules = {
    routeTemplateName: [{ required: true, message: this.$t('pa.params.template.name'), trigger: 'change' }],
    service: [{ required: true, message: this.$t('pa.params.template.detail.servicePlaceholder'), trigger: 'change' }]
  };
  // 模板详情
  detail: TemplateDetail = {};
  // 集群
  serviceLists = [];
  selectService: any = {};
  // topic
  topicLists = [];
  // 操作符
  symbolLists = ['=', '<', '>', '>=', '<=', 'in'];
  // 字段类型
  fieldTypes = ['String', 'Integer', 'Float', 'Long', 'Double'];
  // 字段数据 TODO:后续替换接口
  // fieldLists = [{
  //   name: 'name',
  //   type: 'string'
  // }, {
  //   name: 'age',
  //   type: 'integer'
  // }, {
  //   name: 'price',
  //   type: 'float'
  // }];
  routeInfo: any = [];
  showCollapses: any = [0];
  routeError: RouteError[] = [];
  get ruleTemplateTitle() {
    return this.id ? this.$t('pa.params.template.editTemplate') : this.$t('pa.params.template.addTemplate');
  }
  async created() {
    this.id = this.$route.query.id as string;
    // 获取服务地址
    await this.getServices();
    if (this.id) {
      this.getDetail(this.id);
    }
  }
  // 获取详情
  async getDetail(id) {
    const { data } = await get('/rs/pa/route/findById', { id });
    this.detail = data;
    const targetService: any = this.serviceLists.find((item: any) => item.title === data.serviceName);
    // 表单信息获取
    this.form = {
      routeTemplateName: data.routeTemplateName,
      service: this.detail.resId || this.detail.serviceName || '',
      memo: data.memo
    };
    // 获取路由信息
    this.routeInfo = JSON.parse(data.routeInfo);
    // 获取已选集群的topic列表
    this.handleServiceChange(targetService.id);
  }
  // 获取集群列表
  async getServices() {
    const params = { orgId: this.orgId };
    const { data, success } = await get('/rs/pa/portal/listQuoteRes/KAFKA', params);
    if (success) this.serviceLists = data || [];
  }
  // 集群选择回调
  handleServiceChange(val) {
    this.selectService = this.serviceLists.find((i: any) => i.id === val);
    // 获取topic
    this.getTopic();
  }
  // 获取已选择的集群下的topic
  async getTopic(query = '') {
    const params = {
      orgId: this.orgId,
      resTitle: this.selectService.title,
      resId: this.selectService.id,
      subName: query
    };
    const { data, success } = await get('/rs/pa/portal/getSubList/KAFKA', params);
    if (success) {
      this.topicLists = data.map(({ subName }) => ({ value: subName, label: subName }));
    }
  }
  // 条件中字段选择回调
  // selectField(rules, index, val) {
  //   const field: any = this.fieldLists.find(f => f.name === val);
  //   rules[index].fieldType = field.type;
  // }
  // 校验信息
  validateRoute() {
    const errors: RouteError[] = [];
    const pushError = (index, key, msg) => {
      if (!errors[index]) {
        errors[index] = {};
      }
      if (Array.isArray(errors[index][key])) {
        errors[index][key].push(msg);
      } else if (errors[index][key]) {
        errors[index][key] = [errors[index][key], msg];
      } else {
        errors[index][key] = [msg];
      }
    };
    const checkTypeAndValue = ({ fieldType, fieldValue }) => {
      const type = fieldType.toLowerCase();
      if (type !== 'string') {
        return /\d*.?\d*/.test(fieldValue);
      }
      return true;
    };
    this.routeInfo.forEach((item: any, index) => {
      if (item.target && item.target.length === 0) {
        pushError(index, 'target', this.$t('pa.params.template.detail.targetRequired'));
      } else if (item.rules.length === 0) {
        pushError(
          index,
          'route',
          `${this.$t('pa.params.template.detail.route')}${index + 1}${this.$t('pa.params.template.detail.lackRule')}`
        );
      } else if (item.rules.length > 0) {
        item.rules.forEach((rItem, rIndex) => {
          if (!rItem.fieldName || !rItem.operator || !rItem.fieldValue) {
            pushError(
              index,
              'route',
              `${this.$t('pa.params.template.detail.rule')}${rIndex + 1}${this.$t(
                'pa.params.template.detail.notFullyConfigured'
              )}`
            );
          } else if (!checkTypeAndValue(rItem)) {
            pushError(
              index,
              'route',
              `${this.$t('pa.params.template.detail.rule')}${rIndex + 1}${this.$t(
                'pa.params.template.detail.notMismatching'
              )}`
            );
          }
        });
      }
      // 展开路由
      if (errors[index]) {
        this.showCollapses.push(index);
      }
    });
    return errors;
  }
  // 保存路由模板
  save() {
    (this.$refs['form'] as any).validate(async (valid) => {
      if (valid) {
        this.routeError = this.validateRoute();
        if (this.routeError.length > 0) {
          return false;
        }
        this.routeInfo.forEach((item, index) => {
          item.route = this.$t('pa.params.template.detail.route') + (index + 1);
          item.rules.forEach((rItem, rindex) => {
            rItem.rule = this.$t('pa.params.template.detail.rule') + (rindex + 1);
          });
        });
        const params = {
          id: this.id,
          orgName: this.userName,
          resType: 'Kafka',
          routeInfo: this.routeInfo,
          serviceAddress: this.selectService.address,
          serviceName: this.selectService.title,
          ...this.form
        };
        // 调用新建保存接口
        const url = this.id ? '/rs/pa/route/update' : '/rs/pa/route/add';
        const { data, msg, success } = await post(url, params);
        if (success) {
          this.$message({ message: msg, type: 'success' });
          if (url === '/rs/pa/route/add') {
            const oldFullPath = this.$route.fullPath;
            this.$nextTick(() => {
              try {
                const tabsNavInstance = (this as any).$tabNav;
                tabsNavInstance.handleTabsDelete(oldFullPath);
                // 跳转至新的详情页
                this.$router.replace({
                  path: 'routeTemplateDetail',
                  query: { id: data, title: this.form.routeTemplateName }
                });
              } catch (err) {}
            });
          }
        } else {
          this.$message({ message: msg, type: 'error' });
        }
      }
    });
  }
  // 添加路由
  addRoute() {
    this.routeInfo.push(new Route());
  }
  // 删除路由
  delRoute(index: number) {
    this.routeInfo.splice(index, 1);
  }
  // 添加条件
  addRule(item) {
    item.push(new Rule());
  }
  // 删除条件
  delRule(item, index: number) {
    item.splice(index, 1);
  }
}
</script>

<style lang="scss" scoped>
.title-slot {
  margin-left: 20px;
  &-content {
    margin-right: 10px;
  }
}
.rule-grid {
  margin-top: 18px;
  height: calc(100vh - 198px);
  overflow: auto;
  &--list {
    margin-top: 18px;
    height: calc(100vh - 538px);
    background: #fff;
  }
  &--form {
    padding: 15px 0;
    width: 60%;
    .el-input,
    .el-select {
      width: 100%;
    }
  }
}
.rule-detail {
  height: calc(100vh - 106px);
  &-content {
    height: calc(100% - 50px);
    overflow: auto;
    padding: 20px;
    background: #fff;
    border-top: 1px solid #f1f1f1;
    &__select {
      margin-left: 10px;
      margin-bottom: 10px;
    }
  }

  .title-slot {
    width: 100%;
    padding: 0 20px;
    display: flex;
    align-items: center;
    .title-slot-content {
      font-size: 14px;
      font-weight: bold;
      flex: 1;
    }
  }
  .title-slot-table {
    border: 1px solid #ebeef5;
    border-bottom: none;
  }
  .target-select {
    margin-bottom: 10px;
    > span {
      padding-right: 10px;
      color: #606266;
    }
  }
}
.no-data {
  line-height: calc(100vh - 602px);
  text-align: center;
  font-size: 15px;
  color: #606266;
}
.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  &-title {
    flex: auto;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 700;
    font-size: 15px;
  }
}

.rule-header {
  height: 50px;
  background: #ffffff;
  border-left: none;
  border-right: none;
  padding: 0 20px;
  font-size: 16px;
  display: flex;
  align-items: center;
}
.rule-header-operate {
  flex: 1;
  text-align: right;
}
</style>
