<template>
  <bs-dialog
    id="interval-join-dialog"
    :title="data.nodeName + $t('pa.flow.comConfig')"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    :confirm-button="{ disabled }"
    size="large"
    append-to-body
    @close="closeDialog(false)"
    @confirm="submit('ruleForm')"
  >
    <el-form ref="ruleForm" :model="formData" :rules="rules" :label-width="isEn ? '180px' : '120px'">
      <div style="display: flex">
        <div style="width: 50%">
          <el-divider content-position="left">{{ $t('pa.flow.label23') }}</el-divider>
          <el-form-item
            :label="$t('pa.flow.label24')"
            align="left"
            prop="leftKey"
            :rules="[
              {
                required: true,
                message: $t('pa.flow.msg206'),
                trigger: 'blur'
              }
            ]"
          >
            <bs-select
              v-model="formData.leftKey"
              :placeholder="$t('pa.flow.msg206')"
              clearable
              filterable
              multiple
              show-all
              collapse-tags
              confirm-when-deleting
              style="width: calc(100% - 30px)"
              :options="myLeftInputs"
            />
          </el-form-item>
          <el-form-item
            :label="$t('pa.flow.timeField')"
            align="left"
            prop="leftTimeKey"
            :rules="[
              {
                required: true,
                message: $t('pa.flow.msg207'),
                trigger: 'blur'
              }
            ]"
          >
            <el-select
              v-model="formData.leftTimeKey"
              :placeholder="$t('pa.flow.msg207')"
              clearable
              filterable
              style="width: calc(100% - 30px)"
            >
              <el-option v-for="item in myLeftInputs" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('pa.flow.label25')"
            align="left"
            prop="leftOutputFieldPrefix"
            :rules="[
              {
                required: true,
                message: $t('pa.flow.msg208'),
                trigger: 'blur'
              }
            ]"
          >
            <el-input
              v-model="formData.leftOutputFieldPrefix"
              :placeholder="$t('pa.flow.msg208')"
              clearable
              style="width: calc(100% - 30px)"
            />
          </el-form-item>

          <el-form-item
            :label="$t('pa.flow.outputFields')"
            align="left"
            prop="leftOutputField"
            :rules="[
              {
                required: true,
                message: $t('pa.flow.msg209'),
                trigger: 'blur'
              }
            ]"
          >
            <bs-select
              v-model="formData.leftOutputField"
              :placeholder="$t('pa.flow.msg209')"
              multiple
              clearable
              filterable
              show-all
              collapse-tags
              confirm-when-deleting
              style="width: calc(100% - 30px)"
              :options="myLeftInputs"
            />
          </el-form-item>
        </div>
        <div style="width: 50%">
          <el-divider content-position="left">{{ $t('pa.flow.label26') }}</el-divider>
          <el-form-item
            :label="$t('pa.flow.label24')"
            align="left"
            prop="rightKey"
            :rules="[
              {
                required: true,
                message: $t('pa.flow.msg210'),
                trigger: 'blur'
              }
            ]"
          >
            <bs-select
              v-model="formData.rightKey"
              :placeholder="$t('pa.flow.msg210')"
              clearable
              filterable
              multiple
              show-all
              collapse-tags
              style="width: calc(100% - 30px)"
              confirm-when-deleting
              :options="myRightInputs"
            />
          </el-form-item>
          <el-form-item
            :label="$t('pa.flow.timeField')"
            align="left"
            prop="rightTimeKey"
            :rules="[
              {
                required: true,
                message: $t('pa.flow.msg211'),
                trigger: 'blur'
              }
            ]"
          >
            <el-select
              v-model="formData.rightTimeKey"
              :placeholder="$t('pa.flow.msg211')"
              clearable
              filterable
              style="width: calc(100% - 30px)"
            >
              <el-option v-for="item in myRightInputs" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('pa.flow.label25')"
            align="left"
            prop="rightOutputFieldPrefix"
            :rules="[
              {
                required: true,
                message: $t('pa.flow.msg212'),
                trigger: 'blur'
              }
            ]"
          >
            <el-input
              v-model="formData.rightOutputFieldPrefix"
              :placeholder="$t('pa.flow.msg212')"
              clearable
              style="width: calc(100% - 30px)"
            />
          </el-form-item>
          <el-form-item
            :label="$t('pa.flow.outputFields')"
            align="left"
            prop="rightOutputField"
            :rules="[
              {
                required: true,
                message: $t('pa.flow.msg213'),
                trigger: 'blur'
              }
            ]"
          >
            <bs-select
              v-model="formData.rightOutputField"
              :placeholder="$t('pa.flow.msg213')"
              multiple
              clearable
              filterable
              show-all
              collapse-tags
              confirm-when-deleting
              style="width: calc(100% - 30px)"
              :options="myRightInputs"
            />
          </el-form-item>
        </div>
      </div>
      <el-divider content-position="left">{{ $t('pa.flow.config4') }}</el-divider>

      <el-form-item
        :label="$t('pa.flow.label27')"
        align="left"
        prop="joinType"
        :rules="[
          {
            required: true,
            message: $t('pa.flow.msg214'),
            trigger: 'blur'
          }
        ]"
      >
        <el-select
          v-model="formData.joinType"
          :placeholder="$t('pa.flow.msg214')"
          clearable
          filterable
          style="width: calc(100% - 30px)"
        >
          <el-option
            v-for="item in [
              {
                name: $t('pa.flow.label28'),
                type: 'inner'
              }
              /* {
                name: '左连接',
                type: 'left'
              },
              {
                name: '右连接',
                type: 'right'
              }*/
            ]"
            :key="item.type"
            :label="item.name"
            :value="item.type"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="$t('pa.flow.label29')"
        align="left"
        prop="lowerBound"
        :rules="[
          {
            required: true,
            message: $t('pa.flow.placeholder35'),
            trigger: 'blur'
          }
        ]"
      >
        <el-input-number
          v-model="formData.lowerBound"
          :max="0"
          :placeholder="$t('pa.flow.placeholder35')"
          :style="{ width: itemWidth }"
        />
        {{ $t('pa.flow.s') }}
      </el-form-item>

      <el-form-item
        :label="$t('pa.flow.label30')"
        align="left"
        prop="upperBound"
        :rules="[
          {
            required: true,
            message: $t('pa.flow.msg215'),
            trigger: 'blur'
          }
        ]"
      >
        <el-input-number
          v-model="formData.upperBound"
          :min="0"
          :placeholder="$t('pa.flow.msg215')"
          :style="{ width: itemWidth }"
        />
        {{ $t('pa.flow.s') }}
      </el-form-item>
      <el-form-item
        :label="$t('pa.flow.label31')"
        align="left"
        prop="orderlessTime"
        :rules="[
          {
            required: true,
            message: $t('pa.flow.msg217'),
            trigger: 'blur'
          }
        ]"
      >
        <el-input-number
          v-model="formData.orderlessTime"
          :min="1"
          :placeholder="$t('pa.flow.msg216')"
          :style="{ width: itemWidth }"
        />
        {{ $t('pa.flow.s') }}
      </el-form-item>
    </el-form>
    <print-log slot="footer-left" v-model="printLog" :disabled="disabled" />
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import * as _ from 'lodash';

const _DEFAULT_FORMDATA = {
  leftInputField: [],
  rightInputField: [],
  leftKey: '',
  rightKey: '',
  leftTimeKey: '',
  rightTimeKey: '',
  joinType: '',
  leftOutputField: [],
  rightOutputField: [],
  leftOutputFieldPrefix: 'l',
  rightOutputFieldPrefix: 'r'
};
@Component({
  components: {
    'print-log': () => import('./components/print-log.vue')
  }
})
export default class NodeIntervalJoin extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: false }) disabled!: boolean;

  formData: any = {};
  rules: any = {};

  myLeftInputs: any = [];
  myRightInputs: any = [];
  leftStartNodeId = '';
  rightStartNodeId = '';
  printLog = false;

  get itemWidth() {
    return this.isEn ? 'calc(100% - 90px)' : 'calc(100% - 30px)';
  }

  get title() {
    const { nodeName = '', componentName = '' } = this.data || {};
    return this.$t('pa.flow.msg288', [nodeName, componentName]);
  }
  submit(formName) {
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        const nodeDto = _.cloneDeep(this.data);
        const properties = _.cloneDeep(this.formData);
        for (const pKey of Object.keys(properties)) {
          if (properties[pKey] === undefined) {
            delete properties[pKey];
          }
        }
        nodeDto.inputFields = _.concat(this.myLeftInputs, this.myRightInputs);
        nodeDto.printLog = this.printLog;
        const outputs: any = [];
        this.formData.leftOutputField.forEach((n) => {
          const rec = _.find(this.myLeftInputs, { value: n });
          if (rec) {
            outputs.push({
              name: this.formData.leftOutputFieldPrefix + n,
              outputable: true,
              type: rec.type
            });
          }
        });
        this.formData.rightOutputField.forEach((n) => {
          const rec = _.find(this.myRightInputs, { value: n });
          if (rec) {
            outputs.push({
              name: this.formData.rightOutputFieldPrefix + n,
              outputable: true,
              type: rec.type
            });
          }
        });
        nodeDto.outputFields = outputs;
        nodeDto.properties = properties;
        nodeDto.properties.leftStartNodeId = this.leftStartNodeId;
        nodeDto.properties.rightStartNodeId = this.rightStartNodeId;
        nodeDto.properties.inputPointFields = {
          left: this.myLeftInputs,
          right: this.myRightInputs
        };
        nodeDto.leftStartNodeId = this.leftStartNodeId;
        nodeDto.rightStartNodeId = this.rightStartNodeId;

        this.closeDialog(true, nodeDto);
      } else {
        this.$tip.error(this.$t('pa.flow.msg178'));
        return false;
      }
    });
  }
  handleBeforeDeleting() {
    return false;
  }
  setLeftSelectItem(result) {
    this.formData.leftInputField = result.values;
  }
  setRightSelectItem(result) {
    this.formData.rightInputField = result.values;
  }
  setLeftOutputItem(result) {
    this.formData.leftOutputField = result.values;
  }
  setRightOutputItem(result) {
    this.formData.rightOutputField = result.values;
  }

  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    this.formData = {};
    this.myLeftInputs = [];
    this.myRightInputs = [];
    this.leftStartNodeId = '';
    this.rightStartNodeId = '';
    return { needUpdate, jobNode };
  }
  created() {
    this.printLog = this.data.printLog;
  }
  mounted() {
    // 获取左右端点
    (this.data.pointIn || []).forEach((point, idx) => {
      this[idx === 0 ? 'myLeftInputs' : 'myRightInputs'] = this.transformToOptions(point.data);
    });
    this.leftStartNodeId = this.data.leftStartNodeId;
    this.rightStartNodeId = this.data.rightStartNodeId;
    this.formData = _.cloneDeep(this.data.properties ? this.data.properties : _DEFAULT_FORMDATA);
  }
  transformToOptions(data) {
    return data.map((item) => ({ label: item.name, value: item.name, type: item.type }));
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-select {
  width: 100%;
}
</style>
