<template>
  <div class="version__container">
    <!-- 头部 -->
    <div class="tab-title">
      <div class="title-text">{{ $t('pa.flow.version') }}</div>
      <el-input
        v-model="params.search"
        :debounce="1000"
        class="version__input"
        :placeholder="$t('pa.flow.placeholder14')"
        @input="getVersionList"
      />
    </div>
    <!-- 内容 -->
    <div class="version-content">
      <bs-table
        :column-settings="false"
        :column-data="tableData.columnData"
        :page-data="tableData.pageData"
        :data="tableData.tableData"
        height="100%"
        @page-change="handleCurrentChange"
      >
        <template slot="updateTime" slot-scope="{ row }">
          {{ row.updateTime | dateFormat }}
        </template>
        <template slot="operator" slot-scope="{ row, $index }">
          <template v-for="el in tableConfig">
            <el-tooltip
              v-if="el.tipMessage !== '回滚' || !row.hideRollBack"
              :key="el.tipMessage"
              :content="el.tipMessage"
              effect="light"
              placement="bottom"
            >
              <i
                class="iconfont"
                :class="el.iconfont"
                style="margin-left: 10px; cursor: pointer"
                @click="el.handler(row, $index)"
              ></i>
            </el-tooltip>
          </template>
        </template>
      </bs-table>
      <code-version-compare-dialog
        v-if="showCompareDialog"
        :visible.sync="showCompareDialog"
        :current-version-data="version"
        :version-data="versionList"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { URL_VERSION_LIST } from '@/apis/commonApi';
import CodeVersionCompareDialog from '@/components/code-version-compare-dialog.vue';
import { post } from '@/apis/utils/net';
import { cloneDeep } from 'lodash';
import { versionBack } from '@/apis/flowNewApi';
import { hasPermission } from '@/utils';

@Component({
  components: {
    CodeVersionCompareDialog
  }
})
export default class FlowVersion extends Vue {
  @Prop({ default: '' }) flowId!: string;
  @Prop({ default: 'PROCESSFLOW' }) flowType!: string;

  private loading = false;
  private params: any = {
    search: '',
    sortData: { jobVersion: 'DESC' },
    pageData: { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 }
  };
  private tableData: ITableData = { columnData: [], tableData: [] };
  private showCompareDialog = false;
  private version: any = {};
  private versionList: any = [];
  get tableConfig() {
    return [
      {
        tipMessage: this.$t('pa.flow.rollBack'),
        iconfont: 'icon-roll-back',
        handler: ({ id }: any) => this.handleVersionBack(id),
        hasAuthority: hasPermission('PA.FLOW.FLOW_MGR.VERSION_ROLL_BACK')
      },
      {
        tipMessage: this.$t('pa.flow.view'),
        iconfont: 'icon-chakan',
        handler: ({ id }: any) => window.open(`#/flowVersion?flowId=${id}`),
        hasAuthority: true
      },
      {
        tipMessage: this.$t('pa.flow.versionCompare'),
        iconfont: 'icon-banbenduibi',
        handler: (row: any) => this.handleVersionCompare(row),
        hasAuthority: this.flowType === 'FLINK_SQL'
      }
    ].filter((item) => item.hasAuthority);
  }

  created() {
    this.flowId && this.getVersionList();
  }

  async getVersionList() {
    try {
      this.loading = true;
      const { success, data, error } = await post(`${URL_VERSION_LIST}?id=${this.flowId}`, this.params);
      if (success) {
        data.columnData.forEach((item) => {
          item.value = item.prop;
        });
        // 自定义JAR包类型流程不可编辑
        this.flowType !== 'UDJ' &&
          data.columnData.push({ label: this.$t('pa.action.action'), value: 'operator', width: 150, fixed: 'right' });
        data.pageData.layout = 'total, prev, pager, next, jumper';
        this.tableData = { ...data };
        // 最新版本隐藏回滚功能
        if (this.params.pageData.currentPage === 1) {
          (this.tableData.tableData[0] || {}).hideRollBack = true;
        }
        this.loading = false;
        return;
      }
      this.$tip.error(error);
      this.loading = false;
    } catch {
      this.loading = false;
    }
  }
  handleVersionBack(id = '') {
    this.$confirm(this.$t('pa.flow.msg50'), this.$t('pa.flow.tip'), {
      confirmButtonText: this.$t('pa.flow.confirm'),
      cancelButtonText: this.$t('pa.flow.cancel')
    }).then(async () => {
      const { success, msg } = await versionBack({ jobVersionId: id });
      if (success) {
        this.$message.success(msg);
        this.getVersionList();
        this.$emit('back');
        return;
      }
      this.$message.error(msg);
    });
  }

  async handleVersionCompare(rawData: any) {
    rawData && (this.version = rawData);
    const params = cloneDeep(this.params);
    delete params.pageData.pageSize;
    const { success, data, error } = await post(`${URL_VERSION_LIST}?id=${this.flowId}`, params);
    if (success) {
      if (Array.isArray(data.tableData)) {
        this.versionList = data.tableData.map((el) => el);
        this.showCompareDialog = true;
      }
      return;
    }
    this.$tip.error(error);
  }

  handleCurrentChange(val) {
    this.params.pageData.currentPage = val;
    this.getVersionList();
  }
}
</script>
<style lang="scss" scoped>
.version {
  &__container {
    height: calc(100% - 40px);
  }
  &__input {
    width: 210px;
  }
  &-content {
    height: calc(100% - 76px);
    font-size: 12px;
    color: #666666;
    .bs-table {
      height: calc(100% - 40px);
    }
  }
}
</style>
