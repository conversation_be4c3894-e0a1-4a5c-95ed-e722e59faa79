<template>
  <div class="version__container">
    <!-- 头部 -->
    <div class="tab-title">
      <div class="title-text">版本信息</div>
      <el-input
        v-model="params.search"
        :debounce="1000"
        class="version__input"
        placeholder="请输入标签、备注"
        @input="getVersionList"
      />
    </div>
    <!-- 内容 -->
    <div class="version-content">
      <base-table
        :table-config="tableConfig"
        :table-data="tableData"
        @handleCurrentChange="handleCurrentChange"
      />
      <code-version-compare-dialog
        v-if="showCompareDialog"
        :visible.sync="showCompareDialog"
        :current-version-data="version"
        :version-data="versionList"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { URL_VERSION_LIST } from '@/apis/commonApi';
import CodeVersionCompareDialog from '@/components/code-version-compare-dialog.vue';
import { post } from '@/apis/utils/net';
import { cloneDeep } from 'lodash';
import { versionBack } from '@/apis/flowNewApi';
import { hasPermission } from '@/utils';

@Component({
  components: {
    'base-table': () => import('@/components/base-table.vue'),
    CodeVersionCompareDialog
  }
})
export default class FlowVersion extends Vue {
  @Prop({ default: '' }) flowId!: string;
  @Prop({ default: 'PROCESSFLOW' }) flowType!: string;

  private loading = false;
  private params: any = {
    search: '',
    sortData: { jobVersion: 'DESC' },
    pageData: { pageSize: 20, currentPage: 1, total: 1 }
  };
  private tableData: ITableData = { columnData: [], tableData: [] };
  private showCompareDialog = false;
  private version: any = {};
  private versionList: any = [];

  get tableConfig() {
    return {
      width: 200,
      columnsExtend: {
        edit: [
          {
            tipMessage: '回滚',
            iconfont: 'icon-roll-back',
            handler: ({ id }: any) => this.handleVersionBack(id),
            hasAuthority: hasPermission('PA.FLOW.FLOW_MGR.VERSION_ROLL_BACK')
          },
          {
            tipMessage: '查看',
            iconfont: 'icon-chakan',
            handler: ({ id }: any) => window.open(`#/flowVersion?flowId=${id}`)
          },
          {
            tipMessage: '版本比对',
            iconfont: 'icon-banbenduibi',
            handler: (row: any) => this.handleVersionCompare(row),
            hasAuthority: this.flowType === 'FLINK_SQL'
          }
        ]
      }
    };
  }

  created() {
    this.flowId && this.getVersionList();
  }

  async getVersionList() {
    try {
      this.loading = true;
      const { success, data, error } = await post(
        `${URL_VERSION_LIST}?id=${this.flowId}`,
        this.params
      );
      if (success) {
        this.tableData = { ...data };
        this.loading = false;
        return;
      }
      this.$tip.error(error);
      this.loading = false;
    } catch {
      this.loading = false;
    }
  }
  handleVersionBack(id = '') {
    this.$confirm('是否确认回滚?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(async () => {
      const { success, msg } = await versionBack({ jobVersionId: id });
      if (success) {
        this.$message.success(msg);
        this.getVersionList();
        this.$emit('back');
        return;
      }
      this.$message.error(msg);
    });
  }

  async handleVersionCompare(rawData: any) {
    rawData && (this.version = rawData);
    const params = cloneDeep(this.params);
    delete params.pageData.pageSize;
    const { success, data, error } = await post(`${URL_VERSION_LIST}?id=${this.flowId}`, params);
    if (success) {
      if (Array.isArray(data.tableData)) {
        this.versionList = data.tableData.map((el) => el);
        this.showCompareDialog = true;
      }
      return;
    }
    this.$tip.error(error);
  }

  handleCurrentChange(val) {
    this.params.pageData.currentPage = val;
    this.getVersionList();
  }
}
</script>
<style lang="scss" scoped>
.version {
  &__container {
    height: calc(100% - 40px);
  }
  &__input {
    width: 210px;
  }
  &-content {
    height: calc(100% - 76px);
    font-size: 12px;
    color: #666666;
    border-top: 1px solid #d4dce2;
  }
}
</style>
