<script lang="tsx">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { ExtraChangedFields } from '../../interface';
import { cloneDeep } from 'lodash';
type Type = 'add' | 'del';
@Component
export default class TargetField extends Vue {
  @Prop({ default: () => [] }) data!: ExtraChangedFields;
  addType(fields, type: Type) {
    return fields.map((i) => {
      i.type = type;
      return i;
    });
  }
  getClassName(type: Type) {
    return {
      add: 'add-field',
      del: 'del-field'
    }[type];
  }
  render() {
    const { label, changedFields } = cloneDeep(this.data);
    const fields = [
      ...this.addType(changedFields.addFields || [], 'add'),
      ...this.addType(changedFields.delFields || [], 'del')
    ];
    const showText = fields.reduce((cur, val) => {
      return (cur += `${val.name}、`);
    }, '');
    return (
      <el-tooltip content={showText.slice(0, -1)} placement="top" effect="light">
        <p class="line">
          {label}：
          {fields.map((item, idx) => {
            return (
              <span class="field">
                <i class={this.getClassName(item.type)}>{item.name}</i>
                {idx < fields.length - 1 && <i>、</i>}
              </span>
            );
          })}
        </p>
      </el-tooltip>
    );
  }
}
</script>
<style lang="scss" scoped>
.line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.field i {
  font-style: normal;
}
.add-field {
  color: #54c958;
}
.del-field {
  color: #ff5353;
}
</style>
