<template>
  <div class="flink-sql-table">
    <el-table :key="tableKey" stripe tooltip-effect="light" :height="height" :data="tableData">
      <el-table-column
        v-for="column in tableHeader"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        align="center"
        :sortable="column.sortable === true ? 'custom' : false"
        show-overflow-tooltip
      />
    </el-table>
    <el-pagination
      v-if="pageSize && total > pageSize"
      background
      :total="total"
      :pager-count="5"
      :page-size="pageSize"
      :current-page.sync="current"
      layout="total, prev, pager, next, jumper"
      @current-change="getCurrentData"
    />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { cloneDeep } from 'lodash';

@Component
export default class FlinkSqlTable extends Vue {
  @Prop() tableConfig!: any;
  @Prop({ type: String, default: '400px' }) height!: string;
  private current = 1;
  private pageSize = 10;
  private total = 0;
  private tableHeader = [];
  private tableData = [];
  private tableAllData = [];
  private tableKey = Date.now();

  created() {
    const { tData = [], tHead = [], pageSize = 10 } = this.tableConfig;
    this.current = 1;
    this.pageSize = pageSize;
    this.tableHeader = tHead.map((el: any) => (el.prop ? el : { prop: el, label: el }));
    this.tableAllData = cloneDeep(tData);
    this.total = this.tableAllData.length;
    this.getCurrentData();
  }

  getCurrentData() {
    this.tableKey = Date.now();
    const { tableAllData, current, pageSize } = this;
    const start = (current - 1) * 10;
    this.tableData = tableAllData.slice(start, start + pageSize);
  }
}
</script>
