<template>
  <div v-loading="loading" class="generate-table-field" :class="{ 'generate-table-field__us': isEn }">
    <div class="generate-ul">
      <div
        v-for="(item, index) in tableList"
        :key="item.name || index"
        :class="[
          'edit-row',
          nameErrorIndex.includes(index) || aliasErrorIndex.includes(index) ? 'is-error' : '',
          isEn ? 'edit-row--us' : ''
        ]"
      >
        <bs-select
          v-model="item.name"
          :placeholder="$t('pa.flow.selectTable')"
          :options="options"
          :class="nameErrorIndex.includes(index) ? 'is-error' : ''"
          @visible-change="(val) => handleVisibleChange(val, item.name)"
        />
        <el-input
          v-model="item.alias"
          :placeholder="$t('pa.flow.placeholder49')"
          :class="aliasErrorIndex.includes(index) ? 'is-error' : ''"
        />
        <i class="iconfont icon-tianjia" @click="addRow(index)"></i>
        <i v-if="tableList.length > 1" class="iconfont icon-shanchu1" @click="removeRow(index)"></i>
      </div>
    </div>
    <div class="generate-btn">
      <el-button type="primary" @click="generate">{{ $t('pa.flow.gen') }}</el-button>
    </div>
  </div>
</template>
<script lang="ts">
import { extractSqlFields } from '@/apis/sqlApi';
import { Component, Prop, Vue } from 'vue-property-decorator';
import { KEYWORDS } from '../service';
@Component
export default class GenerateTableField extends Vue {
  @Prop() content!: string;
  options: { label: string; value: string }[] = [];
  allOptions: { label: string; value: string; fields: [] }[] = [];
  tableList = [{ name: '', alias: '' }];
  nameErrorIndex: number[] = [];
  aliasErrorIndex: number[] = [];
  loading = true;
  async created() {
    const { content } = this;
    const lines = content.split('\n');
    // 提取表名称和字段名称
    const getTableName = (lineStr) => {
      return (
        lineStr
          .replace('CREATE TABLE', '')
          .split(' ')
          .filter((s) => /^[`.a-z0-9A-Z_]+$/.test(s) && !KEYWORDS.includes(s.toUpperCase()))[0] || ''
      );
    };
    const tableNames: string[] = [];
    lines.forEach((str) => {
      if (str.includes('CREATE TABLE')) {
        tableNames.push(getTableName(str));
      }
    });
    const { data = {} } = (await extractSqlFields({ content, tableList: tableNames.filter(Boolean) })) || {};
    this.loading = false;
    Object.keys(data).forEach((tableName) => {
      this.allOptions.push({
        label: tableName,
        value: tableName,
        fields: (data[tableName].fields || []).map((i) => i.fieldName)
      });
    });
  }
  addRow(index) {
    this.tableList.splice(index + 1, 0, { name: '', alias: '' });
  }
  removeRow(index) {
    this.tableList.splice(index, 1);
  }
  handleVisibleChange(val, name) {
    if (val) {
      const names = this.tableList.map((e) => e.name);
      this.options = this.allOptions.filter((i) => i.value === name || !names.includes(i.value));
    }
  }
  validate() {
    this.nameErrorIndex = [];
    this.aliasErrorIndex = [];
    this.tableList.map(({ name, alias }, index) => {
      !name && this.nameErrorIndex.push(index);
      !/^[a-zA-Z0-9_]*$/.test(alias) && this.aliasErrorIndex.push(index);
    });
  }
  generate() {
    this.validate();
    if (this.nameErrorIndex.length) {
      return this.$message.error(this.$t('pa.flow.selectTable'));
    }
    if (this.aliasErrorIndex.length) {
      return this.$message.error(this.$t('pa.flow.msg283'));
    }
    const lines: string[] = [];
    this.tableList.forEach(({ name, alias }) => {
      const fields = this.allOptions.find((i) => i.value === name)?.fields || [];
      fields.forEach((f) => {
        lines.push(`${alias || name}.\`${f}\` as \`${f}\`,`);
      });
    });
    // 最后一行去掉逗号
    lines.length && (lines[lines.length - 1] = lines[lines.length - 1].replace(',', ''));
    this.$emit('generate', lines);
  }
}
</script>
<style lang="scss" scoped>
.generate-table-field {
  position: relative;
  box-sizing: border-box;
  width: calc(100% - 300px);
  padding: 12px 12px 12px 12px;
  &__us {
    width: 650px;
  }
}
.generate-ul {
  min-height: 148px;
  max-height: 200px;
  overflow-y: auto;
}
.edit-row {
  margin-bottom: 16px;
  ::v-deep .bs-select .el-input,
  .el-input {
    width: 120px;
  }
  &--us {
    ::v-deep .bs-select .el-input,
    .el-input {
      width: 200px;
    }
  }
  ::v-deep .bs-select .el-input__inner {
    border-radius: 4px 0 0 4px;
    border-right: none;
  }
  > .el-input {
    margin-right: 20px;
    ::v-deep .el-input__inner {
      padding: 0 10px;
      border-radius: 0 4px 4px 0;
    }
  }
  i {
    font-size: 14px;
    vertical-align: middle;
    color: #777;
    cursor: pointer;
  }
  i.icon-tianjia {
    margin-right: 16px;
  }

  &.is-error > .el-input ::v-deep .el-input__inner {
    border-left-color: $--bs-color-red;
  }
  > .el-input.is-error ::v-deep .el-input__inner {
    border-color: $--bs-color-red;
  }
  > .is-error ::v-deep .bs-select .el-input__inner {
    border-color: $--bs-color-red;
  }
}
.generate-btn {
  text-align: right;
}
</style>
