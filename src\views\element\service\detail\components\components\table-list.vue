<template>
  <div class="table-list__container">
    <!-- search -->
    <div class="table-list-bar">
      <bs-search v-model="search" :placeholder="$t('pa.placeholder.tableName')" @search="handleSearch" />
      <el-button @click="getTableList(true)">{{ $t('pa.action.refresh') }}</el-button>
    </div>
    <!-- table -->
    <bs-table
      v-loading="loading"
      height="540"
      :data="tableData"
      :page-data="pageData"
      highlight-current-row
      :column-settings="false"
      :column-data="columnData"
      @page-change="handlePageChange"
      @row-click="(row) => $emit('row-click', row)"
    >
      <template v-slot:operator="{ row }">
        <el-tooltip effect="light" :content="$t('pa.viewField')" placement="top">
          <span class="iconfont icon-xinxichaxun" @click="$emit('field-preview', row)"></span>
        </el-tooltip>
      </template>
    </bs-table>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getTableList } from '@/apis/serviceApi';
import { safeArray } from '@/utils';
@Component
export default class TableList extends Vue {
  @Prop({ default: '' }) type!: string;
  @Prop({ default: '' }) id!: string;

  loading = false;
  search = '';
  tableData: any[] = [];
  columnData: any[] = [];
  pageData: any = {
    total: 0,
    pagerCount: 5,
    currentPage: 1,
    layout: 'total, prev, pager, next',
    pageSize: this.$store.getters.pageSize || 25
  };

  created() {
    this.getTableList();
  }

  async getTableList(isRefresh = false) {
    try {
      this.loading = true;
      const { success, data, error } = await getTableList(this.type, this.id, this.search, this.pageData);
      if (!success) return this.$message.error(error);
      this.columnData = safeArray(data?.columnData).concat({
        label: this.$t('pa.action.action'),
        value: 'operator',
        width: 60
      });
      this.tableData = safeArray(data?.tableData);
      this.pageData.total = data?.pageData?.total || 0;
      isRefresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    } finally {
      this.loading = false;
    }
  }
  handleSearch() {
    this.pageData.currentPage = 1;
    this.getTableList();
  }
  handlePageChange(currentPage: number, pageSize: number) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    this.getTableList();
  }
}
</script>
<style lang="scss" scoped>
.table-list {
  &-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 10px 10px;
    .bs-search {
      width: 280px !important;
    }
  }
}
</style>
