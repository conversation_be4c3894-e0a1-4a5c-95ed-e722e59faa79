<template>
  <bs-dialog
    title="测试结果"
    class="test-content-result__dialog"
    width="1000px"
    :visible.sync="testResultDialogVisible"
  >
    <div class="test-content-result__content">
      <el-tabs v-for="tab in tabPaneList" :key="tab.name" class="test-content-result__content--tab">
        {{ tab.label }}
        <el-tab-pane v-for="pane in tab.list" :key="pane.label" :label="pane.label">
          <bs-table
            v-if="input.typeOfData !== 'CUSTOM'"
            class="test-content-result__content--table"
            height="200px"
            :column-settings="false"
            :data="isSql ? pane.data.tableShowData : pane.data.tableData"
            :column-data="pane.data.columnData"
            :page-data="pane.pageData"
            @page-change="handlePageChange(...arguments, tab, pane)"
          />

          <pre v-else class="test-content-result__content--pre">{{ pane.data }}</pre>
        </el-tab-pane>
      </el-tabs>

      <div v-if="!isSql" class="test-content-result__content--data">
        <span>测试结果</span>
        <pre class="test-content-result__content--pre">{{ output.data }}</pre>
      </div>
    </div>
    <div slot="footer">
      <el-button @click="closeDialog">关 闭</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Vue, Component, PropSync, Prop } from 'vue-property-decorator';
@Component
export default class TestResultDialog extends Vue {
  @PropSync('visible', { default: false }) testResultDialogVisible!: boolean;
  // 测试用例数据
  @Prop({ default: () => [] }) input!: any;
  // 测试结果数据
  @Prop({ default: () => [] }) output!: any;
  // 判断流程是否为SQL输入组件
  @Prop({ default: true }) isSql!: boolean;
  @Prop({ default: false }) noInput!: boolean;
  // 选中组件tab
  private activeTestDataTab = '';
  private tabPaneList: any = [];

  created() {
    this.parseData();
  }
  closeDialog() {
    this.testResultDialogVisible = false;
  }
  handlePageChange(currentPage, pageSize, { name }, pane) {
    pane.pageData.currentPage = currentPage;
    pane.pageData.pageSize = pageSize;
    const setData = () => {
      const tabIndex = name === 'data' ? 0 : 1;
      this.tabPaneList[tabIndex].list.forEach((el) => {
        if (el.label === pane.label) {
          el.data.tableShowData = pane.data.tableShowData;
        }
      });
      this.$forceUpdate();
    };
    if (currentPage == 1) {
      pane.data.tableShowData = pane.data.tableData.slice(0, pageSize);
      setData();
      return;
    }
    currentPage = (currentPage - 1) * pageSize;
    pane.data.tableShowData = pane.data.tableData.slice(currentPage, currentPage + pageSize);
    setData();
  }
  parseData() {
    const keyMap = {
      fieldName: '属性',
      fieldType: '值'
    };
    const isSql = this.isSql && !this.noInput;
    const { typeOfData } = this.input;

    this.tabPaneList[0] = {
      name: 'data',
      label: '测试数据',
      list:
        typeOfData === 'CUSTOM'
          ? this.input.data.map((el) => {
              return {
                label: el.sourceNodeName,
                name: el.sourceNodeName,
                data: el.content
              };
            })
          : isSql
          ? this.input.map((el) => {
              return {
                label: el.nodeName,
                name: el.nodeName,
                pageData: { pageSize: 10, currentPage: 1, total: 0 },
                data: {
                  tableData: el.result,
                  tableShowData: [],
                  columnData: el.columns.map((column) => {
                    return {
                      label: column,
                      value: column,
                      minWidth: '160px'
                    };
                  })
                }
              };
            })
          : this.input.data.map((el) => {
              return {
                label: el.sourceNodeName,
                name: el.sourceNodeName,
                ...(this.isSql && { pageData: { pageSize: 10, currentPage: 1, total: 0 } }),
                data: {
                  tableData: JSON.parse(el.content),
                  tableShowData: [],
                  columnData: Object.keys(JSON.parse(el.content)[0]).map((key) => {
                    return {
                      label: ['fieldName', 'fieldType'].includes(key) ? keyMap[key] : key,
                      value: key,
                      minWidth: '160px'
                    };
                  })
                }
              };
            })
    };

    if (this.isSql && typeOfData !== 'CUSTOM') {
      this.tabPaneList[0].list.forEach((el) => {
        el.pageData.total = el.data.tableData.length;
        el.data.tableShowData = el.data.tableData.slice(0, el.pageData.pageSize);
      });
    }
    if (this.isSql) {
      if (typeOfData === 'CUSTOM') {
        this.tabPaneList[1] = {
          name: 'result',
          label: '测试结果',
          list: this.output.map((el) => {
            return {
              label: el.nodeName,
              name: el.nodeName,
              data: JSON.stringify(el.result)
            };
          })
        };
      } else {
        this.tabPaneList[1] = {
          name: 'result',
          label: '测试结果1',
          list: this.output.map((el) => {
            return {
              label: el.nodeName,
              name: el.nodeName,
              pageData: { pageSize: 10, currentPage: 1, total: 0 },
              data: {
                tableData: el.result,
                tableShowData: [],
                columnData: el.columns.map((column) => {
                  return {
                    label: column,
                    value: column,
                    minWidth: '160px'
                  };
                })
              }
            };
          })
        };

        this.tabPaneList[1].list.forEach((el) => {
          el.pageData.total = el.data.tableData.length;
          el.data.tableShowData = el.data.tableData.slice(0, el.pageData.pageSize);
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.test-content-result__content {
  font-weight: bolder;
  &--tab:nth-child(2) {
    margin-top: 20px;
  }
  &--table {
    margin-top: 10px;
    font-weight: normal;
  }
  &--data {
    margin-top: 15px;
  }
  &--pre {
    font-weight: normal;
    height: 137px;
    color: #000;
    font-size: 14px;
    text-align: left;
    line-height: 20px;
    padding: 10px;
    border: 1px solid #e5e5e5;
    overflow: auto;
  }
}
</style>
