// 针对路由名称一致，但是fullPath不一样的情况
const names = [
  'templateDetail',
  'scriptDetail',
  'conScriptDetail',
  'serverDd',
  'func-detail-view',
  'ServiceCustom',
  'FlowDetail',
  'ServiceCustom',
  'ElementRuleTemplateDetail',
  'EventConsumerEdit',
  'ProducerDetail',
  'ElementFilterTemplateDetail',
  'ElementFilterTemplateHistory',
  'ElementMapTemplateEdit',
  'TemplateCharts',
  'ViewEditTab',
  'DataAddEdit',
  'ViewAddEdit',
  'ViewHistory'
];
const isDef = (v: any) => {
  return v !== undefined && v !== null;
};
const getFirstComponentChild = (children: any) => {
  if (Array.isArray(children)) {
    for (const child of children) {
      const c = child;
      if (isDef(c) && isDef(c.componentOptions)) {
        return c;
      }
    }
  }
};
export default {
  name: 'BsKeepAlive',
  functional: true,
  render(h, context) {
    let name = '';
    try {
      const childVDom = getFirstComponentChild(context.slots().default);
      name =
        childVDom &&
        childVDom.componentOptions &&
        (childVDom.componentOptions.Ctor.options.name || childVDom.componentOptions.tag);
      if (name && names.includes(name)) {
        childVDom.key = `${window.location.hash}` || null;
      }
    } catch {
      console.log('can not get component name!');
    }
    return context.parent.$createElement('keep-alive', context.data, context.children);
  }
};
