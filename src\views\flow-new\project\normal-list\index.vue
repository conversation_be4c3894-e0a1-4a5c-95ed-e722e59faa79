<template>
  <pro-page class="normal-list" :title="$t('pa.flow.design')" :fixed-header="false">
    <!-- 项目视图头部信息 -->
    <div slot="operation" class="normal-list-header">
      <!-- 服务级联选择器 -->
      <bs-cascader
        v-model="selectedServices"
        v-access="'PA.FLOW.FLOW_MGR.FILTER'"
        :options="serviceOptions"
        :props="{ checkStrictly: false, emitPath: false, multiple: true }"
        clearable
        collapse-tags
        :placeholder="$t('pa.flow.serviceInfo')"
        class="normal-list-header__service-cascader"
        @change="handleServiceChange"
      />
      <!-- 项目级联选择器 -->
      <bs-cascader
        v-model="selectedProjects"
        v-access="'PA.FLOW.FLOW_MGR.FILTER'"
        :options="projectOptions"
        :props="{ checkStrictly: true, emitPath: false, multiple: true }"
        clearable
        collapse-tags
        :placeholder="$t('pa.flow.projectInfo')"
        class="normal-list-header__project-cascader"
        @change="handleProjectChange"
      />
      <!-- 流程/集群名称搜索 -->
      <bs-search
        v-model.trim="keywords"
        v-access="'PA.FLOW.FLOW_MGR.FILTER'"
        :placeholder="$t('pa.flow.placeholder2')"
        maxlength="30"
        @search="handleSearch"
      />
      <!-- 筛选popover -->
      <filter-popover ref="filterPopover" :visible.sync="filterPopoverVisible" @search="handleFilter" />
      <el-button
        v-for="item in buttonList"
        :key="item.label"
        size="small"
        :icon="item.icon"
        type="primary"
        @click="operateHandler(item.event)"
      >
        {{ item.label }}
      </el-button>
    </div>
    <!-- 流程视图内容 -->
    <normal-list-content
      :params="searchParams"
      :selected-project="selectedProject"
      :project-tree="projectTree"
      @clearSearch="clearSearch"
    />
  </pro-page>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import { hasPermission } from '@/utils';
import { getAllProjectTree, getServiceResList } from '@/apis/flowNewApi';
import NormalListContent from './normal-list-content.vue';
import FilterPopover from '../../components/filter-popover.vue';
@Component({
  components: {
    NormalListContent,
    FilterPopover
  }
})
export default class NormalList extends Vue {
  @Ref('filterPopover') readonly filterPopoverRef!: InstanceType<typeof FilterPopover>;

  projectId = '';
  searchParams: any = {
    name: '',
    jobStatus: 'ALL',
    jobRunTimeStatus: 'ALL',
    mode: 'ALL',
    clusterType: 'ALL',
    jobType: 'ALL'
  };
  keywords = '';
  selectType = {};
  filterPopoverVisible = false;
  fullSearchDrawerVisible = false;
  selectedProject = '';
  selectedProjects: string[] = [];
  selectedServices: string[] = [];
  projectOptions: any[] = [];
  serviceOptions: any[] = [];
  originalServiceData: any[] = []; // 保存原始服务数据，用于过滤
  projectTree: any[] = []; // 保存原始项目树数据，用于查找根目录ID
  get buttonList() {
    return [
      { label: this.$t('pa.flow.batchOperationInfo'), event: 'showBatchOperationInfo', access: 'PA.FLOW.FLOW_MGR.VIEW' },
      {
        label: this.$t('pa.flow.view2'),
        event: 'switchToCardList',
        icon: 'el-icon-sort',
        access: 'PA.FLOW.PROJECT_MGR.VIEW'
      }
    ].filter((item) => hasPermission(item.access));
  }

  created() {
    this.getServiceList();
    this.getProjectList();
  }

  operateHandler(event: string) {
    event && this[event]();
  }

  switchToCardList() {
    this.$emit('switch', true);
  }

  handleSearch() {
    if (!Object.keys(this.selectType).length) this.selectType = this.filterPopoverRef.filterParams;
    this.searchParams = {
      ...this.searchParams, // 保持现有字段
      name: this.keywords,
      ...this.selectType
    };
  }

  clearSearch() {
    this.keywords = '';
    this.filterPopoverRef.handleSearch('clear');
    this.searchParams = {
      name: '',
      jobStatus: 'ALL',
      jobRunTimeStatus: 'ALL',
      mode: 'ALL',
      clusterType: 'ALL',
      jobType: 'ALL'
    };
  }

  // 显示批量操作信息
  showBatchOperationInfo() {
    this.$router.push({
      name: 'batchOperationInfo'
    });
  }

  handleFilter(params: any) {
    this.selectType = params;
    this.searchParams = {
      ...this.searchParams, // 保持现有字段
      name: this.keywords,
      ...params
    };
  }

  // 处理服务选择变化
  handleServiceChange(serviceIds: string[]) {
    // 过滤掉第一级的服务类型，只保留具体的服务ID
    const actualServiceIds = (serviceIds || []).filter((id: string) => {
      // 检查是否是服务类型（第一级）
      const isServiceType = this.originalServiceData.some((item: any) => item.resType === id);
      return !isServiceType; // 只保留不是服务类型的ID（即具体服务的ID）
    });

    this.selectedServices = actualServiceIds;

    // 更新搜索参数
    this.searchParams = {
      ...this.searchParams,
      resId: this.selectedServices.join(',') // 传递逗号分隔的服务ID字符串
    };
  }

  // 处理项目选择变化
  handleProjectChange(projectIds: string[]) {
    this.selectedProjects = projectIds || [];
    // 为了保持向后兼容，设置第一个选中的项目为主项目
    this.selectedProject = projectIds && projectIds.length > 0 ? projectIds[0] : '';

    // 确保包含所有必要的字段
    this.searchParams = {
      name: this.keywords || '',
      jobStatus: 'ALL',
      jobRunTimeStatus: 'ALL',
      mode: 'ALL',
      clusterType: 'ALL',
      jobType: 'ALL',
      ...this.selectType, // 保持用户已选择的筛选条件
      projectId: this.selectedProjects.join(','), // 传递逗号分隔的项目ID字符串
      resId: this.selectedServices.join(',') // 确保服务参数也包含在内
    };
  }

  // 获取服务列表
  async getServiceList() {
    try {
      const { data, success } = await getServiceResList();
      if (success) {
        this.originalServiceData = data; // 保存原始数据
        this.serviceOptions = this.transformServiceData(data);
      }
    } catch (error) {
      // 静默处理错误
    }
  }

  // 获取项目列表数据
  async getProjectList() {
    try {
      const { data } = await getAllProjectTree({ keyword: '' });

      // 设置根目录ID
      const setRootId = (children: any[], rootId: string) => {
        children.forEach((item) => {
          item.rootId = rootId;
          item.children && setRootId(item.children, rootId);
        });
      };
      data.forEach((item: any) => {
        item.rootId = item.nodeId;
        item.children && setRootId(item.children, item.rootId);
      });

      this.projectTree = data; // 保存带有rootId的原始数据
      this.projectOptions = this.transformProjectData(data);
      // 默认不选择任何项目，传递空字符串
      this.selectedProject = '';
      this.selectedProjects = [];
      // 只在初始化时设置默认参数，不调用 handleProjectChange 避免覆盖用户选择
      if (!this.searchParams.projectId) {
        this.searchParams = {
          name: this.keywords || '',
          jobStatus: 'ALL',
          jobRunTimeStatus: 'ALL',
          mode: 'ALL',
          clusterType: 'ALL',
          jobType: 'ALL',
          ...this.selectType,
          projectId: '',
          resId: this.selectedServices.join(', ')
        };
      }
    } catch (error) {
      // 静默处理错误
    }
  }

  // 转换服务数据格式
  transformServiceData(data: any[]): any[] {
    return data.map((resType) => {
      const result: any = {
        value: resType.resType, // 第一级有 value，可以被选择
        label: resType.title
      };

      if (resType.resList && resType.resList.length > 0) {
        result.children = resType.resList.map((res: any) => ({
          value: res.resId,
          label: res.title,
          resType: res.resType
        }));
      } else {
        // 如果没有子服务，禁用该服务类型
        result.disabled = true;
      }

      return result;
    });
  }

  // 转换项目数据格式
  transformProjectData(data: any[]): any[] {
    return data.map((item) => ({
      value: item.nodeId,
      label: item.nodeName,
      jobCount: item.jobCount,
      children: item.children && item.children.length > 0 ? this.transformProjectData(item.children) : undefined
    }));
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-icon-sort {
  transform: rotate(90deg);
}
::v-deep .el-form-item__content .el-input--suffix {
  width: 100% !important;
}
::v-deep .bs-pro-page__header .bs-pro-page__header-operation .el-input {
  width: 218px;
}
::v-deep .bs-pro-page__header .bs-pro-page__header-operation {
  .bs-search {
    width: 218px !important;
    margin-left: 12px !important;
  }

  .normal-list-header__service-cascader,
  .normal-list-header__project-cascader {
    &.el-cascader {
      width: 150px !important;
    }

    .el-input {
      width: 150px !important;
    }

    // 确保折叠标签宽度不会撑开高度
    .el-tag--info {
      max-width: 65px !important;
    }
  }
}
.normal-list-header {
  display: flex;
  align-items: center;
  & > *:not(:first-child) {
    margin-left: 12px;
  }
  &__search {
    color: #377cff;
    background: #ebf2ff;
    border-color: #afcbff;
  }
}
</style>
