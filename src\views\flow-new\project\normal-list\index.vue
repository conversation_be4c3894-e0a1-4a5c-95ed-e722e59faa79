<template>
  <pro-page class="normal-list" title="流程设计" :fixed-header="false">
    <!-- 项目视图头部信息 -->
    <div slot="operation" class="normal-list-header">
      <!-- 服务级联选择器 -->
      <bs-cascader
        v-model="selectedServices"
        v-access="'PA.FLOW.FLOW_MGR.FILTER'"
        :options="serviceOptions"
        :props="{ checkStrictly: false, emitPath: false, multiple: true }"
        clearable
        size="small"
        collapse-tags
        placeholder="请选择服务"
        class="normal-list-header__service-cascader"
        @change="handleServiceChange"
      />
      <!-- 项目多选选择器 -->
      <bs-select
        v-model="selectedProjects"
        v-access="'PA.FLOW.FLOW_MGR.FILTER'"
        :options="projectOptions"
        multiple
        clearable
        size="small"
        collapse-tags
        filterable
        placeholder="请选择项目"
        class="normal-list-header__project-select"
        @change="handleProjectChange"
      />
      <!-- 流程/集群名称搜索 -->
      <bs-search
        v-model.trim="keywords"
        v-access="'PA.FLOW.FLOW_MGR.FILTER'"
        placeholder="请输入流程名、集群名"
        maxlength="30"
        @search="handleSearch"
      />
      <!-- 筛选popover -->
      <filter-popover
        ref="filterPopover"
        :visible.sync="filterPopoverVisible"
        @search="handleFilter"
      />
      <el-button
        v-access="'PA.FLOW.FLOW_MGR.FILTER'"
        plain
        type="primary"
        @click="showFullSearchDrawer"
      >
        全量搜索
      </el-button>
      <el-button
        v-access="'PA.FLOW.FLOW_MGR.VIEW'"
        size="small"
        type="primary"
        @click="showBatchOperationInfo"
      >
        批量任务
      </el-button>
      <el-button
        v-for="item in buttonList"
        :key="item.label"
        size="small"
        :icon="item.icon"
        type="primary"
        @click="operateHandler(item.event)"
      >
        {{ item.label }}
      </el-button>
      <!-- 新建流程弹窗 -->
      <add-flow
        v-if="addFlowDialogVisible"
        :visible.sync="addFlowDialogVisible"
        :project-id="projectId"
        @close="handleAddFlowDialogClose"
      />
      <!-- 导入流程弹窗 -->
      <import-flow-dialog
        v-if="importFlowDialogVisible"
        :show.sync="importFlowDialogVisible"
        :project-id="projectId"
        @close="handleImportFlowDialogClose"
      />
    </div>
    <!-- 流程视图内容 -->
    <normal-list-content
      ref="normalListContent"
      :params="searchParams"
      @clearSearch="clearSearch"
    />
  </pro-page>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { hasPermission } from '@/utils';
import { getProjectList, getServiceResList } from '@/apis/flowNewApi';
import NormalListContent from './normal-list-content.vue';
import FilterPopover from './filter-popover.vue';
import AddFlow from '@/views/flow-new/design/flow-list/modals/add-flow.vue';
import ImportFlowDialog from '../../components/import-flow-dialog.vue';
@Component({
  components: {
    NormalListContent,
    FilterPopover,
    ImportFlowDialog,
    AddFlow
  }
})
export default class NormalList extends Vue {
  projectId = '';
  searchParams: any = {
    name: '',
    jobStatus: 'ALL',
    jobRunTimeStatus: 'ALL',
    mode: 'ALL',
    clusterType: 'ALL',
    jobType: 'ALL'
  };
  keywords = '';
  selectType = {};
  addFlowDialogVisible = false;
  filterPopoverVisible = false;
  fullSearchDrawerVisible = false;
  importFlowDialogVisible = false;
  selectedProject = '';
  selectedProjects: string[] = [];
  selectedServices: string[] = [];
  projectOptions: any[] = [];
  serviceOptions: any[] = [];
  originalServiceData: any[] = []; // 保存原始服务数据，用于过滤
  get buttonList() {
    return [
      // { label: '新建', event: 'showAddProjectDialog', access: 'PA.FLOW.FLOW_MGR.ADD' },
      // { label: '导入', event: 'showImportFlowDialog', access: 'PA.FLOW.FLOW_MGR.IMPORT' },
      // { label: '刷新', event: 'refreshList', access: 'PA.FLOW.FLOW_MGR.VIEW' },
      {
        label: '切换视图',
        event: 'switchToCardList',
        icon: 'el-icon-sort',
        access: 'PA.FLOW.PROJECT_MGR.VIEW'
      }
    ].filter((item) => hasPermission(item.access));
  }

  mounted() {
    this.getServiceList();
    this.getProjectList();
  }

  operateHandler(event: string) {
    event && this[event]();
  }

  showAddProjectDialog() {
    this.projectId = (this.$refs.normalListContent as any).currentId;
    this.projectId && (this.addFlowDialogVisible = true);
  }

  showImportFlowDialog() {
    this.projectId = (this.$refs.normalListContent as any).currentId;
    this.projectId && (this.importFlowDialogVisible = true);
  }

  // 显示批量操作信息
  showBatchOperationInfo() {
    this.$router.push({
      name: 'batchOperationInfo'
    });
  }

  switchToCardList() {
    this.$emit('switch', true);
  }

  showFullSearchDrawer() {
    this.$emit('showDrawer');
  }

  handleSearch() {
    if (!Object.keys(this.selectType).length)
      this.selectType = (this.$refs.filterPopover as any).selectType;
    this.searchParams = {
      ...this.searchParams,
      name: this.keywords,
      ...this.selectType
    };
  }

  // 刷新流程列表
  refreshList() {
    (this.$refs.normalListContent as any).getFlowList(true);
  }

  handleFilter(params: any) {
    this.selectType = params;
    this.searchParams = {
      ...this.searchParams,
      name: this.keywords,
      ...params
    };
  }

  // 处理服务选择变化
  handleServiceChange(serviceIds: string[]) {
    // 过滤掉第一级的服务类型，只保留具体的服务ID
    const actualServiceIds = (serviceIds || []).filter((id: string) => {
      // 检查是否是服务类型（第一级）
      const isServiceType = this.originalServiceData.some((item: any) => item.resType === id);
      return !isServiceType; // 只保留不是服务类型的ID（即具体服务的ID）
    });

    this.selectedServices = actualServiceIds;

    // 更新搜索参数，创建新对象确保Vue能检测到变化
    this.searchParams = {
      name: this.keywords || '',
      jobStatus: 'ALL',
      jobRunTimeStatus: 'ALL',
      mode: 'ALL',
      clusterType: 'ALL',
      jobType: 'ALL',
      ...this.selectType, // 保持用户已选择的筛选条件
      projectId: this.selectedProjects.length > 0 ? this.selectedProjects.join(',') : 'ALL', // 没有选择时为ALL
      resId: this.selectedServices.length > 0 ? this.selectedServices.join(',') : 'ALL' // 没有选择时为ALL，多选用逗号分隔
    };
  }

  // 处理项目选择变化
  handleProjectChange(projectIds: string[]) {
    this.selectedProjects = projectIds || [];
    // 为了保持向后兼容，设置第一个选中的项目为主项目
    this.selectedProject = projectIds && projectIds.length > 0 ? projectIds[0] : '';

    // 确保包含所有必要的字段
    this.searchParams = {
      name: this.keywords || '',
      jobStatus: 'ALL',
      jobRunTimeStatus: 'ALL',
      mode: 'ALL',
      clusterType: 'ALL',
      jobType: 'ALL',
      ...this.selectType, // 保持用户已选择的筛选条件
      projectId: this.selectedProjects.length > 0 ? this.selectedProjects.join(',') : 'ALL', // 没有选择时为ALL
      resId: this.selectedServices.length > 0 ? this.selectedServices.join(',') : 'ALL' // 没有选择时为ALL，多选用逗号分隔
    };
  }

  handleImportFlowDialogClose() {
    this.importFlowDialogVisible = false;
    this.handleSearch();
  }

  handleAddFlowDialogClose() {
    this.addFlowDialogVisible = false;
    this.handleSearch();
  }

  clearSearch() {
    this.keywords = '';
    this.searchParams = {
      name: '',
      jobStatus: 'ALL',
      jobRunTimeStatus: 'ALL',
      mode: 'ALL',
      clusterType: 'ALL',
      jobType: 'ALL',
      projectId: 'ALL',
      resId: 'ALL'
    };
    this.selectType = {};
    this.selectedProjects = [];
    this.selectedProject = '';
    this.selectedServices = [];
    (this.$refs.filterPopover as any).clearFilter();
  }

  // 获取服务列表
  async getServiceList() {
    try {
      const { data, success } = await getServiceResList();
      if (success) {
        this.originalServiceData = data; // 保存原始数据
        this.serviceOptions = this.transformServiceData(data);
      }
    } catch (error) {}
  }

  // 获取项目列表数据
  async getProjectList() {
    try {
      const { data } = await getProjectList({ name: '', sortByName: false });
      this.projectOptions = this.transformProjectData(data);
      // 默认不选择任何项目，传递空字符串
      this.selectedProject = '';
      this.selectedProjects = [];
      // 只在初始化时设置默认参数，不调用 handleProjectChange 避免覆盖用户选择
      if (!this.searchParams.projectId) {
        this.searchParams = {
          name: this.keywords || '',
          jobStatus: 'ALL',
          jobRunTimeStatus: 'ALL',
          mode: 'ALL',
          clusterType: 'ALL',
          jobType: 'ALL',
          ...this.selectType,
          projectId: 'ALL',
          resId: 'ALL'
        };
      }
    } catch (error) {}
  }

  // 转换服务数据格式
  transformServiceData(data: any[]): any[] {
    return data.map((resType) => {
      const result: any = {
        value: resType.resType, // 第一级有 value，可以被选择
        label: resType.title
      };

      if (resType.resList && resType.resList.length > 0) {
        result.children = resType.resList.map((res: any) => ({
          value: res.resId,
          label: res.title,
          resType: res.resType
        }));
      } else {
        // 如果没有子服务，禁用该服务类型
        result.disabled = true;
      }
      return result;
    });
  }

  // 转换项目数据格式
  transformProjectData(data: any[]): any[] {
    return data.map((item) => ({
      value: item.projectId,
      label: item.projectName
    }));
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-icon-sort {
  transform: rotate(90deg);
}
::v-deep .el-form-item__content .el-input--suffix {
  width: 100% !important;
  margin-left: 0px !important;
}
::v-deep .bs-pro-page__header .bs-pro-page__header-operation .el-input {
  width: 188px;
}
::v-deep .bs-pro-page__header .bs-pro-page__header-operation {
  .bs-search {
    width: 188px !important;
    margin-left: 12px !important;
  }

  .el-cascader {
    width: 278px !important;
  }

  .normal-list-header__service-cascader,
  .normal-list-header__project-select {
    &.el-cascader {
      width: 145px !important;
    }

    .el-input {
      width: 145px !important;
    }
    .el-tag--info {
      max-width: 56px !important;
    }
  }
}
.normal-list-header {
  display: flex;
  align-items: center;
  & > *:not(:first-child) {
    margin-left: 12px;
  }
  &__search {
    color: #377cff;
    background: #ebf2ff;
    border-color: #afcbff;
  }
}
</style>
