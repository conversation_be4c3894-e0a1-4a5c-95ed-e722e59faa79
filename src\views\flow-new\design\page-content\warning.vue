<template>
  <div class="warning__container">
    <warn-rule ref="warnRuleRef" :flow-id="flowId" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Ref, Vue } from 'vue-property-decorator';
import WarnRule from '@/views/element/service/detail/components/warn-rules.vue';

@Component({ components: { WarnRule } })
export default class FlowWarning extends Vue {
  @Prop({ default: '' }) flowId!: string;
  @Ref('warnRuleRef') readonly warnRule!: WarnRule;

  mounted() {
    this.warnRule.getWarnRuleList();
  }
}
</script>
<style lang="scss" scoped>
.warning {
  &__container {
    height: calc(100% - 40px);
    background: #fff;
    ::v-deep .bs-detail-block {
      height: 100% !important;
      .tab-content {
        height: calc(100% - 10px) !important;
        .el-table__body-wrapper {
          height: calc(100% - 100px) !important;
        }
      }
    }
  }
}
</style>
