{"i18n-ally.localesPaths": ["src/i18n/lang"], "i18n-ally.sortKeys": true, "i18n-ally.keystyle": "nested", "i18n-ally.namespace": true, "i18n-ally.enabledParsers": ["json", "js", "ts"], "i18n-ally.displayLanguage": "zh-CN", "cSpell.words": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "BSUI", "bsview", "cancle", "clickoutside", "daterange", "Datestream", "datetime", "HDFS", "INOFF", "INPROD", "INPUB", "INSTALLATIONPACKAGE", "loadmore", "onlining", "PROCESSFLOW", "savepoint", "timerange", "wujie", "<PERSON><PERSON><PERSON>"], "i18n-ally.extract.ignored": ["YYYY-MM-DD HH:mm:ss", "String", "Boolean", "Date", "Double", "Float", "Integer", "<PERSON>", "&", "({{ it.count }})", "iconfont icon-bianji", "iconfont icon-shanchu", "iconfont icon-chakan", "iconfont icon-fenxiang", "iconfont icon-jiquntopic", "iconfont icon-zhongduan", "iconfont icon-shangchuan", "calc(100vh - 288px)", "calc(100vh - 333px)", "[object File]", "calc(100vh - 181px)", "iconfont icon-qidong", "iconfont icon-guanbi", "*", "Catalog", "{{ subKey }}:", "{{ subKey }}:", "Topics", "iconfont icon-yinyongguanxi", "total, prev, pager, next, jumper", "：{{ lagTotal }}", "{a} <br/>{b} : {c} ({d}%)", "rgba(0, 0, 0, 0.5)", "${params.seriesName}</br>${params.data.name}: ${params.data.des} (${params.percent}%)</br>总共: ${params.data.total}", "`${it.label}:`", "total, prev, pager, next", "Not valid JSON string", "Not valid JSON string", "Not valid XML string", "Not valid XML string", "Text", "Timestamp", "Value", "Key", "Partition", "Offset", " ：\n            ", "：", "<span class=\"code-red\"> $1 </span>", "${funcName}(<span class=\"code-green\">${params.join(',')}</span>)", "|", "、", "（", "）", "`${item.title}${item.url ? '（' + item.url + '）' : ''}`", "简体中文", "English", "Language", "iconfont icon-yong<PERSON><PERSON>nxian2", "iconfont icon-xitongpeizhi3", "iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON>", "iconfont icon-data-source", "?", "=", "<!DOCTYPE html>", "invalid session", ";", "100vh - 180px", "<PERSON><PERSON>", "Arrow", "Bottom", "Top", ".el-select-dropdown .el-select-dropdown__wrap", "iconfont icon-<PERSON><PERSON><PERSON>guanx<PERSON>", "iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON>", "${getClustersTitle(to.query.resType)}：${to.query.title}", "iconfont icon-zhuye", "\n          {{ item.label }}({{ item.number }})\n        ", "<span style=\"color:#377CFF\">${a}</span>", "[object Object]", "Refresh", "×", "${this.zoomValue}%", "M 0,0 L 8,4 L 8,-4 Z", "M 0,0 L 8,4 L 8,-4 Z", "M -6,4 L 0,0 L -6,-4", "data:image/svg+xml;base64,${window.btoa(\n    String.fromCharCode.apply(null, new TextEncoder().encode(svgContent) as any)\n  )}", "All", "calc(70vh - 283px)", "calc(70vh - 208px)", "Fields", " * ", "calc(100vh - 264px)", "public static String functionName(){\n    return \"\";\n}", "${URL_UDF_ADD_ON_UPLOAD}/?tempSave=${this.tempSave}&createType=${\n                  this.chartInfo.createType\n                }&udfExplain=${encodeURIComponent(this.chartInfo.businessExplain)}&udfName=${\n                  this.chartInfo.udf\n                }&udfNameCn=${encodeURIComponent(this.chartInfo.udfCn)}&udfNameMain=${this.chartInfo.domain}&udfType=${\n                  this.chartInfo.type\n                }", "iconfont icon-roll-back", "iconfont icon-ziyuan", "iconfont icon-xia<PERSON>", "<svg width=\"${width}px\" height=\"${width}px\" viewBox=\"0 0 ${size} ${size}\"", "custom-item is removed, use scoped slot instead.", "props is removed, use value-key instead.", ".el-autocomplete-suggestion__list li", "Test", "iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "iconfont icon-piliang", "iconfont icon-shaixuan", "iconfont icon-sousuo", "iconfont icon-shuaxin", "iconfont icon-fabu", "iconfont icon-quxiaofabu", "iconfont icon-qidong1", "iconfont icon-tingzhi", "iconfont icon-fuzhi", "iconfont icon-yidong", "【", "】", "…", "<p class=\"error-msg\">", "<p class=\"error-msg\">", "（{{ raw.jobCount }}）", "iconfont icon-baocun ${this.isDev ? 'is-active' : ''}", "iconfont icon-bianyi ${this.isDev && !this.typeIsJar ? 'is-active' : ''}", "iconfont icon-ceshi ${this.isDev && !this.typeIsJar ? 'is-active' : ''}", "iconfont icon-fabu ${this.isDev ? 'is-active' : ''}", "iconfont icon-quxiaofabu ${this.isPub ? 'is-active' : ''}", "iconfont icon-xieyuanguanxi2 ${!this.typeIsJar && !this.isDev ? 'is-active' : ''}", "(", "[", "]", "showText.slice(0, -1)", "${val.name}、", "：\n          ", "~", "!", "@", "，", "`", "。", "Detail", "Relationship", "In", "Decimal", "`${title || ''}`", "Invalid Date", "\n      {{ group.title }} (\n      ", "{{ $t('pa.flow.str') }}：", ")", "{{ $t('pa.flow.resultStr') }}：", "Processing", "Event", "Tu<PERSON>ling", "YYYY-MM-DD HH:mm", "ws://${window.location.hostname}:${window.location.port}${\n      process.env.NODE_ENV === 'development' ? baseUrl.prev : window.location.pathname.replace('/pipeace.html', '')\n    }", "${alias || name}.\\`${f}\\` as \\`${f}\\`,", "CREATE TABLE", "&nbsp;a.`district` as `district`", "&nbsp;u.`area_id` as `area_id`,", "&nbsp;u.`number` as `number`,", "&nbsp;u.`name` as `name`,", "&nbsp;a.`id` as `id`,", "&nbsp;a.`province` as `province`,", "&nbsp;a.`city` as `city`,", "&nbsp;u.`sex` as `sex`,", "&nbsp;u.`id` as `id`,", "{{ $t('pa.flow.table') }}area：id,province,city,district", "{{ $t('pa.flow.table') }}user：id,name,sex,number,area_id", ".editor-scrollable .lines-content", "calc(100vh - ${this.isFullScreen ? 100 : 166 + addHeight}px)", "「/」{{ $t('pa.flow.util') }} ", "{{ $t('pa.flow.qkey') }}：/", "iconfont icon-huabu", "iconfont icon-bao<PERSON>n", "iconfont icon-yujing", "iconfont icon-yujing", "iconfont icon-l<PERSON><PERSON><PERSON>", "iconfont icon-banben", "iconfont icon-rizhi", "iconfont icon-y<PERSON><PERSON><PERSON>", "%", "Flow", "TPS：{{ nodeInfo.tps }}", "ws://${window.location.hostname}:${window.location.port}${\n    process.env.NODE_ENV === 'development' ? baseUrl.prev : window.location.pathname.replace('/pipeace.html', '')\n  }", "<font style='color: #377cff'>${keyword}</font>", "iconfont icon-s<PERSON><PERSON><PERSON><PERSON>", "iconfont icon-s<PERSON><PERSON><PERSON><PERSON>", "<span style=\"display:inline-block;color: #777777;font-weight: 400;padding:5px 0\">${params.name}</span>\n      <br/>\n      <span>${params.seriesName}</span><span style=\"display:inline-block;margin-left:12%\">${params.data.number}</span>${\n        unitMap[params.name]\n      }<br/>\n      <span>占比</span><span style=\"display:inline-block;margin-left:22%\">${params.value}</span>%<br/>\n    ", "Slots", "Ad", "<span style=\"display:inline-block;widht:5px;height:5px;background:${params.color};border-radius:50%\"></span><span>${params.data.name}:${params.data.value}</span>", "<span style=\"display:inline-block;color: #777777;font-weight: 400;padding:5px 0\">${value[0].axisValue}</span>\n    <br/>\n    <span>${value[0].seriesName}：</span><span style=\"display:inline-block;margin-left:5%\">${value[0].data.displayValue}${value[0].data.unit}</span>", "${name}\\xa0\\xa0\\xa0\\xa0${val[0].value}\\xa0\\xa0\\xa0${\n          val[0].value === 0 ? '0.00' : ((val[0].value / pieCount) * 100).toFixed(2)\n        }%", "Top 5", "Top 10", "Top 15", "calc(70vh - 285px)", "calc(70vh - 210px)", "Enum", "iconfont icon-lishi", "prev, pager, next", "<", ">", "Kafka", "calc(70vh - 150px)", "[{ \"data\": \"\" }]", "yyyy-MM-dd HH:mm:ss", "<span style=\"background:yellow\">${a}</span>", " ：", "；", "return import(\"./vite-svg-register.js\")", "\n      )\n      "], "i18n-ally.extract.ignoredByFiles": {"src/views/flow-new/design/page-content/config-popup/component/detail/modals/node-filter/logic-relation.vue": [")"]}, "editor.formatOnPaste": false, "editor.formatOnSave": true}