<template>
  <div class="bar__container">
    <span class="bar-title">{{ title }}</span>
    <!-- 结果表达式 -->
    <el-tooltip
      v-if="showResult"
      :key="resultExpression"
      v-hide="resultExpression"
      effect="light"
      placement="top"
    >
      <div slot="content" v-html="resultExpression"></div>
      <div class="bar-result" v-html="resultExpression"></div>
    </el-tooltip>
    <!-- 逻辑关系  -->
    <el-form v-else class="bar-logic__form">
      <el-select
        v-model="type"
        class="bar-logic__form__type"
        filterable
        :disabled="disabled"
        @change="handleChange($event, 'select')"
      >
        <el-option v-for="el in typeList" :key="el.value" :label="el.label" :value="el.value" />
      </el-select>
      <el-tooltip
        :disabled="expressionDisabled"
        effect="light"
        :content="expression"
        placement="top"
      >
        <el-form-item :error="errorMsg" class="bar-logic__form__item">
          <el-input
            v-model="expression"
            class="bar-logic__form__expression"
            :disabled="disabled || inputDisabled"
            @input="handleChange($event, 'input')"
          />
        </el-form-item>
      </el-tooltip>
    </el-form>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue, Watch } from 'vue-property-decorator';
import { debounce } from 'lodash';
import { hide } from './utils';

@Component({
  directives: {
    hide
  }
})
export default class Bar extends Vue {
  @PropSync('logicType', { default: '&&' }) type!: string;
  @PropSync('logicExpression', { default: '' }) expression!: string;
  @PropSync('expressionError', { default: '' }) errorMsg!: string;
  @Prop() title!: string;
  @Prop() resultExpression!: string;
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => [] }) conditions!: any[];
  @Prop({ type: Boolean, default: false }) showResult!: boolean;

  private typeList: any[] = [
    {
      value: '&&',
      label: '全‘&&’关系'
    },
    {
      value: '||',
      label: '全‘||’关系'
    },
    {
      value: 'CUSTOM',
      label: '自定义关系'
    }
  ];
  private handleClick = debounce(() => this.$emit('click'));

  get inputDisabled() {
    return this.type !== 'CUSTOM';
  }

  get expressionDisabled() {
    if (this.inputDisabled) {
      return this.expression.length < (this.type === '&&' ? 50 : 70);
    }
    return !this.disabled;
  }

  @Watch('conditions', { immediate: true, deep: true })
  checkExpression() {
    const letter = this.conditions.map(({ name }: any) => name).join('|');
    const rule = new RegExp(`(\\&\\&|\\|\\||\\(|\\)|\\s|${letter})+`, 'g');
    const str = this.expression.replace(rule, '');
    this.errorMsg = str.length > 0 ? '请输入合法的逻辑关系！' : '';
  }

  async handleChange(type, name) {
    await this.$emit('change', ...[type, name]);
    if (name !== 'input') return;
    this.checkExpression();
  }
}
</script>
<style lang="scss" scoped>
.bar {
  &__container {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 35px;
  }

  &-title {
    margin-right: 12px;
    font-size: 14px;
    font-weight: 400;
    color: #444444;
    line-height: 20px;
  }

  &-result {
    width: calc(100% - 100px);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &-logic {
    &__form {
      display: flex;
      align-items: center;
      width: calc(100% - 76px);

      &__item {
        margin-bottom: 0px !important;
      }

      &__type {
        margin-right: 10px;
        width: 120px;
      }

      &__expression {
        width: 360px;
      }
    }
  }
}
</style>
