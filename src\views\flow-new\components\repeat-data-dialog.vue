<template>
  <el-dialog
    width="35%"
    append-to-body
    :visible.sync="display"
    class="repeat-container"
    title="平台中已存在下列名称的流程，请更改流程"
  >
    <!-- 统计信息 -->
    <div class="repeat-bar">
      <span>共</span>
      <span class="repeat-highLight">{{ total }}</span>
      <span>条,需处理</span>
      <span class="repeat-highLight">{{ must }}</span>
      <span>条,已处理</span>
      <span class="repeat-highLight">{{ solved }}</span>
      <span>条</span>
    </div>
    <!-- 表单 -->
    <el-table :data="tableData" :height="280">
      <el-table-column prop="jobName" label="名称" />
      <el-table-column prop="newJobName" label="新的名称">
        <template slot-scope="scope">
          <el-input v-model="scope.row.newJobName" @input="handleInput($event, scope.row.id)" />
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 -->
    <div class="repeat-pagination">
      <el-pagination
        background
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        layout="sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- footer -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="display = false">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Vue, Watch } from 'vue-property-decorator';
import { cloneDeep, debounce } from 'lodash';

@Component
export default class RepeatData extends Vue {
  @PropSync('show', { type: Boolean }) display!: boolean;
  @PropSync('data', { type: Array }) list!: any[];
  @Prop({ default: '' }) projectId!: string;
  private tableData: any = []; // 显示数据
  private rootList: any = []; // 元数据
  private form: object = {}; // 修改信息存放
  private total = 0; // 总条数
  private must = 0; // 需处理条数
  private solved = 0; // 以处理条数
  private pageSize = 10; // 没有条数
  private currentPage = 1; // 页数
  private submit = debounce(this.handleSubmit, 1000);

  @Watch('display')
  handleDisplayChange(val) {
    if (!val) {
      this.rootList = [];
      this.currentPage = 1;
      this.pageSize = 10;
      this.solved = 0;
      this.form = {};
    }
  }

  @Watch('list')
  handleRootData(val) {
    this.rootList = cloneDeep(this.trans(val));
    this.solved = 0;
  }

  @Watch('rootList')
  handleRootList() {
    this.total = this.must = this.rootList.length;
    this.generateTableData();
  }

  /* 输入事件处理 */
  handleInput(e = '', id = '') {
    if (id) {
      this.form[id] = e;
      this.solved = Object.values(this.form).filter((o) => o).length;
      this.must = this.total - this.solved;
    }
  }

  /* 生成表格数据 */
  generateTableData() {
    const { currentPage, pageSize, rootList } = this;
    this.tableData = [...rootList].splice((currentPage - 1) * pageSize, pageSize);
  }

  /* 条数变化 */
  handleSizeChange(val) {
    this.currentPage = 1;
    this.pageSize = val;
    this.generateTableData();
  }

  /* 页数变化 */
  handleCurrentChange(val) {
    this.currentPage = val;
    this.generateTableData();
  }

  async handleSubmit() {
    const { form, projectId, rootList } = this;
    const jobs = [...rootList].map((item) => {
      const newJobName = form[item.id];
      if (newJobName) {
        item.jobName = newJobName;
      }
      return item;
    });
    const res: any = await Vue.axios.post(`${'/rs/pa/job/importJob'}?projectId=${projectId}`, jobs);
    if (res.success) {
      if (Array.isArray(res.data)) {
        this.rootList = this.trans(res.data, true);
        this.currentPage = 1;
        this.pageSize = 10;
        this.solved = 0;
        this.form = {};
        this.$message.warning(res.msg);
      } else {
        this.$message.success(res.msg);
        this.display = false;
        this.$emit('close');
      }
    } else {
      this.$message.error(res.msg);
    }
  }

  trans(list = [], needChange = false) {
    return list.map((item) => {
      const only: any = item;
      if (needChange) {
        only.jobName = only.applicationId;
      } else {
        only.applicationId = only.jobName;
      }
      return item;
    });
  }
}
</script>

<style lang="scss" scoped>
.repeat {
  &-container {
    overflow: hidden;

    ::v-deep .el-dialog {
      &__body {
        padding: 10px 20px;
        height: 370px;
        overflow-x: hidden;
        overflow-y: auto;
        box-sizing: border-box;
      }
    }
  }

  &-bar {
    padding: 0 0 10px;
    text-align: right;
  }

  &-highLight {
    display: inline-block;
    padding: 0 5px;
    color: orange;
  }

  &-form {
    &-item {
      margin: 0;
    }
  }

  &-pagination {
    margin: 12px 0 0;
    text-align: right;
  }
}
</style>
