<template>
  <bs-dialog :title="title" size="large" :visible.sync="display" @close="close">
    <div v-loading="loading" class="container">
      <config-form v-if="isInit" ref="configRef" :data="formData" />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t('pa.action.cancel') }}</el-button>
      <el-button type="primary" @click="save()">{{ $t('pa.action.save') }}</el-button>
      <el-button v-if="isOnline" type="primary" @click="online">{{ $t('pa.action.online') }}</el-button>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import { getFlowById, onlineFlow, preOnlineFlow, updateResourceConfig } from '@/apis/flowNewApi';
import { FLOW_DEFAULT_CONFIG } from './config';
@Component({
  components: {
    ConfigForm: () => import('./components/index.vue')
  }
})
export default class SingleResourceConfig extends Vue {
  @PropSync('show', { required: true, default: false }) display!: boolean;
  @Prop({ required: true }) flowId!: string;
  @Prop({ required: true }) projectId!: string;
  @Prop({ default: true }) status!: boolean; // 启动：无状态启动&基于上次状态启动
  @Prop({ default: true }) isOnline!: boolean; // 是否展示启动按钮
  @Ref('configRef') readonly configRef!: any;

  private loading = false;
  private formData: any = FLOW_DEFAULT_CONFIG();
  isInit = false;
  get title() {
    return this.$t('pa.resourceConf', [this.formData.jobName]);
  }

  created() {
    this.getFlowDetail();
  }

  async getFlowDetail() {
    this.loading = true;
    const { success, data, msg } = await getFlowById({ id: this.flowId, isMonitor: false });
    this.loading = false;
    this.isInit = true;
    if (success) {
      this.formData = {
        ...this.formData,
        ...JSON.parse(data?.properties || '{}'),
        jobName: data.jobName,
        jobType: data.jobType,
        orgId: data.orgId,
        memo: data.memo,
        jobStatus: data.jobStatus,
        originalJobName: data.originalJobName,
        prefix: data.prefix,
        suffix: data.suffix,
        projectId: data.projectId
      };
      return;
    }
    this.$tip.error(msg);
  }

  /**
   * @description: 直接点击【保存】、如果是流程监控会多个【启动】按钮，点击【启动】前先进行【保存】
   * @param { boolean } beforeOnline 是否是点击【启动】前的保存，如果是则不进行更新成功的提示
   */
  async save(beforeOnline?: boolean) {
    const data = await this.configRef.getFormData();
    this.loading = true;
    data.pajob = new Blob([JSON.stringify(Object.assign(data.pajob, { id: this.flowId, projectId: this.projectId }))], {
      type: 'application/json'
    });
    const { success, msg, error } = await updateResourceConfig(data);
    this.loading = false;
    this.close();
    if (success) {
      if (!beforeOnline) this.$tip.success(msg);
      return;
    }
    this.$tip.error(error);
  }

  close() {
    this.display = false;
    this.$emit('close');
  }

  async online() {
    await this.save(true);
    this.loading = true;
    const params = [{ id: this.flowId, fromLastCheckpoint: this.status }];
    const res = await preOnlineFlow(params.map(({ id }) => id));
    if (res.success) {
      this.$tip.success(res.msg);
      const { success, msg, data, msgType } = await onlineFlow(params);
      this.loading = false;
      if (success) {
        this.$tip.success(msg);
        this.close();
        return;
      }
      this.$tip.errorPro({ msgType, msg, data });
      return;
    }
    this.loading = false;
    this.$tip.errorPro(res);
  }
}
</script>
<style lang="scss" scoped>
.container {
  min-height: 50vh;
}
</style>
