<template>
  <bs-dialog
    title="导出"
    :visible.sync="visible"
    width="40%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
  >
    <div style="width: 100%; height: 300px">
      <div style="height: 100%; box-sizing: border-box">
        <div class="head">
          <bs-search
            v-model="filterText"
            placeholder="输入关键字进行过滤"
            clearable
            style="width: 200px; margin-right: 10px"
          />
          <el-button type="primary" @click="allChecked">全选</el-button>
          <el-button type="primary" @click="resetChecked">全不选</el-button>
        </div>
        <div class="dia-tree">
          <el-tree
            ref="treeRef"
            v-loading="treeLoading"
            node-key="id"
            show-checkbox
            :default-expand-all="false"
            :data="treeData"
            :props="defaultProps"
            :filter-node-method="filterNode"
            @check-change="handleCheckChange"
          />
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" :loading="loading" @click="submit">导 出</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import {
  Component,
  Prop,
  Ref,
  Inject,
  Watch,
  Emit,
  Vue,
  InjectReactive
} from 'vue-property-decorator';
import { URL_EXPIMP_GETRESLIST, URL_EXPIMP_DOWNLOADRECORDS } from '@/apis/commonApi';
import { groupBy } from 'lodash';
import { get, post } from '@/apis/utils/net';
import Tree from 'bs-ui-pro/lib/tree';
@Component
export default class Export extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Inject('enableSql') enableSql;
  @InjectReactive('isFuseMode') isFuseMode!: boolean;
  @Ref('treeRef') readonly tree!: Tree;
  loading = false;
  treeLoading = false;
  filterText = '';
  defaultProps: any = {
    children: 'children',
    label: 'label'
  };
  treeData: any = [];

  @Emit('close')
  private closeDialog() {
    this.treeData = [];
    this.resetChecked();
  }
  @Watch('filterText')
  watchFilterText(val) {
    this.tree.filter(val);
  }
  @Watch('visible')
  onVisibleChange(val) {
    val && this.getTreeData();
  }

  async getTreeData() {
    try {
      this.treeLoading = true;
      const { success, data, error } = await get(URL_EXPIMP_GETRESLIST);
      if (success) {
        const newData = [
          { id: 1, label: '流程', children: this.formatArray(data.job) },
          !this.isFuseMode && {
            id: 2,
            label: '数据定义、方法、第三方类库',
            type: 'asset'
          },
          this.enableSql && { id: 3, label: '表', children: this.formatArray(data.table) },
          this.enableSql && { id: 4, label: '视图', children: this.formatArray(data.view) },
          this.enableSql && { id: 5, label: '选项', children: this.formatArray(data.item) }
        ];
        this.treeData = newData.filter(Boolean);
        this.treeLoading = false;
        return;
      }
      this.$message.error(error);
      this.treeLoading = false;
    } catch {
      this.treeLoading = false;
    }
  }

  formatArray(raw: any) {
    return Array.isArray(raw) ? raw : [];
  }
  filterNode(value, data) {
    return !value ? true : data.label.includes(value);
  }
  handleCheckChange({ children }) {
    if (Array.isArray(children)) {
      children.forEach((item) => {
        const node = this.tree.getNode(item);
        !node.visible && this.tree.setChecked(item, false);
      });
    }
  }
  allChecked() {
    this.tree.setCheckedKeys([1, 2, 3, 4, 5], false);
  }
  resetChecked() {
    this.tree.setCheckedKeys([]);
  }
  async submit() {
    try {
      this.loading = true;
      const checkedNodes = this.tree.getCheckedNodes(true);
      const result = groupBy(checkedNodes, 'type');
      if ('asset' in result) {
        (result as any).asset = true;
      }
      const { blob, fileName } = await post(URL_EXPIMP_DOWNLOADRECORDS, result, {
        responseType: 'blob'
      });
      const file = new Blob([blob], { type: 'application/zip;charset=UTF-8' });
      this.download(file, fileName);
      this.loading = false;
    } catch {
      this.loading = false;
    }
  }
  download(file: Blob, fileName: string) {
    const url = window.URL.createObjectURL(file);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', decodeURI(fileName));
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
}
</script>
<style scoped lang="scss">
.head {
  text-align: left;
  padding-left: 20px;
  margin-bottom: 10px;
}
.dia-tree {
  height: calc(100% - 40px);
  overflow-y: auto;
}
</style>
