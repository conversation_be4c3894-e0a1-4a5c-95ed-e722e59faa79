<template>
  <pro-page :title="title" :loading="loading">
    <div slot="operation">
      <el-button type="primary" style="marginleft: 10px" @click="submit(true)">SQL预览</el-button>
      <el-button type="primary" style="marginleft: 10px" @click="submit()">保存</el-button>
    </div>
    <pro-grid direction="column" :gutter="20" style="padding-top: 20px">
      <pro-grid type="info" title="视图信息">
        <el-row class="info">
          <el-col :span="20" style="diplay: flex">
            <el-form
              ref="chartForm"
              :model="chartInfo"
              :rules="chartRules"
              label-width="70px"
              :label-position="'right'"
            >
              <el-row>
                <el-col :span="24" style="">
                  <el-form-item label="视图名" prop="tableName">
                    <el-select
                      v-model="chartInfo.tableNamePrefix"
                      placeholder="请选择前缀名"
                      filterable
                    >
                      <el-option
                        v-for="item in chartList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                    <el-input
                      :value="chartInfo.tableName"
                      style="width: 193px; margin-left: 10px; margin-right: 10px"
                      maxlength="100"
                      @input="(e) => (chartInfo.tableName = e.replace(/[^\w\d]/g, ''))"
                    />
                    <el-select
                      v-model="chartInfo.tableNameSuffix"
                      placeholder="请选择后缀名"
                      filterable
                    >
                      <el-option
                        v-for="item in chartListBack"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="中文名" prop="tableNameCn" style="diplay: inline; width: 466px">
                <el-input v-model="chartInfo.tableNameCn" maxlength="20" />
              </el-form-item>

              <el-form-item label="业务口径" prop="businessExplain" style="width: 100%">
                <el-input
                  v-model="chartInfo.businessExplain"
                  type="textarea"
                  row="5"
                  style="width: 100%"
                  placeholder="请输入业务口径"
                  maxlength="50"
                />
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </pro-grid>
      <!-- 字段信息 -->
      <field-table
        ref="fieldTable"
        :value.sync="selectedTables"
        :status="status"
        :data="detailSource"
        @field-change="handelFieldChange"
      />
      <!-- 字段规则 -->
      <field-rule
        ref="fieldRule"
        :status="status"
        :conditions="conditions"
        :field-lists="fieldLists"
      />
      <reference-relation
        v-if="status === '2'"
        :id="this.$route.query.id"
        type="JOB"
        relation="view"
      />
    </pro-grid>
    <save-confirm ref="saveConfirm" @saveConfirmCallback="saveConfirmCallback" />
    <preview ref="preview" :title="'SQL预览'" :data="sourceCode" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Ref, Vue } from 'vue-property-decorator';
import { vm } from '@/main';
import ReferenceRelation from '@/views/data/components/reference-relation.vue';
import FieldRule from './field-rule.vue';
import FieldTable from './field-table.vue';
import { addView, getItemListByType, getViewDetail, previewView, updateView } from '@/apis/dataApi';
@Component({
  name: 'ViewAddEdit',
  components: {
    upload: () => import('./../../modals/upload.vue'),
    'save-confirm': () => import('./../../modals/save-confirm.vue'),
    preview: () => import('../../modals/flink-sql.vue'),
    ReferenceRelation,
    FieldRule,
    FieldTable
  }
})
export default class ViewAddEdit extends Vue {
  @Ref('fieldTable') readonly fieldTable!: any;
  @Ref('fieldRule') readonly fieldRule!: any;
  // 视图ID
  id = '';
  // 页面标题
  title = '';
  // 状态： 0 新建 1编辑 2详情
  status: any = '0';
  detailSource: any = {};
  // 表前缀列表
  chartList: any = [];
  // 表后缀列表
  chartListBack: any = [];
  // 视图信息
  chartInfo = {
    tableNamePrefix: '',
    tableNameSuffix: '',
    tableName: '',
    tableNameCn: '',
    businessExplain: ''
  };
  // 视图信息交易
  chartRules = {
    tableNamePrefix: [{ required: true, message: '请选择视图前缀', trigger: 'blur' }],
    tableName: [{ required: true, validator: this.validateTableName, trigger: 'blur' }]
  };
  saveMemo = '';
  loading = false;
  sourceCode: any = {};
  // 选中的数据表
  selectedTables = [];
  // 字段条件
  conditions: any[] = [];
  // 字段列表
  fieldLists = [];
  async created() {
    // 初始信息获取
    this.id = (this.$route.query.id as string) || '';
    this.status = this.$route.query.status || '0';
    this.title = this.getTitle(this.status);
    // 查询表前缀
    this.chartList = await this.queryChartList('PRE_FIX');
    // 查询表前缀
    this.chartListBack = await this.queryChartList('SUF_FIX');
    // 存在id 获取详情
    if (this.id) this.getDetail();
  }
  async getDetail() {
    this.loading = true;
    let data: any = {};
    await getViewDetail({ id: this.id }).then((resp: any) => {
      data = resp!.data || {};
    });
    this.detailSource = Object.freeze(data);
    this.setChartInfo(data);
    // 获取选中的数据表
    this.selectedTables = JSON.parse(data.dependTableInfo || '[]');
    this.conditions = JSON.parse(data.viewCondition || '[]');
    this.loading = false;
  }
  // 视图基本信息
  setChartInfo(data) {
    this.chartInfo.tableNamePrefix = data.viewNamePrefix;
    this.chartInfo.tableNameSuffix = data.viewNameSuffix;
    this.chartInfo.tableName = data.viewName
      .replace(data.viewNamePrefix, '')
      .replace(data.viewNameSuffix, '');
    this.chartInfo.tableNameCn = data.viewNameCn;
    this.chartInfo.businessExplain = data.businessExplain;
  }
  getTitle(status) {
    const titleObj = {
      '0': '新建视图',
      '1': '编辑视图'
    };
    return titleObj[status];
  }
  // 视图名称校验
  validateTableName(rule, value, callback) {
    const re = /^[1-9]+[0-9]*]*$/;
    if (value === '') {
      callback(new Error('请填写视图名'));
    } else if (!this.chartInfo.tableNamePrefix) {
      callback(new Error('请填写前缀名'));
    } else if (value !== '' && re.test(value[0])) {
      callback(new Error('视图名不能以数字开头'));
    } else {
      callback();
    }
  }
  // 字段列表变更
  handelFieldChange(val) {
    this.fieldLists = Object.freeze(val);
  }
  // 校验包数据表及字段
  checkField() {
    const { setError, advancedFieldList } = this.fieldTable;
    if (!this.selectedTables.length) {
      setError('table');
      return false;
    }
    if (this.fieldLists.length === 0 && advancedFieldList.length === 0) {
      setError('field', '字段信息或高级表字段需要至少一项');
      return false;
    }
    return true;
  }
  // 校验
  checkFieldCondition() {
    const { setError } = this.fieldRule;
    if (this.conditions.length) {
      const emptyIndexs: number[] = [];
      const conditionFields: { field: string; index: number }[] = [];
      this.conditions.forEach((condition: any, index: number) => {
        conditionFields.push({ field: condition.fieldName, index });
        if (!condition.fieldName || !condition.operator || !condition.fieldValue) {
          emptyIndexs.push(index + 1);
        }
      });
      if (emptyIndexs.length) {
        setError('条件' + emptyIndexs.join('，') + '未配置完成。');
        return false;
      }
      const fields = this.fieldLists.map((item: any) => item.fieldName);
      const unExistFields = conditionFields.filter((item) => !fields.includes(item.field));
      return unExistFields.length ? unExistFields : true;
    }
    return true;
  }
  submit(priview: any = false) {
    const submitFn = priview ? this.privew : this.save;
    (this.$refs['chartForm'] as any).validate(async (valid) => {
      if (valid) {
        if (!this.checkField()) {
          return;
        }
        const checkFieldCondition = this.checkFieldCondition();
        if (!checkFieldCondition) {
          return;
        } else if (Array.isArray(checkFieldCondition)) {
          const fieldStr = [...new Set(checkFieldCondition.map((i) => i.field))].join('，');
          await this.$confirm(
            `表内字段${fieldStr}已经被删除，所配置的条件将无法生效，需要对条件进行删除吗？`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          );
          // 删除以被删除字段的条件配置信息
          checkFieldCondition.forEach((item) => {
            this.conditions[item.index] = null;
          });
          this.conditions = this.conditions.filter((i) => i);
          submitFn();
        } else {
          submitFn();
        }
      }
    });
  }
  // 获取参数
  getParams() {
    // 获取字段条件信息
    const viewCondition = this.conditions.map((item, index) => {
      return Object.assign(item, { condition: '条件' + (index + 1) });
    });
    // 获取基础字段、高级字段
    const { advancedFieldList, fieldList, allTableData } = this.fieldTable;
    const baseFieldInfo = allTableData.length ? allTableData : fieldList;
    const advanceFieldInfo = advancedFieldList;
    return {
      id: this.id,
      memo: this.saveMemo,
      businessExplain: this.chartInfo.businessExplain,
      viewName:
        this.chartInfo.tableNamePrefix + this.chartInfo.tableName + this.chartInfo.tableNameSuffix,
      viewNameCn: this.chartInfo.tableNameCn,
      viewNamePrefix: this.chartInfo.tableNamePrefix,
      viewNameSuffix: this.chartInfo.tableNameSuffix,
      dependTableInfo: JSON.stringify(this.selectedTables),
      viewCondition: JSON.stringify(viewCondition),
      baseFieldInfo: JSON.stringify(baseFieldInfo),
      advanceFieldInfo: JSON.stringify(advanceFieldInfo)
    };
  }
  // SQL预览
  async privew() {
    const params = this.getParams();
    const { success, data } = ((await previewView(params)) || {}) as any;
    if (success) {
      this.sourceCode = data;
      const previewRef: any = this.$refs.preview;
      previewRef.visible = true;
    } else {
      this.$message.error(data.msg);
    }
  }
  // 保存
  async save(isConirm = false) {
    // 编辑打开弹窗
    if (this.id && !isConirm) {
      (this.$refs.saveConfirm as any).visible = true;
      return;
    }
    const params = this.getParams();
    const { success, msg = '' } = (await (this.id ? updateView(params) : addView(params))) as any;
    if (!success) {
      this.$message.error(msg);
      return;
    }
    const oidFullPath = this.$route.fullPath;
    const tabsNavInstance = vm.$children[0].$refs['tabsNav'];
    tabsNavInstance.handleTabsDelete(oidFullPath);
    this.$router.push({ path: '/data/viewManage' });
  }
  // 获取表前缀列表
  async queryChartList(type: 'PRE_FIX' | 'SUF_FIX') {
    const { data } = await getItemListByType({ itemType: type });
    const target = data.map((n) => {
      return {
        value: n,
        label: n
      };
    });
    return target;
  }
  saveConfirmCallback(data) {
    // 编辑时保存
    if (!data.comments) {
      return;
    }
    this.saveMemo = data.comments;
    (this.$refs.saveConfirm as any).visible = false;
    this.save(true);
  }
}
</script>

<style scoped lang="scss">
.info {
  padding: 20px;
}
.page-content {
  overflow: auto;
  .sheet-bar {
    background-color: rgb(255, 255, 255);
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #d4dce2;
    border-left: none;
    padding: 0 20px;
    font-size: 14px;
    .span {
      cursor: pointer;
    }
    .blod {
      font-weight: bolder;
    }
  }
  .first_content {
    border: unset;
    background-color: #f6f7f9;
    height: calc(100% - 50px);
    .detail_content {
      background-color: #ffffff;
      min-height: 0px;
      margin-top: 20px;
    }
    .tab-title {
      .title-choose {
        display: flex;
        padding-left: 6px;
        align-items: center;
        .shu {
          width: 3px;
          margin-right: 6px;
          background: #2196f3;
          height: 21px;
        }
        .depentTable {
          font-weight: normal;
          margin-left: 20px;
        }
      }
      .info {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
    .tab-content {
      border-top: 1px solid #f1f1f1;
      padding: 20px 25px;
      .service-row {
        display: flex;
      }
      .table-list {
        padding: 1px;
        min-height: 300px;
        max-height: 500px;
        overflow-y: auto;
        background: #fff;
        .advanced-item {
          border: 1px solid #e2e2e2;
          margin: 10px;
          padding: 15px;
          .item-title {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }
        }
        .el-row {
          height: 50px;
          line-height: 50px;
          .grid-content {
            display: flex;
            justify-content: center;
            align-items: center;
            .data-card-icon {
              font-size: 19px;
              margin-left: 20px;
            }
          }
        }
        .el-row:nth-child(even) {
          background: #fafdff;
        }
        .even {
          background: #fafdff;
        }

        .el-row .grid-content {
          text-align: center;
        }
      }
    }
  }
}
</style>
