<template>
  <el-collapse v-model="activeList" class="advanced-config-container">
    <el-collapse-item name="advanced">
      <div slot="title" class="resource-title">高级参数</div>
      <template v-for="el in renderList">
        <el-form-item
          v-show="!el.hidden"
          :key="el.name"
          :prop="el.name"
          :label="el.label"
          :rules="rules[el.name] || []"
        >
          <div :class="['resource-item', el.tip || el.unit ? 'resource-item--tip' : '']">
            <!-- select -->
            <el-select
              v-if="el.type === 'select'"
              v-model="form[el.name]"
              filterable
              clearable
              :popper-append-to-body="true"
              :placeholder="el.placeholder"
            >
              <el-option v-for="item in el.options" :key="item" :label="item" :value="item" />
            </el-select>
            <!-- number -->
            <el-input-number
              v-if="el.type === 'number'"
              v-model="form[el.name]"
              number
              :min="el.min"
              :max="el.max"
              :step="el.step ? el.step : 1"
              :precision="el.precision ? el.precision : 0"
              :placeholder="el.placeholder"
            />
            <!-- 复选框 -->
            <el-checkbox v-if="el.type === 'checkbox'" v-model="form[el.name]" />
          </div>
          <!-- 单位 -->
          <span v-if="el.unit" class="advanced-config-unit">{{ el.unit }}</span>
          <!-- 提示 -->
          <el-tooltip v-if="el.tip" effect="light" :content="el.tip" placement="bottom">
            <div v-if="el.wrap" slot="content" v-html="el.tip"></div>
            <i class="iconfont icon-wenhao advanced-config-icon"></i>
          </el-tooltip>
        </el-form-item>
      </template>
    </el-collapse-item>
  </el-collapse>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';

@Component
export default class AdvancedConfig extends PaBase {
  @Prop({ type: Object, default: () => ({}) }) property!: any;
  @Prop({ type: Object, default: () => ({}) }) data!: any;
  @Prop({ type: Boolean, default: false }) isCloud!: boolean;
  @PropSync('data', { type: Object, default: () => ({}) }) form!: any;

  private activeList: any[] = [];
  disabled = false;
  private renderList: any[] = [
    {
      name: 'taskManagerLimitCpu',
      label: 'task manager CPU 爆发倍数',
      placeholder: '请输入task manager CPU 爆发倍数',
      type: 'number',
      min: 1,
      max: 4,
      hidden: true,
      tip: '该配置用于开启TaskManager CPU爆发能力，指定了TaskManager运行时能够使用的CPU上限'
    },
    {
      name: 'taskManagerLimitMemory',
      label: 'task manager 内存爆发倍数',
      placeholder: '请输入task manager 内存爆发倍数',
      type: 'number',
      min: 1,
      max: 4,
      hidden: true,
      tip: '该配置项用于开启TaskManager内存爆发能力，指定了TaskManager运行时能够使用的内存上限。'
    },
    {
      name: 'jobManagerRequestCpu',
      label: 'job manager CPU',
      placeholder: '请输入job manager CPU',
      type: 'number',
      min: 0.1,
      max: 10,
      step: 0.1,
      hidden: true,
      tip: '该配置项指定了JobManager运行分配的最小CPU'
    },
    {
      name: 'jobManagerMemory',
      label: 'job manager内存(MB)',
      placeholder: '请输入job manager内存(MB)',
      hidden: false,
      type: 'number',
      min: 1,
      max: 1048576,
      tip: 'JobManager负责整个job的生命周期管理，包括资源申请，状态监控，协调、控制的执行过程如处理调度任务、保存checkpoint、容错等；该配置项指定 了JVM 堆内存。'
    },
    {
      name: 'queue',
      label: '队列',
      placeholder: '请选择队列',
      type: 'select',
      options: [],
      hidden: true,
      tip: 'yarn通过队列实现用户和资源的管理，配置该项指定提交到集群的某个队列中。'
    },
    {
      name: 'taskmanager_managed_frac',
      label: 'taskmanager托管内存因子',
      placeholder: '请输入taskmanager托管内存因子',
      hidden: false,
      type: 'number',
      min: 0,
      max: 0.7,
      step: 0.1,
      precision: 1,
      tip: '作用于RocksDB状态后端，批流作业中的排序，哈希表以及缓存中间结果。'
    },
    {
      name: 'restartStrategy',
      label: '重启策略',
      placeholder: '请选择重启策略',
      type: 'select',
      options: ['fixed-delay', 'failure-rate', 'exponential-delay', 'none']
    },
    {
      name: 'attempts',
      label: '尝试次数',
      placeholder: '请输入尝试次数',
      type: 'number',
      min: 1,
      max: 2147483647,
      hidden: true
    },
    {
      name: 'delay',
      label: '固定重启延迟时间',
      placeholder: '请输入固定重启延迟时间',
      type: 'number',
      min: 1,
      max: 100,
      hidden: true,
      unit: 's'
    },
    {
      name: 'initialBackoff',
      label: '初始间隔时间',
      type: 'number',
      min: 1,
      max: 2147483647,
      hidden: true,
      unit: 's'
    },
    {
      name: 'maxBackoff',
      label: '最大间隔时间',
      type: 'number',
      min: 2,
      max: 2147483647,
      hidden: true,
      unit: 's'
    },
    {
      name: 'backoffMultiplier',
      label: '增长乘数',
      type: 'number',
      precision: 1,
      step: 0.1,
      hidden: true
    },
    {
      name: 'resetBackoffThreshold',
      label: '最小稳定运行时间',
      type: 'number',
      min: 0,
      max: 2147483647,
      unit: 'min',
      hidden: true
    },
    {
      name: 'jitterFactor',
      label: '振动因子',
      type: 'number',
      step: 0.1,
      min: 0.1,
      max: 1.0,
      precision: 1,
      hidden: true
    },
    {
      name: 'failureRateDelay',
      label: '失败重启延迟时间',
      placeholder: '请输入失败重启延迟时间',
      type: 'number',
      min: 1,
      max: 100,
      hidden: true,
      unit: 's'
    },
    {
      name: 'failuresPerInterval',
      label: '重启次数',
      placeholder: '请输入重启次数',
      type: 'number',
      min: 1,
      max: 2147483647,
      hidden: true
    },
    {
      name: 'failureRateInterval',
      label: '时间间隔',
      placeholder: '请输入时间间隔',
      type: 'number',
      min: 1,
      max: 2147483647,
      hidden: true,
      unit: 'min'
    },
    {
      name: 'disableOperatorChain',
      label: '是否打断算子链',
      type: 'checkbox',
      tip: `打断算子链，可用于分析流程性能瓶颈，会对作业性能产生影响`,
      wrap: true
    },
    {
      name: 'logOutputKafka',
      label: '是否输出日志到Kafka',
      type: 'checkbox'
    },
    {
      name: 'enableCheckPoint',
      label: '是否开启checkpoint',
      type: 'checkbox',
      tip: `进行周期性状态快照，保证数据一致性，会对作业性能产生影响`,
      wrap: true
    },
    {
      name: 'cp_unaligned',
      label: '是否开启未对齐检查点',
      type: 'checkbox',
      tip: '开启后可以提高checkpoint的速度，但会增加内存使用'
    },
    {
      name: 'checkpointInterval',
      label: 'checkpoint周期',
      placeholder: '请输入checkpoint周期',
      type: 'number',
      min: 1,
      max: 1000000,
      unit: 'ms',
      hidden: false
    },
    {
      name: 'cp_timeout',
      label: 'checkpoint超时时间',
      placeholder: '请输入超时时间',
      type: 'number',
      min: 1,
      max: 1000000,
      unit: 'ms',
      hidden: false
    },
    {
      name: 'cp_min_pause',
      label: 'checkpoint最小停顿时间',
      placeholder: '请输入最小停顿时间',
      type: 'number',
      min: 1,
      max: 10000,
      unit: 'ms',
      hidden: false
    },
    {
      name: 'cp_failed',
      label: '可容忍的检查点故障',
      placeholder: '请输入托管内存大小因子',
      type: 'number',
      min: 0,
      max: 100,
      tip: 'checkpoint最大失败次数'
    },
    {
      name: 'stateBackend',
      label: '状态后端',
      placeholder: '请选择状态后端',
      type: 'select',
      options: ['jobmanager', 'filesystem', 'rocksdb']
    },
    {
      name: 'jobManagerLimitCpu',
      label: 'job manager CPU爆发倍数',
      placeholder: '请输入job manager CPU爆发倍数',
      type: 'number',
      min: 1,
      max: 4,
      hidden: true,
      tip: '该配置项用于开启JobManager CPU爆发能力，指定了JobManager运行时能够使用的CPU上限'
    },
    {
      name: 'jobManagerLimitMemory',
      label: 'job manager 内存爆发倍数',
      placeholder: '请输入job manager 内存爆发倍数',
      type: 'number',
      min: 1,
      max: 4,
      hidden: true,
      tip: '该配置项用于开启JobManager内存爆发能力，指定了1个JobManager运行时能够使用的内存上限。'
    },
    {
      name: 'namespace',
      label: '命名空间',
      placeholder: '请选择命名空间',
      type: 'select',
      options: [],
      hidden: true,
      tip: 'yarn通过命名空间实现用户和资源的管理，配置该项指定提交到集群的某个命名空间中。'
    }
  ]; // 渲染列表
  private rules: any = {
    taskManagerLimitCpu: [
      {
        required: false,
        message: '请输入task manager CPU 爆发倍数',
        trigger: 'blur'
      }
    ],
    queue: [
      {
        required: false,
        message: '请选择队列'
      }
    ],
    namespace: [
      {
        required: false,
        message: '请选择命名空间'
      }
    ],
    taskManagerLimitMemory: [
      {
        required: false,
        message: '请输入task manager 内存爆发倍数',
        trigger: 'blur'
      }
    ],
    jobManagerRequestCpu: [{ required: false, message: '请输入job manager CPU', trigger: 'blur' }],
    jobManagerMemory: [{ required: true, message: '请输入job manager内存', trigger: 'blur' }],
    jobManagerLimitCpu: [
      {
        required: false,
        message: '请输入job manager CPU爆发倍数',
        trigger: 'blur'
      }
    ],
    jobManagerLimitMemory: [
      {
        required: false,
        message: '请输入job manager 内存爆发倍数',
        trigger: 'blur'
      }
    ]
  };

  @Watch('property', { immediate: true, deep: true })
  handleProperty({ custom = '', clusterType = '' }: any) {
    this.handleIsCloud(clusterType === 'CLOUD');
    const key = clusterType === 'CLOUD' ? 'namespace' : 'queue';
    const info = custom.split(',');
    // 1.当选中Standalone、yarn session模式，隐藏托管内存因子,job manager内存;
    if (info.length > 0) {
      const isHiddenClusterType = ['STANDALONE', 'YARN_SESSION'].includes(clusterType);
      this.setRenderListValue('taskmanager_managed_frac', isHiddenClusterType ? true : false);
      this.setRenderListValue('jobManagerMemory', isHiddenClusterType ? true : false);
    }
    if (info.length > 0 && ['YARN_APPLICATION', 'YARN_PER_JOB', 'CLOUD'].includes(clusterType)) {
      this.setRenderListValue(key, false);
      this.setRenderListValue(key, info, 'options');
      this.setRulesValue(key, true);
      this.$set(this.form, key, this.form[key] || info[0] || '');
      return;
    }
    this.$set(this.form, key, '');
    this.setRenderListValue(key, true);
    this.setRenderListValue(key, [], 'options');
    this.setRulesValue(key, false);
  }

  // @Watch('isCloud', { immediate: true })
  handleIsCloud(val: boolean) {
    [
      'taskManagerLimitCpu',
      'taskManagerLimitMemory',
      'taskManagerNumber',
      'jobManagerRequestCpu',
      'jobManagerLimitCpu',
      'jobManagerLimitMemory'
    ].forEach((el) => {
      this.setRenderListValue(el, !val);
      this.setRulesValue(el, val);
    });
    this.setRenderListValue('queue', val);
    this.setRenderListValue('namespace', !val);
    this.setRulesValue('queue', !val);
    this.setRulesValue('namespace', val);
  }

  @Watch('form.enableCheckPoint', { immediate: true })
  handleCheckPointChange(val: string) {
    this.setRenderListValue('checkpointInterval', !val, 'hidden');
    this.setRenderListValue('cp_timeout', !val, 'hidden');
    this.setRenderListValue('cp_min_pause', !val, 'hidden');
    this.setRenderListValue('cp_failed', !val, 'hidden');
    this.setRenderListValue('cp_unaligned', !val, 'hidden');
    this.setRenderListValue('stateBackend', !val, 'hidden');
  }

  @Watch('form.restartStrategy', { immediate: true })
  handleIsRestartStrategy(val: string, oldVal: string) {
    const arr = [
      { name: 'failuresPerInterval', showRule: 'failure-rate' },
      { name: 'failureRateInterval', showRule: 'failure-rate' },
      { name: 'failureRateDelay', showRule: 'failure-rate' },
      { name: 'attempts', showRule: 'fixed-delay' },
      { name: 'delay', showRule: 'fixed-delay' },
      { name: 'initialBackoff', showRule: 'exponential-delay' },
      { name: 'maxBackoff', showRule: 'exponential-delay' },
      { name: 'backoffMultiplier', showRule: 'exponential-delay' },
      { name: 'resetBackoffThreshold', showRule: 'exponential-delay' },
      { name: 'jitterFactor', showRule: 'exponential-delay' }
    ];
    arr.forEach(({ name: key, showRule }) => {
      this.setRenderListValue(key, showRule !== val);
    });
    if (oldVal) {
      this.form.attempts = 5; // 尝试次数
      this.form.delay = 10; // 固定重启延迟时间
    }
  }

  setRenderListValue(name: string, value: any, key = 'hidden') {
    const index = this.renderList.findIndex((el) => el.name === name);
    if (index > -1) {
      this.$set(this.renderList[index], key, value);
    }
  }

  setRulesValue(name: string, value: any, key = 'required') {
    if (name in this.rules) {
      this.$set(this.rules[name][0], key, value);
    }
  }
}
</script>

<style scoped lang="scss">
.advanced-config {
  &-icon {
    margin: 1px 0 0 10px;
    font-size: 16px;
    vertical-align: middle;
  }
  &-unit {
    display: inline-block;
    padding: 0 0 0 10px;
    width: 33px;
    box-sizing: border-box;
  }
}
</style>
