<!-- >保存提示弹窗<-->
<template>
  <bs-dialog :title="$t('pa.data.udf.detail.savePrompt')" :visible.sync="display" @confirm="handleConfirm">
    <div>
      <p style="margin-bottom: 10px; text-align: left">{{ $t('pa.placeholder.updatePlaceholder') }}</p>
      <el-input
        v-model="memo"
        rows="5"
        type="textarea"
        maxlength="100"
        show-word-limit
        autocomplete="off"
        :placeholder="$t('pa.placeholder.updatePlaceholder')"
      />
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';

@Component
export default class SaveConfirm extends Vue {
  @PropSync('show', { type: Boolean }) display!: boolean;
  @Prop({ default: true }) require!: boolean;

  memo = '';

  handleConfirm() {
    if (!this.memo && this.require) return this.$message.error(this.$t('pa.data.udf.detail.placeholder.updataPlaceholder'));
    this.$emit('confirm', { memo: this.memo });
    this.display = false;
  }
}
</script>
