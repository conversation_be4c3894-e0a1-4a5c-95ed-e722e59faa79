<!--
 * @Description: 组件页面设计
 -->
<template>
  <div>
    <span v-if="!formConf.forms">{{ $t('pa.noData') }}</span>
    <el-collapse v-if="formConf.forms" v-model="activeNames">
      <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="120px">
        <el-collapse-item :title="$t('pa.basicParams')" name="1">
          <div v-for="(formItem, index) in formConf.forms" :key="index">
            <div v-if="formItem.itemType === undefined || formItem.itemType === 'base'">
              <el-form-item
                v-show="show(formItem)"
                v-if="formItem.layout === undefined"
                :ref="formItem.model"
                :label="formItem.label"
                :prop="formItem.model"
              >
                <div style="display: flex">
                  <div class="config-form-item" align="left" :style="formItem.divStyle">
                    <el-select
                      v-if="formItem.type === 'select'"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      :clearable="formItem.componentCfg.clearable"
                      :multiple="formItem.componentCfg.multiple"
                      :filterable="formItem.componentCfg.filterable"
                      :remote-method="remoteMethod"
                      :loading="selectLoading"
                      :remote="formItem.model.includes('subName')"
                      :disabled="disabled"
                      @change="selChangeHandle($event, formItem)"
                      @visible-change="visibleChangeHandle($event, formItem)"
                      @focus="inputFocusHandle(formItem)"
                    >
                      <span v-if="!formItem.componentCfg.groupable">
                        <el-option
                          v-for="selItem in formItem.componentCfg.options"
                          :key="selItem[formItem.componentCfg.valueField]"
                          :label="selItem[formItem.componentCfg.labelField]"
                          :value="selItem[formItem.componentCfg.valueField]"
                          :disabled="selItem['more']"
                        />
                      </span>
                      <span v-if="formItem.componentCfg.groupable">
                        <el-option-group
                          v-for="group in formItem.componentCfg.options"
                          :key="group.label"
                          :label="group.label"
                        >
                          <el-option
                            v-for="selItem in group.options"
                            v-show="selItem[formItem.componentCfg.valueField] !== ''"
                            :key="selItem[formItem.componentCfg.valueField]"
                            :label="selItem[formItem.componentCfg.labelField]"
                            :value="selItem[formItem.componentCfg.valueField] + getOptionId(selItem)"
                          />
                        </el-option-group>
                      </span>
                    </el-select>
                    <el-input
                      v-if="formItem.type === 'input'"
                      :id="'itemId_' + formItem.model"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      :maxlength="formItem.maxlength"
                      :readonly="formItem.readonly"
                      :clearable="true"
                      :disabled="disabled"
                    />
                    <el-input
                      v-if="formItem.type === 'textarea'"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      type="textarea"
                      :placeholder="formItem.placeholder"
                      :maxlength="formItem.maxlength"
                      :readonly="formItem.readonly"
                      :disabled="disabled"
                    />
                    <el-autocomplete
                      v-if="formItem.type === 'autocomplete'"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      :maxlength="formItem.maxlength"
                      :readonly="formItem.readonly"
                      :fetch-suggestions="querySearchHandle"
                      :clearable="true"
                      :placement="formItem.componentCfg.placement"
                      :disabled="disabled"
                      @focus="inputFocusHandle(formItem)"
                      @select="selectFieldHandle($event, formItem, formItem.componentCfg.valueField)"
                    >
                      <template slot-scope="{ item }">
                        <span style="font-weight: bold">{{ item[formItem.componentCfg.valueField] }}</span>
                        <span style="float: right">{{ item[formItem.componentCfg.labelField] }}</span>
                      </template>
                    </el-autocomplete>
                    <el-select
                      v-if="formItem.type === 'selectByInput'"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      clearable
                      :multiple="formItem.componentCfg.multiple"
                      :filterable="formItem.componentCfg.filterable"
                      :disabled="disabled"
                    >
                      <el-option
                        v-for="selItem in input"
                        :key="selItem[formItem.componentCfg.valueField]"
                        :value="selItem[formItem.componentCfg.valueField]"
                      />
                    </el-select>
                    <el-input-number
                      v-if="formItem.type === 'number'"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      :min="formItem.min"
                      :max="formItem.max"
                      :disabled="disabled"
                    />
                    <el-cascader
                      v-if="formItem.type === 'cascader'"
                      v-model="formData[formItem.model]"
                      :style="formItem.style"
                      :options="formItem.componentCfg.options"
                      :props="{ expandTrigger: 'hover' }"
                    />
                    <el-time-picker
                      v-if="formItem.type === 'time-picker'"
                      v-model="formData[formItem.model]"
                      :is-range="formItem.componentCfg.range"
                      :range-separator="formItem.componentCfg.rangeSeparator"
                      :start-placeholder="formItem.componentCfg.startPlaceholder"
                      :end-placeholder="formItem.componentCfg.endPlaceholder"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      :maxlength="formItem.maxlength"
                      :readonly="formItem.readonly"
                      :clearable="true"
                      :disabled="disabled"
                    />
                    <el-date-picker
                      v-if="formItem.type === 'date-picker'"
                      v-model="formData[formItem.model]"
                      :type="formItem.componentCfg.type"
                      :range-separator="formItem.componentCfg.rangeSeparator"
                      :start-placeholder="formItem.componentCfg.startPlaceholder"
                      :end-placeholder="formItem.componentCfg.endPlaceholder"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      :maxlength="formItem.maxlength"
                      :readonly="formItem.readonly"
                      :clearable="true"
                      :disabled="disabled"
                    />
                    <el-input
                      v-if="formItem.type === 'password'"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      type="password"
                      :placeholder="formItem.placeholder"
                      :maxlength="formItem.maxlength"
                      :readonly="formItem.readonly"
                      :clearable="true"
                      :disabled="disabled"
                    />
                  </div>
                  <div>
                    <el-tooltip v-if="formItem.desc !== undefined" class="item" effect="light" placement="bottom">
                      <div slot="content">
                        {{ formItem.desc }}
                      </div>
                      <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer"></i>
                    </el-tooltip>
                  </div>
                </div>
              </el-form-item>
              <!--特殊布局-->
              <el-form-item
                v-if="formItem.layout !== undefined && formItem.layoutFirst"
                v-show="show(formItem)"
                :ref="formItem.model"
                :label="formItem.label"
                :prop="formItem.model"
              >
                <div style="display: flex">
                  <div style="width: 90%">
                    <!--布局1-->
                    <div v-if="formItem.layout === 'layout1'" style="width: 100%; display: flex">
                      <div
                        v-for="(inputItem, inputIndex) in getLayoutItems(formItem.layoutModel)"
                        :key="inputIndex"
                        :style="inputItem.divStyle"
                      >
                        <div>{{ inputItem.label }}</div>
                        <div>
                          <el-form-item :prop="inputItem.model" :rules="inputItem.rules">
                            <el-input
                              v-if="inputItem.type === 'input'"
                              v-model="formData[inputItem.model]"
                              :style="inputItem.style"
                              :placeholder="inputItem.placeholder"
                              :maxlength="inputItem.maxlength"
                              :readonly="inputItem.readonly"
                              :clearable="true"
                              :disabled="disabled"
                            />

                            <el-select
                              v-if="inputItem.type === 'select'"
                              v-model="formData[inputItem.model]"
                              style="width: 100%"
                              :placeholder="inputItem.placeholder"
                              :clearable="inputItem.componentCfg.clearable"
                              :multiple="inputItem.componentCfg.multiple"
                              :filterable="inputItem.componentCfg.filterable"
                              :disabled="disabled"
                              :remote-method="remoteMethod"
                              :loading="selectLoading"
                              remote
                              @change="selChangeHandle($event, inputItem)"
                              @visible-change="visibleChangeHandle($event, inputItem)"
                              @focus="inputFocusHandle(inputItem)"
                            >
                              <span v-if="!inputItem.componentCfg.groupable">
                                <el-option
                                  v-for="selItem in inputItem.componentCfg.options"
                                  :key="selItem[inputItem.componentCfg.valueField]"
                                  :label="selItem[inputItem.componentCfg.labelField]"
                                  :value="selItem[inputItem.componentCfg.valueField]"
                                  :disabled="selItem['more']"
                                />
                              </span>
                              <span v-if="inputItem.componentCfg.groupable">
                                <el-option-group
                                  v-for="group in inputItem.componentCfg.options"
                                  :key="group.label"
                                  :label="group.label"
                                >
                                  <el-option
                                    v-for="selItem in group.options"
                                    v-show="selItem[inputItem.componentCfg.valueField] !== ''"
                                    :key="selItem[inputItem.componentCfg.valueField]"
                                    :label="selItem[inputItem.componentCfg.labelField]"
                                    :value="`${selItem[inputItem.componentCfg.valueField]}${getOptionId(selItem)}`"
                                  />
                                </el-option-group>
                              </span>
                            </el-select>
                          </el-form-item>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <el-tooltip v-if="formItem.desc !== undefined" class="item" effect="light" placement="bottom">
                      <div slot="content">
                        {{ formItem.desc }}
                      </div>
                      <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer"></i>
                    </el-tooltip>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item :title="$t('pa.advancedParams')" name="2">
          <div v-for="(formItem, index) in formConf.forms" :key="index">
            <div v-if="formItem.itemType && formItem.itemType === 'advance'">
              <el-form-item
                v-show="show(formItem)"
                v-if="formItem.layout === undefined"
                :ref="formItem.model"
                :label="formItem.label"
                :prop="formItem.model"
              >
                <div style="display: flex">
                  <div class="config-form-item" align="left" :style="formItem.divStyle">
                    <el-select
                      v-if="formItem.type === 'select'"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      :clearable="formItem.componentCfg.clearable"
                      :multiple="formItem.componentCfg.multiple"
                      :filterable="formItem.componentCfg.filterable"
                      :remote-method="remoteMethod"
                      :loading="selectLoading"
                      remote
                      :disabled="disabled"
                      @change="selChangeHandle($event, formItem)"
                      @visible-change="visibleChangeHandle($event, formItem)"
                      @focus="inputFocusHandle(formItem)"
                    >
                      <span v-if="!formItem.componentCfg.groupable">
                        <el-option
                          v-for="selItem in formItem.componentCfg.options"
                          :key="selItem[formItem.componentCfg.valueField]"
                          :label="selItem[formItem.componentCfg.labelField]"
                          :value="selItem[formItem.componentCfg.valueField]"
                          :disabled="selItem['more']"
                        />
                      </span>
                      <span v-if="formItem.componentCfg.groupable">
                        <el-option-group
                          v-for="group in formItem.componentCfg.options"
                          :key="group.label"
                          :label="group.label"
                        >
                          <el-option
                            v-for="selItem in group.options"
                            v-show="selItem[formItem.componentCfg.valueField] !== ''"
                            :key="selItem[formItem.componentCfg.valueField]"
                            :label="selItem[formItem.componentCfg.labelField]"
                            :value="selItem[formItem.componentCfg.valueField] + getOptionId(selItem)"
                          />
                        </el-option-group>
                      </span>
                    </el-select>
                    <el-input
                      v-if="formItem.type === 'input'"
                      :id="'itemId_' + formItem.model"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      :maxlength="formItem.maxlength"
                      :readonly="formItem.readonly"
                      :clearable="true"
                      :disabled="disabled || checkValueType(formItem)"
                    />

                    <el-switch
                      v-if="formItem.valueType && formItem.valueType === 1"
                      v-model="formData[makeSwitchModelName(formItem.model)]"
                      inactive-text="自定义"
                    />
                    <el-input
                      v-if="formItem.type === 'textarea'"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      type="textarea"
                      :placeholder="formItem.placeholder"
                      :maxlength="formItem.maxlength"
                      :readonly="formItem.readonly"
                      :disabled="disabled"
                    />
                    <el-autocomplete
                      v-if="formItem.type === 'autocomplete'"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      :maxlength="formItem.maxlength"
                      :readonly="formItem.readonly"
                      :fetch-suggestions="querySearchHandle"
                      :clearable="true"
                      :placement="formItem.componentCfg.placement"
                      :disabled="disabled"
                      @focus="inputFocusHandle(formItem)"
                      @select="selectFieldHandle($event, formItem, formItem.componentCfg.valueField)"
                    >
                      <template slot-scope="{ item }">
                        <span style="font-weight: bold">{{ item[formItem.componentCfg.valueField] }}</span>
                        <span style="float: right">{{ item[formItem.componentCfg.labelField] }}</span>
                      </template>
                    </el-autocomplete>
                    <el-select
                      v-if="formItem.type === 'selectByInput'"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      clearable
                      :multiple="formItem.componentCfg.multiple"
                      :filterable="formItem.componentCfg.filterable"
                      :disabled="disabled"
                    >
                      <el-option
                        v-for="selItem in input"
                        :key="selItem[formItem.componentCfg.valueField]"
                        :value="selItem[formItem.componentCfg.valueField]"
                      />
                    </el-select>
                    <el-input-number
                      v-if="formItem.type === 'number'"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      :min="formItem.min"
                      :max="formItem.max"
                      :disabled="disabled"
                    />
                    <el-cascader
                      v-if="formItem.type === 'cascader'"
                      v-model="formData[formItem.model]"
                      :style="formItem.style"
                      :options="formItem.componentCfg.options"
                      :props="{ expandTrigger: 'hover' }"
                    />
                    <el-time-picker
                      v-if="formItem.type === 'time-picker'"
                      v-model="formData[formItem.model]"
                      :is-range="formItem.componentCfg.range"
                      :range-separator="formItem.componentCfg.rangeSeparator"
                      :start-placeholder="formItem.componentCfg.startPlaceholder"
                      :end-placeholder="formItem.componentCfg.endPlaceholder"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      :maxlength="formItem.maxlength"
                      :readonly="formItem.readonly"
                      :clearable="true"
                      :disabled="disabled"
                    />
                    <el-date-picker
                      v-if="formItem.type === 'date-picker'"
                      v-model="formData[formItem.model]"
                      :type="formItem.componentCfg.type"
                      :range-separator="formItem.componentCfg.rangeSeparator"
                      :start-placeholder="formItem.componentCfg.startPlaceholder"
                      :end-placeholder="formItem.componentCfg.endPlaceholder"
                      style="width: 100%"
                      :placeholder="formItem.placeholder"
                      :maxlength="formItem.maxlength"
                      :readonly="formItem.readonly"
                      :clearable="true"
                      :disabled="disabled"
                    />
                    <el-input
                      v-if="formItem.type === 'password'"
                      v-model="formData[formItem.model]"
                      style="width: 100%"
                      type="password"
                      :placeholder="formItem.placeholder"
                      :maxlength="formItem.maxlength"
                      :readonly="formItem.readonly"
                      :clearable="true"
                      :disabled="disabled"
                    />
                  </div>
                  <div>
                    <el-tooltip v-if="formItem.desc !== undefined" class="item" effect="light" placement="bottom">
                      <div slot="content">
                        {{ formItem.desc }}
                      </div>
                      <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer"></i>
                    </el-tooltip>
                  </div>
                </div>
              </el-form-item>
              <!--特殊布局-->
              <el-form-item
                v-if="formItem.layout !== undefined && formItem.layoutFirst"
                v-show="show(formItem)"
                :ref="formItem.model"
                :label="formItem.label"
                :prop="formItem.model"
              >
                <div style="display: flex">
                  <div style="width: 90%">
                    <!--布局1-->
                    <div v-if="formItem.layout === 'layout1'" style="width: 100%; display: flex">
                      <div
                        v-for="(inputItem, inputIndex) in getLayoutItems(formItem.layoutModel)"
                        :key="inputIndex"
                        :style="inputItem.divStyle"
                      >
                        <div>{{ inputItem.label }}</div>
                        <div>
                          <el-form-item :prop="inputItem.model" :rules="inputItem.rules">
                            <el-input
                              v-if="inputItem.type === 'input'"
                              v-model="formData[inputItem.model]"
                              :style="inputItem.style"
                              :placeholder="inputItem.placeholder"
                              :maxlength="inputItem.maxlength"
                              :readonly="inputItem.readonly"
                              :clearable="true"
                              :disabled="disabled"
                            />

                            <el-select
                              v-if="inputItem.type === 'select'"
                              v-model="formData[inputItem.model]"
                              style="width: 100%"
                              :placeholder="inputItem.placeholder"
                              :clearable="inputItem.componentCfg.clearable"
                              :multiple="inputItem.componentCfg.multiple"
                              :filterable="inputItem.componentCfg.filterable"
                              :disabled="disabled"
                              :remote-method="remoteMethod"
                              :loading="selectLoading"
                              remote
                              @change="selChangeHandle($event, inputItem)"
                              @visible-change="visibleChangeHandle($event, inputItem)"
                              @focus="inputFocusHandle(inputItem)"
                            >
                              <span v-if="!inputItem.componentCfg.groupable">
                                <el-option
                                  v-for="selItem in inputItem.componentCfg.options"
                                  :key="selItem[inputItem.componentCfg.valueField]"
                                  :label="selItem[inputItem.componentCfg.labelField]"
                                  :value="selItem[inputItem.componentCfg.valueField]"
                                  :disabled="selItem['more']"
                                />
                              </span>
                              <span v-if="inputItem.componentCfg.groupable">
                                <el-option-group
                                  v-for="group in inputItem.componentCfg.options"
                                  :key="group.label"
                                  :label="group.label"
                                >
                                  <el-option
                                    v-for="selItem in group.options"
                                    v-show="selItem[inputItem.componentCfg.valueField] !== ''"
                                    :key="selItem[inputItem.componentCfg.valueField]"
                                    :label="selItem[inputItem.componentCfg.labelField]"
                                    :value="`${selItem[inputItem.componentCfg.valueField]}${getOptionId(selItem)}`"
                                  />
                                </el-option-group>
                              </span>
                            </el-select>
                          </el-form-item>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <el-tooltip v-if="formItem.desc !== undefined" class="item" effect="light" placement="bottom">
                      <div slot="content" style="width: 400px">
                        {{ formItem.desc }}
                      </div>
                      <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer"></i>
                    </el-tooltip>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
        </el-collapse-item>
      </el-form>

      <!-- 动态参数 -->
      <div v-if="formConf.custom">
        <el-collapse-item :title="$t('pa.customParams')" name="3">
          <el-button :disabled="disabled" style="float: right; margin-bottom: 7px" @click="addRow">{{
            $t('pa.action.addRow')
          }}</el-button>
          <el-table :data="tableData" style="width: 100%" stripe size="mini" border @row-dblclick="rowdbclickHandle">
            <el-table-column prop="name" :label="$t('pa.key')">
              <template slot-scope="{ row }">
                <input v-if="row.edit" v-model="row.name" class="node-form-input" :title="row.nameDesc || row.name" />
                <span v-if="!row.edit" :title="row.nameDesc || row.name">{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" :label="$t('pa.value')">
              <template slot-scope="{ row }">
                <input v-if="row.edit" v-model="row.value" class="node-form-input" :title="row.valueDesc || row.value" />
                <span v-if="!row.edit" :title="row.valueDesc || row.value">{{ row.value }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" :label="$t('pa.action.action')" width="120" header-align="center" align="center">
              <template slot-scope="{ row, $index }">
                <el-button
                  v-if="row.edit"
                  type="text"
                  size="small"
                  :disabled="disabled"
                  @click.native="handleUpdate($index, row)"
                >
                  {{ $t('pa.action.update') }}
                </el-button>
                <el-button
                  v-if="row.edit"
                  type="text"
                  size="small"
                  :disabled="disabled"
                  @click.native="handleCancel($index, row)"
                >
                  {{ $t('pa.action.cancel') }}
                </el-button>
                <el-button v-if="!row.edit" type="text" size="small" :disabled="disabled" @click.native="handleEdit(row)">
                  {{ $t('pa.action.edit') }}
                </el-button>
                <el-button
                  v-if="!row.edit"
                  type="text"
                  size="small"
                  :disabled="disabled"
                  @click.native="handleDelete($index, row)"
                >
                  {{ $t('pa.action.del') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </div>
      <div v-show="data.operateType !== 'SOURCE'">
        <el-collapse-item :title="$t('pa.fieldParams')" name="4">
          <node-field-list ref="NodeFieldList" />
        </el-collapse-item>
      </div>
    </el-collapse>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import * as _ from 'lodash';
import { get } from '@/apis/utils/net';

const _PRIMARY_ID = '_primaryId';
@Component({
  components: {
    'node-field-list': () => import('../modals/node-field-list.vue')
  }
})
export default class NodeForm extends PaBase {
  visible = false;
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop() versionComponentList!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: false }) disabled!: boolean;
  private title = '';
  private formData: any = {};
  private rules: any = {};
  private formLabelWidth = '50px';
  private formConf: any = {};
  private jobNode: any = {};
  private input: any = [];
  private output: any = [];
  private tableData: any = [];
  private focusModel = '';
  private oldRowVal: any = { name: '', value: '' };
  private selectLoading = false;
  private formConfBak: any = {};
  private initComplete = false;
  private activeNames: string[] = ['1', '4'];

  makeSwitchModelName(model) {
    return model + '_switch';
  }

  checkValueType(formItem) {
    const name = this.makeSwitchModelName(formItem.model);
    const switchItem = this.formData[name];
    if (switchItem === undefined) {
      this.$set(this.formData, name, false);
    }
    return formItem.valueType !== undefined && formItem.valueType === 1 && !switchItem;
  }

  submit(formName: string) {
    let canSubmit = true;
    const targetList: any = [];
    this.formConf.forms.forEach((n) => {
      if (n.componentCfg && n.componentCfg.useTarget) {
        const str = this.formData[n.model];
        _.split(str, ',').forEach((f) => {
          if (f !== '') {
            targetList.push({
              name: f,
              formElName: n.label
            });
          }
        });
      }
    });
    const outputFields: any = [];
    for (const t of Object.keys(targetList)) {
      const obj: any = targetList[t];
      if (_.findIndex(this.input, { name: obj.name }) >= 0) {
        this.$tip.warning(this.$t('pa.tip.fieldRepeat', [targetList[t].formElName]));
        canSubmit = false;
        break;
      }
      outputFields.push({
        name: obj.name,
        type: 'String',
        outputable: true,
        targetable: true
      });
    }
    if (canSubmit) {
      const form: any = this.$refs[formName];
      form.validate((valid: any) => {
        if (valid) {
          const custList: any = [];
          this.tableData.forEach((n) => {
            if (_.toString(n.name) !== '' && _.toString(n.value) !== '') {
              custList.push({
                name: n.name,
                value: n.value
              });
            }
          });
          this.$set(this.formData, 'customData', custList);
          // 重新组装jobNode更新DAG
          const nodeDto: any = {};
          const properties = _.cloneDeep(this.formData);
          // 排查value为undefined的key
          for (const pKey of Object.keys(properties)) {
            if (properties[pKey] === undefined) {
              delete properties[pKey];
            }
          }
          console.log('before properties', properties);
          this.formConf.forms.forEach((f) => {
            if (f.type === 'select') {
              const idField = f.model + _PRIMARY_ID;
              if (properties[idField] !== undefined) {
                properties[f.model] = _.split(properties[f.model], ',')[0];
              }
            }
          });
          console.log('after properties', properties);
          nodeDto.inputFields = this.input;
          nodeDto.outputFields = outputFields;
          nodeDto.properties = properties;
        } else {
          this.$tip.error(this.$t('pa.tip.checkMessage'));
        }
      });
    }
  }

  getLayoutItems(models) {
    const list: any = [];
    if (models) {
      models.forEach((n) => {
        const rec = _.find(this.formConf.forms, { model: n });
        if (rec) {
          list.push(_.cloneDeep(rec));
        }
      });
    }
    return list;
  }

  addRow() {
    this.addRowWithData('', '');
  }

  addRowWithData(name, value) {
    this.tableData.push({
      name: name,
      value: value,
      edit: false
    });
  }

  handleEdit(row) {
    this.$set(row, 'edit', true);
    this.oldRowVal.name = row.name;
    this.oldRowVal.value = row.value;
  }

  handleCancel(index, row) {
    this.$set(row, 'name', this.oldRowVal.name);
    this.$set(row, 'value', this.oldRowVal.value);
    this.$set(row, 'edit', false);
  }

  handleUpdate(index, row) {
    this.$set(row, 'edit', false);
  }

  handleDelete(index) {
    this.tableData.splice(index, 1);
  }

  rowdbclickHandle(row) {
    this.handleEdit(row);
  }

  makeReqParam(cfg) {
    const params = {};
    if (cfg.reqParamsField !== undefined) {
      cfg.reqParamsField.forEach((n) => {
        this.$set(params, n, this[n]);
      });
    }
    if (cfg.dependsOn) {
      cfg.dependsOn.forEach((n) => {
        this.$set(params, n, this.formData[n]);
      });
    }
    return params;
  }

  selectFieldHandle(val, row, valueField) {
    this.$set(this.formData, row.model, val[valueField]);
  }

  inputFocusHandle(item) {
    this.focusModel = item.model;
    if (item.type === 'autocomplete') {
      if (_.toString(item.componentCfg.remoteUrl) !== '') {
        const params = this.makeReqParam(item.componentCfg);
        get(item.componentCfg.remoteUrl, params).then((resp: any) => {
          if (resp.success) {
            item.componentCfg.options = resp.data;
          }
        });
      } else {
        const cfg = item.componentCfg;
        if (cfg.useInputAndTarget) {
          // 把配置字段作为下拉选项
          const ref: any = this.$refs.NodeFieldList;
          if (ref !== undefined) {
            this.$set(cfg, 'options', ref.getAllField());
          }
        }
        if (cfg.useTarget) {
          // 把配置字段作为下拉选项
          const ref: any = this.$refs.NodeFieldList;
          if (ref !== undefined) {
            this.$set(cfg, 'options', ref.getTableData());
          }
        }
        if (cfg.useInput) {
          // 把配置字段作为下拉选项
          this.$set(cfg, 'options', this.input);
        }
      }
    }
  }

  visibleChangeHandle(event, item) {
    if (event) {
      const cfg = item.componentCfg;
      if (_.toString(cfg.remoteUrl) !== '') {
        this.selectLoading = true;
        // 请求接口
        const params = this.makeReqParam(item.componentCfg);
        get(cfg.remoteUrl, params).then((resp: any) => {
          if (resp.success) {
            if (item.label !== 'queueName') {
              cfg.options = resp.data;
            } else {
              /* rabbitmq组件单独处理 */
              const { VirtualHost } = this.formData;
              const { componentCfg } = this.formConf.forms.find((el) => el.label === 'queueName');
              componentCfg.options = [];
              resp.data.forEach((el) => {
                if (el.virtualHost === VirtualHost) {
                  el.queueList.forEach((ele) => {
                    componentCfg.options.push({
                      queueList: ele
                    });
                  });
                }
              });
            }
            // 分组
            if (_.toString(cfg.groupBy) !== '') {
              const groupOpts: any = [];
              const groupBy = cfg.groupBy;
              const groupObj = _.groupBy(cfg.options, groupBy);
              for (const key of Object.keys(groupObj)) {
                if (_.filter(groupObj[key], { subName: '' }).length === 0) {
                  groupOpts.push({
                    label: key,
                    options: groupObj[key]
                  });
                }
              }
              this.$set(cfg, 'options', groupOpts);
              this.$set(cfg, 'groupable', true);
            } else {
              this.$set(cfg, 'groupable', false);
            }
          }
          this.selectLoading = false;
        });
      }
      if (cfg.useInputAndTarget) {
        // 把配置字段作为下拉选项
        const ref: any = this.$refs.NodeFieldList;
        if (ref !== undefined) {
          this.$set(cfg, 'options', ref.getAllField());
        }
      }
      if (cfg.useTarget) {
        // 把配置字段作为下拉选项
        const ref: any = this.$refs.NodeFieldList;
        if (ref !== undefined) {
          this.$set(cfg, 'options', ref.getTableData());
        }
      }
    }
  }

  remoteMethod(query) {
    if (query !== '') {
      const rec: any = _.find(this.formConf.forms, { model: this.focusModel });
      if (rec) {
        const cfg = rec.componentCfg;
        if (_.toString(cfg.remoteUrl) !== '') {
          this.selectLoading = true;
          // 请求接口
          const params: any = this.makeReqParam(rec.componentCfg);
          params.subName = query;
          get(cfg.remoteUrl, params).then((resp: any) => {
            if (resp.success) {
              cfg.options = resp.data;
            }
            this.selectLoading = false;
          });
        }
      }
    }
  }

  getOptionId(selItem) {
    if (selItem.id !== undefined) {
      return ',' + selItem.id;
    }
    return '';
  }

  selChangeHandle(val2, item) {
    // 设置一个变量用于存放下拉框数据的id
    const array = _.split(val2, ',');
    let val = '';
    if (array.length === 2 && !item.componentCfg.multiple) {
      const idModel = item.model + _PRIMARY_ID;
      // 格式:数据值,id
      this.formData[idModel] = val2;
      val = array[1];
    } else {
      val = array[0];
    }
    /* rabbitmq组件单独处理 */
    if (item.label === 'VirtualHost') {
      this.$set(this.formData, 'queueName', '');
    }
    if (item.componentCfg.fill.length > 0) {
      const p = {};
      if (array.length === 2) {
        const _ID = 'id';
        p[_ID] = val;
      } else {
        p[item.componentCfg.valueField] = val;
      }
      let data = [];
      if (item.componentCfg.groupable) {
        item.componentCfg.options.forEach((o) => {
          data = _.union(data, o.options);
        });
      } else {
        data = item.componentCfg.options;
      }
      const rec: any = _.find(data, p);
      if (rec !== undefined) {
        item.componentCfg.fill.forEach((n) => {
          if (rec[n.fromField]) {
            this.$set(this.formData, n.toModel, rec[n.fromField]);
          } else {
            // 从resProperty中获取
            try {
              const resProperty = JSON.parse(rec.resProperty);
              this.$set(this.formData, n.toModel, resProperty[n.fromField]);
            } catch (e) {
              // donothing
            }
          }
        });
        // 显示和隐藏
        this.hideEl(item, val);
      }
    } else {
      this.hideEl(item, val);
    }
    // 查询其他文本框是否依赖于当前下拉框，数据重置
    this.formConf.forms.forEach((n) => {
      if (n.componentCfg && n.componentCfg.dependsOn) {
        n.componentCfg.dependsOn.forEach((m) => {
          if (m === item.model && item.model !== n.model) {
            delete this.formData[n.model];
          }
        });
      }
    });
  }

  setScriptDefaultValue(needUpdate: boolean) {
    /* eslint-disable-next-line */
    const that = this;
    if (this.visible && this.initComplete && this.formConf.customConf && this.formConf.customConf !== null) {
      this.formConf.customConf.forEach((n) => {
        const td: any = {
          name: n.name,
          nameDesc: n.nameDesc,
          valueDesc: n.valueDesc
        };
        const rec: any = _.find(this.tableData, { name: n.name });
        if (rec === undefined) {
          if (n.valueType === 0) {
            // 常量
            td.value = n.value;
          } else if (n.valueType === 1) {
            // 脚本
            // eslint-disable-next-line
            td.value = eval(n.script);
            this.tableData.push(td);
          }
        } else {
          if (needUpdate && n.valueType === 1) {
            // 脚本
            // eslint-disable-next-line
            rec.value = eval(n.script);
          }
        }
      });
    }
    if (this.visible && this.initComplete) {
      if (this.formConf && this.formConf.forms) {
        this.formConf.forms.forEach((n) => {
          if (n.valueType === 1) {
            // 找出switch
            const switchName = this.makeSwitchModelName(n.model);
            if (!this.formData[switchName]) {
              if (_.toString(this.formData[n.model]) === '') {
                // eslint-disable-next-line
                this.$set(this.formData, n.model, eval(n.script));
              } else {
                if (needUpdate) {
                  // eslint-disable-next-line
                  this.$set(this.formData, n.model, eval(n.script));
                }
              }
            }
          }
        });
      }
    }
  }

  private hideEl(item, val) {
    if (item.componentCfg.hideEl[val] !== undefined) {
      // 先都置为true
      for (const k of Object.keys(item.componentCfg.hideEl)) {
        item.componentCfg.hideEl[k].forEach((el) => {
          // 找到原始记录
          const old: any = _.find(this.formConfBak.forms, { model: el });
          const cur: any = _.find(this.formConf.forms, { model: el });
          if (old) {
            this.$set(cur, 'show', old.show);
            if (this.formData[el] === undefined && old.defaultVal !== undefined) {
              this.$nextTick(() => {
                this.$set(this.formData, el, old.defaultVal);
              });
              this.$forceUpdate();
            }
          }
          if (cur) {
            cur.rules = _.cloneDeep(old.rules);
            this.$set(this.rules, el, cur.rules);
          }
        });
      }
      this.hide(item, val);
    }
  }

  private hide(item, val) {
    item.componentCfg.hideEl[val].forEach((el) => {
      const rec: any = _.find(this.formConf.forms, { model: el });
      if (rec) {
        this.$set(rec, 'show', false);
        // rules设置为失效
        if (rec.rules) {
          rec.rules.forEach((r) => {
            this.$set(r, 'required', false);
          });
        }
        // 找到对应的item，数据清除
        this.$nextTick(() => {
          delete this.formData[rec.model];
          delete this.rules[rec.model];
        });
      }
    });
  }

  private hideAllEl(item) {
    for (const el of Object.keys(item.componentCfg.hideEl)) {
      this.hide(item, el);
    }
  }

  show(item) {
    if (item.show === undefined) {
      return true;
    }
    return item.show;
  }

  querySearchHandle(queryString, cb) {
    const index = _.findIndex(this.formConf.forms, { model: this.focusModel });
    const ff = this.formConf.forms[index];
    const list = ff.componentCfg.options;
    const results = queryString ? list.filter(this.createFilter(queryString)) : list;
    // 调用 callback 返回建议列表的数据
    cb(results);
  }

  createFilter(queryString) {
    return (obj) => {
      return _.toLower(obj.value).indexOf(_.toLower(queryString)) >= 0;
    };
  }

  init() {
    // 创建节点默认对象
    this.jobNode = _.cloneDeep(this.data);
    this.jobNode.inputFields = [];
    this.jobNode.outputFields = [];
    this.jobNode.parallelism = 1;
    this.jobNode.properties = {};
    if (_.toString(this.jobData.content) !== '') {
      // 获取上一个节点的输出字段作为该节点的输入字段
      const edge: any = _.find(this.jobData.content.edges, {
        endNode: this.data.nodeId
      });
      let preOutputFields: any = [];
      if (edge !== undefined) {
        const preNode: any = _.find(this.jobData.content.nodes, {
          nodeId: edge.startNode
        });
        preOutputFields = preNode.outputFields;
      }
      this.input = [];
      const filterResult: any = _.filter(preOutputFields, { outputable: true });
      filterResult.forEach((n) => {
        this.input.push({
          name: n.name,
          type: n.type,
          targetable: n.targetable
        });
      });
      const nn = _.find(this.jobData.content.nodes, {
        nodeId: this.data.nodeId
      });
      if (nn !== undefined) {
        this.jobNode = nn;
      }
      this.formData = this.jobNode.properties !== undefined ? this.jobNode.properties : {};
    }
    // 设置自定义参数
    if (this.formData.customData !== undefined) {
      this.formData.customData.forEach((n) => {
        let nameDesc = '';
        let valueDesc = '';
        if (this.formConf.customConf) {
          const customRec: any = _.find(this.formConf.customConf, {
            name: n.name
          });
          if (customRec) {
            nameDesc = _.toString(customRec.nameDesc);
            valueDesc = _.toString(customRec.valueDesc);
          }
        }
        this.tableData.push({
          name: n.name,
          nameDesc: nameDesc,
          value: n.value,
          valueDesc: valueDesc,
          edit: false
        });
      });
    }
    if (this.formConf && this.formConf.forms) {
      this.formConf.forms.forEach((n) => {
        // 填充校验规则
        if (n.rules !== undefined) {
          n.rules.forEach((r) => {
            if (r.pattern) {
              // eslint-disable-next-line
              r.pattern = eval(r.pattern);
            }
          });
          this.$set(this.rules, n.model, n.rules);
        }
        // 设置默认值
        if (this.formData[n.model] === undefined) {
          if (n.defaultVal !== undefined) {
            this.$set(this.formData, n.model, n.defaultVal);
          } else if (n.type === 'select' && n.componentCfg.multiple) {
            this.$set(this.formData, n.model, []);
          }
        }
        // 初始化
        // 若select未选择，如果存在switchEl配置需要隐藏对应的文本框
        if (n.type === 'select') {
          if (n.componentCfg.useInput) {
            // 把输入字段作为下拉选项
            n.componentCfg.options = _.cloneDeep(this.input);
          }
          const selVal = _.toString(this.formData[n.model]);
          if (selVal === '') {
            this.hideAllEl(n);
          } else {
            this.hideEl(n, selVal);
          }
        }
      });
    }
  }

  private getComponentList() {
    let list: any = [];
    if (this.versionComponentList) {
      this.versionComponentList.forEach((n) => {
        list = _.concat(list, n.paJobComponentList);
      });
    } else {
      this.$store.getters.componentList.forEach((n) => {
        list = _.concat(list, n.paJobComponentList);
      });
    }
    return list;
  }

  private getFormConf(className: string) {
    const rec: any = _.find(this.getComponentList(), {
      className: className
    });
    if (rec === undefined) {
      return {
        forms: [],
        custom: false
      };
    }
    return JSON.parse(rec.properties);
  }

  private renderForm(formConf: any) {
    this.visible = true;
    this.formConf = _.cloneDeep(formConf);
    this.formConfBak = _.cloneDeep(formConf);
    if (!this.formConf) {
      this.formConf = {};
    }
    this.init();
    this.initComplete = true;
    this.setScriptDefaultValue(false);
  }

  @Watch('formData', { deep: true })
  formdataChanged() {
    this.setScriptDefaultValue(true);
  }
}
</script>
<style lang="scss" scoped>
.config-form-item {
  width: 90%;
}
.node-form-input {
  width: 100%;
  height: 25px;
  border: 1px solid #e5e5e5;
  border-radius: 5px;
  &:focus {
    outline: none;
    border: 1px solid #377cff;
  }
}
</style>
