<template>
  <bs-dialog
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    title="资源配置"
    :visible.sync="visible"
    :width="winWidth"
    :before-close="closeDialog"
  >
    <div
      v-if="!this.isPerJob"
      style="display: flex; justify-content: center; height: 70px; margin-bottom: 10px"
    >
      <div style="width: 50%" align="center">
        <div style="height: 100%; width: 80%">
          <div class="title">当前剩余slots</div>
          <div class="total">{{ useInfo.orgResidualSlots }}</div>
        </div>
      </div>
      <div style="width: 50%" align="center">
        <div style="height: 100%; width: 80%">
          <div class="title">预计剩余slots</div>
          <div class="total" :style="{ color: finalSlosts < 0 ? 'red' : '#52c41a' }">
            {{ finalSlosts }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="this.isPerJob" style="display: flex; justify-content: center; height: 90px">
      <div style="width: 25%" align="center">
        <div style="height: 100%; width: 80%">
          <div class="title">当前剩余内存(MB)</div>
          <div class="total">{{ useInfo.orgResidualMemory }}</div>
        </div>
      </div>
      <div style="width: 25%" align="center">
        <div style="height: 100%; width: 80%">
          <div class="title">预计剩余内存(MB)</div>
          <div class="total" :style="{ color: finalMemory < 0 ? 'red' : '#52c41a' }">
            {{ finalMemory }}
          </div>
        </div>
      </div>
      <div style="width: 25%" align="center">
        <div style="height: 100%; width: 80%">
          <div class="title">当前剩余CPU</div>
          <div class="total">{{ useInfo.orgResidualCpu }}</div>
        </div>
      </div>
      <div style="width: 25%" align="center">
        <div style="height: 100%; width: 80%">
          <div class="title">预计剩余CPU</div>
          <div class="total" :style="{ color: finalCpu < 0 ? 'red' : '#52c41a' }">
            {{ finalCpu }}
          </div>
        </div>
      </div>
    </div>
    <!--列表-->
    <div style="height: 40px">
      <span v-if="!this.isPerJob" class="label">调整后的Slots:</span>
      <el-input-number
        v-if="!this.isPerJob"
        v-model="unifiedSlots"
        placeholder="输入统一调整后的Slots"
        style="float: left; width: 100px"
        :class="'input'"
        :min="0"
        :precision="0"
      />
      <el-button
        v-if="!this.isPerJob"
        type="primary"
        style="float: left"
        :class="'input'"
        @click="applyAll"
      >
        应用全部
      </el-button>
      <span v-if="this.isPerJob" class="label">调整后的内存:</span>
      <el-input-number
        v-if="this.isPerJob"
        v-model="unifiedMemory"
        style="float: left; width: 120px"
        :class="'input'"
        :min="0"
        :precision="0"
      />
      <el-button
        v-if="this.isPerJob"
        type="primary"
        style="float: left"
        :class="'input'"
        @click="applyAll('memory')"
      >
        应用全部
      </el-button>
      <span v-if="this.isPerJob" class="label">调整后的CPU:</span>
      <el-input-number
        v-if="this.isPerJob"
        v-model="unifiedCpu"
        style="float: left; width: 100px"
        :class="'input'"
        :min="0"
        :precision="0"
      />
      <el-button
        v-if="this.isPerJob"
        type="primary"
        style="float: left"
        :class="'input'"
        @click="applyAll('cpu')"
      >
        应用全部
      </el-button>

      <el-input
        v-model="search"
        placeholder="输入机构名称过滤"
        style="float: right; width: 150px"
        :class="'input'"
      />
    </div>
    <el-table :data="tableData" size="mini" height="350">
      <el-table-column prop="orgId" label="下级机构ID" />
      <el-table-column prop="orgName" label="下级机构名称" />
      <el-table-column v-if="this.isPerJob" prop="parentMemory" label="总内存(MB)" width="100" />
      <el-table-column
        v-if="this.isPerJob"
        prop="residualMemory"
        label="剩余内存(MB)"
        width="100"
      />
      <el-table-column v-if="this.isPerJob" prop="parentCpu" label="总CPU" width="100" />
      <el-table-column v-if="this.isPerJob" prop="residualCpu" label="剩余CPU" width="100" />
      <el-table-column v-if="!this.isPerJob" prop="parentSlots" label="总Slots" width="100" />
      <el-table-column v-if="!this.isPerJob" prop="residualSlots" label="剩余Slots" width="100" />
      <el-table-column v-if="this.isPerJob" label="调整内存(MB)" min-width="140">
        <template slot-scope="{ row }">
          <el-input-number
            v-model="row.afterMemory"
            style="width: 100%"
            :min="0"
            :precision="0"
            placeholder="请输入调整后的内存"
            @change="memoryChange"
          />
        </template>
      </el-table-column>
      <el-table-column v-if="this.isPerJob" label="调整CPU" min-width="140">
        <template slot-scope="{ row }">
          <el-input-number
            v-model="row.afterCpu"
            style="width: 100%"
            :min="0"
            :precision="0"
            placeholder="请输入调整后的CPU"
            @change="cpuChange"
          />
        </template>
      </el-table-column>
      <el-table-column v-if="!this.isPerJob" label="调整Slots" min-width="140">
        <template slot-scope="{ row }">
          <el-input-number
            v-model="row.afterSlots"
            style="width: 100%"
            :min="0"
            :precision="0"
            placeholder="请输入调整后的Slots"
            @change="slotsChange"
          />
        </template>
      </el-table-column>
      <el-table-column v-if="this.isPerJob" label="队列" min-width="140">
        <template slot-scope="{ row }">
          <el-select
            v-model="row.afterQueueName"
            placeholder="请选择队列名称"
            @change="getQueueToMsg(row)"
          >
            <el-option
              v-for="item in queueNameList"
              :key="item"
              :min="0"
              :precision="0"
              :label="item"
              :value="item"
            />
          </el-select>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" :loading="loading" @click="submit()">保 存</el-button>
    </span>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Watch, Emit } from 'vue-property-decorator';
import {
  URL_FLINK_CLUSTERMSG,
  URL_FLINK_ALLOCATED,
  URL_FLINK_QUEUENAME,
  URL_FLINK_QUEUETOMSG
} from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {}
})
export default class Allocate extends PaBase {
  @Prop({ default: false })
  visible!: boolean;
  @Prop({ default: '40%' })
  winWidth!: string;
  @Prop({ default: false })
  isPerJob!: boolean;
  @Prop({ default: false })
  orgId!: boolean;
  @Prop({ default: [] })
  selectOrgs!: any;
  @Prop({ default: '' })
  toQueueOrgId!: string;
  loading = false;
  useInfo: any = {};
  finalSlosts = 0;
  finalMemory = 0;
  finalCpu = 0;
  search = '';

  unifiedSlots = 0;
  unifiedMemory = 0;
  unifiedCpu = 0;

  copyTableData: any = [];
  tableData: any = [];
  // 队列名
  queueNameList: any = [];
  getQueueName() {
    this.doGet(URL_FLINK_QUEUENAME, {
      params: {
        id: this.$route.query.id
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.queueNameList = resp.data;
      });
    });
  }
  getQueueToMsg(row) {
    this.doPost(
      ((URL_FLINK_QUEUETOMSG + '?clusterId=' + this.$route.query.id) as string) +
        '&orgId=' +
        this.toQueueOrgId +
        '&queue=' +
        row.afterQueueName
    ).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.useInfo = resp.data;
        this.finalSlosts = this.useInfo.orgResidualSlots;
        this.finalMemory = this.useInfo.orgResidualMemory;
        this.finalCpu = this.useInfo.orgResidualCpu;
      });
    });
  }
  applyAll(type: string) {
    if (this.isPerJob) {
      this.tableData.forEach((n) => {
        if (type === 'memory') {
          this.$set(n, 'afterMemory', this.unifiedMemory);
        }
        if (type === 'cpu') {
          this.$set(n, 'afterCpu', this.unifiedCpu);
        }
      });
      if (type === 'memory') {
        this.memoryChange();
      }
      if (type === 'cpu') {
        this.cpuChange();
      }
    } else {
      this.tableData.forEach((n) => {
        this.$set(n, 'afterSlots', this.unifiedSlots);
      });
      this.slotsChange();
    }
  }
  getUseInfo() {
    this.doPost(URL_FLINK_CLUSTERMSG + '?clusterId=' + this.$route.query.id).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.useInfo = resp.data;
        this.finalSlosts = this.useInfo.orgResidualSlots;
        this.finalMemory = this.useInfo.orgResidualMemory;
        this.finalCpu = this.useInfo.orgResidualCpu;
      });
    });
  }

  slotsChange() {
    this.finalSlosts = this.useInfo.orgResidualSlots + this.calcSlots();
  }
  memoryChange() {
    this.finalMemory = this.useInfo.orgResidualMemory + this.calcMemory();
  }
  cpuChange() {
    this.finalCpu = this.useInfo.orgResidualCpu + this.calcCpu();
  }
  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  closeDialog(needFresh) {
    this.loading = false;
    this.finalSlosts = 0;
    this.finalMemory = 0;
    this.finalCpu = 0;
    this.search = '';

    this.unifiedSlots = 0;
    this.unifiedMemory = 0;
    this.unifiedCpu = 0;

    this.copyTableData = [];
  }
  submit() {
    if (this.isPerJob) {
      if (this.finalMemory < 0) {
        this.$message.warning('预计剩余内存不足');
        return;
      }
      if (this.finalCpu < 0) {
        this.$message.warning('预计剩余Cpu不足');
        return;
      }
    } else {
      if (this.finalSlosts < 0) {
        this.$message.warning('预计剩余slots不足');
        return;
      }
    }
    this.search = '';
    const all = this.union();
    this.tableData = [];
    this.copyTableData = [];
    this.tableData = all;

    const allocationInfos: any = [];
    const noQueueList: string[] = [];
    this.tableData.forEach((n) => {
      if (!n.afterQueueName) noQueueList.push(n.orgName);
      allocationInfos.push({
        allocationCpu: n.afterCpu,
        allocationMemory: n.afterMemory,
        allocationSlots: n.afterSlots,
        childrenOrgId: n.orgId,
        orgName: n.orgName,
        queue: n.afterQueueName
      });
    });
    if (noQueueList.length && this.isPerJob) {
      this.$message.error(`请配置${noQueueList.join(',')}的队列`);
      return;
    }
    this.loading = true;
    this.doPost(URL_FLINK_ALLOCATED, {
      clusterId: this.$route.query.id,
      allocationInfos,
      orgId: this.orgId
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.closeDialog(true);
      });
      this.loading = false;
    });
  }

  calcSlots() {
    let count = 0;
    this.union().forEach((n) => {
      if (n.afterSlots !== undefined) {
        const delta = n.parentSlots - n.afterSlots;
        count = count + delta;
      }
    });
    return count;
  }
  calcMemory() {
    let count = 0;
    this.union().forEach((n) => {
      if (n.afterMemory !== undefined) {
        const delta = n.parentMemory - n.afterMemory;
        count = count + delta;
      }
    });
    return count;
  }
  calcCpu() {
    let count = 0;
    this.union().forEach((n) => {
      if (n.afterCpu !== undefined) {
        const delta = n.parentCpu - n.afterCpu;
        count = count + delta;
      }
    });
    return count;
  }
  union() {
    const result = _.concat(this.tableData, this.copyTableData);
    return result;
  }

  @Watch('visible')
  watchVisible(val) {
    if (val) {
      this.tableData = _.cloneDeep(this.selectOrgs);
      this.getUseInfo();
      this.getQueueName();
    }
  }

  @Watch('search')
  watchSearch(val) {
    const all = this.union();
    this.tableData = [];
    this.copyTableData = [];
    if (val !== '') {
      all.forEach((n) => {
        if (n.orgName.indexOf(val) >= 0) {
          this.tableData.push(n);
        } else {
          this.copyTableData.push(n);
        }
      });
    } else {
      this.tableData = all;
    }
  }
}
</script>
<style scoped>
.title {
  display: inline-block;
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 16px;
  font-weight: bold;
}
.total {
  font-variant: tabular-nums;
  list-style: none;
  color: #52c41a;
  font-size: 36px;
  line-height: 38px;
}
.el-dialog_body {
  padding: 20px !important;
}
.label {
  float: left;
  margin: 10px 5px 5px;
}
.input {
  margin: 5px;
}
</style>
