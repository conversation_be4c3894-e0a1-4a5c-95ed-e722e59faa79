<template>
  <div>
    <div class="head-info">
      <div class="name">高级字段</div>
      <div>
        <el-button
          v-if="status !== '1'"
          size="small"
          type="primary"
          style="marginleft: 10px"
          @click="chartAdd"
        >
          添加字段
        </el-button>
      </div>
    </div>
    <el-table
      :data="list"
      style="
        width: 100%;
        max-height: 400px;
        overflow: auto;
        border: 1px solid #f1f1f1;
        border-bottom: none;
      "
      size="mini"
    >
      <el-table-column prop="type" label="字段分类" width="220">
        <template slot-scope="{ $index, row }">
          <el-select
            v-model="row.advanceFieldType"
            placeholder="请选择"
            style="width: 100%"
            @change="typeChange($index)"
          >
            <el-option
              v-for="item in typeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="字段名" min-width="130">
        <template slot-scope="{ row }">
          <div v-if="row.advanceFieldType === 'WATERMARK'" style="display: flex">
            <el-select
              v-model="row.field"
              title="请在基础字段表内添加字段类型=“TIMESTAMP(3)”字段"
              placeholder="请在基础字段表内添加字段类型=“TIMESTAMP(3)”字段"
              style="flex: 1"
            >
              <el-option
                v-for="item in baseField"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-input-number
              v-model="row.column1"
              controls-position="right"
              style="width: 100px"
              placeholder="请输入"
              :precision="0"
              :min="0"
            />
            <el-select v-model="row.column2" placeholder="请选择" style="width: 90px">
              <el-option
                v-for="item in timeType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div v-else>
            <el-input
              v-model="row.field"
              style="width: 100%"
              placeholder="请输入字段名"
              :disabled="status === '1'"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="status !== '1'" label="操作" width="80">
        <template slot-scope="{ $index, row }">
          <i
            class="el-icon-delete"
            style="font-size: 18px"
            @click="deleteChartList($index, row.type)"
          ></i>
        </template>
      </el-table-column>
    </el-table>
    <div class="tabTip">
      {{ error }}
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
@Component({})
export default class SuperField extends PaBase {
  @Prop({
    default() {
      return [];
    }
  })
  fields!: any[];
  @Prop({
    default() {
      return [];
    }
  })
  table!: any[];
  @Prop() status!: '0' | '1' | '2';
  @Prop() error!: '';
  typeList: any = [
    {
      label: '水位线',
      value: 'WATERMARK'
    },
    {
      label: '处理时间',
      value: 'PROCTIME'
    },
    {
      label: '自定义字段',
      value: 'OTHER'
    }
  ];
  timeType: any = [
    {
      label: '时',
      value: 'HOUR'
    },
    {
      label: '分',
      value: 'MINUTE'
    },
    {
      label: '秒',
      value: 'SECOND'
    }
  ];
  list: any = [];
  // 水平线字段选择
  baseField: any = [];
  index = 0;
  created() {
    this.list = this.fields || [];
    this.fieldsChange(this.list);
  }

  fieldsChange(array) {
    if (Array.isArray(array)) {
      const line = array.find((item) => item.advanceFieldType === 'WATERMARK') ? true : false;
      const time = array.find((item) => item.advanceFieldType === 'PROCTIME') ? true : false;
      this.typeList = this.typeList.map((item) => {
        return {
          ...item,
          disabled: (item.value === 'PROCTIME' && time) || (item.value === 'WATERMARK' && line)
        };
      });
    }
  }
  @Watch('fields', { immediate: true, deep: true })
  fieldChange(val) {
    this.list = val || [];
    this.fieldsChange(this.list);
  }

  //水平线字段名选项
  @Watch('table', { immediate: true, deep: true })
  change(list) {
    this.index++;
    const res = list.filter((item) => item.fieldType === 'TIMESTAMP(3)' && item.fieldName);
    this.baseField = res.map((tar) => {
      return {
        label: tar.fieldName,
        value: tar.fieldName
      };
    });
    const water = this.list.find((item) => item.advanceFieldType === 'WATERMARK' && item.field);
    if (water && !this.baseField.find((item) => item.value === water.field) && this.index > 2) {
      this.list = this.list.map((tar) => {
        return {
          ...tar,
          field: tar.advanceFieldType === 'WATERMARK' ? '' : tar.field
        };
      });
    }
  }

  // 增加高级字段
  chartAdd() {
    const line = this.list.find((item) => item.advanceFieldType === 'WATERMARK') ? true : false;
    const time = this.list.find((item) => item.advanceFieldType === 'PROCTIME') ? true : false;
    this.list.push({
      advanceFieldType: line && time ? 'OTHER' : '',
      field: '',
      value: ''
    });
  }
  // 删除高级表字段
  deleteChartList(i) {
    this.list.splice(i, 1);
  }
  // 选择字段分类
  typeChange(index) {
    this.list = this.list.map((item, it) => {
      if (index === it) {
        return {
          advanceFieldType: item.advanceFieldType,
          field: '',
          value: ''
        };
      }
      return item;
    });
    this.fieldsChange(this.list);
  }
}
</script>
<style lang="scss" scoped>
.head-info {
  line-height: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
  .name {
    font-size: 14px;
    font-weight: 500;
  }
}
.tabTip {
  color: red;
}
</style>
