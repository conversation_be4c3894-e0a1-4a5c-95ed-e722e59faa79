import store from '@/store';
import VueRouter, { RawLocation, Route } from 'vue-router';
import { ErrorHandler } from 'vue-router/types/router';
import { hasPermission } from '@/utils';
import i18n from '@/i18n';

export const rawPush = VueRouter.prototype.push;
export const rawReplace = VueRouter.prototype.replace;
export const fixVueRouterError = () => {
  const handler = function (
    this: any,
    location: RawLocation,
    onResolve: ((route: Route) => void) | undefined,
    onReject: ErrorHandler | undefined
  ) {
    if (onResolve || onReject) return rawPush.call(this, location, onResolve, onReject);
    return (rawReplace.call(this, location) as any).catch((err) => err);
  };
  (VueRouter.prototype as any).push = handler;
  (VueRouter.prototype as any).replace = handler;
};

export const hasCustomPermission = (access = '') => {
  const { app = {} } = store.state || {};
  if (!access || !(access in app)) return true;
  return access === 'isCloud' ? !app[access] : app[access];
};
export const beforeEachHook = (to: any, from: any, next: (arg0: boolean | undefined) => void) => {
  if (to.matched && to.matched.length < 1) return next(false);
  if (!hasCustomPermission(to.meta.customAccess)) return next(false);
  if (store.getters.isCloud && ['assets', 'portal/sysconf'].some((it) => to.path.includes(it))) return next(false);
  const isAboutSqlRoute = ['DAEMON', 'DTS'].includes(to.query.type);
  if (!store.getters.enableSql && isAboutSqlRoute) return next(false);
  if (!hasPermission(to.meta.access)) return next(false);
  next(true);
};

export const CLUSTERS_MAPPING = Object.freeze({
  JDBC: i18n.t('pa.jdbc'),
  STREAMCUBE: i18n.t('pa.streamcube'),
  FLINK: i18n.t('pa.flink'),
  HOST: i18n.t('pa.host'),
  HTTP: 'HTTP'
});
export const getClustersTitle = (type: string): string => CLUSTERS_MAPPING[type] || type.toLowerCase();
