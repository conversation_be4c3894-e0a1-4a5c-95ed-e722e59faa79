<template>
  <div>
    <div v-if="!isJar" style="margin-bottom: 8px; text-align: right">
      <el-button size="small" @click="handleAdd">{{ $t('pa.action.addNewRow') }}</el-button>
    </div>
    <pro-edit-table
      v-if="!isJar"
      ref="editTableRef"
      :data="data"
      :columns="columns"
      :edit-props="editProps"
      @change="handleChange"
    />
    <el-form-item v-if="isJar" :label="$t('pa.argument')">
      <el-input v-model="configText" type="textarea" :rows="6" :maxlength="300" style="margin-right: 32px" />
    </el-form-item>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch, Ref } from 'vue-property-decorator';

@Component
export default class CustomConfig extends Vue {
  @Prop({ required: true, default: () => [] }) data!: any;
  @Prop({ type: String }) type!: string;
  @Ref('editTableRef') readonly editTableRef: any;
  get isJar() {
    return this.type === 'jar';
  }
  columns = [
    {
      label: this.$t('pa.key'),
      value: 'key',
      formProps: {
        type: 'input',
        placeholder: this.$t('pa.placeholder.newKey'),
        rules: [
          { required: true, message: this.$t('pa.placeholder.newKey') },
          { validator: this.validatKey, trigger: 'blur' }
        ]
      }
    },
    {
      label: this.$t('pa.value'),
      value: 'value',
      formProps: {
        type: 'input',
        placeholder: this.$t('pa.placeholder.value'),
        rules: [{ required: true, message: this.$t('pa.placeholder.value') }]
      }
    }
  ];
  tableData: any = [];
  editProps: any = { multiple: true, operateColumnWidth: this.isEn ? 150 : 120, defaultActions: ['edit', 'del'] };

  configText = '';
  @Watch('data', { immediate: true })
  dataChange(val) {
    if (this.isJar) {
      this.configText = val || '';
    } else {
      this.tableData = val || [];
    }
  }

  //添加一行
  handleAdd() {
    this.editTableRef.action.create();
  }

  handleChange(val) {
    this.tableData = (val || []).filter((el) => el.key && el.value);
  }

  validatKey(rule, value, callback) {
    const isExist = this.tableData.some((el) => el.key === value);
    if (isExist) {
      callback(new Error(this.$t('pa.tip.someKey')));
      return;
    }
    callback();
  }
  // public methods
  public getData() {
    return this.isJar ? this.configText : this.tableData;
  }
  // public methods
  public async validateForm() {
    const valid = this.isJar ? true : await this.editTableRef.validate();
    return valid;
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-popover__reference span {
  margin-left: 10px;
}
</style>
