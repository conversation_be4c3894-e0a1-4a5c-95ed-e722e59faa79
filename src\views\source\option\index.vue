<template>
  <pro-page :title="$t('pa.menu.optionManage')" :loading="loading" :fixed-header="false">
    <div slot="operation" class="operate-box">
      <bs-search
        v-model="search"
        :class="isEn ? 'option-manage__search' : ''"
        :placeholder="$t('pa.data.option.placeholder.optionNamePlaceholder')"
        maxlength="30"
        @search="getListData()"
      />
      <el-button v-access="'PA.RES.ITEM.ADD'" type="primary" style="margin-left: 10px" @click="newOption">
        {{ $t('pa.data.option.addOption') }}
      </el-button>
    </div>

    <bs-table
      v-loading="tableLoading"
      :height="selectedIds.length ? 'calc(100vh - 344px)' : 'calc(100vh - 288px)'"
      :data="tableData"
      :column-data="columnData"
      :page-data="pageData"
      :selection="true"
      @page-change="handlePageChange"
      @selection-change="handleSelectionChange"
      @refresh="getListData(true)"
    >
      <el-button slot="headerOperator" v-access="'PA.RES.ITEM.DELETE'" size="small" @click="delOptions()">
        {{ $t('pa.action.del') }}
      </el-button>

      <template slot="header-itemReference">
        <div class="option-manage__relations">
          <span> {{ $t('pa.data.text1') }} </span>
          <el-tooltip effect="light" :content="$t('pa.data.text2')">
            <i class="iconfont icon-wenhao"></i>
          </el-tooltip>
        </div>
      </template>
      <template slot="action" slot-scope="{ row }">
        <el-tooltip v-if="hasDelAuthority(row.dataLevelType)" :content="$t('pa.action.del')" effect="light">
          <i class="iconfont icon-shanchu" @click="delOptions(row)"></i>
        </el-tooltip>
      </template>
    </bs-table>
    <option-add v-if="dialogVisible" :visible="dialogVisible" @close="closeDialog" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import { getItemList, delItems } from '@/apis/dataApi';
import { hasPermission } from '@/utils';
import CommonDelete from '@/utils/mixins/common-delete';
import dayjs from 'dayjs';
@Component({
  components: {
    'option-add': () => import('./modals/edit-option-dialog.vue')
  }
})
export default class OptionManage extends Mixins(CommonDelete) {
  tableLoading = false;
  dialogVisible = false;
  loading = false;
  search = '';
  pageData = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 };
  tableData: any = [];
  columnData: any = [];
  selectedIds: string[] = [];
  // 获取列表数据
  async getListData(refresh = false) {
    this.tableLoading = true;
    const { search, pageData } = this;
    const params = {
      search: search ? search.trim() : '',
      pageData,
      sortData: { updateTime: 'DESC' }
    };

    const { data, success, msg } = await getItemList(params);
    if (success) {
      data.tableData.forEach((item: any) => {
        item.createTime = dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss');
      });
      data.columnData.forEach((item: any) => {
        if (item.value === 'itemReference') {
          item.width = 200;
        }
      });
      this.tableData = data.tableData;
      this.columnData = data.columnData;
      this.pageData.total = data.pageData.total || 1;
      refresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    } else {
      this.$message.error(msg);
    }
    this.tableLoading = false;
  }
  // 判断是否有删除权限
  hasDelAuthority(dataLevelType: string) {
    return ['SELF', 'CHILD'].includes(dataLevelType) && hasPermission('PA.RES.ITEM.DELETE');
  }
  // 新建选项
  newOption() {
    this.dialogVisible = true;
  }
  // 删除选项
  delOptions(row: any) {
    const ids = !row ? this.selectedIds : { id: row.id, name: row.itemName, reference: row.itemReference };
    this.commonDel(ids, async (delIds) => {
      const { success, msg, error } = await delItems(delIds);
      if (success) {
        this.getListData();
        this.$message.success(msg);
      } else {
        this.$message.error(error);
      }
    });
  }
  // 表格页码修改回调
  handlePageChange(page: number, size: number) {
    this.pageData.currentPage = page;
    this.pageData.pageSize = size;
    this.getListData();
  }

  // 表格多选回调
  handleSelectionChange(sel: any) {
    this.selectedIds = sel.map((e) => ({ id: e.id, name: e.itemName, reference: e.itemReference }));
  }
  // 关闭弹窗
  closeDialog(needFresh: any) {
    needFresh && this.getListData();
    this.dialogVisible = false;
  }
  created() {
    this.getListData();
    (this.search as any) = this.$route.query.resTitle;
  }
}
</script>

<style lang="scss" scoped>
.iconfont.icon-shanchu {
  cursor: pointer;
}
.option-manage {
  &__search {
    width: 235px !important;
    ::v-deep .el-input {
      width: 235px !important;
    }
  }
  &__relations {
    display: flex;
    align-items: center;
    .icon-wenhao {
      margin-left: 5px;
    }
  }
}
</style>
