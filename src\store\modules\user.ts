import { GET_USER_INFO, SET_USER_INFO } from '../event-name';
import { getUserInfo } from '@/apis/userApi';

const user: UserModule = {
  username: '',
  realname: '',
  orgId: '',
  authorities: [],
  pageSize: 25,
  access: []
};

export default {
  state: () => user,
  mutations: {
    [SET_USER_INFO](state: UserModule, payload: UserModule) {
      Object.keys(payload).forEach((key) => (state[key] = payload[key]));
    }
  },
  actions: {
    async [GET_USER_INFO]({ commit, rootState }) {
      try {
        const { success, data } = await getUserInfo();
        if (!success || !data) return (location.href = rootState.app.loginUrl);
        console.log('用户信息: ', Object.freeze(data));
        commit(SET_USER_INFO, data);
        rootState.paUser.access = data.authorities; // 兼容asssts
      } catch (e) {
        return Promise.reject(e);
      }
    }
  },
  getters: {
    userInfo: (state: UserModule) => state,
    userName: (state: UserModule) => state.username,
    authorities: (state: UserModule) => (Array.isArray(state.authorities) ? state.authorities : []),
    orgId: (state: UserModule) => state.orgId || '',
    orgName: (state: UserModule) => state.orgName || '',
    pageSize: ({ pageSize }) => pageSize
  }
};
