<template>
  <pro-grid type="info" class="connector-info__container">
    <template slot="title">
      <span class="connector-info-title">{{ $t('pa.data.table.detail.connector') }}</span>
      <!-- select -->
      <bs-select
        v-if="allConnectorList.length > 1"
        v-model="connectorId"
        filterable
        :options="allConnectorList"
        @change="handleConnectorChange"
      />
    </template>
    <!-- operation -->
    <template slot="operation">
      <el-checkbox v-model="isSuperConnector">{{ $t('pa.data.table.detail.seniorConnector') }}</el-checkbox>
      <el-tooltip effect="light" placement="top" :content="$t('pa.data.table.tooltip.tooltip3')">
        <i class="iconfont icon-wenhao"></i>
      </el-tooltip>
    </template>
    <!-- body -->
    <div class="connector-info-body">
      <!-- 基础连接器 -->
      <!-- header -->
      <div class="connector-info-header">
        <span class="connector-info-header__title">{{ $t('pa.data.table.detail.baseConnector') }}</span>
      </div>
      <!-- table -->
      <bs-table size="mini" border :column-settings="false" :data="baseConnectorList" :column-data="baseColumnData">
        <!-- name -->
        <template slot="name" slot-scope="{ row }">
          <div class="connector-info-name" :class="{ required: row.required }" :title="row.name">
            {{ row.name }}
          </div>
          <el-tooltip effect="light" :content="row.keyExplain" placement="top">
            <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer; font-weight: 700"></i>
          </el-tooltip>
        </template>
        <!-- defaultValue -->
        <template slot="defaultValue" slot-scope="{ row }">
          <!-- input -->
          <el-input
            v-if="row.pageType === 'INPUT_TYPE' && !row.encrypted"
            :value="row.defaultValue"
            maxlength="128"
            :disabled="row.forbidOperate"
            @input="handleInput($event, 'defaultValue', row)"
          />
          <!-- password -->
          <el-input
            v-if="row.encrypted"
            v-model="row.defaultValue"
            type="password"
            auto-complete="off"
            :disabled="row.forbidOperate"
          />
          <!-- select -->
          <bs-select
            v-if="row.pageType !== 'INPUT_TYPE' && !row.encrypted"
            v-model="row.defaultValue"
            filterable
            style="width: 100%"
            :options="row.options"
            :disabled="row.forbidOperate"
          />
        </template>
      </bs-table>
      <!-- 高级连接器 -->
      <div v-if="isSuperConnector" class="connector-info-advanced">
        <!-- header -->
        <div class="connector-info-header">
          <span class="connector-info-header__title">{{ $t('pa.data.table.detail.seniorConnector') }}</span>
          <el-button type="primary" size="small" @click="handleAddProperty">
            {{ $t('pa.data.table.detail.addAttribute') }}
          </el-button>
        </div>
        <!-- table -->
        <bs-table
          size="mini"
          border
          :column-settings="false"
          :data="advancedConnectorList"
          :column-data="advancedColumnData"
        >
          <!-- key -->
          <template slot="key" slot-scope="{ row }">
            <el-input v-model="row.key" maxlength="100" @input="handleInput($event, 'key', row)" />
          </template>
          <!-- value -->
          <template slot="value" slot-scope="{ row }">
            <el-input v-model="row.value" maxlength="100" @input="handleInput($event, 'value', row)" />
          </template>
          <!-- operator -->
          <template slot="operator" slot-scope="{ $index }">
            <i class="el-icon-delete" @click="handleDeleteProperty($index)"></i>
          </template>
        </bs-table>
      </div>
    </div>
  </pro-grid>
</template>

<script lang="ts">
import { Component, ModelSync, Prop, Vue } from 'vue-property-decorator';
import { getConnectorList } from '@/apis/dataApi';
import { safeArray, safeParse } from '@/utils';

@Component
export default class ConnectorInfo extends Vue {
  @ModelSync('value', 'input', { type: Object, default: () => ({}) }) formData!: any;
  @Prop({ default: () => null }) data!: any;
  @Prop({ default: () => null }) serviceInfo!: any;

  connectorId = '';
  allConnectorList: any[] = [];
  connectList: any[] = [];
  baseColumnData: any[] = [
    {
      label: this.$t('pa.flow.attr'),
      value: 'name',
      width: '25%',
      showOverflowTooltip: false
    },
    {
      label: this.$t('pa.data.table.detail.value'),
      value: 'defaultValue',
      width: '75%',
      showOverflowTooltip: false
    }
  ];
  baseConnectorList: any[] = [];
  isSuperConnector = false;
  advancedColumnData: any[] = [
    {
      label: this.$t('pa.data.table.detail.connectorNature'),
      value: 'key',
      minWidth: '130'
    },
    {
      label: this.$t('pa.data.table.detail.value'),
      value: 'value',
      minWidth: '130'
    },
    {
      label: this.$t('pa.action.action'),
      value: 'operator',
      width: this.isEn ? 120 : 60
    }
  ];
  advancedConnectorList: any[] = [];

  async getConnectorList(serviceInfo) {
    const { success, data, error } = await getConnectorList(serviceInfo.resType, 'all' || serviceInfo.jdbcType);
    if (!success) return this.$message.error(error);
    this.allConnectorList = safeArray(data).map((it) => {
      it.label = it.componentName;
      it.value = it.id;
      it.properties = safeParse(it.properties);
      return it;
    });
    if (this.data) {
      /* 基础 */
      const target = this.allConnectorList.find((it) => it.value === this.data?.connectorId);
      this.connectList = safeArray(target?.properties);
      this.connectorId = this.data?.connectorId || '';
    } else {
      this.connectList = safeArray(this.allConnectorList[0]?.properties);
      this.connectorId = this.allConnectorList[0]?.value;
    }
    this.baseConnectorList = this.getBaseConnectorList(this.connectList, safeParse(this.data?.connectorInfo));
  }
  handleConnectorChange() {
    const target = this.allConnectorList.find((it) => it.value === this.connectorId);
    this.connectList = safeArray(target?.properties);
    this.baseConnectorList = this.getBaseConnectorList(this.connectList);
  }
  getBaseConnectorList(list: any[] = [], data: any = {}) {
    return list
      .map((it) => {
        if (it.needHide) return null;
        return {
          name: it.name,
          required: it.required,
          pageType: it.pageType,
          encrypted: it.encrypted,
          keyExplain: it.keyExplain,
          options: safeArray(it.items).map((value) => ({ label: value, value })),
          forbidOperate: it.forbidOperate,
          defaultValue: this.data?.connectorInfo[it.name] || it.defaultValue || ''
        };
      })
      .filter(Boolean);
  }
  handleAddProperty() {
    this.advancedConnectorList.push({ key: '', value: '' });
  }
  handleInput(value: string, key: string, row: any) {
    row[key] = value.replace(/[\u4E00-\u9FA5]/g, '');
  }
  handleDeleteProperty(index: number) {
    this.advancedConnectorList.splice(index, 1);
  }

  public async init(serviceInfo: any) {
    await this.getConnectorList(serviceInfo);
  }
  public initAdvancedConnector() {
    if (!this.data) return;
    this.advancedConnectorList = this.data?.advanceConnectorInfo.map((it) => {
      const [key, value] = Object.entries(it)[0];
      return { key, value };
    });
    this.isSuperConnector = !!this.advancedConnectorList.length;
  }
  public intLength(length: number) {
    this.connectList = this.connectList.map((it) => {
      if (it.name !== 'field-column') return it;
      return { ...it, needHide: !(length === 3), required: length === 3 };
    });
    this.baseConnectorList = this.getBaseConnectorList(this.connectList);
  }
  public async validate() {
    const connectorInfo = await this.validateBaseConnector();
    const advanceConnectorInfo = await this.validateAdvancedConnector();
    const resProperty = this.serviceInfo.getResProperty();
    for (const it of this.connectList) {
      if (it.name && !(it.name in connectorInfo)) {
        if (it.resInfoKey) {
          connectorInfo[it.name] = resProperty[it.name] || resProperty[it.resInfoKey] || it.defaultValue || '';
        } else {
          connectorInfo[it.name] = resProperty[it.name] || it.defaultValue || '';
        }
      }
    }
    return { connectorId: this.connectorId, connectorInfo, advanceConnectorInfo };
  }
  validateBaseConnector() {
    const result: any = {};
    for (const it of this.baseConnectorList) {
      if (it.required && !it.defaultValue) {
        throw this.$message.error(this.$t('pa.data.table.detail.tips.linkerTip'));
      }
      result[it.name] = it.defaultValue;
    }
    return result;
  }
  validateAdvancedConnector() {
    const result: any[] = [];
    const temp: any = {};
    for (const { key, value } of this.advancedConnectorList) {
      if (key) {
        if (temp[key]) {
          throw this.$message.error(this.$t('pa.data.table.detail.tips.seniorLinkerTip2'));
        } else {
          temp[key] = 1;
        }
        if (!value) {
          throw this.$message.error(this.$t('pa.data.table.detail.tips.seniorLinkerTip1'));
        }
        result.push({ [key]: value });
      }
    }
    return result;
  }
}
</script>

<style lang="scss" scoped>
.connector-info {
  &__container {
    margin: 20px 0;
    ::v-deep .bs-pro-grid__header {
      border-bottom: 1px solid #f1f1f1 !important;
    }
    ::v-deep .bs-select {
      width: 100%;
    }
    .el-form {
      padding: 20px 25px;
    }
    .icon-wenhao {
      margin-left: 10px;
      cursor: pointer;
    }
    .el-icon-delete {
      font-size: 18px;
      cursor: pointer;
    }
  }
  &-title {
    display: inline-block;
    margin-right: 20px;
  }
  &-body {
    padding: 0 35px 20px;
    width: 100%;
  }
  &-name {
    display: inline-block;
    position: relative;
    padding-left: 10px;
    width: calc(100% - 26px);
    vertical-align: middle;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
    }
    &.required {
      &::before {
        content: '*';
        color: red;
      }
    }
  }
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
    &__title {
      font-size: 14px;
      font-weight: 500;
    }
  }
}
</style>
