<template>
  <pro-grid type="info" :title="title">
    <!-- operation -->
    <el-button slot="operation" :disabled="!disabled" type="primary" @click="executeSql">{{
      $t('pa.action.run')
    }}</el-button>
    <!-- main -->
    <div class="table-view-body">
      <!-- list -->
      <table-list :id="id" :type="type" class="table-view-left" @row-click="handleRowClick" @field-preview="showFields" />
      <div class="table-view-right">
        <bs-code
          ref="codeRef"
          :value="sql"
          language="sql"
          :title="codeTitle"
          :read-only="false"
          :extra-style="{ height: '120px' }"
          :formatter="type === 'elasticsearch'"
          @change="handleChange"
        />
        <!-- 结果 -->
        <table-result
          v-loading="loading"
          :data="result"
          :is-string="isString"
          :column-data="columnData"
          :formatter="type !== 'hbase'"
        />
      </div>
    </div>
    <!-- 字段弹窗 -->
    <field-dialog
      v-if="showFieldDialog"
      :id="id"
      :type="type"
      :table-name="tableName"
      :show.sync="showFieldDialog"
      @confirm="getSqlContent"
    />
  </pro-grid>
</template>
<script lang="ts">
import { Component, Prop, Ref, Vue } from 'vue-property-decorator';
import { getSqlContent, runSql } from '@/apis/serviceApi';
import { getCodeTitle } from '../utils';
import { safeArray } from '@/utils';

@Component({
  components: {
    TableList: () => import('./components/table-list.vue'),
    FieldDialog: () => import('./components/field-dialog.vue'),
    TableResult: () => import('./components/table-result.vue')
  }
})
export default class TableView extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: () => ({}) }) params!: any;
  @Ref('codeRef') readonly codeRef!: any;

  loading = false;
  disabled = false;
  codeTitle = '';
  tableName = '';
  showFieldDialog = false;
  sql = '';
  result: any[] = [];
  columnData: any[] = [];

  get id() {
    return this.data.id;
  }
  get type() {
    return (this.data?.resType || '').toLowerCase();
  }
  get title() {
    return this.data?.resType === 'ELASTICSEARCH' ? this.$t('pa.searchDoc') : this.$t('pa.tableInfo');
  }
  get isString() {
    return ['HBASE', 'ELASTICSEARCH'].includes(this.data?.resType);
  }

  created() {
    this.codeTitle = getCodeTitle(this.data.resType) as string;
  }

  /* 处理PostgreSQL的SQL语句，移除OFFSET语法 */
  handlePostgreSQLSql(sql: string): string {
    const resProperty = JSON.parse(this.data.resProperty || '{}');
    if (resProperty.jdbcType === 'PostgreSQL') {
      // 对于PostgreSQL，移除LIMIT X OFFSET Y的语法，只保留LIMIT X
      return sql.replace(/\s+LIMIT\s+(\d+)\s+OFFSET\s+\d+/i, ' LIMIT $1');
    } else {
      return sql;
    }
  }

  /*  执行sql语句 */
  async executeSql() {
    try {
      this.loading = true;
      this.sql = this.codeRef.getValue();
      console.log('this.sql', this.sql);
      this.result = [];
      this.columnData = [];
      const { data, success, error } = await runSql(this.type, this.id, this.$store.getters.encrypt(this.sql));
      if (!success) return this.$message.error(error);
      if (this.isString) {
        this.result = [data === '[]' || !data ? this.$t('pa.noData') : data];
        return;
      }
      this.columnData = safeArray(data?.headers).map((it: any) => {
        const upperLength = it.replace(/[a-z]/g, '').length;
        const minWidth = upperLength * 10 + (it.length - upperLength) * 9 + 30;
        return { label: it, value: it, minWidth: minWidth > 50 ? minWidth : 50 };
      });
      this.result = safeArray(data?.datas).map((it: any) => {
        Object.keys(it).forEach((key) => (it[key] === undefined || it[key] === '') && (it[key] = '-'));
        return it;
      });
    } finally {
      this.loading = false;
    }
  }
  handleRowClick({ name }: any) {
    this.tableName = name || '';
    this.result = [];
    this.columnData = [];
    this.getSqlContent('*');
  }
  /* 打开字段选择弹窗 */
  showFields({ name }: any) {
    this.showFieldDialog = true;
    this.tableName = name || '';
  }
  async getSqlContent(fields: string) {
    try {
      this.loading = true;
      const { success, data, error } = await getSqlContent(this.type, this.id, this.tableName, fields);
      if (!success) return this.$message.error(error);
      const decryptedSql = this.$store.getters.decrypt(data);
      this.sql = this.handlePostgreSQLSql(decryptedSql);
    } finally {
      this.loading = false;
    }
  }
  handleChange(value) {
    this.disabled = !!value;
  }
}
</script>
<style lang="scss" scoped>
.table-view {
  &-body {
    display: flex;
    width: 100%;
    height: 643px;
    border-top: 1px solid $--bs-color-border-lighter;
  }
  &-left {
    padding-top: 10px;
    width: 400px;
    border-right: 1px solid $--bs-color-border-lighter;
  }
  &-right {
    flex: 1;
    padding: 10px;
    overflow: hidden;
  }
}
</style>
