<template>
  <div v-loading="loading" class="table-detail">
    <!-- 表信息 -->
    <div class="table-info">
      <!-- 标题 -->
      <div class="table-info__title">
        <div class="table-info__title__text">表信息</div>
      </div>
      <!-- 内容 -->
      <div class="table-info__content">
        <!-- 信息 -->
        <div v-if="tableInfo.tableName.status" class="tableDetail">
          <el-row class="table__name li">
            <div style="min-width: 240px">
              表名：<span :class="tableInfo.tableName.status">{{ tableInfo.tableName.value }}</span>
            </div>
            <div>
              中文名：
              <span :class="tableInfo.tableNameCn.status">
                {{ tableInfo.tableNameCn.value }}
              </span>
            </div>
          </el-row>
          <el-row class="li">
            业务口径：
            <span :class="tableInfo.businessExplain.status">
              {{ tableInfo.businessExplain.value }}
            </span>
          </el-row>
        </div>
      </div>
    </div>
    <!-- 服务信息 -->
    <div class="table-info">
      <!-- 标题 -->
      <div class="table-info__title">
        <div class="table-info__title__text">服务信息</div>
      </div>
      <!-- 内容 -->
      <div class="table-info__content">
        <!-- 信息 -->
        <div v-if="serviceInfo.restype.status" class="infoDetail">
          <div class="infoDetail__row">
            <div>服务类型：</div>
            <div class="infoDetail__row__value" :class="serviceInfo.restype.status">
              <span>{{ serviceInfo.restype.value }}</span>
            </div>
          </div>
          <div class="infoDetail__row" style="margin-left: 30px">
            <div>服务：</div>
            <div
              class="infoDetail__row__value"
              :class="serviceInfo.resName.status"
              :title="serviceInfo.resName.value"
            >
              <span>{{ serviceInfo.resName.value }} </span>
            </div>
          </div>
          <!-- level1  label 和value -->
          <div v-if="serviceInfo.label.value" class="infoDetail__row" style="margin-left: 30px">
            <div :class="serviceInfo.label.status">
              <span>{{ serviceInfo.label.value }}</span> ：
            </div>
            <div
              class="infoDetail__row__value"
              :class="serviceInfo.level1.status"
              :title="serviceInfo.level1.value"
            >
              <span>{{ serviceInfo.level1.value }}</span>
            </div>
          </div>
          <div
            v-if="serviceInfo.orgCodeFiled && serviceInfo.orgCodeFiled.value"
            class="infoDetail__row"
            style="margin-left: 30px"
          >
            <div>组织机构字段：</div>
            <div
              class="infoDetail__row__value"
              :class="serviceInfo.orgCodeFiled.status"
              :title="serviceInfo.orgCodeFiled.value"
            >
              <span>{{ serviceInfo.orgCodeFiled.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 字段信息 -->
    <div class="table-info">
      <!-- 标题 -->
      <div class="table-info__title">
        <div class="table-info__title__text">字段信息</div>
      </div>
      <!-- 内容 -->
      <div class="table-info__content table-info__field" style="max-height: 600px">
        <!-- 信息 -->
        <div class="table-info__content__advanced">
          <div class="table-info__content__advanced-title">基础字段</div>
          <el-table
            size="mini"
            :data="fieldInfo"
            class="table-info__table"
            :row-class-name="tableRowClassName"
          >
            <!-- 字段名 -->
            <el-table-column min-width="130" align="center">
              <template slot="header">
                <span class="table-info__table__required">字段名</span>
              </template>
              <template slot-scope="{ row }">
                <span>{{ row.value.fieldName }}</span>
              </template>
            </el-table-column>
            <!-- 中文名 -->
            <el-table-column prop="fieldNameCn" min-width="130" align="center">
              <template slot="header">
                <span>中文名</span>
              </template>
              <template slot-scope="{ row }">
                <span>{{ row.value.fieldNameCn }}</span>
              </template>
            </el-table-column>
            <!-- 字段类型 -->
            <el-table-column prop="fieldType" label="字段类型" min-width="130" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.value.fieldType }}</span>
              </template>
            </el-table-column>
            <!-- 列蔟 -->
            <el-table-column
              v-if="type === 'HBASE'"
              prop="columnFamily"
              label="列簇"
              min-width="130"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.value.columnFamily }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="primaryKey" label="主键" min-width="130" align="center">
              <template slot-scope="{ row }">
                <el-switch
                  v-model="row.value.primaryKey"
                  active-value="1"
                  inactive-value="0"
                  disabled
                />
              </template>
            </el-table-column>
            <el-table-column
              v-if="type === 'HIVE'"
              prop="partition"
              label="分区"
              min-width="130"
              align="center"
            >
              <template slot-scope="{ row }">
                <el-switch
                  v-model="row.value.partition"
                  active-value="1"
                  inactive-value="0"
                  disabled
                />
              </template>
            </el-table-column>
            <el-table-column prop="businessExplain" min-width="130" align="center">
              <template slot="header">
                <span>业务口径</span>
              </template>
              <template slot-scope="{ row }">
                <span>{{ row.value.businessExplain }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="table-info__content__advanced">
          <div class="table-info__content__advanced-title">高级字段</div>
          <el-table
            size="mini"
            :data="advanceTableField"
            class="table-info__table"
            :row-class-name="tableRowClassName"
          >
            <el-table-column prop="value" label="字段类型" min-width="130">
              <template slot-scope="{ row }">
                <span>{{ getType(row.advanceFieldType, typeList) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="字段" min-width="130">
              <template slot-scope="{ row }">
                <div v-if="row.advanceFieldType === 'WATERMARK'">
                  <span style="margin-right: 100px">{{ row.field }}</span>
                  {{ row.column1 }} {{ getType(row.column2, timeType) }}
                </div>
                <div v-else>
                  {{ row.field || row.value }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- 连接器 -->
    <div class="table-info">
      <!-- 标题 -->
      <div class="table-info__title">
        <div class="table-info__title__text">
          <div class="table-info__title__flag" @click="handleFlag('connector')">
            <span>连接器</span>
            <i
              v-if="flagMap.includes('connector')"
              class="el-icon-caret-right table-info__title__icon"
            ></i>
            <i v-else class="el-icon-caret-bottom table-info__title__icon"></i>
            <span
              v-if="connectorName.value"
              style="font-weight: normal; margin-left: 5px"
              :class="connectorName.status"
            >
              {{ connectorName.value }}
            </span>
          </div>
        </div>
      </div>
      <!-- 内容 -->
      <div v-if="!flagMap.includes('connector')" class="table-info__content table-info__connect">
        <!-- 信息 -->
        <div class="table-info__content__advanced">
          <div class="table-info__content__advanced-title">基础连接器属性</div>
          <el-table
            size="mini"
            :data="connectList"
            class="table-info__table"
            :row-class-name="tableRowClassName"
          >
            <el-table-column prop="name" label="属 性" min-width="130" align="center">
              <template slot-scope="{ row }">
                <span>{{ Object.keys(row.value)[0] }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="defaultValue" label="值" min-width="130" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.value[Object.keys(row.value)[0]] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="table-info__content__advanced">
          <div class="table-info__content__advanced-title">高级连接器属性</div>
          <el-table
            size="mini"
            :data="advanceConnectorInfo"
            class="table-info__table"
            :row-class-name="tableRowClassName"
          >
            <el-table-column prop="key" label="连接器属性" min-width="130" align="center">
              <template slot-scope="{ row }">
                <span>{{ Object.keys(row.value)[0] }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="值" min-width="130" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.value[Object.keys(row.value)[0]] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { URL_FINDBYCODE } from '@/apis/commonApi';
import { get } from '@/apis/utils/net';

@Component
export default class TableDetail extends PaBase {
  @Prop({ type: String, default: '' }) id!: string;
  @Prop({ type: Array, default: [] }) data;
  private loading = false;
  private flagMap: any[] = [];
  private serviceInfo: any = {
    restype: {},
    resName: {},
    label: {},
    level1: {},
    orgCodeFiled: {}
  }; // 服务信息
  private tableInfo = {
    tableName: {},
    tableNameCn: {},
    createdBy: {},
    createdByMobile: {},
    businessExplain: {},
    label: {}
  };
  private connectorName: any = {};
  private tableInfoRenderList: any[] = [
    {
      label: '表名',
      name: 'tableName'
    },
    {
      label: '中文名',
      name: 'tableNameCn'
    },
    {
      label: '创建人',
      name: 'createdBy'
    },
    {
      label: '创建人电话',
      name: 'createdByMobile'
    },
    {
      label: '业务口径',
      name: 'businessExplain'
    },
    {
      label: '标签',
      name: 'label'
    }
  ];
  fieldInfo: any = []; // 字段信息
  typeEnums: any = [];
  private connectList: any[] = []; // 连接器字段
  private connectorId = ''; // 选中连接器id
  private connector: any[] = []; // 连接器列表
  private connectorMapping = {}; // 连接器id映射
  advanceTableField: any = [];
  advanceConnectorInfo: any = [];
  tableAttributes: any = []; // 表属性
  type = ''; //
  typeList: any = [
    {
      label: '水位线',
      value: 'WATERMARK'
    },
    {
      label: '处理时间',
      value: 'PROCTIME'
    },
    {
      label: '自定义字段',
      value: 'OTHER'
    }
  ];
  timeType: any = [
    {
      label: '时',
      value: 'HOUR'
    },
    {
      label: '分',
      value: 'MINUTE'
    },
    {
      label: '秒',
      value: 'SECOND'
    }
  ];
  getType(type, typeList) {
    const value = typeList.find((item) => item.value === type);
    return value && value['label'];
  }
  handleFlag(name: string) {
    const index = this.flagMap.findIndex((el) => el === name);
    if (index > -1) {
      this.flagMap.splice(index, 1);
    } else {
      this.flagMap.push(name);
    }
  }
  tableRowClassName({ row }) {
    return row.status;
  }
  @Watch('data')
  dataChange(data) {
    if (data.length) {
      this.$forceUpdate();
      this.connectList = [];
      this.advanceTableField = [];
      this.advanceConnectorInfo = [];
      this.fieldInfo = [];
      this.tableAttributes = [];
      this.connectorId = '';

      data.forEach((item) => {
        if (item.resType) {
          this.serviceInfo.restype = item.resType;
        }
        if (item.resName) {
          this.serviceInfo.resName = item.resName;
        }
        if (item.level1Lebel) {
          this.serviceInfo.label = item.level1Lebel;
        }
        // level1 value
        if (item.level1) {
          this.serviceInfo.level1 = item.level1;
        }
        if (item.orgCodeFiled) {
          this.serviceInfo.orgCodeFiled = item.orgCodeFiled;
        }
        // 表信息
        if (item.tableName) {
          this.tableInfo.tableName = item.tableName;
        }
        if (item.tableNameCn) {
          this.tableInfo.tableNameCn = item.tableNameCn;
        }
        if (item.businessExplain) {
          this.tableInfo.businessExplain = item.businessExplain;
        }
        if (item.label) {
          this.tableInfo.label = item.label;
        }
        if (item.baseFieldInfo) {
          this.fieldInfo = item.baseFieldInfo;
        }
        if (item.connectorName) {
          this.connectorName = item.connectorName;
        }
        //  设置连接器
        if (item.connectorInfo && item.connectorInfo.length) {
          this.setConnectorInfo(item.connectorInfo);
        }
        // 设置高级表字段
        if (item.advanceFieldInfo) {
          this.advanceTableField = item.advanceFieldInfo;
        }
        // 设置高级连接器属性
        if (item.advanceConnectorInfo) {
          this.advanceConnectorInfo = item.advanceConnectorInfo;
        }
        // 设置表属性
        if (item.advanceProperty) {
          this.tableAttributes = item.advanceProperty;
        }
      });
    }
  }
  // 设置连接器
  setConnectorInfo(connect) {
    this.connectList = connect;
  }
  created() {
    this.getFieldType();
  }

  /* 获取字段类型 */
  async getFieldType() {
    try {
      const { success, data, error } = await get(URL_FINDBYCODE);
      if (success) {
        const info = JSON.parse(data.value1);
        const arr = Array.isArray(info) ? info : [];
        this.typeEnums = arr.map(({ value, label }: any) => ({ value, label }));
        return;
      }
      this.$message.error(error);
    } catch (e) {
      this.typeEnums = [];
    }
  }
}
</script>

<style scoped lang="scss">
.table {
  &-detail {
    padding-bottom: 30px;
    text-align: left;
  }
  &-info {
    margin: 0 0 10px;
    /* 标题 */
    &__title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 16px;
      height: 50px;
      background: #fff;
      border: 1px solid #e2e2e2;

      &__text {
        margin: 0 0 0 16px;
        padding: 0 0 0 6px;
        color: #666;
        font-size: 14px;
        display: flex;
        align-items: center;
        font-weight: bolder;
        .ADD {
          background: #2196f3;
        }
        .UPDATE {
          background: #ffff80;
        }
        .DELETE {
          background: #f56c6c;
        }
      }
      &__text::before {
        content: '';
        position: relative;
        left: 0;
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 6px;
        background: #ff9c00;
      }

      &__button {
        margin: 0 10px 0 0;
      }

      &__icon {
        margin: 0 15px 0 0;
      }

      &__flag {
        display: flex;
        align-items: center;
        cursor: pointer;

        i {
          margin-left: 15px;
          font-size: 18px;
        }
      }
    }
    &__connect {
      min-height: 200px;
      overflow: auto;
    }
    &__field {
      min-height: 300px;
      overflow: auto;
    }
    /* 内容 */
    &__content {
      margin: 0 16px;
      padding: 20px 25px;
      font-size: 12px;
      border-style: solid;
      border-color: #e2e2e2;
      border-width: 0 1px 1px 1px;
      background: #fff;
      ::v-deep .el-table .ADD {
        background: #2196f3;
      }
      ::v-deep .el-table .UPDATE {
        background: #ffff80;
      }
      ::v-deep .el-table .DELETE {
        background: #f56c6c;
      }
      .ADD {
        background: #2196f3;
      }
      .UPDATE {
        background: #ffff80;
      }
      .DELETE {
        background: #f56c6c;
      }
      .NONE {
        background: #fff;
      }
      > div {
        margin-bottom: 30px;
      }
      .tableDetail {
        .table__name {
          display: flex;
        }
        .li {
          margin-bottom: 30px;
        }
      }

      &__block {
        display: block;
      }

      &__inline {
        display: inline-block;
        width: 25%;
      }

      &__advanced {
        &-title {
          margin: 0 0 10px 0;
          font-size: 14px;
        }
      }
    }

    &__table {
      width: 100%;
      //  height:400px;
      //   overflow-y: auto;

      &__required {
        position: relative;
        padding: 0 0 0 10px;

        &::before {
          content: '*';
          position: absolute;
          top: 0;
          left: 0;
          color: red;
        }
      }

      &__attr {
        display: inline-block;
        margin: 0 10px 0 0;
      }
    }
  }
}

.chartInfoDetail {
  .item {
    margin-bottom: 30px;
  }

  .top {
    div {
      width: 240px;
    }
  }
}
.service-row {
  display: flex;
}
.table-list {
  padding: 1px;
  min-height: 300px;
  max-height: 500px;
  // overflow-y: auto;
  background: #fff;
  .even:nth-child(even) {
    background: #fafdff;
  }
  .advanced-item {
    border: 1px solid #e2e2e2;
    margin: 10px;
    padding: 15px;
    .item-title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
  }

  .el-row {
    height: 50px;
    line-height: 50px;

    .grid-content {
      display: flex;
      justify-content: center;
      align-items: center;
      min-width: 210px;
      .data-card-icon {
        font-size: 19px;
      }
    }
  }

  .el-row .grid-content {
    text-align: center;
  }
}
.connectList {
  min-height: 100px;
  overflow-y: hidden;
  overflow-x: hidden;
}
.infoDetail {
  display: flex;
  // justify-content: space-between;
  &__row {
    display: flex;
    &__value {
      max-width: 100px;
      // padding: 0 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    // width: 240px;
  }
}
</style>
