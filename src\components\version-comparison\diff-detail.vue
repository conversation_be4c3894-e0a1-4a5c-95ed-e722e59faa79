<template>
  <div v-loading="loading" class="table-detail">
    <!-- 表信息 -->
    <div class="table-info">
      <!-- 标题 -->
      <div class="table-info__title">
        <div class="table-info__title__text">{{ $t('pa.data.table.detail.tableInfo') }}</div>
      </div>
      <!-- 内容 -->
      <div class="table-info__content">
        <!-- 信息 -->
        <div v-if="tableInfo.tableName.status" class="tableDetail">
          <el-row class="table__name li">
            <div style="min-width: 240px">
              {{ $t('pa.data.table.detail.tableName') }}
              <span>：</span><span :class="tableInfo.tableName.status">{{ tableInfo.tableName.value }}</span>
            </div>
            <div>
              {{ $t('pa.data.table.detail.chineseName') }}
              <span>：</span>
              <span :class="tableInfo.tableNameCn.status">
                {{ tableInfo.tableNameCn.value }}
              </span>
            </div>
          </el-row>
          <el-row class="li">
            {{ $t('pa.data.table.detail.businessCaliber') }}<span>：</span>
            <span :class="tableInfo.businessExplain.status">
              {{ tableInfo.businessExplain.value }}
            </span>
          </el-row>
        </div>
      </div>
    </div>
    <!-- 服务信息 -->
    <div class="table-info">
      <!-- 标题 -->
      <div class="table-info__title">
        <div class="table-info__title__text">{{ $t('pa.data.table.detail.serviceInfo') }}</div>
      </div>
      <!-- 内容 -->
      <div class="table-info__content">
        <!-- 信息 -->
        <div v-if="serviceInfo.restype.status" class="infoDetail">
          <div class="infoDetail__row">
            <div>{{ $t('pa.serviceType') }}<span>：</span></div>
            <div class="infoDetail__row__value" :class="serviceInfo.restype.status">
              <span>{{ serviceInfo.restype.value }}</span>
            </div>
          </div>
          <div class="infoDetail__row" style="margin-left: 30px">
            <div>{{ $t('pa.service') }}<span>：</span></div>
            <div class="infoDetail__row__value" :class="serviceInfo.resName.status" :title="serviceInfo.resName.value">
              <span>{{ serviceInfo.resName.value }} </span>
            </div>
          </div>
          <!-- level1  label 和value -->
          <div v-if="serviceInfo.label.value" class="infoDetail__row" style="margin-left: 30px">
            <div :class="serviceInfo.label.status">
              <span>{{ serviceInfo.label.value }}</span>
              <span>：</span>
            </div>
            <div class="infoDetail__row__value" :class="serviceInfo.level1.status" :title="serviceInfo.level1.value">
              <span>{{ serviceInfo.level1.value }}</span>
            </div>
          </div>
          <div
            v-if="serviceInfo.orgCodeFiled && serviceInfo.orgCodeFiled.value"
            class="infoDetail__row"
            style="margin-left: 30px"
          >
            <div>{{ $t('pa.orgField') }}<span>：</span></div>
            <div
              class="infoDetail__row__value"
              :class="serviceInfo.orgCodeFiled.status"
              :title="serviceInfo.orgCodeFiled.value"
            >
              <span>{{ serviceInfo.orgCodeFiled.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 字段信息 -->
    <div class="table-info">
      <!-- 标题 -->
      <div class="table-info__title">
        <div class="table-info__title__text">{{ $t('pa.fieldInfo') }}</div>
      </div>
      <!-- 内容 -->
      <div class="table-info__content table-info__field" style="max-height: 600px">
        <!-- 信息 -->
        <div class="table-info__content__advanced">
          <div class="table-info__content__advanced-title">{{ $t('pa.data.baseFields') }}</div>
          <el-table size="mini" :data="fieldInfo" class="table-info__table" :row-class-name="tableRowClassName">
            <!-- 字段名 -->
            <el-table-column min-width="130" align="center">
              <template slot="header">
                <span class="table-info__table__required">{{ $t('pa.data.table.detail.fieldName') }}</span>
              </template>
              <template slot-scope="{ row }">
                <span>{{ row.value.fieldName }}</span>
              </template>
            </el-table-column>
            <!-- 中文名 -->
            <el-table-column prop="fieldNameCn" min-width="130" align="center">
              <template slot="header">
                <span>{{ $t('pa.data.table.detail.chineseName') }}</span>
              </template>
              <template slot-scope="{ row }">
                <span>{{ row.value.fieldNameCn }}</span>
              </template>
            </el-table-column>
            <!-- 字段类型 -->
            <el-table-column prop="fieldType" :label="$t('pa.fieldType')" min-width="130" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.value.fieldType }}</span>
              </template>
            </el-table-column>
            <!-- 列蔟 -->
            <el-table-column
              v-if="type === 'HBASE'"
              prop="columnFamily"
              :label="$t('pa.data.table.detail.columnCluster')"
              min-width="130"
              align="center"
            >
              <template slot-scope="{ row }">
                <span>{{ row.value.columnFamily }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="primaryKey" :label="$t('pa.data.table.detail.primaryKey')" min-width="130" align="center">
              <template slot-scope="{ row }">
                <el-switch v-model="row.value.primaryKey" active-value="1" inactive-value="0" disabled />
              </template>
            </el-table-column>
            <el-table-column
              v-if="type === 'HIVE'"
              prop="partition"
              :label="$t('pa.data.table.detail.partition')"
              min-width="130"
              align="center"
            >
              <template slot-scope="{ row }">
                <el-switch v-model="row.value.partition" active-value="1" inactive-value="0" disabled />
              </template>
            </el-table-column>
            <el-table-column prop="businessExplain" :min-width="isEn ? 180 : 130" align="center">
              <template slot="header">
                <span>{{ $t('pa.data.table.detail.businessCaliber') }}</span>
              </template>
              <template slot-scope="{ row }">
                <span>{{ row.value.businessExplain }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="table-info__content__advanced">
          <div class="table-info__content__advanced-title">{{ $t('pa.data.seniorFields') }}</div>
          <el-table size="mini" :data="advanceTableField" class="table-info__table" :row-class-name="tableRowClassName">
            <el-table-column prop="value" :label="$t('pa.fieldType')" min-width="130">
              <template slot-scope="{ row }">
                <span>{{ getType(row.advanceFieldType, typeList) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="value" :label="$t('pa.fields')" min-width="130">
              <template slot-scope="{ row }">
                <div v-if="row.advanceFieldType === 'WATERMARK'">
                  <span style="margin-right: 100px">{{ row.field }}</span>
                  {{ row.column1 }} {{ getType(row.column2, timeType) }}
                </div>
                <div v-else>
                  {{ row.field || row.value }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- 连接器 -->
    <div class="table-info">
      <!-- 标题 -->
      <div class="table-info__title">
        <div class="table-info__title__text">
          <div class="table-info__title__flag" @click="handleFlag('connector')">
            <span>{{ $t('pa.data.table.detail.connector') }}</span>
            <i v-if="flagMap.includes('connector')" class="el-icon-caret-right table-info__title__icon"></i>
            <i v-else class="el-icon-caret-bottom table-info__title__icon"></i>
            <span v-if="connectorName.value" style="font-weight: normal; margin-left: 5px" :class="connectorName.status">
              {{ connectorName.value }}
            </span>
          </div>
        </div>
      </div>
      <!-- 内容 -->
      <div v-if="!flagMap.includes('connector')" class="table-info__content table-info__connect">
        <!-- 信息 -->
        <div class="table-info__content__advanced">
          <div class="table-info__content__advanced-title">{{ $t('pa.baseConnectorAttr') }}</div>
          <el-table size="mini" :data="connectList" class="table-info__table" :row-class-name="tableRowClassName">
            <el-table-column prop="name" :label="this.$t('pa.data.table.detail.attribute')" min-width="130" align="center">
              <template slot-scope="{ row }">
                <span>{{ Object.keys(row.value)[0] }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="defaultValue" :label="$t('pa.value')" min-width="130" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.value[Object.keys(row.value)[0]] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="table-info__content__advanced">
          <div class="table-info__content__advanced-title">{{ $t('pa.AdvancedConnectorAttr') }}</div>
          <el-table size="mini" :data="advanceConnectorInfo" class="table-info__table" :row-class-name="tableRowClassName">
            <el-table-column prop="key" :label="$t('pa.data.table.detail.connectorNature')" min-width="130" align="center">
              <template slot-scope="{ row }">
                <span>{{ Object.keys(row.value)[0] }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="value" :label="$t('pa.value')" min-width="130" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.value[Object.keys(row.value)[0]] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { URL_FINDBYCODE } from '@/apis/commonApi';
import { get } from '@/apis/utils/net';

@Component
export default class TableDetail extends PaBase {
  @Prop({ type: String, default: '' }) id!: string;
  @Prop({ type: Array, default: [] }) data;
  private loading = false;
  private flagMap: any[] = [];
  private serviceInfo: any = {
    restype: {},
    resName: {},
    label: {},
    level1: {},
    orgCodeFiled: {}
  }; // 服务信息
  private tableInfo = {
    tableName: {},
    tableNameCn: {},
    createdBy: {},
    createdByMobile: {},
    businessExplain: {},
    label: {}
  };
  private connectorName: any = {};
  private tableInfoRenderList: any[] = [
    {
      label: this.$t('pa.data.table.detail.tableName'),
      name: 'tableName'
    },
    {
      label: this.$t('pa.data.table.detail.chineseName'),
      name: 'tableNameCn'
    },
    {
      label: this.$t('pa.creator'),
      name: 'createdBy'
    },
    {
      label: this.$t('pa.data.table.detail.creatorPhone'),
      name: 'createdByMobile'
    },
    {
      label: this.$t('pa.flow.business'),
      name: 'businessExplain'
    },
    {
      label: this.$t('pa.flow.tag'),
      name: 'label'
    }
  ];
  fieldInfo: any = []; // 字段信息
  typeEnums: any = [];
  private connectList: any[] = []; // 连接器字段
  private connectorId = ''; // 选中连接器id
  private connector: any[] = []; // 连接器列表
  private connectorMapping = {}; // 连接器id映射
  advanceTableField: any = [];
  advanceConnectorInfo: any = [];
  tableAttributes: any = []; // 表属性
  type = ''; //
  typeList: any = [
    {
      label: this.$t('pa.waterLevelLine'),
      value: 'WATERMARK'
    },
    {
      label: this.$t('pa.handleTime'),
      value: 'PROCTIME'
    },
    {
      label: this.$t('pa.customFields'),
      value: 'OTHER'
    }
  ];
  timeType: any = [
    {
      label: this.$t('pa.unit.hour'),
      value: 'HOUR'
    },
    {
      label: this.$t('pa.unit.minute'),
      value: 'MINUTE'
    },
    {
      label: this.$t('pa.unit.second'),
      value: 'SECOND'
    }
  ];
  getType(type, typeList) {
    const value = typeList.find((item) => item.value === type);
    return value && value['label'];
  }
  handleFlag(name: string) {
    const index = this.flagMap.findIndex((el) => el === name);
    if (index > -1) {
      this.flagMap.splice(index, 1);
    } else {
      this.flagMap.push(name);
    }
  }
  tableRowClassName({ row }) {
    return row.status;
  }
  @Watch('data')
  dataChange(data) {
    if (data.length) {
      this.$forceUpdate();
      this.connectList = [];
      this.advanceTableField = [];
      this.advanceConnectorInfo = [];
      this.fieldInfo = [];
      this.tableAttributes = [];
      this.connectorId = '';

      data.forEach((item) => {
        if (item.resType) {
          this.serviceInfo.restype = item.resType;
        }
        if (item.resName) {
          this.serviceInfo.resName = item.resName;
        }
        if (item.level1Lebel) {
          this.serviceInfo.label = item.level1Lebel;
        }
        // level1 value
        if (item.level1) {
          this.serviceInfo.level1 = item.level1;
        }
        if (item.orgCodeFiled) {
          this.serviceInfo.orgCodeFiled = item.orgCodeFiled;
        }
        // 表信息
        if (item.tableName) {
          this.tableInfo.tableName = item.tableName;
        }
        if (item.tableNameCn) {
          this.tableInfo.tableNameCn = item.tableNameCn;
        }
        if (item.businessExplain) {
          this.tableInfo.businessExplain = item.businessExplain;
        }
        if (item.label) {
          this.tableInfo.label = item.label;
        }
        if (item.baseFieldInfo) {
          this.fieldInfo = item.baseFieldInfo;
        }
        if (item.connectorName) {
          this.connectorName = item.connectorName;
        }
        //  设置连接器
        if (item.connectorInfo && item.connectorInfo.length) {
          this.setConnectorInfo(item.connectorInfo);
        }
        // 设置高级表字段
        if (item.advanceFieldInfo) {
          console.log(item.advanceFieldInfo);
          this.advanceTableField = item.advanceFieldInfo;
        }
        // 设置高级连接器属性
        if (item.advanceConnectorInfo) {
          this.advanceConnectorInfo = item.advanceConnectorInfo;
        }
        // 设置表属性
        if (item.advanceProperty) {
          this.tableAttributes = item.advanceProperty;
        }
      });
      console.log(this.tableAttributes);
    }
  }
  // 设置连接器
  setConnectorInfo(connect) {
    this.connectList = connect;
  }
  created() {
    this.getFieldType();
  }

  /* 获取字段类型 */
  async getFieldType() {
    try {
      const { success, data, error } = await get(URL_FINDBYCODE);
      if (success) {
        const info = JSON.parse(data.value1);
        const arr = Array.isArray(info) ? info : [];
        this.typeEnums = arr.map(({ value, label }: any) => ({ value, label }));
        return;
      }
      this.$message.error(error);
    } catch (e) {
      this.typeEnums = [];
    }
  }
}
</script>

<style scoped lang="scss">
.table {
  &-detail {
    padding-bottom: 30px;
    text-align: left;
  }
  &-info {
    margin: 0 0 10px;
    /* 标题 */
    &__title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 16px;
      height: 50px;
      background: #fff;
      border: 1px solid #e2e2e2;

      &__text {
        margin: 0 0 0 16px;
        padding: 0 0 0 6px;
        color: #666;
        font-size: 14px;
        display: flex;
        align-items: center;
        font-weight: bolder;
        .ADD {
          background: #2196f3;
        }
        .UPDATE {
          background: #ffff80;
        }
        .DELETE {
          background: #f56c6c;
        }
      }
      &__text::before {
        content: '';
        position: relative;
        left: 0;
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 6px;
        background: #ff9c00;
      }

      &__button {
        margin: 0 10px 0 0;
      }

      &__icon {
        margin: 0 15px 0 0;
      }

      &__flag {
        display: flex;
        align-items: center;
        cursor: pointer;

        i {
          margin-left: 15px;
          font-size: 18px;
        }
      }
    }
    &__connect {
      min-height: 200px;
      overflow: auto;
    }
    &__field {
      min-height: 300px;
      overflow: auto;
    }
    /* 内容 */
    &__content {
      margin: 0 16px;
      padding: 20px 25px;
      font-size: 12px;
      border-style: solid;
      border-color: #e2e2e2;
      border-width: 0 1px 1px 1px;
      background: #fff;
      ::v-deep .el-table .ADD {
        background: #2196f3;
      }
      ::v-deep .el-table .UPDATE {
        background: #ffff80;
      }
      ::v-deep .el-table .DELETE {
        background: #f56c6c;
      }
      .ADD {
        background: #2196f3;
      }
      .UPDATE {
        background: #ffff80;
      }
      .DELETE {
        background: #f56c6c;
      }
      .NONE {
        background: #fff;
      }
      > div {
        margin-bottom: 30px;
      }
      .tableDetail {
        .table__name {
          display: flex;
        }
        .li {
          margin-bottom: 30px;
        }
      }

      &__block {
        display: block;
      }

      &__inline {
        display: inline-block;
        width: 25%;
      }

      &__advanced {
        &-title {
          margin: 0 0 10px 0;
          font-size: 14px;
        }
      }
    }

    &__table {
      width: 100%;
      //  height:400px;
      //   overflow-y: auto;

      &__required {
        position: relative;
        padding: 0 0 0 10px;

        &::before {
          content: '*';
          position: absolute;
          top: 0;
          left: 0;
          color: red;
        }
      }

      &__attr {
        display: inline-block;
        margin: 0 10px 0 0;
      }
    }
  }
}

.chartInfoDetail {
  .item {
    margin-bottom: 30px;
  }

  .top {
    div {
      width: 240px;
    }
  }
}
.service-row {
  display: flex;
}
.table-list {
  padding: 1px;
  min-height: 300px;
  max-height: 500px;
  // overflow-y: auto;
  background: #fff;
  .even:nth-child(even) {
    background: #fafdff;
  }
  .advanced-item {
    border: 1px solid #e2e2e2;
    margin: 10px;
    padding: 15px;
    .item-title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
  }

  .el-row {
    height: 50px;
    line-height: 50px;

    .grid-content {
      display: flex;
      justify-content: center;
      align-items: center;
      min-width: 210px;
      .data-card-icon {
        font-size: 19px;
      }
    }
  }

  .el-row .grid-content {
    text-align: center;
  }
}
.connectList {
  min-height: 100px;
  overflow-y: hidden;
  overflow-x: hidden;
}
.infoDetail {
  display: flex;
  // justify-content: space-between;
  &__row {
    display: flex;
    &__value {
      max-width: 100px;
      // padding: 0 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    // width: 240px;
  }
}
</style>
