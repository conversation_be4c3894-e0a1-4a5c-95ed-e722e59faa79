// assets 流立方样式兼容
@import './other-platform.scss';
// 组件库全局样式覆盖
@import './cover.scss';

html,
body {
  height: 100%;
  // 设置全局滚动条样式
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  /*定义滚动条轨道 内阴影*/
  ::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 6px;
  }

  /*定义滑块 内阴影*/
  ::-webkit-scrollbar-thumb {
    background: #c9c9c9;
    border-radius: 6px;
  }

  .el-table--striped {
    border-top: 1px solid #ebeef5;
  }

  // 流立方样式调整
  .content {
    height: calc(100vh - 123px);
    overflow-y: auto;

    .page-content {
      height: 50px;
      background: #fff;
      padding: 0 20px;
    }
  }
  li {
    list-style: none;
  }
}

/*
 * @description: 按钮默认大小
 */
.default-btn {
  width: 90px;
  margin-left: 10px;

  &:first-child {
    margin-left: unset;
  }
}

/*
 * @description: 页面内容区域样式：不包含左侧菜单栏和头部信息（平台信息、用户信息）
 */
.page-content {
  height: calc(100vh - 130px);

  .first_content {
    height: calc(100% - 42px);
  }

  header {
    height: 50px;
    background: #ffffff;
    border-bottom: 1px solid #f1f1f1;
    border-left: none;
    border-right: none;
    padding: 0 20px;
    font-size: 16px;
    display: flex;
    align-items: center;

    .operate-box {
      flex: 1;
      text-align: right;
    }
  }

  .content {
    height: calc(100% - 30px);
    background: #ffffff;
    overflow: auto;

    .table-header {
      height: 50px;
      font-size: 12px;
      color: #666666;
      padding: 0 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .detail_content {
    min-height: 170px;
    border: 1px solid #f1f1f1;
    border-top: none;
    background: #ffffff;
    overflow: auto;
  }
}

.menu-item-container {
  .menu-item {
    padding: 10px;
    line-height: 18px;
    font-size: 12px;
    cursor: pointer;

    &:hover {
      background-color: #f5f7f9;
    }
  }
}

.add-item {
  border: 1px solid #0096f0;
  -moz-border-radius: 5px;
  border-radius: 5px;
  opacity: 0.8;
  width: 180px;
  height: 34px;
  line-height: 34px;
  cursor: pointer;
  text-align: center;
  z-index: 20;
  position: absolute;
  color: #a7a6a6;
  font-size: 12px;
}

.nodeInstance {
  border: 1px solid #0096f0;
  -moz-border-radius: 5px;
  border-radius: 5px;
  opacity: 0.8;
  width: 180px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  z-index: 20;
  position: absolute;
  color: #252525;
  font-size: 12px;
  cursor: pointer;

  .node-text {
    display: inline-block;
    max-width: 150px;
    margin-right: 26px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .bg-circle {
    position: absolute;
    width: 26px;
    height: 26px;
    color: #ffffff;
    background: #2196f3;
    border-radius: 5px;
    right: 4px;
    bottom: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .badge {
    position: absolute;
    color: white;
    font-size: 14px;
    background-color: red;
    min-height: 18px;
    min-width: 20px;
    line-height: 18px;
    right: 92%;
    top: -16px;
    text-align: center;
    -webkit-border-radius: 24px;
    border-radius: 24px;
    padding: 2px;
  }
}

.active-node {
  background: aliceblue;
}

.nodeInstance.error {
  border: 1px solid red;
  box-shadow: 0 0 10px red;
}

.tippy-tooltip {
  font-size: 12px !important;
  color: #666666 !important;
}

.pagination {
  padding-top: 10px;
  padding-right: 20px;
  text-align: right;
}

.tab-title {
  font-size: 14px;
  height: 50px;
  display: flex;
  align-items: center;
  color: #666666;
  // font-weight: bolder;
  padding-right: 15px;

  .title-text {
    // border-left: 3px solid #2196f3;
    margin-left: 16px;
    padding-left: 6px;
    flex: 1;

    span {
      vertical-align: middle;
    }

    .title-memo {
      font-size: 12px;
      font-weight: normal;
      border-left: 1px solid #e2e2e2;
      margin-left: 16px;
      padding-left: 16px;
    }

    .title-refresh {
      float: right;
      cursor: pointer;
      font-size: 16px;
    }
  }

  .title-text::before {
    content: ' ';
    position: relative;
    left: 0;
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    background: #ff9e2b;
    border-radius: 2px;
  }

  .title-search {
    flex: 1;
    text-align: right;
  }
}

.el-loading-mask {
  z-index: 1999;
}
.tipIndex {
  z-index: 3001 !important;
  p {
    word-break: break-all;
  }
}

.tipIndex {
  z-index: 3001 !important;
}

.pa-en .tipIndex {
  width: 400px;
}

.tooltip-auto-wrap {
  max-width: 300px;
  overflow-wrap: break-word;
}

.flow-canvas-overlay {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 1999;
  height: 9000px;
  width: 9000px;
  opacity: 0.85;
  background: #5a7497;
}

.iconfont {
  color: #577690;
}

.iconfont:focus {
  outline: none;
}
.iconfont.icon-wenhao {
  color: #444;
}

.danhua-link {
  stroke: rgb(127, 128, 129, 0.3);
}

.danhua-node {
  opacity: 0.2;
}

// 想做的是高亮显示可以连接的点，但是scope无法动态修改
// .active svg circle {
//   fill: rgba(26, 161, 241, 0.8);
//   stroke: #1aa1f1;
// }

.code-mirror {
  height: 100%;
}

.CodeMirror {
  border: 1px solid #d7d8d8;
  height: calc(100% - 2px) !important;
  font: 12px / normal 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
}

.CodeMirror-scroll {
  height: 100% !important;
  overflow-y: hidden;
  overflow-x: auto;
}

.el-dialog__headerbtn {
  top: 10px;
}

.abow_dialog {
  display: flex;
  justify-content: center;
  align-items: Center;
  overflow: hidden;

  .el-dialog {
    margin: 0 auto !important;
    height: 90%;
    overflow-y: hidden;

    .el-dialog__body {
      position: relative;
      height: calc(100% - 55px);
      padding: 5px 20px;
      overflow-y: auto;

      .el-divider--horizontal {
        margin: 14px 0;
      }
    }

    .el-dialog__footer {
      position: absolute;
      bottom: 0px;
      width: 100%;
      z-index: 10;
      background: #fff;
    }
  }
}

.jtk-endpoint svg {
  z-index: 22;
  cursor: crosshair;
}

// 脚本包构建页面样式重置
.build-content .ivu-card-head div.ivu-tooltip {
  line-height: 28px !important;
  height: 28px !important;
}

.build-content .ivu-card-head div.ivu-tooltip-rel {
  line-height: 28px !important;
  height: 28px !important;
  width: 100%;
  text-align: center;
  display: block;
}

.el-date-editor .el-range__close-icon {
  height: auto;
}

.el-button:not(.el-button--text) {
  padding: 7px 12px;
}

.el-message-box {
  & .el-message-box__message p {
    font-size: 14px;
    text-align: left;
  }

  & .el-message-box__btns {
    text-align: right;
  }
}

.CodeMirror-hints {
  z-index: 5000 !important;
}
/* 过滤组件 start */
.code {
  &-green {
    color: #54c958;
  }

  &-red {
    color: #ff5353;
  }
}

pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}
code {
  &.hljs {
    padding: 3px 5px;
  }
  .hljs {
    background: #fff;
    color: #000;
    &-comment,
    &-quote,
    &-variable {
      color: green;
    }
    &-built_in,
    &-keyword,
    &-name,
    &-selector-tag,
    &-tag {
      color: #00f;
    }
    &-addition,
    &-attribute,
    &-literal,
    &-section,
    &-string,
    &-template-tag,
    &-template-variable,
    &-title,
    &-type {
      color: #a31515;
    }
    &-deletion,
    &-meta,
    &-selector-attr,
    &-selector-pseudo {
      color: #2b91af;
    }
    &-doctag {
      color: grey;
    }
    &-attr {
      color: red;
    }
    &-bullet,
    &-link,
    &-symbol {
      color: #00b0e8;
    }
    &-emphasis {
      font-style: italic;
    }
    &-strong {
      font-weight: 700;
    }
  }
}
.relation-tooltip {
  display: inline-block;
  position: relative;
  width: 108px;
  padding: 16px 12px;
  font-size: 14px;
  font-weight: 400;
  color: #191f25;
  text-align: center;
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(25, 31, 37, 0.1);
  box-sizing: border-box;
  &::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent #ffffff transparent;
  }
}
/* 过滤组件 end */

.el-tag {
  max-width: 100% !important;
}
.el-cascader__more-tags {
  max-height: 150px;
  overflow: scroll;
}
.Node {
  &__container {
    position: relative;
    display: inline-flex !important;
    justify-content: center;
    align-items: center;
    width: 35px;
    height: 35px;
    // border: 1px solid red;
    background: transparent;
    pointer-events: none;
    font-size: 12px;
  }
  &-tag {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 3px;
    width: 8px;
    height: 8px;
    text-align: center;
    line-height: 8px;
    color: #fff;
    background: red;
    border-radius: 100%;
    &--t0 {
      left: unset;
      right: 1px;
    }
    &--t1 {
      left: 1px;
      right: unset;
    }
    &__span {
      display: inline-block;
      font-size: 12px !important;
      transform: scale(0.4);
    }
  }
  &-name {
    font-family: MicrosoftYaHei;
    color: #ffffff;
  }
}
.source-ani {
  position: relative;
  div {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    border-radius: 50%;
    animation-duration: 3s;
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
    animation-iteration-count: infinite;
  }
  .c1 {
    animation-name: c1;
    animation-delay: 0;
  }
  .c2 {
    animation-name: c1;
    animation-delay: 1s;
  }
  .c3 {
    animation-name: c1;
    animation-delay: 2s;
  }
  @keyframes c1 {
    0% {
      transform: scale(1);
      opacity: 0.6;
    }
    100% {
      transform: scale(1.6);
      opacity: 0.1;
    }
  }
  @keyframes c2 {
    0% {
      opacity: 0.2;
    }
    100% {
      opacity: 0;
    }
  }
}

// 覆盖bsview3.0 el-tree样式
.el-tree-node {
  line-height: 21px;
  &__content {
    padding-top: unset;
    padding-bottom: unset;
    margin-top: unset;
  }
}

.in-active-path {
  .el-cascader-node__label {
    color: #377cff;
  }
}

.el-switch__label.is-active {
  color: #aaa;
}

.bs-detail-block .bs-detail__header-title {
  font-weight: normal;
}

// 级联 流程目录
.el-popper.el-cascader__dropdown.dir-cascader {
  .el-cascader-panel {
    max-width: 660px;
  }
  .el-cascader-menu {
    flex-shrink: 0;
    width: 220px;
  }
  .el-cascader__suggestion-panel {
    max-width: 660px;
  }
}
