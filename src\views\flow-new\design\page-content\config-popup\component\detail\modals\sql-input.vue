<template>
  <el-dialog
    :width="width"
    :title="title"
    :visible.sync="display"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <!-- 表单 -->
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      :disabled="disabled"
      :label-width="labelWidth"
      class="sql-input-container"
    >
      <!-- 选择表 -->
      <el-form-item label="选择表/视图" prop="sourceTable">
        <el-select
          v-model="form.sourceTable"
          filterable
          clearable
          placeholder="请选择表/视图"
          @change="handleSourceTableChange"
        >
          <el-option v-for="el in tableList" :key="el.id" :value="el.name">
            <div class="option">
              <span>{{ el.name }}</span>
              <span :class="`option-tag option-tag--${el.type}`">{{ getTagLabel(el.type) }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <!-- 选中表 -->
      <el-form-item label="选中表/视图">
        <div class="sql-input-selected">
          <span @click="goTableDetail">{{ form.sourceTable }}</span>
        </div>
      </el-form-item>
      <!-- 连接器配置 -->
      <connector-config
        ref="connector"
        :disabled="disabled"
        :data="connectorConfigData"
        :origin-data="form.connectorInfo"
        :job-data="jobData"
        :node-name="this.data.nodeName"
        @change="handleConnectorChange"
      />
    </el-form>
    <!-- footer -->
    <sql-footer
      slot="footer"
      change-name
      :data.sync="form"
      @submit="submit"
      @cancel="closeDialog"
    />
    <!-- 详情 -->
    <views-detail-dialog v-if="showDetail" :id="id" :show.sync="showDetail" :type="type" />
  </el-dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import { cloneDeep } from 'lodash';
import Form from 'bs-ui-pro/lib/form';
import { get } from '@/apis/utils/net';
import { URL_GET_ALL_TABLE, URL_GET_CONNECTOR_INFO } from '@/apis/commonApi';
import ConnectorConfig from './components/connector-config.vue';

@Component({
  components: {
    ConnectorConfig,
    SqlFooter: () => import('./components/sql-footer.vue'),
    ViewsDetailDialog: () => import('@/components/views-detail-dialog/index.vue')
  }
})
export default class SqlInput extends Vue {
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @PropSync('show', { type: Boolean, default: false }) display!: boolean;
  @Ref('form') readonly formRef!: Form;
  @Ref('connector') readonly connector!: ConnectorConfig;

  private title = 'sql输入';
  private labelWidth = '100px';
  private width = '650px';
  private tableList: any = []; // 表或视图列表
  private rules = {
    sourceTable: {
      required: true,
      message: '请选择表/视图',
      trigger: 'change'
    }
  }; // 表单校验规则
  private form: any = {
    sourceTable: '', // name
    sourceTableId: '', // id
    sourceTableType: '', // 类型
    connectorInfo: {}, // 连接器配置信息,
    autoChangeName: true
  }; // 表单
  private outputFields: any[] = []; // 输出信息
  private connectorConfigData: any[] = []; // 连接器配置数据
  private showDetail = false;
  private id = '';
  private type = '';

  async created() {
    await this.getTableList();
    const { properties = {}, outputFields = [] } = this.data;
    this.form = { ...this.form, ...properties };
    if (Object.keys(properties).length > 0 && !('autoChangeName' in properties)) {
      this.form.autoChangeName = false;
    }
    this.outputFields = Array.isArray(outputFields) ? [...outputFields] : [];
    this.handleSourceTableChange(this.form.sourceTable);
  }

  /* 获取表或视图列表 */
  async getTableList() {
    const { error, data, success }: any = await get(URL_GET_ALL_TABLE);
    if (success) {
      this.tableList = Array.isArray(data) ? data : [];
      return;
    }
    this.$tip.error(error);
  }

  /* 请选择表数据变化 */
  handleSourceTableChange(tableName: string) {
    const item: any = this.tableList.find(({ name }: any) => name === tableName);
    if (item) {
      const {
        id: sourceTableId,
        type: sourceTableType,
        properties = {},
        connectorId,
        fields
      } = item;
      this.getTableField(fields, properties.resType);
      this.getConnectorInfo(connectorId);
      this.form = { ...this.form, sourceTableId, sourceTableType, ...properties };
    } else {
      tableName && this.$tip.warning('当前选中表或视图不存在');
    }
  }

  /* 获取表或视图对应的字段 */
  getTableField(data: any[] = [], sourceResType = '') {
    const fields = Array.isArray(data) ? data : [];
    const { sourceTable: name, sourceTableType: type } = this.form;
    const { nodeId } = this.data;
    this.outputFields = [{ name, type, fields, outputable: true, nodeId, sourceResType }];
    if (fields.length < 1) {
      this.$tip.warning('当前选中表或视图无输出字段');
    }
  }

  /* 获取连接器数据 */
  async getConnectorInfo(connectorId: string) {
    if (!connectorId) {
      this.connectorConfigData = [];
      return;
    }
    try {
      const { error, data, success }: any = await get(URL_GET_CONNECTOR_INFO, {
        connectorId,
        propertyType: 'SOURCE'
      });
      if (success) {
        const newData = JSON.parse(data);
        this.connectorConfigData = Array.isArray(newData) ? newData : [];
        return;
      }
      this.connectorConfigData = [];
      this.$tip.error(error);
    } catch (e) {
      this.connectorConfigData = [];
    }
  }

  /* 连接器配置信息变化 */
  handleConnectorChange(data: any) {
    this.form.connectorInfo = { ...data };
  }

  /* 前往表或视图详情 */
  goTableDetail() {
    const { sourceTableType: type, sourceTableId: id, sourceTable } = this.form;
    const hasData = this.tableList.some((el: any) => el.id === id);
    if (!hasData) {
      this.$tip.error(`表或视图${sourceTable}不存在，无法查看详情！`);
      return;
    }
    if (!type) {
      this.$tip.error(`表或视图${sourceTable}数据不完整，无法查看详情！`);
      return;
    }
    this.showDetail = true;
    this.id = id;
    this.type = type;
  }

  /* 确定 保存 */
  async submit() {
    await this.formRef.validate();
    if (this.connectorConfigData.length > 0) {
      await this.connector.validate();
    }
    this.closeDialog(true);
  }

  /* 关闭  */
  @Emit('close')
  private closeDialog(needUpdate = false) {
    if (needUpdate) {
      const properties = cloneDeep(this.form);
      const { data, outputFields } = this;
      const jobNode = cloneDeep(data);
      jobNode.properties = { ...properties };
      jobNode.outputFields = cloneDeep(outputFields);
      jobNode.nodeName = this.handleNodeName();
      this.reset();
      return { needUpdate, jobNode };
    }
    this.reset();
  }

  reset() {
    this.display = false;
    this.formRef.resetFields();
  }
  handleNodeName() {
    const index = this.data.nodeName.lastIndexOf('_');
    if (!this.form.autoChangeName || index < 0) return this.data.nodeName;
    return `${this.form.sourceTable}${this.data.nodeName.substring(index)}`;
  }

  getTagLabel(type: string) {
    if (type === 'TABLE') return '表';
    if (type === 'VIEW') return '视图';
    return '未知';
  }
}
</script>
<style lang="scss" scoped>
.option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  &-tag {
    display: inline-block;
    margin-right: 8px;
    width: 36px;
    height: 18px;
    line-height: 18px;
    font-size: 12px;
    border-radius: 4px;
    background: #377cff;
    color: #fff;
    text-align: center;
    &--TABLE {
      background: #0098fd;
    }
    &--VIEW {
      background: #9f55d5;
    }
  }
}
::v-deep .el-dialog__header {
  padding-left: 16px;
  padding-right: 16px;
  border-bottom: solid 1px #e4e7ed;
  &::before {
    content: ' ';
    position: relative;
    left: 0;
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 4px;
    border: 3px solid #ff9c00;
    background: #fff;
    border-radius: 50%;
    box-sizing: content-box;
  }
}
::v-deep .el-dialog__footer {
  padding: 13px 20px;
  border-top: solid 1px #e4e7ed;
}
.sql-input {
  &-container {
    ::v-deep .el {
      &-select {
        width: 100%;
      }

      &-table {
        td,
        th {
          padding: 4px 0;
        }
      }
    }
  }

  &-selected {
    text-align: left;

    span {
      color: #2440b3;

      &:hover {
        cursor: pointer;
        color: #315efb;
        text-decoration: underline;
      }
    }
  }
}
</style>
