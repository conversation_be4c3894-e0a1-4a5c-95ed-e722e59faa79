import CryptoJS from 'crypto-js';
import dayjs from 'dayjs';
// uuid function
function S4() {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
}

// id不能以纯数字开头，不能含有$符
export function guid() {
  const uuid = 'n' + S4() + S4() + S4() + S4() + S4() + S4() + S4() + S4();
  return uuid.substr(0, uuid.length - 1);
}

function getKey() {
  const strArr = [
    '＼u0031＼u0063＼u0062＼u0063',
    '＼u0063＼u0037＼u0033＼u0065',
    '＼u0034＼u0035＼u0065＼u0036',
    '＼u0031＼u0037＼u0061＼u0030'
  ];
  return strArr.map((s) => unescape(s.replace(/＼u/g, '%u'))).join('');
}
// 加密
export function Encrypt(word) {
  let encrypted;
  const key = CryptoJS.enc.Utf8.parse(getKey());
  try {
    const srcs = CryptoJS.enc.Utf8.parse(word);
    encrypted = CryptoJS.AES.encrypt(srcs, key, {
      // iv:iv,
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
  } catch (error) {
    console.log(error);
  }
  return encrypted.toString();
}

// 解密
export function Decrypt(word) {
  const key = CryptoJS.enc.Utf8.parse(getKey());
  let decryptedStr;
  try {
    const decrypt = CryptoJS.AES.decrypt(word, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    // console.log(decrypt);
    decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    // et resData=decryptResult.toString(CryptoJS.enc.Utf8).toString();
  } catch (error) {
    console.log(error);
  }
  return decryptedStr.toString();
}

// 判断是不是FormData
export function isFormData(val) {
  return typeof FormData !== 'undefined' && val instanceof FormData;
}

// 获取url去除参数的部分
export function getOrigin(url) {
  const index = url.indexOf('?');
  if (index > -1) {
    return url.slice(0, index);
  } else {
    return url;
  }
}

export function canParse(str) {
  if (typeof str !== 'string') {
    return false;
  }
  if (str.includes('"') || str === '{}' || str === '[]') {
    return true;
  }
}

export function download(data: any, fileName?: string, type?: string) {
  const blob = type ? new Blob([data.blob], { type: type }) : new Blob([data.blob]);
  const objectUrl = URL.createObjectURL(blob);
  //  创建a标签模拟下载
  let a: any = document.createElement('a');
  a.style.display = 'none';
  // 处理文件名
  a.download = fileName ? fileName : data.fileName;
  a.href = objectUrl;
  a.click();
  a = null;
}
export const isFlinkSql = (str: string) => {
  return typeof str === 'string' ? str.includes('_SQL') : false;
};

export const isFlinkSqlFlow = ({ content }: any) => {
  if (content) {
    const { nodes } = content;
    if (Array.isArray(nodes)) {
      const [first] = nodes;
      if (first && first.type) {
        return isFlinkSql(first.type);
      }
    }
  }
  return;
};

export const dateFormat = (dateStr: any, formater?: string) => {
  if (formater) {
    return dayjs(dateStr).format(formater);
  } else {
    return dateStr === undefined ? '' : dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss');
  }
};
