<template>
  <pro-page title="日志管理" :fixed-header="false">
    <div slot="operation" class="operate-box">
      <el-date-picker
        v-model="dateTime"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="timeChange"
      />
      <bs-search
        v-model="searchObj.search"
        placeholder="请输入内容"
        style="width: 210px; margin-left: 10px"
        @input="fetchList"
      />
      <el-button
        v-if="hasFeatureAuthority('PA.MONITOR.LOG.EXPORT')"
        type="primary"
        class="default-btn"
        @click="exportLog"
      >
        导出
      </el-button>
    </div>
    <bs-table
      v-loading="tableLoading"
      :height="'calc(100vh - 290px)'"
      :data="tableData.tableData"
      :column-data="tableData.columnData"
      :page-data="searchObj.pageData"
      @page-change="handleCurrentChange"
      @sort-change="handleSortChange"
      @refresh="getListData"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { URL_PORTAL_EXPORTLOG, URL_PORTAL_LISTOPERLOG } from '@/apis/commonApi';
import { cloneDeep, debounce } from 'lodash';
import moment from 'moment';
@Component
export default class Log extends PaBase {
  formLoading = false;
  dialogVisible = false;
  recordData: any = {};
  tableLoading = false;
  dateTime: any[] = [];
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: {
      operTime: 'DESC'
    }
  };
  tableData: ITableData = {
    columnData: [],
    tableData: []
  };
  fetchList: any = debounce(this.getListData, 500);

  timeChange() {
    this.getListData();
  }
  handleCurrentChange(currentPage, pageSize) {
    this.searchObj.pageData.currentPage = currentPage;
    this.searchObj.pageData.pageSize = pageSize;
    this.fetchList();
  }

  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.fetchList();
  }
  getListData() {
    this.setDefault();
    this.tableLoading = true;
    const searchObj = cloneDeep(this.searchObj);
    searchObj.search = searchObj.search.trim();
    this.doPost(
      URL_PORTAL_LISTOPERLOG +
        '?startTime=' +
        this.timeFormatter(this.dateTime[0]) +
        '&endTime=' +
        this.timeFormatter(this.dateTime[1]),
      searchObj
    ).then((resp: any) => {
      this.parseResponse(resp, () => {
        const { tableData, columnData, pageData } = resp.data;
        columnData.forEach((el) => {
          el.value = el.prop;
        });
        tableData.forEach((el) => {
          el.operTime = moment(el.operTime).format('YYYY-MM-DD HH:mm:ss');
        });
        this.searchObj.pageData = pageData;
        this.tableData = {
          ...resp.data
        };
      });
      this.tableLoading = false;
    });
  }

  exportLog() {
    this.setDefault();
    this.axios({
      method: 'post',
      url:
        URL_PORTAL_EXPORTLOG +
        '?startTime=' +
        this.timeFormatter(this.dateTime[0]) +
        '&endTime=' +
        this.timeFormatter(this.dateTime[1]),
      data: this.searchObj,
      responseType: 'blob'
    })
      .then((res) => {
        res.data = res;
        const link = document.createElement('a');
        const blob = new Blob([res.data.blob], {
          type: 'application/vnd.ms-excel'
        });
        link.style.display = 'none';
        link.href = URL.createObjectURL(blob);
        link.setAttribute('download', decodeURIComponent(res.data.fileName));
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      })
      .catch(() => {
        this.$message.error('导出失败');
      });
  }

  setDefault() {
    if (this.dateTime === null || this.dateTime.length < 2) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      this.dateTime = [start, end];
    }
  }

  created() {
    this.setDefault();
    this.getListData();
  }
}
</script>

<style scoped></style>
