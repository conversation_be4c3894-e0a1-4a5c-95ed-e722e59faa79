<template>
  <pro-grid type="info">
    <!-- title -->
    <div slot="title">
      <span>{{ $t('pa.citationRelation') }}</span>
      <!-- select -->
      <bs-select v-model="type" filterable :options="relationOptions" @change="getRelationList" />
    </div>
    <!-- operation -->
    <el-button slot="operation" :loading="downloadLoading" type="primary" @click="downloadRelation">{{
      $t('pa.action.downloadRelation')
    }}</el-button>
    <!-- main -->
    <bs-table
      v-loading="loading"
      :height="height"
      :data="tableData"
      :page-data="pageData"
      :column-settings="false"
      :column-data="columnData"
      @page-change="handlePageChange"
    />
  </pro-grid>
</template>
<script lang="ts">
import { Component, Inject, Prop, Vue } from 'vue-property-decorator';
import { getDependRelation, downloadRelation } from '@/apis/serviceApi';
import { safeArray, timeFormat } from '@/utils';

@Component
export default class DependRelation extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: () => ({}) }) params!: any;
  @Inject('enableSql') enableSql;

  type = 'JOB';
  downloadLoading = false;
  loading = false;
  columnData: any[] = [];
  tableData: any[] = [];
  pageData = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 0 };

  get id() {
    return this.data.id;
  }
  get height() {
    return this.params?.height || '400px';
  }
  get relationOptions() {
    return [
      { label: this.$t('pa.process'), value: 'JOB' },
      { label: this.$t('pa.table'), value: 'TABLE' },
      this.enableSql && this.$route.params.type === 'HIVE' && { label: 'Catalog', value: 'CATALOG' }
    ].filter(Boolean);
  }

  created() {
    if (!this.$route.query.resType) return;
    this.getRelationList();
  }
  /* 获取引用关系 */
  async getRelationList() {
    try {
      this.loading = true;
      const { success, data, error } = await getDependRelation(this.id, this.type, this.pageData);
      if (!success) return this.$message.error(error);
      this.columnData = safeArray(data?.columnData).map((it) => {
        it.value = it.prop;
        return it;
      });
      this.tableData = safeArray(data?.tableData)
        .sort(this.sort('createTime'))
        .map((it) => {
          it.createTime = timeFormat(it.createTime);
          return it;
        });
      this.pageData.total = data?.pageData?.total || 0;
    } finally {
      this.loading = false;
    }
  }
  sort(key: string) {
    return (a: any, b: any) => a[key] - b[key];
  }
  /* 分页变化 */
  handlePageChange(currentPage: number, pageSize: number) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    this.getRelationList();
  }
  /* 下载引用关系 */
  async downloadRelation() {
    try {
      this.downloadLoading = true;
      await downloadRelation(this.id, this.type);
    } finally {
      this.downloadLoading = false;
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .bs-select {
  margin-left: 10px;
}
</style>
