<template>
  <el-form
    ref="formRef"
    :rules="rules"
    :model="formData"
    :disabled="disabled"
    label-position="right"
    class="main__container"
  >
    <template v-for="el in formConfig">
      <el-form-item v-if="!el.hidden" :key="el.name" :label="el.label" :prop="el.name" class="main-item">
        <!-- 类型 -->
        <div class="main-main" :class="`main-main--${el.type}`">
          <!-- select -->
          <el-select v-if="el.type === 'select'" v-model="formData[el.name]" :placeholder="el.placeholder">
            <el-option v-for="item in el.options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <!-- number -->
          <el-input-number
            v-if="el.type === 'number'"
            v-model="formData[el.name]"
            controls-position="right"
            :max="el.max"
            :min="el.min"
            :placeholder="el.placeholder"
          />
          <!-- config -->
          <div v-if="el.type === 'config'" class="main-config">
            <!-- 内容 -->
            <div class="main-config__main">
              <span>{{ $t('pa.flow.str') }}：</span>
              <el-tooltip :key="generateKey(formData[el.name].resultExpression)" v-hide effect="light" placement="top">
                <div slot="content" v-html="formData[el.name].resultExpression"></div>
                <div class="main-config__main-resultExpression" v-html="formData[el.name].resultExpression"></div>
              </el-tooltip>
            </div>
            <!-- 按钮 -->
            <el-button type="text" :disabled="false" size="mini" class="tab-bar__button" @click="$emit('config', el)">
              {{ disabled ? $t('pa.flow.viewCondition') : $t('pa.flow.config3') }}
            </el-button>
          </div>
        </div>
        <!-- 提示 -->
        <el-tooltip v-if="el.tooltip" effect="light" :content="el.tooltip" placement="bottom">
          <i class="iconfont icon-wenhao"></i>
        </el-tooltip>
      </el-form-item>
    </template>
  </el-form>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue, Watch } from 'vue-property-decorator';
import { sha256 } from './utils';
import { FormData, FormConfig } from './type';
import elForm from 'bs-ui-pro/packages/form/index.js';

@Component
export default class MainForm extends Vue {
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => [] }) fieldList!: any[];
  @PropSync('data', { default: () => ({}) }) formData!: FormData;
  @Ref('formRef') readonly form!: elForm;

  private labelWidth = '120px';
  private formConfig: FormConfig[] = [
    {
      label: this.$t('pa.flow.label7'),
      name: 'delayTime',
      type: 'number',
      min: 1,
      max: 999999,
      tooltip: this.$t('pa.flow.label8')
    },
    {
      label: this.$t('pa.flow.key5'),
      name: 'keyByModel',
      type: 'select',
      options: [],
      tooltip: this.$t('pa.flow.msg192')
    },
    {
      label: this.$t('pa.flow.msg193'),
      name: 'paTimeCharacteristic',
      type: 'select',
      options: [
        {
          label: this.$t('pa.handleTime'),
          value: 'ProcessingTime'
        },
        {
          label: this.$t('pa.flow.label10'),
          value: 'EventTime'
        }
      ]
    },
    {
      hidden: true,
      label: this.$t('pa.flow.timeField'),
      name: 'timeModel',
      type: 'select',
      options: [],
      tooltip: this.$t('pa.flow.msg194')
    },
    {
      hidden: true,
      label: this.$t('pa.flow.label11'),
      name: 'orderlessTime',
      type: 'number',
      min: 1,
      max: 999999,
      tooltip: this.$t('pa.flow.msg195')
    },
    {
      label: this.$t('pa.flow.msg196'),
      name: 'conditionA',
      type: 'config'
    },
    {
      label: this.$t('pa.flow.msg197'),
      name: 'conditionB',
      type: 'config'
    }
  ];
  private rules: any = {
    delayTime: {
      required: true,
      message: this.$t('pa.flow.msg198'),
      trigger: 'blur'
    },
    keyByModel: {
      required: true,
      message: this.$t('pa.flow.msg199'),
      trigger: 'change'
    },
    paTimeCharacteristic: {
      required: true,
      message: this.$t('pa.flow.msg200'),
      trigger: 'change'
    },
    timeModel: {
      required: false,
      message: this.$t('pa.flow.msg135'),
      trigger: 'change'
    },
    orderlessTime: {
      required: false,
      message: this.$t('pa.flow.msg201'),
      trigger: 'blur'
    }
  };

  @Watch('formData.paTimeCharacteristic', { immediate: true })
  handler() {
    const result = this.formData.paTimeCharacteristic === 'EventTime';
    this.setValue('timeModel', !result);
    this.setValue('orderlessTime', !result);
    this.setRuleValue('timeModel', result);
    this.setRuleValue('orderlessTime', result);
  }

  created() {
    this.setValue('keyByModel', this.fieldList, 'options');
    this.setValue('timeModel', this.fieldList, 'options');
  }

  generateKey(str = '') {
    return sha256(str);
  }
  setValue(name: string, value: any, key = 'hidden') {
    const index = this.formConfig.findIndex((el) => el.name === name);
    if (index > -1) {
      this.$set(this.formConfig[index], key, value);
    }
  }
  setRuleValue(name: string, value: any, key = 'required') {
    if (name in this.rules) {
      this.$set(this.rules[name], key, value);
    }
  }
  validate() {
    return this.form.validate();
  }
}
</script>
<style lang="scss" scoped>
.main {
  &__container {
    display: block;
    width: 100%;
    // width: 600px;
    ::v-deep .el-form-item {
      &__content {
        display: flex;
        align-items: center;
      }
    }
  }
  &-main {
    margin-right: 10px;
    width: calc(100% - 30px);
    > div {
      width: 100%;
    }
  }
  &-config {
    display: flex;
    align-items: center;
    width: 100%;
    &__main {
      display: flex;
      align-items: center;
      margin-right: 22px;
      width: 410px;
      font-size: 14px;
      font-weight: 400;
      color: #444444;
      &-resultExpression {
        width: calc(100% - 70px);
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    ::v-deep .el-button--mini {
      padding-top: unset !important;
      padding-bottom: unset !important;
    }
  }
}
</style>
