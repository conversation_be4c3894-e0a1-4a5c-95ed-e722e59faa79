<!-- >保存提示弹窗<-->
<template>
  <bs-dialog
    :title="title"
    :visible.sync="display"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @confirm="confirm"
  >
    <el-form v-if="isFlow" ref="formRef" :model="formData" :label-width="isEn ? '100px' : '50px'" @submit.native.prevent>
      <el-form-item :label="jobTagLabel" prop="jobTag">
        <el-input
          v-model="formData.jobTag"
          autocomplete="off"
          maxlength="30"
          show-word-limit
          :placeholder="jobTagPlaceholder"
        />
      </el-form-item>
      <el-form-item :label="commentsLabel" prop="comments">
        <el-input
          v-model="formData.comments"
          type="textarea"
          rows="5"
          :placeholder="commentsPlaceholder"
          autocomplete="off"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <div v-else>
      <p style="margin-bottom: 10px; text-align: left">{{ updatePlaceholder }}</p>
      <el-input
        v-model="comments"
        type="textarea"
        rows="5"
        :placeholder="updatePlaceholder"
        autocomplete="off"
        maxlength="100"
        show-word-limit
      />
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Ref, Vue } from 'vue-property-decorator';
import ElForm from 'bs-ui-pro/packages/form';
import { cloneDeep, debounce } from 'lodash';
import i18n from '@/i18n';

@Component
export default class SaveConfirm extends Vue {
  @Ref('formRef') readonly form!: ElForm;
  private title = '';
  private isFlow = false;
  private display = false;
  private require = false;
  private comments = '';
  private resolve: any = null;
  private reject: any = null;
  private formData = { jobTag: '', comments: '' };
  private confirm = debounce(this.handleConfirm, 1200);

  jobTagLabel = i18n.t('pa.flow.tag') as string;
  jobTagPlaceholder = i18n.t('pa.jobTag') as string;
  commentsLabel = i18n.t('pa.flow.memo') as string;
  commentsPlaceholder = i18n.t('pa.comments') as string;
  updatePlaceholder = i18n.t('pa.placeholder.updatePlaceholder') as string;

  show({ require = false, isFlow = false, title = i18n.t('pa.data.udf.detail.savePrompt') as string } = {}) {
    return new Promise((resolve, reject) => {
      this.title = title;
      this.isFlow = isFlow;
      this.require = require;
      this.display = true;
      this.resolve = resolve;
      this.reject = reject;
    });
  }
  closeDialog(isCancel = true) {
    if (isCancel) {
      this.reject && this.reject.bind(Promise)(new Error(i18n.t('pa.userCancel') as string));
    }
    this.require = false;
    this.display = false;
    this.comments = '';
    this.resolve = null;
    this.reject = null;
    this.isFlow && this.form.resetFields();
    this.isFlow = false;
  }
  handleConfirm() {
    if (this.isFlow) {
      this.resolve && this.resolve.bind(Promise)(cloneDeep(this.formData));
      this.form.resetFields();
      this.closeDialog(false);
      return;
    }
    if (!this.comments && this.require) {
      return this.$message.error(i18n.t('pa.data.udf.detail.placeholder.updataPlaceholder') as string);
    }
    this.resolve && this.resolve.bind(Promise)(this.comments);
    this.closeDialog(false);
  }
}
</script>
<style lang="scss" scoped></style>
