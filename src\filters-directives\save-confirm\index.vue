<!-- >保存提示弹窗<-->
<template>
  <bs-dialog
    :title="title"
    :visible.sync="dispaly"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @confirm="confirm"
  >
    <el-form
      v-if="isFlow"
      ref="formRef"
      :model="formData"
      :label-width="labelWidth"
      @submit.native.prevent
    >
      <el-form-item label="标签" prop="jobTag">
        <el-input
          v-model="formData.jobTag"
          autocomplete="off"
          maxlength="30"
          show-word-limit
          placeholder="请输入标签，长度不超过30字符"
        />
      </el-form-item>
      <el-form-item label="描述" prop="comments">
        <el-input
          v-model="formData.comments"
          type="textarea"
          rows="5"
          placeholder="请输入描述"
          autocomplete="off"
          maxlength="255"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <div v-else>
      <p style="margin-bottom: 10px; text-align: left">请输入更新内容</p>
      <el-input
        v-model="comments"
        type="textarea"
        rows="5"
        placeholder="请输入更新内容"
        autocomplete="off"
        maxlength="100"
        show-word-limit
      />
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Ref, Vue } from 'vue-property-decorator';
import ElForm from 'bs-ui-pro/packages/form';
import { cloneDeep, debounce } from 'lodash';
@Component
export default class SaveConfirm extends Vue {
  @Ref('formRef') readonly form!: ElForm;
  private title = '';
  private isFlow = false;
  private dispaly = false;
  private require = false;
  private comments = '';
  private reslove: any = null;
  private reject: any = null;
  private labelWidth = '50px';
  private formData = { jobTag: '', comments: '' };
  private confirm = debounce(this.handleConfirm, 1200);

  show({ require = false, isFlow = false, title = '保存提示' } = {}) {
    return new Promise((reslove, reject) => {
      this.title = title;
      this.isFlow = isFlow;
      this.require = require;
      this.dispaly = true;
      this.reslove = reslove;
      this.reject = reject;
    });
  }
  closeDialog(isCancel = true) {
    if (isCancel) {
      this.reject && this.reject.bind(Promise)(new Error('用户取消'));
    }
    this.require = false;
    this.dispaly = false;
    this.comments = '';
    this.reslove = null;
    this.reject = null;
    this.isFlow && this.form.resetFields();
    this.isFlow = false;
  }
  handleConfirm() {
    if (this.isFlow) {
      this.reslove && this.reslove.bind(Promise)(cloneDeep(this.formData));
      this.form.resetFields();
      this.closeDialog(false);
      return;
    }
    if (!this.comments && this.require) {
      return this.$message.error('请填写更新内容');
    }
    this.reslove && this.reslove.bind(Promise)(this.comments);
    this.closeDialog(false);
  }
}
</script>
<style lang="scss" scoped></style>
