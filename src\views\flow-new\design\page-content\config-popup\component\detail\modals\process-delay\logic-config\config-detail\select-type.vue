<template>
  <el-form-item :prop="prop" :rules="rules" class="select__container">
    <el-select
      v-model="config.funcArgs[index].key"
      clearable
      filterable
      size="small"
      @change="handleChange"
    >
      <el-option
        v-for="field in options"
        :key="field.name"
        :label="field.name"
        :value="field.value"
      />
    </el-select>
    <el-tooltip v-if="showTooltip" effect="light" placement="top" :content="content">
      <i class="select-icon el-icon-warning-outline"></i>
    </el-tooltip>
  </el-form-item>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';

@Component
export default class SelectType extends Vue {
  @Prop({ default: 0 }) index!: number;
  @Prop({ default: () => [] }) fieldList!: any[];
  @PropSync('data', { default: () => ({}) }) config!: any;

  private rules: any = {
    required: true,
    message: '请选择上游组件字段',
    trigger: 'change'
  };

  get prop() {
    return `funcArgs[${this.index}].key`;
  }
  get options() {
    return this.fieldList;
  }
  get key() {
    return this.config.funcArgs[this.index].key;
  }
  get showTooltip() {
    const result = this.fieldList.map(({ value }) => value);
    return this.key && !result.includes(this.key);
  }
  get content() {
    return `${this.key || '字段'}已经被删除`;
  }

  handleChange(value: string) {
    if (this.config.funcType !== 'DEFAULT') return;
    const item = this.options.find((el) => el.value === value);
    if (item && item.type) {
      this.$set(this.config.funcArgs[this.index], 'type', item.type);
    }
  }
}
</script>
<style lang="scss" scoped>
.select {
  &-icon {
    display: inline-block;
    margin-left: 8px;
    width: 16px;
    height: 16px;
    font-weight: bolder;
    color: #ff5353;
  }
}
</style>
