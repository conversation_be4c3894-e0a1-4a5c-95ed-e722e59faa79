# PIPEACE 开发文档

- [PIPEACE 开发文档](#pipeace开发文档)
  - [项目结构-src](#项目结构-src)
  - [技术栈](#技术栈)
  - [协同规范](#协同规范)
    - [1、Vue（参照 Vue 官方风格指南）](#1vue参照-vue-官方风格指南)
    - [2、语言 - TypeScript](#2语言---typescript)
      - [为什么选用 Typescript](#为什么选用-typescript)
    - [3、代码规范化插件-TSLint+Prettier](#3代码规范化插件-tslintprettier)
    - [4、项目组织结构(上边已给出项目详细结构)](#4项目组织结构上边已给出项目详细结构)
      - [如何新增页面](#如何新增页面)
      - [如何新增公共组件/插件](#如何新增公共组件插件)
      - [如何增加或是更改全局样式](#如何增加或是更改全局样式)
      - [如何新增后端接口定义](#如何新增后端接口定义)
      - [如何新增 Router](#如何新增-router)
    - [5、CSS 规范](#5css-规范)
      - [采用 BEM 来规范 CSS 样式命名](#采用-bem-来规范-css-样式命名)
      - [规范 style 声明顺序](#规范-style-声明顺序)
    - [6、其他特殊配置](#6其他特殊配置)
      - [页面缓存配置](#页面缓存配置)
      - [对多个相同路由组件分别进行缓存](#对多个相同路由组件分别进行缓存)
  - [开发](#开发)
    - [启动](#启动)
  - [生产/部署](#生产部署)
    - [生产](#生产)
      - [打包](#打包)
    - [部署](#部署)
  - [重要源码注解](#重要源码注解)

## 项目结构-src

```text
├─assets                                      // 静态资源（字体图标、图片、语言包等...）
├─common                                      // 整个项目公用部分（组件、插件、枚举、过滤器等...）
|  ├─components                               // 公用组件
│  │  └─base-table.vue                        // 公用表格组件
│  ├─filter.ts                                // 自定义指令文件
|  ├─utils.ts                                 // 业务无关的工具类函数文件
│  └─enums                                    // 枚举类型
├─config                                      // 整个项目公用的配置
│  └─index.ts                                 // 左侧菜单栏、流程节点的配置等
├─store                                       // VueX部分
│  ├─index.ts                                 // 全局的state和getters定义
│  ├─actions                                  // 全局的actions方法定义
│  └─mutations                                // 全局的mutations方法定义
├─style                                       // 公用样式
│  ├─other-platform.scss                      // 流立方和assets的定制样式
│  └─style.scss                               // PA项目自身的公用样式
├─router                                      // 公用样式
│  ├─flow.ts                                  // 流程模块的路由信息
│  └─index.ts                                 // 路由整合到处、全局路由钩子、VueRouter初始化
└─views                                       // 业务模块
```

## 技术栈

- [TypeScript](https://www.tslang.cn/docs/handbook/basic-types.html)
- [Vue.js-一套用于构建用户界面的渐进式框架](https://cn.vuejs.org/)
- [Rx.js-处理事件的库](https://cn.rx.js.org/)
- [BSUI-基于 element-ui 改版的组件库](http://************:7071/#/zh-CN/component/installation)
- [BSView-基于 iview 改版的组件库](http://***********:8081/#/modal)

## 协同规范

### 1、Vue（[参照 Vue 官方风格指南](https://cn.vuejs.org/v2/style-guide/)）

- 规范组件顺序

  ```js
  @Component({
    // 业务组件最好在此处配置name
    name: 'Flow',
    components: {
      'flow-list': () => import('./flow-list/index.vue'),
      'element-list': () => import('./element-list/index.vue')
    }
  })
  export default class Flow extends Vue {
      @Prop({ default: false }) visible!: boolean;
      name: boolean = true;
      computed: {},
      watch: {},
      // 生命周期钩子，按调用顺序编写
      created() {},
      beforeCreate () {},
      ...,
      destroyed () {},
      // methods方法
      getList() {},
      // 使用render函数时，置于末尾
      render () {}
    }
  ```

### 2、语言 - TypeScript

#### 为什么选用 Typescript

- 提高后期代码的可读性、可以在编译阶段就发现大部分错误，这总比在运行时候出错好。
- 类型系统实际上是最好的文档，大部分的函数看看类 型的定义就可以知道如何使用了
- ...

### 3、代码规范化插件-TSLint+Prettier

- 项目已自带相关配置文件

### 4、项目组织结构(上边已给出项目详细结构)

> **新增结构建议**

#### 如何新增页面

- 所有的页面都放 views 对应的模块目录里面，文件夹以功能模块语义化命名，多个单词用`-`连接）
- 比如在`monitor`下新增文件夹`warnning-record`

```text
  monitor                           // 监控管理模块
     |─components                    // 模块内公用业务模块
     |   ├─edit-modal.vue            // 单文件组件（对于组件逻辑较为简单代码量较少的情况）
     |   └─edit-modal                // 复合文件组件
     |      ├─edit-modal.vue         // 将样式、dom以及js逻辑进项拆分
     |      ├─edit-modal.scss
     |      └─edit-modal.ts
     └─log                           // 日志子模块
        ├─log.vue
        ├─log.scss
        ├─log.interface.scss         // 模块内部使用的ts接口文件
        └─log.ts
```

#### 如何新增公共组件/插件

- 在 src\common 中添加全局公共组件和公共自定义方法

#### 如何增加或是更改全局样式

- 全局样式在 styles\style.sass 中修改
- 少量局部样式一般放在 vue 单文件中，样式内容较多时建议在组件文件夹下创建单独样式文件，在.vue 单文件中引入

#### 如何新增后端接口定义

- 在[graph-analysis | management]\common\api 中添加后端接口定义
- 接口风格建议遵循 RESTful 风格（具体可与后端协商）
- 案例：

  | 操作         | >                   | 传统风格 | >          | RESTful 风格 |
  | ------------ | ------------------- | -------- | ---------- | ------------ |
  |              | URL                 | method   | URL        | method       |
  | 增加         | /addNode            | POST     | /node      | POST         |
  | 修改         | /updateNode?id=uuid | POST     | /node/uuid | PUT          |
  | 删除         | /deleteNode?id=uuid | GET      | /node/uuid | DELETE       |
  | 获取         | /getNode?id=uuid    | GET      | /node/uuid | GET          |
  | 获取全部节点 | /getAllNodes        | GET      | /node      | GET          |

#### 如何新增 Router

- 在 router 中找到对应一级模块文件，在对应文件中增加自己的模块信息。
- 路由配置默认模块化，如果很多的页面或模块，常出现路由嵌套的情况，导致 router.ts 文件庞大，此时，建议以路由层级进行模块拆分。

> 以`monitor`模块为例,展示路由配置结构。

```js
const router = [
  {
    // 一级路由模块信息
    path: '/monitor',
    name: 'monitor',
    // 二级路由尽量不有子路由。因为跳转到子路由后会触发顶部tab增加一个tab标签。
    // 路由在入口处做了扁平化处理，主要是为了tab标签做处理。所以尽量避免在页面内部嵌套一个子路由标签。
    // 如果仍是需要嵌套子路由，可以在meta中配置noJump为true，来阻止激活子路由时新开tab标签。
    children: [
      {
        path: 'service',
        name: 'monitorService'
      },
      {
        path: 'warningRule',
        name: 'monitorWarningRule'
      }
    ]
  }
];
```

> meta 字段配置

| 字段名称 | 字段说明                                                      | 值      |
| -------- | ------------------------------------------------------------- | ------- |
| access   | 路由权限                                                      | string  |
| title    | 该模块对应显示的 tab 标签名称，若为动态配置模式，参照以下代码 | string  |
| noJump   | 配置为 true，在路由激活时不会打开新的 tab 标签                | boolean |

> 动态配置路由 title

```js
// 在beforeEnter路由钩子中进行配置，确保在进入路由之前完成title的配置。
beforeEnter: (to, from, next) => {
  to.meta.title = to.query.title;
  next();
},
```

### 5、CSS 规范

#### 采用 [BEM](http://getbem.com/introduction/) 来规范 CSS 样式命名

- BEM 示例：比如分页组件：/app/components/page-btn/，那么该组件模块就名为 page-btn，组件内部的元素命名都必须加上模块名，用双下划线来明确区分模块名和元素名，比如：

  ```html
  <div class="page-btn">
    <button type="button" class="page-btn__prev">上一页</button>
    <!-- ... -->
    <button type="button" class="page-btn__next">下一页</button>
  </div>
  ```

#### 规范 style 声明顺序

- 相关的属性声明应当归为一组，并按照下面的顺序排列。

  1. position-位置
  2. box model-盒模型
  3. Typographic-排版
  4. Visual-视觉

  - 案例：

    ```css
    .graph-analysis-main {
      //Position
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      z-index: 1000;

      //Box-model
      diplay: block;
      float: right;
      width: 100px;
      height: 100px;

      //Typography
      font: normal 13px '微软雅黑', '楷体';
      line-height: 1.5;
      color: #666666;
      text-align: center;

      //Visual
      background-color: red;
      border: 1px solid #ddd;
      border-radius: 3px;
    }
    ```

### 6、其他特殊配置

#### 页面缓存配置

在`src\common\components\tabs-nav\tabs-nav.vue`中进行配置，每个页面默认会进行缓存。

```html
<!-- exclude: 不需要缓存的组件名称 -->
<bs-keep-alive :exclude="['pkgList', 'MonitorFlow', 'Flow', 'assets-func']">
  <router-view v-if="needUpdate"></router-view>
</bs-keep-alive>
```

#### 对多个相同路由组件分别进行缓存

> 比如一个相同的详情页面，打开多个详情页面时，需要分别进行缓存，而默认的逻辑是相同的组件只会缓存一次，所以需要配置，进行特殊处理。
> 在`src\common\components\bs-keep-alive.ts`中进行配置。

```js
// 针对路由名称一致，但是fullPath不一样的情况
const names = [
  'templateDetail',
  'scriptDetail',
  'conScriptDetail',
  'serverDd',
  'func-detail-view',
  'ServiceCustom',
  'FlowDetail',
  'ServiceCustom'
];
```

## 开发

### 启动

```cmd
npm run serve
```

## 生产/部署

### 生产

#### 打包

```cmd
npm run build
```

### 部署

- Jenkins 配置文件

- Maven 配置文件

## 重要源码注解
