<template>
  <div
    ref="flowContentRef"
    v-loading="loading"
    class="node-content"
    :class="{ 'node-content__batch': isBatch }"
  >
    <div
      v-for="item in data"
      :key="item.id"
      :class="['node-item', item.id === $route.query.flowId ? 'node-item--active' : '']"
      @contextmenu.prevent="openContextMenu(item, $event)"
    >
      <div class="node-left" :style="{ cursor: isBatch ? '' : 'pointer' }">
        <el-checkbox v-if="isBatch" v-model="item.checked" @change="getSelected($event, item)" />
        <span class="node-type"> {{ item.jobType === 'PROCESSFLOW' ? 'DS' : 'SQL' }}</span>
        <el-tooltip v-hide effect="light" placement="right" :content="item.jobName">
          <span class="node-name" @click="getCurFlow(item)">{{ item.jobName }}</span>
        </el-tooltip>
      </div>
      <el-tooltip
        :open-delay="500"
        effect="light"
        placement="right"
        :content="handleStatus(item.jobStatus)"
      >
        <span :class="generateClass(item)" class="node-status"></span>
      </el-tooltip>
    </div>
    <!-- 右键菜单 -->
    <bs-popover-menu
      v-if="showContextMenu"
      :visible="showContextMenu"
      :list="list"
      :wrapper="false"
      :position="position"
      @click="cilckTheItem"
      @hideMenu="hideMenu"
    />
    <bs-empty v-if="!data.length && !loading" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Ref } from 'vue-property-decorator';
import { hasPermission } from '@/utils';
@Component({
  name: 'NodeList',
  directives: {
    hide: {
      inserted(el, bind, vnode) {
        if (el.clientWidth === el.scrollWidth) {
          (vnode as any).componentInstance.$destroy();
        }
      }
    }
  }
})
export default class NodeList extends Vue {
  @Prop() data!: any;
  @Prop({ default: false }) isBatch!: boolean;
  @Prop({ default: true }) loading!: boolean;
  @Prop() flowId!: string;
  @Ref('flowContentRef') readonly flowContentRef!: any;
  showContextMenu = false;
  position = [0, 0];

  //右键菜单内容
  get list(): any {
    return [
      { label: '流程配置', value: 'config', access: 'PA.FLOW.FLOW_MGR.VIEW' },
      { label: '复制', value: 'copy', access: 'PA.FLOW.FLOW_MGR.COPY' },
      { label: '移动', value: 'move', access: 'PA.FLOW.FLOW_MGR.MOVE' },
      { label: '删除', value: 'delete', access: 'PA.FLOW.FLOW_MGR.DELETE' }
    ].filter((el) => hasPermission(el.access));
  }

  @Watch('data')
  handleData() {
    if (this.isBatch) return;
    // 找到当前流程的索引，计算其距离容器顶部的高度，修改滚动条位置
    this.$nextTick(() => {
      const curFlowHeight = this.data.findIndex((el: any) => el.id === this.flowId) * 40;
      this.flowContentRef.scrollTop = curFlowHeight - this.flowContentRef.clientHeight / 2;
    });
  }

  getSelected(isChecked: boolean, data: any) {
    this.$emit('change', isChecked, data);
  }

  // 获取当前流程
  getCurFlow(data) {
    this.$emit('click', data);
  }

  // 右击打开菜单栏
  openContextMenu(item: any, $event) {
    if (this.isBatch) return; //批量操作不可点击切换流程
    if (item.id === this.flowId) {
      this.position = [$event.clientX, $event.clientY];
      this.showContextMenu = true;
    }
  }

  //隐藏右击菜单栏
  hideMenu(val) {
    this.showContextMenu = val;
  }

  // 右键菜单的回调
  cilckTheItem(value) {
    this.showContextMenu = false;
    this.$emit('rightClick', value);
  }

  handleStatus(jobStatus: string) {
    if (jobStatus) {
      const mapping = {
        PROD: '已上线',
        PUB: '已发布',
        DEV: '开发'
      };
      return mapping[jobStatus];
    }
    return;
  }

  // 根据流程状态展示不同颜色圆点
  generateClass({ jobStatus }) {
    if (jobStatus) {
      const mapping = {
        PROD: 'node-status--green',
        PUB: 'node-status--blue'
      };
      return mapping[jobStatus] || 'node-status--grey';
    }
    return 'node-status--grey';
  }
}
</script>

<style lang="scss" scoped>
.node-content {
  width: 300px;
  height: calc(100vh - 246px);
  overflow: auto;
  &__batch {
    height: calc(100vh - 280px);
  }
  .node-item {
    position: relative;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    &--active {
      background: $--bs-color-background-base;
    }
    .node-left {
      display: flex;
      width: 100%;
      padding-right: 12px;
      .node-type {
        display: inline-block;
        width: 36px;
        height: 18px;
        margin: 0 8px;
        padding: 0 6px;
        font-size: 12px;
        border-radius: 4px;
        background-color: $--bs-color-primary;
        color: #fff;
      }
      .node-name {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .node-status {
      position: absolute;
      right: 20px;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      cursor: pointer;

      &--green {
        background: $--bs-color-green;
      }

      &--blue {
        background: $--bs-color-primary;
      }

      &--grey {
        background: $--bs-color-text-placeholder;
      }
    }
  }
}
</style>
