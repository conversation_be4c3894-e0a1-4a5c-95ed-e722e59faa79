import i18n from '@/i18n';

export const FIELD_COLUMN_DATA = [
  {
    value: 'name',
    label: i18n.t('pa.flow.fieldName'),
    width: '142px',
    showOverflowTooltip: false
  },
  {
    value: 'cnName',
    label: i18n.t('pa.flow.chineseName'),
    width: '142px'
  },
  {
    value: 'type',
    label: i18n.t('pa.flow.fieldType'),
    width: '142px'
  }
];
export const CONNECTOR_COLUMN_DATA = [
  {
    value: 'attribute',
    label: i18n.t('pa.flow.connectorAttr')
  },
  {
    value: 'value',
    label: i18n.t('pa.flow.connectorVal')
  }
];
export const GetRightArray = (data: any) => (Array.isArray(data) ? data : []);

export const GetFieldInfoUrl = (type = '', id = '', name = '') => {
  let resType = type.toLowerCase();
  if (resType !== 'hive' && resType !== 'hudi') resType = 'jdbc';
  return resType && id && name ? `/rs/pa/res/detail/${resType}/column?id=${id}&tableName=${encodeURIComponent(name)}` : '';
};
export const GetFieldTableData = (data = []) => {
  const newData = GetRightArray(data);
  return newData.map((el: any) => {
    return {
      name: el.columnName || el.fieldName || '',
      cnName: el.fieldNameCn || '-',
      type: el.typeName || el.fieldType || '',
      isAdvField: !!el.checkIfAdvancedField
    };
  });
};

export const ParseArrayStr = (raw = '[]') => {
  try {
    return [JSON.parse(raw)].flat(2);
  } catch {
    return [];
  }
};

export const GetaConnectorTableData = (data: any[] = []) => {
  return data
    .filter(Boolean)
    .map((el) => {
      return Object.entries(el).map(([attribute, value]) => ({ attribute, value }));
    })
    .flat(2);
};

export const GetTableInfoData = (data: any = {}) => {
  return {
    tableName: data?.tableName || '', // 表名
    tableNameCn: data?.tableNameCn || '', // 中文名
    businessExplain: data?.businessExplain || '', // 业务口径
    serviceType: data?.serviceType || '', // 服务类型
    serviceName: data?.serviceName || '', // 服务
    serviceAddress: data?.serviceAddress || '', // 服务地址
    serviceLabel: data?.serviceLabel || '', // 数据表、topic、''
    serviceValue: data?.serviceValue || '', // label 对应的值
    connectorName: data?.connectorName || '', //  连接器名称
    baseConnector: GetaConnectorTableData([data.baseConnector]), // 基础连接器
    advancedConnector: GetaConnectorTableData(ParseArrayStr(data.advancedConnector)), // 高级连接器
    fieldInfo: data?.fieldInfo || [] // 字段信息
  };
};
export { default as SHA256 } from 'sha256';
export const GetAllParentAttr = (e, props = 'className') => {
  let result = '';
  let target = e.target;
  while (target.parentNode) {
    result += target[props] + ' ';
    target = target.parentNode;
  }
  return result;
};
