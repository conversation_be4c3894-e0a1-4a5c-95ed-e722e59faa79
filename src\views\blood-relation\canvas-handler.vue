<template>
  <div class="bs-dag-canvas-handler">
    <i class="bs-icon-suoxiao" @click="handleOutClick"></i>
    <el-dropdown placement="bottom" @command="handleCommand">
      <span class="bs-dag-canvas-handler__dropdown">{{ zoomText }}</span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="item in options" :key="item.value" :command="item.value">{{ item.label }}</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <i class="bs-icon-fangda" @click="handleInClick"></i>
    <el-tooltip effect="light" :content="$t('pa.blood.size')" placement="top">
      <i class="bs-icon-a-11" @click="$emit('canvas-handler', 100)"></i>
    </el-tooltip>
    <el-tooltip effect="light" :content="$t('pa.blood.center')" placement="top">
      <i class="iconfont icon-center" @click="$emit('canvas-handler', 'center')"></i>
    </el-tooltip>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';

@Component
export default class CanvasHandler extends Vue {
  @Prop({ type: Number, default: 1 }) zoom!: number;
  @Prop({ type: Number, default: 0.2 }) zoomStep!: number;
  zoomValue = 100;
  options = [
    {
      value: 'fit',
      label: this.$t('pa.blood.fit')
    },
    {
      value: 200,
      label: '200%'
    },
    {
      value: 150,
      label: '150%'
    },
    {
      value: 100,
      label: '100%'
    },
    {
      value: 50,
      label: '50%'
    },
    {
      value: 25,
      label: '25%'
    }
  ];
  get zoomText() {
    const options = this.options.find((item) => item.value === this.zoomValue);
    return options ? options.label : typeof this.zoomValue === 'number' ? `${this.zoomValue}%` : '100%';
  }
  @Watch('zoom')
  handleZoomChange(val) {
    const zoomValue = Math.round(val * 100);
    if (this.zoomValue !== zoomValue) {
      this.zoomValue = zoomValue;
    }
  }
  computeZoom(type) {
    const { zoomValue, zoomStep } = this;
    const zoom = typeof zoomValue === 'number' ? zoomValue : Math.round((this.zoom || 1) * 100);
    return type === 'out' ? zoom - zoomStep * 100 : zoom + zoomStep * 100;
  }
  // 画布缩小
  handleOutClick() {
    if (this.zoomValue === 0) return '';
    this.zoomValue = this.computeZoom('out');
    this.$emit('canvas-handler', 'out');
  }
  // 画布放大
  handleInClick() {
    this.zoomValue = this.computeZoom('in');
    this.$emit('canvas-handler', 'in');
  }
  handleCommand(type) {
    this.zoomValue = type;
    this.$emit('canvas-handler', type);
  }
}
</script>
<style scoped>
.bs-dag-canvas-handler {
  width: 196px;
}
.bs-dag-canvas-handler > i.icon-center {
  margin-left: 20px;
  font-size: 16px;
}
</style>
