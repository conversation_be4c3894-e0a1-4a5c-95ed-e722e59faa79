<template>
  <bs-dialog :title="$t('pa.flow.title2')" :visible="visible" size="small" @close="closeDialog" @confirm="handleConfirm">
    <el-form ref="form" :model="formData" :rules="formRules" label-width="100px">
      <el-form-item :label="$t('pa.flow.codeName')" prop="name">
        <el-input v-model="formData.name" maxlength="30" :placeholder="$t('pa.flow.placeholder5')" />
      </el-form-item>
      <el-form-item :label="$t('pa.flow.codeKey')" prop="shortCode">
        <el-input v-model="formData.shortCode" maxlength="30" :placeholder="$t('pa.flow.placeholder16')" />
      </el-form-item>
      <el-form-item :label="$t('pa.flow.remark')" prop="memo">
        <el-input v-model="formData.memo" maxlength="500" :placeholder="$t('pa.flow.placeholder0')" type="textarea" />
      </el-form-item>
    </el-form>
  </bs-dialog>
</template>
<script lang="ts">
import Form from 'bs-ui-pro/lib/form';
import { Component, Prop, Vue, Ref } from 'vue-property-decorator';
import { addSql } from '@/apis/sqlApi';
import i18n from '@/i18n';
const validateSpace = (rule, value, callback) => {
  if (/^\s+$/.test(value)) {
    return callback(new Error(i18n.t('pa.flow.msg75') as string));
  }
  callback();
};
@Component
export default class GenerateCodeDialog extends Vue {
  @Prop() visible!: boolean;
  @Prop() code!: string;
  @Ref('form') form!: Form;
  formData = { name: '', shortCode: '', memo: '' };
  formRules = {
    name: [
      { required: true, message: this.$t('pa.flow.placeholder7'), trigger: 'change' },
      { validator: validateSpace, trigger: 'change' }
    ],
    shortCode: [
      { required: true, message: this.$t('pa.flow.placeholder16'), trigger: 'change' },
      { validator: validateSpace, trigger: 'change' }
    ]
  };
  closeDialog() {
    this.$emit('update:visible', false);
  }
  handleConfirm() {
    this.form.validate(async (valid) => {
      if (valid) {
        const params = {
          body: this.code,
          type: 0, // 个人
          ...this.formData
        };
        const { success, msg, error } = await addSql(params);
        if (success) {
          this.$message.success(msg);
          this.$emit('confirm');
          this.closeDialog();
        } else {
          this.$message.error(error);
        }
      }
    });
  }
}
</script>
<style lang="scss" scoped></style>
