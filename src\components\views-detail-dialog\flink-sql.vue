<!-- >保存提示弹窗<-->
<template>
  <bs-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :before-close="closeDialog"
    :modal-append-to-body="true"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="40%"
  >
    <div class="code-content">
      <flink-sql-editor
        :code="code"
        :origin-code="code"
        :height="editorHeight"
        :options="editorOptions"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="closeDialog">确定</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Vue, Prop, PropSync } from 'vue-property-decorator';
import FlinkSqlEditor from '@/components/flink-sql-editor/index.vue';
@Component({
  components: {
    FlinkSqlEditor
  }
})
export default class SourceCode extends Vue {
  @Prop({
    default: () => {
      return {};
    }
  })
  code!: any;
  @Prop({ default: '' }) title;
  @PropSync('visible') dialogVisible!: boolean;
  private editorHeight = '300px';
  private editorOptions = {
    mode: 'text/flink-sql',
    indentUnit: 2,
    smartIndent: true,
    lineNumbers: true,
    showCursorWhenSelecting: true,
    lineWiseCopyCut: true,
    autofocus: true,
    lineWrapping: true,
    foldGutter: true,
    readOnly: true
  };
  // 编辑器配置
  closeDialog() {
    this.dialogVisible = false;
  }
}
</script>
<style lang="scss" scoped>
.code-content {
  margin-top: -1px;
  margin-right: -1px;
}
</style>
