<template>
  <div class="hive-table">
    <bs-search
      v-model="keyword"
      class="hive-table__search"
      clearable
      :placeholder="$t('pa.flow.msg315')"
      @search="searchChange"
    />
    <el-table border stripe :data="tableData" max-height="300">
      <el-table-column v-for="item in columnData" :key="item.prop" :label="item.label" align="center">
        <template slot-scope="{ row }">
          <el-select
            v-if="item.isSelect"
            :key="selectKey"
            v-model="row.inputFieldName"
            clearable
            size="mini"
            filterable
            :disabled="disabled"
            @change="handlePreOutputChange(row)"
          >
            <el-option v-for="el in preOutput" :key="el.name" :label="el.name" :value="el.name" />
          </el-select>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination"
      background
      :current-page.sync="pageData.currentPage"
      :page-size="pageData.pageSize"
      :page-sizes="[10, 20, 40, 80, 100]"
      :total="pageData.total"
      layout="slot, total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script lang="ts">
import { cloneDeep } from 'lodash';
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
interface TableFieldItem {
  fieldName: string;
  fieldType: string;
  inputFieldName?: string;
  inputFieldType?: string;
  [key: string]: any;
}

interface PreOutputItem {
  name: string;
  type: string;
  [key: string]: any;
}
@Component
export default class HiveTable extends Vue {
  // 表字段信息列表
  @Prop({ default: () => [] }) data!: TableFieldItem[];
  // 上游节点输出字段
  @Prop({ default: () => [] }) preOutput!: PreOutputItem[];
  // 后端保存值
  @Prop({ default: () => [] }) fieldList!: TableFieldItem[];
  @Prop({ default: false }) disabled!: boolean;
  //   表字段信息列表
  hiveFieldInfoData: TableFieldItem[] = [];
  fieldListCopy: TableFieldItem[] = [];
  initData: TableFieldItem[] = [];
  selectKey = 0;
  // 表列名
  columnData = [
    { label: this.$t('pa.flow.key6'), prop: 'fieldName' },
    { label: this.$t('pa.flow.key7'), prop: 'fieldType' },
    { label: this.$t('pa.flow.key8'), prop: 'inputFieldType' },
    {
      label: this.$t('pa.flow.key9'),
      prop: 'inputFieldName',
      isSelect: true
    }
  ];
  keyword = '';
  // 分页数据
  pageData = {
    currentPage: 1,
    pageSize: 10,
    total: 0
  };
  handleSizeChange(val) {
    this.pageData.pageSize = val;
  }
  handleCurrentChange(val) {
    this.pageData.currentPage = val;
  }
  get tableData() {
    const { pageSize, currentPage } = this.pageData;
    return this.initData.slice(pageSize * (currentPage - 1), pageSize * currentPage);
  }
  @Watch('data', { immediate: true, deep: true })
  handleHiveFieldInfoDataChange(newData) {
    const dataCopy = cloneDeep(newData);
    this.hiveFieldInfoData = [];
    this.initData = [];
    this.hiveFieldInfoData = dataCopy;
    this.initData = dataCopy;
    this.pageData.total = dataCopy.length || 0;
    this.setHiveTableData();
  }
  @Watch('initData')
  handleDataChange(newData, preData) {
    this.hiveFieldInfoData.forEach((el) => {
      const matchedItem =
        preData &&
        preData.find((item) => item.inputFieldName && el.fieldName === item.fieldName && el.fieldType === item.fieldType);
      if (matchedItem) {
        el.inputFieldName = matchedItem.inputFieldName;
        el.inputFieldType = matchedItem.inputFieldType;
      }
      const preOutputMatch = this.preOutput.find((item) => item.name === el.fieldName);
      if (preOutputMatch) {
        el.inputFieldName = preOutputMatch.name;
        el.inputFieldType = preOutputMatch.type;
      }
    });
  }

  created() {
    this.fieldListCopy = cloneDeep(this.fieldList);
    this.hiveFieldInfoData = [];
    this.hiveFieldInfoData = this.fieldListCopy;
    this.initData = [];
    this.initData = this.fieldListCopy;
  }
  //筛选
  searchChange(keyword) {
    this.initData = keyword
      ? this.hiveFieldInfoData.filter((item) => item.fieldName.includes(keyword))
      : this.hiveFieldInfoData;
    this.pageData.total = keyword ? this.initData.length : this.hiveFieldInfoData.length;
  }
  // 初始化映射表
  setHiveTableData() {
    this.hiveFieldInfoData.forEach((el) => {
      el.inputFieldName = '';
      el.inputFieldType = '';
      this.preOutput.forEach((item) => {
        if (item.name === el.fieldName) {
          el.inputFieldName = item.name;
          el.inputFieldType = item.type;
        }
      });
    });
  }

  handlePreOutputChange(row) {
    const index = this.hiveFieldInfoData.findIndex((el) => el.fieldName === row.fieldName);
    if (row.inputFieldName === '') {
      this.hiveFieldInfoData[index].inputFieldType = '';
      this.selectKey = Date.now();
      return;
    }
    const foundItem = this.preOutput.find((el) => el.name === row.inputFieldName);
    foundItem && this.$set(this.hiveFieldInfoData[index], 'inputFieldType', foundItem.type);
    this.selectKey = Date.now();
  }
}
</script>
<style lang="scss" scoped>
.hive-table {
  &__search {
    width: 200px;
    float: right;
    margin-bottom: 14px;
  }
}
</style>
