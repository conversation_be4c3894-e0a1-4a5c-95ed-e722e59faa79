<template>
  <el-table border stripe :data="hiveFieldInfoData" max-height="300">
    <el-table-column v-for="item in columnData" :key="item.prop" :label="item.label" align="center">
      <template slot-scope="{ row, $index }">
        <el-select
          v-if="item.isSelect"
          :key="selectKey"
          v-model="row.inputFieldName"
          clearable
          size="mini"
          filterable
          @change="handlePreOutputChange(row, $index)"
        >
          <el-option v-for="el in preOutput" :key="el.name" :label="el.name" :value="el.name" />
        </el-select>
        <span v-else>{{ row[item.prop] }}</span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts">
import { cloneDeep } from 'lodash';
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
@Component
export default class HiveTable extends Vue {
  // 表字段信息列表
  @Prop({ default: () => [] }) data!: any[];
  // 上游节点输出字段
  @Prop({ default: () => [] }) preOutput!: any[];
  //   选中表名称
  @Prop({ default: '' }) headTitle!: string;
  @Prop({ default: '' }) subName!: string;
  // 后端保存值
  @Prop({ default: () => [] }) fieldList!: any[];

  //   表字段信息列表
  private hiveFieldInfoData: any[] = [];
  private fieldListCopy: any[] = [];
  private isSet = false;
  private selectKey = 0;
  // 表列名
  private columnData: any = [
    { label: 'HIVE字段', prop: 'fieldName' },
    { label: 'HIVE字段类型', prop: 'fieldType' },
    { label: '上游输入字段类型', prop: 'inputFieldType' },
    {
      label: '上游输入字段',
      prop: 'inputFieldName',
      isSelect: true
    }
  ];

  @Watch('data', { deep: true })
  handleDataChange(newVal) {
    this.hiveFieldInfoData = [];
    this.hiveFieldInfoData = newVal || [];
    this.setHiveTableData();
  }

  created() {
    this.fieldListCopy = cloneDeep(this.fieldList);
    this.hiveFieldInfoData = [];
    this.hiveFieldInfoData = this.fieldListCopy;
  }

  changeValue(field, index, canMerge) {
    const result = canMerge
      ? this.fieldListCopy[index]
      : this.preOutput.find((it) => it.name === field.fieldName);
    const name = result?.inputFieldName || result?.name;
    const type = result?.inputFieldType || result?.type;
    name && (field.inputFieldName = name);
    type && (field.inputFieldType = type);
  }

  setHiveTableData() {
    const fieldLength = this.fieldListCopy.length;
    const hiveFieldLength = this.hiveFieldInfoData.length;
    const canMerge = hiveFieldLength === fieldLength;
    this.hiveFieldInfoData.forEach((it, index) => {
      it.inputFieldName = '';
      it.inputFieldType = '';
      this.changeValue(it, index, canMerge);
    });
  }
  handlePreOutputChange(row, $index) {
    if (row.inputFieldName === '') {
      this.hiveFieldInfoData[$index].inputFieldType = '';
      this.selectKey = Date.now();
      return;
    }
    const { type } = this.preOutput.find((el) => el.name === row.inputFieldName);
    this.hiveFieldInfoData[$index].inputFieldType = type;
    this.selectKey = Date.now();
  }
}
</script>
