<template>
  <pro-grid>
    <table-block
      paging-front
      title="Topics"
      height="300px"
      :loading="loading"
      :table-data="tableData"
      :column-data="columnData"
      :page-data="pageData"
    >
      <!-- operation -->
      <div slot="operation">
        <bs-search v-model="search" :placeholder="$t('pa.placeholder.keyword')" @search="handleSearch" />
        <!-- topic数量大于0展示创建按钮 -->
        <el-button v-if="showAddBtn && tableData.length" size="small" @click="showAddTopicDialog = true">{{
          $t('pa.action.create')
        }}</el-button>
        <el-button v-if="showDataClearBtn" type="primary" size="small" @click="handleDataClear">{{
          $t('pa.action.dataClear')
        }}</el-button>
      </div>
      <!-- topic -->
      <template slot="topic" slot-scope="{ item }">
        <div v-if="!item.row.available" style="color: red">{{ item.row.topic }}</div>
        <div v-else class="title">
          <span class="data-title">{{ item.row.topic }} </span>
          <el-tag v-if="item.row.shareFlag" size="mini">{{ $t('pa.action.share') }}</el-tag>
        </div>
      </template>
      <!-- operator -->
      <template slot="operator" slot-scope="{ item }">
        <el-tooltip
          v-for="el in getOperatorList(item.row)"
          :key="el.value"
          effect="light"
          placement="bottom"
          :content="el.content"
        >
          <i :class="el.icon" class="topic-icon" @click="handleClick(el.value, item.row)"></i>
        </el-tooltip>
      </template>
    </table-block>
    <!-- 创建 -->
    <add-topic-dialog
      v-if="showAddTopicDialog"
      :res-id="id"
      :res-type="resType"
      :show.sync="showAddTopicDialog"
      @refresh="getTopicList"
    />
    <!-- topic数据预览弹窗 -->
    <topic-preview-dialog v-if="showPreviewDialog" :show.sync="showPreviewDialog" :data="curTopic" />
    <!-- 查看引用关系弹窗 -->
    <topic-relation-dialog v-if="showRelationDialog" :show.sync="showRelationDialog" :data="curTopic" />
  </pro-grid>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getKafkaTopicList, delKafkaTopic, kafkaClearInvalidData } from '@/apis/serviceApi';
import { safeArray, includesPro, hasPermission } from '@/utils';
import { TOPIC_MANAGER_COLUMN_DATA } from '../../utils';
@Component({
  components: {
    TableBlock: () => import('../components/table-block.vue'),
    AddTopicDialog: () => import('../components/add-topic-dialog.vue'),
    TopicPreviewDialog: () => import('../components/topic-preview-dialog.vue'),
    TopicRelationDialog: () => import('../components/topic-relation-dialog.vue')
  }
})
export default class TopicManager extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: false }) shareFlag!: boolean;

  loading = false;
  id = '';
  resType = '';
  search = '';
  showAddTopicDialog = false;
  showTopShare = false; // 顶部创建风险显示
  rawTableData: any[] = [];
  columnData: any[] = TOPIC_MANAGER_COLUMN_DATA;
  tableData: any[] = [];
  pageData = {
    layout: 'total, prev, pager, next, jumper',
    pageSize: this.$store.getters.pageSize || 25,
    currentPage: 1,
    total: 0
  };
  showPreviewDialog = false;
  showRelationDialog = false;
  curTopic: any = {}; // 当前topic

  get showAddBtn() {
    return hasPermission('PA.ELE.SERVICE.EDIT') && this.data?.dataLevel !== 'PARENT';
  }
  get showDataClearBtn() {
    return hasPermission('PA.ELE.SERVICE.EDIT') && this.data?.dataLevel !== 'PARENT';
  }

  created() {
    this.id = this.$route.query.id as string;
    this.resType = this.$route.query.resType as string;
    this.getTopicList();
  }

  async getTopicList() {
    try {
      if (!this.id) return;
      this.loading = true;
      const { success, data, error } = await getKafkaTopicList(this.id);
      if (!success) return this.$message.error(error);
      this.rawTableData = safeArray(data);
      this.showTopShare = this.rawTableData.some(({ shareFlag }) => !shareFlag);
      this.search = '';
      this.handleSearch();
    } finally {
      this.loading = false;
    }
  }
  handleSearch() {
    this.tableData = this.rawTableData.filter(({ topic }) => includesPro(topic, this.search));
    this.pageData.currentPage = 1;
    this.pageData.total = this.tableData.length;
  }
  async handleDataClear() {
    try {
      const { success, msg, error } = await kafkaClearInvalidData(this.id);
      if (!success) return this.$message.error(error);
      this.$message.success(msg);
      this.getTopicList();
    } finally {
      this.loading = false;
    }
  }
  getOperatorList({ available }) {
    return [
      !available &&
        hasPermission('PA.ELE.SERVICE.DELETE') &&
        this.data?.dataLevel !== 'PARENT' && {
          content: this.$t('pa.action.del'),
          icon: 'iconfont icon-shanchu',
          value: 'delete'
        },
      available && { content: this.$t('pa.dataPreview'), icon: 'iconfont icon-chakan', value: 'preview' },
      available && { content: this.$t('pa.citationRelation'), icon: 'iconfont icon-yinyongguanxi', value: 'relation' }
    ].filter(Boolean);
  }
  handleClick(type: string, row: any) {
    type === 'delete' && this.handleDeleteTopic(row);
    type === 'preview' && this.handlePreviewTopic(row);
    type === 'relation' && this.handleTopicRelation(row);
  }
  /* 删除Topic事件 */
  async handleDeleteTopic(row) {
    await this.$confirm(this.$t('pa.tip.delConfirm'), this.$t('pa.action.tip'));
    const { success, msg, error } = await delKafkaTopic(row.id);
    if (!success) return this.$message.error(error);
    this.$message.success(msg);
    this.getTopicList();
  }
  /* topic数据预览事件 */
  handlePreviewTopic(row: any) {
    this.showPreviewDialog = true;
    this.curTopic = row;
  }
  /* topic引用关系事件 */
  handleTopicRelation(row) {
    this.curTopic = row;
    this.showRelationDialog = true;
  }
}
</script>
<style scoped lang="scss">
::v-deep .bs-search {
  margin-right: 10px;
  width: 200px;
}
.topic-icon {
  margin-right: 10px;
  cursor: pointer;
}
</style>
