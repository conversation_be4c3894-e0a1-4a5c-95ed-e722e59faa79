import { get, post, download } from './utils/net';
/**
 * 获取服务资源和流程资源类型的列表
 */
export const getAllTypeList = () => {
  return get('/rs/pa/resConf/getAllTypeList');
};

/**
 * 获取服务资源和流程资源类型的列表
 */
export const getTypeList = () => {
  return get('/rs/pa/resConf/getResTypeList');
};

/**
 * 获取服务资源资源类型的列表
 */
export const getServiceList = ({ type, params }) => {
  return post(`/rs/pa/res/listNoRole/${type}`, params);
};

/**
 * 获取数据库中所有表，路径：【元件管理-服务管理-数据库-查看-表信息】
 */
export const getDatabaseTable = (id, data) => {
  return post(`/rs/pa/res/detail/mysql/table?id=${id}`, data);
};

/**
 * 运行sql，路径：【元件管理-服务管理-数据库-查看-表信息】
 */
export const runSql = (data) => {
  return get('/rs/pa/res/detail/mysql/sql', data);
};

/**
 * 获取mysq库中对应表的字段，路径：【元件管理-服务管理-数据库-查看-表信息-查看】
 */
export const getSqlColumn = (id, tableName, data) => {
  return post('/rs/pa/res/detail/mysql/column' + '?id=' + id + '&tableName=' + tableName, data);
};

// pulsar: 获取topic列表
export const getTopicList = (resId, data) => {
  return post('rs/pa/res/detail/pulsar/getTopics?resId=' + resId, data);
};

// pulsar: Topics-创建
export const createTopic = (data, resId) => {
  return post('rs/pa/res/detail/pulsar/createTopic?resId=' + resId, data);
};

// pulsar: 获取tenant租户列表
export const getPulsarTenantList = (resId) => {
  return get('rs/pa/res/detail/pulsar/tenantlist?resId=' + resId);
};

// pulsar: 获取namespace列表
export const getPulsarNamespaceList = (resId, tenant) => {
  return get(`rs/pa/res/detail/pulsar/namespacelist?resId=${resId}&tenant=${tenant}`);
};

// pulsar: 数据预览
export const pulsarPreview = (resId, topic) => {
  return post(`rs/pa/res/detail/pulsar/preview?resId=${resId}`, topic);
};

// pulsar: 查看引用关系
export const pulsarViewRelation = (resId, params) => {
  return post(`rs/pa/res/detail/pulsar/listRes?resId=${resId}`, params);
};

// kafka: 数据预览
export const kafkaPreview = (resId, topic) => {
  return post(`/rs/pa/res/detail/kafka/preview?resId=${resId}`, topic);
};

// kafka: 查看引用关系
export const kafkaViewRelation = (type, params) => {
  return post(`/rs/pa/res/detail/kafka/listRelation/${type}`, params);
};

// kafka: 引用关系下载
export const kafkaDownloadRelation = (type, id) => {
  return download(`/rs/pa/res/detail/kafka/exportRelation/${type}?id=${id}`);
};

// rocket: 数据预览
export const rocketPreview = (resId, topic) => {
  return get(`/rs/pa/res/detail/rocketmq/preview?id=${resId}&topic=${topic}`);
};

// pulsar: 下载引用关系
export const pulsarExportRelation = (resId, params) => {
  return download(
    `rs/pa/res/detail/pulsar/topic/resDown?resId=${resId}`,
    '',
    params,
    {
      blob: 'blob',
      fileName: 'fileName'
    },
    { method: 'post' }
  );
};

/**
 * 获取hbase中所有表，路径：【元件管理-服务管理-hbase-查看-表信息】
 */
export const getHbaseTable = (id, data) => {
  return post(`/rs/pa/res/detail/hbase/table?id=${id}`, data);
};

// 查看hbase表字段
export const getHbaseTableFields = (id, tableName, data) => {
  return post(`/rs/pa/res/detail/hbase/column?id=${id}&tableName=${tableName}`, data);
};

// 执行hbaseSQL接口
export const runHbaseSQL = ({ id, sql }) => {
  return post(`/rs/pa/res/detail/hbase/execute?id=${id}`, { sql });
};
/**
 * 获取ES中字段信息，路径：【元件管理-服务管理-ES-查看-表信息】
 */
export const getEsTable = (id, data) => {
  return post(`/rs/pa/res/detail/es/indexes?id=${id}`, data);
};

// 查看ES字段
export const getEsTableFields = (id, index, data) => {
  return post(`/rs/pa/res/detail/es/column?id=${id}&index=${index}`, data);
};

// 执行esSQL接口
export const runEsSQL = ({ id, sql }) => {
  return post(`/rs/pa/res/detail/es/execute?id=${id}`, { sql });
};
/**
 * 获取hive中所有表，路径：【元件管理-服务管理-hive-查看-表信息】
 */
export const getHiveTable = (id, data) => {
  return post(`/rs/pa/res/detail/hive/table?id=${id}`, data);
};

// 查看hive表字段
export const getHiveTableFields = (id, tableName, data) => {
  return post(`/rs/pa/res/detail/hive/column?id=${id}&tableName=${tableName}`, data);
};

// 执行hiveSQL接口
export const runHiveSQL = ({ id, sql }) => {
  return post(`/rs/pa/res/detail/hive/execute?id=${id}&sql=${sql}`);
};

// kafka 查看topic 消费情况
export const kafkaDescribeGroup = (params) => {
  return get(`/rs/pa/res/detail/kafka/describeGroup`, params);
};

// kafka消费查所有列表
export const kafkaAllTopic = (params) => {
  return get(`/rs/pa/res/detail/kafka/topicList`, params);
};

// kafka topic上的消费者
export const kafkaGroup = (params) => {
  return get(`/rs/pa/res/detail/kafka/group`, params);
};

// 获取rocketmq的消费情况
export const rocketDescribeGroup = (params) => {
  return get(`/rs/pa/res/detail/rocketmq/describeGroup`, params);
};

// 获取rocketmq的所有topic列表
export const rocketAllTopic = (params) => {
  return get(`/rs/pa/res/detail/rocketmq/allTopics`, params);
};

// 获取rocketmq的所有group列表
export const rocketGroup = (params) => {
  return get(`/rs/pa/res/detail/rocketmq/groups`, params);
};

/**
 * zookeeper 节点树
 */
export const zookeeperTree = (id) => {
  return get(`/rs/pa/res/detail/zookeeper/tree?id=${id}`);
};

/**
 * zookeeper 子节点信息
 */
export const zookeeperNodeInfo = (params) => {
  return get(`/rs/pa/res/detail/zookeeper/nodeInfo`, params);
};

/**
 * 查询计算引擎服务脚本
 */
export const detailScScript = (id) => {
  return get(`/rs/pa/res/detail/sc/script?id=${id}`);
};

/**
 * 查询计算引擎订阅的udf列表
 */
export const detailScUdf = (id) => {
  return get(`/rs/pa/res/detail/sc/udf?id=${id}`);
};
/**
 * 查询计算引擎订阅的udj列表
 */
export const detailScUdj = (id) => {
  return get(`/rs/pa/res/detail/sc/udj?id=${id}`);
};
