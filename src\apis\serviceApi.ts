import { get, post, download, del, put, file } from './utils/net';
/**
 * 获取服务资源和流程资源类型的列表
 */
export const getAllTypeList = () => {
  return get('/rs/pa/resConf/getAllTypeList');
};
// 获取表单配置
export const getFormConf = (params) => {
  return get('/rs/pa/resConf/getFormConf', params);
};

/**
 * 获取服务资源和流程资源类型的列表
 */
export const getServiceConf = () => {
  return get('/rs/pa/resConf/getResTypeList');
};
export const getTypeList = () => {
  return get('/rs/pa/resConf/getResTypeList');
};

/**
 * 获取服务资源资源类型的列表
 */
export const getServiceList = ({ type, params }) => {
  return post(`/rs/pa/res/listNoRole/${type}`, params);
};

// 服务连通性检查
export const testConnect = (params) => {
  return get('/rs/pa/res/testConnect', params);
};

/**
 * 获取数据库中所有表，路径：【元件管理-服务管理-数据库-查看-表信息】
 */
export const getDatabaseTable = (id, data) => {
  return post(`/rs/pa/res/detail/mysql/table?id=${id}`, data);
};

/**
 * 获取mysq库中对应表的字段，路径：【元件管理-服务管理-数据库-查看-表信息-查看】
 */
export const getSqlColumn = (id, tableName, data) => {
  return post('/rs/pa/res/detail/jdbc/column' + '?id=' + id + '&tableName=' + tableName, data);
};
// pulsar: 获取topic列表
export const getTopicList = (resId, data) => {
  return post('rs/pa/res/detail/pulsar/getTopics?resId=' + resId, data);
};

// pulsar: Topics-创建
export const createTopic1 = (data, resId) => {
  return post('rs/pa/res/detail/pulsar/createTopic?resId=' + resId, data);
};

// pulsar: 获取tenant租户列表
export const getPulsarTenantList = (resId) => {
  return get('rs/pa/res/detail/pulsar/tenantlist?resId=' + resId);
};

// pulsar: 获取namespace列表
export const getPulsarNamespaceList = (resId, tenant) => {
  return get(`rs/pa/res/detail/pulsar/namespacelist?resId=${resId}&tenant=${tenant}`);
};

// pulsar: 数据预览
export const pulsarPreview = (resId, topic) => {
  return post(`rs/pa/res/detail/pulsar/preview?resId=${resId}`, topic);
};

// pulsar: 查看引用关系
export const pulsarViewRelation = (resId, params) => {
  return post(`rs/pa/res/detail/pulsar/listRes?resId=${resId}`, params);
};

// kafka: 同步集群topic
export const kafkaSyncTopics = (id) => {
  return get(`/rs/pa/res/detail/kafka/syncTopics?id=${id}`);
};
// kafka: 清楚无效数据（topic）
export const kafkaClearInvalidData = (id) => {
  return get(`/rs/pa/res/detail/kafka/clearInvalidData?id=${id}`);
};

// rocket: 数据预览
export const rocketPreview = (resId, topic) => {
  return get(`/rs/pa/res/detail/rocketmq/preview?id=${resId}&topic=${topic}`);
};

// pulsar: 下载引用关系
export const pulsarExportRelation = (resId, params) => {
  return download(
    `rs/pa/res/detail/pulsar/topic/resDown?resId=${resId}`,
    '',
    params,
    {
      blob: 'blob',
      fileName: 'fileName'
    },
    { method: 'post' }
  );
};

/**
 * 获取hbase中所有表，路径：【元件管理-服务管理-hbase-查看-表信息】
 */
export const getHbaseTable = (id, data) => {
  return post(`/rs/pa/res/detail/hbase/table?id=${id}`, data);
};

// 查看hbase表字段
export const getHbaseTableFields = (id, tableName, data) => {
  return post(`/rs/pa/res/detail/hbase/column?id=${id}&tableName=${tableName}`, data);
};

// 执行hbaseSQL接口
export const runHbaseSQL = (data) => {
  return post(`/rs/pa/res/detail/hbase/execute`, data);
};
/**
 * 获取ES中字段信息，路径：【元件管理-服务管理-ES-查看-表信息】
 */
export const getEsTable = (id, data) => {
  return post(`/rs/pa/res/detail/es/indexes?id=${id}`, data);
};

// 查看ES字段
export const getEsTableFields = (id, index, data) => {
  return post(`/rs/pa/res/detail/es/column?id=${id}&index=${index}`, data);
};

// 执行esSQL接口
export const runEsSQL = (data) => {
  return post(`/rs/pa/res/detail/es/execute`, data);
};
/**
 * 获取hive中所有表，路径：【元件管理-服务管理-hive-查看-表信息】
 */
export const getHiveTable = (id, data) => {
  return post(`/rs/pa/res/detail/hive/table?id=${id}`, data);
};

// 查看hive表字段
export const getHiveTableFields = (id, tableName, data) => {
  return post(`/rs/pa/res/detail/hive/column?id=${id}&tableName=${tableName}`, data);
};

// 执行hiveSQL接口
export const runHiveSQL = (data) => {
  return post(`/rs/pa/res/detail/hive/execute`, data);
};

// kafka 查看topic 消费情况
export const kafkaDescribeGroup = (params) => {
  return get(`/rs/pa/res/detail/kafka/describeGroup`, params);
};

// kafka消费查所有列表
export const kafkaAllTopic = (params) => {
  return get(`/rs/pa/res/detail/kafka/topicList`, params);
};

// kafka topic上的消费者
export const kafkaGroup = (params) => {
  return get(`/rs/pa/res/detail/kafka/group`, params);
};

// 获取rocketmq的消费情况
export const rocketDescribeGroup = (params) => {
  return get(`/rs/pa/res/detail/rocketmq/describeGroup`, params);
};

// 获取rocketmq的所有topic列表
export const rocketAllTopic = (params) => {
  return get(`/rs/pa/res/detail/rocketmq/allTopics`, params);
};

// 获取rocketmq的所有group列表
export const rocketGroup = (params) => {
  return get(`/rs/pa/res/detail/rocketmq/groups`, params);
};

/**
 * zookeeper 节点树
 */
export const zookeeperTree = (id) => {
  return get(`/rs/pa/res/detail/zookeeper/tree?id=${id}`);
};

/**
 * zookeeper 子节点信息
 */
export const zookeeperNodeInfo = (params) => {
  return get(`/rs/pa/res/detail/zookeeper/nodeInfo`, params);
};

/**
 * 查询计算引擎服务脚本
 */
export const detailScScript = (id) => {
  return get(`/rs/pa/res/detail/sc/script?id=${id}`);
};

/**
 * 查询计算引擎订阅的udf列表
 */
export const detailScUdf = (id) => {
  return get(`/rs/pa/res/detail/sc/udf?id=${id}`);
};
/**
 * 查询计算引擎订阅的udj列表
 */
export const detailScUdj = (id) => {
  return get(`/rs/pa/res/detail/sc/udj?id=${id}`);
};

//查询所有的引用关系列表
export const detailScFef = (selectType, data) => {
  return post(`/rs/pa/res/listRelation/${selectType}`, data);
};

export const getClickhouseTable = (id, data) => {
  return post(`/rs/pa/res/detail/clickhouse/table?id=${id}`, data);
};
export const getClickhouseColumn = (id, tableName, data) => {
  return post('/rs/pa/res/detail/clickhouse/column' + '?id=' + id + '&tableName=' + tableName, data);
};
export const runClickhouse = (data) => {
  return post('/rs/pa/res/detail/clickhouse/sql', data);
};
//获取未分配资源的机构名称
export const getUnDisOrgName = (queue, resId) => {
  return get(`/rs/pa/clusterResConf/getUnAssignOrgName?queue=${queue}&resId=${resId}`);
};

//回收机构资源
export const recycleOrg = (clusterId, data) => {
  return post(`/rs/pa/clusterResConf/recycleRes?clusterId=${clusterId}`, data);
};
//回收机构校验
export const checkrecycleOrg = (clusterId, data) => {
  return post(`/rs/pa/clusterResConf/checkResource?clusterId=${clusterId}`, data);
};

// 获取doris表列表
export const getDorisTable = (id, data) => {
  return post(`/rs/pa/res/detail/doris/table?id=${id}`, data);
};

// doris：执行sql
export const runDoris = (data) => {
  return post('/rs/pa/res/detail/doris/sql', data);
};

// 获取doris表列表
export const getDorisColumn = (id, tableName, data) => {
  return post('/rs/pa/res/detail/doris/column' + '?id=' + id + '&tableName=' + tableName, data);
};

// as服务-图形-数据查询
export const getAerospikeCacheInfo = (data) => {
  return get('/rs/pa/res/detail/aerospike/cacheInfo', data);
};

// cubeBase-图形-数据查询
export const getCubeBaseCacheInfo = (data) => {
  return get('/rs/pa/res/detail/cubebase/cacheInfo', data);
};

// 获取服务列表详情
export const getResDetail = (id) => {
  return get('/rs/pa/res/findById', { id });
};

// 测试服务联通性
export const testResConnect = (params) => {
  return post('/rs/pa/res/testConnect', params);
};

// 获取服务依赖关系
export const getResRelation = (id) => {
  return get('/rs/pa/res/serverRelationCount', { id });
};

// 注册服务
export const addRes = (data) => {
  return file('/rs/pa/res/add', data, {
    method: 'post',
    headers: { ContentType: 'multipart/form-data' }
  });
};

// 更新服务
export const updateRes = (data) => {
  return file('/rs/pa/res/update', data, {
    method: 'put',
    headers: { ContentType: 'multipart/form-data' }
  });
};

// 删除服务集群
export const delRes = (ids) => {
  return del('/rs/pa/res/deleteById', null, { data: ids });
};
/* 获取服务详情数据 */
export const getServerData = (id: string, isHost = false) => {
  return get(`/rs/pa/${isHost ? 'host' : 'res'}/findById`, { id });
};
/* 获取服务详情配置 */
export const getServerConfig = (resType: string, formType: string) => {
  return get('/rs/pa/resConf/getFormConf', { resType, formType });
};
/* 获取引用关系列表 */
export const getDependRelation = (search: string, type: string, pageData: any = {}) => {
  return post(`/rs/pa/res/listRelation/${type}`, { search, pageData });
};
/* 下载引用关系 */
export const downloadRelation = (id: string, type: string) => {
  return download(`/rs/pa/res/exportRelation/${type}?id=${id}`);
};

/* 获取预警规则 */
export const getWarnRules = (resId: string) => {
  return get('/rs/pa/warnRule/listByResId', { resId });
};
/* 获取预警规则 */
export const updateWarnRuleState = (id: string, stateType: string) => {
  return put('/rs/pa/warnRule/updateState', [{ id, stateType }]);
};
/* 获取节点列表 */
export const getNodeData = (resId: string) => {
  return get('/rs/pa/resNode/listNode', { resId });
};
/* 获取服务缓存数据 */
export const getServerCacheData = (type: string, data: any) => {
  return get(`/rs/pa/res/detail/${type}/getCacheData`, data);
};
/* 获取服务缓存信息 */
export const getServerCacheInfo = (type: string, id: string) => {
  return get(`/rs/pa/res/detail/${type}/cacheInfo`, { id });
};
export const getKafkaTopic = (id: string) => {
  return get('/rs/pa/res/detail/kafka/topicAvailableList', { id });
};
export const getKafkaGroup = (id: string, topic: string) => {
  return get('/rs/pa/res/detail/kafka/group', { id, topic });
};
export const getKafkaDetail = (data: any) => {
  return get('/rs/pa/res/detail/kafka/describeGroup', data);
};
/* 表信息  */
/* 获取表信息 */
export const getTableList = (type: string, id: string, search: string, pageData: any) => {
  return post(`/rs/pa/res/detail/${type}/table?id=${id}`, { search, pageData });
};
/* 获取表字段 */
export const getFieldList = (type: string, id: string, tableName: string, search: string, pageData: any) => {
  return post(`/rs/pa/res/detail/${type}/column?id=${id}&tableName=${tableName}`, { search, pageData });
};
/* 获取sql */
export const getSqlContent = (resType: string, resId: string, tableName: string, fieldNames: string) => {
  return get(`/rs/pa/res/detail/getResExecSQL`, { resType, resId, tableName, fieldNames });
};
/* 获取表字段 */
export const runSql = (type: string, id: string, sql: string) => {
  return post(`/rs/pa/res/detail/${type}/execute`, { id, sql });
};

export const getJobMgrInfo = (id: string) => {
  return get('/rs/pa/res/detail/flink/jobmanagerConf', { id });
};
export const getTaskMgrInfo = (id: string) => {
  return get('/rs/pa/res/detail/flink/taskmanagers', { id });
};

export const canAllocate = (id: string) => {
  return get('/rs/pa/res/validAssign', { id });
};

export const getQueueList = (id: string) => {
  return get('/rs/pa/clusterResConf/getQueueName', { id });
};
export const getAllocateList = (clusterId: string) => {
  return post(`/rs/pa/clusterResConf/orgResAllocateList?clusterId=${clusterId}`, {});
};
//回收机构校验
export const checkRecycleOrg = (clusterId, data) => {
  return post(`/rs/pa/clusterResConf/checkResource?clusterId=${clusterId}`, data);
};
//回收机构资源
export const realRecycle = (clusterId, data) => {
  return post(`/rs/pa/clusterResConf/recycleRes?clusterId=${clusterId}`, data);
};
//获取未分配资源的机构名称
export const getUnDisOrgList = (queue: string, resId: string) => {
  return get('/rs/pa/clusterResConf/getUnAssignOrgName', { queue, resId });
};
export const getUseInfo = (id: string, orgId: string, queue: string) => {
  return post(`/rs/pa/clusterResConf/allocateMsg?clusterId=${id}&orgId=${orgId}&queue=${queue}`, {});
};
export const allocatedRes = (clusterId: string, allocationInfos: any[], orgId: string) => {
  return post('/rs/pa/clusterResConf/allocated', { clusterId, allocationInfos, orgId });
};
export const getFlinkQueueList = (id: string) => {
  return post(`/rs/pa/clusterResConf/getQueueList?id=${id}`, {});
};
export const getFlinkUseInfo = (clusterId: string) => {
  return post(`/rs/pa/clusterResConf/clusterMsg?clusterId=${clusterId}`, {});
};
export const createTopic = (data: any) => {
  return get('/rs/pa/res/detail/kafka/createTopic', data);
};
// kafka获取topic列表
export const getKafkaTopicList = (id) => {
  return get('/rs/pa/res/detail/kafka/topic', { id });
};
// kafka删除topic
export const delKafkaTopic = (topicId: string) => {
  return del('/rs/pa/res/detail/kafka/delete', { topicId });
};
export const refreshCluster = (clusterId: string) => {
  return get('/rs/pa/clusterResConf/refreshClusterMsg', { clusterId });
};
// 获取服务类型下集群列表
export const getResList = (type: string, data: any) => {
  return post(`/rs/pa/res/list/${type}`, data);
};
export const scpToHost = (data: any) => {
  return post('/rs/pa/host/scp', data, { timeout: 1500000, 'Content-Type': 'multipart/form-data' });
};
export const getTopicRelationList = (type: string, search: string, pageData: any) => {
  return post(`/rs/pa/res/detail/kafka/listRelation/${type}`, { search, pageData });
};
export const downloadTopicRelation = (type: string, id: string) => {
  return download(`/rs/pa/res/detail/kafka/exportRelation/${type}?id=${id}`);
};

export const getTopicPreviewData = (data: any) => {
  return post('/rs/pa/res/detail/kafka/previewTopicData', data);
};
