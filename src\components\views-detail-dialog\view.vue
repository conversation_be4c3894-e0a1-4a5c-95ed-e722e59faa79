<template>
  <div v-loading="loading" class="page-content" style="overflow: unset">
    <transition name="el-zoom-in-center">
      <div style="height: fit-content">
        <!-- 表信息 -->
        <div class="detail_content">
          <div class="no-bg-color-content">
            <div class="tab-title">
              <div class="title-text">
                <span>视图信息</span>
              </div>
            </div>
            <div class="tab-content">
              <div v-for="el in viewInfoRenderList" :key="el.name" :class="generateClass(el.name)">
                {{ el.label }}: {{ viewInfo[el.name] }}
              </div>
            </div>
          </div>
        </div>
        <!-- 字段信息 -->
        <div class="detail_content">
          <div class="no-bg-color-content">
            <div class="tab-title choose-title">
              <div class="title-choose">
                <div class="shu"></div>
                <span>选择表</span>
                <span class="depentTable"> {{ selectedTableData }}</span>
              </div>
            </div>
            <div class="tab-content">
              <p>基础字段</p>
              <el-table
                :data="basicField"
                style="width: 100%; max-height: 400px; overflow: auto"
                stripe
                size="mini"
                border
              >
                <el-table-column prop="fieldName" label="字段名" min-width="130" />
                <el-table-column prop="fieldNameCn" label="中文名" min-width="130" />
                <el-table-column prop="fieldType" label="字段类型" min-width="130" />
                <el-table-column prop="primaryKey" label="主键" min-width="130">
                  <template slot-scope="{ row }">
                    <el-switch
                      v-model="row.primaryKey"
                      active-value="1"
                      inactive-value="0"
                      :disabled="true"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="partition" label="分区" min-width="130">
                  <template slot-scope="{ row }">
                    <el-switch
                      v-model="row.partition"
                      active-value="1"
                      inactive-value="0"
                      :disabled="true"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="businessExplain" label="业务口径" min-width="130" />
              </el-table>
            </div>
          </div>
        </div>
        <div class="first_content" style="overflow-y: auto">
          <!-- 高级 -->
          <div v-if="showAdvancedField" class="detail_content">
            <div class="no-bg-color-content">
              <div class="tab-title">
                <div class="title-text">
                  <span>高级字段</span>
                </div>
              </div>
              <div class="tab-content">
                <el-table
                  :data="advancedField"
                  style="width: 100%; max-height: 400px; overflow: auto"
                  stripe
                  size="mini"
                  border
                >
                  <el-table-column prop="value" min-width="130" />
                </el-table>
              </div>
            </div>
          </div>
        </div>
        <!-- 字段条件 -->
        <div class="detail_content detail-field">
          <div class="no-bg-color-content">
            <div class="tab-title choose-title">
              <div class="title-choose">
                <div class="shu"></div>
                <span>字段条件</span>
                <el-tooltip
                  class="item"
                  effect="light"
                  content="条件逻辑：字段相同是or，字段不同是and"
                  placement="top"
                >
                  <i class="iconfont icon-wenhao title-choose-icon"></i>
                </el-tooltip>
              </div>
            </div>
            <div class="tab-content">
              <el-table
                class="title-slot-table"
                :data="fieldInformation"
                border
                style="width: 100%"
              >
                <el-table-column prop="condition" label="条件" width="100" />
                <el-table-column prop="fieldName" label="判断字段" />
                <el-table-column prop="fieldType" label="字段类型" />
                <el-table-column prop="operator" label="操作符" />
                <el-table-column prop="fieldValue" label="值" />
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { URL_VIEW_FIND, URL_VIEW_TABLEINFO } from '@/apis/commonApi';
import { get } from '@/apis/utils/net';

@Component
export default class ViewDetail extends PaBase {
  @Prop({ type: String, default: '' }) id!: string;
  @Prop({ type: Boolean, default: true }) showAdvancedField!: boolean;
  private loading = false;
  private selectedTableData = '';
  private viewInfo = {
    tableName: '',
    tableNameCn: '',
    createdBy: '',
    createdByMobile: '',
    businessExplain: '',
    label: ''
  };
  private viewInfoRenderList: any[] = [
    {
      label: '视图名',
      name: 'tableName'
    },
    {
      label: '中文名',
      name: 'tableNameCn'
    },
    {
      label: '创建人',
      name: 'createdBy'
    },
    {
      label: '创建人电话',
      name: 'createdByMobile'
    },
    {
      label: '业务口径',
      name: 'businessExplain'
    },
    {
      label: '标签',
      name: 'label'
    }
  ];
  private basicField: any = [];
  private advancedField: any = [];
  private fieldInformation: any = [];

  created() {
    this.getViewDetail();
  }
  async getViewDetail() {
    this.loading = true;
    const { id } = this;
    if (!id) {
      this.$message.error('id不能为空');
      return;
    }
    const { success, data, error } = await get(URL_VIEW_FIND, { id });
    if (success) {
      const {
        viewNamePrefix: prefix,
        viewName: name,
        viewNameCn: tableNameCn,
        createdBy,
        createdByMobile,
        businessExplain,
        label,
        dependTableNames,
        baseFieldInfo,
        advanceFieldInfo,
        viewCondition
      } = data;
      /* 表信息 */
      const tableName = prefix + (name.includes(prefix) ? name.replace(prefix, '') : name);
      this.viewInfo = {
        ...this.viewInfo,
        tableName,
        tableNameCn,
        createdBy,
        createdByMobile,
        businessExplain,
        label
      };
      /* 选择表 */
      this.selectedTableData = dependTableNames;
      /* 基础字段、高级字段 */
      this.basicField = JSON.parse(baseFieldInfo);
      this.advancedField = JSON.parse(advanceFieldInfo);
      /* 字段信息 */
      this.fieldInformation = JSON.parse(viewCondition);
      this.loading = false;
      return;
    }
    this.loading = false;
    this.$message.error(error);
  }

  generateClass(name: string) {
    return ['businessExplain', 'label'].includes(name) ? 'block' : 'inline-block';
  }

  async getAllFields(infoStr = '') {
    try {
      const [[, id]] = JSON.parse(infoStr);
      if (!id) {
        this.basicField = [];
        this.advancedField = [];
        return;
      }
      const { success, data, error } = await get(URL_VIEW_TABLEINFO, { id });
      if (success) {
        const [{ value: basic }, { value: advanced }]: any = Array.isArray(data) ? [...data] : [];
        this.basicField = JSON.parse(basic);
        this.advancedField = JSON.parse(advanced);
        return;
      }
      this.$message.error(error);
    } catch (e) {
      this.basicField = [];
      this.advancedField = [];
    }
  }
}
</script>

<style scoped lang="scss">
.page-content {
  overflow: hidden;
  background-color: #f6f7f9;
  .sheet-bar {
    background-color: rgb(255, 255, 255);
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #d4dce2;
    border-left: none;
    padding: 0 20px;
    font-size: 14px;
    .span {
      cursor: pointer;
    }
    .blod {
      font-weight: bolder;
    }
  }
}

.first_content {
  border: unset;
}

.detail_content {
  min-height: 237px;
  margin-top: 10px;
}

.detail {
  &-field {
    //margin-bottom: 1000px!important;
  }
}

.no-bg-color-content {
  background-color: #ffffff;

  .tab-title {
    .title-text {
      display: flex;
    }

    .title-choose {
      display: flex;
      padding-left: 6px;
      align-items: center;

      &-icon {
        display: inline-block;
        margin-left: 10px;
        vertical-align: middle;
        cursor: pointer;
      }

      .shu {
        width: 3px;
        margin-right: 6px;
        background: #2196f3;
        height: 21px;
      }

      .depentTable {
        font-weight: normal;
        margin-left: 20px;
      }
    }

    .info {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .choose-title {
    padding: 10px;
    height: auto;
    max-height: 200px;
  }

  .tab-content {
    border-top: 1px solid #e2e2e2;
    padding: 20px 25px;
    text-align: left;
    height: 100%;
    font-size: 12px;

    .block {
      margin-bottom: 30px;
      display: block;
    }
    .inline-block {
      margin-bottom: 30px;
      display: inline-block;
      width: 240px;
    }
    .service-row {
      display: flex;
    }

    .table-list {
      padding: 1px;
      min-height: 300px;
      max-height: 500px;
      overflow-y: auto;
      background: #fff;

      .advanced-item {
        border: 1px solid #e2e2e2;
        margin: 10px;
        padding: 15px;

        .item-title {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
        }
      }

      .el-row {
        height: 50px;
        line-height: 50px;

        .grid-content {
          display: flex;
          justify-content: center;
          align-items: center;

          .data-card-icon {
            font-size: 19px;
            margin-left: 20px;
          }
        }
      }

      .el-row:nth-child(even) {
        background: #fafdff;
      }

      .even {
        background: #fafdff;
      }

      .el-row .grid-content {
        text-align: center;
      }
    }
  }
}
</style>
