type OriginField = {
  name: string; // 字段名称
  targetable?: boolean;
  outputable?: boolean; // 是否是选择输出的字段
  disabled?: boolean;
  type: string; // 字段类型
};
type OriginFields = OriginField[];
type OriginPoint = {
  type?: 'IN' | 'OUT';
  uuid: string;
  data?: any;
};

type extraOriginPoint = OriginPoint & { nodeId: string };
// 源数据 节点数据模型
interface OriginNode {
  className: string;
  componentId?: string;
  componentName: string;
  componentVersion?: string;
  inEndPoint?: string;
  inputFields: OriginFields;
  ioProperties?: string;
  jarId?: string;
  jobId: string;
  nodeId: string;
  nodeName: string;
  operateType?: string;
  outEndPoint?: string;
  outputFields: OriginFields;
  parallelism: number;
  parentNodes?: string[];
  pointIn: OriginPoint[];
  pointOut: OriginPoint[];
  position: number[];
  printLog?: boolean;
  properties?: Base;
  type: string;
  updateTime?: number;
  status?: number;
  errorMsg?: string; // 节点状态的报错信息
}

// Dag组件所需的节点数据
interface DagNode {
  id: string;
  nodeName: string;
  inPort: { description: string; id: string }[];
  outPort: { description: string; id: string }[];
  position: number[];
  status: number;
  msg: string;
  selected: boolean;
  parallelism: number;
  parentNodes: string[];
  options?: { [k: string]: any };
}

// 源数据 连线数据模型
interface OriginEdge {
  id?: string;
  endNode: string;
  endNodePoint: string;
  endNodePointType: string;
  startNode: string;
  startNodePoint: string;
  startNodePointType: string;
}

// Dag组件 所需的连线数据模型
interface DagEdge {
  id: string;
  source: {
    cell: string;
    port: string;
  };
  target: {
    cell: string;
    port: string;
  };
  status: number;
}

interface ChangedFields<T> {
  delFields: T[];
  addFields: T[];
}

type StringChangedFields = ChangedFields<string>;

type OriginChangedFields = ChangedFields<OriginField>;
interface ExtraChangedFields {
  label: string;
  model: string;
  status: 0 | 1 | 2;
  isOutput?: boolean;
  changedFields: OriginChangedFields;
}

interface ChangedNodeInfo {
  nodeId: string;
  portId: string;
  nodeName?: string;
  operateType?: string;
  msg: string[]; // 节点错误信息
  sourceChangedFields: OriginChangedFields;
  targetChangedFields: ExtraChangedFields[];
}
export {
  OriginNode,
  DagNode,
  OriginEdge,
  DagEdge,
  extraOriginPoint,
  OriginFields,
  ChangedFields,
  OriginChangedFields,
  ExtraChangedFields,
  ChangedNodeInfo
};
