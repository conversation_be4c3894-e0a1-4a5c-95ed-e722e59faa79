{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "strict": true,
    "skipLibCheck": true,
    "jsx": "preserve",
    "importHelpers": true,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitAny": false,
    "sourceMap": true,
    "baseUrl": ".",
    "types": ["webpack-env"],
    "paths": {
      "@/*": ["src/*"]
    },
    "lib": ["esnext", "dom", "dom.iterable", "scripthost"],
    "resolveJsonModule": true,
  },
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/**/**/*.vue", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"],
  "exclude": ["node_modules", "node_modules/**"]
}
