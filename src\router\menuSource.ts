const router = [
  {
    path: '/source',
    name: 'source',
    meta: {
      title: '资源管理',
      access: 'PA.RES',
      icon: 'iconfont icon-quanjuziyuan'
    },
    component: () => import('../views/source/index.vue'),
    children: [
      {
        name: 'assetsDd',
        path: '/element/assets/dd',
        meta: {
          title: '数据定义',
          access: 'ASSETS.DD',
          customAccess: 'isFuseMode'
        }
      },
      {
        name: 'assetsFunction',
        path: '/element/assets/function',
        meta: {
          title: '方法',
          access: 'ASSETS.FUNC',
          customAccess: 'isFuseMode'
        }
      },
      {
        name: 'assetsJar',
        path: '/element/assets/jar',
        meta: {
          title: '第三方类库',
          access: 'ASSETS.JAR',
          customAccess: 'isFuseMode'
        }
      },
      {
        name: 'dataset',
        path: '/element/assets/dataset',
        meta: {
          title: '造数工具',
          access: 'ASSETS.DATASET',
          customAccess: 'isFuseMode'
        }
      },
      {
        path: '/source/importExport',
        name: 'importExport',
        meta: {
          access: 'PA.RES.PRIVATE.TRANSPORT.MENU', // 权限信息
          title: '导入导出'
        },
        component: () => import('../views/source/export-import/index.vue')
      }
    ]
  }
];

export { router };
