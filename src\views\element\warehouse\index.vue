<template>
  <pro-page title="组件库" :fixed-header="false">
    <div slot="operation" class="operate-box">
      <bs-search
        v-model="searchObj.search"
        placeholder="输入名称"
        style="width: 210px; margin-right: 10px"
        @input="fetchList"
      />

      <el-dropdown v-if="hasFeatureAuthority('PA.ELE.WAREHOUSE.UPLOAD')" @command="handleCommand">
        <el-button type="primary">
          上传<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="one">单个</el-dropdown-item>
          <el-dropdown-item command="batch">批量</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button type="primary" class="default-btn ml" @click="desingForm">页面设计</el-button>
    </div>
    <bs-table
      v-loading="tableLoading"
      :height="'calc(100vh - 290px)'"
      :data="tableData.tableData"
      :column-data="tableData.columnData"
      :page-data="searchObj.pageData"
      @page-change="handleCurrentChange"
      @sort-change="handleSortChange"
      @refresh="getListData"
    >
      <template slot="operator" slot-scope="{ row }">
        <el-tooltip content="编辑图标" effect="light">
          <i
            v-if="connectAuthority(hasFeatureAuthority('PA.ELE.WAREHOUSE.EDIT'), row)"
            class="iconfont icon-bianjitubiao"
            @click="icon(row)"
          ></i>
        </el-tooltip>
        <el-tooltip content="编辑" effect="light">
          <i
            v-if="hasFeatureAuthority('PA.ELE.WAREHOUSE.EDIT')"
            class="iconfont icon-bianji"
            @click="edit(row)"
          ></i>
        </el-tooltip>
        <el-tooltip content="删除" effect="light">
          <i
            v-if="hasFeatureAuthority('PA.ELE.WAREHOUSE.DELETE')"
            class="iconfont icon-shanchu"
            @click="del(row)"
          ></i>
        </el-tooltip>
        <el-tooltip content="下载" effect="light">
          <i class="iconfont icon-xiazai" @click="download(row)"></i>
        </el-tooltip>
        <el-tooltip content="详情" effect="light">
          <i class="iconfont icon-chakan" @click="detail(row)"></i>
        </el-tooltip>
      </template>
    </bs-table>
    <add-edit
      :visible="dialogVisible"
      :title="dialogTitle"
      :data="recordData"
      :form-loading="formLoading"
      @close="closeDialog"
    />
    <edit-icon
      :visible="iconDialogVisible"
      :title="iconDialogTitle"
      :data="recordData"
      @close="closeDialog"
    />
    <design ref="Design" :visible="dialogDesignVisible" @close="closeDialog" />
    <batch-upload
      v-if="batchUploadDialogVisible"
      :visible="batchUploadDialogVisible"
      @close="closeDialog"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import {
  URL_COMPONENT_LIST,
  URL_COMPONENT_DELETE,
  URL_INSTALLATIONPACKAGE_DOWNLOAD_COM,
  URL_COMPONENT_FIND_BY_ID
} from '@/apis/commonApi';
import * as _ from 'lodash';
import { Loading } from 'bs-ui-pro';
import { cloneDeep } from 'lodash';
import moment from 'moment';
@Component({
  components: {
    'base-table': () => import('@/components/base-table.vue'),
    'add-edit': () => import('./modals/add-edit.vue'),
    'edit-icon': () => import('./modals/edit-icon.vue'),
    design: () => import('./modals/design.vue'),
    'batch-upload': () => import('./modals/batch-upload.vue')
  }
})
export default class ElementWarehouse extends PaBase {
  formLoading = false;
  dialogVisible = false;
  dialogDesignVisible = false;
  iconDialogTitle = '';
  iconDialogVisible = false;
  batchUploadDialogVisible = false;
  dialogTitle = '新建';
  recordData: any = {};
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: {
      updateTime: 'DESC'
    }
  };
  tableData: ITableData = {
    columnData: [],
    tableData: []
  };
  fetchList: any = _.debounce(this.getListData, 500);
  handleCommand(command) {
    if (command === 'one') {
      this.add();
    }
    if (command === 'batch') {
      this.batchUpload();
    }
  }
  hasAuthority(hasRole: any, row: any) {
    return !_.isEmpty(_.toString(row.dataLevelType)) && row.dataLevelType !== 'PARENT' && hasRole;
  }
  connectAuthority(hasRole: any, row: any) {
    return (
      !_.isEmpty(_.toString(row.dataLevelType)) &&
      row.dataLevelType !== 'PARENT' &&
      row.operateType !== 'SQL' &&
      hasRole
    );
  }
  download(row: any) {
    window.location.href =
      Vue.axios.defaults.baseURL + URL_INSTALLATIONPACKAGE_DOWNLOAD_COM + '?id=' + row.jarId;
  }
  icon(row: any) {
    this.iconDialogVisible = true;
    this.iconDialogTitle = '编辑图标[' + row.componentName + ']';
    this.recordData = row;
  }
  handleCurrentChange(currentPage, pageSize) {
    this.searchObj.pageData.currentPage = currentPage;
    this.searchObj.pageData.pageSize = pageSize;
    this.fetchList();
  }
  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.fetchList();
  }
  batchUpload() {
    this.batchUploadDialogVisible = true;
  }
  add() {
    this.dialogVisible = true;
    this.dialogTitle = '新建';
  }
  edit(row: any) {
    this.dialogVisible = true;
    this.dialogTitle = '编辑[' + row.componentName + ']';
    this.recordData = row;
  }
  detail(row: any) {
    const loadingInstance1 = Loading.service({ fullscreen: false });
    this.doGet(URL_COMPONENT_FIND_BY_ID, {
      params: { id: row.id }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.dialogDesignVisible = true;
        this.$nextTick(() => {
          const beautify = require('js-beautify');
          (this.$refs.Design as any).show(beautify.js_beautify(resp.data.properties));
        });
      });
      loadingInstance1.close();
    });
  }
  del(row: any) {
    this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        this.tableLoading = true;
        this.doDelete(URL_COMPONENT_DELETE, { params: { id: row.id } }).then((resp: any) => {
          this.parseResponse(resp, () => {
            this.getListData();
          });
          this.tableLoading = false;
        });
      })
      .catch(() => {
        return true;
      });
  }
  closeDialog(needFresh: any) {
    if (needFresh === true) {
      this.getListData();
    }
    this.dialogVisible = false;
    this.iconDialogVisible = false;
    this.dialogDesignVisible = false;
    this.batchUploadDialogVisible = false;
  }
  getListData() {
    this.tableLoading = true;
    const searchObj = cloneDeep(this.searchObj);
    searchObj.search = searchObj.search.trim();
    this.doPost(URL_COMPONENT_LIST, searchObj).then((resp: any) => {
      this.parseResponse(resp, () => {
        const { columnData, tableData, pageData } = resp.data;
        columnData.forEach((el) => {
          if (el.prop === 'tableName') {
            el.width = 150;
          }
          el.value = el.prop;
        });
        columnData.push({ label: '操作', value: 'operator', width: 180, fixed: 'right' });
        tableData.forEach((el) => {
          el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
        });
        this.searchObj.pageData = pageData;
        this.tableData = {
          columnData,
          tableData
        };
      });
      this.tableLoading = false;
    });
  }
  desingForm() {
    this.dialogDesignVisible = true;
  }
  created() {
    this.getListData();
  }
}
</script>

<style scoped>
.ml {
  margin-left: 10px;
}
.iconfont {
  cursor: pointer;
}
.iconfont + .iconfont {
  margin-left: 10px;
}
</style>
