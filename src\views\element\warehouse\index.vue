<template>
  <pro-page :title="$t('pa.warehouse')" :fixed-header="false">
    <div slot="operation" class="operate-box">
      <bs-search
        v-model="searchObj.search"
        :placeholder="$t('pa.placeholder.name')"
        style="width: 210px; margin-right: 10px"
        @input="fetchList()"
      />

      <el-dropdown v-access="'PA.ELE.WAREHOUSE.UPLOAD'" @command="handleCommand">
        <el-button type="primary"> {{ $t('pa.action.upload') }}<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="one">{{ $t('pa.single') }}</el-dropdown-item>
          <el-dropdown-item command="batch">{{ $t('pa.batch') }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button type="primary" style="margin-left: 10px" @click="designForm">{{ $t('pa.pageDesign') }}</el-button>
    </div>
    <bs-table
      v-loading="tableLoading"
      :height="'calc(100vh - 290px)'"
      :data="tableData.tableData"
      :column-data="tableData.columnData"
      :page-data="searchObj.pageData"
      @page-change="handleCurrentChange"
      @sort-change="handleSortChange"
      @refresh="getListData(true)"
    >
      <template slot="operator" slot-scope="{ row }">
        <el-tooltip
          v-if="hasAuthority(row.dataLevelType, 'PA.ELE.WAREHOUSE.EDIT') && row.operateType !== 'SQL'"
          :content="$t('pa.action.editIcon')"
          effect="light"
        >
          <i class="iconfont icon-bianjitubiao" @click="icon(row)"></i>
        </el-tooltip>
        <el-tooltip
          v-if="hasAuthority(row.dataLevelType, 'PA.ELE.WAREHOUSE.EDIT')"
          :content="$t('pa.action.edit')"
          effect="light"
        >
          <i class="iconfont icon-bianji" @click="edit(row)"></i>
        </el-tooltip>
        <el-tooltip
          v-if="hasAuthority(row.dataLevelType, 'PA.ELE.WAREHOUSE.DELETE')"
          :content="$t('pa.action.del')"
          effect="light"
        >
          <i class="iconfont icon-shanchu" @click="del(row)"></i>
        </el-tooltip>
        <el-tooltip :content="$t('pa.action.download')" effect="light">
          <i class="iconfont icon-xiazai" @click="handleDownload(row)"></i>
        </el-tooltip>
        <el-tooltip :content="$t('pa.action.detail')" effect="light">
          <i class="iconfont icon-chakan" @click="detail(row)"></i>
        </el-tooltip>
      </template>
    </bs-table>
    <add-edit
      v-if="dialogVisible"
      :visible="dialogVisible"
      :title="dialogTitle"
      :data="recordData"
      :form-loading="formLoading"
      @close="closeDialog"
    />
    <edit-icon :visible="iconDialogVisible" :title="iconDialogTitle" :data="recordData" @close="closeDialog" />
    <form-design-dialog v-if="showDesignDialog" :show.sync="showDesignDialog" :code.sync="curCode" />
    <batch-upload v-if="batchUploadDialogVisible" :visible="batchUploadDialogVisible" @close="closeDialog" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import {
  URL_COMPONENT_LIST,
  URL_COMPONENT_DELETE,
  URL_INSTALLATIONPACKAGE_DOWNLOAD_COM,
  URL_COMPONENT_FIND_BY_ID
} from '@/apis/commonApi';
import * as _ from 'lodash';
import { Loading } from 'bs-ui-pro';
import { cloneDeep } from 'lodash';
import dayjs from 'dayjs';
import { hasPermission } from '@/utils';
import { get, post, del, download } from '@/apis/utils/net';
@Component({
  components: {
    'add-edit': () => import('./modals/add-edit.vue'),
    'edit-icon': () => import('./modals/edit-icon.vue'),
    FormDesignDialog: () => import('./modals/form_design_dialog.vue'),
    'batch-upload': () => import('./modals/batch-upload.vue')
  }
})
export default class ElementWarehouse extends Vue {
  formLoading = false;
  dialogVisible = false;
  iconDialogTitle = '';
  iconDialogVisible = false;
  batchUploadDialogVisible = false;
  dialogTitle = this.$t('pa.action.add');
  recordData: any = {};
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: this.$store.getters.pageSize || 25, currentPage: 1, total: 1 },
    sortData: {
      updateTime: 'DESC'
    }
  };
  tableData: ITableData = {
    columnData: [],
    tableData: []
  };
  fetchList: any = _.debounce(this.getListData, 500);
  showDesignDialog = false;
  curCode = '';

  created() {
    this.getListData();
  }
  handleCommand(command) {
    if (command === 'one') {
      this.add();
    }
    if (command === 'batch') {
      this.batchUpload();
    }
  }
  // 判断是否有编辑、删除权限
  hasAuthority(dataLevelType: string, accessCode: string) {
    return ['SELF', 'CHILD'].includes(dataLevelType) && hasPermission(accessCode);
  }
  async handleDownload(row: any) {
    try {
      this.tableLoading = true;
      await download(URL_INSTALLATIONPACKAGE_DOWNLOAD_COM + '?id=' + row.jarId);
    } finally {
      this.tableLoading = false;
    }
  }
  icon(row: any) {
    this.iconDialogVisible = true;
    this.iconDialogTitle = this.$t('pa.action.editIconCustom', [row.componentName]);
    this.recordData = row;
  }
  handleCurrentChange(currentPage, pageSize) {
    this.searchObj.pageData.currentPage = currentPage;
    this.searchObj.pageData.pageSize = pageSize;
    this.fetchList();
  }
  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.fetchList();
  }
  batchUpload() {
    this.batchUploadDialogVisible = true;
  }
  add() {
    this.dialogVisible = true;
    this.dialogTitle = this.$t('pa.action.add');
  }
  edit(row: any) {
    this.dialogVisible = true;
    this.dialogTitle = this.$t('pa.action.editCustom', [row.componentName]);
    this.recordData = row;
  }
  async detail(row: any) {
    const loadingInstance1 = Loading.service({ fullscreen: false });
    try {
      const { success, msg, data } = await get(URL_COMPONENT_FIND_BY_ID, { id: row.id });
      if (!success) throw msg;
      const beautify = require('js-beautify');
      this.curCode = beautify.js_beautify(data.properties);
      this.showDesignDialog = true;
      loadingInstance1.close();
    } catch (err) {
      this.$tip.error({ message: err, duration: 5000 });
      loadingInstance1.close();
    }
  }
  del(row: any) {
    this.$confirm(this.$t('pa.tip.deleteConfirm'), this.$t('pa.action.tip'), {
      confirmButtonText: this.$t('pa.action.makeSure'),
      cancelButtonText: this.$t('pa.action.cancel'),
      type: 'warning'
    })
      .then(async () => {
        this.tableLoading = true;
        try {
          const { success, msg } = await del(URL_COMPONENT_DELETE, { id: row.id });
          this.tableLoading = false;
          if (!success) throw msg;
          this.getListData();
        } catch (err) {
          this.$tip.error({ message: err, duration: 5000 });
          this.tableLoading = false;
        }
      })
      .catch(() => {
        return true;
      });
  }
  closeDialog(needFresh: any) {
    if (needFresh === true) {
      this.getListData();
    }
    this.recordData = {}; // 弹窗关闭时，清空所当前行数据
    this.dialogVisible = false;
    this.iconDialogVisible = false;
    this.showDesignDialog = false;
    this.batchUploadDialogVisible = false;
  }
  async getListData(refresh = false) {
    this.tableLoading = true;
    const searchObj = cloneDeep(this.searchObj);
    searchObj.search = searchObj.search.trim();
    try {
      const { success, msg, data } = await post(URL_COMPONENT_LIST, searchObj);
      if (!success) throw msg;
      const { columnData, tableData, pageData } = data;
      columnData.forEach((el) => {
        if (el.prop === 'tableName') {
          el.width = 150;
        }
        el.value = el.prop;
      });
      columnData.push({ label: this.$t('pa.action.action'), value: 'operator', width: 180, fixed: 'right' });
      tableData.forEach((el) => {
        el.updateTime = dayjs(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
      });
      this.searchObj.pageData = pageData;
      this.tableData = {
        columnData,
        tableData
      };
      refresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
      this.tableLoading = false;
    } catch (err) {
      this.$tip.error({ message: err, duration: 5000 });
      this.tableLoading = false;
    }
  }
  designForm() {
    this.showDesignDialog = true;
  }
}
</script>

<style scoped>
.ml {
  margin-left: 10px;
}
.iconfont {
  cursor: pointer;
}
.iconfont + .iconfont {
  margin-left: 10px;
}
</style>
