<template>
  <div class="page-content">
    <header>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>数据接入</el-breadcrumb-item>
        <el-breadcrumb-item>代理管理</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="operate-box">
        <el-button
          type="primary"
          class="copy-btn"
          :data-clipboard-text="installCmd"
          data-clipboard-action="copy"
          @click="onCopy"
          title="点击可复制代理程序的安装命令"
          >安装命令</el-button
        >
        <el-button
          type="primary"
          class="default-btn"
          @click="reg"
          v-if="
            this.getResLabel('DAEMON', 'hasReg') &&
            this.hasFeatureAuthority(this.getResLabel('DAEMON', 'listConf')['regAuthCode'])
          "
          >注册</el-button
        >
        <el-button type="primary" icon="el-icon-refresh-right"></el-button>
      </div>
    </header>
    <div class="content">
      <base-table
        :tableData="tableData"
        :tableConfig="tableConfig"
        v-loading="tableLoading"
      ></base-table>
    </div>
    <addEdit ref="AddEdit" />
    <contact-info ref="ContactInfo" />
  </div>
</template>
<script lang="ts">
import Clipboard from 'clipboard';
import { serverCfg } from '@/apis/commonApi';
import { CommonServiceCustom } from '@/views/element/service/custom/custom';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import * as _ from 'lodash';
@Component
export default class PaDaemon extends CommonServiceCustom {
  private installCmd: string = '';
  private onCopy() {
    const clipboard = new Clipboard('.copy-btn');
    this.$message.success('复制成功');
  }
  // private tableLoading: boolean = false;
  // private tableData: ITableData = {
  //   columnData: [
  //     { label: 'ip', prop: 'ip' },
  //     { label: '主机名', prop: 'hostName' },
  //     { label: '端口', prop: 'port' },
  //     { label: 'contextPath', prop: 'contextPath' },
  //   ],
  //   tableData: [
  //     { ip: '234234', hostName: 'test', port: 22, contextPath: '/daemon' },
  //   ],
  // };
  // private tableConfig: ITableConfig = {
  //   width: 100,
  //   columnsExtend: {
  //     edit: [
  //       {
  //         tipMessage: '编辑',
  //         handler: this.edit.bind(this),
  //         iconfont: 'icon-bianji',
  //       },
  //       {
  //         tipMessage: '删除',
  //         handler: this.delete.bind(this),
  //         iconfont: 'icon-shanchu',
  //       },
  //     ],
  //   },
  // };
  // private edit() {}
  // private delete() {}
  // mounted() {
  //   this.doGet(serverCfg).then((resp: any) => {
  //     this.parseResponse(resp, () => {});
  //   });
  // }
  mounted() {
    this.doGet(serverCfg).then((resp: any) => {
      this.parseResponse(resp, () => {
        try {
          const mgrUrl = resp.data.serverIp + ':' + resp.data.serverPort + resp.data.appCtx;
          this.installCmd =
            'wget -O daemon.zip http://' +
            mgrUrl +
            '/pa/openapi/downloadDaemon;unzip daemon.zip;cd pipeace-daemon*;echo "./run.sh --pipeace.daemon.user=' +
            resp.data.user +
            ' --server.port=' +
            _.random(10000, 15000) +
            ' --pipeace.daemon.mgr-url=' +
            mgrUrl +
            '" > start.sh;chmod +x start.sh;./start.sh;ps -ef| grep pipeace-daemon';
        } catch (e) {
          this.$message.error(e);
        }
      });
    });
  }
}
</script>
