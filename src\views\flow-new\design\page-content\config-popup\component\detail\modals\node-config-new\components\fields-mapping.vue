<template>
  <div class="mapping-dialog">
    <!-- header -->
    <div class="mapping-header">
      <!-- title -->
      <span class="mapping-header__title">{{ $t('pa.flow.fieldMap') }}</span>
      <!-- search -->
      <bs-search v-model="keyword" :placeholder="$t('pa.placeholder.input')" @search="handleSearch" />
    </div>
    <bs-table
      v-loading="loading"
      border
      stripe
      :height="370"
      paging-front
      row-key="fieldName"
      size="mini"
      align="center"
      :data="tableData"
      :page-data="pageData"
      :selection="!disabled"
      :crossing="!disabled"
      :column-settings="false"
      :column-data="columnData"
      :checked-rows="checkedData"
      :show-multiple-selection="false"
      cell-class-name="mapping-center"
      header-cell-class-name="mapping-center"
      @page-change="handlePageChange"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择全部 -->
      <template slot="footer-expand">
        <el-checkbox v-if="!disabled" v-model="checkAll" @change="handleCheckedAll">
          {{ $t('pa.flow.selectAll1') }}
        </el-checkbox>
        <span class="mapping-dialog__total">{{ $t('pa.flow.msg123', [currentSelectionData.length]) }}</span>
      </template>
      <!-- 表字段 -->
      <template slot="fieldName" slot-scope="{ row }">
        <div class="mapping-key">
          <!-- v-hide -->
          <el-tooltip effect="light" placement="top" :content="row.fieldName">
            <span class="mapping-key__id">{{ row.fieldName }}</span>
          </el-tooltip>
          <bs-tag v-if="row.isPrimarykey">{{ $t('pa.flow.mainKey') }}</bs-tag>
          <bs-tag v-if="row.isPartition" color="green">{{ $t('pa.flow.fenqu') }}</bs-tag>
        </div>
      </template>
      <!-- 输入字段 -->
      <template slot="inputField" slot-scope="{ row }">
        <bs-select
          v-if="!disabled"
          v-model="row.inputField"
          clearable
          filterable
          size="small"
          :disabled="disabled"
          :placeholder="$t('pa.flow.placeholder17')"
          :options="inputOptions"
        />
        <span v-else>{{ row.inputField }}</span>
      </template>
    </bs-table>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

// import { getValue, hide, includesPro } from '../utils';
import { uniqBy } from 'lodash';
import { runRequest } from '../utils';
interface outputField {
  fieldName: string;
  fieldNameCn?: string;
  // 字段类型
  fieldType: string;
  // 是否为主键
  isPrimaryKey: boolean;
  // 是否为分区字段
  isPartition: boolean;
  // 输入字段
  inputField: string;
}
// { directives: { hide } }
@Component
export default class FieldsMapping extends Vue {
  @Prop({ default: '' }) value!: outputField[];
  @Prop() tableName!: string;
  @Prop() formData!: Record<string, any>;
  @Prop() config!: Record<string, any>;
  @Prop({ default: () => [] }) inputOptions!: any[];
  @Prop() disabled!: boolean;

  loading = false;
  private keyword = '';
  private pageData = {
    pageSize: 10,
    currentPage: 1,
    total: 0,
    layout: 'total, prev, pager, next'
  };
  private checkAll = false;
  private tableData: any[] = [];
  private checkedData: outputField[] = [];
  private currentSelectionData: outputField[] = [];
  private isRendered = true;
  tableFields: any[] = [];
  rawTableData: any[] = [];
  get orgId() {
    return this.$store.getters.orgId;
  }
  get inputFieldNames() {
    return this.inputOptions.map((item) => item.label);
  }
  get valueMaps() {
    return this.value.reduce((res, item) => {
      res.set(item.fieldName, item.inputField);
      return res;
    }, new Map());
  }
  get columnData() {
    return [
      {
        label: this.tableName,
        value: 'fieldName',
        width: '296px',
        showOverflowTooltip: false
      },
      {
        label: this.$t('pa.flow.inputField'),
        value: 'inputField',
        width: '296px',
        showOverflowTooltip: false
      }
    ];
  }

  @Watch('tableData', { deep: true })
  handleCurrentSelectionDataChange(data) {
    this.isRendered && this.$set(this, 'checkedData', this.currentSelectionData);
    !this.isRendered && (this.isRendered = true);
    this.checkAll = !data.length
      ? false
      : this.currentSelectionData.filter((el) => this.tableData.map((el) => el.fieldName).includes(el.fieldName)).length ===
        this.tableData.length;
  }

  async created() {
    try {
      this.loading = true;
      await this.getFiledList();
      this.handleSearch('', !this.disabled);
    } finally {
      this.loading = false;
    }
  }
  // 获取表格字段
  async getFiledList() {
    const { request } = this.config;
    if (!request) return;
    const { data = [] } = (await runRequest(request, Object.assign(this.formData, { orgId: this.orgId }))) || {};
    data.forEach((item) => {
      // 测试数据转换 后期去除
      // item.fieldName = item.value;
      // item.isPrimaryKey = true;
      // item.isPartition = true;
      // item.fieldNameCn = item.value;
      // 默认显示原映射关系对应的字段 若没有 选择当前字段和上游字段相同的字段
      item.inputField =
        this.valueMaps.get(item.fieldName) || this.inputFieldNames.includes(item.fieldName) ? item.fieldName : '';
    });

    console.log(data);
    this.rawTableData = data;
  }
  /* 处理搜索事件 */
  handleSearch(keyword: string, isFirst = false) {
    this.tableData = !keyword
      ? this.rawTableData
      : (this.rawTableData as any).filter(({ fieldName }) => fieldName.toLowerCase().includes(keyword.toLowerCase()));
    this.pageData = { ...this.pageData, currentPage: 1, total: this.tableData.length };
    if (isFirst) {
      this.$set(
        this,
        'checkedData',
        (this.checkedData = Object.keys(this.value)
          .map((key) => this.tableData.find((el) => String(el.fieldName) === key))
          .filter(Boolean))
      );
      this.isRendered = false;
    }
  }
  handlePageChange(currentPage) {
    this.pageData.currentPage = currentPage;
  }
  /* 处理表格选中事件 */
  handleSelectionChange(data: outputField[] = []) {
    const tableDataIds = this.tableData.map((el) => el.fieldName);
    const preSelections = this.currentSelectionData.filter((el) => !tableDataIds.includes(el.fieldName));
    if (!data.length && preSelections.length) {
      this.$set(this, 'currentSelectionData', preSelections);
    } else if (data.length && preSelections.length) {
      this.$set(this, 'currentSelectionData', uniqBy([...data, ...preSelections], 'fieldName'));
    } else {
      this.$set(this, 'currentSelectionData', data);
    }
    if (!this.keyword) {
      this.checkAll = this.currentSelectionData.length === this.tableData.length;
    } else {
      this.checkAll =
        this.currentSelectionData.filter((el) => this.tableData.map((el) => el.fieldName).includes(el.fieldName)).length ===
        this.tableData.length;
    }
  }
  /* 处理表格全选事件 */
  handleCheckedAll(isChecked) {
    this.checkedData = isChecked ? this.tableData : [];
    this.handleSelectionChange(this.checkedData);
  }
  /* 处理确认事件 */
  public async confirm(done: (bol: boolean, data?: any) => void) {
    try {
      await this.checkFieids();
      const result = this.getSpecialField();
      if (result) {
        await this.$confirm(this.$t('pa.flow.msg293', [result]), this.$t('pa.flow.tip'));
      }
      done(true, [...this.currentSelectionData]);
    } catch (err) {
      done(false);
    }
  }
  /* 字段映射校验 */
  checkFieids() {
    return new Promise((resolve: any, reject: any) => {
      if (this.currentSelectionData.length < 1) {
        this.$tip.error(this.$t('pa.flow.msg83'));
        return reject(false);
      }
      const target = this.currentSelectionData.find((el) => !el.inputField);
      if (target) {
        this.$tip.error(this.$t('pa.flow.msg84', [target.inputField]));
        return reject(false);
      }
      resolve(true);
    });
  }
  /* 获取第一个但未选的字段 */
  getSpecialField() {
    const idList = this.tableData.reduce((pre: string[], { fieldName, inputField }: any) => {
      inputField && pre.push(fieldName);
      return pre;
    }, []);
    const selectedIdList = this.currentSelectionData.reduce((pre: string[], { fieldName }: any) => {
      fieldName && pre.push(fieldName);
      return pre;
    }, []);
    for (const i of idList) {
      if (!selectedIdList.includes(i)) return i;
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-select {
  width: 100%;
}
::v-deep .mapping-center {
  text-align: center;
}
::v-deep .bs-table-footer-slot {
  padding-left: 0;
  text-align: left;
}

.mapping {
  &-dialog {
    ::v-deep .el-dialog__body {
      padding-bottom: 0;
      min-height: 490px !important;
    }
    &__total {
      font-weight: bolder;
      letter-spacing: 1px;
    }
  }
  &-header {
    display: flex;
    justify-content: space-between;
    align-content: center;
    margin-bottom: 16px;
    height: 32px;
    &__title {
      font-weight: bold;
      font-size: 14px;
    }
  }

  &-key {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    &__id {
      display: inline-block;
      width: calc(100% - 45px);
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}
</style>
