<template>
  <bs-dialog
    size="large"
    :title="title"
    :visible.sync="visible"
    class="mapping-dialog"
    append-to-body
    :confirm-button="{ disabled: loading }"
    :before-close="closeDialog"
    @close="closeDialog(false, null)"
    @confirm="submit"
  >
    <div v-loading="loading" :element-loading-text="$t('pa.flow.comLoading')">
      <el-alert
        :title="configType === 'text' ? $t('pa.flow.msg160') + ' ' + $t('pa.flow.msg317') : $t('pa.flow.msg160')"
        type="error"
        :closable="false"
      />
      <el-tabs v-model="configType" :before-leave="handleTabBeforeLeave" @tab-click="handleConfigTypeChange">
        <el-tab-pane label="表格" name="table">
          <div>
            <!-- header -->
            <div class="mapping-header">
              <!-- action -->
              <div class="mapping-header__action">
                <bs-search v-model="keyword" :placeholder="$t('pa.flow.placeholder0')" @search="handleFilter" />
                <bs-select
                  v-model="filterType"
                  class="mapping-header__select"
                  :options="filterOptions"
                  @change="handleFilter"
                />
                <el-button v-if="!disabled" class="mapping-header__btn" @click="addNewRow">
                  {{ $t('pa.flow.createRow') }}
                </el-button>
              </div>
            </div>
            <!-- body -->
            <config-table
              :data.sync="tableData"
              :keyword="keyword"
              :disabled="disabled"
              :page-data="pageData"
              :share-methods="shareMethods"
              :input-fields="inputFields"
              :private-methods="privateMethods"
              @detete="handleDetete"
              @page-change="handlePageChange"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="文本" name="text">
          <config-text
            v-if="configType === 'text'"
            ref="configText"
            :data="tableData"
            :input-data="data.inputFields"
            :methods="methodMaps"
            :read-only="disabled"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <print-log slot="footer-left" v-model="printLog" :disabled="disabled" />
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Emit, Vue } from 'vue-property-decorator';
import { SET_ASSET_FUNCS } from '@/store/event-name';
import { URL_COMPONENT_FUNC_LIST } from '@/apis/commonApi';
import cloneDeep from 'lodash/cloneDeep';
import { get } from '@/apis/utils/net';
import { vaildArray, includesPro, handleArray, uuid } from './utils';
@Component({
  components: {
    ConfigTable: () => import('./config-table.vue'),
    PrintLog: () => import('../components/print-log.vue'),
    ConfigText: () => import('./config-text.vue')
  }
})
export default class Mapping extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: false }) disabled!: boolean;

  private loading = false;
  private printLog = false; // 日志输出
  private keyword = ''; // 搜索词
  private tableData: any[] = []; // 配置数据
  private shareMethods: any[] = []; // 共享方法
  private privateMethods: any[] = []; // 私有方法
  private pageData = { pageSize: 10, currentPage: 1, total: 0, layout: 'total,prev,pager,next' };
  private filterType: 'all' | 'selected' | 'unselected' = 'all'; // 筛选类型：all-全部，selected-已选中，unselected-未选中
  private filterOptions = [
    { value: 'all', label: this.$t('pa.flow.showAll') },
    { value: 'selected', label: this.$t('pa.flow.showSelected') },
    { value: 'unselected', label: this.$t('pa.flow.showUnselected') }
  ];
  private inputFields: any = [];
  private configType: 'table' | 'text' = 'table';
  private methodMaps = new Map();
  get title() {
    return `${this.data.nodeName || ''}${this.$t('pa.flow.comConfig')}`;
  }
  get checkedData() {
    return this.tableData.filter(({ outputable }) => outputable);
  }
  async created() {
    try {
      this.loading = true;
      await this.getMethods();
      /* 是否开启日志 */
      this.printLog = Boolean(this.data.printLog);
      /* tabledData */
      this.generateTableData();
      this.handleFilter();
      this.loading = false;
    } catch {
      this.loading = false;
    }
  }
  /* 获取所有方法 */
  async getMethods() {
    const { success, data, error } = await get(URL_COMPONENT_FUNC_LIST);
    if (success) {
      const newData = Array.isArray(data) ? data : [];
      this.shareMethods = newData.filter(({ funcType }) => funcType === 'SHARE');
      this.privateMethods = newData.filter(({ funcType }) => funcType === 'PRIVATE');
      this.methodMaps = newData.reduce((res, val) => {
        res.set(val.name, val);
        return res;
      }, new Map());
      this.$store.commit(SET_ASSET_FUNCS, this.shareMethods);
      return;
    }
    this.$tip.error(error);
  }
  /* 生成表格数据 */
  generateTableData() {
    this.inputFields = handleArray(this.data.inputFields).map((el) => {
      return {
        name: el.name,
        type: el.type,
        targetable: el.targetable
      };
    });
    const outputFields = handleArray(this.data.outputFields);
    const isInputArray = outputFields.filter(({ isInput, targetable }) => isInput || targetable).map(({ name }) => name);
    const outputNames = outputFields.filter(({ outputable }) => outputable).map(({ name }) => name);
    const customData = outputFields.filter(({ name }) => !isInputArray.includes(name));
    const newData = this.inputFields.map((el) => {
      return {
        hide: false,
        name: el.name,
        type: el.type,
        disabled: true,
        outputable: outputNames.includes(el.name),
        params: [],
        targetable: el.targetable,
        isInput: true
      };
    });
    const result = [...newData, ...customData];
    result.forEach((el) => !el.id && (el.id = uuid()));
    this.tableData = result;
    // for (let i = 0; i < 3000; i++) {
    //   this.tableData.push({
    //     id: uuid(),
    //     hide: false,
    //     name: `${i}`,
    //     type: 'String',
    //     disabled: false,
    //     outputable: false,
    //     params: []
    //   });
    // }
  }
  /* 重置筛选条件 */
  resetQuery() {
    this.keyword = '';
    this.pageData.currentPage = 1;
    this.pageData.total = this.tableData.filter((el) => !el.hide).length;
  }
  /* 处理筛选事件 - 统一处理搜索和选择筛选 */
  handleFilter() {
    this.tableData.forEach((el) => {
      // 首先根据搜索关键词判断是否隐藏
      const hideBySearch = !this.keyword ? false : !includesPro(el?.name, this.keyword);
      // 再根据筛选类型判断是否隐藏
      let hideByFilter = false;
      if (this.filterType === 'selected' && !el.outputable) {
        hideByFilter = true;
      } else if (this.filterType === 'unselected' && el.outputable) {
        hideByFilter = true;
      }
      // 两个条件任一为true则隐藏该行
      this.$set(el, 'hide', hideBySearch || hideByFilter);
    });
    // 同步分页总数
    this.pageData.total = this.tableData.filter((el) => !el.hide).length;
  }

  /* 添加行 */
  addNewRow() {
    this.tableData.push({
      id: uuid(),
      hide: false,
      outputable: false,
      name: '',
      type: 'String',
      method: '',
      params: [],
      disabled: false
    });
    this.pageData.total = this.tableData.filter((el) => !el.hide).length;
  }
  /* 处理删除事件 */
  handleDetete(currentPage) {
    // 更新筛选数据
    this.handleFilter();
    // this.pageData.total 已在 handleFilter 里同步
    const total = Math.ceil(this.pageData.total / this.pageData.pageSize);
    this.pageData.currentPage = currentPage < total ? currentPage : total;
  }
  /* 处理页数变化 */
  handlePageChange(currentPage) {
    this.pageData.currentPage = currentPage;
  }
  /* 处理tab切换 */
  handleConfigTypeChange() {
    // TODO:
  }
  /* 处理tab切换前 */
  handleTabBeforeLeave(activeName) {
    return new Promise((resolve: any, reject: any) => {
      // 切换到表格之前进行校验 若验证失败则不切换
      if (activeName === 'table') {
        if (this.validateCode()) {
          this.resetQuery();
          resolve(true);
        } else {
          reject(false);
        }
      } else {
        resolve(true);
      }
    });
  }
  /* 表单提交 */
  async submit() {
    const result = await this.validate();
    if (!result) return;
    const jobNode = cloneDeep(this.data);
    const outputFields = cloneDeep(this.tableData);
    outputFields.forEach((el: any) => {
      el.name = el.name.replace(/\s*/g, '');
    });
    jobNode.outputFields = outputFields;
    jobNode.printLog = this.printLog;
    this.closeDialog(true, jobNode);
  }
  /* 验证文本配置是否正确，若正确给tableData赋值 */
  validateCode() {
    const configText: any = this.$refs.configText;
    if (!configText) return true;
    if (configText.validate()) {
      this.tableData = configText.syncTableData();
      return true;
    } else {
      return false;
    }
  }
  /* 数据校验 */
  validate() {
    const isText = this.configType === 'text';
    const { methodMaps } = this;
    // TODO:需要优化
    return new Promise((resolve: any, reject: any) => {
      // 文本配置
      if (isText && !this.validateCode()) {
        reject(false);
      }
      const nameList = this.tableData.map(({ name }) => name);
      for (const [i, { name, type, method, params = [] }] of this.tableData.entries()) {
        // if (disabled) continue; 上游输入字段也可能会重复
        if (!name || !type) {
          this.$tip.error(this.$t('pa.flow.msg125', [Number(i) + 1]));
          reject(false);
          break;
        } else {
          const index = nameList.findIndex((el) => el === name);
          if (String(index) !== String(i)) {
            this.$tip.error(this.$t('pa.flow.msg126', [Number(i) + 1]));
            reject(false);
            break;
          }
        }
        if (vaildArray(params)) {
          const item = params.find(({ value }) => !value);
          if (item) {
            this.$tip.error(this.$t('pa.flow.msg161', [Number(i) + 1]));
            reject(false);
            break;
          }
        }
        // 增加校验方法名称 返回值 参数类型是否正确
        if (!method) continue;
        if (methodMaps.has(method)) {
          const { paramTypes = [] } = methodMaps.get(method);
          if (paramTypes.length !== params.length || paramTypes.some((item, index) => item !== (params[index] || {}).type)) {
            this.$tip.error(this.$t('pa.flow.msg319') + `${Number(i) + 1}` + this.$t('pa.flow.msg320'));
            reject(false);
            break;
          }
        } else if (!nameList.filter((n) => n !== name).includes(method.replace(/#/g, ''))) {
          this.$tip.error(this.$t('pa.flow.msg319') + `${Number(i) + 1}` + this.$t('pa.flow.msg321'));
          reject(false);
          break;
        }
      }
      resolve(true);
    });
  }

  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    return { needUpdate, jobNode };
  }
}
</script>
<style lang="scss" scoped>
.mapping {
  &-dialog {
    ::v-deep .el-dialog__body {
      padding-bottom: 0;
      min-height: 584px !important;
    }
  }
  &-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 10px;
    &__title {
      color: red;
    }
    &__action {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    &__btn {
      margin-left: 8px;
    }
    &__select {
      width: 150px;
      margin-left: 8px;
    }
  }
}
.el-tabs.el-tabs--top:not(.el-tabs--card):not(.el-tabs--border-card) ::v-deep .el-tabs__item {
  height: 46px;
  line-height: 46px;
}
</style>
