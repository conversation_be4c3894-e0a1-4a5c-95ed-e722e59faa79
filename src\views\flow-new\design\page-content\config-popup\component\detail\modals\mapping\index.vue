<template>
  <bs-dialog
    width="80%"
    :title="title"
    :visible.sync="visible"
    class="mapping-dialog"
    :before-close="closeDialog"
    append-to-body
  >
    <div v-loading="loading" element-loading-text="配置加载中...">
      <!-- header -->
      <div class="mapping-header">
        <span class="mapping-header__title">
          注意：处理规则引用的方法中，如需输入固定参数，需要对特殊字符串进行转义，如"需写为\\"。
        </span>
        <!-- action -->
        <div class="mapping-header__action">
          <bs-search v-model="keyword" placeholder="请输入" @search="handleSearch" />
          <el-button v-if="!disabled" class="mapping-header__btn" @click="addNewRow">
            添加行
          </el-button>
        </div>
      </div>
      <!-- body -->
      <config-table
        :data.sync="tableData"
        :keyword="keyword"
        :page-data="pageData"
        :share-methods="shareMethods"
        :input-fields="inputFields"
        :private-methods="privateMethods"
        @detete="handleDetete"
        @page-change="handlePageChange"
      />
    </div>
    <div slot="footer" class="mapping-footer">
      <el-switch
        v-model="printLog"
        active-color="#13ce66"
        inactive-color="#ff4949"
        active-text="开启日志"
        inactive-text="关闭日志"
        :disabled="disabled"
      />
      <div>
        <el-button @click="closeDialog(false)">取消</el-button>
        <el-button v-if="!disabled" type="primary" @click="submit">确定</el-button>
      </div>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Emit, Vue } from 'vue-property-decorator';
import { UPDATE_ASSET_FUNCS } from '@/store/event-names/mutations';
import { URL_COMPONENT_FUNC_LIST } from '@/apis/commonApi';
import cloneDeep from 'lodash/cloneDeep';
import { get } from '@/apis/utils/net';
import { vaildArray, includesPro, handleArray, uuid } from './utils';

@Component({ components: { ConfigTable: () => import('./config-table.vue') } })
export default class Mapping extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: false }) disabled!: boolean;

  private loading = false;
  private printLog = false; // 日志输出
  private keyword = ''; // 搜索词
  private tableData: any[] = []; // 配置数据
  private shareMethods: any[] = []; //共享方法
  private privateMethods: any[] = []; //共享方法
  private pageData = { pageSize: 10, currentPage: 1, total: 0, layout: 'total,prev,pager,next' };
  inputFields: any = [];

  get title() {
    return `${this.data.nodeName || ''}组件配置`;
  }
  get checkedData() {
    return this.tableData.filter(({ outputable }) => outputable);
  }
  async created() {
    try {
      this.loading = true;
      await this.getMethods();
      /* 是否开启日志 */
      this.printLog = Boolean(this.data.printLog);
      /* tabledData */
      this.generateTableData();
      this.handleSearch('');
      this.loading = false;
    } catch {
      this.loading = false;
    }
  }
  /* 获取所有方法 */
  async getMethods() {
    const { success, data, error } = await get(URL_COMPONENT_FUNC_LIST);
    if (success) {
      const newData = Array.isArray(data) ? data : [];
      this.shareMethods = newData.filter(({ funcType }) => funcType === 'SHARE');
      this.privateMethods = newData.filter(({ funcType }) => funcType === 'PRIVATE');
      this.$store.commit(UPDATE_ASSET_FUNCS, this.shareMethods);
      return;
    }
    this.$tip.error(error);
  }
  /* 生成表格数据 */
  generateTableData() {
    this.inputFields = handleArray(this.data.inputFields).map((el) => {
      return {
        name: el.name,
        type: el.type,
        targetable: el.targetable
      };
    });
    const outputFields = handleArray(this.data.outputFields);
    const isInputArray = outputFields
      .filter(({ isInput, targetable }) => isInput || targetable)
      .map(({ name }) => name);
    const outputNames = outputFields.filter(({ outputable }) => outputable).map(({ name }) => name);
    const customData = outputFields.filter(({ name }) => !isInputArray.includes(name));
    const newData = this.inputFields.map((el) => {
      return {
        hide: false,
        name: el.name,
        type: el.type,
        disabled: true,
        outputable: outputNames.includes(el.name),
        params: [],
        targetable: el.targetable,
        isInput: true
      };
    });
    const result = [...newData, ...customData];
    result.forEach((el) => !el.id && (el.id = uuid()));
    this.tableData = result;
    // for (let i = 0; i < 3000; i++) {
    //   this.tableData.push({
    //     id: uuid(),
    //     hide: false,
    //     name: `${i}`,
    //     type: 'String',
    //     disabled: false,
    //     outputable: false,
    //     params: []
    //   });
    // }
  }

  /* 处理搜索事件 */
  handleSearch(queryStr: string) {
    this.tableData.forEach((el) => {
      this.$set(el, 'hide', !queryStr ? false : !includesPro(el?.name, queryStr));
    });
    this.pageData.total = this.tableData.filter((el: any) => !el.hide).length;
    this.pageData.currentPage = 1;
  }
  /* 添加行 */
  addNewRow() {
    this.tableData.push({
      id: uuid(),
      hide: false,
      outputable: false,
      name: '',
      type: 'String',
      method: '',
      params: [],
      disabled: false
    });
    this.pageData.total = this.tableData.filter((el) => !el.hide).length;
    this.pageData.currentPage = Math.ceil(this.pageData.total / this.pageData.pageSize);
  }
  /* 处理删除事件 */
  handleDetete(currentPage) {
    // this.handleSearch(this.keyword);
    this.pageData.total = this.tableData.filter((el) => !el.hide).length;
    const total = Math.ceil(this.pageData.total / this.pageData.pageSize);
    this.pageData.currentPage = currentPage < total ? currentPage : total;
  }
  /* 处理页数变化 */
  handlePageChange(currentPage) {
    this.pageData.currentPage = currentPage;
  }
  /* 表单提交 */
  async submit() {
    await this.validate();
    const jobNode = cloneDeep(this.data);
    const outputFields = cloneDeep(this.tableData);
    outputFields.forEach((el: any) => {
      el.name = el.name.replace(/\s*/g, '');
    });
    jobNode.outputFields = outputFields;
    jobNode.printLog = this.printLog;
    this.closeDialog(true, jobNode);
  }
  /* 数据校验 */
  validate() {
    // TODO:需要优化
    return new Promise((reslove: any, reject: any) => {
      const nameList = this.tableData.map(({ name }) => name);
      for (const i in this.tableData) {
        const { disabled, name, type, params } = this.tableData[i];
        if (!disabled) {
          if (!name || !type) {
            this.$tip.error(`第${Number(i) + 1}条记录未配置完整`);
            reject(false);
            break;
          } else {
            const index = nameList.findIndex((el) => el === name);
            if (String(index) !== i) {
              this.$tip.error(`第${Number(i) + 1}条记录的名称与其他字段名有重复`);
              reject(false);
              break;
            }
          }
          if (vaildArray(params)) {
            const item = params.find(({ value }) => !value);
            if (item) {
              this.$tip.error(`第${Number(i) + 1}条记录方法的参数未配置完整`);
              reject(false);
              break;
            }
          }
        }
      }
      reslove(true);
    });
  }

  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    return { needUpdate, jobNode };
  }
}
</script>
<style lang="scss" scoped>
.mapping {
  &-dialog {
    ::v-deep .el-dialog__body {
      padding-bottom: 0;
      min-height: 490px !important;
    }
  }
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    &__title {
      color: red;
    }
    &__action {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    &__btn {
      margin-left: 8px;
    }
  }
  &-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
