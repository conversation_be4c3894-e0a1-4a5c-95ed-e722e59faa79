import type { PluginObject } from 'vue';
import type { ElNotificationOptions } from 'bs-ui-pro/types/notification';
import { Notification } from 'bs-ui-pro';
import ErrorDialog from './index.vue';

const generateHtml = (msg = '', hasData: boolean) => {
  if (!hasData) return `${msg}`;
  return `
  <div>
    <span>${msg}</span>
    <button id="DETAIL" class="el-button el-button--text">查看详情</button>
  </div
  `;
};

const instance = new ErrorDialog();
const Tip: PluginObject<any> = {
  install(Vue: any) {
    document.body.append(instance.$mount(document.createElement('div')).$el);
    const notify = (config: ElNotificationOptions) => {
      if (typeof config.message !== 'string') config.message = (config.message as any).message;
      if (config && !config.title) config.title = '提示';
      return Notification({ ...config });
    };
    notify.success = (message: string, title = '') =>
      notify({ title, message, type: 'success', customClass: 'tipIndex' });
    notify.warning = (message: string, title = '') => {
      notify({ title, message, type: 'warning', duration: 10000, customClass: 'tipIndex' });
    };
    notify.info = (message: string, title = '') => {
      notify({ title, message, type: 'info', duration: 10000, customClass: 'tipIndex' });
    };
    notify.error = (message: string, title = '') => {
      notify({ title, message, type: 'error', duration: 30000, customClass: 'tipIndex' });
    };
    notify.errorPro = (error = '', detail: any = {}) => {
      instance.detail = detail;
      notify({
        title: '',
        type: 'error',
        duration: 30000,
        customClass: 'tipIndex',
        message: generateHtml(error, Object.keys(detail || {}).length > 0),
        dangerouslyUseHTMLString: true,
        onClick(e: any) {
          e?.target?.id === 'DETAIL' && (instance.dispaly = true);
        }
      });
    };
    Vue.prototype.$tip = notify;
  }
};
export default Tip;
