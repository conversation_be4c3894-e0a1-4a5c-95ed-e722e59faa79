import i18n from '@/i18n';
import type { PluginObject } from 'vue';
import type { ElNotificationOptions } from 'bs-ui-pro/types/notification';
import { Notification } from 'bs-ui-pro';
import ErrorDialog from './index.vue';
import './style.scss';

const generateHtml = (type = 'SIMPLE', msg = '') => {
  if (type === 'SIMPLE') return `${msg}`;
  return `
  <div>
    <span>${msg}</span>
    <button id="DETAIL" class="el-button el-button--text">${i18n.t('pa.flow.detail')}</button>
  </div>
  `;
};

const instance = new ErrorDialog();
const Tip: PluginObject<any> = {
  install(Vue: any) {
    document.body.append(instance.$mount(document.createElement('div')).$el);
    const notify = (config: ElNotificationOptions) => {
      if (typeof config.message !== 'string') config.message = (config.message as any).message;
      if (config && !config.title) config.title = i18n.t('pa.prompt') as string;
      return Notification({ ...config });
    };
    notify.success = (message: string, title = '') => notify({ title, message, type: 'success', customClass: 'tipIndex' });
    notify.warning = (message: string, title = '') => {
      notify({ title, message, type: 'warning', duration: 10000, customClass: 'tipIndex' });
    };
    notify.info = (message: string, title = '') => {
      notify({ title, message, type: 'info', duration: 10000, customClass: 'tipIndex' });
    };
    notify.error = (message: string, title = '') => {
      notify({ title, message, type: 'error', duration: 30000, customClass: 'tipIndex' });
    };
    notify.errorPro = ({ msgType = 'SIMPLE', msg = '', data = [] }: ErrorProInfo) => {
      instance.errorList = data;
      notify({
        title: '',
        type: 'error',
        duration: 30000,
        customClass: 'tipIndex',
        message: generateHtml(msgType, msg),
        dangerouslyUseHTMLString: true,
        onClick(e: any) {
          if (e?.target?.id === 'DETAIL') {
            instance.display = true;
          }
        }
      });
    };
    Vue.prototype.$tip = notify;
  }
};
export default Tip;
