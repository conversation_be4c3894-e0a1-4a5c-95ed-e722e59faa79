export interface TableItem {
  id: string;
  value: string;
  isPrimarykey: boolean;
}

export interface FormData {
  jdbcType: string; // 服务类型
  resTitle: string; // 服务名称
  resId: string; // 服务Id
  jdbcUrl: string; //服务地址
  tableName: string; // 选择表
  selectField: string[]; //输出字段
  userDefinedWhere?: string; // while 条件
  queryStrategy: string; // 查询方式
  timeField?: string; // 时间字段
  queryInterval?: number; // 区间大小
  startTime?: string | number; // 起始时间
  indexField?: string; // 索引字段
  pageSize?: number; // 分页大小
  waitTime?: number; // 间隔时间
  resetType: string; // 游标重置模式
  cronCycle?: string; // 周期模式
  sec_1?: string; // 定时模式-秒
  min_1?: string; // 定时模式-分钟
  hour_1?: string; // 定时模式-小时
  day_1?: string; // 定时模式-日
  sec_2?: string; // 高级模式-秒
  min_2?: string; // 高级模式-分钟
  hour_2?: string; // 高级模式-小时
  day_2?: string; // 高级模式-日
  month_2?: string; // 高级模式-月
  week_2?: string; // 高级模式-星期
}

interface Info {
  resId: string;
  resTitle: string;
  address: string;
}
export interface Option {
  label: string;
  value: string;
  disabled?: boolean;
  info?: Info;
  type?: string;
  tooltip?: string;
}
export interface RenderListItem {
  show: boolean;
  label: string;
  fieid: string;
  type: string;
  options?: Option[];
  min?: number;
  max?: number;
  placeholder?: string;
  tooltip?: string;
  rules?: any;
  clearable?: boolean;
  config?: boolean;
  readonly?: boolean;
  autosize?: any;
  remotemethod?: any;
  fetchSuggestions?: any;
  renderList?: any[];
  disabled?: boolean;
}
