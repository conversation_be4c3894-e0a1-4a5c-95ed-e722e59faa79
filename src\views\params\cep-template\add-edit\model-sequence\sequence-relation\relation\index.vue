<template>
  <div class="relation__container">
    <list-item v-for="(el, index) in data" :key="index" :data="el" :index="index" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import ListItem from './list-item.vue';

@Component({ components: { ListItem } })
export default class RelationA extends Vue {
  @Prop({ default: () => [] }) data!: any;
}
</script>
<style lang="scss" scoped>
.relation {
  &__container {
    padding: 0 2px;
    width: 632px;
    max-height: 300px;
    overflow-x: hidden !important;
    overflow-y: auto !important;
  }
}
</style>
