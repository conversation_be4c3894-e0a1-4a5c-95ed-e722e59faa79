import { Vue, Component } from 'vue-property-decorator';
import { State } from 'vuex-class';
import { closeRealTestSession, validateTest } from '@/apis/flowNewApi';
import uniqueId from 'lodash/uniqueId';
import { isEqual } from 'lodash';
import { SET_SESSION_WS_MAP, DEL_SESSION_WS_MAP } from '@/store/event-name';
import { MsgType } from '../interface';
import { transformLineErrorInfo } from '../utils';
@Component
export default class Operator extends Vue {
  showTestResultDrawer;
  testResultActiveTab;
  isRunning;
  testLog;
  testResultColumns;
  testResultTableData;
  sqlExcuteSub: any;

  @State((state) => state.job.sessionWsMap)
  sessionWsMap!: any;

  get curFlowId() {
    return this.$route.query.flowId as string;
  }
  // 启动会话
  async handleSessionConn() {
    const { success, msg, msgType, error } = await validateTest(this.curFlowId);
    if (!success) {
      if (msgType === MsgType.LINE_MESSAGE) {
        const lineErrors = JSON.parse(error || '[]');
        (this as any).flowSqlCode.setCodeError(transformLineErrorInfo(lineErrors));
      } else {
        msg && this.$message.error(msg);
      }
      return;
    }
    this.$store.commit(SET_SESSION_WS_MAP, this.curFlowId);
    this.testResultActiveTab = 'LOG';
    this.testLog = [];
    this.testResultColumns = [];
    this.testResultTableData = [];
    this.sessionWsMap.get(this.curFlowId).subscribe({
      next: (res) => {
        if (res.msgType === 'ERROR_LOG' || res.msgType === 'INFO_LOG') {
          this.showTestResultDrawer = this.isRunning = true;
          this.testLog.push({ ...res, frontUuid: uniqueId() });
        }
        if (res.msgType === 'GLOBAL_INFO') {
          this.$emit('sessionConn');
          this.$message.success(res.data);
        }
        if (res.msgType === 'GLOBAL_ERROR') {
          this.$emit('sessionConn');
          this.$message.error(res.data);
        }
      },
      complete: () => {
        this.isRunning = false;
        this.testResultActiveTab = 'LOG';
      }
    });
  }
  // 关闭会话
  async handleSessionStopConn() {
    const ws = this.sessionWsMap.get(this.curFlowId);
    if (ws) {
      ws.next({ msgType: 'CLOSE_SESSION' });
      this.$store.commit(DEL_SESSION_WS_MAP, this.curFlowId);
      this.showTestResultDrawer = this.isRunning = false;
    } else {
      const { msg, success } = await closeRealTestSession(this.curFlowId);
      this.showTestResultDrawer = this.isRunning = false;
      this.testLog = [];
      if (success) {
        this.$message.success(msg);
        return;
      }
      this.$message.error(msg);
    }
  }
  // 执行sql
  handleRealTest(selectionCode) {
    const ws = this.sessionWsMap.get(this.curFlowId);
    if (!ws || !this.isRunning) {
      this.$message.warning(this.$t('pa.flow.msg46'));
      return;
    }
    if (this.sqlExcuteSub) this.sqlExcuteSub.unsubscribe();
    ws.next({ msgType: 'EXECUTE', data: selectionCode });
    this.sqlExcuteSub = ws.subscribe(({ msgType, data }) => {
      if (msgType === 'RESULT') {
        this.testResultActiveTab = 'RESULT';
        const res = JSON.parse(data);
        if (
          res.columns &&
          !isEqual(
            res.columns,
            this.testResultColumns.map((el) => el.value)
          )
        ) {
          this.testResultColumns = res.columns.map((el) => {
            return { label: el, value: el };
          });
        }
        this.testResultTableData.push(res.result[0]);
      }
    });
    this.showTestResultDrawer = true;
  }

  handleStopExcute() {
    const ws = this.sessionWsMap.get(this.curFlowId);
    ws.next({ msgType: 'CANCEL' });
    const sub = ws.subscribe(({ msgType, data }) => {
      if (msgType === 'GLOBAL_ERROR') {
        this.$message.error(data);
      }
    });
    sub.unsubscribe();
  }
}
