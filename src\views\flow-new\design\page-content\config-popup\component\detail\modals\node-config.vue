<template>
  <bs-dialog
    :title="title + '组件配置'"
    :visible.sync="visible"
    :before-close="closeDialog"
    append-to-body
    width="60%"
  >
    <div style="margin-bottom: 50px">
      <el-collapse v-model="activeNames" v-loading="hiveComponentLoading">
        <el-form ref="ruleForm" :key="isHide" :model="formData" :rules="rules" label-width="120px">
          <el-collapse-item title="基本参数" name="1">
            <div v-for="(formItem, index) in formConf.forms" :key="index">
              <div v-if="formItem.itemType === undefined || formItem.itemType === 'base'">
                <el-form-item
                  v-show="showInitItem(formItem)"
                  v-if="formItem.layout === undefined"
                  :ref="formItem.model"
                  :label="formItem.label"
                  :prop="formItem.model"
                >
                  <div style="display: flex">
                    <div class="config-form-item" align="left" :style="formItem.divStyle">
                      <bs-select
                        v-if="isSelect(formItem.type, data.componentName, formItem.label)"
                        v-model="formData[formItem.model]"
                        class="btn"
                        style="width: 100%"
                        :confirm-when-deleting="formItem.componentCfg.multiple"
                        :placeholder="formItem.placeholder"
                        :clearable="formItem.componentCfg.clearable"
                        :multiple="formItem.componentCfg.multiple"
                        :show-all="formItem.componentCfg.multiple"
                        :filterable="formItem.componentCfg.filterable"
                        :remote-method="formItem.model.includes('subName') ? remoteMethod : null"
                        :loading="selectLoading"
                        :remote="formItem.model.includes('subName')"
                        :disabled="disabled"
                        :collapse-tags="!disabled"
                        :options="selectOptions(formItem.componentCfg)"
                        @focus="inputFocusHandle(formItem)"
                        @change="selChangeHandle($event, formItem)"
                        @visible-change="visibleChangeHandle($event, formItem)"
                        @clear="clearMethod(formItem)"
                      />
                      <bs-page-select
                        v-if="data.componentName === '数据库采集' && formItem.label === '输出字段'"
                        v-model="formData[formItem.model]"
                        class="btn"
                        style="width: 100%"
                        :confirm-when-deleting="formItem.componentCfg.multiple"
                        :placeholder="formItem.placeholder"
                        :clearable="formItem.componentCfg.clearable"
                        :multiple="formItem.componentCfg.multiple"
                        :show-all="formItem.componentCfg.multiple"
                        :filterable="formItem.componentCfg.filterable"
                        :loading="selectLoading"
                        :disabled="disabled"
                        :collapse-tags="!disabled"
                        :options="formItem.componentCfg.options"
                        @focus="inputFocusHandle(formItem)"
                        @change="selChangeHandle($event, formItem)"
                        @visible-change="visibleChangeHandle($event, formItem)"
                        @clear="clearMethod(formItem)"
                      >
                        <div slot-scope="{ item }" class="options">
                          <div
                            style="
                              display: flex;
                              justify-content: space-between;
                              width: 98%;
                              padding-left: 10px;
                            "
                          >
                            <div>{{ item.label }}</div>
                            <div>{{ item.type }}</div>
                          </div>
                        </div>
                      </bs-page-select>
                      <el-input
                        v-if="formItem.type === 'input'"
                        :id="'itemId_' + formItem.model"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        :placeholder="formItem.placeholder"
                        :maxlength="formItem.maxLength"
                        :readonly="formItem.readonly"
                        :clearable="true"
                        :disabled="disabled"
                        @blur="() => handleInputBlur(formItem)"
                      />
                      <bs-tag-input
                        v-if="formItem.type === 'tagInput'"
                        :id="'itemId_' + formItem.model"
                        :ref="formItem.model + index"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        separator=","
                        draggable
                        :placeholder="formItem.placeholder"
                        :readonly="formItem.readonly"
                        :validate-method="(val) => validatehttp(val)"
                        :disabled="disabled"
                        @change="(val) => tagChange(val, formItem.model + index)"
                      />
                      <el-input
                        v-if="formItem.type === 'textarea'"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        type="textarea"
                        :placeholder="formItem.placeholder"
                        :maxlength="formItem.maxLength"
                        :readonly="formItem.readonly"
                        :disabled="disabled"
                      />
                      <el-autocomplete
                        v-if="formItem.type === 'autocomplete'"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        :placeholder="formItem.placeholder"
                        :maxlength="formItem.maxLength"
                        :readonly="formItem.readonly"
                        :fetch-suggestions="querySearchHandle"
                        :clearable="true"
                        :placement="formItem.componentCfg.placement"
                        :disabled="disabled"
                        @focus="inputFocusHandle(formItem)"
                        @select="
                          selectFieldHandle($event, formItem, formItem.componentCfg.valueField)
                        "
                      >
                        <template slot-scope="{ item }">
                          <span style="font-weight: bold">{{
                            item[formItem.componentCfg.valueField]
                          }}</span>
                          <span style="float: right">{{
                            item[formItem.componentCfg.labelField]
                          }}</span>
                        </template>
                      </el-autocomplete>
                      <el-select
                        v-if="formItem.type === 'selectByInput'"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        :placeholder="formItem.placeholder"
                        clearable
                        :confirm-when-deleting="formItem.componentCfg.multiple"
                        :multiple="formItem.componentCfg.multiple"
                        :filterable="formItem.componentCfg.filterable"
                        :disabled="disabled"
                      >
                        <el-option
                          v-for="selItem in input"
                          :key="selItem[formItem.componentCfg.valueField]"
                          :value="selItem[formItem.componentCfg.valueField]"
                        />
                      </el-select>
                      <el-input-number
                        v-if="formItem.type === 'number'"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        :placeholder="formItem.placeholder"
                        :min="formItem.min"
                        :max="formItem.max"
                        :disabled="disabled"
                      />
                      <el-cascader
                        v-if="formItem.type === 'cascader'"
                        v-model="formData[formItem.model]"
                        :style="formItem.style"
                        :options="formItem.componentCfg.options"
                        :props="{ expandTrigger: 'hover' }"
                        :disabled="disabled"
                      />
                      <el-time-picker
                        v-if="formItem.type === 'time-picker'"
                        v-model="formData[formItem.model]"
                        :is-range="formItem.componentCfg.range"
                        :range-separator="formItem.componentCfg.rangeSeparator"
                        :start-placeholder="formItem.componentCfg.startPlaceholder"
                        :end-placeholder="formItem.componentCfg.endPlaceholder"
                        style="width: 100%"
                        :placeholder="formItem.placeholder"
                        :maxlength="formItem.maxLength"
                        :readonly="formItem.readonly"
                        :clearable="true"
                        :disabled="disabled"
                      />
                      <el-date-picker
                        v-if="formItem.type === 'date-picker'"
                        v-model="formData[formItem.model]"
                        :type="formItem.componentCfg.type"
                        :range-separator="formItem.componentCfg.rangeSeparator"
                        :start-placeholder="formItem.componentCfg.startPlaceholder"
                        :end-placeholder="formItem.componentCfg.endPlaceholder"
                        style="width: 100%"
                        :placeholder="formItem.placeholder"
                        :maxlength="formItem.maxLength"
                        :readonly="formItem.readonly"
                        :clearable="true"
                        :disabled="disabled"
                      />
                      <el-input
                        v-if="formItem.type === 'password'"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        type="password"
                        :placeholder="formItem.placeholder"
                        :maxlength="formItem.maxLength"
                        :readonly="formItem.readonly"
                        :clearable="true"
                        :disabled="disabled"
                      />
                      <div v-if="formItem.type !== 'tagInput'" style="color: #ff9e2b">
                        {{ hasSpace(formItem) }}
                      </div>
                    </div>
                    <div>
                      <el-tooltip
                        v-if="formItem.desc !== undefined"
                        class="item"
                        effect="light"
                        placement="bottom"
                      >
                        <span slot="content">
                          {{ formItem.desc }}
                        </span>
                        <span class="iconfont icon-wenhao" style="marginleft: 5px"></span>
                      </el-tooltip>
                    </div>
                  </div>
                </el-form-item>
                <!--特殊布局-->
                <el-form-item
                  v-if="formItem.layout !== undefined && formItem.layoutFirst"
                  v-show="show(formItem)"
                  :ref="formItem.model"
                  :label="formItem.label"
                  :prop="formItem.model"
                >
                  <div style="display: flex">
                    <div style="width: 90%">
                      <!--布局1-->
                      <div v-if="formItem.layout === 'layout1'" style="width: 100%; display: flex">
                        <div
                          v-for="(inputItem, inputIndex) in getLayoutItems(formItem.layoutModel)"
                          :key="inputIndex"
                          :style="inputItem.divStyle"
                        >
                          <div>{{ inputItem.label }}</div>
                          <div>
                            <el-form-item :prop="inputItem.model" :rules="inputItem.rules">
                              <el-input
                                v-if="inputItem.type === 'input'"
                                v-model="formData[inputItem.model]"
                                :style="inputItem.style"
                                :placeholder="inputItem.placeholder"
                                :maxlength="inputItem.maxLength"
                                :readonly="inputItem.readonly"
                                :clearable="true"
                                :disabled="disabled"
                              />
                              <bs-select
                                v-if="inputItem.type === 'select'"
                                v-model="formData[inputItem.model]"
                                style="width: 100%"
                                :placeholder="inputItem.placeholder"
                                :clearable="inputItem.componentCfg.clearable"
                                :multiple="inputItem.componentCfg.multiple"
                                :show-all="inputItem.componentCfg.multiple"
                                collapse-tags
                                :filterable="inputItem.componentCfg.filterable"
                                :disabled="disabled"
                                :loading="selectLoading"
                                :options="selectOptions(inputItem.componentCfg)"
                                @change="selChangeHandle($event, inputItem)"
                                @visible-change="visibleChangeHandle($event, inputItem)"
                                @focus="inputFocusHandle(inputItem)"
                              />
                            </el-form-item>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <el-tooltip
                        v-if="formItem.desc !== undefined"
                        class="item"
                        effect="light"
                        placement="bottom"
                      >
                        <span slot="content">
                          {{ formItem.desc }}
                        </span>
                        <span class="iconfont icon-wenhao" style="marginleft: 5px"></span>
                      </el-tooltip>
                    </div>
                  </div>
                </el-form-item>
              </div>
            </div>
            <hive-table
              v-if="hiveTableVisible"
              ref="hiveTable"
              :data="hiveFieldInfoData"
              :pre-output="preOutputFields"
              :head-title="getFieldInfoDataParams.level1"
              :field-list="fieldList"
              :sub-name="subName"
            />
          </el-collapse-item>
          <el-collapse-item v-if="showExportFiels.includes(data.className)" name="4">
            <div slot="title" class="nums">
              <div>
                输出字段(
                <span style="color: #0096f0">{{ outputFieldCount }}</span>
                )
              </div>
              <div>
                <el-tooltip class="item" effect="light" placement="bottom">
                  <span slot="content">
                    组件的输出字段=自定义字段+选中的上游输入字段<br />
                    顺序:自定义字段>上游输入字段
                  </span>
                  <span class="iconfont icon-wenhao" style="margin-right: 5px"></span>
                </el-tooltip>
              </div>
            </div>
            <div
              v-for="(formItem, index) in formConf.forms.filter(
                (item) => item.type === 'tagInput' && item.show
              )"
              :key="index"
            >
              <div v-if="formItem.itemType === undefined || formItem.itemType === 'base'">
                <el-form-item
                  v-show="show(formItem) && formItem.label === '自定义字段'"
                  v-if="formItem.layout === undefined"
                  :ref="formItem.model"
                  :label="formItem.label"
                  :prop="formItem.model"
                  :rules="[
                    { required: true, message: '请输入自定义字段', trigger: 'change' },
                    { validator: validateTag, trigger: 'change' }
                  ]"
                >
                  <div style="display: flex">
                    <div class="config-form-item" align="left" :style="formItem.divStyle">
                      <bs-tag-input
                        v-if="formItem.type === 'tagInput'"
                        :id="'itemId_' + formItem.model"
                        :ref="formItem.model + index"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        separator=","
                        :mincollapsed-num="4"
                        draggable
                        placeholder="支持黏贴多个字段，回车后自动输入，默认采用英文逗号分隔"
                        :readonly="formItem.readonly"
                        :disabled="disabled"
                        :validate-method="(val) => validateMethod(val, formData[formItem.model])"
                        @change="(val) => tagChange(val, formItem.model + index)"
                      />
                    </div>
                    <div>
                      <span
                        v-if="formItem.label === '自定义字段'"
                        class="trans"
                        @click="transModalShow(formItem.model)"
                      >
                        字段转换
                      </span>
                    </div>
                  </div>
                </el-form-item>
                <!--特殊布局-->
                <el-form-item
                  v-if="formItem.layout !== undefined && formItem.layoutFirst"
                  v-show="show(formItem)"
                  :ref="formItem.model"
                  :label="formItem.label"
                  :prop="formItem.model"
                >
                  <div style="display: flex">
                    <div style="width: 90%">
                      <!--布局1-->
                      <div v-if="formItem.layout === 'layout1'" style="width: 100%; display: flex">
                        <div
                          v-for="(inputItem, inputIndex) in getLayoutItems(formItem.layoutModel)"
                          :key="inputIndex"
                          :style="inputItem.divStyle"
                        >
                          <div>{{ inputItem.label }}</div>
                          <div>
                            <el-form-item :prop="inputItem.model" :rules="inputItem.rules">
                              <el-input
                                v-if="inputItem.type === 'input'"
                                v-model="formData[inputItem.model]"
                                :style="inputItem.style"
                                :placeholder="inputItem.placeholder"
                                :maxlength="inputItem.maxLength"
                                :readonly="inputItem.readonly"
                                :clearable="true"
                                :disabled="disabled"
                              />

                              <bs-select
                                v-if="inputItem.type === 'select'"
                                v-model="formData[inputItem.model]"
                                style="width: 100%"
                                :placeholder="inputItem.placeholder"
                                :clearable="inputItem.componentCfg.clearable"
                                :multiple="inputItem.componentCfg.multiple"
                                :show-all="inputItem.componentCfg.multiple"
                                collapse-tags
                                :filterable="inputItem.componentCfg.filterable"
                                :disabled="disabled"
                                :loading="selectLoading"
                                :options="selectOptions(inputItem.componentCfg)"
                                @change="selChangeHandle($event, inputItem)"
                                @visible-change="visibleChangeHandle($event, inputItem)"
                                @focus="inputFocusHandle(inputItem)"
                              />
                            </el-form-item>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <el-tooltip
                        v-if="formItem.desc !== undefined"
                        class="item"
                        effect="light"
                        placement="bottom"
                      >
                        <span slot="content">
                          {{ formItem.desc }}
                        </span>
                        <span class="iconfont icon-wenhao" style="marginleft: 5px"></span>
                      </el-tooltip>
                    </div>
                  </div>
                </el-form-item>
              </div>
            </div>
            <div style="display: flex">
              <div v-show="showFieldList" class="config-form-item" align="left">
                <el-form-item label="上游输入字段" label-width="120px">
                  <bs-page-select
                    v-model="pageSelect"
                    class="btn"
                    style="width: 102%"
                    placeholder="请选择"
                    filterable
                    :disabled="disabled"
                    :options="seOptions"
                    :collapse-tags="!disabled"
                  >
                    <div slot-scope="{ item }" class="options">
                      <div
                        style="
                          display: flex;
                          justify-content: space-between;
                          width: 98%;
                          padding-left: 10px;
                        "
                      >
                        <div>{{ item.label }}</div>
                        <div>{{ item.type }}</div>
                      </div>
                    </div>
                  </bs-page-select>
                </el-form-item>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="isHiveOutputComponent" title="HIVE配置参数">
            <el-button
              :disabled="disabled"
              style="float: right; margin-bottom: 16px"
              @click="addHiveConfigDataRow"
            >
              添加行
            </el-button>
            <el-table :data="hiveConfigData" style="width: 100%" stripe size="mini" border>
              <el-table-column label="键" prop="key">
                <template slot-scope="{ row }">
                  <el-input v-if="row.edit" v-model="row.key" class="table-input" />
                  <span v-else>{{ row.key }}</span>
                </template>
              </el-table-column>
              <el-table-column label="值" prop="value">
                <template slot-scope="{ row }">
                  <el-input v-if="row.edit" v-model="row.value" class="table-input" />
                  <span v-else>{{ row.value }}</span>
                </template>
              </el-table-column>
              <el-table-column
                fixed="right"
                label="操作"
                width="120"
                header-align="center"
                align="center"
              >
                <template slot-scope="{ row, $index }">
                  <el-button
                    v-if="row.edit"
                    type="text"
                    size="small"
                    :disabled="disabled"
                    @click.native="rowConfig('update', row, $index)"
                  >
                    更新
                  </el-button>
                  <el-button
                    v-if="row.edit"
                    type="text"
                    size="small"
                    :disabled="disabled"
                    @click.native="rowConfig('cancel', row)"
                  >
                    取消
                  </el-button>
                  <el-button
                    v-if="!row.edit"
                    type="text"
                    size="small"
                    :disabled="disabled"
                    @click.native="rowConfig('edit', row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    v-if="!row.edit"
                    type="text"
                    size="small"
                    :disabled="disabled"
                    @click.native="rowConfig('delete', row, $index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item v-show="hasAdvance(formConf.forms)" title="高级参数" name="2">
            <div v-for="(formItem, index) in formConf.forms" :key="index">
              <div v-if="formItem.itemType && formItem.itemType === 'advance'">
                <el-form-item
                  v-show="show(formItem)"
                  v-if="formItem.layout === undefined"
                  :ref="formItem.model"
                  :label="formItem.label"
                  :prop="formItem.model"
                >
                  <div style="display: flex">
                    <div class="config-form-item" align="left" :style="formItem.divStyle">
                      <bs-select
                        v-if="formItem.type === 'select'"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        :placeholder="formItem.placeholder"
                        :confirm-when-deleting="formItem.componentCfg.multiple"
                        :clearable="formItem.componentCfg.clearable"
                        :multiple="formItem.componentCfg.multiple"
                        :show-all="formItem.componentCfg.multiple"
                        collapse-tags
                        :filterable="formItem.componentCfg.filterable"
                        :loading="selectLoading"
                        :disabled="disabled"
                        :options="selectOptions(formItem.componentCfg)"
                        @change="selChangeHandle($event, formItem)"
                        @visible-change="visibleChangeHandle($event, formItem)"
                        @focus="inputFocusHandle(formItem)"
                      />
                      <el-input
                        v-if="formItem.type === 'input'"
                        :id="'itemId_' + formItem.model"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        :placeholder="formItem.placeholder"
                        :maxlength="formItem.maxLength"
                        :readonly="formItem.readonly"
                        :clearable="true"
                        :disabled="disabled || checkValueType(formItem)"
                      />
                      <el-switch
                        v-if="formItem.valueType && formItem.valueType === 1"
                        v-model="formData[makeSwitchModelName(formItem.model)]"
                        active-text="自定义"
                        inactive-text="默认"
                      />
                      <el-input
                        v-if="formItem.type === 'textarea'"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        type="textarea"
                        :placeholder="formItem.placeholder"
                        :maxlength="formItem.maxLength"
                        :readonly="formItem.readonly"
                        :disabled="disabled"
                      />
                      <el-autocomplete
                        v-if="formItem.type === 'autocomplete'"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        :placeholder="formItem.placeholder"
                        :maxlength="formItem.maxLength"
                        :readonly="formItem.readonly"
                        :fetch-suggestions="querySearchHandle"
                        :clearable="true"
                        :placement="formItem.componentCfg.placement"
                        :disabled="disabled"
                        @focus="inputFocusHandle(formItem)"
                        @select="
                          selectFieldHandle($event, formItem, formItem.componentCfg.valueField)
                        "
                      >
                        <template slot-scope="{ item }">
                          <span style="font-weight: bold">{{
                            item[formItem.componentCfg.valueField]
                          }}</span>
                          <span style="float: right">{{
                            item[formItem.componentCfg.labelField]
                          }}</span>
                        </template>
                      </el-autocomplete>
                      <el-select
                        v-if="formItem.type === 'selectByInput'"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        :placeholder="formItem.placeholder"
                        clearable
                        :multiple="formItem.componentCfg.multiple"
                        :filterable="formItem.componentCfg.filterable"
                        :disabled="disabled"
                      >
                        <el-option
                          v-for="selItem in input"
                          :key="selItem[formItem.componentCfg.valueField]"
                          :value="selItem[formItem.componentCfg.valueField]"
                        />
                      </el-select>
                      <el-input-number
                        v-if="formItem.type === 'number'"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        :placeholder="formItem.placeholder"
                        :min="formItem.min"
                        :max="formItem.max"
                        :disabled="disabled"
                      />
                      <el-cascader
                        v-if="formItem.type === 'cascader'"
                        v-model="formData[formItem.model]"
                        :style="formItem.style"
                        :options="formItem.componentCfg.options"
                        :props="{ expandTrigger: 'hover' }"
                        :disabled="disabled"
                      />
                      <el-time-picker
                        v-if="formItem.type === 'time-picker'"
                        v-model="formData[formItem.model]"
                        :is-range="formItem.componentCfg.range"
                        :range-separator="formItem.componentCfg.rangeSeparator"
                        :start-placeholder="formItem.componentCfg.startPlaceholder"
                        :end-placeholder="formItem.componentCfg.endPlaceholder"
                        style="width: 100%"
                        :placeholder="formItem.placeholder"
                        :maxlength="formItem.maxLength"
                        :readonly="formItem.readonly"
                        :clearable="true"
                        :disabled="disabled"
                      />
                      <el-date-picker
                        v-if="formItem.type === 'date-picker'"
                        v-model="formData[formItem.model]"
                        :type="formItem.componentCfg.type"
                        :range-separator="formItem.componentCfg.rangeSeparator"
                        :start-placeholder="formItem.componentCfg.startPlaceholder"
                        :end-placeholder="formItem.componentCfg.endPlaceholder"
                        style="width: 100%"
                        :placeholder="formItem.placeholder"
                        :maxlength="formItem.maxLength"
                        :readonly="formItem.readonly"
                        :clearable="true"
                        :disabled="disabled"
                      />
                      <el-input
                        v-if="formItem.type === 'password'"
                        v-model="formData[formItem.model]"
                        style="width: 100%"
                        type="password"
                        :placeholder="formItem.placeholder"
                        :maxlength="formItem.maxLength"
                        :readonly="formItem.readonly"
                        :clearable="true"
                        :disabled="disabled"
                      />
                    </div>
                    <div>
                      <el-tooltip
                        v-if="formItem.desc !== undefined"
                        class="item"
                        effect="light"
                        placement="bottom"
                      >
                        <span slot="content">
                          {{ formItem.desc }}
                        </span>
                        <span class="iconfont icon-wenhao" style="marginleft: 5px"></span>
                      </el-tooltip>
                    </div>
                  </div>
                </el-form-item>
                <!--特殊布局-->
                <el-form-item
                  v-if="formItem.layout !== undefined && formItem.layoutFirst"
                  v-show="show(formItem)"
                  :ref="formItem.model"
                  :label="formItem.label"
                  :prop="formItem.model"
                >
                  <div style="display: flex">
                    <div style="width: 90%">
                      <!--布局1-->
                      <div v-if="formItem.layout === 'layout1'" style="width: 100%; display: flex">
                        <div
                          v-for="(inputItem, inputIndex) in getLayoutItems(formItem.layoutModel)"
                          :key="inputIndex"
                          :style="inputItem.divStyle"
                        >
                          <div>{{ inputItem.label }}</div>
                          <div>
                            <el-form-item :prop="inputItem.model" :rules="inputItem.rules">
                              <el-input
                                v-if="inputItem.type === 'input'"
                                v-model="formData[inputItem.model]"
                                :style="inputItem.style"
                                :placeholder="inputItem.placeholder"
                                :maxlength="inputItem.maxLength"
                                :readonly="inputItem.readonly"
                                :clearable="true"
                                :disabled="disabled"
                              />

                              <bs-select
                                v-if="inputItem.type === 'select'"
                                v-model="formData[inputItem.model]"
                                style="width: 100%"
                                :confirm-when-deleting="formItem.componentCfg.multiple"
                                :placeholder="inputItem.placeholder"
                                :clearable="inputItem.componentCfg.clearable"
                                :multiple="inputItem.componentCfg.multiple"
                                :show-all="inputItem.componentCfg.multiple"
                                collapse-tags
                                :filterable="inputItem.componentCfg.filterable"
                                :disabled="disabled"
                                :loading="selectLoading"
                                :options="selectOptions(inputItem.componentCfg)"
                                @change="selChangeHandle($event, inputItem)"
                                @visible-change="visibleChangeHandle($event, inputItem)"
                                @focus="inputFocusHandle(inputItem)"
                              />
                            </el-form-item>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <el-tooltip
                        v-if="formItem.desc !== undefined"
                        class="item"
                        effect="light"
                        placement="bottom"
                      >
                        <span slot="content">
                          {{ formItem.desc }}
                        </span>
                        <span class="iconfont icon-wenhao" style="marginleft: 5px"></span>
                      </el-tooltip>
                    </div>
                  </div>
                </el-form-item>
              </div>
            </div>
          </el-collapse-item>
        </el-form>

        <!-- 动态参数 -->
        <div v-if="formConf.custom">
          <el-collapse-item title="自定义参数" name="3">
            <CustomFieldList
              :data="tableData"
              :disabled="disabled"
              @change="handleCustomFieldChange"
            />
          </el-collapse-item>
        </div>
      </el-collapse>
    </div>
    <div slot="footer" class="dialog-footer">
      <div style="display: flex; justify-content: space-between">
        <div style="width: 60%">
          <el-switch
            v-model="jobNode.printLog"
            :disabled="disabled"
            style="display: block; float: left"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="开启日志"
            inactive-text="关闭日志"
          />
        </div>
        <div>
          <el-button @click="closeDialog(false)">取消</el-button>
          <el-button v-if="!disabled" type="primary" @click="submit('ruleForm')">确定</el-button>
        </div>
      </div>
    </div>
    <FieldTrans
      v-if="transVisible"
      :show.sync="transVisible"
      :only-csv="isConvertTuple2Tuple"
      :info="transData"
      :disabled="disabled"
      :inputs="input"
      @close="transVisible = false"
      @trans="transValue"
    />
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import * as _ from 'lodash';
import HiveTable from './components/hive-table.vue';
import FieldTrans from '../modals/components/field-trans.vue';
import { getHiveInfo } from '@/apis/flowApi';
import CustomFieldList from './components/custom-field-list.vue';

const _PRIMARY_ID = '_primaryId';
const FIELD_REG = /^[A-Za-z0-9-_!@#￥%&*()]+$/;
@Component({
  components: {
    HiveTable,
    FieldTrans,
    CustomFieldList // 自定义参数
  }
})
export default class NodeConfig extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop() versionComponentList!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: false }) disabled!: boolean;
  private title = '';
  private formData: any = {};
  private rules: any = {};
  private formLabelWidth = '50px';
  private formConf: any = {};
  private jobNode: any = {};
  private input: any = [];
  private output: any = [];
  private tableData: any = [];
  private focusModel = '';
  private selectLoading = false;
  private formConfBak: any = {};
  private initComplete = false;
  private activeNames: string[] = ['1', '4'];
  private userName: string = this.$store.state.userInfo.userName;
  // 获取表字段信息接口传参
  private getFieldInfoDataParams: any = {};
  // 上游节点输出字段
  private preOutputFields: any[] = [];
  private hiveFieldInfoData: any[] = [];
  private hiveComponentLoading = false;
  private showHiveTable = false;
  private fieldList: any[] = [];
  private subName = '';
  private hiveConfigData: object[] = [];
  private repeatInfo = '';
  //显隐变化的标记
  private isHide = false;
  // 是否展示转换弹窗
  private transVisible = false;
  private fieldName = ''; // 转换字段名字
  //翻页下拉选择数据处理
  private pageSeData: any = [];
  private pageSelect: any = []; // 上游输入字段值
  private seOptions: any = []; // 上游输入字段选项
  private customFieldCount = 0; // 自定义输出字段总数
  private transData: any = []; // 转换恢复字段
  private optionData: any = [];
  autoMatch = false;
  //判断是否展示输入模块
  showExportFiels = [
    'cn.com.bsfit.pipeace.component.process.http.json.HttpJSONProcessComponent', // httpjson查找
    'cn.com.bsfit.pipeace.component.redis.async.RedisAsyncProcessComponent', // redies异步查找
    'cn.com.bsfit.pipeace.component.process.convert.ConvertComponent', // 转换
    'cn.com.bsfit.pipeace.component.flink.hbase.async.HbaseAsyncProcessComponent', // Hbase异步查询
    'cn.com.bsfit.pipeace.component.process.jdbc.async.JdbcAsyncQueryComponent', // 数据库异步查询组件
    'cn.com.bsfit.pipeace.component.flatmap.CsvFlatMap', // 1 -> N
    'cn.com.bsfit.pipeace.component.process.http.HttpProcessComponent', // HTTP 查找
    'cn.com.bsfit.pipeace.component.process.aerospike.AerospikeProcessComponent', // AS查找
    'cn.com.bsfit.pipeace.component.process.mapping.JsonDynamicMappingComponent', // JSON动态映射
    'cn.com.bsfit.pipeace.iplocation.IpLocationProcessComponent'
  ];
  // 判断是否为HIVE输出组件
  get isHiveOutputComponent() {
    return this.data.componentName === 'HIVE输出组件';
  }
  get hiveTableVisible() {
    return this.isHiveOutputComponent && this.showHiveTable;
  }
  get transferHiveConfigData() {
    return this.hiveConfigData
      .filter((el: any) => el.key !== '' && el.value !== '')
      .map((el: any) => {
        return { [el.key]: el.value };
      });
  }
  // 是否在转换组件情况的字段顺序调整下
  // 自定义字段只支持csv
  // 不显示上游输入字段
  get isConvertTuple2Tuple() {
    return this.data.type === 'CONVERT' && this.formData.type === 'TUPLE_2_TUPLE';
  }

  // 输出字段总和
  get outputFieldCount() {
    return this.customFieldCount + this.pageSelect.length;
  }
  // 展示转换弹窗并替换
  transModalShow(val) {
    this.transVisible = true;
    this.transData = this.formData[val];
    this.fieldName = val;
  }
  // 保存转换字段
  transValue(val) {
    if (!this.formData[this.fieldName] || typeof this.formData[this.fieldName] === 'string') {
      this.$set(this.formData, this.fieldName, []);
    }
    if (Array.isArray(val)) {
      this.customFieldCount = val.length;
      this.$set(this.formData, this.fieldName, val);
    }
  }
  //输入字段校验逻辑
  validatehttp(val) {
    if (val.match(FIELD_REG)) {
      const index = this.useList.findIndex((item) => item === val);
      if (index > 0) {
        return false;
      }
      return true;
    } else {
      return false;
    }
  }
  // 自定义字段校验逻辑
  useList: any = [];
  validateMethod(val) {
    // 校验是否与上游字段重复
    if (
      !this.isConvertTuple2Tuple &&
      this.jobNode.inputFields.map(({ name }) => name).includes(val)
    ) {
      return false;
    }
    if (this.isConvertTuple2Tuple) {
      const fields = this.input.map((e) => e.name);
      if (!fields.includes(val)) {
        return false;
      }
    }
    if (val.match(FIELD_REG)) {
      const le = this.useList.filter((item) => item === val) || [];
      if (le.length > 1) {
        return false;
      }
      return true;
    } else {
      return false;
    }
  }
  tagChange(val, model) {
    this.useList = val;
    this.customFieldCount = val.length;
    (this.$refs[model] as any)[0].validate();
  }
  validateTag(rule, value, callback) {
    !value && callback(new Error('请填写自定义字段'));
    if (value && value.length) {
      // 非法字符校验
      const str = value.join('');
      if (str && !str.match(FIELD_REG))
        callback(new Error('标签不可以包含特殊字符，仅可以包含字母，数字，_-!@#￥%&*()'));

      // 文件采集、HTTP输入组件只能输入一个输出字段
      if (['文件采集', 'HTTP 输入'].includes(this.data.componentName)) {
        value.length > 1 && callback(new Error('只能输入一个输出字段'));
      }

      // 重复字段校验
      const uniqueValue = [...new Set(value)];
      uniqueValue.length < value.length && callback(new Error('不能输入重复字段'));

      // 上游重复字段校验
      if (
        !this.isConvertTuple2Tuple &&
        !value.every((el) => !this.jobNode.inputFields.map(({ name }) => name).includes(el))
      ) {
        callback(new Error('自定义字段不能与上游输入字段重复'));
      }
      // 转换组件 顺序调整不能出现新字段
      if (this.isConvertTuple2Tuple) {
        const fields = this.input.map((e) => e.name);
        const newFields = value.filter((el) => !fields.includes(el));
        if (newFields.length === 0) {
          callback();
        } else {
          callback(new Error(`字段${newFields.join('、')}不存在`));
        }
      }
    }
    callback();
  }
  hasAdvance(forms) {
    return _.filter(forms, { itemType: 'advance' }).length > 0;
  }
  makeSwitchModelName(model) {
    return model + '_switch';
  }
  checkValueType(formItem) {
    const name = this.makeSwitchModelName(formItem.model);
    const switchItem = this.formData[name];
    if (switchItem === undefined) {
      this.$set(this.formData, name, false);
    }
    return formItem.valueType !== undefined && formItem.valueType === 1 && !switchItem;
  }
  getLayoutItems(models) {
    const list: any = [];
    if (models) {
      models.forEach((n) => {
        const rec = _.find(this.formConf.forms, { model: n });
        if (rec) {
          list.push(_.cloneDeep(rec));
        }
      });
    }
    return list;
  }
  selectOptions(config) {
    if (!config.groupable) {
      return config.options.map((item) => {
        return {
          label: item[config.labelField] || item[config.valueField],
          value: item[config.valueField],
          type: item['title']
        };
      });
    } else {
      return config.options.map((item) => {
        const list = item.options.filter((it) => it[config.valueField] !== '');
        return {
          ...item,
          options: list.map((tr) => {
            return {
              label: tr[config.labelField],
              value: `${tr[config.valueField]}${this.getOptionId(tr)}`
            };
          })
        };
      });
    }
  }
  makeReqParam(cfg) {
    const params = {};
    if (cfg.reqParamsField !== undefined) {
      cfg.reqParamsField.forEach((n) => {
        this.$set(params, n, this[n]);
      });
    }
    if (cfg.dependsOn) {
      cfg.dependsOn.forEach((n) => {
        this.$set(params, n, this.formData[n]);
      });
    }
    return params;
  }
  selectFieldHandle(val, row, valueField) {
    this.$set(this.formData, row.model, val[valueField]);
  }
  inputFocusHandle(item) {
    // 缓存key属性设置
    if (item.model === 'key' && this.formData.key) {
      const res = item.componentCfg.options.some((el) => el.name === this.formData.key);
      if (!res) this.formData.key = '';
    }
    this.focusModel = item.model;
    if (item.type === 'autocomplete') {
      if (_.toString(item.componentCfg.remoteUrl) !== '') {
        const params = this.makeReqParam(item.componentCfg);
        this.doGet(item.componentCfg.remoteUrl, {
          params: params
        }).then((resp: any) => {
          if (resp.success) {
            item.componentCfg.options = resp.data;
          }
        });
      } else {
        const cfg = item.componentCfg;
        // 把配置字段作为下拉选项
        cfg.useInputAndTarget && this.$set(cfg, 'options', this.optionData);
        cfg.useTarget && this.$set(cfg, 'options', _.filter(this.optionData, { disabled: false }));
        cfg.useInput && this.$set(cfg, 'options', this.input);
      }
    }
  }

  clearMethod({ label }) {
    if (label === 'VirtualHost') {
      this.formData.queueName = '';
    }
  }

  visibleChangeHandle(event, item) {
    if (event) {
      const cfg = item.componentCfg;
      if (_.toString(cfg.remoteUrl) !== '') {
        this.selectLoading = true;
        // 请求接口
        const params = this.makeReqParam(item.componentCfg);
        this.doGet(cfg.remoteUrl, { params: params }).then((resp: any) => {
          if (resp.success) {
            if (item.label === 'queueName') {
              /* rabbitmq组件单独处理 */
              const { VirtualHost } = this.formData;
              const { componentCfg } = this.formConf.forms.find((el) => el.label === 'queueName');
              componentCfg.options = [];
              resp.data.forEach((el) => {
                if (el.virtualHost === VirtualHost) {
                  el.queueList.forEach((ele) => {
                    componentCfg.options.push({
                      queueList: ele
                    });
                  });
                }
              });
            } else if (this.data.componentName === '数据库采集' && item.label === '输出字段') {
              const { componentCfg } = this.formConf.forms.find((el) => el.label === '输出字段');
              componentCfg.options = [];
              resp.data.forEach((el) => {
                componentCfg.options.push({
                  label: el.subName,
                  value: el.subName,
                  type: el.title
                });
              });
            } else {
              cfg.options = resp.data;
            }
            // 分组
            if (_.toString(cfg.groupBy) !== '') {
              const groupOpts: any = [];
              const groupBy = cfg.groupBy;
              const groupObj = _.groupBy(cfg.options, groupBy);
              for (const key of Object.keys(groupObj)) {
                if (_.filter(groupObj[key], { subName: '' }).length === 0) {
                  groupOpts.push({
                    label: key,
                    options: groupObj[key]
                  });
                }
              }
              this.$set(cfg, 'options', groupOpts);
              this.$set(cfg, 'groupable', true);
            } else {
              this.$set(cfg, 'groupable', false);
            }
          }
          this.selectLoading = false;
        });
      }
      // 把配置字段作为下拉选项
      cfg.useInputAndTarget && this.$set(cfg, 'options', this.optionData);
      // 把配置字段作为下拉选项
      cfg.useTarget && this.$set(cfg, 'options', _.filter(this.optionData, { disabled: false }));
    }
  }
  remoteMethod(query) {
    if (query !== '') {
      const rec: any = _.find(this.formConf.forms, { model: this.focusModel });
      if (rec) {
        const cfg = rec.componentCfg;
        if (_.toString(cfg.remoteUrl) !== '') {
          this.selectLoading = true;
          // 请求接口
          const params: any = this.makeReqParam(rec.componentCfg);
          params.subName = query;
          this.doGet(cfg.remoteUrl, { params: params }).then((resp: any) => {
            if (resp.success) {
              cfg.options = resp.data;
            }
            this.selectLoading = false;
          });
        }
      }
    }
  }
  getOptionId(selItem) {
    if (selItem.id !== undefined) {
      return ',' + selItem.id;
    }
    return '';
  }
  selChangeHandle(val2, item) {
    // 设置一个变量用于存放下拉框数据的id
    const array = _.split(val2, ',');
    let val = '';
    if (array.length === 2 && !item.componentCfg.multiple) {
      const idModel = item.model + _PRIMARY_ID;
      // 格式:数据值,id
      this.formData[idModel] = val2;
      val = array[1];
    } else {
      val = array[0];
    }
    /* rabbitmq组件单独处理 */
    if (item.label === 'VirtualHost') {
      this.$set(this.formData, 'queueName', '');
    }
    if (item.componentCfg.fill.length > 0) {
      const p = {};
      if (array.length === 2) {
        const _ID = 'id';
        p[_ID] = val;
      } else {
        p[item.componentCfg.valueField] = val;
      }
      let data = [];
      if (item.componentCfg.groupable) {
        item.componentCfg.options.forEach((o) => {
          data = _.union(data, o.options);
        });
      } else {
        data = item.componentCfg.options;
      }
      const rec: any = _.find(data, p);
      if (rec !== undefined) {
        item.componentCfg.fill.forEach((n) => {
          if (rec[n.fromField]) {
            this.$set(this.formData, n.toModel, rec[n.fromField]);
            this.$forceUpdate();
          } else {
            // 从resProperty中获取
            try {
              const resProperty = JSON.parse(rec.resProperty);
              this.$set(this.formData, n.toModel, resProperty[n.fromField]);
            } catch (e) {
              // donothing
            }
          }
        });
        // 显示和隐藏
        this.hideEl(item, val);
      }
    } else {
      this.hideEl(item, val);
    }
    // 查询其他文本框是否依赖于当前下拉框，数据重置
    this.formConf.forms.forEach((n) => {
      if (n.componentCfg && n.componentCfg.dependsOn) {
        n.componentCfg.dependsOn.forEach((m) => {
          if (m === item.model && item.model !== n.model) {
            if (n.model === 'subName') {
              this.$set(this.formData, n.model, undefined);
            } else {
              delete this.formData[n.model];
            }
          }
        });
      }
    });
    if (this.isHiveOutputComponent && item.model === 'subName') {
      if (val2 === '') {
        this.showHiveTable = false;
        return;
      } else {
        this.showHiveTable = true;
      }
      const { resId, subName } = this.formData;
      this.getFieldInfoDataParams = {
        level1: subName,
        resId
      };
      this.getHiveFieldInfoData();
    }
    // 转换组件的顺序调整特殊处理
    if (this.data.type === 'CONVERT') {
      const target = this.formConf.forms.find((n) => n.type === 'tagInput' && n.show);
      if (!target) return;
      if (this.formData[item.model] === 'TUPLE_2_TUPLE') {
        this.$set(
          this.formData,
          target.model,
          this.input.map((e) => e.name)
        );
      }
      this.customFieldCount = (this.formData[target.model] || []).length;
    }
  }

  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    this.jobNode = {};
    this.tableData = [];
    this.formData = {};
    this.selectLoading = false;
    this.initComplete = false;
    this.activeNames = ['1', '4'];
    return { needUpdate, jobNode };
  }
  setScriptDefaultValue(needUpdate: boolean) {
    /* eslint-disable-next-line */
    const that = this;
    if (
      this.visible &&
      this.initComplete &&
      this.formConf.customConf &&
      this.formConf.customConf !== null
    ) {
      this.formConf.customConf.forEach((n) => {
        const td: any = {
          name: n.name,
          nameDesc: n.nameDesc,
          valueDesc: n.valueDesc
        };
        const rec: any = _.find(this.tableData, { name: n.name });
        if (rec === undefined) {
          if (n.valueType === 0) {
            // 常量
            td.value = n.value;
          } else if (n.valueType === 1) {
            // 脚本
            /* eslint-disable-next-line */
            td.value = eval(n.script);
            this.tableData.push(td);
          }
        } else {
          if (needUpdate && n.valueType === 1) {
            // 脚本
            /* eslint-disable-next-line */
            rec.value = eval(n.script);
          }
        }
      });
    }
    if (this.visible && this.initComplete) {
      if (this.formConf && this.formConf.forms) {
        this.formConf.forms.forEach((n) => {
          if (n.valueType === 1) {
            // 找出switch
            const switchName = this.makeSwitchModelName(n.model);
            if (!this.formData[switchName]) {
              if (_.toString(this.formData[n.model]) === '') {
                /* eslint-disable-next-line */
                this.$set(this.formData, n.model, eval(n.script));
              } else {
                if (needUpdate) {
                  /* eslint-disable-next-line */
                  this.$set(this.formData, n.model, eval(n.script));
                }
              }
            }
          }
        });
      }
    }
  }
  submit(formName: string) {
    if (this.isHiveOutputComponent) {
      const data = (this.$refs as any).hiveTable['hiveFieldInfoData'];
      const isNotSelect = data.every((el) => {
        return el.inputFieldName === '';
      });
      if (isNotSelect) {
        this.$message.error('请至少选择一项上游输入字段');
        return;
      }
    }
    if (this.repeatInfo) {
      this.$message.error('请处理错误提示信息，再提交');
      return;
    }
    // const ref: any = this.$refs.NodeFieldList;
    // const result = ref.getJobNode();
    // if (result === null) {
    //   return;
    // }
    let canSubmit = true;
    const targetList: any = [];
    const outputFields: any = [];
    this.formConf.forms.forEach((n) => {
      const str = this.formData[n.model];
      if (n.componentCfg && n.componentCfg.useTarget && n.type !== 'tagInput') {
        _.split(str, ',').forEach((f) => {
          if (f !== '') {
            targetList.push({
              name: f.trim(),
              formElName: n.label
            });
          }
        });
      } else if (n.type === 'tagInput' && n.show) {
        // this.formData[n.model] = this.formData[n.model].join(',');
        Array.isArray(str) &&
          str.forEach((item) => {
            outputFields.push({
              name: item,
              type: 'String',
              outputable: true,
              targetable: true
            });
          });
      }
    });
    // keyBy和水位线 特殊处理 输入转输出
    if (this.data.type === 'KEY_BY_OR_WATER_MARK') {
      outputFields.push(
        ...this.data.inputFields.map(({ name, type = 'String' }) => ({
          name,
          type,
          outputable: true,
          targetable: true
        }))
      );
    }
    if (targetList.length) {
      for (const t of Object.keys(targetList)) {
        const obj: any = targetList[t];
        if (_.findIndex(this.input, { name: obj.name }) >= 0) {
          this.$tip.warning(
            '基本参数中[' + targetList[t].formElName + ']所填字段名与字段配置中的字段有重复'
          );
          canSubmit = false;
          break;
        }
        outputFields.push({
          name: obj.name,
          type: 'String',
          outputable: true,
          targetable: true
        });
      }
    }

    if (canSubmit) {
      const form: any = this.$refs[formName];
      form.validate((valid: any) => {
        if (valid) {
          if (this.isHiveOutputComponent) {
            this.$set(
              this.formData,
              'fieldList',
              (this.$refs as any).hiveTable['hiveFieldInfoData']
            );
            this.$set(this.formData, 'configs', this.transferHiveConfigData);
          }
          // 重新组装jobNode更新DAG
          const nodeDto = _.cloneDeep(this.jobNode);
          if (Array.isArray(this.formData.outputFields)) {
            this.formData.outputFields.forEach((el) => {
              if (el.name) {
                el.name = el.name.replace(/\s*/g, '');
              }
            });
          }
          const properties = _.cloneDeep(this.formData);
          // 排查value为undefined的key
          for (const pKey of Object.keys(properties)) {
            if (properties[pKey] === undefined) {
              delete properties[pKey];
            }
          }
          this.formConf.forms.forEach((f) => {
            if (f.type === 'select') {
              const idField = f.model + _PRIMARY_ID;
              if (properties[idField] !== undefined) {
                properties[f.model] = _.split(properties[f.model], ',')[0];
              }
            }
            // 去除输出字段的空格
            if (
              f &&
              f.componentCfg &&
              f.componentCfg.useTarget &&
              typeof properties[f.model] === 'string'
            ) {
              properties[f.model] = properties[f.model]
                .split(',')
                .map((s) => s.trim())
                .join(',');
            }
          });
          nodeDto.inputFields = this.input;
          // 根据pageSelect来处理result数据; 不显示上游输入字段的置为空
          const resultOutputFields = (this.showFieldList ? this.seOptions : [])
            .filter((item) => {
              if (this.pageSelect.includes(item.value)) {
                item.outputable = true;
              } else {
                item.outputable = false;
              }
              return item.outputable;
            })
            .map((item) => ({
              name: item.value,
              outputable: true,
              targetable: true,
              type: item.type
            }));
          // if (!this.showFieldList) {
          //   resultOutputFields.forEach((item) => {
          //     item.outputable = false;
          //   });
          // }
          const classList = [
            'cn.com.bsfit.pipeace.component.process.keyBy.FlinkProcessComponentKeyBy'
          ];
          if (classList.includes(this.data.className)) {
            resultOutputFields.forEach((item) => {
              item.outputable = true;
            });
          }
          nodeDto.outputFields = _.concat(resultOutputFields, outputFields);
          const name = nodeDto.outputFields
            .filter((it) => it.outputable)
            .map((it) => {
              return it.name;
            });
          const lastLength = [...new Set(name)].length;
          if (name.length !== lastLength) {
            this.$tip.warning('基本参数中所填字段名与字段配置中的字段有重复');
            return;
          }
          nodeDto.properties = properties;
          // emit更新dag
          this.closeDialog(true, nodeDto);
        } else {
          this.$tip.error('请检查输入内容');
        }
      });
    }
  }
  private hideEl(item, val) {
    if (item.componentCfg.hideEl[val] !== undefined) {
      // 先都置为true
      // 遍历item.componentCfg.hideEl，把key不等于val的记录进行恢复
      for (const k of Object.keys(item.componentCfg.hideEl)) {
        item.componentCfg.hideEl[k].forEach((el) => {
          // 找到原始记录
          const old: any = _.find(this.formConfBak.forms, { model: el });
          const cur: any = _.find(this.formConf.forms, { model: el });
          if (old) {
            this.$set(cur, 'show', old.show);
            if (this.formData[el] === undefined && old.defaultVal !== undefined) {
              this.$nextTick(() => {
                this.$set(this.formData, el, old.defaultVal);
              });
              this.$forceUpdate();
            }
          }
          if (cur) {
            cur.rules = _.cloneDeep(old.rules);
            this.$set(this.rules, el, cur.rules);
          }
        });
      }
      this.isHide = !this.isHide;
      this.hide(item, val);
    }
  }

  private hide(item, val) {
    item.componentCfg.hideEl[val].forEach((el) => {
      const rec: any = _.find(this.formConf.forms, { model: el });
      if (rec) {
        this.$set(rec, 'show', false);
        // rules设置为失效
        if (rec.rules) {
          rec.rules.forEach((r) => {
            this.$set(r, 'required', false);
          });
        }
        // 找到对应的item，数据清除
        this.$nextTick(() => {
          delete this.formData[rec.model];
          delete this.rules[rec.model];
        });
      }
    });
    // 动态增加表单项会触发校验，在此处清除校验
    this.$nextTick(() => {
      const form: any = this.$refs['ruleForm'];
      form.clearValidate();
    });
  }
  private hideAllEl(item) {
    for (const el of Object.keys(item.componentCfg.hideEl)) {
      this.isHide = !this.isHide;
      this.hide(item, el);
    }
  }
  show(item) {
    if (item.show === undefined) {
      return true;
    }
    return item.show;
  }
  showInitItem(item) {
    if (item.show === undefined) {
      return true;
    } else if (item.label === '自定义字段') {
      if (this.showExportFiels.includes(this.data.className)) {
        return false;
      } else {
        return item.show;
      }
    }
    return item.show;
  }
  querySearchHandle(queryString, cb) {
    const index = _.findIndex(this.formConf.forms, { model: this.focusModel });
    const ff = this.formConf.forms[index];
    const list = ff.componentCfg.options;
    const results = queryString ? list.filter(this.createFilter(queryString)) : list;
    // 调用 callback 返回建议列表的数据
    cb(results);
  }
  createFilter(queryString) {
    return (obj) => {
      return _.toLower(obj.value).indexOf(_.toLower(queryString)) >= 0;
    };
  }
  // 原逻辑：初始化时对jobNode的必要字段进行必要填充
  // 重构后若不需要 可删除该逻辑
  complementJobNode(jobNode) {
    !Array.isArray(jobNode.inputFields) && (jobNode.inputFields = []);
    !Array.isArray(jobNode.outputFields) && (jobNode.outputFields = []);
    typeof jobNode.parallelism !== 'number' && (jobNode.parallelism = 1);
    !jobNode.properties && (jobNode.properties = {});
  }

  init() {
    // 创建节点默认对象
    this.jobNode = _.cloneDeep(this.data);
    this.complementJobNode(this.jobNode);
    if (_.toString(this.jobData.content) !== '') {
      // 获取上一个节点的输出字段作为该节点的输入字段
      // 由于组件有多个上游组件进行连线时，字段需要一一对应，但是字段顺序可以不同。
      // 且连线时，后连的输入顺序会覆盖前一条连线的顺序，所以此处去获取最后一条连线信息
      const edges = this.jobData.content.edges.filter((e) => e.endNode === this.data.nodeId);
      let preOutputFields: any = [];
      if (edges && edges.length > 0) {
        const preNode: any = _.find(this.jobData.content.nodes, {
          nodeId: edges[edges.length - 1].startNode
        });
        preOutputFields = preNode.outputFields;
      }
      this.input = [];
      const filterResult: any = _.filter(preOutputFields, { outputable: true });
      filterResult.forEach((n) => {
        this.input.push({
          name: n.name,
          type: n.type,
          targetable: n.targetable
        });
      });
      this.formData = this.jobNode.properties !== undefined ? this.jobNode.properties : {};
      const { outputFields = [], inputFields = [] } = this.jobNode;
      const inputList = inputFields.map((item) => item.name);
      const list: any = [];
      // 过滤在自定义字段中定义的字段
      const customItem =
        this.formConf.forms.find(
          (i) => i.type === 'tagInput' && i.label === '自定义字段' && this.formData[i.model]
        ) || {};
      const customFields = Array.isArray(this.formData[customItem.model])
        ? this.formData[customItem.model]
        : [];
      outputFields.forEach((item: any) => {
        if (
          inputList.includes(item.name) &&
          item.outputable === true &&
          !customFields.includes(item.name)
        ) {
          list.push(item.name);
        }
      });
      this.pageSelect = this.showFieldList ? list : [];
      if (this.isHiveOutputComponent) {
        this.preOutputFields = preOutputFields.filter((el) => el.outputable);
        const { resId, subName, fieldList } = this.formData;
        if (resId && subName && fieldList) {
          this.showHiveTable = true;
          this.subName = subName;
          this.fieldList = fieldList;
          this.hiveFieldInfoData = fieldList;
          this.$set(this, 'hiveConfigData', []);
          this.formData.configs.forEach((el) => {
            Object.keys(el).forEach((item) => {
              this.hiveConfigData.push({
                key: item,
                value: el[item],
                edit: false
              });
            });
          });
        }
      }
    }
    // 设置自定义参数
    if (this.formData.customData !== undefined) {
      this.formData.customData.forEach((n) => {
        let nameDesc = '';
        let valueDesc = '';
        if (this.formConf.customConf && this.formConf.customConf !== null) {
          const customRec: any = _.find(this.formConf.customConf, {
            name: n.name
          });
          if (customRec) {
            nameDesc = _.toString(customRec.nameDesc);
            valueDesc = _.toString(customRec.valueDesc);
          }
        }
        this.tableData.push({
          name: n.name,
          nameDesc: nameDesc,
          value: n.value,
          valueDesc: valueDesc
        });
      });
    }
    if (this.formConf && this.formConf.forms) {
      this.formConf.forms.forEach((n) => {
        // 填充校验规则
        if (n.rules !== undefined) {
          n.rules.forEach((r) => {
            if (r.pattern) {
              /* eslint-disable-next-line */
              r.pattern = eval(r.pattern);
            }
          });
          if (
            n.type === 'tagInput' &&
            ['文件采集', 'HTTP 输入'].includes(this.data.componentName)
          ) {
            this.$set(this.rules, n.model, [
              { required: true, message: '请输入输出字段', trigger: 'change' },
              { validator: this.validateTag, trigger: 'change' }
            ]);
          } else {
            this.$set(this.rules, n.model, n.rules);
          }
        }

        // 设置默认值
        if (this.formData[n.model] === undefined) {
          if (n.defaultVal !== undefined) {
            this.$set(this.formData, n.model, n.defaultVal);
          } else if (n.type === 'select' && n.componentCfg.multiple) {
            this.$set(this.formData, n.model, []);
          }
        }
        // 初始化
        // 若select未选择，如果存在switchEl配置需要隐藏对应的文本框
        if (n.type === 'select') {
          if (n.componentCfg.useInput) {
            // 把输入字段作为下拉选项
            n.componentCfg.options = _.cloneDeep(this.input);
          }
          const selVal = _.toString(this.formData[n.model]);
          if (selVal === '') {
            this.hideAllEl(n);
          } else {
            this.hideEl(n, selVal);
          }
        }
        if (n.type === 'select' && n.show && n.componentCfg.multiple) {
          if (this.formData[n.model]) {
            typeof this.formData[n.model] === 'string' &&
              this.$set(this.formData, n.model, this.formData[n.model].split(','));
          }
        }
        if (n.type === 'tagInput' && n.show) {
          if (this.formData[n.model] && typeof this.formData[n.model] === 'string') {
            const val = this.formData[n.model].split(',');
            this.customFieldCount = val.length;
            this.$set(this.formData, n.model, val);
          } else if (Array.isArray(this.formData[n.model])) {
            this.customFieldCount = this.formData[n.model].length;
          }
        }
      });
    }
    if (this.data.componentName === '数据库采集') {
      this.formConf.forms.forEach((item) => {
        if (item.label === '输出字段') {
          this.visibleChangeHandle(true, item);
        }
      });
    }
  }

  // 添加HIVE配置参数表格行
  addHiveConfigDataRow() {
    this.hiveConfigData.push({
      key: '',
      value: '',
      edit: false
    });
  }

  // 获取表字段信息列表
  async getHiveFieldInfoData() {
    try {
      this.hiveComponentLoading = true;
      const { resId, level1 } = this.getFieldInfoDataParams;
      const {
        data: { configs, fields, sinkRollPolicyFileSize, sinkRollPolicyRolloverInterval },
        success,
        msg
      }: any = await getHiveInfo({ resId, level1 });
      if (success) {
        this.hiveFieldInfoData = fields;
        this.$set(this, 'hiveConfigData', []);
        this.$set(this.formData, 'sinkRollPolicyFileSize', sinkRollPolicyFileSize);
        this.$set(this.formData, 'sinkRollPolicyRolloverInterval', sinkRollPolicyRolloverInterval);
        Object.keys(configs).forEach((el, index) => {
          this.hiveConfigData.push({
            key: el,
            value: Object.values(configs)[index],
            edit: false
          });
        });
      } else {
        this.$message.error(msg);
      }
      this.hiveComponentLoading = false;
    } catch {
      this.hiveComponentLoading = false;
    }
  }

  rowConfig(name, row, index) {
    switch (name) {
      case 'edit':
        this.$set(row, 'edit', true);
        break;
      case 'cancel':
        this.$set(row, 'edit', false);
        break;
      case 'update':
        this.$set(row, 'edit', false);
        break;
      case 'delete':
        this.hiveConfigData.splice(index, 1);
        break;
      default:
        break;
    }
  }

  private getComponentList() {
    let list: any = [];
    if (this.versionComponentList) {
      this.versionComponentList.forEach((n) => {
        list = _.concat(list, n.paJobComponentList);
      });
    } else {
      this.$store.state.job.componentList.forEach((n) => {
        list = _.concat(list, n.paJobComponentList);
      });
    }
    return list;
  }

  private getFormConf(componentName: string) {
    const rec: any = _.find(this.getComponentList(), {
      componentName: componentName
    });
    if (rec === undefined) {
      return {
        forms: [],
        custom: false
      };
    }
    return JSON.parse(rec.properties);
  }
  // 处理输出参数的去空格
  handleInputBlur(formConf) {
    const val = this.formData[formConf.model];
    if (
      formConf &&
      formConf.componentCfg &&
      formConf.componentCfg.useTarget &&
      typeof val === 'string'
    ) {
      this.formData[formConf.model] = val
        .split(',')
        .map((s) => s.trim())
        .join(',');
    }
  }
  exportChange(value, bool) {
    if (bool && value) {
      const list = value.split(',');
      if (list.length !== 0 && list.length !== _.uniq(list).length) {
        this.repeatInfo = '存在重复输出数据,请重新输入';
      } else {
        this.repeatInfo = '';
      }
    }
  }

  hasSpace(formConf) {
    const val = this.formData[formConf.model];
    if (val && typeof val === 'string') {
      if (val && val.trim().lastIndexOf(' ') !== -1) {
        return '文本内容有空格，请确认输入是否有误';
      }
    }
  }
  get showFieldList() {
    const classList = [
      'cn.com.bsfit.pipeace.component.process.keyBy.FlinkProcessComponentKeyBy',
      'cn.com.bsfit.pipeace.component.process.flink.cep.FlinkCepProcessComponent'
    ];
    if (classList.includes(this.data.className)) return false;
    // 对于全局聚合组件和聚合计算做特殊处理
    // "cn.com.bsfit.pipeace.component.process.agg.SimpleAggComponent"
    if (this.data.operateType === 'SOURCE' || this.data.operateType === 'SINK') {
      return false;
    } else if (
      this.data.className === 'cn.com.bsfit.pipeace.component.process.global.agg.GlobalAggComponent'
    ) {
      if (this.jobNode.properties && this.jobNode.properties.aggMethod === 'distinct') {
        return true;
      } else if (this.formData.aggMethod === 'distinct') {
        return true;
      } else {
        return false;
      }
    } else if (
      this.data.className === 'cn.com.bsfit.pipeace.component.process.agg.SimpleAggComponent'
    ) {
      return false;
    } else if (this.isConvertTuple2Tuple) {
      return false;
    } else {
      return true;
    }
  }

  isSelect(type = '', componentName = '', label = '') {
    return type === 'select' && !(componentName === '数据库采集' && label === '输出字段');
  }

  getPreInputFields() {
    const edges = this.jobData.content.edges.filter((e) => e.endNode === this.data.nodeId);
    let preOutputFields: any = [];
    if (edges && edges.length > 0) {
      const preNode: any = _.find(this.jobData.content.nodes, {
        nodeId: edges[edges.length - 1].startNode
      });
      preOutputFields = preNode.outputFields;
    }
    const filterResult: any = _.filter(preOutputFields, { outputable: true });
    filterResult.forEach((n) => {
      this.seOptions.push({
        label: n.name,
        value: n.name,
        type: n.type
      });
      // 原Node-field-list代码
      this.optionData.push({
        name: n.name,
        type: n.type,
        disabled: true,
        outputable: false,
        targetable: n.targetable
      });
    });
    if (this.jobNode.outputFields !== undefined) {
      let outputFields = [];
      outputFields = this.jobNode.outputFields;
      outputFields.forEach((n: any) => {
        const rec: any = _.find(this.optionData, { name: n.name });
        if (rec !== undefined && (rec.disabled === undefined || rec.disabled)) {
          this.$set(rec, 'outputable', n.outputable);
        } else if (!n.disabled) {
          if (n.targetable === undefined || !n.targetable) {
            // 设置不是目标字段到表格中
            this.optionData.push({
              name: n.name,
              type: n.type,
              method: n.method,
              params: n.params,
              disabled: false,
              targetable: false,
              outputable: n.outputable
            });
          }
        }
      });
    }
  }

  created() {
    // 找到组件的配置页面
    this.formConf = this.getFormConf(this.data.componentName);
    this.formConfBak = this.getFormConf(this.data.componentName);
    this.title = this.data.nodeName;
    this.init();
  }

  mounted() {
    this.$nextTick(() => {
      this.getPreInputFields();
      this.initComplete = true;
      this.setScriptDefaultValue(false);
    });
  }

  @Watch('formData', { deep: true })
  formdataChanged() {
    this.setScriptDefaultValue(true);
  }
  // 处理自定义参数变更回调
  handleCustomFieldChange(data) {
    this.$set(this.formData, 'customData', data);
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-select {
  width: 100%;
}
.config-form-item {
  width: 90%;
}
.table-input {
  outline: none;
  padding: 0 12px;
  border-radius: 4px;
  color: #666;
}
.trans {
  color: #0096f0;
  cursor: pointer;
  margin-left: 4px;
  word-break: keep-all;
}
.red {
  color: #ff5353;
  font-size: 12px;
}
.options {
  display: inline-flex;
  width: 100%;
}
.nums {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
