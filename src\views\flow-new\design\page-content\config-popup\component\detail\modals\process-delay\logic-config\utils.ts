import i18n from '@/i18n';

export default {};

export const generateName = (num: number) => {
  let result = '';
  while (num > 0) {
    let m = num % 26;
    if (m == 0) m = 26;
    result = `${String.fromCharCode(m + 64)}${result}`;
    num = (num - m) / 26;
  }
  return result;
};

export const isString = (data, defaultValue = '') => (typeof data === 'string' && data.length > 0 ? data : defaultValue);
export const isArray = (data, defaultValue = []) => (Array.isArray(data) ? data : defaultValue);
export const isObject = (data, defaultValue = {}) =>
  Object.prototype.toString.call(data) === '[object Object]' ? data : defaultValue;

export const isValidType = (type) => ['PRIVATE', 'SHARE', 'DEFAULT'].includes(type);
export const hasSourceCode = (type) => ['PRIVATE', 'SHARE'].includes(type);
export const isDefaultFunc = (type) => 'DEFAULT' === type;

export const handleTableHead = (methodFuncType, dataFuncType, methodFuncArgs, funcArgs) => {
  if (isValidType(methodFuncType)) return getThead(methodFuncType, methodFuncArgs, funcArgs);
  if (isValidType(dataFuncType)) return getThead(dataFuncType, methodFuncArgs, funcArgs);
  return [];
};
export const getThead = (type, methodFuncArgs, funcArgs) => {
  const flag = type === 'DEFAULT';
  const disabled = (methodFuncArgs ? methodFuncArgs : funcArgs).length < 1;
  const assemble = (flag, value) => (flag ? value : []);
  return [
    {
      prop: 'order',
      label: i18n.t('pa.serialNumber'),
      width: '100px'
    },
    ...assemble(!flag, [
      {
        prop: 'params',
        label: i18n.t('pa.flow.label4'),
        minWidth: '100px'
      },
      {
        prop: 'type',
        label: i18n.t('pa.flow.paramType'),
        minWidth: '100px'
      }
    ]),
    {
      prop: 'upstreamField',
      label: i18n.t('pa.flow.label5'),
      type: 'select',
      minWidth: '320px'
    },
    ...assemble(flag && !disabled, [
      { prop: 'threshold', label: i18n.t('pa.flow.label6'), type: 'input', minWidth: '250px' }
    ])
  ];
};

export const handleFuncArgs = (methodFuncType, methodFuncArgs, funcArgs, isChange) => {
  if (!isValidType(methodFuncType)) return funcArgs;
  if (methodFuncArgs.length < 1 && isDefaultFunc(methodFuncType)) {
    methodFuncArgs = [{ key: null, value: null }];
  }
  return methodFuncArgs.map((el, index) => {
    const { key = '', value = '' } = isChange ? {} : funcArgs[index];
    return methodFuncType === 'DEFAULT'
      ? { key, value }
      : { key, type: methodFuncArgs[index].type, name: methodFuncArgs[index].name };
  });
};

export const handleTableData1 = (flag, methodFuncArgs, funcArgs) => {
  if (flag) funcArgs = [{ key: 11 }];
  return funcArgs.map(({ name, type, key }, index) => {
    return {
      order: index + 1,
      upstreamField: flag ? isString(funcArgs[0].key) : key,
      ...(type && { type }),
      ...(name && { params: isString(name, i18n.t('pa.flow.params1', [index + 1]) as string) }),
      ...(flag && {
        disabled: (methodFuncArgs ? methodFuncArgs : funcArgs).length < 1,
        threshold: isString(funcArgs[0].value)
      })
    };
  });
};

export const handleTableData = (type, methodFuncArgs, funcArgs, paramsType: string) => {
  if (type === 'DEFAULT') {
    return [
      {
        order: 1,
        upstreamField: isString(funcArgs[0].key),
        disabled: (methodFuncArgs ? methodFuncArgs : funcArgs).length < 1,
        threshold: isString(funcArgs[0].value),
        type: paramsType === 'NUMBER' ? 'number' : 'text'
      }
    ];
  }
  return funcArgs.map(({ name, type, key }, index) => {
    return {
      type,
      order: index + 1,
      params: isString(name, i18n.t('pa.flow.params1', [index + 1]) as string),
      upstreamField: key
    };
  });
};

export const sha256 = require('sha256');
