<template>
  <pro-table
    ref="proTable"
    :columns="columnData"
    :request="request"
    :options="{ showPage: false, height: '100vh - 169px' }"
    :actions="actions"
    @row-dblclick="view"
    @action-click="handleClick"
  />
</template>

<script lang="ts">
import { Vue, Component, Prop, Ref } from 'vue-property-decorator';
import { getHistorys, rollBack } from '@/apis/cep-api';
import { dateFormat } from '@/utils/format';

@Component
export default class History extends Vue {
  @Ref('proTable') readonly proTable!: any;
  @Prop({ type: String }) id!: string;
  actions = [
    { label: '回滚', value: 'rollBack', icon: 'iconfont icon-roll-back' },
    { label: '查看', value: 'view', icon: 'iconfont icon-chakan' }
  ];

  columnData: any = [];
  get cepName() {
    return this.$route.query ? this.$route.query.title : '';
  }

  async request() {
    const {
      data: { columnData, tableData }
    } = await getHistorys(this.id);
    this.columnData = columnData;
    tableData.forEach((el) => {
      el.updateTime = dateFormat(el.updateTime);
    });
    return { data: tableData };
  }

  handleClick(val: 'rollBack', { row }) {
    this[val](row);
  }

  // 回滚
  rollBack(row) {
    this.$confirm('是否确认回滚?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        const { success, msg } = await rollBack(row.id);
        if (success) {
          this.$message.success(msg);
          this.proTable.loadDataAndReset();
        } else {
          this.$message.error(msg);
        }
      })
      .catch(() => {
        return true;
      });
  }

  // 查看历史版本的基本信息
  view(row) {
    this.$router.push({
      name: 'templateCepDetail',
      query: {
        id: row.id,
        version: row.version + ''
      }
    });
  }
}
</script>

<style scoped lang="scss">
.roll-back {
  overflow: scroll;
  background: #fff;
  &__icon {
    cursor: pointer;
    margin: 0 5px;
  }
}
</style>
