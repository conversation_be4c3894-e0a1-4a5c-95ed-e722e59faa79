<template>
  <div class="line__container" :class="containerClass">
    <!-- 上 -->
    <arrow-line v-if="isRight" show-arrow :type="direction" />
    <arrow-line v-else :type="direction" />
    <!-- 中 -->
    <el-tooltip v-hide="content" effect="light" :content="content" placement="bottom">
      <div class="line-content">{{ content }}</div>
    </el-tooltip>
    <!-- 下 -->
    <arrow-line v-if="isRight" :type="direction" />
    <arrow-line v-else show-arrow :type="direction" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { hide } from '../../../../util';
import ArrowLine from './arrow-line.vue';

@Component({ directives: { hide }, components: { ArrowLine } })
export default class LineType extends Vue {
  @Prop({ default: '' }) content!: string;
  @Prop({ default: 'left' }) direction!: string;

  get containerClass() {
    return `line__container--${this.direction}`;
  }
  get mainClass() {
    return `line-main--${this.direction}`;
  }
  get arrowClass() {
    return `line-arrow--${this.direction}`;
  }
  get isRight() {
    return this.direction === 'right';
  }
  get isBottom() {
    return this.direction === 'bottom';
  }
  get icon() {
    return this.direction === 'bottom' ? 'el-icon-bottom' : 'el-icon-right';
  }
}
</script>
<style lang="scss" scoped>
$linwWidth: 35px;
.line {
  &__container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: $moduleLineWidth;
    height: 34px;

    &--left,
    &--right {
      line-height: 34px;
    }
    &--bottom {
      display: block;
      margin: 0 auto;
      width: 100%;
      height: 105px;
      text-align: center;
      > div {
        margin: 0 auto;
      }
    }
  }
  /* 左线 */
  &-main {
    height: 1px;
    background: #7f8081;
    &--left,
    &--right {
      width: 35px;
    }
    &--bottom {
      display: block;
      width: 1px;
      height: 35px;
    }
  }
  &-content {
    width: 90p x s s s;
    line-height: 34px;
    vertical-align: middle;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  &-arrow {
    display: flex;
    align-items: center;
    width: 35px;
    background: pink;

    &__line {
      width: 90px;
      height: 1px;
      background: #7f8081;
    }
    &--bottom {
      display: block;
    }
  }
}
</style>
