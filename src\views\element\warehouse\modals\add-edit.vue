<template>
  <bs-dialog
    :title="title"
    :visible.sync="visible"
    :width="width"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
  >
    <el-form
      ref="ruleForm"
      v-loading="formLoading"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
      :element-loading-text="$t('pa.tip.loading')"
    >
      <el-form-item :label="$t('pa.file')" style="text-align: left" prop="pkg">
        <el-upload
          ref="upload"
          action
          :http-request="handleFile"
          :multiple="false"
          :limit="1"
          :on-exceed="handleExceed"
          :before-upload="beforeUpload"
          :file-list="formData.fileList"
          accept=".jar"
          :headers="headers"
        >
          <el-button size="small" type="primary">{{ $t('pa.choseFile') }}</el-button>
          <span slot="tip" class="el-upload__tip" style="padding-left: 16px">{{ $t('pa.tip.onlyJar250MB') }}</span>
        </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">{{ $t('pa.action.close') }}</el-button>
      <el-button type="primary" :loading="loading" @click="submit('ruleForm')">{{ $t('pa.action.makeSure') }}</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Emit } from 'vue-property-decorator';
import { URL_COMPONENT_SAVE } from '@/apis/commonApi';
import { cloneDeep } from 'lodash';
import { PaBase } from '@/common/pipeace-base';
import { put } from '@/apis/utils/net';
import { getToken } from '@/utils';
import i18n from '@/i18n';

@Component
export default class AddEdit extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: i18n.t('pa.uploadSingle') }) title!: string;
  @Prop({ default: {} }) data!: any;
  @Prop({ default: false }) formLoading!: boolean;

  formData: any = {};
  loading = false;
  headers = {
    Authorization: getToken()
  };
  rules: any = {
    pkg: [{ required: true, validator: this.validateFile, trigger: 'blur' }]
  };
  validateFile(rule: any, value: any, callback: any) {
    if (this.formData.fileList === undefined || this.formData.fileList.length === 0) {
      if (this.formData.id) {
        callback();
      } else {
        callback(new Error(this.$t('pa.tip.addFile')));
      }
    } else {
      callback();
    }
  }

  get width() {
    return this.isEn ? '653px' : '500px';
  }

  created() {
    this.formData = cloneDeep(this.data);
  }

  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  private closeDialog(needFresh: boolean) {
    (this.$refs.upload as any).clearFiles();
    this.formData = {};
    this.loading = false;
    this.formLoading = false;
  }
  beforeUpload(file) {
    const name = file.name;
    const isJar = name.indexOf('.jar') >= 0;
    const isLt250M = file.size / 1024 / 1024 < 250;
    if (!isJar) {
      this.$message.error(this.$t('pa.tip.onlyJar'));
    }
    if (!isLt250M) {
      this.$message.error(this.$t('pa.tip.size250MB'));
    }
    return isJar && isLt250M;
  }
  /**
   * 表单提交
   */
  submit(formName: string) {
    if (this.formData.fileList === undefined || this.formData.fileList.length === 0) {
      this.$message.warning(this.$t('pa.tip.selectFile'));
      return;
    }
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        const config = {
          timeout: 1500000,
          'Content-Type': 'multipart/form-data'
        };
        const submitData = new FormData();
        if (this.formData.jarId) {
          submitData.append('jarId', this.formData.jarId);
        }
        if (this.formData.fileList !== undefined) {
          submitData.append('file', this.formData.fileList[0]);
        }
        this.loading = true;
        put(URL_COMPONENT_SAVE, submitData, config).then((resp: any) => {
          this.parseResp(resp);
        });
      } else {
        this.$message.error(this.$t('pa.tip.checkMessage'));
        return false;
      }
    });
  }
  /**
   * 解析返回结果
   */
  private parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.closeDialog(true);
    });
  }
  /**
   * 处理文件上传
   */
  private handleFile(file: any) {
    this.formData.fileList = [];
    this.formData.fileList.push(file.file);
  }
  /**
   * 判断上传文件个数
   */
  private handleExceed(files: any) {
    this.$message.warning(this.$t('pa.tip.fileCount', [files.length]));
  }
}
</script>
