<template>
  <bs-dialog
    :title="title"
    :visible.sync="visible"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
  >
    <el-form
      ref="ruleForm"
      v-loading="formLoading"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
      element-loading-text="数据正在加载中..."
    >
      <el-form-item label="文件" style="text-align: left" prop="pkg">
        <el-upload
          ref="upload"
          action
          :http-request="handleFile"
          :multiple="false"
          :limit="1"
          :on-exceed="handleExceed"
          :before-upload="beforeUpload"
          :file-list="formData.fileList"
          accept=".jar"
        >
          <el-button size="small" type="primary">选择文件</el-button>
          <span slot="tip" class="el-upload__tip" style="padding-left: 16px">
            只能上传jar文件，且不超过60mb
          </span>
        </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" :loading="loading" @click="submit('ruleForm')">确 定</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { URL_COMPONENT_SAVE } from '@/apis/commonApi';
import { cloneDeep } from 'lodash';
import { PaBase } from '@/common/pipeace-base';

@Component
export default class AddEdit extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: '单个上传' }) title!: string;
  @Prop({ default: {} }) data!: any;
  @Prop({ default: false }) formLoading!: boolean;

  formData: any = {};
  loading = false;

  rules: any = {
    pkg: [{ required: true, validator: this.validateFile, trigger: 'blur' }]
  };
  validateFile(rule: any, value: any, callback: any) {
    if (this.formData.fileList === undefined || this.formData.fileList.length === 0) {
      if (this.formData.id) {
        callback();
      } else {
        callback(new Error('请添加文件'));
      }
    } else {
      callback();
    }
  }

  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  private closeDialog(needFresh: boolean) {
    (this.$refs.upload as any).clearFiles();
    this.formData = {};
    this.loading = false;
    this.formLoading = false;
  }
  beforeUpload(file) {
    const name = file.name;
    const isJar = name.indexOf('.jar') >= 0;
    const isLt60M = file.size / 1024 / 1024 < 60;
    if (!isJar) {
      this.$message.error('只能上传jar类型的文件!');
    }
    if (!isLt60M) {
      this.$message.error('不能上传超过60MB的文件!');
    }
    return isJar && isLt60M;
  }
  /**
   * 表单提交
   */
  submit(formName: string) {
    if (this.formData.fileList === undefined || this.formData.fileList.length === 0) {
      this.$message.warning('请选择文件');
      return;
    }
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        Vue.axios.defaults.timeout = 1500000;
        Vue.axios.defaults.headers.post['Content-Type'] = 'multipart/form-data';
        const submitData = new FormData();
        if (this.formData.jarId) {
          submitData.append('jarId', this.formData.jarId);
        }
        if (this.formData.fileList !== undefined) {
          submitData.append('file', this.formData.fileList[0]);
        }
        this.loading = true;
        this.doPut(URL_COMPONENT_SAVE, submitData).then((resp: any) => {
          this.parseResp(resp);
        });
      } else {
        this.$message.error('请检查输入内容');
        return false;
      }
    });
  }
  /**
   * 解析返回结果
   */
  private parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.closeDialog(true);
    });
  }
  /**
   * 处理文件上传
   */
  private handleFile(file: any) {
    this.formData.fileList = [];
    this.formData.fileList.push(file.file);
  }
  /**
   * 判断上传文件个数
   */
  private handleExceed(files: any) {
    this.$message.warning(`最多上传 ${files.length} 个文件`);
  }

  @Watch('data')
  onDataChange() {
    this.formData = cloneDeep(this.data);
  }
}
</script>
