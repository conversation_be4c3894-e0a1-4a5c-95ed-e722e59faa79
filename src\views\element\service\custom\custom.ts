import { Component, Vue } from 'vue-property-decorator';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
import {
  URL_HOST_LIST,
  URL_HOST_DELETE,
  URL_HOST_FIND,
  URL_RES_LIST,
  URL_RES_DELETE,
  URL_RES_FINDBYID,
  URL_RESCONF_GETRESTYPELIST,
  URL_RESCONF_GETFORMCONF,
  URL_RES_TESTCONNECT
} from '@/apis/commonApi';
import term from '@/components/term.vue';
import scp from '@/components/scp.vue';
import deptRole from '@/components/dept-role-1.vue';
import addEdit from '@/views/element/service/custom/modals/add-edit.vue';
import baseTable from '@/components/base-table.vue';
import contactInfo from '@/components/contact-info.vue';
@Component({
  name: 'CommonServiceCustom',
  components: {
    baseTable,
    term,
    scp,
    deptRole,
    addEdit,
    contactInfo
  }
})
export class CommonServiceCustom extends PaBase {
  private pageLoading = false;
  private tableLoading = false;
  private currentData: any = {};
  private currentComponent: any = null;
  private searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: {
      updateTime: 'DESC'
    }
  };
  private tableData: ITableData = {
    columnData: [],
    tableData: []
  };
  private tableConfig: any = {};
  private fetchList: any = _.debounce(this.getListData, 500);
  // 分享弹窗相关
  private showDeptRoleDialog = false;
  private deptData: any = {};
  private addFile: any;
  private regFile: any;
  private clusterType: any;
  handleCellClick(row, col, cell) {
    const ContactInfo: any = this.$refs.ContactInfo;
    if (cell.property === 'createdBy') {
      ContactInfo.open([row.row.createdBy]);
    }
    if (cell.property === 'updatedBy') {
      ContactInfo.open([row.row.updatedBy]);
    }
  }
  handleRowDblclick(data) {
    this.detail(data.row);
  }
  isHost() {
    return this.$route.meta!.resType === 'HOST';
  }
  hasAuthority(hasRole: any, row: any) {
    return !_.isEmpty(_.toString(row.dataLevelType)) && row.dataLevelType !== 'PARENT' && hasRole;
  }

  async getRecordById(id: string) {
    if (!id) {
      return;
    }
    const params = { id: id };
    return await Vue.axios.get(this.makeDetailUrl(), { params: params });
  }
  showEdit(row: any) {
    const addEditEl: any = this.$refs.AddEdit;
    addEditEl.formLoading = true;
    this.pageLoading = true;
    this.doGet(URL_RESCONF_GETFORMCONF, {
      params: {
        file: this.getAddFile()
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.getRecordById(row.id).then((result: any) => {
          if (result.success) {
            addEditEl.visible = true;
            addEditEl.oper = 'edit';
            addEditEl.title = '编辑';
            addEditEl.formData = result.data;
            addEditEl.formData.resType = this.$route.meta!.resType;
            addEditEl.formData.belongType = 'SELF';
            addEditEl.formConf = JSON.parse(resp.data);
          } else {
            this.$message.warning(result.msg);
          }
        });
      });
      addEditEl.formLoading = false;
      this.pageLoading = false;
    });
  }
  showReg(row: any) {
    const addEditEl: any = this.$refs.AddEdit;
    addEditEl.formLoading = true;
    this.pageLoading = true;
    this.doGet(URL_RESCONF_GETFORMCONF, {
      params: {
        file: this.getRegFile()
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.getRecordById(row.id).then((result: any) => {
          if (result.success) {
            addEditEl.visible = true;
            addEditEl.oper = 'edit';
            addEditEl.title = '编辑';
            addEditEl.formData = result.data;
            addEditEl.formData.resType = this.$route.meta!.resType;
            addEditEl.formData.belongType = 'REG';
            addEditEl.formConf = JSON.parse(resp.data);
          } else {
            this.$message.warning(result.msg);
          }
        });
      });
      addEditEl.formLoading = false;
      this.pageLoading = false;
    });
  }
  edit(row) {
    if (row.belongType !== undefined) {
      if (row.belongType === 'SELF') {
        this.showEdit(row);
      }
      if (row.belongType === 'REG') {
        this.showReg(row);
      }
    } else {
      this.showEdit(row);
    }
  }
  detail(row) {
    if (row.resProperty) {
      this.clusterType = JSON.parse(row.resProperty).clusterType;
      if (this.clusterType === undefined) {
        this.clusterType = '';
      }
    } else {
      this.clusterType = '';
    }
    this.$router.push({
      path:
        this.$route.path +
        '/detail?id=' +
        row.id +
        '&resType=' +
        row.resType +
        '&clusterType=' +
        this.clusterType +
        '&title=' +
        row.title
    });
  }
  deptRole(row) {
    this.deptData = row;
    this.showDeptRoleDialog = true;
  }
  term(row) {
    const termEl: any = this.$refs.Term;
    termEl.open(row.id, row.ip);
    termEl.visible = true;
  }
  scp(row) {
    const scpEl: any = this.$refs.Scp;
    scpEl.recordId = row.id;
    scpEl.visible = true;
  }
  delete(row: any) {
    this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        this.tableLoading = true;
        const ids: any[] = [];
        ids.push(row.id);
        this.doDelete(this.makeDeleteUrl(), { data: ids }).then((resp: any) => {
          this.parseResponse(resp, () => {
            this.getListData();
          });
          this.tableLoading = false;
        });
      })
      .catch(() => {
        return true;
      });
  }
  handleCurrentChange(val) {
    this.searchObj.pageData.currentPage = val;
    this.fetchList();
  }

  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.fetchList();
  }
  closeDialog(needFresh: boolean) {
    if (needFresh) {
      this.getListData();
    }
  }
  reg() {
    const addEditEl: any = this.$refs.AddEdit;
    addEditEl.formLoading = true;
    this.doGet(URL_RESCONF_GETFORMCONF, {
      params: {
        file: this.getRegFile()
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        addEditEl.visible = true;
        addEditEl.oper = 'add';
        addEditEl.title = '注册';
        addEditEl.formData.resType = this.$route.meta!.resType;
        addEditEl.formData.belongType = 'REG';
        addEditEl.formConf = JSON.parse(resp.data);
      });
      addEditEl.formLoading = false;
    });
  }
  add() {
    const addEditEl: any = this.$refs.AddEdit;
    addEditEl.formLoading = true;
    this.doGet(URL_RESCONF_GETFORMCONF, {
      params: {
        file: this.getAddFile()
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        addEditEl.visible = true;
        addEditEl.oper = 'add';
        addEditEl.title = '新建';
        addEditEl.formData.resType = this.$route.meta!.resType;
        addEditEl.formData.belongType = 'SELF';
        addEditEl.formConf = JSON.parse(resp.data);
      });
      addEditEl.formLoading = false;
    });
  }
  getListData() {
    this.tableLoading = true;
    this.doPost(this.makeQueryUrl(), this.searchObj).then((resp: any) => {
      this.parseResponse(resp, () => {
        if (this.$route.meta!.resType === 'FLINK') {
          const target = resp.data.columnData.find((item) => item.prop === 'authorized');
          if (target) {
            target.dataType = 'Enum';
            target.enumColor = { false: 'red', true: 'green' };
            target.enumData = { false: '否', true: '是' };
          }
          resp.data.tableData.forEach((item) => {
            item.authorized = item.resProperty && JSON.parse(item.resProperty).authorized;
          });
        }

        this.tableData = {
          ...resp.data
        };
      });
      this.tableLoading = false;
    });
  }
  makeQueryUrl() {
    if (this.isHost()) {
      return URL_HOST_LIST;
    } else {
      return URL_RES_LIST + '/' + this.$route.meta!.resType;
    }
  }
  makeDeleteUrl() {
    if (this.isHost()) {
      return URL_HOST_DELETE;
    } else {
      return URL_RES_DELETE;
    }
  }
  makeDetailUrl() {
    if (this.isHost()) {
      return URL_HOST_FIND;
    } else {
      return URL_RES_FINDBYID;
    }
  }
  init() {
    this.getListData();
    Vue.axios.get(URL_RESCONF_GETRESTYPELIST).then((resp: any) => {
      if (resp.success) {
        this.resEnums = resp.data;
        this.tableConfig = {
          width: 200,
          columnsExtend: {
            edit: [
              {
                tipMessage: '编辑',
                handler: this.edit.bind(this),
                iconfont: 'icon-bianji',
                hasAuthority: this.hasAuthority.bind(
                  this,
                  this.hasFeatureAuthority(this.getEditAuthCode())
                )
              },
              {
                tipMessage: '删除',
                handler: this.delete.bind(this),
                iconfont: 'icon-shanchu',
                hasAuthority: this.hasAuthority.bind(
                  this,
                  this.hasFeatureAuthority(this.getDeleteAuthCode())
                )
              },
              {
                tipMessage: '详情',
                handler: this.detail.bind(this),
                iconfont: 'icon-chakan',
                // hasAuthority: this.hasAuthority.bind(
                //   this,
                //   this.hasFeatureAuthorityWithParent(this.getMenuAuthCode())
                // )
                hasAuthority: this.hasFeatureAuthorityWithParent(this.getMenuAuthCode())
              },
              {
                tipMessage: '分享',
                handler: this.deptRole.bind(this),
                iconfont: 'icon-fenxiang',
                hasAuthority: this.hasAuthority.bind(
                  this,
                  this.hasFeatureAuthority(this.getShareAuthCode())
                )
              },
              {
                tipMessage: '终端',
                handler: this.term.bind(this),
                iconfont: 'icon-zhongduan',
                hasAuthority: this.hasAuthority.bind(
                  this,
                  this.hasFeatureAuthority(this.getTermAuthCode())
                )
              },
              {
                tipMessage: '上传',
                handler: this.scp.bind(this),
                iconfont: 'icon-shangchuan',
                hasAuthority: this.hasAuthority.bind(
                  this,
                  this.hasFeatureAuthority(this.getScpAuthCode())
                )
              }
            ]
          }
        };
      }
    });
  }
  getListConf() {
    return this.getResLabel(this.$route.meta!.resType, 'listConf');
  }
  getEditAuthCode() {
    const name = 'editAuthCode';
    return this.getListConf()[name];
  }
  getDeleteAuthCode() {
    const name = 'deleteAuthCode';
    return this.getListConf()[name];
  }
  getMenuAuthCode() {
    const name = 'menuAuthCode';
    return this.getListConf()[name];
  }
  getShareAuthCode() {
    const name = 'shareAuthCode';
    return this.getListConf()[name];
  }
  getTermAuthCode() {
    const name = 'termAuthCode';
    return this.getListConf()[name];
  }
  getScpAuthCode() {
    const name = 'scpAuthCode';
    return this.getListConf()[name];
  }
  getAddFile() {
    const name = 'file';
    this.addFile = this.getResLabel(this.$route.meta!.resType, 'addConf')[name];
    if ((this.$route.query.clusterType as string) === 'YARN_PER_JOB') {
      this.addFile = this.addFile.replace(/detail.json/, 'detail-per-job.json');
    } else if ((this.$route.query.clusterType as string) === 'YARN_SESSION') {
      this.addFile = this.addFile.replace(/detail.json/, 'detail-yarn-session.json');
    } else if ((this.$route.query.clusterType as string) === 'YARN_APPLICATION') {
      this.addFile = this.addFile.replace(/detail.json/, 'detail-yarn-application.json');
    }
    return this.addFile;
  }
  getRegFile() {
    const name = 'file';
    this.regFile = this.getResLabel(this.$route.meta!.resType, 'regConf')[name];
    if ((this.$route.query.clusterType as string) === 'YARN_PER_JOB') {
      this.regFile = this.regFile.replace(/detail.json/, 'detail-per-job.json');
    } else if ((this.$route.query.clusterType as string) === 'YARN_SESSION') {
      this.addFile = this.addFile.replace(/detail.json/, 'detail-yarn-session.json');
    } else if ((this.$route.query.clusterType as string) === 'YARN_APPLICATION') {
      this.addFile = this.addFile.replace(/detail.json/, 'detail-yarn-application.json');
    }
    return this.regFile;
  }
  tableClickHandler(obj) {
    if (obj.col === 'status') {
      this.$set(obj.row, 'status', 2);
      this.doGet(URL_RES_TESTCONNECT, {
        params: {
          id: obj.row.id
        }
      }).then((resp: any) => {
        this.parseResponse(resp, () => {
          const all = resp.data.fail + resp.data.success;
          if (resp.data.success === 0) {
            this.$set(obj.row, 'status', 0);
            this.$set(obj.row, 'checkResult', '0/' + all);
          } else {
            this.$set(obj.row, 'status', 1);
            this.$set(obj.row, 'checkResult', resp.data.success + '/' + all);
          }
        });
      });
    }
  }
  created() {
    this.init();
  }
}
