<template>
  <bs-dialog
    top="30px"
    :title="title"
    :visible.sync="display"
    width="90%"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="views-detail-dialog"
  >
    <div class="views-content">
      <!-- 头部 -->
      <div class="views-content__header">
        <span
          :class="{ 'views-content__header__highLight': showDetail }"
          @click="activeName = 'detail'"
        >
          详情
        </span>
        <span>丨</span>
        <span
          :class="{ 'views-content__header__highLight': showVersion }"
          @click="activeName = 'version'"
        >
          版本
        </span>
      </div>
      <div class="views-content__body">
        <table-detail v-if="showDetail && showTable" :id="id" />
        <view-detail v-if="showDetail && showView" :id="id" />
        <udf-detail v-if="showDetail && showUDF" :id="id" />
        <version-detail v-if="showVersion" :id="id" :type="type" />
      </div>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, PropSync } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import TableDetail from './table.vue';
import ViewDetail from './view.vue';
import VersionDetail from './version.vue';
import udfDetail from './udf.vue';
import ReferenceRelation from '@/views/data/components/reference-relation.vue';
@Component({
  components: {
    TableDetail,
    ViewDetail,
    VersionDetail,
    udfDetail,
    ReferenceRelation
  }
})
export default class ViewsDetailDialog extends PaBase {
  @Prop({ type: String, default: '' }) id!: string;
  @Prop({ type: String, default: '' }) type!: string;
  @PropSync('show', { type: Boolean, default: true }) display!: boolean;

  private activeName = 'detail';

  get showDetail() {
    return this.activeName === 'detail';
  }

  get showVersion() {
    return this.activeName === 'version';
  }

  get showTable() {
    return this.type === 'TABLE';
  }

  get showView() {
    return this.type === 'VIEW';
  }

  get showUDF() {
    return this.type === 'UDF';
  }

  get title() {
    const mapping = {
      TABLE: '表详情',
      VIEW: '视图详情',
      UDF: 'UDF详情'
    };
    return mapping[this.type] || '';
  }

  created() {
    console.log(this.type);
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header {
  &::before {
    display: none !important;
  }
}
.views-content {
  height: 100%;
  max-height: 100%;
  overflow: hidden;
  background: #f6f7f9;

  &__header {
    display: flex;
    align-items: center;
    padding: 0 20px;
    height: 50px;

    span {
      cursor: pointer;
    }

    &__highLight {
      font-weight: bolder;
    }
  }

  &__body {
    height: calc(100% - 60px);
    overflow-x: hidden;
    overflow-y: auto;
  }
}
.views-detail-dialog {
  height: 100%;
  overflow: hidden;
  ::v-deep .el-dialog {
    height: calc(100% - 60px);
    overflow: hidden;

    &__body {
      max-height: calc(100% - 44px);
      height: calc(100% - 44px);
      overflow: hidden;
      box-sizing: border-box;
    }
  }
}
</style>
