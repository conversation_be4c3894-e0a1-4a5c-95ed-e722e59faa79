<template>
  <div class="search-container">
    <div class="search-header">
      <bs-search
        v-model.trim="keywords"
        placeholder="请输入"
        clearable
        :auto-search="false"
        @keyup.enter.native="handleSearch"
        @search="handleSearch"
      />
      <div class="search-header__type">
        <div
          v-for="item in types"
          :key="item.value"
          class="type"
          :class="item.value === currenType ? 'is-active' : ''"
          @click="handleTypeChange(item.value)"
        >
          {{ item.label }}({{ item.number }})
        </div>
      </div>
    </div>
    <div v-loading="loading" class="search-content">
      <div class="search-content__content">
        <list v-if="tableData.length" :data="tableData" />
        <bs-empty v-else />
      </div>
      <el-pagination
        :current-page="pageData.currentPage"
        :page-sizes="pageSizes"
        :page-size="pageData.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Inject, InjectReactive } from 'vue-property-decorator';
import List from './list.vue';
import { globalDataQuery } from '@/apis/globalSearchApi';
@Component({
  components: {
    List
  }
})
export default class GlobalSearch extends Vue {
  loading = false;
  keywords = '';
  pageSizes = [25, 50, 75, 100];
  pageData = {
    pageSize: 25,
    currentPage: 1,
    total: 0
  };
  currenType = 'ALL';
  types = [
    { label: '全部', value: 'ALL', number: 0 },
    { label: '项目', value: 'PROJECT', number: 0 },
    { label: '流程', value: 'JOB', number: 0 },
    { label: '服务', value: 'SERVICE', number: 0 },
    { label: '第三方类库', value: 'ASSETS', number: 0 },
    { label: '方法', value: 'FUNCTION', number: 0 },
    { label: '表', value: 'TABLE', number: 0 },
    { label: '视图', value: 'VIEW', number: 0 },
    { label: 'udf', value: 'UDF', number: 0 }
  ];
  tableData = [];
  @Inject('enableSql') enableSql;
  @InjectReactive('isFuseMode') isFuseMode!: boolean;

  created() {
    this.init();
    if (!this.enableSql) {
      this.types = this.types.slice(0, -3);
    }
    if (this.isFuseMode) {
      this.types.splice(4, 2);
    }
  }

  init() {
    this.keywords = this.$route.query.keywords as string;
    this.getData();
  }

  async getData() {
    try {
      this.loading = true;
      const { data, success, msg, error } = await globalDataQuery(
        {
          search: this.keywords,
          pageData: this.pageData
        },
        this.currenType
      );
      this.loading = false;
      if (success) {
        const reg = new RegExp(`(${this.keywords})`, 'gi');
        data.tableData.length &&
          data.tableData.forEach((item) => {
            item.longName = item.name.replaceAll(
              reg,
              (a) => `<span style="color:#377CFF">${a}</span>`
            );
          });
        this.tableData = data.tableData;
        this.pageData.total = data.countMap[this.currenType];
        this.types.forEach((item) => {
          if (data.countMap[item.value]) {
            item.number = data.countMap[item.value];
          } else {
            item.number = 0;
          }
        });
      } else {
        this.$message.error(error || msg);
      }
    } catch (e) {
      this.loading = false;
    }
  }

  //搜索
  handleSearch() {
    if (!this.keywords) {
      this.$message.error('请输入搜索关键字');
    } else {
      this.pageData.currentPage = 1;
      this.getData();
    }
  }

  handleTypeChange(type = '') {
    this.currenType = type;
    this.pageData.currentPage = 1;
    this.getData();
  }

  handleSizeChange(pageSize) {
    this.pageData.currentPage = 1;
    this.pageData.pageSize = pageSize;
    this.getData();
  }

  handleCurrentChange(currentPage) {
    this.pageData.currentPage = currentPage;
    this.getData();
  }
}
</script>

<style lang="scss" scoped>
.search-container {
  width: 100%;
  height: 100%;
  background: $--bs-color-text-white-primary;
}
.search-header {
  height: 154px;
  padding: 30px 36px 19px;
  border-bottom: 1px solid #e4e7ed;

  ::v-deep .el-input {
    &--suffix {
      width: 720px;
      height: 50px;
      display: flex;
      align-items: center;
    }
    &__inner {
      height: calc(100% - 2px);
      padding-right: 110px;
      border-radius: 0px 8px 8px 0px;
      font-size: 14px;
      line-height: 20px;
    }
    &__suffix {
      width: 72px;
      background: $--bs-color-primary;
      border-radius: 0px 8px 8px 0px;
    }
    &__clear {
      position: absolute;
      right: 80px;
      font-size: 20px;
    }
  }

  ::v-deep .bs-search-icon {
    color: $--bs-color-text-white-primary;
    font-size: 20px;
  }

  &__type {
    margin-top: 23px;
    .type {
      display: inline-block;
      padding: 6px 12px;
      margin-right: 10px;
      cursor: pointer;
      background: #f6f6f6;
      border-radius: 4px;
    }
    .is-active {
      background: #ebf2ff;
      border: 1px solid #377cff;
      color: #377cff;
    }
  }
}
.search-content {
  padding: 0 36px;
  &__content {
    height: calc(100vh - 309px);
    overflow: auto;
  }
}
</style>
