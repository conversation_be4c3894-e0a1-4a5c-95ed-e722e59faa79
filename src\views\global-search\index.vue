<template>
  <div class="search-container">
    <div class="search-header" :class="{ 'search-header__en': isEn }">
      <bs-search
        v-model.trim="keywords"
        :placeholder="$t('pa.placeholder.input')"
        clearable
        :auto-search="false"
        @keyup.enter.native="handleSearch"
        @search="handleSearch"
      />
      <div class="search-header__type">
        <div
          v-for="item in types"
          :key="item.value"
          class="type"
          :class="[item.value === currenType ? 'is-active' : '', isEn ? 'type__us' : '']"
          @click="handleTypeChange(item.value)"
        >
          {{ item.label }}({{ item.number }})
        </div>
      </div>
    </div>
    <div v-loading="loading" class="search-content">
      <div class="search-content__content">
        <list v-if="tableData.length" :data="tableData" />
        <bs-empty v-else />
      </div>
      <el-pagination
        :current-page="pageData.currentPage"
        :page-sizes="pageSizes"
        :page-size="pageData.pageSize"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Inject, InjectReactive } from 'vue-property-decorator';
import List from './list.vue';
import { globalDataQuery } from '@/apis/globalSearchApi';
@Component({
  components: {
    List
  }
})
export default class GlobalSearch extends Vue {
  loading = false;
  keywords = '';
  pageData = {
    pageSize: this.$store.getters.pageSize || 25,
    currentPage: 1,
    total: 0
  };
  currenType = 'ALL';
  types = [
    { label: this.$t('pa.all'), value: 'ALL', number: 0 },
    { label: this.$t('pa.flow.project'), value: 'PROJECT', number: 0 },
    { label: this.$t('pa.data.text31'), value: 'DIRECTORY', number: 0 },
    { label: this.$t('pa.flowName'), value: 'JOB', number: 0 },
    { label: this.$t('pa.service'), value: 'SERVICE', number: 0 },
    { label: this.$t('pa.table'), value: 'TABLE', number: 0 },
    { label: 'CATALOG', value: 'CATALOG', number: 0 },
    { label: 'UDF', value: 'UDF', number: 0 },
    { label: this.$t('pa.menu.dataDefine'), value: 'DATA_DEFINITION', number: 0 },
    { label: this.$t('pa.thirdPartyLibs'), value: 'ASSETS', number: 0 },
    { label: this.$t('pa.method'), value: 'FUNCTION', number: 0 }
  ];
  tableData = [];
  @Inject('enableSql') enableSql;
  @InjectReactive('isCloud') isCloud!: boolean;
  get pageSizes() {
    return Array.from({ length: 4 }, (i, index) => this.pageData.pageSize * (index + 1));
  }
  created() {
    this.init();
    if (!this.enableSql) {
      this.types = this.types.filter((el) => ![this.$t('pa.table'), 'UDF', 'CATALOG'].includes(el.label));
    }
    if (this.isCloud) {
      this.types = this.types.filter(
        (el) => ![this.$t('pa.thirdPartyLibs'), this.$t('pa.menu.dataDefine'), this.$t('pa.method')].includes(el.label)
      );
    }
  }

  init() {
    this.keywords = this.$route.query.keywords as string;
    this.getData();
  }

  async getData() {
    try {
      this.loading = true;
      const { data, success, msg, error } = await globalDataQuery(
        {
          search: this.keywords,
          pageData: this.pageData
        },
        this.currenType
      );
      this.loading = false;
      if (success) {
        const reg = new RegExp(`(${this.keywords})`, 'gi');
        data.tableData.length &&
          data.tableData.forEach((item) => {
            item.longName = item.name.replaceAll(reg, (a) => `<span style="color:#377CFF">${a}</span>`);
          });
        this.tableData = data.tableData;
        this.pageData.total = data.countMap[this.currenType];
        this.types.forEach((item) => {
          if (data.countMap[item.value]) {
            item.number = data.countMap[item.value];
          } else {
            item.number = 0;
          }
        });
      } else {
        this.$message.error(error || msg);
      }
    } catch (e) {
      this.loading = false;
    }
  }

  //搜索
  handleSearch() {
    if (!this.keywords) {
      this.$message.error(this.$t('pa.placeholder.keyWord'));
    } else {
      this.pageData.currentPage = 1;
      this.getData();
    }
  }

  handleTypeChange(type = '') {
    this.currenType = type;
    this.pageData.currentPage = 1;
    this.getData();
  }

  handleSizeChange(pageSize) {
    this.pageData.currentPage = 1;
    this.pageData.pageSize = pageSize;
    this.getData();
  }

  handleCurrentChange(currentPage) {
    this.pageData.currentPage = currentPage;
    this.getData();
  }
}
</script>

<style lang="scss" scoped>
.search-container {
  width: 100%;
  height: 100%;
  background: $--bs-color-text-white-primary;
}
.search-header {
  height: 154px;
  padding: 30px 36px 19px;
  border-bottom: 1px solid #e4e7ed;
  &__en {
    padding: 30px 25px 19px;
  }
  ::v-deep .el-input {
    &--suffix {
      width: 720px;
      height: 50px;
      display: flex;
      align-items: center;
    }
    &__inner {
      height: calc(100% - 2px);
      padding-right: 110px;
      border-radius: 0px 8px 8px 0px;
      font-size: 14px;
      line-height: 20px;
    }
    &__suffix {
      width: 72px;
      background: $--bs-color-primary;
      border-radius: 0px 8px 8px 0px;
      .bs-icon-sousuo {
        color: #ffffff;
      }
    }
    &__clear {
      background: transparent !important;
      position: absolute;
      left: -30px !important;
      top: 2px;
      font-size: 20px;
    }
  }

  ::v-deep .bs-search-icon {
    color: $--bs-color-text-white-primary;
    font-size: 20px;
  }

  &__type {
    margin-top: 23px;
    .type {
      display: inline-block;
      padding: 6px 7px;
      margin-right: 10px;
      cursor: pointer;
      background: #f6f6f6;
      border-radius: 4px;
      &__us {
        padding: 6px 2px;
      }
    }
    .is-active {
      background: #ebf2ff;
      border: 1px solid #377cff;
      color: #377cff;
    }
  }
}
.search-content {
  &__content {
    height: calc(100vh - 309px);
    overflow: auto;
  }
}
</style>
