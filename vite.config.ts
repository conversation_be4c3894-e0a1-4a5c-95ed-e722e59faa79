import path from 'path';
import { defineConfig } from 'vite';
import { createVuePlugin } from 'vite-plugin-vue2';
import requireContext from 'rollup-plugin-require-context';
import { viteCommonjs } from '@originjs/vite-plugin-commonjs';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import requireTransform from 'vite-plugin-require-transform';
const target = 'http://10.100.0.130:34000/';

export default defineConfig({
  css: {
    preprocessorOptions: {
      scss: { additionalData: `@import "@/style/bsui-variables.scss";` }
    }
  },
  define: { 'process.env': { IS_VITE: true } },
  optimizeDeps: {
    include: ['jquery']
  },
  plugins: [
    createVuePlugin({ jsx: true }),
    viteCommonjs(),
    requireContext(),
    requireTransform({
      fileRegex: /.ts$|.js$|.tsx$|.vue$/
    }),
    createSvgIconsPlugin({
      iconDirs: [path.resolve(process.cwd(), 'src/assets/svg')],
      symbolId: 'icon-[name]',
      inject: 'body-last',
      customDomId: '__svg__icons__dom__'
    })
  ],
  resolve: {
    alias: [
      { find: '@', replacement: path.resolve(__dirname, 'src') },
      { find: /^~/, replacement: '' }
    ]
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    proxy: {
      '/api': {
        target,
        changeOrigin: true,
        ws: true,
        rewrite: (path) => path.replace(/^\/api/, '/pipe')
      },
      '/rs': {
        target,
        changeOrigin: true,
        ws: true,
        rewrite: (path) => path.replace(/^\/rs/, '/pipe/rs')
      },
      '/cluster': {
        target,
        changeOrigin: true,
        ws: true,
        rewrite: (path) => path.replace(/^\/cluster/, '/pipe/cluster')
      },
      '/charts': {
        target,
        changeOrigin: true,
        ws: true,
        rewrite: (path) => path.replace(/^\/charts/, '/pipe/charts')
      }
    }
  }
});
