<template>
  <div class="node__container">
    <el-popover v-if="isGroup" placement="right" width="400" trigger="hover">
      <relation :data="data.children" />
      <name-type
        slot="reference"
        :name="name"
        :direction="direction"
        :content="data.content"
        :hidden-line="hiddenLine"
      />
    </el-popover>
    <name-type
      v-else
      :name="name"
      :direction="direction"
      :content="data.content"
      :hidden-line="hiddenLine"
    />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import NameType from './name.vue';

@Component({
  components: {
    NameType,
    Relation: () => import('./index.vue')
  }
})
export default class NodeType extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: 'left' }) direction!: string;

  get name() {
    return this.data.name || '';
  }
  get isGroup() {
    return Array.isArray(this.data.children) && this.data.children.length > 0;
  }
  get hiddenLine() {
    return !Boolean(this.data.hiddenLine);
  }
}
</script>
<style lang="scss" scoped>
.node {
  &__container {
    display: inline-block;
  }
}
</style>
