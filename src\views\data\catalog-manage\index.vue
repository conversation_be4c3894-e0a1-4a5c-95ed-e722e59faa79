<template>
  <pro-page :title="$t('pa.cataLog.text5')" :fixed-header="false" class="catalog-content">
    <div slot="operation" class="operate-box">
      <bs-search
        v-model="search"
        :class="{ 'catalog-content__search': isEn }"
        :placeholder="$t('pa.placeholder.keyPlaceholder')"
        @search="getListData()"
      />
      <el-button v-access="'PA.DATA.CATALOG.ADD'" type="primary" style="margin-left: 10px" @click="newCatalog">
        {{ $t('pa.action.register') }}
      </el-button>
    </div>
    <bs-table
      v-loading="tableLoading"
      :height="selectedIds.length ? 'calc(100vh - 344px)' : 'calc(100vh - 288px)'"
      :data="tableData"
      :column-data="columnData"
      :page-data="pageData"
      :selection="true"
      @row-dblclick="viewDetail"
      @page-change="handlePageChange"
      @selection-change="handleSelectionChange"
      @refresh="getListData(true)"
    >
      <el-button slot="headerOperator" v-access="'PA.DATA.CATALOG.DELETE'" size="small" @click="delCatalogs()">
        {{ $t('pa.action.del') }}
      </el-button>
      <div slot="catalogName" slot-scope="{ row }" class="viewName-slot">
        <el-tooltip :content="row.catalogName" effect="light" placement="top">
          <div class="viewName">{{ row.catalogName }}</div>
        </el-tooltip>
        <el-tag v-if="row.shareFlag" size="mini" style="margin-left: 8px"> {{ $t('pa.action.share') }} </el-tag>
      </div>
      <template slot="header-relationNum">
        <div class="catalog-content__relations">
          <span> {{ $t('pa.data.text1') }} </span>
          <el-tooltip effect="light" :content="$t('pa.data.text2')">
            <i class="iconfont icon-wenhao"></i>
          </el-tooltip>
        </div>
      </template>
      <!-- 操作按钮（编辑、查看、删除、分享） -->
      <template slot="operator" slot-scope="{ row }">
        <el-tooltip
          v-for="item in getButtons(row.shareFlag)"
          :key="item.label"
          v-access="item.authCode"
          :content="item.label"
          effect="light"
          class="catalog-content__main--tooltip"
        >
          <i :class="item.class" class="catalog-content__main--icon" @click="operateHandler(item.event, row)"></i>
        </el-tooltip>
      </template>
    </bs-table>
    <!-- 新建 -->
    <catalog-add-edit
      v-if="showAddEditDialog"
      ref="ShareDialog"
      :visible.sync="showAddEditDialog"
      :catalog-info="catalogInfo"
      @close="getListData()"
    />
    <!-- 分享 -->
    <share-dialog
      v-if="showShareDialog"
      ref="ShareDialog"
      :visible.sync="showShareDialog"
      :data="shareData"
      type="catalog"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import CommonDelete from '@/utils/mixins/common-delete';
import { getCatalogList, delCatalog } from '@/apis/dataApi';
import dayjs from 'dayjs';
import { hasPermission } from '@/utils';
@Component({
  components: {
    ShareDialog: () => import('@/components/share-dialog/index.vue'),
    CatalogAddEdit: () => import('./modals/add-edit-dialog.vue')
  },
  mixins: [CommonDelete]
})
export default class CatalogManage extends Vue {
  tableLoading = false;
  search = '';
  pageData = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 };
  tableData: any = [];
  columnData: any = [];
  selectedIds: string[] = [];
  shareData: any = {};
  //分享弹窗
  showShareDialog = false;
  //新建编辑弹窗
  showAddEditDialog = false;
  catalogInfo = {};

  catalogConfigButtons = [
    {
      label: this.$t('pa.action.edit'),
      class: 'iconfont icon-bianji',
      event: 'editCatalog',
      authCode: 'PA.DATA.CATALOG.EDIT'
    },
    {
      label: this.$t('pa.action.view'),
      class: 'iconfont icon-chakan',
      event: 'viewDetail',
      authCode: 'PA.DATA.CATALOG.VIEW_DETAIL',
      shareFlag: true //默认他人分享给你的数据，你有查看权限
    },
    {
      label: this.$t('pa.action.del'),
      class: 'iconfont icon-shanchu',
      event: 'delCatalogs',
      authCode: 'PA.DATA.CATALOG.DELETE'
    },
    {
      label: this.$t('pa.action.share'),
      class: 'iconfont icon-fenxiang',
      event: 'shareCatalog',
      authCode: 'PA.DATA.CATALOG.SHARE'
    }
  ];
  created() {
    this.getListData();
  }
  //获取表格数据
  async getListData(refresh = false) {
    try {
      this.tableLoading = true;
      const params = { pageData: this.pageData, search: this.search };
      const { success, msg, data } = await getCatalogList(params);
      if (!success) return this.$message.error(msg);
      data.columnData.push({ label: this.$t('pa.action.action'), value: 'operator', width: 180, fixed: 'right' });
      data.columnData.forEach((el) => {
        if (el.prop) {
          el.value = el.prop;
          delete el.prop;
        }
      });
      data.columnData[0].fixed = 'left';
      this.columnData = data.columnData;
      this.tableData = data.tableData;
      this.tableData.forEach((el) => {
        el.updateTime = dayjs(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
        el.createTime = dayjs(el.createTime).format('YYYY-MM-DD HH:mm:ss');
      });
      this.pageData.total = data.pageData.total;
      refresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    } finally {
      this.tableLoading = false;
    }
  }
  //获取操作按钮，数据权限控制
  getButtons(shareFlag) {
    return shareFlag ? this.catalogConfigButtons.filter((item) => item.shareFlag) : this.catalogConfigButtons;
  }
  // 表格页码修改回调
  handlePageChange(page: number, size: number) {
    this.pageData.currentPage = page;
    this.pageData.pageSize = size;
    this.getListData();
  }
  // 表格多选回调
  handleSelectionChange(sel: any) {
    this.selectedIds = sel;
  }
  //新建
  newCatalog() {
    this.catalogInfo = {};
    this.showAddEditDialog = true;
  }
  // 操作按钮触发
  operateHandler(event, row) {
    this[event](row);
  }
  // 查看详情
  viewDetail(row: any) {
    if (!hasPermission('PA.DATA.CATALOG.VIEW_DETAIL')) return;
    this.$router.push({
      path: '/data/catalogDetail',
      query: { title: this.$t('pa.cataLog.test6'), id: row.id }
    });
  }
  // 删除
  delCatalogs(row: any) {
    const ids = !row ? this.selectedIds : { id: row.id };
    this['commonDel'](ids, async (delIds) => {
      const { success, msg, error } = await delCatalog(delIds);
      if (success) {
        this.getListData();
        this.$message.success(msg);
      } else {
        this.$message.error(error);
      }
    });
  }
  //编辑
  editCatalog(row) {
    this.catalogInfo = row;
    this.showAddEditDialog = true;
  }
  //分享
  shareCatalog(row) {
    this.shareData = row;
    this.showShareDialog = true;
  }
}
</script>
<style scoped lang="scss">
.catalog-content {
  .viewName-slot {
    display: flex;
    align-items: center;
  }
  .viewName {
    display: inline-block;
    max-width: 120px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  &__search {
    width: 235px !important;
    ::v-deep .el-input {
      width: 235px;
    }
  }
  &__main {
    &--tooltip {
      cursor: pointer;
    }
    &--icon {
      margin: 0 5px;
    }
  }
  &__relations {
    display: flex;
    align-items: center;
    .icon-wenhao {
      margin-left: 5px;
    }
  }
}
</style>
