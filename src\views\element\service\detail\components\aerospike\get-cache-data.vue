<template>
  <query-block
    :loading="loading"
    :title="$t('pa.cacheQuery')"
    :result="result"
    :form-data="formData"
    :form-item="formItem"
    :form-rule="formRule"
    @submit="handleSubmit"
  />
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getServerCacheData } from '@/apis/serviceApi';

@Component({
  components: { QueryBlock: () => import('../components/query-block.vue') }
})
export default class CacheData extends Vue {
  @Prop({ default: () => ({}) }) data!: any;

  loading = false;
  result = '';
  formData: any = { id: this.data.id, key: '', namespace: '', set: '' };
  formItem: any[] = [
    { label: 'namespace', value: 'namespace', type: 'input', props: {} },
    { label: 'set', value: 'set', type: 'input', props: {} },
    { label: 'key', value: 'key', type: 'input', props: {} }
  ];
  formRule: any = {
    namespace: [{ required: true, message: this.$t('pa.placeholder.namespace'), trigger: 'blur' }],
    set: [{ required: true, message: this.$t('pa.placeholder.set'), trigger: 'blur' }],
    key: [{ required: true, message: this.$t('pa.placeholder.key'), trigger: 'blur' }]
  };

  get type() {
    return this.data?.resType === 'AEROSPIKE' ? 'aerospike' : 'cubebase';
  }

  async handleSubmit() {
    try {
      this.loading = true;
      const { success, data, error } = await await getServerCacheData(this.type, this.formData);
      if (!success) return this.$message.error(error);
      this.result = data || this.$t('pa.noData');
    } finally {
      this.loading = false;
    }
  }
}
</script>
