<template>
  <table-block
    :title="$t('pa.queueResourceDetail')"
    :height="height"
    :loading="loading"
    :table-data="tableData"
    :column-data="columnData"
  >
    <bs-search slot="operation" v-model="search" :placeholder="$t('pa.placeholder.queueName')" @search="handleSearch" />
  </table-block>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getFlinkQueueList } from '@/apis/serviceApi';
import { safeArray, includesPro } from '@/utils';

@Component({
  components: { TableBlock: () => import('../components/table-block.vue') }
})
export default class QueueResourceInfo extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: () => ({}) }) params!: any;

  loading = false;
  search = '';
  columnData: any[] = [];
  rawTableData: any[] = [];
  tableData: any[] = [];

  get height() {
    return this.params?.height || '300px';
  }

  created() {
    this.getQueueList();
  }

  async getQueueList() {
    try {
      this.loading = true;
      const { success, data, error } = await getFlinkQueueList(this.data.id);
      if (!success) return this.$message.error(error);
      this.columnData = safeArray(data?.columnData).map((it) => {
        it.value = it.prop;
        return it;
      });
      this.rawTableData = safeArray(data?.tableData);
      this.handleSearch();
    } finally {
      this.loading = false;
    }
  }
  handleSearch() {
    this.tableData = this.rawTableData.filter((it) => includesPro(it.queue, this.search));
  }
}
</script>
