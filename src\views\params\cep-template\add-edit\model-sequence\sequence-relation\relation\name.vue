<template>
  <div class="name__container" :class="{ xxx: isBottom }">
    <template v-if="direction === 'right'">
      <!-- line -->
      <line-type v-if="hiddenLine" :direction="direction" :content="content" />
      <!-- name -->
      <div class="name-main">{{ name }}</div>
    </template>
    <template v-else>
      <!-- name -->
      <div class="name-main">{{ name }}</div>
      <!-- line -->
      <line-type v-if="hiddenLine" :direction="direction" :content="content" />
    </template>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { hide } from '../../../../util';
import LineType from './line.vue';

@Component({ directives: { hide }, components: { LineType } })
export default class NameType extends Vue {
  @Prop({ default: '' }) name!: string;
  @Prop({ default: '' }) content!: string;
  @Prop({ default: '' }) direction!: string;
  @Prop({ default: true }) hiddenLine!: boolean;

  get isBottom() {
    return this.direction === 'bottom';
  }
}
</script>
<style lang="scss" scoped>
.xxx {
  display: block !important;
  width: $modulelWidth;
  // flex-flow: wrap;
}
.name {
  &__container {
    display: flex;
    &--left {
      display: flex;
      align-items: center;
      height: 32px;
    }
    &--bottom {
      width: $modulelWidth;
    }
  }
  &-main {
    display: inline-block;
    padding: 0 20px;
    width: $modulelWidth;
    height: 34px;
    line-height: 34px;
    text-align: center;
    font-size: 12px;
    border: 1px solid #0096f0;
    border-radius: 5px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    box-sizing: border-box;
    vertical-align: middle;
  }
  &-one {
    width: 50px;
    height: 1px;
    background: #7f8081;
  }

  &__logic {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  &__detail {
    margin: 20px 0;
  }
  &__canvas {
    width: 100%;
    height: 300px;
    border: 1px solid;
    overflow: hidden;
  }
  &-item {
    display: inline-block;
    // width: 100px;
    height: 60px;
  }
  &-row {
    display: flex;
    align-items: center;
    .aa {
      padding: 0 20px;
      width: 160px;
      height: 34px;
      line-height: 34px;
      text-align: center;
      font-size: 12px;
      border: 1px solid #0096f0;
      border-radius: 5px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      box-sizing: border-box;
    }
  }
}
</style>
