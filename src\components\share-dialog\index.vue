<template>
  <bs-dialog
    v-loading="loading"
    :title="$t('pa.action.share')"
    size="large"
    :visible.sync="visible"
    :before-close="closeDialog"
  >
    <div class="share-dialog">
      <share-left
        ref="shareLeft"
        class="share-dialog__left"
        :class="{ isEn }"
        :cur-tab.sync="activeTab"
        :res-id="id"
        :type="type"
        @checkChange="checkChange"
        @nodeClick="nodeClick"
      />
      <div class="share-dialog__right">
        <div v-show="orgData[activeTab].checkedNode.length" class="share-dialog__right__checked">
          <div class="title">{{ $t('pa.tip.selectedOrg', [orgData[activeTab].checkedNode.length]) }}</div>
          <el-tag
            v-for="node in orgData[activeTab].checkedNode"
            :key="node.id"
            type="info"
            size="small"
            color="#F6F6F6"
            closable
            disable-transitions
            @close="closeTag(node.id)"
          >
            {{ node.name }}
          </el-tag>
        </div>
        <div v-if="type === 'kafka'" class="share-dialog__right__topic">
          <span class="title">{{ $t('pa.tip.shareTopic') }}</span>
          <bs-page-select v-model="orgData[activeTab].sharedTopic" :options="topicOptions" filterable style="width: 257px" />
        </div>
        <div class="share-dialog__right__authority">
          <span class="title">{{ $t('pa.tip.shareAccess') }}</span>
          <el-checkbox
            v-for="item in orgData[activeTab].authority"
            :key="item.name"
            v-model="item.checked"
            :disabled="item.disabled"
          >
            {{ item.label }}
          </el-checkbox>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">{{ $t('pa.action.cancel') }}</el-button>
      <el-button
        v-if="activeTab === 'allOrg' || type === 'kafka'"
        type="primary"
        :disabled="disabled"
        @click="handelBatch('add')"
      >
        {{ $t('pa.action.batchUAdd') }}
      </el-button>
      <el-button type="primary" :disabled="disabled" @click="handelBatch('delete')">
        {{ $t('pa.action.batchDel') }}
      </el-button>
    </span>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { URL_LIST_SHARE_AUTHORITIES, URL_LIST_SHARED_TOPIC } from '@/apis/commonApi';
import { get, post } from '@/apis/utils/net';
import { patchMap } from './utils';
import { getKafkaTopicList } from '@/apis/serviceApi';
@Component({
  components: { ShareLeft: () => import('./share-left.vue') }
})
export default class ShareDialog extends Vue {
  @Prop({ default: false, type: Boolean }) visible;
  @Prop({ default: 'server', type: String }) type;
  @Prop() data!: any;

  loading = false;
  activeTab = 'allOrg';
  orgData = {
    allOrg: {
      checkedNode: [],
      authority: [],
      sharedTopic: []
    },
    sharedOrg: {
      checkedNode: [],
      authority: [],
      sharedTopic: []
    }
  };
  topicOptions = [];

  get disabled() {
    if (this.type !== 'kafka') return !this.orgData[this.activeTab].checkedNode.length;
    return !this.orgData[this.activeTab].checkedNode.length || !this.orgData[this.activeTab].sharedTopic.length;
  }

  get id() {
    return this.data.id;
  }
  get showTopic() {
    return this.type === 'kafka';
  }
  created() {
    this.init();
  }

  init() {
    this.getAuthority();
    this.type === 'kafka' && this.getTopicOptions();
  }

  async getAuthority() {
    try {
      this.loading = true;
      const { data, success, msg, error } = await get(URL_LIST_SHARE_AUTHORITIES);
      this.loading = false;
      if (!success) return this.$message.error(error || msg);
      this.$set(this.orgData.allOrg, 'authority', data);
      this.$set(this.orgData.sharedOrg, 'authority', data);
    } catch (e) {
      this.loading = false;
    }
  }

  async getTopicOptions() {
    try {
      this.loading = true;
      const { data, success, msg, error } = await getKafkaTopicList(this.id);
      this.loading = false;
      if (!success) return this.$message.error(error || msg);
      this.$set(
        this,
        'topicOptions',
        data.map((item) => {
          return { value: item.id, label: item.topic };
        })
      );
    } catch (e) {
      this.loading = false;
    }
  }

  async getSharedTopic({ id, resId }) {
    const { data, success, msg, error } = await get(URL_LIST_SHARED_TOPIC, {
      orgId: id,
      resId: this.data.id
    });
    if (!success) return this.$message.error(error || msg);
    this.orgData[this.activeTab].sharedTopic = data.map((item) => item.id);
  }

  checkChange(data) {
    if (this.type == 'kafka' && this.orgData[this.activeTab].checkedNode.length == 0) {
      this.orgData[this.activeTab].sharedTopic = [];
    }
    this.orgData[this.activeTab].checkedNode = data;
    if (data.length === 0) {
      this.orgData[this.activeTab].sharedTopic = [];
    }
  }

  //点击机构回显topic
  nodeClick(data) {
    if (this.type !== 'kafka' || this.orgData[this.activeTab].checkedNode.length) return;
    this.getSharedTopic(data);
  }

  closeTag(id) {
    (this.$refs.shareLeft as any).closeTag(id);
  }

  async handelBatch(type: string) {
    try {
      this.loading = true;
      const { checkedNode, authority, sharedTopic } = this.orgData[this.activeTab];
      const params: any = {
        [this.type === 'catalog' ? 'catalogId' : 'resId']: this.id,
        orgIds: checkedNode.map((item) => item.id),
        authority: authority,
        operatorType: type
      };
      if (this.type === 'kafka') params.topics = sharedTopic;
      const { success, msg, error } = await post(patchMap[this.type], params);
      this.loading = false;
      if (!success) return this.$message.error(error || msg);
      this.$message.success(msg);
      this.closeDialog();
    } catch (e) {
      this.loading = false;
    }
  }

  closeDialog() {
    this.loading = false;
    this.$emit('update:visible', false);
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  max-height: 330px !important;
  height: 330px !important;
  padding: 0;
}
.share-dialog {
  display: flex;
  height: 100%;
  &__left {
    width: 242px;
    overflow: auto;
    &.isEn {
      width: 370px;
    }
  }
  &__right {
    flex: 1;
    border-left: 1px solid $--bs-color-border-lighter;
    overflow: auto;
    &__checked {
      height: 294px;
      padding: 21px 11px 21px 21px;
      border-bottom: 1px solid $--bs-color-border-lighter;
      .title {
        margin-bottom: 16px;
      }
      .el-tag {
        margin-bottom: 12px;
        margin-right: 10px;
      }
    }
    &__topic {
      padding: 21px 21px 5px;
      .title {
        margin-right: 8px;
      }
    }
    &__authority {
      padding: 21px;
      .title {
        margin-right: 27px;
      }
    }
    .title {
      font-weight: bold;
    }
  }
}
::v-deep .el-tag__close.el-icon-close {
  background-color: #d8d8d8;
  color: #fff;
}
</style>
