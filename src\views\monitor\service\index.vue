<template>
  <pro-page title="服务监控" :fixed-header="viewType === 'LIST' ? false : true">
    <div slot="operation">
      <el-button type="primary" icon="el-icon-sort" @click="changeViewType">
        {{ viewType === 'GRID' ? '全局视图' : '列表视图' }}
      </el-button>
      <el-button v-if="viewType !== 'LIST'" icon="el-icon-refresh-right" @click="getResTypeList()">
        刷新
      </el-button>
    </div>
    <div v-show="viewType === 'GRID'" class="sevice-content">
      <!-- 全局视图 -->
      <div class="center-info">
        <div
          v-for="item in tableNameArray"
          :key="item.label"
          class="card"
          @click="toClusters(item)"
        >
          <div class="sevice-item">
            <div class="sevice-item__img">
              <svg-icon :name="item.icon" style="width: 40px; height: 40px" />
            </div>
            <div class="sevice-item__info">
              <span class="name">{{ item.name }}</span>
              <span class="num">{{ item.count }}</span>
            </div>
          </div>
          <div class="warn-info">
            <div>
              <el-badge is-dot class="item" type="success" />
              <span class="info">连接 </span>
              <span class="value">{{ item.connectCount }}</span>
            </div>
            <div>
              <el-badge is-dot class="item" type="danger" />
              <span class="info">未连接 </span>
              <span class="value">{{ item.unConnectCount }}</span>
            </div>
            <div>
              <el-badge is-dot class="item" type="primary" />
              <span class="info">未知 </span>
              <span class="value">{{ item.unknowCount || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="viewType === 'LIST'" class="my-wrap-row">
      <div class="left-tree">
        <el-tree
          :data="tableNameArray"
          :props="treeProps"
          :load="loadNode"
          lazy
          accordion
          @node-click="handleNodeClick"
        />
      </div>
      <div class="right-table">
        <div v-if="nodeData.name" class="tab-title">
          <div class="title-text">{{ nodeData.name }}</div>
          <div class="title-search">
            <el-button
              v-show="showDetailBtn()"
              size="small"
              type="primary"
              style="float: right"
              @click="detail"
            >
              服务详情
            </el-button>
          </div>
        </div>
        <div class="tab-content">
          <bs-table
            v-loading="tableLoading"
            :height="nodeData.name ? 'calc(100vh - 327px)' : 'calc(100vh - 242px)'"
            :data="tableData.tableData"
            :column-data="tableData.columnData"
            :table-data="tableData"
            @refresh="getListData(id)"
          >
            <div slot="status" slot-scope="{ row }">
              <bs-tag size="mini" :color="row.statusColor">
                {{ row.status }}
              </bs-tag>
            </div>
          </bs-table>
        </div>
      </div>
    </div>
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue, Inject } from 'vue-property-decorator';
import {
  URL_RES_LIST,
  URL_HOST_LIST,
  URL_RES_NODE_LISTFORMONITOR,
  URL_RESCONF_GETRESTYPELIST
} from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
import moment from 'moment';
@Component
export default class ServiceMonitor extends PaBase {
  @Inject('enableSql') enableSql;
  // 视图类型
  viewType: 'GRID' | 'LIST' = 'GRID';
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: {
      createTime: 'DESC'
    }
  };
  tableData: ITableData = {
    columnData: [],
    tableData: []
  };
  fetchList!: () => void;

  tableNameArray: any[] = [];
  treeProps: any = {
    label: 'name',
    children: 'cols',
    isLeaf: 'leaf'
  };
  private nodeData: any = {};
  private currentNode: any = {};
  id = '';
  // 切换视图类型
  changeViewType() {
    this.viewType = this.viewType === 'GRID' ? 'LIST' : 'GRID';
  }
  mounted() {
    this.getResTypeList();
  }
  // handleCurrentChange(currentPage, pageSize) {
  //   this.searchObj.pageData.currentPage = currentPage;
  //   this.searchObj.pageData.pageSize = pageSize;
  // }

  loadNode(node: any, resolve: any) {
    if (node.level === 1) {
      let url = URL_RES_LIST + '/' + node.data.resType;
      if (node.data.resType === 'HOST') {
        url = URL_HOST_LIST;
      }
      this.doPost(url, {}).then((resp: any) => {
        this.parseResponse(resp, () => {
          const data: any[] = [];
          resp.data.tableData.forEach((n: any) => {
            data.push({
              record: n,
              level: node.level,
              name: n.title,
              leaf: true
            });
          });
          resolve(data);
        });
      });
    }
    if (node.level === 0) {
      resolve(this.tableNameArray);
    }
  }
  handleNodeClick(data: any, node: any) {
    if (data.level === 1) {
      // this.showTitle = true;
      this.nodeData = data;
      this.currentNode = node.parent;
      this.id = data.record.id;
      this.getListData(data.record.id);
    } else {
      // this.showTitle = false;
      this.tableData = {
        tableData: [],
        columnData: []
      };
    }
  }
  showDetailBtn() {
    if (this.nodeData.record) {
      return this.hasFeatureAuthority(this.currentNode.data.detailAuthCode);
    }
    return false;
  }
  detail() {
    const type = _.toLower(this.nodeData.record.resType);
    this.$router.push({
      path:
        '/element/clusters/' +
        type +
        '/detail?id=' +
        this.nodeData.record.id +
        '&resType=' +
        this.nodeData.record.resType +
        '&title=' +
        this.nodeData.name
    });
  }

  getListData(resId: string) {
    this.tableLoading = true;
    this.doGet(URL_RES_NODE_LISTFORMONITOR, {
      params: {
        resId
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        const { columnData, tableData } = resp.data;
        columnData.forEach((el) => {
          el.value = el.prop;
        });
        tableData.forEach((el) => {
          el.createTime = moment(el.createTime).format('YYYY-MM-DD HH:mm:ss');
          el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
          const statusColumn = columnData.find((item) => item.prop === 'status');
          const status = el.status;
          el.status = statusColumn?.enumData[status];
          el.statusColor = statusColumn?.enumColor[status];
        });
        this.tableData = {
          columnData,
          tableData
        };
        this.searchObj.pageData.total = tableData.length;
        resp.data.tableData.forEach((n) => {
          n.url = n.ip + ':' + n.port;
          const rowData = _.cloneDeep(n);
          rowData.resProperty = '';
          // this.ws.next(rowData);
        });
      });
      this.tableLoading = false;
    });
  }

  wsCallback(res: any) {
    if (res.id) {
      const rec = _.find(this.tableData.tableData, { id: res.id });
      if (rec) {
        this.$set(rec, 'status', res.status);
      }
    }
  }
  getResTypeList() {
    this.tableNameArray = [];
    Vue.axios.get(URL_RESCONF_GETRESTYPELIST).then((resp: any) => {
      if (resp.success) {
        const { data } = resp;
        const list = data.sort((a, b) => a.type.charCodeAt(0) - b.type.charCodeAt(0));
        list.forEach((n) => {
          if (n.type !== 'HOST' && this.hasFeatureAuthority(n.listConf.menuAuthCode)) {
            this.tableNameArray.push({
              resType: n.type,
              label: n.label,
              count: n.count,
              name: n.label,
              icon: n.type.toLowerCase(),
              authCode: n.listConf.menuAuthCode,
              detailAuthCode: n.listConf.menuAuthCode,
              connectCount: n.connectCount,
              unConnectCount: n.unConnectCount,
              unknowCount: n.unknowCount,
              type: n.type
            });
          }
        });
        // 服务监控无SQL模块开关能力时过滤daemon、dts
        !this.enableSql &&
          (this.tableNameArray = this.tableNameArray.filter(
            (el) => !['daemon', 'dts'].includes(el.label)
          ));
      }
    });
  }
  // 跳转至集群列表
  toClusters({ label, type }) {
    this.$router.push({
      path: `/monitor/clusters/${type}`,
      query: {
        type,
        title: label
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.sevice-content {
  padding-top: 20px;
  width: 100%;
  //height: calc(100vh - 200px);
}
.sevice-content .card {
  border: 1px solid #f5f5f5;
  border-radius: 4px;
  margin-bottom: 15px;
}

.sevice-item {
  display: flex;
  align-items: center;
  height: 120px;
  background: #fff;
  cursor: pointer;
  border-bottom: 1px solid #f1f1f1;
}
.sevice-item__img {
  width: 72px;
  height: 72px;
  margin: 0 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: $--bs-color-background-page;
  border-radius: 23px;
}
.sevice-item__info {
  display: flex;
  flex-direction: column;
  padding-right: 20px;
  & .name {
    font-size: 16px;
    line-height: 20px;
    color: $--bs-color-text-secondary;
  }
  & .num {
    font-size: 32px;
    line-height: 46px;
  }
}
.warn-info {
  display: flex;
  justify-content: space-between;
  background: #fff;
  line-height: 31px;
  margin-bottom: 20px;
  padding: 0 24px;
  font-size: 12px;
  .info {
    color: #777;
  }
  .value {
    color: #444;
  }
}
.item {
  margin-top: 10px;
  margin-right: 4px;
}
.my-wrap-row {
  display: flex;
  height: calc(100vh - 180px);
}
.tab-title {
  background: #fff;
}
.left-tree {
  overflow: auto;
  width: 200px;
  padding: 10px;
  background: #fff;
  border-right: 1px solid #f1f1f1;
  ::v-deep .el-tree {
    margin: 10px;
    &-node {
      &__content {
        height: 32px;
        &:hover {
          background-color: #f9f9f9;
          border-radius: 6px;
        }
      }
      &__label {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &:focus {
        > .el-tree-node__content {
          background-color: #f6f6f6;
          border-radius: 6px;
        }
      }
    }
  }
}
.right-table {
  display: flex;
  flex-direction: column;
  width: calc(100% - 200px);
  height: 100%;
  .tab-content {
    flex: 1;
  }
}
::v-deep .el-icon-sort {
  transform: rotate(90deg);
}
.center-info {
  height: 100%;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  &::after {
    content: '';
    width: 39.2%;
  }
}
.card {
  height: 160px;
  width: 20%;
  min-width: 250px;
  padding-right: 10px;
}
.card:nth-child(5n) {
  padding-right: 0;
}
@media (max-width: 1480px) {
  .card {
    width: 25%;
    min-width: 0px;
    height: 172px;
    &:nth-child(4n) {
      padding-right: 0;
    }
    &:nth-child(5n) {
      padding-right: 10px;
    }
  }
}
</style>
