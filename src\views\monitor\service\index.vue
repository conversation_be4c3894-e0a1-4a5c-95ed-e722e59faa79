<template>
  <pro-page v-loading="loading" :title="$t('pa.menu.serviceMonitor')" :fixed-header="true">
    <!-- operation -->
    <el-button slot="operation" icon="el-icon-refresh-right" @click="getServiceList(true)">
      {{ $t('pa.action.refresh') }}
    </el-button>
    <pro-grid :gutter="10" class="monitor-service-body">
      <pro-grid
        v-for="it in serviceList"
        :key="it.label"
        type="card"
        :col-span="isEn ? 4 : 3"
        @click.native="handleClick(it)"
      >
        <!-- top -->
        <div class="monitor-service-top">
          <div class="monitor-service-top__icon" v-html="it.icon"></div>
          <div>
            <div class="monitor-service-top__name">{{ it.label }}</div>
            <div class="monitor-service-top__count">{{ it.count }}</div>
          </div>
        </div>
        <div class="monitor-service-status">
          <div v-for="el in statusList" :key="el.value" class="monitor-service-status__item">
            <el-badge is-dot :type="el.type" class="monitor-service-status__dot" />
            <span class="monitor-service-status__label">{{ el.label }} </span>
            <span class="monitor-service-status__value">{{ it[el.value] }}</span>
          </div>
        </div>
      </pro-grid>
    </pro-grid>
  </pro-page>
</template>

<script lang="ts">
import { Component, Inject, Vue } from 'vue-property-decorator';
import { getServiceConf } from '@/apis/serviceApi';
import { safeArray } from '@/utils';
import { getIconByType } from '@/views/element/service/utils';

@Component
export default class ServiceMonitor extends Vue {
  @Inject('enableSql') enableSql;

  loading = false;
  serviceList: any[] = [];
  tableNameArray: any[] = [];
  statusList: any[] = [
    {
      label: this.$t('pa.monitor.service.connect'),
      type: 'success',
      value: 'connectCount'
    },
    {
      label: this.$t('pa.monitor.service.unConnect'),
      type: 'danger',
      value: 'unConnectCount'
    },
    {
      label: this.$t('pa.status.unKnow'),
      type: 'primary',
      value: 'unknownCount'
    }
  ];

  created() {
    this.getServiceList();
  }

  /* 获取服务列表 */
  async getServiceList(isRefresh = false) {
    try {
      this.loading = true;
      const { success, data, error } = await getServiceConf();
      if (!success) return this.$message.error(error);
      this.serviceList = safeArray(data)
        .map((it) => {
          if (it.type === 'HOST') return null;
          if (!this.enableSql && ['daemon', 'dts'].includes(it.label)) return null;
          return {
            label: it.label,
            type: it.type,
            count: it.count,
            icon: it.iconByte ? getIconByType(it.iconByte, it.type, 40) : null,
            connectCount: it.connectCount || 0,
            unConnectCount: it.unConnectCount || 0,
            unknownCount: it.unknownCount || 0
          };
        })
        .filter(Boolean);
      isRefresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    } finally {
      this.loading = false;
    }
  }
  // 跳转至集群列表
  handleClick({ label, type }) {
    this.$router.push({ path: `/monitor/clusters/${type}`, query: { type, title: label } });
  }
}
</script>

<style lang="scss" scoped>
.monitor-service {
  &-body {
    margin-top: 20px;
  }
  &-top {
    display: flex;
    align-items: center;
    padding: 24px;
    height: 120px;
    cursor: pointer;
    border-bottom: 1px solid #f1f1f1;
    &__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24px;
      width: 72px;
      height: 72px;
      background: $--bs-color-background-page;
      border-radius: 23px;
    }
    &__name {
      font-size: 16px;
      line-height: 20px;
      color: $--bs-color-text-secondary;
    }
    &__count {
      font-size: 32px;
      line-height: 46px;
    }
  }
  &-status {
    display: flex;
    align-items: center;
    padding: 0 24px;
    width: 100%;
    height: 40px;
    font-size: 12px;
    &__item {
      display: flex;
      align-items: center;
      flex: 1 1 auto;
    }
    &__dot {
      width: 8px;
      height: 8px;
      overflow: hidden;
    }
    &__label {
      display: inline-block;
      margin: 0 4px;
      color: #444;
    }
    &__value {
      color: #777;
    }
  }
}
</style>
