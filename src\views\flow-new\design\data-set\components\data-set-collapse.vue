<template>
  <div class="data-set-collapse">
    <bs-collapse-item v-if="data.children" :name="data.id">
      <div slot="title" class="data-set-collapse--group utils-ellipsis" :title="data.name">
        {{ data.name }}
        <span v-if="data.loading" class="el-tree-node__loading-icon el-icon-loading"></span>
      </div>
      <bs-collapse
        v-if="data.children"
        v-model="activeCollapse"
        accordion
        :name="data.id"
        @change="handelCollapseChange($event, data)"
      >
        <data-set-collapse
          v-for="node in data.children"
          :key="node.id"
          :data="node"
          :disabled="disabled"
          :is-table-mgr="isTableMgr"
          :conn-options="connOptions"
          class="data-set-collapse--recursion"
          @click="(data) => $emit('click', data)"
          @getSqlByConnInfo="(data) => $emit('getSqlByConnInfo', data)"
          @viewTableInfo="(data) => $emit('viewTableInfo', data)"
        />
      </bs-collapse>
    </bs-collapse-item>
    <div v-else class="data-set-collapse--node">
      <el-popover v-model="showPopover" trigger="click" class="node-popover" :disabled="!data.needConfig || disabled">
        <el-form
          ref="formRef"
          v-loading="formLoading"
          :element-loading-text="$t('pa.flow.msg28')"
          :model="form"
          :rules="formRules"
        >
          <el-form-item v-if="showPopover">
            <el-cascader-panel
              v-model="form.connInfo"
              :options="connOptions"
              :props="{ expandTrigger: 'hover' }"
              @change="handleFormChange"
            />
          </el-form-item>
        </el-form>
        <div slot="reference" class="node-left" @click.stop="handleClick(data)">
          <div class="node-img">
            <i class="iconfont icon-biao node-img-icon"></i>
          </div>
          <div class="utils-ellipsis" :title="data.name">
            {{ data.name }}
          </div>
        </div>
      </el-popover>
      <i class="iconfont icon-chakan table-view-icon" @click.stop="$emit('viewTableInfo', data)"></i>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { getOriginTableByResId, getOriginTableSql, getTableMgrSql } from '@/apis/sqlApi';
import { CollapseItem, ConnInfoOptions } from '../../interface/data-set';
import cloneDeep from 'lodash/cloneDeep';

@Component({ name: 'DataSetCollapse' })
export default class DataSetCollapse extends Vue {
  @Prop() data!: CollapseItem;
  @Prop() disabled!: boolean;
  @Prop() isTableMgr!: boolean;
  @Prop() connOptions!: ConnInfoOptions[];

  public activeCollapse = ''; // 父组件切换collapse时，重置子组件的展开状态
  public activeNode: CollapseItem = { id: '', name: '', resType: '' }; // 父组件切换collapse时，重置子组件的展开状态
  showPopover = false;
  formLoading = false;
  form: any = { connInfo: [] };
  formRules: any = {
    connInfo: [{ required: true, message: this.$t('pa.flow.msg29'), trigger: 'change' }]
  };

  handleFormChange() {
    (this.$refs.formRef as any).validate(async (valid: any) => {
      if (valid) {
        this.formLoading = true;
        const getParams = (data: any) => {
          return this.isTableMgr
            ? { tableId: this.activeNode.id, ...data, jobId: this.$route.query?.flowId }
            : {
                id: this.activeNode.resId,
                tableName: this.activeNode.name,
                resType: this.activeNode.resType,
                ...data,
                jobId: this.$route.query?.flowId
              };
        };
        const params = getParams({
          connInfo: this.form.connInfo[0],
          tableUseFor: this.form.connInfo[1]
        });
        const { data } = this.isTableMgr ? await getTableMgrSql(params) : await getOriginTableSql(params);

        this.showPopover = false;
        this.form.connInfo = [];
        this.formLoading = false;
        this.$emit('getSqlByConnInfo', this.$store.getters.decrypt(data));
      }
    });
  }

  // 原始表-点击展开二级菜单【服务】获取其下的所表
  handelCollapseChange(activeNames, data) {
    const dataSetRef: any = this.$parent.$parent;
    // 收起二级菜单时，搜索条件置灰
    if (!activeNames) {
      dataSetRef.isExpandOriginRes = false;
      dataSetRef.originTableSearch = '';
      return;
    }

    data.children.forEach(async (item: any) => {
      if (item.id === activeNames) {
        this.$set(item, 'loading', true);
        const { data, success, msg } = await getOriginTableByResId(item.id, item.resType);
        item.loading = false;
        if (success) {
          this.$set(item, 'children', data);
        } else {
          this.$message.error(msg);
          return;
        }

        // 处理父组件的originTableList，用于原始表的前端搜索
        if (dataSetRef.$el.className === 'data-set') {
          dataSetRef.isExpandOriginRes = true;
          dataSetRef.sonActiveCollapse = this.activeCollapse;
          dataSetRef.originTableList = cloneDeep(dataSetRef.tableList);
        }
      } else {
        item.children = [];
      }
    });
  }

  handleClick(data) {
    this.activeNode = data;
    // 关闭其他的连接器配置弹窗
    this.$parent.$children.forEach((el: any) => {
      if (el['_uid'] !== this['_uid']) {
        el.showPopover = false;
      }
    });
    this.$emit('click', data);
  }
}
</script>

<style lang="scss" scoped>
.data-set-collapse {
  &--group {
    max-width: 100%;
    font-size: 14px;
    color: #000;
  }
  &--node {
    display: flex;
    align-items: center;
    width: 100%;
    height: 40px;
    margin: 10px 0;
    border: 1px solid #f1f1f1;
    border-radius: 4px;
    cursor: pointer;
    .node-left {
      display: flex;
      align-items: center;
    }
    .node-popover {
      width: calc(100% - 25px);
    }
    &:hover {
      .table-view-icon {
        display: inline-block;
        animation: icon-fade-in 0.3s;
      }
    }
    .node-img {
      width: 38px;
      height: 38px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 8px;
      margin-right: 12px;
      background: #f9f9f9;
      &-icon {
        font-size: 18px;
      }
    }
    .table-view-icon {
      display: none;
      animation: icon-fade-out 0.3s;
      margin-right: 10px;
    }
  }
}
::v-deep .el-form-item {
  margin-bottom: initial;
}
// 渐显
@keyframes icon-fade-in {
  0% {
    display: none;
  }
  100% {
    display: inline-block;
  }
}
// 渐隐
@keyframes icon-fade-out {
  0% {
    display: inline-block;
  }
  100% {
    display: none;
  }
}
</style>
