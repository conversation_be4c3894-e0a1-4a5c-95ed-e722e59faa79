<!-- >保存提示弹窗<-->
<template>
  <bs-dialog :title="$t('pa.data.udf.detail.savePrompt')" :visible.sync="visible" :before-close="closeDialog">
    <div>
      <p style="margin-bottom: 10px; text-align: left">{{ $t('pa.placeholder.updatePlaceholder') }}</p>
      <el-input
        v-model="comments"
        type="textarea"
        rows="5"
        :placeholder="$t('pa.placeholder.updatePlaceholder')"
        autocomplete="off"
        maxlength="100"
        show-word-limit
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">{{ $t('pa.action.cancel') }}</el-button>
      <el-button type="primary" @click="submit()">{{ $t('pa.flow.confirm') }}</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Vue, Prop, Emit, PropSync } from 'vue-property-decorator';

@Component
export default class SaveConfirm extends Vue {
  @PropSync('show', { type: Boolean }) visible!: boolean;
  @Prop({ default: true }) require!: boolean;
  comments = '';
  closeDialog() {
    this.comments = '';
    this.visible = false;
    this.callback(0);
  }

  submit() {
    if (!this.comments && this.require) {
      this.$message.error(this.$t('pa.data.udf.detail.placeholder.updataPlaceholder'));
    } else {
      this.callback(1);
      this.comments = '';
    }
  }

  @Emit('saveConfirmCallback')
  callback(e) {
    if (e) {
      return { comments: this.comments };
    }
  }
}
</script>
<style lang="scss" scoped></style>
