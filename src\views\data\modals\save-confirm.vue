<!-- >保存提示弹窗<-->
<template>
  <bs-dialog :title="'保存提示'" :visible.sync="visible" :before-close="closeDialog">
    <div>
      <p style="margin-bottom: 10px; text-align: left">请输入更新内容</p>
      <el-input
        v-model="comments"
        type="textarea"
        rows="5"
        placeholder="请输入更新内容"
        autocomplete="off"
        maxlength="100"
        show-word-limit
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="submit()">确定</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Vue, Prop, Emit } from 'vue-property-decorator';

@Component
export default class SaveConfirm extends Vue {
  visible = false;
  @Prop({ default: true }) require!: boolean;
  comments = '';
  closeDialog() {
    this.comments = '';
    this.visible = false;
    this.callback(0);
  }

  submit() {
    if (!this.comments && this.require) {
      this.$message.error('请填写更新内容');
    } else {
      this.callback(1);
      this.comments = '';
    }
  }

  @Emit('saveConfirmCallback')
  callback(e) {
    if (e) {
      return { comments: this.comments };
    }
  }
}
</script>
<style lang="scss" scoped></style>
