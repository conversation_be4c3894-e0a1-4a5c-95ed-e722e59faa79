import i18n from '@/i18n';
import store from '@/store';
const getJobTypeOptions = () => {
  if (!store.getters.enableSql && !store.getters.enableJar) return [];
  return [
    ...(store.getters.enableSql
      ? [
          {
            value: 'FLINK_SQL',
            label: 'SQL'
          }
        ]
      : []),
    {
      value: 'PROCESSFLOW',
      label: 'DataStream'
    },
    ...(store.getters.enableJar
      ? [
          {
            value: 'UDJ',
            label: i18n.t('pa.flow.customSize')
          }
        ]
      : [])
  ];
};
export const filterPopoverList = [
  {
    label: i18n.t('pa.flow.status'),
    options: [
      { label: i18n.t('pa.status.published'), value: 'PUB' },
      { label: i18n.t('pa.status.online'), value: 'PROD' },
      { label: i18n.t('pa.status.develop'), value: 'DEV' }
    ],
    value: 'jobStatus'
  },
  {
    label: i18n.t('pa.flow.runStatus'),
    options: [
      {
        value: 'NONE',
        label: i18n.t('pa.status.none')
      },
      {
        value: 'RUNNING',
        label: i18n.t('pa.home.running')
      },
      {
        value: 'FINISHED',
        label: i18n.t('pa.home.text12')
      },
      {
        value: 'FAILED',
        label: i18n.t('pa.home.text13')
      },
      {
        value: 'CANCELED',
        label: i18n.t('pa.home.text14')
      },
      {
        value: 'RESTARTING',
        label: i18n.t('pa.home.restart')
      },
      {
        value: 'UNKNOWN',
        label: i18n.t('pa.status.unKnow')
      }
    ],
    value: 'jobRunTimeStatus'
  },
  {
    label: i18n.t('pa.flow.mode'),
    options: [
      { label: i18n.t('pa.flow.streamingMode'), value: 'stream' },
      { label: i18n.t('pa.flow.batchMode'), value: 'batch' }
    ],
    value: 'mode'
  },
  {
    label: i18n.t('pa.flow.clusterType'),
    options: [
      { label: 'standalone', value: 'STANDALONE' },
      { label: 'yarn_application', value: 'YARN_APPLICATION' }
    ],
    value: 'clusterType'
  },
  ...(getJobTypeOptions().length
    ? [
        {
          label: i18n.t('pa.flow.type'),
          options: getJobTypeOptions(),
          value: 'jobType'
        }
      ]
    : [])
];
