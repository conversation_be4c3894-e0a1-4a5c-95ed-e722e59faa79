<template>
  <div class="header-item__container">
    <li v-for="it in renderList" :key="it.label" :style="{ width: it.width, 'text-align': 'center' }">
      <!-- queue -->
      <div v-if="it.isQueue" class="header-item-queue">
        <span class="header-item-queue__label">{{ it.label }}</span>
        <span>{{ it.value }}</span>
      </div>
      <div v-else class="header-item-title">
        <i class="header-item-point"></i>
        <span>{{ it.label }}</span>
      </div>
      <span v-if="!it.isQueue" class="header-item-total" :style="{ color: it.value < 0 ? 'red' : '#444' }">
        {{ it.value }}
      </span>
    </li>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component
export default class HeaderItem extends Vue {
  @Prop({ default: 0 }) cpu!: number;
  @Prop({ default: '' }) queue!: string;
  @Prop({ default: 0 }) slosts!: number;
  @Prop({ default: 0 }) memory!: number;
  @Prop({ default: false }) isPerJob!: boolean;
  @Prop({ default: () => [] }) tableData!: any[];

  get finalMemory() {
    return this.memory - this.getTotal('afterMemory');
  }
  get finalCpu() {
    return this.cpu - this.getTotal('afterCpu');
  }
  get finalSlosts() {
    return this.slosts - this.getTotal('afterSlots');
  }

  get renderList() {
    return [
      !this.isPerJob && {
        label: this.$t('pa.currentSlots'),
        value: this.slosts,
        width: '50%'
      },
      !this.isPerJob && {
        label: this.$t('pa.remainSlots'),
        value: this.finalSlosts,
        width: '50%'
      },
      this.isPerJob && {
        isQueue: true,
        label: this.$t('pa.queue'),
        value: this.queue,
        width: '24%'
      },
      this.isPerJob && {
        label: this.$t('pa.currentMemory'),
        value: this.memory,
        width: '19%'
      },
      this.isPerJob && {
        label: this.$t('pa.preRemainMemory'),
        value: this.finalMemory,
        width: '19%'
      },
      this.isPerJob && {
        label: this.$t('pa.currentCpu'),
        value: this.cpu,
        width: '19%'
      },
      this.isPerJob && {
        label: this.$t('pa.preRemainCpu'),
        value: this.finalCpu,
        width: '19%'
      }
    ].filter(Boolean);
  }

  getTotal(key: string) {
    return this.tableData.reduce((pre, next) => {
      pre += next[key] || 0;
      return pre;
    }, 0);
  }
  validate() {
    if (!this.isPerJob && this.finalSlosts < 0) return this.$t('pa.preLackCpu');
    if (this.isPerJob && this.finalMemory < 0) return this.$t('pa.preLackMemory');
    if (this.isPerJob && this.finalCpu < 0) return this.$t('pa.preLackSlots');
  }
}
</script>
<style lang="scss" scoped>
.header-item {
  &__container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    height: 84px;
    background-color: #fafbfc;
    border: 1px solid#F1F1F1;
  }
  &-title {
    padding-top: 10px;
    color: #777;
    font-style: normal;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
    box-sizing: border-box;
  }
  &-point {
    display: inline-block;
    margin-right: 6px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #376eff;
  }
  &-queue {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    &__label {
      margin-right: 20px;
      color: #777;
    }
  }
  &-total {
    padding-left: 10px;
    font-variant: tabular-nums;
    list-style: none;
    color: #444;
    font-size: 24px;
    line-height: 38px;
  }
}
</style>
