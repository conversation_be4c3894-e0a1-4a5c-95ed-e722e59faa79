<template>
  <bs-dialog
    size="medium"
    title="查看模式序列"
    :visible.sync="display"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :footer-visible="false"
  >
    <div v-loading="loading" element-loading-text="组件加载中" class="seqRelation__container">
      <!-- 逻辑关系 -->
      <el-tooltip
        v-hide="logicalRelationship"
        :content="logicalRelationship"
        effect="light"
        placement="top"
      >
        <div class="seqRelation__logic">逻辑关系：{{ logicalRelationship }}</div>
      </el-tooltip>
      <!-- 序列明细 -->
      <div class="seqRelation__detail">序列明细:</div>
      <!-- 画布 -->
      <relation :data="graphData" />
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { hide, generateAllRelationship, generateLoopRelation } from '../../../util';
import Relation from './relation/index.vue';

@Component({ directives: { hide }, components: { Relation } })
export default class SequenceRelation extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: () => [] }) groupData!: any[];
  @PropSync('show', { default: false }) display!: boolean;

  private loading = false;
  private graphData: any = [];

  get logicalRelationship() {
    return generateAllRelationship(this.data[0].groupCepPatternConditionList);
  }

  get groupMap() {
    return this.groupData.reduce((pre, { groupName, groupCepPatternConditionList }) => {
      pre[groupName] = this.splitArr(this.transformData(groupCepPatternConditionList));
      return pre;
    }, {});
  }

  created() {
    this.loading = true;
    const res = this.transformData(this.data[0].groupCepPatternConditionList, true);
    this.graphData = this.splitArr(res);
    this.loading = false;
  }

  transformData(data: any[], parseChildren = false) {
    return data.map((el, index) => {
      const temp: any = {
        name: el.modelOrGroupName,
        content: generateLoopRelation(el),
        children: parseChildren ? this.parseChildren(el.modelOrGroupName) : [],
        hiddenLine: false
      };
      if (index + 1 === data.length) temp.hiddenLine = true;
      return temp;
    });
  }
  parseChildren(name: string) {
    return Array.isArray(this.groupMap[name]) ? this.groupMap[name] : [];
  }
  splitArr(data: any[], n = 3) {
    const result: any[] = [];
    for (let i = 0; i < data.length; i += n) {
      result.push(data.slice(i, i + n));
    }
    return result;
  }
}
</script>
<style lang="scss" scoped>
.seqRelation {
  &__container {
    overflow: hidden;
    font-size: 14px;
    font-weight: 400;
    color: #444444;
    line-height: 20px;
  }
  &__logic {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  &__detail {
    margin: 20px 0;
  }
}
</style>
