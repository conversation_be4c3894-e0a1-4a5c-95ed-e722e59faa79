<template>
  <el-form
    ref="formRef"
    :model="formData"
    :disabled="disabled"
    label-position="left"
    class="form__container"
  >
    <!-- 头部 -->
    <item-header
      :index="index"
      :name="name"
      :all-name="allName"
      :data.sync="formData"
      :is-sequence="isSequence"
      @click="handlerClickEvent"
      @change="handleGroupNameChange"
    />
    <!-- 表格 -->
    <el-table :data="conditions" stripe class="form-table">
      <el-table-column
        v-for="el in tableHead"
        :key="el.prop"
        :prop="el.prop"
        :label="el.label"
        :width="el.width"
        :min-width="el.minWidth"
      >
        <!-- 表头 -->
        <template slot="header">
          <span v-if="el.required" class="form-table-required">*</span><span>{{ el.label }}</span>
        </template>
        <!-- 内容 -->
        <template v-slot="scope">
          <!-- 模式名称 -->
          <name-type
            v-if="el.type === 'name'"
            :data="el"
            :index="index"
            :form-data="formData"
            :options="nameOptions"
            :scope-index="scope.$index"
            :used-module-list.sync="moduleList"
            @change="handleModelNameChange"
          />
          <!-- 循环规则 -->
          <circular-rule-type
            v-else-if="el.type === 'circularRule'"
            :form-data="formData"
            :scope-index="scope.$index"
            @change="handleCircularRuleChange"
          />
          <!-- 循环次数 -->
          <cycles-type
            v-else-if="el.type === 'cycles'"
            :form-data="formData"
            :scope-index="scope.$index"
          />
          <!-- 连接方式  -->
          <connection-type
            v-else-if="el.type === 'connectionModeType'"
            :data="el"
            :index="index"
            :form-data="formData"
            :scope-index="scope.$index"
            @change="handleConnectionModeTypeChange"
          />
          <!-- 操作栏 -->
          <operate-type
            v-else-if="el.type === 'operate'"
            :config="el"
            :index="index"
            :length="length"
            :scope-index="scope.$index"
            @click="handlerClickEvent"
          />
          <!-- default -->
          <span v-else>{{ scope.row[el.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import { cloneDeep } from 'lodash';
import { hide } from '../../util';
import ItemHeader from './item-header.vue';
import NameType from './name-type.vue';
import CircularRuleType from './circular-rule-type.vue';
import CyclesType from './cycles-type.vue';
import ConnectionType from './connection-type.vue';
import OperateType from './operate-type.vue';
import elForm from 'bs-ui-pro/packages/form/index.js';
// import type { ModelConfigItem } from '../../type';

@Component({
  directives: { hide },
  components: {
    ItemHeader,
    NameType,
    CircularRuleType,
    CyclesType,
    ConnectionType,
    OperateType
  }
})
export default class ConfigItem extends Vue {
  @Prop({ default: () => [] }) allName!: any;
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ type: Number, default: 0 }) index!: number;
  @Prop({ type: String, default: '' }) name!: string;
  @Prop({ type: Array, default: () => [] }) nameOptions!: any;
  @Prop({ type: Boolean, default: false }) isSequence!: boolean;
  @PropSync('usedModuleList', { default: () => [] }) moduleList!: any;
  @PropSync('modeGrouplList', { default: () => [] }) grouplList!: any;
  @PropSync('data', { type: Object, default: () => ({}) }) formData!: any;
  @PropSync('rowData', { type: Array, default: () => [] }) rootData!: any;
  @Ref('formRef') readonly form!: elForm;

  get conditions() {
    return this.formData.groupCepPatternConditionList;
  }

  get length() {
    return this.conditions.length;
  }

  get tableHead() {
    return [
      {
        label: this.isSequence ? '模式/模式组名称' : '模式名称',
        required: true,
        type: 'name',
        width: '235px',
        prop: 'modelOrGroupName'
      },
      {
        label: '循环规则',
        required: true,
        prop: 'circularRuleTimes',
        minWidth: '330px',
        type: 'circularRule',
        optionsKey: 'cycles',
        optionsKey1: 'cycles1'
      },
      {
        label: '循环次数',
        required: true,
        prop: 'cycles',
        minWidth: '330px',
        type: 'cycles'
      },
      {
        label: '连接方式',
        required: true,
        width: '239px',
        prop: 'connectionModeType',
        type: 'connectionModeType',
        options: [
          {
            label: 'next()',
            value: 'NEXT'
          },
          {
            label: 'followedBy()',
            value: 'FOLLOWED_BY'
          },
          {
            label: 'followedByAny()',
            value: 'FOLLOWED_BY_ANY'
          },
          {
            label: 'notNext()',
            value: 'NOT_NEXT'
          },
          {
            label: 'notFollowedBy()',
            value: 'NOT_FOLLOWED_BY'
          },
          {
            label: '无',
            value: 'NULL'
          }
        ]
      },
      {
        label: '操作',
        prop: 'operate',
        type: 'operate',
        width: '226px',
        options: [
          {
            type: 'up',
            icon: 'icon-shangyi',
            tooltip: '上移'
          },
          {
            type: 'down',
            icon: 'icon-xiayi',
            tooltip: '下移'
          },
          {
            type: 'copy',
            icon: 'icon-banbenduibi',
            tooltip: '复制'
          },
          {
            type: 'delete',
            icon: 'icon-shanchu',
            tooltip: '删除'
          }
        ]
      }
    ];
  }

  created() {
    const unWatch = this.$watch('conditions', () => {
      if (this.conditions.length > 0) {
        this.conditions.forEach((el, index) => {
          this.handleConnectionModeTypeChange(index);
          this.handleCircularRuleChange(index);
        });
        unWatch(); // 取消监听
      }
    });
  }
  /* 统一处理点击事件 */
  async handlerClickEvent(type: string, index: number, $index: number) {
    if (type === 'view') return this.$emit('view');
    if (type === 'add') {
      this.rootData[index].groupCepPatternConditionList.push({
        modelOrGroupName: '',
        circularRuleTimes: '',
        excessRuleTimesList: [],
        leftInterval: 0,
        rightInterval: 0,
        connectionModeType: 'NULL'
      });
    }
    if (type === 'delete') {
      await this.$confirm('确认则删除该行数据', '提示');
      if ($index === undefined) {
        this.changeModuleList(cloneDeep(this.rootData[index]));
        this.rootData.splice(index, 1);
        this.grouplList.splice(index, 1);
        return;
      }
      const { modelOrGroupName } = this.rootData[index].groupCepPatternConditionList[$index];
      const arr = this.moduleList.filter((el) => el !== modelOrGroupName);
      this.$set(this, 'moduleList', arr);
      this.rootData[index].groupCepPatternConditionList.splice($index, 1);
    }
    if (type === 'up') {
      const list = cloneDeep(this.rootData[index].groupCepPatternConditionList);
      [list[$index - 1], list[$index]] = [list[$index], list[$index - 1]];
      this.$set(this.rootData[index], 'groupCepPatternConditionList', list);
    }
    if (type === 'down') {
      const list = cloneDeep(this.rootData[index].groupCepPatternConditionList);
      [list[$index], list[$index + 1]] = [list[$index + 1], list[$index]];
      this.$set(this.rootData[index], 'groupCepPatternConditionList', list);
    }
    if (type === 'copy') {
      const data = cloneDeep(this.rootData[index].groupCepPatternConditionList[$index]);
      data.modelOrGroupName = '';
      this.rootData[index].groupCepPatternConditionList.push(data);
    }
    this.$forceUpdate();
    this.checkConnectionRule(this.rootData[index].groupCepPatternConditionList);
  }
  /* 连接方式规则校验 */
  checkConnectionRule(data: any[]) {
    const { length } = data ? data : this.conditions;
    const handerLastTwo = (data, i) => {
      if (data[i] && ['NOT_FOLLOWED_BY', 'NULL'].includes(data[i].connectionModeType)) {
        data[i].connectionModeType = '';
      }
    };
    const handerLastOne = (data, i) => {
      if (data && data[i] && 'connectionModeType' in data[i]) {
        data[i].connectionModeType = 'NULL';
      }
    };
    const handerOthers = (data, i) => {
      if (data && data[i] && data[i].connectionModeType === 'NULL') {
        data[i].connectionModeType = '';
      }
    };
    for (let i = 0; i < length; i += 1) {
      this.handleConnectionModeType(i);
      if (i === length - 2) {
        handerLastTwo(data, i);
      } else if (i === length - 1) {
        handerLastOne(data, i);
      } else {
        handerOthers(data, i);
      }
    }
  }
  handleConnectionModeType($index: number) {
    this.specialHandler($index);
  }

  /* 处理模式组名称变化 */
  handleGroupNameChange(index: number, label: string) {
    const has = this.grouplList.some((el) => el.label === label);
    if (!has) {
      this.$set(this.grouplList, index, {
        label,
        value: label,
        type: 'group',
        disabled: false
      });
    }
  }
  /* 处理模式/模式组名称变化 */
  handleModelNameChange($index: number, oldData: string) {
    const arr = this.moduleList.filter((el) => el !== oldData);
    const { modelOrGroupName } = this.formData.groupCepPatternConditionList[$index];
    arr.push(modelOrGroupName);
    this.$set(this, 'moduleList', arr);
  }
  /* 处理循环规则变化 */
  handleCircularRuleChange($index: number) {
    const { circularRuleTimes } = this.conditions[$index];
    if (circularRuleTimes === 'TIMES') return;
    if (circularRuleTimes === 'TIMES_OR_MORE') {
      this.$set(this.formData.groupCepPatternConditionList[$index], 'rightInterval', '+∞');
      return;
    }
    this.$set(this.formData.groupCepPatternConditionList[$index], 'leftInterval', '');
    this.$set(this.formData.groupCepPatternConditionList[$index], 'rightInterval', '');
  }

  /* 处理连接方式变化 */
  handleConnectionModeTypeChange($index: number) {
    this.specialHandler($index);
    this.checkConnectionRule(this.conditions);
  }
  specialHandler($index: number) {
    if ($index === this.length - 1) return;
    if ($index === 0) {
      this.$set(this.formData.groupCepPatternConditionList[0], 'isNotConneModeType', false);
    }
    const result = this.conditions[$index].connectionModeType.includes('NOT');
    this.$set(this.formData.groupCepPatternConditionList[$index + 1], 'isNotConneModeType', result);

    if (result) {
      ['circularRuleTimes', 'excessRuleTimesList', 'leftInterval', 'rightInterval'].forEach(
        (el, index) => {
          this.$set(
            this.formData.groupCepPatternConditionList[$index + 1],
            el,
            index === 1 ? [] : ''
          );
        }
      );
    }
  }
  changeModuleList({ groupCepPatternConditionList = [] }: any) {
    const useList = groupCepPatternConditionList.map(({ modelOrGroupName }) => modelOrGroupName);
    const arr = this.moduleList.filter((el) => !useList.includes(el));
    this.$set(this, 'moduleList', arr);
  }
  /* 表单校验 */
  validate() {
    return this.form.validate();
  }
}
</script>

<style lang="scss" scoped>
.form {
  &__container {
    display: block;
    padding: 10px 20px;
    width: 100%;
    box-sizing: border-box;
  }

  &-table {
    /* 小红点 */
    &-required {
      display: inline-block;
      vertical-align: middle;
      color: red;
    }
  }
}
</style>
