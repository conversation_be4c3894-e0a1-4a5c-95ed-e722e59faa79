<template>
  <pro-page v-loading="loading" :title="`${$t('pa.menu.sqlClip')}${title}`" class="sql-detail">
    <!-- 头部操作按钮 -->
    <div slot="operation" class="sql-detail__header">
      <el-button
        v-for="item in headerButtonList"
        v-show="item.display"
        :key="item.label"
        size="small"
        :type="item.type"
        @click="operateHandler(item.event)"
      >
        {{ item.label }}
      </el-button>
    </div>
    <div class="sql-detail__content">
      <pro-grid direction="column" :gutter="18">
        <!-- 基本信息 -->
        <pro-grid :title="$t('pa.baseInformation')" type="info">
          <div class="baseInfo">
            <!-- 查看 -->
            <el-descriptions v-if="!isEdit" :column="1">
              <el-descriptions-item :label="$t('pa.resource.sql.detail.partName')">{{ baseInfo.name }}</el-descriptions-item>
              <el-descriptions-item :label="$t('pa.resource.sql.detail.partAcronym')">{{
                baseInfo.shortCode
              }}</el-descriptions-item>
              <el-descriptions-item :label="$t('pa.notes')">
                {{ baseInfo.memo }}
              </el-descriptions-item>
            </el-descriptions>
            <!-- 编辑 -->
            <el-form
              v-else
              ref="baseInfoForm"
              :model="baseInfo"
              :rules="baseInfoRules"
              :label-width="isEn ? '175px' : '95px'"
              :label-position="'right'"
              style="padding: 0 20px"
            >
              <el-form-item :label="$t('pa.resource.sql.detail.partName')" prop="name">
                <el-input
                  v-model="baseInfo.name"
                  :placeholder="$t('pa.resource.sql.detail.placeholder.name')"
                  show-word-limit
                  maxlength="255"
                  class="sql-detail-input"
                />
              </el-form-item>
              <el-form-item :label="$t('pa.resource.sql.detail.partAcronym')" prop="shortCode">
                <el-input
                  v-model="baseInfo.shortCode"
                  :placeholder="$t('pa.resource.sql.detail.placeholder.acronymPlaceholder')"
                  show-word-limit
                  maxlength="30"
                  class="sql-detail-input"
                />
                <el-tooltip effect="light" :content="$t('pa.resource.sql.detail.tooltip.tooltip1')" placement="bottom">
                  <i style="margin-left: 10px; cursor: pointer" class="iconfont icon-wenhao"></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item :label="$t('pa.notes')" prop="memo">
                <el-input
                  v-model="baseInfo.memo"
                  :placeholder="$t('pa.resource.sql.detail.placeholder.notesPlaceholder')"
                  show-word-limit
                  maxlength="255"
                  class="sql-detail-input"
                />
              </el-form-item>
            </el-form>
          </div>
        </pro-grid>
        <!-- SQL代码 -->
        <pro-grid type="info" :title="$t('pa.resource.sql.detail.sqlCode')">
          <div class="source-code">
            <el-form ref="codeForm" :model="code" :rules="codeRules">
              <el-form-item prop="sqlCode">
                <bs-code
                  :title="$t('pa.resource.sql.detail.sqlCode')"
                  :value="code.sqlCode"
                  :operatable="false"
                  language="sql"
                  :read-only="!isEdit"
                />
              </el-form-item>
            </el-form>
          </div>
        </pro-grid>
      </pro-grid>
    </div>
  </pro-page>
</template>
<script lang="ts">
import { Component, Vue, Ref } from 'vue-property-decorator';
import ElForm from 'bs-ui-pro/lib/form';
import { addSql, updateSql, findById } from '@/apis/sqlApi';
import { hasPermission } from '@/utils';
@Component
export default class SqlDetail extends Vue {
  @Ref('baseInfoForm') readonly baseInfoForm!: ElForm;
  @Ref('codeForm') readonly codeForm!: ElForm;
  id = '';
  title = '';
  isEdit = false;
  loading = false;
  baseInfo = { name: '', shortCode: '', memo: '', dataLevelType: '' };
  baseInfoRules = {
    name: [{ required: true, validator: this.validateName, trigger: 'blur' }],
    shortCode: [{ required: true, validator: this.validateShortCode, trigger: 'blur' }],
    memo: [{ required: false, validator: this.validateMemo, trigger: 'blur' }]
  };
  placeholder = {};
  code = { sqlCode: `` };
  codeRules = {
    sqlCode: [{ required: true, message: this.$t('pa.placeholder.sqlPlaceholder'), trigger: 'blur' }]
  };
  get headerButtonList() {
    return [
      {
        label: this.$t('pa.action.cancel'),
        display: this.isEdit,
        event: 'handelCancel',
        type: ''
      },
      {
        label: this.$t('pa.action.save'),
        display: this.isEdit,
        event: 'handelSave',
        type: 'primary'
      },
      {
        label: this.$t('pa.action.edit'),
        display:
          !this.isEdit &&
          ['SELF', 'CHILD'].includes(this.baseInfo.dataLevelType) &&
          hasPermission('PA.ASSETS.SQL_CODE.EDIT'),
        event: 'handelEdit',
        type: 'primary'
      }
    ];
  }

  created() {
    this.init();
  }
  init() {
    this.title = this.$route.query.title as string;
    this.$route.query.status !== 'detail' && (this.isEdit = true);
    if (this.$route.query.id) {
      this.id = this.$route.query.id as string;
      this.getSqlDetail();
    }
  }

  async getSqlDetail() {
    try {
      this.loading = true;
      const { data, success, msg, error } = await findById(this.id);
      this.loading = false;
      if (!success) return this.$tip.error({ message: error || msg, duration: 5000 });
      const { name, shortCode, memo, body, dataLevelType } = data;
      this.baseInfo = { name, shortCode, memo, dataLevelType };
      this.code.sqlCode = this.$store.getters.decrypt(body);
    } catch (err) {
      this.loading = false;
    }
  }

  // 头部操作按钮触发
  operateHandler(event) {
    this[event]();
  }

  handelEdit() {
    this.$router.replace({
      name: 'refresh',
      query: {
        id: this.id,
        title: '：' + this.$t('pa.action.edit'),
        status: 'edit'
      }
    });
  }
  handelSave() {
    let success = true;
    this.baseInfoForm.validate((valid) => {
      if (!valid) {
        success = false;
      }
    });
    this.codeForm.validate((valid) => {
      if (!valid) {
        success = false;
      }
    });
    if (success) {
      if (this.id) return this.updateSqlClip();
      this.addSqlClip();
    }
  }
  handelCancel() {
    const oldFullPath = this.$route.fullPath;
    (this as any).$tabNav.deleteTab(oldFullPath);
  }
  //新建保存
  async addSqlClip() {
    try {
      this.loading = true;
      const { name, shortCode, memo } = this.baseInfo;
      const { data, success, msg, error } = await addSql({
        name,
        shortCode,
        memo,
        body: this.$store.getters.encrypt(this.code.sqlCode),
        type: '1'
      });
      this.loading = false;
      if (!success) return this.$tip.error({ message: error || msg, duration: 5000 });
      this.$router.push({
        name: 'refresh',
        query: { id: data, title: this.$t('pa.action.detail'), status: 'detail' }
      });
    } catch (err) {
      this.loading = false;
    }
  }
  //编辑保存
  async updateSqlClip() {
    try {
      this.loading = true;
      const { name, shortCode, memo } = this.baseInfo;
      const { success, msg, error } = await updateSql({
        id: this.id,
        name,
        shortCode,
        memo,
        body: this.$store.getters.encrypt(this.code.sqlCode)
      });
      this.loading = false;
      if (!success) return this.$tip.error({ message: error || msg, duration: 5000 });
      this.$router.push({
        name: 'refresh',
        query: { id: this.id, title: this.$t('pa.action.detail'), status: 'detail' }
      });
    } catch (err) {
      this.loading = false;
    }
  }
  //校验
  validateName(rule, value, callback) {
    if (!value) return callback(new Error(this.$t('pa.placeholder.name')));
    if (value.length > 255) return callback(new Error(this.$t('pa.tip.maximumInput', ['255'])));
    if (new RegExp(/\s+/g).test(value)) return callback(new Error(this.$t('pa.tip.nonSpaceChar')));
    callback();
  }
  validateShortCode(rule, value, callback) {
    if (!value) return callback(new Error(this.$t('pa.resource.sql.detail.placeholder.acronymPlaceholder')));
    if (new RegExp(/\s+/g).test(value)) return callback(new Error(this.$t('pa.tip.nonSpaceChar')));
    if (!new RegExp(/^[A-z0-9.]+$/g).test(value))
      return callback(new Error(this.$t('pa.resource.sql.detail.tips.inputAcronymTips')));
    if (value.length > 30) return callback(new Error(this.$t('pa.tip.maximumInput', ['30'])));
    callback();
  }
  validateMemo(rule, value, callback) {
    if (value.length > 500) return callback(new Error(this.$t('pa.tip.maximumInput', ['500'])));
    callback();
  }
}
</script>
<style scoped lang="scss">
.sql-detail {
  &__header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  &__content {
    margin-top: 20px;
    .baseInfo {
      padding: 20px 25px;
      .infoDetail {
        display: flex;
        margin-bottom: 14px;
        div {
          width: 240px;
          margin-right: 10px;
        }
      }
      &__memo {
        width: 708px;
      }
    }
    .source-code {
      padding: 1px 20px;
    }
  }
  &-input {
    width: calc(100% - 30px);
  }
  ::v-deep .monaco-editor {
    .mtk4 {
      color: #008000;
    }
    .mtk8 {
      color: #aaaaaa;
    }
  }
  ::v-deep .el-descriptions-item__label {
    font-size: 14px;
    color: #444;
  }
}
</style>
