<template>
  <bs-dialog
    :title="title"
    :visible.sync="display"
    :size="isEn ? 'large' : 'medium'"
    :before-close="closeDialog"
    :confirm-button="{ disabled }"
    append-to-body
    @close="closeDialog"
    @confirm="handleConfirm"
  >
    <div v-loading="loading">
      <pro-form
        v-if="isInit"
        ref="proForm"
        :value="formData"
        :options="{ labelWidth: isEn ? 160 : 100 }"
        :form-items="formItems"
        :disabled="disabled"
        @item-change="handleItemChange"
        @change="handleChange"
      >
        <!-- 表字段列表 -->
        <bs-page-select
          slot="selectField"
          v-model="formData.selectField"
          :options="tableFields"
          clearable
          confirm-when-deleting
          :label-map="selectFieldMap"
          @change="handleSelectField"
        >
          <div slot-scope="{ item }" class="database__pageSelect">
            <span>
              {{ item.label }}
              <el-tag v-if="item.isPrimaryKey" size="mini">{{ $t('pa.flow.mainKey') }}</el-tag>
            </span>
            <span>{{ item.type }}</span>
          </div>
        </bs-page-select>
        <!-- 周期模式 -->
        <el-cascader slot="cronCycle" v-model="formData.cronCycle" :options="cronCycleOptions" />
        <!-- 起始时间 -->
        <el-input
          slot="startTime"
          v-model="formData.startTime"
          :placeholder="$t('pa.flow.msg141')"
          @blur="handleStartTimeBlur"
        />
      </pro-form>
    </div>
    <print-log slot="footer-left" v-model="printLog" :disabled="disabled" />
  </bs-dialog>
</template>
<script lang="ts">
import { getJdbcRes, getJdbcTables } from '@/apis/flowNewApi';
import { Component, Vue, Prop, PropSync, Ref } from 'vue-property-decorator';
import {
  CRON_CYCLE_OPTIONS,
  GenerateOption,
  getDefaultData,
  GetQueryStrategyOptions,
  IsValidDate,
  RESET_TYPE_OPTIONS
} from './utils';
import type { FormData } from './type';
import { getSqlColumn } from '@/apis/serviceApi';
import cloneDeep from 'lodash/cloneDeep';
import PrintLog from '../components/print-log.vue';
import i18n from '@/i18n';
const ALL = 'all';
@Component({
  components: {
    PrintLog
  }
})
export default class DatabaseCollection extends Vue {
  @Ref('proForm') proForm: any;
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @PropSync('show', { type: Boolean, default: false }) display!: boolean;
  loading = true;
  isInit = false;
  formData: FormData = getDefaultData();
  // 周期模式数据
  cronCycleOptions = CRON_CYCLE_OPTIONS;
  formItems = [
    {
      type: 'select',
      label: this.$t('pa.flow.jdbcType'),
      prop: 'jdbcType',
      componentProps: {
        options: [],
        placeholder: this.$t('pa.flow.msg127')
      },
      rules: [{ required: true, message: this.$t('pa.flow.msg127'), trigger: 'change' }]
    },
    {
      type: 'select',
      label: this.$t('pa.flow.serveName'),
      prop: 'resId',
      deps: ['jdbcType'],
      componentProps: {
        searchable: true,
        options: [],
        clearable: true,
        placeholder: this.$t('pa.flow.msg128')
      },
      request: this.getResList,
      rules: [{ required: true, message: this.$t('pa.flow.msg128'), trigger: 'change' }]
    },
    {
      type: 'textarea',
      label: this.$t('pa.flow.serveAddress'),
      prop: 'jdbcUrl',
      componentProps: {
        disabled: true,
        placeholder: this.$t('pa.flow.msg115')
      },
      rules: [{ required: true, message: this.$t('pa.flow.msg115'), trigger: 'blur' }]
    },
    {
      type: 'select',
      label: this.$t('pa.flow.selectTable'),
      prop: 'tableName',
      deps: ['resId', 'resTitle'],
      tooltip: this.$t('pa.flow.msg129'),
      componentProps: {
        clearable: true,
        placeholder: this.$t('pa.flow.selectTable'),
        searchable: true
      },
      request: this.getTableList,
      rules: [{ required: true, message: this.$t('pa.flow.selectTable'), trigger: 'blur' }]
    },
    {
      type: 'custom',
      label: this.$t('pa.flow.outputFields'),
      prop: 'selectField',
      tooltip: this.$t('pa.flow.msg130'),
      componentProps: {
        clearable: true,
        placeholder: this.$t('pa.flow.msg131')
      },
      rules: [{ required: true, message: this.$t('pa.flow.msg131'), trigger: 'change' }]
    },
    {
      type: 'textarea',
      label: this.$t('pa.flow.where'),
      prop: 'userDefinedWhere',
      componentProps: {
        placeholder: this.$t('pa.flow.msg132'),
        minRows: 2
      },
      rules: [
        {
          validator(rule, value, callback) {
            if (!value) return callback();
            if (value.includes('?')) return callback(new Error(i18n.t('pa.flow.msg133') as string));
            callback();
          },
          trigger: 'blur'
        }
      ]
    },
    {
      type: 'select',
      label: this.$t('pa.flow.queryStrategy'),
      prop: 'queryStrategy',
      deps: ['selectField'],
      componentProps: {
        options: [],
        placeholder: this.$t('pa.flow.msg134')
      },
      rules: [{ required: true, message: this.$t('pa.flow.msg134'), trigger: 'change' }]
    },
    {
      type: 'select',
      label: this.$t('pa.flow.timeField'),
      prop: 'timeField',
      deps: ['queryStrategy'],
      visible: (scope) => scope.queryStrategy === 'time',
      rules: [{ required: true, message: this.$t('pa.flow.msg135'), trigger: 'change' }],
      componentProps: {
        options: [],
        placeholder: this.$t('pa.flow.msg135')
      },
      request: this.getTimeField
    },
    {
      type: 'input-number',
      label: this.$t('pa.flow.queryInterval'),
      prop: 'queryInterval',
      deps: ['queryStrategy'],
      visible: (scope) => scope.queryStrategy === 'time',
      tooltip: this.$t('pa.flow.msg137'),
      rules: [{ required: true, message: this.$t('pa.flow.msg138'), trigger: 'blur' }],
      componentProps: {
        min: 1,
        max: 2147483647,
        placeholder: this.$t('pa.flow.msg140')
      }
    },
    {
      type: 'input',
      label: this.$t('pa.flow.startTime'),
      prop: 'startTime',
      deps: ['queryStrategy'],
      visible: (scope) => scope.queryStrategy === 'time',
      tooltip: this.$t('pa.flow.msg139'),
      rules: [
        {
          validator(rule, value, callback) {
            if (!value) return callback();
            if (!IsValidDate(value)) return callback(new Error(i18n.t('pa.flow.msg142') as string));
            callback();
          },
          trigger: 'change'
        }
      ]
    },
    {
      type: 'select',
      label: this.$t('pa.flow.indexField'),
      prop: 'indexField',
      deps: ['queryStrategy'],
      visible: (scope) => scope.queryStrategy === 'index',
      componentProps: {
        placeholder: this.$t('pa.flow.msg143')
      },
      rules: [
        {
          required: true,
          message: this.$t('pa.flow.msg143'),
          trigger: 'change'
        }
      ],
      request: this.getIndexField
    },
    {
      type: 'input-number',
      label: this.$t('pa.flow.pageSize'),
      prop: 'pageSize',
      deps: ['queryStrategy'],
      visible: (scope) => ['primaryKey', 'index', 'none'].includes(scope.queryStrategy),
      tooltip: this.$t('pa.flow.msg144'),
      componentProps: {
        min: 1,
        max: 2147483647,
        placeholder: this.$t('pa.flow.msg144')
      },
      rules: [
        {
          required: true,
          message: this.$t('pa.flow.msg138'),
          trigger: 'blur'
        }
      ]
    },
    {
      type: 'input-number',
      label: this.$t('pa.flow.waitTime'),
      prop: 'waitTime',
      tooltip: this.$t('pa.flow.msg145'),
      componentProps: {
        min: 1,
        max: 2147483647,
        placeholder: this.$t('pa.flow.msg146')
      }
    },
    {
      type: 'select',
      label: this.$t('pa.flow.msg147'),
      prop: 'resetType',
      componentProps: {
        clearable: true,
        options: RESET_TYPE_OPTIONS
      }
    },
    {
      type: 'custom',
      label: this.$t('pa.flow.label70'),
      prop: 'cronCycle',
      deps: ['resetType'],
      visible: (scope) => scope.resetType === '0'
    },
    {
      label: this.$t('pa.flow.key16'),
      gutter: 8,
      deps: ['resetType'],
      visible: (scope) => scope.resetType === '1',
      style: {
        marginBottom: 0
      },
      children: [
        [this.$t('pa.flow.s'), 'sec_1', 59],
        [this.$t('pa.flow.m1'), 'min_1', 59],
        [this.$t('pa.flow.h1'), 'hour_1', 23],
        [this.$t('pa.flow.d'), 'day_1', 31]
      ].map(([label, prop, len]) => ({
        type: 'select',
        prop,
        label,
        labelWidth: 50,
        labelPosition: 'top',
        componentProps: {
          options: [{ label: this.$t('pa.flow.mei') + label, value: '-1' }, ...GenerateOption(len)]
        },
        rules: [{ required: true, message: this.$t('pa.flow.placeholder21'), trigger: 'change' }]
      }))
    },
    {
      label: this.$t('pa.flow.hignMode'),
      gutter: 8,
      deps: ['resetType'],
      visible: (scope) => scope.resetType === '2',
      style: {
        marginBottom: 0
      },
      children: [
        [this.$t('pa.flow.s'), 'sec_2'],
        [this.$t('pa.flow.m1'), 'min_2'],
        [this.$t('pa.flow.h1'), 'hour_2'],
        [this.$t('pa.flow.d'), 'day_2'],
        [this.$t('pa.flow.mm'), 'month_2'],
        [this.$t('pa.flow.week'), 'week_2']
      ].map(([label, prop]) => ({
        type: 'input',
        prop,
        label,
        labelWidth: 50,
        labelPosition: 'top',
        rules: [{ required: true, message: this.$t('pa.flow.placeholder0'), trigger: 'blur' }]
      }))
    }
  ];
  // 是否打开日志
  printLog = false;
  tableFields = [];
  tablePrimaryFields: string[] = [];
  // 服务类型及信息原始数据
  originalData: Record<string, any> = {};
  get title() {
    const { nodeName = '', componentName = '' } = this.data || {};
    return this.$t('pa.flow.msg288', [nodeName, componentName]);
  }
  // 输出字段的lable map
  get selectFieldMap() {
    return (this.formData.selectField || []).reduce((res, item) => {
      res.set(item, item);
      return res;
    }, new Map());
  }
  async created() {
    this.printLog = Boolean(this.data.printLog);
    this.formData = this.initFormData();
    await this.getResType();
    await this.getTableFields();
    this.loading = false;
  }
  // 初始化数据
  initFormData() {
    const data = Object.assign(this.formData, this.data.properties || {});
    // 默认填充全部
    if (!data.jdbcType) {
      data.jdbcType = ALL;
    }
    return data;
  }
  // 获取服务类型
  async getResType() {
    const { data = {} } = await getJdbcRes();
    const options = [
      {
        value: ALL,
        label: this.$t('pa.flow.all')
      }
    ].concat(
      Object.keys(data)
        .filter((key) => key !== ALL)
        .map((key) => ({ value: key, label: key }))
    );
    this.originalData = data;
    // 渲染proForm组件
    this.isInit = true;
    setTimeout(() => {
      this.proForm.updateComponentProps('jdbcType', { options });
    });
  }
  // 获取服务列表
  getResList({ scope, query }) {
    if (!scope.jdbcType) return { data: [] };
    const options = (this.originalData[scope.jdbcType] || [])
      .map((item) => ({
        ...item,
        value: item.id,
        label: item.title
      }))
      .filter((i) => i.label.includes(query));
    return { data: options };
  }
  // 获取表列表
  async getTableList({ scope, query }) {
    const { resId, resTitle } = scope;
    if (!resId) return { data: [] };
    this.loading = true;
    const { data } = await getJdbcTables({
      resTitle,
      resId,
      orgId: this.orgId,
      subName: encodeURIComponent(query)
    });
    // 搜索表名为空 直接创建该下拉数据项
    const needCreate = data.length === 0 && query;
    if (!needCreate && this.formData.tableName && data.every(({ subName }) => subName !== this.formData.tableName)) {
      data.push({ subName: this.formData.tableName });
    }
    this.loading = false;
    return {
      data: needCreate ? [{ value: query, label: query }] : data.map(({ subName }) => ({ value: subName, label: subName }))
    };
  }
  // 获取表字段
  async getTableFields() {
    const { resId, tableName } = this.formData;
    if (!resId || !tableName) return (this.tableFields = []);
    this.loading = true;
    const {
      success,
      data = { tableData: [] },
      error
    } = await getSqlColumn(resId, tableName, { search: '', pageData: null });
    this.loading = false;
    if (!success) return this.$tip.error(error);
    this.tablePrimaryFields = [];
    let hasPrimaryKey = false;
    let hasTime = false;
    this.tableFields = data.tableData.map((el) => {
      if (el.primaryKey) {
        this.tablePrimaryFields.push(el.columnName);
        hasPrimaryKey = true;
      }
      el.dateField && (hasTime = true);
      return {
        label: el.columnName,
        value: el.columnName,
        type: el.typeNameJava,
        isPrimaryKey: el.primaryKey,
        isTime: el.dateField
      };
    });
    // 设置查询方式的数据源
    this.proForm.updateComponentProps('queryStrategy', { options: GetQueryStrategyOptions(hasPrimaryKey, hasTime) });
    this.formData.queryStrategy === 'time' &&
      this.proForm.updateComponentProps('timeField', { options: this.tableFields.filter((el: any) => el.isTime) });
  }
  // 获取时间字段
  getTimeField() {
    return { data: this.tableFields.filter((el: any) => el.isTime) };
  }
  // 获取索引字段数据
  getIndexField() {
    return { data: this.tableFields };
  }
  // 处理单个字段变更
  handleItemChange(prop, value) {
    if (prop === 'jdbcType') {
      ['resId', 'jdbcUrl', 'resTitle', 'tableName'].forEach((key) => this.proForm.updateModel(key, ''));
      this.proForm.updateModel('selectField', []);
    }
    if (prop === 'resId') {
      const target = this.originalData[this.formData.jdbcType].find((i) => i.id === value);
      this.proForm.updateModel('jdbcUrl', target.address);
      this.proForm.updateModel('resTitle', target.title);
      this.proForm.updateModel('tableName', '');
      this.proForm.updateModel('selectField', []);
    }
    if (prop === 'tableName') {
      this.getTableFields();
      this.proForm.updateModel('selectField', []);
    }
  }
  handleChange(data) {
    this.formData = data;
  }
  // 处理输出字段变更
  handleSelectField(val) {
    const includePrimary = (val || []).some((v) => this.tablePrimaryFields.includes(v));
    this.proForm.updateModel('selectField', val);
    // 若所选字段包含主键 查询方式默认选择基于主键查询 反之选择基于分页查询
    this.proForm.updateModel('queryStrategy', includePrimary ? 'primaryKey' : 'none');
  }
  // 处理起始时间离焦
  handleStartTimeBlur() {
    const value = this.formData.startTime;
    if (value && IsValidDate(value)) {
      this.proForm.updateModel('startTime', /^\d+$/.test(value as string) ? value : new Date(value).getTime());
    }
  }
  closeDialog() {
    this.$emit('close', {});
  }
  handleConfirm() {
    this.proForm.validate((vaild) => {
      if (vaild) {
        const getOutputFields = () => {
          const { selectField = [] } = this.formData;
          return this.tableFields
            .map((i: any) => {
              return {
                name: i.value,
                type: i.type,
                outputable: true,
                targetable: true
              };
            })
            .filter((i: any) => selectField.includes(i.name));
        };
        const jobNode = cloneDeep(this.data);
        jobNode.outputFields = getOutputFields();
        // 转换输出字段的顺序和输出字段相同
        this.formData.selectField = jobNode.outputFields.map((i) => i.name);
        jobNode.properties = { ...cloneDeep(this.formData) };
        jobNode.properties.address = jobNode.properties.jdbcUrl || '';
        jobNode.printLog = this.printLog;
        console.log(jobNode);
        this.$emit('close', { needUpdate: true, jobNode });
      }
    });
  }
}
</script>
<style lang="scss" scoped>
.database__pageSelect {
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 10px;
  width: 100%;
  box-sizing: border-box;
}
</style>
