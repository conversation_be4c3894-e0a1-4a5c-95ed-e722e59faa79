<template>
  <div class="import-sql">
    <bs-dialog
      :title="$t('pa.action.add')"
      :visible="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="closeDialog"
    >
      <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="60px">
        <el-form-item :label="$t('pa.resource.sql.detail.file')" prop="file">
          <el-upload
            ref="upload"
            action
            :http-request="handleFile"
            :multiple="false"
            :limit="1"
            :on-exceed="exceed"
            :before-remove="removeFile"
            :before-upload="beforeUpload"
            accept=".xlsx,.xls"
          >
            <el-button size="small" type="primary">{{ $t('pa.data.udf.detail.selectFile') }}</el-button>
            <span slot="tip" style="padding-left: 16px"> {{ $t('pa.params.template.detail.onlyExcel') }} </span>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog(true)">{{ $t('pa.action.close') }}</el-button>
        <el-button :loading="loading" type="primary" @click="submit('ruleForm')">{{ $t('pa.action.makeSure') }}</el-button>
      </span>
    </bs-dialog>
    <repeat-list
      :visible="repListVisible"
      :no-repeat-data="noRepeatData"
      :repeat-data="repeatData"
      @close="closeReplitDialog"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { importSqlList, validateImportSql } from '@/apis/sqlApi';

@Component({
  components: {
    'repeat-list': () => import('./repeat-list.vue')
  }
})
export default class ImportModal extends Vue {
  @Prop({ default: false }) visible!: boolean;
  repListVisible = false; //是否显示重复数据弹窗
  formData: any = {
    file: null
  };
  rules: any = { file: [{ required: true, message: this.$t('pa.data.text9'), trigger: 'blur' }] };
  loading = false;
  //导入校验后重复数据
  repeatData = [];
  //导入校验后不重复数据
  noRepeatData = [];

  handleFile(data: any) {
    this.formData.file = data.file;
  }

  removeFile() {
    this.formData.file = null;
  }

  //上传文件数量限制
  exceed() {
    this.$message.warning(this.$t('pa.params.template.detail.onlyOneExcel'));
  }
  //文件类型判断
  beforeUpload(file) {
    const name = file.name;
    const isXlsx = name.indexOf('.xlsx') >= 0 || name.indexOf('.xls') >= 0;
    if (!isXlsx) {
      this.$message.error(this.$t('pa.data.text10'));
    }
    return isXlsx;
  }

  closeDialog(needFresh = false) {
    this.$emit('close', needFresh);
  }
  submit(formName: string) {
    this.loading = true;
    (this.$refs[formName] as any).validate(async (valid: boolean) => {
      if (valid) {
        const { success, data = [], error } = await validateImportSql({ ...this.formData });
        if (success) {
          const { nonRepetitiveBean = [], repetitiveBean = [] } = data;
          if (repetitiveBean.length === 0) {
            this.importSqlClip(nonRepetitiveBean);
          } else {
            this.repListVisible = true;
            this.repeatData = repetitiveBean;
            this.noRepeatData = nonRepetitiveBean;
          }
          this.formData.file = null;
        } else {
          this.$message.error(error);
        }
        this.loading = false;
      }
    });
  }
  //导入sql代码片段库
  async importSqlClip(val) {
    const { success, msg } = await importSqlList(val);
    if (success) {
      this.closeDialog();
    } else {
      this.$message.error(msg);
    }
  }
  //关闭弹窗
  closeReplitDialog(val) {
    this.repListVisible = false;
    if (!val) this.closeDialog();
  }
}
</script>

<style lang="scss" scoped></style>
