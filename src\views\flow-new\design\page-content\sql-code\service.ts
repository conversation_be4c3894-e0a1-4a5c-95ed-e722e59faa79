import { SQL_KEYWORDS, Flink_SQL_KEYWORDS } from './config';
const KEYWORDS = [...new Set([...SQL_KEYWORDS, ...Flink_SQL_KEYWORDS].map((s) => s.toUpperCase()))];
// 自定义代码触发词
const getTriggerCharacters = () => {
  const upperCase = Array.from({ length: 26 }, (v, i) => String.fromCharCode(65 + i));
  const lowerCase = Array.from({ length: 26 }, (v, i) => String.fromCharCode(65 + i).toLowerCase());
  const numbers = Array.from({ length: 10 }, (v, i) => i + '');
  return ['.', ':', ...lowerCase, ...upperCase, ...numbers];
};
// 自定义提示代码
const generateSuggestions = (customKeywords, monaco) => {
  return KEYWORDS.map((s) => ({
    label: s,
    insertText: s,
    kind: monaco.languages.CompletionItemKind.Keyword
  })).concat(
    customKeywords.map((s) => ({
      label: s,
      insertText: s,
      kind: monaco.languages.CompletionItemKind.Text
    }))
  );
};
// 从代码内容中获取本地变量
const resolveCustomKeywords = (val, codeKeywords) => {
  if (typeof val !== 'string') return [];
  // 去除和默认关键字和代码片段关键字重复的字符串
  const keys = [...KEYWORDS, ...codeKeywords];
  return [...new Set(val.split(/[`\s,()]/))].filter((v) => /\w/.test(v) && !keys.includes(v));
};
export { getTriggerCharacters, generateSuggestions, KEYWORDS, resolveCustomKeywords };
