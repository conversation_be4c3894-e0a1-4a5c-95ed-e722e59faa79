<template>
  <div class="bar__container">
    <!-- 逻辑关系  -->
    <el-form ref="formRef" :inline="true" :model="formData" :disabled="disabled" class="bar-form">
      <!-- 逻辑类型 -->
      <el-form-item prop="logicalRelationship" label="逻辑关系" :rules="logicalRelationshipRule">
        <el-select
          v-model="formData.logicalRelationship"
          filterable
          class="bar-form__select"
          placeholder="请选择"
          @change="handleChange($event, 'select')"
        >
          <el-option v-for="el in typeList" :key="el.value" :label="el.label" :value="el.value" />
        </el-select>
      </el-form-item>
      <!-- 逻辑表达式 -->
      <el-tooltip
        effect="light"
        placement="top"
        :disabled="expressionDisabled"
        :content="formData.logicRelation"
      >
        <el-form-item prop="logicRelation" :rules="logicalRelationRule">
          <el-input
            v-model="formData.logicRelation"
            class="bar-form__input"
            :disabled="disabled || inputDisabled"
            @input="handleChange($event, 'input')"
          />
        </el-form-item>
      </el-tooltip>
    </el-form>
    <!-- 右侧 -->
    <el-button class="bar-btn" type="primary" @click="addNewCondition">增加条件</el-button>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import { debounce } from 'lodash';
import elForm from 'bs-ui-pro/packages/form/index.js';

@Component
export default class Bar extends Vue {
  @Prop({ default: false }) disabled!: boolean;
  @PropSync('data', { default: () => ({}) }) formData!: any;
  @Ref('formRef') readonly form!: elForm;

  private logicalRelationshipRule = {
    required: true,
    message: '请选择',
    trigger: 'change'
  };
  private addNewCondition = debounce(() => this.$emit('increase'), 1200);
  private typeList: any[] = [
    {
      value: '&&',
      label: '全‘&&’关系'
    },
    {
      value: '||',
      label: '全‘||’关系'
    },
    {
      value: 'CUSTOM',
      label: '自定义关系'
    }
  ];

  get logicalRelationRule() {
    return {
      required: true,
      type: this.formData.logicalRelationship,
      conditions: this.formData.singletonCondition,
      validator(rule, value, callback) {
        if (!value) return callback(new Error('请输入'));
        const letter = rule.conditions.map(({ name }: any) => name).join('|');
        const rules = new RegExp(`(\\&\\&|\\|\\||\\(|\\)|\\s|${letter})+`, 'g');
        const str = value.replace(rules, '');
        if (str.length > 0) return callback(new Error('请输入合法的逻辑关系！'));
        if (rule.type === 'CUSTOM') {
          const result = rule.conditions
            .map(({ name }: any) => (value.includes(name) ? null : `条件${name}`))
            .filter(Boolean);
          if (result.length > 0) {
            return callback(new Error(`逻辑关系不完整：${result.join('、')}没有被写入表达式！`));
            return;
          }
        }
        callback();
      },
      trigger: 'blur'
    };
  }

  get inputDisabled() {
    return this.formData.logicalRelationship !== 'CUSTOM';
  }

  get expressionDisabled() {
    if (this.inputDisabled) {
      return (
        this.formData.logicRelation.length < (this.formData.logicalRelationship === '&&' ? 50 : 70)
      );
      return false;
    }
    return !this.disabled;
  }
  async handleChange(type, name) {
    await this.$emit('change', ...[type, name]);
    if (name !== 'input') return;
    // this.checkExpression();
    //  const letter = this.conditions.map(({ name }: any) => name).join('|');
    // const rule = new RegExp(`(\\&\\&|\\|\\||\\(|\\)|\\s|${letter})+`, 'g');
    // const str = this.expression.replace(rule, '');
    // this.errorMsg = str.length > 0 ? '请输入合法的逻辑关系！' : '';
  }
  validate() {
    return this.form.validate();
  }
}
</script>
<style lang="scss" scoped>
.bar {
  &__container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 35px;
  }
  &-form {
    ::v-deep .el-form-item {
      margin-bottom: 20px;
    }
    &__select {
      width: 120px;
    }
    &__input {
      width: 276px;
    }
  }
  &-btn {
    margin-bottom: 20px;
  }
  &__left {
    display: flex;
    align-items: center;
    width: calc(100% - 200px);
    background: pink;

    &__title {
      margin-right: 12px;
      font-size: 14px;
      font-weight: 400;
      color: #444444;
      line-height: 20px;
    }
  }

  &-result {
    width: calc(100% - 100px);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &-logic {
    &__form {
      display: flex;
      align-items: center;
      width: calc(100% - 76px);

      &__item {
        margin-bottom: 0px !important;
      }

      &__type {
        margin-right: 10px;
        width: 120px;
      }

      &__expression {
        width: 360px;
      }
    }
  }
}
</style>
