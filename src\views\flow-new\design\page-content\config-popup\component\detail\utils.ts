export const MAP = 'cn.com.bsfit.pipeace.component.process.mapping.MappingComponent';
export const FILTER = 'cn.com.bsfit.pipeace.component.filter.EmbeddedFilterComponent';
export const JOIN = 'cn.com.bsfit.pipeace.component.process.join.PaJoinComponent';
export const INTERVAL_JOIN = 'cn.com.bsfit.pipeace.component.process.join.PaIntervalJoinComponent';
export const JDBC_OUTPUT = 'cn.com.bsfit.pipeace.component.flink.jdbc.JdbcSinkComponent';
export const JDBC_COLLECTION = 'cn.com.bsfit.pipeace.component.flink.jdbc.JdbcSourceComponent';
export const DD_MAPPING = 'cn.com.bsfit.pipeace.component.process.dd.mapping.PaDataDefMappingProcessComponent';

export const MAPPING = {
  MAP: 'mapDialogVisible',
  FILTER: 'filterDialogVisible',
  JOIN: 'joinDialogVisible',
  INTERVAL_JOIN: 'intervalJoinDialogVisible',
  'JSON-EXTRACT': 'jsonExtractDialogVisible',
  ROUTETEMPLATE: 'eventRouterDialogVisible',
  'Dynamic-MAP': 'dynamicMapDialogVisible',
  DYNAMIC_FILTER: 'dynamicFilterDialogVisible',
  SOURCE_SQL: 'showSqlInput',
  PROCESS_SQL: 'showSqlProcessing',
  CUBE_BATCH_PROCESS: 'showBatchDialog',
  SINK_SQL: 'showSqlOutput',
  DATA_QUALITY_MONITOR: 'dataQualityMonitorDialogVisible',
  DELAY_PROCESSING: 'processDelayDialogVisible',
  JDBC_OUTPUT: 'showDatabaseOutputDialog',
  JDBC_COLLECTION: 'showDatabaseCollectionDialog',
  DD_MAPPING: 'showDdMappingDialogVisible'
};
