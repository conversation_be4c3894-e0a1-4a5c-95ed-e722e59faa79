import { cloneDeep } from 'lodash';
import i18n from '@/i18n';
import { cookie } from '@/utils';

const IS_US = cookie.get('Language') === 'en-US';

let xAxisLabelLength = 15;

const engineResourceOption: any = {
  tooltip: {
    trigger: 'item',
    backgroundColor: '#ffffff',
    extraCssText: `width:160px;height:100px;padding:12px;box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16);
    border-radius: 4px;
    opacity: 0.98;`,
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: '#DFDFDF',
        width: '30',
        opacity: 0.5
      }
    },
    textStyle: {
      color: '#444444'
    },
    formatter: (params) => {
      const unitMap = {
        Slots: i18n.t('pa.home.individual'),
        CPU: 'C',
        [i18n.t('pa.home.memory') as string]: 'G'
      };
      return `<span style="display:inline-block;color: #777777;font-weight: 400;padding:5px 0">${params.name}</span>
      <br/>
      <span>${params.seriesName}</span><span style="display:inline-block;margin-left:12%">${params.data.number}</span>${
        unitMap[params.name]
      }<br/>
      <span>占比</span><span style="display:inline-block;margin-left:22%">${params.value}</span>%<br/>
    `;
    }
  },
  legend: {
    left: 'right',
    itemWidth: 8,
    itemHeight: 8,
    top: 14
  },
  grid: {
    width: IS_US ? '70%' : '82%',
    height: '70%',
    right: IS_US ? '25%' : '14%',
    bottom: '5%',
    containLabel: true
  },
  xAxis: {
    name: i18n.t('pa.home.resource'),
    type: 'category',
    splitLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    data: [
      {
        value: 'Slots',
        textStyle: {
          color: '#777777'
        }
      },
      {
        value: 'CPU',
        textStyle: {
          color: '#777777'
        }
      },
      {
        value: i18n.t('pa.home.memory'),
        textStyle: {
          color: '#777777'
        }
      }
    ],
    axisLine: {
      show: true,
      lineStyle: {
        color: '#F1F1F1'
      }
    },
    nameTextStyle: {
      color: '#AAAAAA'
    }
  },
  yAxis: {
    name: i18n.t('pa.home.text15'),
    type: 'value',
    formatter: '{c}',
    axisLabel: {
      show: true,
      interval: 'auto',
      formatter: function (value) {
        return value <= 1 ? value * 100 : value;
      },
      color: '#777777'
    },
    boundaryGap: false,
    axisLine: {
      show: true,
      lineStyle: {
        color: '#F1F1F1'
      }
    },
    nameTextStyle: {
      color: '#AAAAAA'
    },
    axisTick: {
      show: false
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: '#F1F1F1'
      }
    }
  },
  series: [
    {
      name: i18n.t('pa.home.used'),
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      barWidth: 20,
      itemStyle: { normal: { color: '#F9D452' } }
    },
    {
      name: i18n.t('pa.home.unused'),
      type: 'bar',
      stack: 'Ad',
      barWidth: 20,
      emphasis: {
        focus: 'series'
      },
      itemStyle: { normal: { color: '#377CFF' } }
    }
  ]
};

const flowDistributionOption: any = {
  grid: {},
  tooltip: {
    trigger: 'item',
    extraCssText: `background:#fff;color:#000;box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16);
    border-radius: 4px;
    opacity: 0.98`,
    formatter(params) {
      return params.data.value === 0
        ? ''
        : `<span style="display:inline-block;widht:5px;height:5px;background:${params.color};border-radius:50%"></span><span>${params.data.name}:${params.data.value}</span>`;
    }
  },
  legend: {
    orient: 'vertical',
    right: '1%',
    top: 'center',
    itemGap: 40,
    itemWidth: 8,
    itemHeight: 8,
    borderRadius: 4,
    formatter(name) {
      console.log(name);
    }
  },
  title: {
    text: 0,
    subtext: i18n.t('pa.home.flowTotal'),
    textAlign: 'center',
    textStyle: {
      fontWeight: 'bold',
      fontSize: 24,
      align: 'center'
    },
    subtextStyle: {
      color: '#777777',
      fontSize: 12,
      align: 'center'
    },
    x: '21.5%',
    y: '37%'
  },
  color: ['#377CFF', '#F9D452', '#FF7171', '#54C958', '#F1F1F1'],
  series: [
    {
      type: 'pie',
      radius: ['55%', '75%'],
      center: ['23%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        //中间文字显示
        show: true
      },
      labelLine: {
        show: false
      },
      data: []
    }
  ]
};

const flowProcessingOption: any = {
  legend: {},
  grid: {
    width: '96.5%',
    height: '65%',
    right: '0%',
    bottom: '10%',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    backgroundColor: '#ffffff',
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: '#DFDFDF',
        width: '30',
        opacity: 0.5
      }
    },
    textStyle: {
      color: '#444444'
    },
    extraCssText: `padding:12px;box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16);
    border-radius: 4px;
    opacity: 0.98;
    `,
    formatter(value) {
      return `<span style="display:inline-block;color: #777777;font-weight: 400;padding:5px 0">${value[0].axisValue}</span>
    <br/>
    <span>${value[0].seriesName}：</span><span style="display:inline-block;margin-left:5%">${value[0].data.displayValue}${value[0].data.unit}</span>`;
    }
  },
  xAxis: {
    axisTick: {
      show: false
    },
    triggerEvent: true,
    minInterval: 1,
    axisLabel: {
      color: '#777777',
      interval: 0,
      formatter(value) {
        if (value.length > xAxisLabelLength) {
          return `${value.slice(0, 4)}...`;
        }
        return value;
      }
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: '#F1F1F1'
      }
    },
    type: 'category',
    data: []
  },
  yAxis: {
    name: i18n.t('pa.home.count'),
    type: 'value',
    axisTick: {
      show: false
    },
    axisLabel: {
      color: '#777777'
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: '#F1F1F1'
      }
    },
    nameTextStyle: {
      color: '#AAAAAA'
    },
    nameGap: 20,
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: '#F1F1F1'
      }
    }
  },
  series: [
    {
      name: i18n.t('pa.home.dataCount'),
      data: [],
      type: 'bar',
      barWidth: 20,
      color: '#54C958'
    }
  ]
};

/* 引擎资源图表 */
export class EngineChart {
  static option: any = engineResourceOption;
  static setOption({ used, residual }) {
    engineResourceOption.series[0].data = used;
    engineResourceOption.series[1].data = residual;
  }
}

/* 流程状态/数量分布图表 */
export class FlowDistributionChart {
  static option: object = flowDistributionOption;
  static setOption(data) {
    const valueMap = {
      NONE: {
        title: i18n.t('pa.status.none'),
        color: '#377cff'
      },
      RUNNING: {
        title: i18n.t('pa.home.running'),
        color: '#FF9E2B'
      },
      FINISHED: {
        title: i18n.t('pa.home.text12'),
        color: '#54C958'
      },
      FAILED: {
        title: i18n.t('pa.home.text13'),
        color: '#FF5353'
      },
      CANCELED: {
        title: i18n.t('pa.home.text14'),
        color: '#aaaaaa'
      },
      RESTARTING: {
        title: i18n.t('pa.home.restart'),
        color: '#8C6BD6'
      },
      UNKNOWN: {
        title: i18n.t('pa.status.unKnow'),
        color: '#aaaaaa'
      }
    };
    const types = Object.keys(valueMap);
    const pieData = types
      .map((key) => {
        if (types.includes(key)) {
          return {
            value: data[key],
            name: valueMap[key].title,
            itemStyle: {
              opacity: data[key] > 0 ? 1 : 0
            }
          };
        }
      })
      .filter(Boolean);
    const color = Object.keys(valueMap)
      .map((el) => {
        return valueMap[el] && valueMap[el].color;
      })
      .filter(Boolean);
    const pieCount = pieData.reduce((total, cur: any) => {
      return total + cur.value;
    }, 0);
    flowDistributionOption.title.text = pieCount;
    flowDistributionOption.series[0].data = pieData;
    flowDistributionOption.legend.formatter = (name) => {
      const val: any = pieData.filter((item: any) => item.name === name);
      if (val.length > 0) {
        return `${name}\xa0\xa0\xa0\xa0${val[0].value}\xa0\xa0\xa0${
          val[0].value === 0 ? '0.00' : ((val[0].value / pieCount) * 100).toFixed(2)
        }%`;
      }
    };
    flowDistributionOption.legend.right = '5%';
    flowDistributionOption.legend.orient = 'vertical';
    flowDistributionOption.legend.itemGap = 8;
    flowDistributionOption.color = color;
  }
}
/* 流程数据处理TOP排行图表 */
export class FlowProcessingChart {
  static option: object = flowProcessingOption;
  static getOption() {
    this.option = this.setCount();
    return this.option;
  }
  static setOption({ data, series }) {
    flowProcessingOption.xAxis.data = data[0];
    flowProcessingOption.series[0].data = series[0].data;
  }
  // 设置top排行展示条数
  static setCount(count = 10) {
    const newChartOption = cloneDeep(flowProcessingOption);
    // 根据数据量进行降序排列
    newChartOption.series[0].data.sort((a, b) => b.value - a.value);
    // x轴根据选择Top N进行裁切
    newChartOption.xAxis.data = newChartOption.series[0].data.map((el) => el.label).slice(0, count);
    if (newChartOption.series[0].data.length > 8) {
      const map = {
        '5': 20,
        '10': 10,
        '15': 5
      };
      xAxisLabelLength = map[count];
    }
    return newChartOption;
  }
}
