<template>
  <pro-grid type="info" :title="$t('pa.node')">
    <!-- operation -->
    <el-button v-if="showRefresh" slot="operation" @click="getNodeList">{{ $t('pa.action.refresh') }}</el-button>
    <!-- main -->
    <bs-table
      v-loading="loading"
      paging-front
      :height="height"
      :data="tableData"
      :page-data="pageData"
      :column-settings="false"
      :column-data="columnData"
      @page-change="handlePageChange"
    >
      <!-- 状态 -->
      <template slot="status" slot-scope="{ row, $index }">
        <bs-tag size="mini" :color="getTagColor(row.status)">
          {{ getTagLabel(row.status) }}
        </bs-tag>
      </template>
    </bs-table>
  </pro-grid>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getNodeData } from '@/apis/serviceApi';
import { safeArray, timeFormat } from '@/utils';

@Component
export default class NodeInfo extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: () => ({}) }) params!: any;

  loading = false;
  id = '';
  columnData: any[] = [];
  tableData: any[] = [];
  pageData = {
    pageSize: this.$store.getters.pageSize || 25,
    currentPage: 1,
    total: 0
  };

  get height() {
    return this.tableData.length ? '460px' : '180px';
  }
  get showRefresh() {
    return this.data?.dataLevelType !== 'PARENT' && this.data?.belongType === 'SELF';
  }

  created() {
    this.id = this.$route.query.id as string;
    this.getNodeList();
  }

  /* 获取节点 */
  async getNodeList() {
    try {
      this.loading = true;
      const { success, data, error } = await getNodeData(this.id);
      if (!success) return this.$message.error(error);
      this.columnData = safeArray(data?.columnData).map((it) => {
        it.value = it.prop;
        this.isEn && it.value === 'status' && (it.width = 140);
        return it;
      });
      this.tableData = safeArray(data?.tableData).map((it) => {
        it.url = `${it.ip}:${it.port}`;
        it.createTime = timeFormat(it.createTime);
        it.updateTime = timeFormat(it.updateTime);
        return it;
      });
      this.pageData.total = this.tableData?.length || 0;
    } finally {
      this.loading = false;
    }
  }
  getTagColor(status: string) {
    return this.columnData[0]?.enumColor[status];
  }
  getTagLabel(status: string) {
    return this.columnData[0]?.enumData[status];
  }
  /* 分页变换 */
  handlePageChange(currentPage: number, pageSize: number) {
    this.pageData.currentPage = this.pageData.pageSize !== pageSize ? 1 : currentPage;
    this.pageData.pageSize = pageSize;
  }
}
</script>
<style lang="scss" scoped>
.iconfont {
  margin-right: 10px;
  cursor: pointer;
}
</style>
