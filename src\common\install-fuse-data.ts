import Vue from 'vue';
import Cookies from 'js-cookie';
import { getToken } from '@/utils';
import { SET_LOGIN_URL, SET_TOKEN } from '@/store/event-names/mutations';

export default async (store: any) => {
  const { data } = await Vue.axios.get('/openApi/pa/getLoginUrl');
  Vue.prototype.$global = data;
  store.commit(SET_LOGIN_URL, data);
};
export const handleFuseToekn = (store) => {
  return new Promise((resolve, reject) => {
    try {
      const token = getToken();
      token && Cookies.set('access_token', token, { expires: 1 });
      token && Cookies.set('SESSION', '');
      token && store.commit(SET_TOKEN, token);
      resolve(true);
    } catch {
      reject(false);
    }
  });
};
