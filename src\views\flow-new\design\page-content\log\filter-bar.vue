<template>
  <div class="filter__container tab-title">
    <div class="filter__title title-text">{{ title }}{{ $t('pa.flow.logInfo') }}</div>
    <!-- 表单 -->
    <el-form class="filter-form" size="small">
      <div class="filter-form__row">
        <el-input
          v-if="formData.isManualMode"
          v-model="formData.keyWord"
          clearable
          class="filter-form__input"
          :placeholder="$t('pa.flow.placeholder38')"
          @input="resetParmas"
        />
        <el-date-picker
          v-if="formData.isManualMode"
          v-model="formData.dateTime"
          type="datetimerange"
          :range-separator="$t('pa.flow.zhi')"
          :end-placeholder="$t('pa.flow.endDate')"
          start-placeholder="$t('pa.flow.startDate')"
          class="filter-form__datetime"
          @change="resetParmas"
        />
      </div>
      <div class="filter-form__row">
        <el-select
          v-model="formData.level"
          :placeholder="$t('pa.flow.placeholder39')"
          class="filter-form__select"
          @change="$emit('change')"
        >
          <el-option v-for="el in buttonList" :key="el.value" :label="el.label" :value="el.value" />
        </el-select>
        <el-button v-if="!formData.isManualMode" @click="click('stop')">{{ $t('pa.flow.stop') }}</el-button>
        <el-switch v-model="formData.scroll" class="filter-form__switch" :inactive-text="$t('pa.flow.scroll')" />
        <!-- :class="['flex-item', !level ? 'disabled' : '']" -->
        <el-button :disabled="!showExportButton" type="primary" @click="click('export')">{{
          $t('pa.flow.export')
        }}</el-button>
        <!-- :class="['flex-item', !formData.level ? 'disabled' : '', 'search-button']" -->

        <el-tooltip
          v-if="formData.isManualMode"
          :content="$t('pa.flow.msg223')"
          placement="top"
          effect="light"
          :disabled="!!formData.level"
        >
          <el-button type="primary" @click="click('search')">
            {{ formData.timeStamp ? $t('pa.flow.search1') : $t('pa.flow.search') }}
          </el-button>
        </el-tooltip>
      </div>
    </el-form>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import debounce from 'lodash/debounce';
import { hasPermission } from '@/utils';

@Component
export default class FilterBar extends Vue {
  @Prop({ default: '' }) mode!: string;
  @PropSync('data', { default: () => ({}) }) formData!: any;

  get buttonList() {
    return [
      { label: 'info', value: 'info', type: this.formData.level === 'info' ? 'primary' : '' },
      { label: 'warn', value: 'warn', type: this.formData.level === 'warn' ? 'primary' : '' },
      { label: 'error', value: 'error', type: this.formData.level === 'error' ? 'primary' : '' },
      {
        label: 'warn_data',
        value: 'warn_data',
        type: this.formData.level === 'warn_data' ? 'primary' : ''
      },
      {
        label: this.$t('pa.flow.lowData'),
        value: 'inferior_quality_data',
        type: this.formData.level === 'inferior_quality_data' ? 'primary' : ''
      }
    ];
  }

  get title() {
    return this.formData.level === 'inferior_quality_data' ? this.$t('pa.flow.lowData') : this.formData.level;
  }
  get showExportButton() {
    return ['warn_data', 'inferior_quality_data'].includes(this.formData.level) && hasPermission('PA.FLOW');
  }

  private click = debounce((type: string) => this.handleClick(type), 1200);
  handleClick(type: string) {
    return type === 'search' ? this.formData.level && this.$emit('click', type) : this.$emit('click', type);
  }
  resetParmas() {
    this.formData.lineNum = 0;
    this.formData.timeStamp = 0;
  }
}
</script>
<style lang="scss" scoped>
.filter {
  &__container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-width: 641px;
    height: 80px;
    overflow: hidden;
  }
  &-form {
    display: block;
    width: 467px;
    overflow: hidden;
    &__row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:nth-last-child(1) {
        margin-top: 8px;
      }
    }
    &__input,
    &__select {
      width: 144px;
    }
    &__switch {
      width: 130px;
    }
    &__datetime {
      padding: 0 3px 0 7px;
      width: 319px;

      box-sizing: border-box;
      ::v-deep .el-range {
        &-input {
          width: 136px;
        }
        &-separator {
          padding: 0;
        }
      }
    }
  }
}
</style>
