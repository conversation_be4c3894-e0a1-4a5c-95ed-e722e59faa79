<template>
  <div class="filter__container tab-title">
    <div class="filter__title title-text">{{ title }}日志信息</div>
    <!-- 表单 -->
    <el-form class="filter-form" size="small">
      <div class="filter-form__row">
        <el-input
          v-if="formData.isManualMode"
          v-model="formData.keyWord"
          clearable
          class="filter-form__input"
          placeholder="搜索多个关键字用逗号分隔"
          @input="resetParmas"
        />
        <el-date-picker
          v-if="formData.isManualMode"
          v-model="formData.dateTime"
          type="datetimerange"
          range-separator="至"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          class="filter-form__datetime"
          @change="resetParmas"
        />
      </div>
      <div class="filter-form__row">
        <el-select
          v-model="formData.level"
          placeholder="请选择日志类型"
          class="filter-form__select"
          @change="$emit('change')"
        >
          <el-option v-for="el in buttonList" :key="el.value" :label="el.label" :value="el.value" />
        </el-select>
        <el-button v-if="!formData.isManualMode" @click="click('stop')"> 停止</el-button>
        <el-switch
          v-model="formData.scroll"
          inactive-text="不滚动"
          active-text="滚动"
          active-color="#13ce66"
          inactive-color="#ff4949"
        />
        <!-- :class="['flex-item', !level ? 'disabled' : '']" -->
        <el-button :disabled="!showExportButton" type="primary" @click="click('export')">
          导出
        </el-button>
        <!-- :class="['flex-item', !formData.level ? 'disabled' : '', 'search-button']" -->

        <el-tooltip
          v-if="formData.isManualMode"
          content="请先选择日志类型"
          placement="top"
          effect="light"
          :disabled="!!formData.level"
        >
          <el-button type="primary" @click="click('search')">
            {{ formData.timeStamp ? '继续查询' : '查询' }}
          </el-button>
        </el-tooltip>
      </div>
    </el-form>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import debounce from 'lodash/debounce';
import { hasPermission } from '@/utils';

@Component
export default class FilterBar extends Vue {
  @Prop({ default: '' }) mode!: string;
  @PropSync('data', { default: () => ({}) }) formData!: any;

  get buttonList() {
    return [
      { label: 'info', value: 'info', type: this.formData.level === 'info' ? 'primary' : '' },
      { label: 'warn', value: 'warn', type: this.formData.level === 'warn' ? 'primary' : '' },
      { label: 'error', value: 'error', type: this.formData.level === 'error' ? 'primary' : '' },
      {
        label: 'warn_data',
        value: 'warn_data',
        type: this.formData.level === 'warn_data' ? 'primary' : ''
      },
      {
        label: '低质量数据',
        value: 'inferior_quality_data',
        type: this.formData.level === 'inferior_quality_data' ? 'primary' : ''
      }
    ];
  }

  get title() {
    return this.formData.level === 'inferior_quality_data' ? '低质量数据' : this.formData.level;
  }
  get showExportButton() {
    return (
      ['warn_data', 'inferior_quality_data'].includes(this.formData.level) &&
      hasPermission('PA.FLOW')
    );
  }

  private click = debounce((type: string) => this.handleClick(type), 1200);
  handleClick(type: string) {
    return type === 'search'
      ? this.formData.level && this.$emit('click', type)
      : this.$emit('click', type);
  }
  resetParmas() {
    this.formData.lineNum = 0;
    this.formData.timeStamp = 0;
  }
}
</script>
<style lang="scss" scoped>
.filter {
  &__container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-width: 641px;
    height: 80px;
    overflow: hidden;
  }
  &-form {
    display: block;
    width: 467px;
    overflow: hidden;
    &__row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:nth-last-child(1) {
        margin-top: 8px;
      }
    }
    &__input,
    &__select {
      width: 144px;
    }
    &__datetime {
      padding: 0 3px 0 7px;
      width: 319px;

      box-sizing: border-box;
      ::v-deep .el-range {
        &-input {
          width: 136px;
        }
        &-separator {
          padding: 0;
        }
      }
    }
  }
}
</style>
