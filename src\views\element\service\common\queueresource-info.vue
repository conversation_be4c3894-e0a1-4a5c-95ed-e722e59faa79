<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">队列资源详情</div>
    </div>
    <div class="tab-content" :style="{ height: height }">
      <base-table
        v-loading="tableLoading"
        :height="'100%'"
        :table-data="queueResourceData"
        :table-config="nodeTableConfig"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import baseTable from '@/components/base-table.vue';
@Component({
  components: {
    baseTable
  }
})
export default class QueueResourceInfo extends PaBase {
  height = '300px';
  tableLoading = false;
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  created() {
    this.loadData(this.comDetailRecord.val || {}, this.comParams.QueueResourceInfo || {});
  }
  private queueResourceData: ITableData = {
    columnData: [],
    tableData: []
  };
  private nodeTableConfig: ITableConfig = {
    width: 80,
    columnsExtend: {
      edit: []
    }
  };

  async loadData(data: any, params: any) {
    this.height = params.height;
    if (this.$route.query.resType === 'HOST') {
      this.getDependList(data.title, params.findDependUrl);
    } else {
      this.getDependList(data.title, params.findDependUrl + '/' + this.$route.query.resType);
    }
  }

  getDependList(title: string, api: string) {
    this.tableLoading = true;
    this.doGet(api, {
      params: {
        title
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.queueResourceData = {
          ...resp.data
        };
      });
      this.tableLoading = false;
    });
  }
}
</script>
<style scoped></style>
