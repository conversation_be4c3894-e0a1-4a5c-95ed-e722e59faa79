<template>
  <bs-dialog
    size="large"
    :title="title"
    append-to-body
    :visible.sync="visible"
    class="ddMapping-dialog"
    :before-close="closeDialog"
    @confirm="handlerConfirm"
  >
    <div v-loading="loading" class="ddMapping__container" :element-loading-text="$t('pa.elementLoading') + '...'">
      <!-- header -->
      <el-form ref="ddFormRef" :model="formData" inline class="ddMapping-form">
        <!-- 数据定义 -->
        <el-form-item :label="$t('pa.menu.dataDefine')" prop="className" :rules="classNameRule">
          <el-tooltip effect="light" placement="bottom" :disabled="!classNameStatus" :content="classNameStatus">
            <bs-select
              v-model="formData.className"
              clearable
              filterable
              :options="ddList"
              style="width: 400px"
              :option-width="400"
              :placeholder="$t('pa.flow.msg294')"
              :disabled="!!classNameStatus || disabled"
              @focus="getDdList"
              @change="getDdDetail"
            />
          </el-tooltip>
          <!-- 重新选择 -->
          <el-button
            v-if="classNameStatus"
            type="text"
            :disabled="disabled"
            style="margin-left: 8px"
            @click="handleReselect"
          >
            {{ $t('pa.flow.select') }}
          </el-button>
        </el-form-item>
        <bs-search
          v-model="keyword"
          :class="isEn ? 'ddMapping-dialog__search--en' : ''"
          :placeholder="$t('pa.flow.msg295')"
        />
      </el-form>
      <!-- body -->
      <select-table
        ref="selectTableRef"
        :data="tableData"
        :has-class-name="!!this.formData.className"
        :keyword="keyword"
        :disabled="disabled"
        :input-fields="inputFields"
      />
      <!-- footer -->
      <el-form ref="nameFormRef" :model="formData" inline :disabled="disabled" class="ddMapping-form ddMapping-form--name">
        <!-- 数据定义全类名输出 -->
        <el-form-item :label="$t('pa.flow.label66')" prop="writeClassName" required class="ddMapping-form__item">
          <el-switch v-model="formData.writeClassName" />
          <el-tooltip effect="light" placement="bottom" :content="$t('pa.flow.msg296')">
            <i class="iconfont icon-wenhao"></i>
          </el-tooltip>
        </el-form-item>
        <!-- 输出字段 -->
        <el-form-item :label="$t('pa.flow.outputFields')" prop="name" :rules="nameRule" class="ddMapping-form__item">
          <el-input v-model="formData.name" :maxlength="30" style="width: 380px" :placeholder="$t('pa.flow.msg204')" />
          <el-tooltip effect="light" placement="bottom" :content="$t('pa.flow.msg297')">
            <i class="iconfont icon-wenhao"></i>
          </el-tooltip>
        </el-form-item>
      </el-form>
    </div>
    <!-- dialog footer-left -->
    <print-log slot="footer-left" v-model="printLog" :disabled="disabled" />
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Ref, Emit, Vue } from 'vue-property-decorator';
import { getDdList, getDdStatus, getDdDetail } from '@/apis/flowNewApi';
import type { ElForm } from 'bs-ui-pro/types/form.d';
import SelectTable from './select-table.vue';
import cloneDeep from 'lodash/cloneDeep';
import { safeArray } from '@/utils';

@Component({
  components: { SelectTable, PrintLog: () => import('../components/print-log.vue') }
})
export default class DdMapping extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: false }) disabled!: boolean;
  @Ref('ddFormRef') readonly ddForm!: ElForm;
  @Ref('nameFormRef') readonly outputForm!: ElForm;
  @Ref('selectTableRef') readonly selectTable!: SelectTable;

  loading = false;
  formData = { className: '', writeClassName: false, name: 'data' };
  classNameRule = { required: true, message: this.$t('pa.flow.msg294'), trigger: 'change' };
  nameRule = {
    required: true,
    validator: (rule: any, value: string, callback: any) => {
      if (!value) return callback(new Error(this.$t('pa.flow.msg204')));
      if (!/^[a-zA-Z0-9-_]*$/g.test(value)) {
        return callback(new Error(this.$t('pa.flow.msg298')));
      }
      return callback();
    },
    trigger: 'blur'
  };
  ddList: any[] = [];
  keyword = ''; // 搜索词
  printLog = false; // 日志输出
  tableData: any[] = []; // 配置数据
  inputFields: any = [];
  classNameStatus = ''; // 数据定义状态

  get title() {
    const { nodeName = '', componentName = '' } = this.data || {};
    return this.$t('pa.flow.msg288', [nodeName, componentName]);
  }

  async created() {
    try {
      this.loading = true;
      await this.getDdStatus();
      await this.getDdList();
      await this.initCfgData();
    } finally {
      this.loading = false;
    }
  }
  /* 获取数据定义状态 */
  async getDdStatus() {
    const { success, data, error = '' } = await getDdStatus(this.jobData.id, false);
    if (!success) return this.$tip.error(error);
    this.classNameStatus = data && String(data[this.data.nodeId] || '');
  }
  /* 获取数据定义列表 */
  async getDdList() {
    if (this.classNameStatus) return;
    const { success, data, error = '' } = await getDdList();
    if (!success) return this.$tip.error(error);
    this.ddList = safeArray(data).map((value) => ({ value, label: value }));
  }
  /* 初始化配置数据 */
  initCfgData() {
    const { className = '', writeClassName = false, filedList = [] } = this.data?.properties || {};
    this.inputFields = safeArray(this.data?.inputFields).map((it) => ({ lable: it.name, value: it.name, ...it }));
    this.update(filedList, true);
    const { name = 'data' } = safeArray(this.data?.outputFields)[0];
    this.formData = { ...this.formData, className, writeClassName, name };
    this.printLog = Boolean(this.data.printLog);
  }
  /* 获取数据定义详情 */
  async getDdDetail() {
    if (!this.formData.className) return this.update([]);
    const { success, data, error = '' } = await getDdDetail(this.formData.className);
    if (!success) return this.$tip.error(error);
    this.update(data);
  }
  async update(data = [], isInput = false) {
    this.tableData = this.getTableData(data, isInput);
    await this.$nextTick();
    this.selectTable.filter();
  }
  /* 获取 tableData */
  getTableData(data: any[] = [], isInput = false) {
    return safeArray(data).map((it) => {
      const deriveDependent = safeArray(it.deriveDependent);
      return {
        filedName: it.filedName,
        filedType: it.filedType,
        className: it.className,
        chooseFieldFromParent: isInput ? it.chooseFieldFromParent : '',
        derive: it.derive,
        deriveStr: it.deriveStr,
        deriveDependent,
        selected: isInput ? it.selected : false,
        show: true,
        isNesting: String(it.filedName).includes('/'),
        deriveDependentStr: deriveDependent.join(',')
      };
    });
  }
  /* 重新选择 */
  handleReselect() {
    this.formData.className = '';
    this.tableData = [];
    this.classNameStatus = '';
  }
  /* 表单提交 */
  async handlerConfirm() {
    await this.ddForm.validate();
    await this.outputForm.validate();
    const filedList = await this.selectTable.validate();
    const jobNode = cloneDeep(this.data);
    const { className, writeClassName, name } = this.formData;
    jobNode.ioProperties = `{"emitTime":0,"f0":[],"f1":["outputField"]}`;
    jobNode.properties = { className, writeClassName, filedList, outputField: [name] };
    jobNode.outputFields = [{ name, type: 'String', outputable: true, targetable: true }];
    jobNode.printLog = this.printLog;
    this.closeDialog(true, jobNode);
  }
  @Emit('close')
  closeDialog(needUpdate, jobNode) {
    return { needUpdate, jobNode };
  }
}
</script>
<style lang="scss" scoped>
$height: 480px;
@media screen and (max-height: 710px) {
  .ddMapping-dialog {
    display: flex;
    justify-content: center;
    align-items: center;
    ::v-deep .el-dialog {
      margin: 0 !important;
      &__body {
        padding: 0;
        height: height;
        min-height: height;
        max-height: height;
      }
    }
  }
}
.ddMapping {
  &-dialog {
    ::v-deep .el-dialog__body {
      padding: 0;
      height: $height;
      min-height: $height;
      max-height: $height;
    }
    &__search {
      &--en {
        width: 230px !important;
      }
    }
  }
  &__container {
    ::v-deep .el-form-item {
      margin: 0 !important;
    }
    ::v-deep .bs-select {
      width: 100%;
    }
    ::v-deep .el-tooltip {
      margin-left: 8px;
      vertical-align: middle;
    }
  }
  &-form {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    &--name {
      padding-bottom: 0;
      ::v-deep .el-form-item__content1 {
        width: 500px;
        .el-input {
          display: inline-block;
          margin-right: 8px;
          width: calc(100% - 30px);
        }
      }
    }
  }
  &-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
