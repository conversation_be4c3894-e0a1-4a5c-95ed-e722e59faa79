<template>
  <pro-grid title="模式序列" type="info" class="sequence__container">
    <!-- 主体内容 -->
    <div class="sequence__container__main">
      <pre-form ref="preForm" :data.sync="formData[0]" :group-list="groupList" />
      <config-item
        ref="formRef"
        is-sequence
        :index="0"
        :disabled="disabled"
        :data.sync="formData[0]"
        :row-data.sync="formData"
        :name-options="nameOptions"
        :used-module-list.sync="moduleList"
        @view="showRelationDialog = true"
      />
      <sequence-relation
        v-if="showRelationDialog"
        :show.sync="showRelationDialog"
        :data="formData"
        :group-data="groupData"
      />
    </div>
  </pro-grid>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import ConfigItem from '../component/model-config-item.vue';
import SequenceRelation from './sequence-relation/index.vue';
import PreForm from './pre-form.vue';

@Component({
  components: {
    PreForm,
    ConfigItem,
    SequenceRelation
  }
})
export default class ModelSequence extends Vue {
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => [] }) nameOptions!: any;
  @Prop({ default: () => [] }) groupData!: any;
  @PropSync('data', { default: () => [] }) formData!: any[];
  @PropSync('usedModuleList', { default: () => [] }) moduleList!: any;
  @Ref('formRef') readonly form!: ConfigItem;

  private showRelationDialog = false;

  get groupList() {
    return this.groupData.map(({ groupName }: any) => groupName);
  }

  validate() {
    return Promise.all([(this.$refs as any).preForm.validate(), this.form.validate()]);
  }
}
</script>

<style lang="scss" scoped>
.sequence {
  &__container {
    &__main {
      width: 100%;
    }
  }
}
</style>
