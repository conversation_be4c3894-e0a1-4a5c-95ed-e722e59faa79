<template>
  <div class="view-detial-history">
    <bs-table
      height="calc(100vh - 289px)"
      :data="tableData"
      :column-data="columnData"
      :page-data="pageData"
      @page-change="handleCurrentChange"
      @sort-change="handleSortChange"
    >
      <template slot="action" slot-scope="{ row }">
        <el-tooltip content="回滚" effect="light">
          <i class="iconfont icon-roll-back" @click="rollBack(row)"></i>
        </el-tooltip>
        <el-tooltip content="查看源码" effect="light">
          <i class="iconfont icon-ziyuan" @click="showCode(row)"></i>
        </el-tooltip>
      </template>
    </bs-table>
    <preview ref="preview" :title="'源码查看'" :data="sourceCode" />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import * as _ from 'lodash';
import { getViewHistory, getViewHistorySourceCode, viewHistoryRollBack } from '@/apis/dataApi';
import { hasPermission } from '@/utils';
import moment from 'moment';

@Component({
  components: {
    preview: () => import('../../modals/flink-sql.vue')
  }
})
export default class ViewDetailHistory extends Vue {
  sourceCode: any = {};
  tableLoading = false;
  pageData = { pageSize: 20, currentPage: 1, total: 1 };
  sortData = {};
  tableData: any = [];
  columnData: any = [];
  id = '';
  created() {
    this.id = this.$route.query.id as string;
    this.getListData();
  }
  // 显示源代码弹窗
  async showCode(row) {
    const { data } = await getViewHistorySourceCode({ resId: row.id });
    const previewRef: any = this.$refs.preview;
    previewRef.visible = true;
    this.sourceCode = data;
  }
  // 回滚
  rollBack(row: any) {
    this.$confirm('是否确认回滚?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        await viewHistoryRollBack({ viewHisId: row.id });
        this.getListData();
      })
      .catch(() => {
        return true;
      });
  }
  // 分页事件回调
  handleCurrentChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    this.getListData();
  }
  // 获取数据列表
  async getListData() {
    const params = {
      pageData: this.pageData,
      search: this.id,
      sortData: {}
    };
    const { data } = await getViewHistory(params);
    this.tableData = data.tableData;
    this.tableData.forEach((el) => {
      el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
    });
    this.columnData = data.columnData;
    this.pageData.total = data.pageData.total;
  }
  hasAuthority(dataLevelType: string, accessCode: string) {
    return !_.isEmpty(_.toString(dataLevelType)) && hasPermission(accessCode);
  }
  // 表格排序回调
  handleSortChange(val) {
    this.sortData = {
      [val.prop]: val.order === 'ascending' ? 'ASC' : 'DESC'
    };
    this.getListData();
  }
}
</script>

<style scoped lang="scss">
.iconfont {
  cursor: pointer;
}
.iconfont.icon-roll-back {
  margin-right: 8px;
}
</style>
