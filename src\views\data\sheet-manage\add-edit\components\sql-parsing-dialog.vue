<template>
  <bs-dialog
    v-loading="loading"
    :title="$t('pa.data.table.detail.sqlParsing')"
    :visible.sync="display"
    size="medium"
    class="parsing-dialog"
  >
    <bs-code
      ref="codeRef"
      :value="code"
      language="sql"
      :read-only="false"
      :operatable="false"
      :extra-style="{ height: '400px' }"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="display = false">{{ $t('pa.action.cancel') }}</el-button>
      <el-button type="primary" @click="handelParsing">{{ $t('pa.action.analysis') }}</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, PropSync, Ref, Vue } from 'vue-property-decorator';
import { parsingSql } from '@/apis/sqlApi';
@Component
export default class SqlParsingDialog extends Vue {
  @PropSync('show', { type: Boolean }) display!: boolean;
  @Ref('codeRef') readonly codeRef!: any;

  loading = false;
  code = '';

  async handelParsing() {
    try {
      this.loading = true;
      this.code = this.codeRef.getValue();
      if (!this.code) return this.$message.error(this.$t('pa.placeholder.sqlPlaceholder'));
      const { data, success, msg, error } = await parsingSql({ sql: this.code });
      this.loading = false;
      if (!success) return this.$message.error({ message: error, duration: 5000 });
      this.$message.success(msg);
      this.$emit('confirm', data);
      this.display = false;
    } finally {
      this.loading = false;
    }
  }
}
</script>
<style lang="scss" scoped>
.parsing-dialog {
  ::v-deep .el-dialog__body {
    padding: 0;
    overflow: hidden;
  }
}
</style>
