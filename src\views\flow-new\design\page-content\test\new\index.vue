<template>
  <div>
    <bs-dialog :title="title" :visible="true" :before-close="closeDialog" width="1000px">
      <el-form ref="ruleForm" v-loading="loading" :model="formData" :rules="rules">
        <div style="display: flex">
          <el-form-item
            label="用例名称"
            prop="testDataName"
            label-width="80px"
            style="width: 50%; margin-right: 20px"
          >
            <el-input
              v-model="formData.testDataName"
              maxlength="30"
              :disabled="disabled"
              type="text"
              show-word-limit
              placeholder="请输入用例名称，长度不超过30字符"
            />
          </el-form-item>

          <el-form-item prop="typeOfData" label="造数逻辑" label-width="80px" style="width: 50%">
            <el-select v-model="formData.typeOfData" :disabled="disabled" style="width: 100%">
              <el-option
                v-for="item in logicList"
                :key="item.label"
                :label="item.label"
                :value="item.value"
                :show-operate-column="!disabled"
              >
                {{ item.label }}
              </el-option>
            </el-select>
          </el-form-item>
        </div>

        <div style="position: relative">
          <el-tabs v-model="activeTab">
            <el-tab-pane
              v-for="item in processName"
              :key="item.value"
              :name="item.value"
              :label="item.name"
            >
              <div v-if="!isSql">
                <pro-edit-table
                  v-show="isManual"
                  :ref="item.value"
                  :data="item.handList.data"
                  :columns="item.handList.columns"
                  :edit-props="editType"
                  row-key="rowKey"
                  :show-operate-column="false"
                  @change="handleChange"
                />
              </div>
              <div v-if="isSql">
                <div v-show="isManual">
                  <el-form-item
                    label="用例数据"
                    required
                    label-width="80px"
                    style="margin-bottom: 0px"
                  >
                    <span style="color: #377cff">{{ `(${item.handList.data.length})` }}</span>
                  </el-form-item>

                  <pro-edit-table
                    :ref="item.value"
                    :data="item.handList.data"
                    :columns="item.handList.columns"
                    :edit-props="editProps"
                    row-key="index"
                    :show-operate-column="!disabled"
                    @change="handleChange"
                  />
                </div>
                <div v-show="isCreate">
                  <el-form-item v-if="isSql" label="行数" prop="rows" label-width="80px">
                    <el-input-number
                      v-model="formData.rows"
                      controls-position="right"
                      :max="100"
                      :min="1"
                      :disabled="disabled"
                      :step="1"
                    />
                  </el-form-item>
                  <bs-table
                    :column-settings="false"
                    :data="item.list.data"
                    :column-data="item.list.columns"
                    :page-sizes="[10, 20, 40, 60, 80]"
                    :page-data="item.list.pageData"
                  >
                    <template slot="operator" slot-scope="{ row }">
                      <el-tooltip v-if="!disabled" content="编辑" effect="light">
                        <i class="iconfont icon-bianji" @click="edit(row)"></i>
                      </el-tooltip>
                    </template>
                  </bs-table>
                </div>
              </div>
              <div v-show="isCsv">
                <span style="position: absolute; left: 650px; z-index: 1">
                  分隔符
                  <el-input
                    v-model="formData.delimiter"
                    :disabled="disabled"
                    style="width: 220px"
                    placeholder="不输入默认使用英文逗号(,)"
                  />
                </span>
                <el-form-item label-position="top" label="测试数据" required label-width="80px">
                  <el-input
                    v-model="item.csvList.csvData"
                    type="textarea"
                    placeholder="请输入"
                    :autosize="{ minRows: 14 }"
                    maxlength="3000"
                    :disabled="disabled"
                    style="width: 100%; height: 100%; margin-top: 10px"
                  />
                </el-form-item>
              </div>
            </el-tab-pane>
          </el-tabs>

          <el-button
            v-if="isManual && isSql"
            type="primary"
            :disabled="disabled"
            style="position: absolute; right: 5px; top: 5px"
            @click="addHandInfo"
          >
            添加数据
          </el-button>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog()">取消</el-button>
        <el-button type="primary" @click="submit('ruleForm')">确定</el-button>
      </div>
    </bs-dialog>
    <NumLogic v-if="show" :data="rowInfo" @close="show = false" @submit="changeInfo" />
  </div>
</template>
<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import NumLogic from '../components/num-logic.vue';
import FlinkSqlEditor from '@/components/flink-sql-editor/index.vue';
import {
  getColumns,
  saveParams,
  getParams,
  saveInfo,
  getDataResult,
  validataProcess
} from './index';
import { addTestData, updateTestData, testDataDetail, getJobFields } from '@/apis/flowTestApi';
// import { Form } from 'bs-ui-pro/packages/form';
@Component({
  components: {
    NumLogic,
    FlinkSqlEditor
  }
})
export default class Add extends PaBase {
  @Prop() data!: any;
  @Prop() info!: any;
  @Prop() visible!: boolean;
  @Prop() title!: string;
  @Prop() type!: string;
  loading = false;
  editProps = {
    iconStyle: true,
    operateColumnWidth: 120
  };
  dataEdit = {
    multiple: true,
    autoSave: true
  };
  formData: any = {
    jobId: '',
    testDataName: '',
    typeOfData: 'MANUAL',
    rows: 1,
    delimiter: ''
  };
  // 高亮tab项
  activeTab = '';
  //测试用例数据
  processName: any = [];
  //造数逻辑弹窗
  show = false;
  // 造数编辑数据
  rowInfo = {};

  get logicList() {
    const dsList = [
      {
        value: 'MANUAL',
        label: '手动添加'
      },
      {
        value: 'CUSTOM',
        label: 'csv输入'
      }
    ];
    const sqlList = [
      ...dsList,
      {
        value: 'CREATE',
        label: '随机造数'
      }
    ];
    const result = this.isSql ? sqlList : dsList;
    return result;
  }
  get rules() {
    return {
      testDataName: { required: true, message: '请输入用例名称', trigger: 'change' },
      data: { required: true, message: '请输入用例数据', trigger: 'blur' },
      typeOfData: { required: true, message: '请选择造数逻辑', trigger: 'blur' },
      rows: { required: true, message: '请输入行数', trigger: 'blur' }
    };
  }
  // 流程类型
  get isSql() {
    return this.data.jobType !== 'PROCESSFLOW';
  }

  //禁用
  get disabled() {
    return this.type === 'view';
  }

  //添加方式为手动添加
  get isManual() {
    return this.formData.typeOfData === 'MANUAL';
  }

  //添加方式为CUSTOM输入
  get isCsv() {
    return this.formData.typeOfData === 'CUSTOM';
  }
  get isCreate() {
    return this.formData.typeOfData === 'CREATE';
  }
  get editType() {
    return this.disabled ? this.editProps : { ...this.editProps, ...this.dataEdit };
  }

  created() {
    this.formData.jobId = this.data.id;
    const { nodes } = this.data.content;
    const showList = nodes.filter((item) => item.operateType === 'SOURCE');
    this.activeTab = showList.length && showList[0].nodeId;
    this.processName =
      (showList.length &&
        showList.map((item) => {
          return {
            value: item.nodeId,
            name: item.nodeName,
            // sql类型自动造数
            list: {
              columns: [
                {
                  label: '序号',
                  value: 'index'
                },
                {
                  label: '字段',
                  value: 'fieldName'
                },
                {
                  label: '类型',
                  value: 'fieldType'
                },
                {
                  label: '造数逻辑',
                  value: 'other'
                },
                { label: '操作', value: 'operator', width: 100, fixed: 'right' }
              ],
              data: [],
              pageData: {
                pageSize: 10,
                currentPage: 1,
                total: 0
              }
            },
            // sql类型手动造数
            handList: {
              columns: [
                {
                  label: '序号',
                  type: 'index',
                  index: 1,
                  width: 80
                }
              ],
              data: []
            },
            // data类型数据
            dataList: {
              columns: [
                {
                  label: '序号',
                  type: 'index',
                  index: 1,
                  width: 80
                }
              ],
              data: []
            },
            //csv类型数据
            csvList: {
              csvData: ''
            }
          };
        })) ||
      [];
    // 列表参数查询
    this.fetchSourceNodes();
  }
  // 关闭
  closeDialog() {
    this.$emit('close');
  }

  // sql初始参数
  fetchSourceNodes() {
    getJobFields({ id: this.data.id }).then((resp) => {
      const { data } = resp;
      this.processName = this.processName.map((item) => {
        const headers = getColumns(data[item.value]);
        const defaultdata: any = {};
        headers.forEach((item) => {
          defaultdata[item.value] = '';
          defaultdata.rowKey = Date.now();
        });
        if (this.isSql) {
          return {
            ...item,
            list: {
              ...item.list,
              data: data[item.value].map((it, index) => {
                const { fieldName, fieldType, defaultRule } = it;
                return {
                  fieldName,
                  fieldType,
                  other: defaultRule,
                  index: index + 1
                };
              }),
              pageData: {
                ...item.list.pageData,
                total: data[item.value].length
              }
            },
            handList: {
              ...item.handList,
              columns: [...item.handList.columns, ...headers]
            }
          };
        } else {
          defaultdata.rowKey = Date.now();
          return {
            ...item,
            handList: {
              data: this.disabled ? [] : [defaultdata],
              columns: [...item.handList.columns, ...headers]
            }
          };
        }
      });
      if (['view', 'edit'].includes(this.type)) {
        this.fetchTest(this.info);
      }
    });
  }
  index = 1;
  // 手动添加一行
  addHandInfo() {
    const ref = (this.$refs[this.activeTab] as any)[0];
    this.index++;
    ref.action.create(this.index.toString());
  }
  // 数据变更
  handleChange(data) {
    if (this.isSql) {
      this.processName = this.processName.map((item) => {
        return {
          ...item,
          handList: {
            ...item.handList,
            data: item.value === this.activeTab ? data : item.handList.data
          }
        };
      });
    } else {
      this.processName = this.processName.map((item) => {
        return {
          ...item,
          dataList: {
            ...item.dataList,
            data: item.value === this.activeTab ? data : item.dataList.data
          }
        };
      });
    }
  }

  fetchTest(sData) {
    this.loading = true;
    testDataDetail({ jobId: sData.jobId, dataId: sData.id }).then((resp: any) => {
      if (resp.success) {
        this.loading = false;
        const { form, process } = this.isSql
          ? getParams(resp.data, this.formData, this.processName)
          : getDataResult(resp.data, this.formData, this.processName);
        this.formData = form;
        this.processName = process;
      } else {
        this.$tip.error(resp.msg);
      }
    });
  }
  // 随机造数编辑
  edit(row) {
    this.show = true;
    this.rowInfo = { ...row, type: row.fieldType.toLowerCase() };
  }
  //编辑保存数据
  changeInfo(data) {
    const {
      other: { type }
    } = data;
    if (type === 'ENUM') {
      delete data.other.random;
    } else {
      delete data.other.enumVal;
    }
    this.processName = this.processName.map((item) => {
      return {
        ...item,
        list: {
          ...item.list,
          data:
            item.value === this.activeTab
              ? item.list.data.map((it) => {
                  return it.index === data.index ? data : it;
                })
              : item.list.data
        }
      };
    });
  }
  // 提交数据
  submit(formName: string) {
    this.processName.forEach((el) => {
      //csv状态下，清空手动输入状态下的表格数据，阻止表格内表单验证
      if (this.isCsv) {
        this.$set(el.handList, 'data', []);
      }
      //非随机输入状态下，行数默认为1，防止其他状态下，rows的表单验证
      if (!this.isCreate) {
        this.$set(this.formData, 'rows', 1);
      }
    });
    setTimeout(() => {
      if (this.disabled) {
        this.$emit('close', true);
      } else {
        const form: any = this.$refs[formName];
        form.validate((valid: any) => {
          if (valid) {
            const name = this.$refs[this.activeTab] as any;
            name[0].validate().then((end) => {
              if (end) {
                const res = validataProcess(this.processName, this.isSql, this.formData.typeOfData);
                if (!res) return;
                const params = this.isSql
                  ? saveParams(this.formData, this.processName)
                  : saveInfo(this.formData, this.processName);
                if (this.formData.id) {
                  updateTestData(params).then((resp: any) => {
                    if (resp.success) {
                      this.$tip.success(resp.msg);
                      this.$emit('close', true);
                    } else {
                      this.$tip.error(resp.msg);
                    }
                  });
                } else {
                  addTestData(params).then((resp: any) => {
                    if (resp.success) {
                      this.$tip.success(resp.msg);
                      this.$emit('close', true);
                    } else {
                      this.$tip.error(resp.msg);
                    }
                  });
                }
              }
            });
          }
        });
      }
    });
  }
}
</script>
<style lang="scss" scoped src="./index.scss"></style>
