import { AxiosRequestConfig, AxiosError, AxiosResponse } from 'axios';
import { register } from '@bs/axios';
import store from '@/store';
import { baseUrl } from '@/config';
import { handleConfigToken, handleRequest, handleResponse, handleResponseToken, handleError } from './http-base';
const TIMEOUT = 300000;

const initAxios = () => {
  register.baseUrl(baseUrl.prev);
  register.timeout(TIMEOUT);
  register.requestInterceptor(
    (config: AxiosRequestConfig) => {
      config.withCredentials = true;
      handleConfigToken(config);
      handleRequest(config, store.getters.encryptEnable);
      return config;
    },
    (err) => Promise.reject(err)
  );
  register.responseInterceptor(
    (response: AxiosResponse) => {
      handleResponseToken(response?.headers);
      handleResponseToken(response?.headers);
      return handleResponse(response, store.getters.encryptEnable);
    },
    (error: AxiosError) => {
      handleError(error);
    }
  );
};
export default initAxios();
