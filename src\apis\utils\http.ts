import Vue from 'vue';
import axios, { AxiosRequestConfig, AxiosError, AxiosResponse } from 'axios';
import store from '@/store';
import { UPDATE_SPINSHOW } from '@/store/event-names/mutations';
import { baseUrl } from '@/config';
import $ from 'jquery';
import {
  handleConfigToken,
  handleRequest,
  handleResponse,
  handleResponseToken,
  handleError
} from './http-base';
const TIMEOUT = 300000;

let isEncypt;

// 检查加密类型
function checkEncryptType() {
  $.ajax({
    url: baseUrl.prev + '/openApi/pa/getAesEncrypt',
    async: false,
    success: (data) => {
      isEncypt = data;
    }
  });
}

const AxiosInterceptor = () => {
  Vue.axios.defaults.timeout = 1200000;
  Vue.axios.defaults.baseURL = baseUrl.prev;

  isEncypt === undefined && checkEncryptType();

  Vue.axios.interceptors.request.use((config: AxiosRequestConfig) => {
    handleConfigToken(config, store.state.app.token);
    handleRequest(config, isEncypt);
    return config;
  });
  Vue.axios.interceptors.response.use(
    (response: AxiosResponse) => {
      store.commit(UPDATE_SPINSHOW, false);
      handleResponseToken(response?.headers);
      return handleResponse(response, isEncypt);
    },
    (error: AxiosError) => {
      store.commit(UPDATE_SPINSHOW, false);
      handleError(error);
    }
  );
};

const initAxios = () => {
  const axiosInstance = axios.create({
    timeout: TIMEOUT,
    baseURL: baseUrl.prev
  });

  isEncypt === undefined && checkEncryptType();

  axiosInstance.interceptors.request.use((config: AxiosRequestConfig) => {
    handleConfigToken(config, store.state.app.token);
    handleRequest(config, isEncypt);
    return config;
  });
  axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => {
      store.commit(UPDATE_SPINSHOW, false);
      handleResponseToken(response?.headers);
      return handleResponse(response, isEncypt);
    },
    (error: AxiosError) => {
      store.commit(UPDATE_SPINSHOW, false);
      handleError(error);
    }
  );
  return axiosInstance;
};
export { AxiosInterceptor };
export default initAxios();
