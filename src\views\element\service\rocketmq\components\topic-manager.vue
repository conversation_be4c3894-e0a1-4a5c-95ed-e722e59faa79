<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">Topics</div>
      <div class="bs-detail__header-operation">
        <el-input
          v-model="topicKeyWord"
          placeholder="请输入关键字搜索"
          style="width: 200px; margin-right: 5px"
          @input="search"
        />
        <el-button
          v-if="this.hasFeatureAuthority(listConf.addTopciAuthCode, this.data.dataLevelType)"
          @click="handleCreateTopic"
        >
          创建
        </el-button>
      </div>
    </div>
    <div class="tab-content" :style="{ height: height }">
      <el-table v-loading="tableLoading" :data="topicData" size="mini" :height="'100%'">
        <el-table-column prop="topicName" label="名称" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="handlePreview(scope.row)">数据预览</el-button>
          </template>
        </el-table-column>
      </el-table>
      <topic-add
        :visible="dialogVisible"
        :data="recordData"
        :form-loading="formLoading"
        @close="closeDialog"
      />
      <topic-preview
        :data="previewRecordData"
        :visible="previewDialogVisible"
        @close="previewCloseDialog"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject } from 'vue-property-decorator';
import { URL_RES_DETAIL_ROCKETMQ_ALLTOPICS } from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
import _ from 'lodash';
@Component({
  components: {
    'topic-add': () => import('../modals/topic-add.vue'),
    'topic-preview': () => import('../modals/topic-preview.vue')
  }
})
export default class TopicManager extends PaBase {
  height = '300px';
  formLoading = false;
  dialogVisible = false;
  recordData: any = {};
  tableLoading = false;

  previewDialogVisible = false;
  previewRecordData: any = {};

  topicKeyWord = '';
  sourceTable: any = [];
  topicData: any[] = [];

  listConf: any = {};
  data: any = {};
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  @Inject('comListConf') comListConf;
  created() {
    this.loadData(
      this.comDetailRecord.val || {},
      this.comParams.TopicManager || {},
      this.comListConf.val || {}
    );
  }
  search() {
    this.topicData = this.sourceTable.filter(
      (data) =>
        !this.topicKeyWord || data.topicName.toLowerCase().includes(this.topicKeyWord.toLowerCase())
    );
  }
  handleCreateTopic() {
    this.dialogVisible = true;
    this.recordData.resId = this.$route.query.id;
    this.recordData.resType = this.$route.query.resType;
  }
  handlePreview(row: any) {
    this.previewDialogVisible = true;
    this.previewRecordData = row;
  }

  handleSearchTopic() {
    this.tableLoading = true;
    this.doGet(URL_RES_DETAIL_ROCKETMQ_ALLTOPICS, {
      params: { id: this.$route.query.id }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        // 初始化
        this.topicData = [];
        resp.data.forEach((item) => this.topicData.push({ topicName: item }));
        this.sourceTable = _.cloneDeep(this.topicData);
      });
      this.tableLoading = false;
    });
  }
  closeDialog(needFresh: any) {
    if (needFresh === true) {
      this.handleSearchTopic();
    }
    this.dialogVisible = false;
  }
  previewCloseDialog() {
    this.previewDialogVisible = false;
  }
  async loadData(data: any, params: any, listConf: any) {
    this.listConf = listConf;
    this.data = data;
    this.height = params.height;
    this.handleSearchTopic();
  }
}
</script>
<style scoped>
.my-card_body ::v-deep .el-card__body {
  padding: 0px 0px 0px 0px;
}
.my-wrap-row {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
}
.my-wrap-col {
  border: 1px solid #000;
}
.my-wrap-tag {
  width: 350px;
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
