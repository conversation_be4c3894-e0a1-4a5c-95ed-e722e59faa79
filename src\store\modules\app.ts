import { Message } from 'bs-ui-pro';
import { register } from '@bs/axios';
import { deployConf, getI18n, getSysMenu } from '@/apis/conf';
import { getPortalConfig } from '@/apis/userApi';
import changeThemeColor from 'bs-ui-pro/lib/utils/theme-color';
import { encode, decode, isValidMode, getMenu } from '../utils';
import i18n from '@/i18n';
import {
  GET_PA_CONFIG,
  GET_PORTAL_CONFIG,
  SET_PORTAL_CONFIG,
  SET_PA_MENU,
  SET_PLATFORM_NAME,
  SET_LOGIN_URL,
  SET_PUBLIC_IV,
  SET_PUBLIC_KEY,
  SET_ENCRYPTION_METHOD,
  SET_CLOUD_ENABLE,
  SET_SQL_ENABLE,
  SET_JAR_ENABLE,
  SET_JAR_DFS_ENABLE,
  SET_IS_COLLAPSE,
  SET_ENCRYPT_ENABLE,
  SET_PROJECT_LEVEL,
  SET_MONITOR_URL,
  SET_DIMENSION_SPLIT,
  GET_PA_MENU,
  GET_I18N_MAP,
  SET_I18N_MAP
} from '../event-name';

const appModule: AppModule = {
  encryptEnable: false,
  platformName: '',
  loginUrl: './login.html',
  iv: 'BD3BC335367E149EE45812DB43221132',
  publicKey: '86C63180C2806ED1F47B859DE501215B',
  encryptionMethod: 'BASE64',
  isCloud: false, // 是否融合解决方案，true为融合，false为独立部署
  enableSql: false,
  enableJar: false,
  enableDFS: false,
  isCollapse: false,
  monitorUrl: '',
  portalConfig: {},
  menu: [],
  projectLevelsNumber: 0,
  lineageDimensionSplit: ' ',
  i18nMap: {}
};
export default {
  state: () => appModule,
  mutations: {
    [SET_PA_MENU](state: AppModule, payload: any[]) {
      state.menu = payload;
    },
    [SET_ENCRYPT_ENABLE](state: AppModule, payload: boolean) {
      state.encryptEnable = payload;
    },
    [SET_PLATFORM_NAME](state: AppModule, payload: string = i18n.t('pa.company') as string) {
      state.platformName = payload;
      document.title = payload;
    },
    [SET_LOGIN_URL](state: AppModule, payload: string = './login.html') {
      state.loginUrl = payload;
    },
    [SET_PROJECT_LEVEL](state: AppModule, payload: number) {
      state.projectLevelsNumber = payload;
    },

    [SET_CLOUD_ENABLE](state: AppModule, payload: boolean) {
      state.isCloud = payload;
    },
    [SET_SQL_ENABLE](state: AppModule, payload: boolean) {
      state.enableSql = payload;
    },
    [SET_JAR_ENABLE](state: AppModule, payload: boolean) {
      state.enableJar = payload;
    },
    [SET_MONITOR_URL](state: AppModule, payload: string) {
      state.monitorUrl = payload;
    },
    [SET_JAR_DFS_ENABLE](state: AppModule, payload: boolean) {
      state.enableDFS = payload;
    },
    [SET_PUBLIC_IV](state: AppModule, key: string) {
      state.iv = typeof key === 'string' ? key : state.iv;
    },
    [SET_PUBLIC_KEY](state: AppModule, key: string) {
      state.publicKey = typeof key === 'string' ? key : state.publicKey;
    },
    [SET_ENCRYPTION_METHOD](state: AppModule, encryption: string) {
      state.encryptionMethod = isValidMode(encryption) ? encryption : 'BASE64';
    },
    [SET_PORTAL_CONFIG](state: AppModule, payload: any) {
      state.portalConfig = payload;
    },
    [SET_IS_COLLAPSE](state: AppModule, payload: boolean) {
      state.isCollapse = payload;
    },
    [SET_DIMENSION_SPLIT](state: AppModule, payload: string) {
      state.lineageDimensionSplit = payload;
    },
    [SET_I18N_MAP](state: AppModule, payload: Record<string, string>) {
      state.i18nMap = payload;
    }
  },
  actions: {
    async [GET_PA_CONFIG]({ commit }) {
      try {
        const { success, data, msg, error } = await deployConf();
        if (!success) return Message.error(error || msg);
        /* 配置相关 */
        commit(SET_ENCRYPT_ENABLE, !!data?.aesEncryptEnable);
        commit(SET_PLATFORM_NAME, data?.platformName);
        commit(SET_LOGIN_URL, data?.loginUrl);
        commit(SET_PROJECT_LEVEL, data?.projectLevelsNumber);
        /* 状态相关 */
        commit(SET_CLOUD_ENABLE, !!data?.cloudEnable);
        commit(SET_SQL_ENABLE, !!data?.sqlEnable);
        commit(SET_JAR_ENABLE, !!data?.jarEnable);
        commit(SET_MONITOR_URL, data?.monitorUrl);
        commit(SET_JAR_DFS_ENABLE, !!data?.jarDfsEnable);
        /* 加解密相关 */
        commit(SET_PUBLIC_IV, data?.iv);
        commit(SET_PUBLIC_KEY, data?.publicKey);
        commit(SET_ENCRYPTION_METHOD, data?.encryptionMethod);
        /* 血缘关系相关 */
        commit(SET_DIMENSION_SPLIT, data.lineageDimensionSplit);
      } catch {
        Message.error(i18n.t('pa.getConfFailed') as string);
      }
    },
    async [GET_PA_MENU]({ commit }) {
      try {
        const { success, data, msg, error } = await getSysMenu();
        if (!success) return Message.error(error || msg);
        /* 配置相关 */
        commit(SET_PA_MENU, getMenu(data));
      } catch {
        Message.error(i18n.t('pa.getMenuFailed') as string);
      }
    },
    async [GET_PORTAL_CONFIG]({ commit, state }) {
      try {
        const { success, data, msg, error } = await getPortalConfig();
        if (!success) return Message.error(error || msg);
        commit(SET_PORTAL_CONFIG, data);
        state.isCloud && data.themeColor && changeThemeColor(data.themeColor);
        register.session({ ...data, url: '/login/forceRefreshToken', loginUrl: state.loginUrl });
      } catch {
        Message.error(i18n.t('pa.getConfFailed') as string);
      }
    },
    async [GET_I18N_MAP]({ commit, state }) {
      try {
        const { success, data, msg, error } = await getI18n();
        if (!success) return Message.error(error || msg);
        commit(SET_I18N_MAP, data);
      } catch {
        console.log('[GET_I18N_MAP] ERROR');
      }
    }
  },
  getters: {
    platformName: ({ platformName }: AppModule) => platformName,
    menu: ({ menu }: AppModule) => menu,
    loginUrl: ({ loginUrl }: AppModule) => loginUrl,
    isCloud: ({ isCloud }: AppModule) => isCloud,
    enableSql: ({ enableSql }: AppModule) => enableSql,
    enableJar: ({ enableJar }: AppModule) => enableJar,
    enableDFS: ({ enableDFS }: AppModule) => enableDFS,
    monitorUrl: ({ monitorUrl }: AppModule) => monitorUrl,
    isCollapse: ({ isCollapse }: AppModule) => isCollapse,
    encryptEnable: ({ encryptEnable }: AppModule) => encryptEnable,
    projectLevelsNumber: ({ projectLevelsNumber }: AppModule) => projectLevelsNumber,
    encrypt: (state: AppModule) => (text: string) => encode(text, state),
    decrypt: (state: AppModule) => (text: string) => decode(text, state),
    lineageDimensionSplit: ({ lineageDimensionSplit }: AppModule) => lineageDimensionSplit,
    i18nMap: ({ i18nMap }: AppModule) => i18nMap
  }
};
