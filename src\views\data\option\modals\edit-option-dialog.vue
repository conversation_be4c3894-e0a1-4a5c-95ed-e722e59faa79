<template>
  <bs-dialog
    title="新建选项"
    :visible.sync="visible"
    :before-close="closeDialog"
    confirm-loading
    @close="closeDialog(false)"
    @confirm="submit"
  >
    <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="选项名称" prop="itemName">
        <el-input
          v-model="formData.itemName"
          placeholder="请输入名称"
          :value="formData.itemName"
          maxlength="30"
          show-word-limit
          @input="(e) => (formData.itemName = e.replace(/[^\w\d]/g, ''))"
        />
      </el-form-item>
      <el-form-item label="选项类型" prop="itemType">
        <el-select v-model="formData.itemType" placeholder="请选择选项类型" style="width: 100%">
          <el-option
            v-for="item in typeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getItemTypes, addItem } from '@/apis/dataApi';
@Component
export default class OptionAdd extends Vue {
  @Prop({ default: false }) visible!: boolean;
  created() {
    // 获取选项类型
    this.getTypeList();
  }
  typeList: any = [];
  formData: any = {
    itemType: '',
    itemName: ''
  };
  // 表单校验规则
  rules: any = {
    itemType: [{ required: true, message: '请选择选项类型', trigger: 'blur' }],
    itemName: [{ required: true, validator: this.validatePass, trigger: 'blur' }]
  };
  validatePass(rule, value, callback) {
    const re = /^[1-9]+[0-9]*]*$/;
    if (value === '') {
      callback(new Error('请输入名称'));
    } else if (value !== '' && re.test(value[0])) {
      callback(new Error('不能以数字开头'));
    } else {
      callback();
    }
  }
  closeDialog(needFresh = false) {
    this.$emit('close', needFresh);
  }
  submit(done: any) {
    const form: any = this.$refs['ruleForm'];
    form.validate(async (valid: any) => {
      if (valid) {
        const { success, msg = '' } = await addItem(this.formData);
        if (success) {
          this.$emit('close', true);
        } else if (!success && msg) {
          this.$message.error(msg);
        }
        done();
      } else {
        done();
      }
    });
  }
  async getTypeList() {
    this.typeList = [];
    const { data = [] } = await getItemTypes();
    this.typeList = data.map((item) => {
      return {
        value: item.itemType,
        label: item.itemTypeName
      };
    });
  }
}
</script>

<style scoped></style>
