import i18n from '@/i18n';
import { FormData } from './type';

export const generateFormData: () => FormData = () => ({
  computationalEngine: '',
  serviceAddress: '',
  serviceId: '',
  namespace: '',
  dimensionality: '',
  dimensionalValue: '',
  deadline: '',
  unit: i18n.t('pa.flow.s') as string,
  indicatorConfiguration: [],
  batchSize: 100,
  timeout: 1,
  pushFields: '',
  keyByFields: [],
  resultFields: '',
  scriptList: [],
  pushOrQueryKey: ''
});
export const getArray = (data, defaultValue = []) => (Array.isArray(data) ? data : defaultValue);

export const WAY_OPTIONDS = [
  {
    label: i18n.t('pa.flow.waykey1'),
    value: 'PUSH_THEN_QUERY'
  },
  {
    label: i18n.t('pa.flow.waykey2'),
    value: 'QUERY_THEN_PUSH'
  },
  {
    label: i18n.t('pa.flow.waykey3'),
    value: 'QUERY_ONLY'
  },
  {
    label: i18n.t('pa.flow.waykey4'),
    value: 'PUSH_ONLY'
  }
];

export const PUSH_ARRAY = ['PUSH_THEN_QUERY', 'QUERY_THEN_PUSH', 'PUSH_ONLY'];
export const QUERY_ARRAY = ['PUSH_THEN_QUERY', 'QUERY_THEN_PUSH', 'QUERY_ONLY'];
export const extraction = (value: string, list: any[]): boolean => {
  return !!list.find((el) => el.value === value);
};
export const getScriptOptions = (data) => {
  const result: any[] = [];
  Object.keys(data).forEach((el) => {
    Object.keys(data[el]).forEach((key) => {
      if (Array.isArray(data[el][key]) && Boolean(data[el][key].length)) {
        result.push(...data[el][key]);
      }
    });
  });
  return result.filter(Boolean);
};
