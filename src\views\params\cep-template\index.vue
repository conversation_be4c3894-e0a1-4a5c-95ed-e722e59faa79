<template>
  <pro-page title="CEP模板" fixed-header class="cep">
    <!-- 头部搜索、操作项 -->
    <div slot="operation">
      <bs-search
        v-model="searchObj.search"
        size="small"
        class="cep__header--input"
        placeholder="请输入模板名称"
        @input="debounceSearch"
      />
      <el-button size="small" type="primary" @click="add()"> 新建 </el-button>
    </div>
    <!-- CEP模板列表 -->
    <pro-table
      ref="proTable"
      :columns="columnData"
      :request="request"
      :options="{ height: '100vh - 175px' }"
      :selection="true"
      :actions="actions"
      row-key="id"
      @selection-change="handleSelectionChange"
      @row-dblclick="view"
      @action-click="handleClick"
    >
      <el-button slot="headerOperator" size="small" @click="delCeps()"> 删除 </el-button>
    </pro-table>
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue, Ref } from 'vue-property-decorator';
import { debounce } from 'lodash';
import { deleteCeps, getCeps, canUpdate } from '@/apis/cep-api';
import { dateFormat } from '@/utils/format';
import CommonDelete from '@/utils/mixins/common-delete';

@Component({
  name: 'TemplateCep',
  mixins: [CommonDelete]
})
export default class TemplateCep extends Vue {
  @Ref('proTable') readonly proTable!: any;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: { updateTime: 'DESC' }
  };
  columnData: any = [];

  actions = [
    { label: '编辑', value: 'edit', icon: 'iconfont icon-bianji' },
    { label: '查看', value: 'view', icon: 'iconfont icon-chakan' },
    { label: '删除', value: 'delCeps', icon: 'iconfont icon-shanchu' }
  ];

  selectedList: any = [];

  debounceSearch = debounce(this.search, 500);

  search() {
    this.proTable.loadDataAndReset();
  }

  // 查询列表
  async request({ page }: { page: { currentPage: number; pageSize: number } }) {
    this.searchObj.pageData.currentPage = page.currentPage;
    this.searchObj.pageData.pageSize = page.pageSize;
    const {
      data: { columnData, pageData, tableData }
    } = await getCeps(this.searchObj);
    (tableData || []).forEach((item) => {
      item.createTime = dateFormat(item.createTime);
      item.updateTime = dateFormat(item.updateTime);
    });
    this.columnData = columnData;
    return {
      data: tableData,
      total: pageData.total
    };
  }

  // 新建模板
  add() {
    this.$router.push({
      name: 'templateCepAddEdit'
    });
  }

  handleClick(val: 'edit' | 'view' | 'delete', data?: any) {
    this[val](data.row);
  }

  async edit(row: any) {
    const { success, msg } = await canUpdate(row.id);
    if (success) {
      this.$router.push({
        name: 'templateCepAddEdit',
        query: {
          id: row.id,
          title: row.cepName
        }
      });
    } else {
      this.$message.error(msg);
    }
  }

  view(row: any) {
    this.$router.push({
      name: 'templateCepDetail',
      query: {
        id: row.id,
        title: row.cepName
      }
    });
  }

  async delCeps(row: any) {
    const ids = !row
      ? this.selectedList
      : {
          id: row.id,
          name: row.cepName,
          reference: row.refCount || 0
        };
    this['commonDel'](ids, async (delIds) => {
      const { success, msg } = await deleteCeps(delIds);
      if (success) {
        this.proTable.loadDataAndReset();
        this.$message.success(msg);
      }
    });
  }

  handleSelectionChange(selection) {
    this.selectedList = selection.map((item) => ({
      id: item.id,
      name: item.cepName,
      reference: item.refCount || 0
    }));
  }
  activated() {
    this.proTable.loadDataAndReset();
  }
}
</script>

<style lang="scss" scoped>
.cep {
  &__header {
    &--input {
      margin-right: 20px;
    }
  }
}
.event {
  &-container {
    position: relative;
    width: 100%;
    height: 100%;
    //overflow: auto;
  }
}
</style>
