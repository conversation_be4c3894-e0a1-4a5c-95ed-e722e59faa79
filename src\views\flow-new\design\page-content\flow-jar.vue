<template>
  <div class="flow-jar">
    <el-form ref="flowJarForm" :model="jarData" :rules="formRules" :label-width="isEn ? '150px' : '105px'">
      <el-form-item :label="$t('pa.flow.name')" prop="jobName">
        <span class="flow-jar__name">{{ jarData.jobName }}</span>
      </el-form-item>
      <el-form-item :label="$t('pa.flow.category')" prop="jobType">{{ jarData.jobType }}</el-form-item>
      <el-form-item :label="$t('pa.flow.jarPkg')" prop="flinkJarFile">
        <div class="flow-jar__file">
          <!-- 回显文件路径 -->
          <el-link
            v-if="jarData.flinkJarFile"
            style="poistion: relative"
            class="flow-jar__path"
            :underline="false"
            type="primary"
          >
            {{ jarData.flinkJarFile }}
          </el-link>
          <bs-upload
            v-if="!fromMonitorFlow && !disabled"
            ref="uploader"
            action
            accept=".jar"
            :limit="1"
            :auto-upload="false"
            :upload-text="jarData.flinkJarFile ? this.$t('pa.flow.reload') : this.$t('pa.flow.selectFile')"
            :on-change="handleFileChange"
            :file-list="fileList"
            :before-remove="handleFileRemove"
            :on-exceed="handleFileUploadExceed"
            :tip-text="$t('pa.flow.msg267')"
          />
          <div
            v-if="hiddenButtonVisible"
            ref="hiddenButton"
            :class="{ 'bs-upload-mask': true, 'is-new': !Boolean(jarData.flinkJarFile) }"
            @click="handleUploadClick"
          ></div>
        </div>
      </el-form-item>
      <el-form-item :label="$t('pa.flow.className')" prop="entryClass">
        <el-input
          v-model="jarData.entryClass"
          class="flow-jar__input"
          :placeholder="$t('pa.placeholder.input')"
          clearable
          :disabled="fromMonitorFlow || disabled"
          :maxlength="256"
          @input="handleInput"
        />
      </el-form-item>
    </el-form>
  </div>
</template>
<script lang="ts">
import { BsUpload } from 'bs-ui-pro';
import { ElForm } from 'bs-ui-pro/types/form';
import { cloneDeep } from 'lodash';
import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator';
import ClearEnter from '@/common/clear-enter';
import './style/flow-jar.scss';
import i18n from '@/i18n';

const JOB_TYPE_ENUMS = {
  PROCESSFLOW: 'DataStream',
  FLINK_SQL: 'SQL',
  UDJ: i18n.t('pa.flow.customSize')
};
@Component({
  mixins: [ClearEnter]
})
export default class FlowJar extends Vue {
  @Prop() data!: any;
  @Prop() fromMonitorFlow!: boolean;
  @Prop() disabled!: boolean;
  @Ref('flowJarForm') flowJarForm!: ElForm;
  @Ref('uploader') uploader!: BsUpload;
  jarData: any = {}; // jar流程数据
  fileList = [];
  uploadFileVisible = true;
  flinkJarFile: any = null;
  originalEntryClass = '';
  get formRules() {
    return {
      entryClass: {
        required: true,
        message: this.$t('pa.flow.msg268'),
        trigger: 'change'
      },
      flinkJarFile: {
        required: true,
        trigger: 'change',
        validator: (rule, value, cb) => {
          if (!this.flinkJarFile && !value) return cb(new Error(this.$t('pa.flow.msg269')));
          cb();
        }
      }
    };
  }

  // 是否已配置分布式文件系统
  get hiddenButtonVisible() {
    return !this.$store.getters.enableDFS && !this.disabled;
  }

  @Watch('data', { immediate: true })
  async handleDataChange(data: any) {
    await this.$nextTick();
    this.flowJarForm.clearValidate();
    this.jarData = cloneDeep(data);
    this.jarData.originalJobType = this.jarData.jobType;
    this.jarData.jobType = JOB_TYPE_ENUMS[this.jarData.jobType];
    this.originalEntryClass = this.jarData.entryClass;
    this.flinkJarFile = null;
    this.fileList = [];
  }

  async handleFormValidate() {
    await this.flowJarForm.validate();
  }

  handleInput() {
    this.$emit('content-change', this.jarData.entryClass !== this.originalEntryClass);
    this.jarData.content = JSON.stringify({
      entryClass: this.jarData.entryClass,
      ...(this.jarData.flinkJarFile && { flinkJarFile: this.jarData.flinkJarFile }),
      ...(this.jarData.flinkJarFileUpdateTime && { flinkJarFileUpdateTime: this.jarData.flinkJarFileUpdateTime })
    });
  }

  // 点击上传文件/重新上传前校验
  handleUploadClick() {
    this.$message.warning(this.$t('pa.flow.msg270'));
  }

  handleFileRemove() {
    this.flinkJarFile = null;
    this.$emit('content-change', false);
  }

  handleFileUploadExceed() {
    this.$message.warning(this.$t('pa.flow.msg271'));
  }

  handleFileChange(data) {
    const { name } = data;
    if (!name.endsWith('.jar')) {
      this.fileList = [];
      return this.$message.error(this.$t('pa.flow.msg272'));
    }
    if (data.size > Math.pow(1024, 3)) {
      this.$message.warning(this.$t('pa.flow.msg273'));
      return (this.fileList = []);
    }
    this.flinkJarFile = data.raw;
    this.$emit('content-change', true);
  }
}
</script>
<style lang="scss" scoped>
.flow-jar {
  margin: 50px 0px;
  &__input {
    width: 320px;
  }
  &__name,
  &__path {
    word-break: break-all;
  }
  &__file {
    ::v-deep .bs-upload {
      line-height: 1;
      .el-upload__tip {
        display: inline;
        margin-top: 0px;
        margin-left: 10px;
      }
    }

    .bs-upload-mask {
      position: absolute;
      left: 0;
      top: 69%;
      width: 102px;
      height: 28px;
      background: transparent;
      cursor: pointer;
      @media screen and (min-width: 1400px) {
        top: 55%;
      }
    }
    .is-new {
      top: 0;
    }
  }
}
</style>
