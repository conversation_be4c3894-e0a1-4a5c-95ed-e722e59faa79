<template>
  <bs-dialog title="引用关系" :visible.sync="visible" size="medium" :before-close="closeDialog">
    <el-form label-width="100px">
      <el-form-item label="服务名称：">
        <div class="res-label">{{ data.resTitle || '-' }}</div>
      </el-form-item>
      <el-form-item label="服务地址：">
        <div class="res-label">{{ data.address || '-' }}</div>
      </el-form-item>
      <el-form-item label="资源名称：">
        <div class="res-label">{{ data.subName || '-' }}</div>
      </el-form-item>
      <el-form-item label="引用流程：">
        <div class="res-label">{{ data.projectJobName || '-' }}</div>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">关闭</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class NodeEdit extends Vue {
  @Prop() visible!: boolean;
  @Prop() data!: any;

  closeDialog() {
    this.$emit('update:visible', false);
  }
}
</script>

<style lang="scss" scoped>
.res-label {
  text-align: left;
}
</style>
