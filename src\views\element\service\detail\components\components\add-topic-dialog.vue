<template>
  <bs-dialog :title="$t('pa.action.create')" :visible.sync="display" @confirm="handleConfirm">
    <el-form ref="formRef" :model="formData" :rules="formRule" label-width="100px">
      <el-form-item :label="$t('pa.name')" prop="topic">
        <el-input v-model="formData.topic" :placeholder="$t('pa.placeholder.search')" />
      </el-form-item>
      <el-form-item :label="$t('pa.copies')" prop="replicationFactor">
        <el-input-number v-model="formData.replicationFactor" :min="1" :placeholder="$t('pa.placeholder.search')" />
      </el-form-item>
      <el-form-item :label="$t('pa.partCount')" prop="numPartitions">
        <el-input-number v-model="formData.numPartitions" :min="1" :placeholder="$t('pa.placeholder.search')" />
      </el-form-item>
    </el-form>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, PropSync, Prop, Ref, Vue } from 'vue-property-decorator';
import { createTopic } from '@/apis/serviceApi';
import ElForm from 'bs-ui-pro/lib/form';

@Component
export default class AddTopicDialog extends Vue {
  @PropSync('show', { type: Boolean, default: true }) display!: boolean;
  @Prop({ default: '' }) resId!: string;
  @Prop({ default: '' }) resType!: string;
  @Ref('formRef') readonly form!: ElForm;

  loading = false;
  formData: any = { resId: this.resId, resType: this.resType, topic: '', replicationFactor: '', numPartitions: '' };
  formRule: any = {
    topic: [{ required: true, message: this.$t('pa.placeholder.name'), trigger: 'blur' }],
    replicationFactor: [{ required: true, message: this.$t('pa.placeholder.copies'), trigger: 'blur' }],
    numPartitions: [{ required: true, message: this.$t('pa.placeholder.partCount'), trigger: 'blur' }]
  };

  async handleConfirm() {
    await this.validate();
    try {
      this.loading = true;
      const { success, error } = await createTopic(this.formData);
      if (!success) return this.$message.error(error);
      this.$emit('refresh');
      this.display = false;
    } finally {
      this.loading = false;
    }
  }
  async validate() {
    try {
      await this.form.validate();
    } catch {
      this.$message.error(this.$t('pa.tip.checkMessage'));
      throw new Error(this.$t('pa.tip.checkMessage'));
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-input-number {
  width: 100%;
}
</style>
