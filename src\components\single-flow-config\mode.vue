<template>
  <div class="mode-config-container">
    <!-- 模式选择 -->
    <el-form-item
      prop="mode"
      :rules="rules.mode"
      class="el-collapse-item__header mode-config-choose"
    >
      <div slot="label" class="resource-title">模式选择</div>
      <el-radio-group v-model="form.mode">
        <el-radio label="stream">流模式</el-radio>
        <el-radio :disabled="!isFlinkSql" label="batch">批模式</el-radio>
      </el-radio-group>
    </el-form-item>
    <template v-for="el in renderList">
      <div v-show="!el.hidden" :key="el.name">
        <el-form-item
          v-if="el.name === 'intervalTime'"
          :label="el.label"
          class="mode-config-interval is-required"
        >
          <div class="resource-item">
            <template v-for="item in el.options">
              <el-form-item
                :key="item.name"
                :prop="item.name"
                :label="item.label"
                :rules="rules[item.name]"
              >
                <!-- number -->
                <el-input-number
                  v-if="item.type === 'number'"
                  v-model="form[item.name]"
                  number
                  :min="item.min"
                  :max="item.max"
                  :step="item.step ? item.step : 1"
                  :placeholder="item.placeholder"
                />
                <!-- 单位 -->
                <span v-if="item.unit" class="mode-config-unit">{{ item.unit }}</span>
              </el-form-item>
            </template>
          </div>
        </el-form-item>
        <el-form-item v-else :prop="el.name" :label="el.label" :rules="rules[el.name]">
          <div class="resource-item">
            <!-- select -->
            <el-select
              v-if="el.type === 'select'"
              v-model="form[el.name]"
              filterable
              clearable
              :placeholder="el.placeholder"
              @change="handleSelectChange"
            >
              <el-option
                v-for="item in el.options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <!-- time -->
            <el-time-picker
              v-if="el.type === 'time'"
              v-model="form[el.name]"
              arrow-control
              format="HH:mm"
              value-format="HH:mm"
              :placeholder="el.placeholder"
            />
            <!-- date -->
            <el-date-picker
              v-if="el.type === 'date'"
              v-model="form[el.name]"
              clearable
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :placeholder="el.placeholder"
            />
            <!--  datetime -->
            <el-date-picker
              v-if="el.type === 'datetime'"
              v-model="form[el.name]"
              clearable
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              :placeholder="el.placeholder"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
            <!-- input -->
            <el-input
              v-if="el.type === 'input'"
              v-model="form[el.name]"
              clearable
              :placeholder="el.placeholder"
            />
          </div>
        </el-form-item>
      </div>
    </template>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue, Watch } from 'vue-property-decorator';
import { parseExpression } from 'cron-parser';

@Component
export default class ModeConfig extends Vue {
  @Prop({ type: Boolean, default: false }) isFlinkSql!: boolean;
  @PropSync('data', { type: Object, default: () => ({}) }) form!: any;
  private rules = {
    mode: { required: true, message: '请选择作业模式', trigger: 'blur' },
    jobRunningRule: { required: false, message: '请选择任务运行规则', trigger: 'change' },
    intervalHour: { required: false, message: '请输入小时', trigger: 'blur' },
    intervalMinute: { required: false, message: '请输入分钟', trigger: 'blur' },
    repeatTime: { required: false, message: '请选择重复时间', trigger: 'blur' },
    startDate: { required: false, message: '请选择上线日期', trigger: 'blur' },
    startTime: {
      required: false,
      validator({ required = false }, value: any, callback) {
        if (!required) {
          callback();
          return;
        }
        if (!value) {
          callback(new Error('请选择上线时间'));
          return;
        }
        if (new Date(value).getTime() <= Date.now()) {
          callback(new Error('请选择大于当前时间的上线时间'));
          return;
        }
        callback();
      },
      trigger: 'blur'
    },
    cron: {
      required: false,
      validator({ required = false }, value: any, callback) {
        if (!required) {
          callback();
          return;
        }
        if (!value) {
          callback(new Error('请再次输入正确cron表达式'));
          return;
        }
        try {
          parseExpression(value);
          callback();
        } catch (e) {
          callback(new Error('请再次输入正确cron表达式'));
        }
      },
      trigger: 'blur'
    }
  };
  private pickerOptions = {
    disabledDate(time) {
      return time.getTime() < Date.now() - 8.64e7;
    }
  };
  private renderList: any[] = [
    {
      name: 'jobRunningRule',
      label: '任务运行规则',
      placeholder: '请选择任务运行规则',
      type: 'select',
      options: [
        {
          label: '一次性',
          value: 'once'
        },
        {
          label: '间隔周期',
          value: 'intervalPeriod'
        },
        {
          label: '时间周期',
          value: 'timePeriod'
        },
        {
          label: '自定义',
          value: 'prescribed'
        }
      ],
      hidden: true
    },
    {
      name: 'intervalTime',
      label: '间隔时间',
      options: [
        {
          name: 'intervalHour',
          placeholder: '请输入小时',
          type: 'number',
          min: 0,
          max: 100,
          unit: '时'
        },
        {
          name: 'intervalMinute',
          placeholder: '请输入分钟',
          type: 'number',
          min: 0,
          max: 100,
          unit: '分'
        }
      ],
      hidden: true
    },
    {
      name: 'repeatTime',
      label: '重复时间',
      placeholder: '请选择重复时间',
      type: 'time',
      hidden: true
    },
    {
      name: 'startDate',
      label: '上线日期',
      placeholder: '请选择上线日期',
      type: 'date',
      hidden: true
    },
    {
      name: 'startTime',
      label: '上线时间',
      placeholder: '请选择上线时间',
      type: 'datetime',
      hidden: true
    },
    {
      name: 'cron',
      label: 'cron表达式',
      placeholder: '请输cron表达式',
      type: 'input',
      hidden: true
    }
  ]; // 渲染列表

  @Watch('form', { deep: true, immediate: true })
  async handleFormChange({ mode, jobRunningRule }: any) {
    this.setRenderListValue('jobRunningRule', 'hidden', mode !== 'batch');
    this.setRulesValue('jobRunningRule', mode === 'batch');
    this.handleJobRunningRule(jobRunningRule, mode);
  }

  handleJobRunningRule(rule: string, mode: string) {
    const isStream = mode === 'stream';
    if (isStream) {
      this.$set(this.form, 'jobRunningRule', '');
      this.handleSelectChange();
    }
    const arr = [
      { name: 'startTime', showRule: ['once', 'intervalPeriod'], validateRule: ['startTime'] },
      {
        name: 'intervalTime',
        showRule: ['intervalPeriod'],
        validateRule: ['intervalHour', 'intervalMinute']
      },
      { name: 'repeatTime', showRule: ['timePeriod'], validateRule: ['repeatTime'] },
      { name: 'startDate', showRule: ['timePeriod'], validateRule: ['startDate'] },
      { name: 'cron', showRule: ['prescribed'], validateRule: ['cron'] }
    ];

    arr.forEach(({ name, showRule, validateRule }) => {
      const result = isStream ? false : showRule.includes(rule);
      this.setRenderListValue(name, 'hidden', !result);
      validateRule.forEach((el) => this.setRulesValue(el, result));
    });
  }

  setRenderListValue(name: string, key: string, value: any) {
    const index = this.renderList.findIndex((el) => el.name === name);
    if (index > -1) {
      this.$set(this.renderList[index], key, value);
    }
  }

  setRulesValue(name: string, value: any, key = 'required') {
    if (name in this.rules) {
      this.$set(this.rules[name], key, value);
    }
  }

  handleSelectChange() {
    const keyList = ['startTime', 'intervalHour', 'intervalMinute', 'repeatTime', 'cron'];
    keyList.forEach((key: string) => {
      this.$set(this.form, key, key.includes('interval') ? 0 : '');
    });
  }
}
</script>
<style lang="scss" scoped>
.mode-config {
  &-container {
  }

  /* 模式选择 */
  &-choose {
    padding: 5px 0;

    ::v-deep .el-form-item {
      &__label {
        text-align: left;
        &::before {
          display: none;
        }
      }

      &__content {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        width: 100%;
      }

      &__error {
        left: unset;
        right: 0;
      }
    }
  }

  /* 间隔时间 */
  &-interval {
    ::v-deep .el-form-item__content {
      display: flex;
      justify-content: space-between;
      align-content: center;

      &::before,
      &::after {
        display: none;
      }

      .el-form-item {
        display: flex;
        align-items: center;
        width: 46%;

        .el-form-item__content {
          justify-content: flex-end;
          align-items: center;

          .el-input-number {
            margin: 0 10px 0 0;
            width: calc(100% - 20px);
          }
        }
      }
    }
    ::v-deep .el-form-item__content {
      display: flex;
      justify-content: space-between;
      align-content: center;

      &::before,
      &::after {
        display: none;
      }

      .el-form-item {
        display: flex;
        align-items: center;
        width: 46%;

        .el-form-item__content {
          justify-content: flex-end;
          align-items: center;

          .el-input-number {
            margin: 0 10px 0 0;
            width: calc(100% - 20px);
          }
        }
      }
    }

    &__el {
      display: flex;
      align-items: center;
      width: 48%;
    }
  }

  &-unit {
    display: inline-block;
    margin: 0 0 0 10px;
    width: 26px;
  }
}
</style>
