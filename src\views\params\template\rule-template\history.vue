<template>
  <pro-page :title="$t('pa.params.template.historicalRouteTemplate')" class="rule-history" :fixed-header="false">
    <pro-table v-loading="tableLoading" :columns="columnData" :request="request" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { post } from '@/apis/utils/net';

@Component({
  name: 'ElementRuleTemplate'
})
export default class ElementRuleTemplate extends Vue {
  tableLoading = false;
  columnData = [];

  get id() {
    return this.$route.query.id as string;
  }

  async request(page) {
    this.tableLoading = true;
    try {
      const { data, success, msg } = await post(`/rs/pa/route/versionList?id=${this.id}`, {
        pageData: page.page
      });
      if (success) {
        this.tableLoading = false;
        data.columnData.forEach((el) => {
          el.value = el.prop;
          if (el.value === 'updateTime') el.valueProps = { type: 'datetime' };
        });
        this.columnData = data.columnData;
        return { data: data.tableData, total: data.pageData.total };
      } else {
        this.tableLoading = false;
        this.$message.error(msg);
      }
    } catch {
      this.tableLoading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.rule {
  height: calc(100vh - 107px);
  &-header {
    height: 50px;
    background: #ffffff;
    border-left: none;
    font-size: 16px;
    display: flex;
    align-items: center;
    padding: 0 20px;
  }
  &-header-operate {
    flex: 1;
    text-align: right;
  }
  &-content {
    height: calc(100% - 50px);
    padding-bottom: 20px;
    overflow: hidden;
    background: #fff;
  }
}
</style>
