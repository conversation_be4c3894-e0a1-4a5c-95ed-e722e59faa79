import Vue from 'vue';
import VueI18n from 'vue-i18n';
import cookie from 'js-cookie';
import BSCnLocale from 'bs-ui-pro/lib/locale/lang/zh-CN';
import BSHkLocale from 'bs-ui-pro/lib/locale/lang/zh-HK';
import BSEnLocale from 'bs-ui-pro/lib/locale/lang/en';
import local from 'bs-ui-pro/lib/locale';
import zhCn from './lang/zh-CN.json';
import zhHk from './lang/zh-HK.json';
import enUS from './lang/en-US.json';
export const getLanguage = () => cookie.get('Language') || 'zh-CN';
export const setLanguage = (value: string) => cookie.set('Language', value);
Vue.use(VueI18n);

const i18n = new VueI18n({
  locale: getLanguage(),
  messages: {
    'zh-CN': Object.assign(zhCn, BSCnLocale),
    'zh-HK': Object.assign(zhHk, BSHkLocale),
    'en-US': Object.assign(enUS, BSEnLocale)
  },
  silentTranslationWarn: true
});
local.i18n((key, value) => i18n.t(key, value));
export default i18n;
