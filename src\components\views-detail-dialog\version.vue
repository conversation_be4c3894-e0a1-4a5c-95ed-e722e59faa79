<template>
  <div style="height: 100%">
    <base-table
      v-loading="loading"
      :table-data="tableData"
      :table-config="tableConfig"
      @handleCurrentChange="handleCurrentChange"
      @handleSortChange="handleSortChange"
    />
    <preview
      v-if="dialogVisible"
      ref="preview"
      :title="'源码查看'"
      :code="sourceCode"
      :visible.sync="dialogVisible"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import {
  URL_TABLEHIS,
  URL_TABLEHIS_ROLLLBACK,
  URL_VIEWHIS,
  URL_UDF_UDFHIS
} from '@/apis/commonApi';
import { isEmpty, toString } from 'lodash';
import { get, post, put } from '@/apis/utils/net';
import { PaBase } from '@/common/pipeace-base';
import Preview from './flink-sql.vue';
import BaseTable from '@/components/base-table.vue';

@Component({
  components: { Preview, BaseTable }
})
export default class Version extends PaBase {
  @Prop({ default: '' }) type!: string;
  @Prop({ default: '' }) id!: string;
  private loading = false;
  private sourceCode: any = {};
  private dialogVisible = false;
  private tableData: ITableData = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  private tableConfig: ITableConfig = {
    width: 150,
    columnsExtend: {
      edit: [
        {
          tipMessage: '查看源码',
          handler: this.showCode.bind(this),
          iconfont: 'icon-ziyuan'
        }
      ]
    }
  };
  private params = {
    pageData: {
      pageSize: 20,
      currentPage: 1,
      total: 1
    },
    search: '',
    sortData: {}
  };
  private url = '';

  created() {
    const { id, type } = this;
    if (this.type === 'UDF') {
      this.url = URL_UDF_UDFHIS;
      (this.tableConfig as any).columnsExtend.edit = [];
    } else {
      this.url = type === 'TABLE' ? URL_TABLEHIS : URL_VIEWHIS;
    }
    this.params.search = id;
    const key = `${type.toLowerCase()}Version`;
    this.params.sortData = { [key]: 'DESC' };
    this.getVersionList();
  }

  async getVersionList() {
    this.loading = true;
    try {
      const { success, data, error }: any = await post(this.url, this.params);
      if (success) {
        this.tableData = { ...data };
        this.loading = false;
        return;
      }
      this.loading = false;
      this.$message.error(error);
    } catch (e) {
      this.loading = false;
    }
  }

  async showCode({ id }: any) {
    this.loading = true;
    try {
      const url = `/rs/pa/sql/${this.type.toLowerCase()}His/sql/sourceCode?resId=${id}`;
      const { success, data, error }: any = await get(url);
      if (success) {
        this.sourceCode = data || '';
        this.dialogVisible = true;
        this.loading = false;
        return;
      }
      this.loading = false;
      this.$message.error(error);
    } catch (e) {
      this.loading = false;
    }
  }

  async rollBack({ id: tableHisId }: any) {
    try {
      await this.$confirm('是否确认回滚?', '提示', { type: 'warning' });
      this.loading = true;
      const { success, error }: any = await put(URL_TABLEHIS_ROLLLBACK, {
        tableHisId
      });
      if (success) {
        this.getVersionList();
        this.loading = false;
        return;
      }
      this.loading = false;
      this.$message.error(error);
    } catch (e) {
      this.loading = false;
    }
  }

  handleCurrentChange(val) {
    this.params.pageData.currentPage = val;
    this.getVersionList();
  }

  hasAuthority(hasRole: any, { dataLevelType }: any) {
    return !isEmpty(toString(dataLevelType)) && dataLevelType !== 'PARENT' && hasRole;
  }

  handleSortChange(val) {
    this.params.sortData = val;
    this.getVersionList();
  }
}
</script>
