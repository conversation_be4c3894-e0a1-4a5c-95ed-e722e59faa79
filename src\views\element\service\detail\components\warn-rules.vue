<template>
  <pro-grid type="info" :title="$t('pa.warnRule')">
    <!-- operation -->
    <el-button slot="operation" type="primary" @click="getWarnRuleList(true)">{{ $t('pa.action.refresh') }}</el-button>
    <!-- main -->
    <bs-table
      v-loading="loading"
      th-wrap
      :data="tableData"
      :height="height"
      :column-settings="false"
      :column-data="columnData"
    >
      <!-- 状态 -->
      <template slot="stateType" slot-scope="{ row }">
        {{ getStateLabel(row.stateType) }}
      </template>
      <!-- 操作栏 -->
      <template slot="operator" slot-scope="{ row }">
        <el-tooltip v-for="it in operatorList" :key="it.label" :content="it.label" effect="light">
          <i :class="it.icon" @click="it.handler(row)"></i>
        </el-tooltip>
      </template>
    </bs-table>
    <!-- 编辑  -->
    <warn-rule-edit-dialog
      v-if="showEditDialog"
      :id="curRecordId"
      :org-id="curRecordOrgId"
      :show.sync="showEditDialog"
      @close="getWarnRuleList"
    />
    <!-- 详情 -->
    <warn-record-dialog
      v-if="showRecordDialog"
      :show.sync="showRecordDialog"
      :data="detailData"
      @refresh="getWarnRuleList"
    />
  </pro-grid>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getWarnRules, updateWarnRuleState } from '@/apis/serviceApi';
import { safeArray, timeFormat, hasPermission } from '@/utils';
import { STATE_TYPE } from '../utils';

const EN_COLUMN_WIDTH_MAP = {
  recordTotalCount: 160,
  recordNoReadCount: 180,
  cron: 160,
  effectiveStartTime: 200,
  effectiveEndTime: 200,
  sendEnable: 200
};

@Component({
  components: {
    WarnRuleEditDialog: () => import('@/views/monitor/components/warn-rule-edit-dialog.vue'),
    WarnRecordDialog: () => import('@/views/monitor/components/warn_record_dialog.vue')
  }
})
export default class WarnRules extends Vue {
  @Prop({ default: '' }) flowId!: string;
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: () => ({}) }) params!: any;
  @Prop({ default: false }) shareFlag!: boolean;

  loading = false;
  columnData: any[] = [];
  tableData: any[] = [];
  showEditDialog = false;
  curRecordId = '';
  curRecordOrgId = '';
  showRecordDialog = false;
  detailData: any = {};

  get height() {
    return this.flowId ? 'calc(100vh - 181px)' : this.params?.height || '300px';
  }
  get operatorList() {
    return [
      !this.shareFlag &&
        hasPermission('PA.MONITOR.WARN.EDIT') && {
          label: this.$t('pa.action.edit'),
          icon: 'iconfont icon-bianji',
          handler: (row) => this.edit(row)
        },
      hasPermission('PA.MONITOR.WARN.RECORD_LIST') && {
        label: this.$t('pa.action.detail'),
        icon: 'iconfont icon-chakan',
        handler: (row) => this.detail(row)
      },
      !this.shareFlag &&
        hasPermission('PA.MONITOR.WARN.ON') && {
          label: this.$t('pa.action.enable'),
          icon: 'iconfont icon-qidong',
          handler: (row) => this.updateState(row, 'ON')
        },
      !this.shareFlag &&
        hasPermission('PA.MONITOR.WARN.OFF') && {
          label: this.$t('pa.action.close'),
          icon: 'iconfont icon-guanbi',
          handler: (row) => this.updateState(row, 'OFF')
        }
    ].filter(Boolean);
  }

  created() {
    this.getWarnRuleList();
  }
  /* 获取预警规则 */
  async getWarnRuleList(isRefresh = false) {
    try {
      const id = this.flowId || (this.$route.query.id as string);
      if (!id) return;
      this.loading = true;
      const { success, data, error } = await getWarnRules(id);
      if (!success) return this.$message.error(error);
      this.columnData = safeArray(data?.columnData).map((it) => {
        it.value = it.prop;
        this.isEn && (it.width = EN_COLUMN_WIDTH_MAP[it.value] || it.width);
        return it;
      });
      this.columnData.push({ label: this.$t('pa.action.action'), value: 'operator', width: 150, fixed: 'right' });
      this.tableData = safeArray(data?.tableData).map((it) => {
        it.updateTime = timeFormat(it.updateTime);
        it.sendEnable = this.columnData.filter((el) => el.prop === 'sendEnable')[0].enumData[it.sendEnable];
        return it;
      });
      isRefresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    } finally {
      this.loading = false;
    }
  }
  getStateLabel(state: string) {
    return STATE_TYPE[state] || '';
  }
  /* 编辑 */
  async edit({ id, orgId }: any) {
    this.curRecordId = id;
    this.curRecordOrgId = orgId;
    this.showEditDialog = true;
  }
  /* 查看详情 */
  detail(row: any) {
    this.showRecordDialog = true;
    this.detailData = row;
  }
  /* 状态更新 */
  async updateState(row: any, stateType: string) {
    try {
      this.loading = true;
      const { success, error } = await updateWarnRuleState(row.id, stateType);
      if (!success) return this.$message.error(error);
      this.$set(row, 'stateType', stateType);
    } finally {
      this.loading = false;
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .bs-table {
  width: 100%;
}
.iconfont {
  margin-right: 10px;
  cursor: pointer;
}
</style>
