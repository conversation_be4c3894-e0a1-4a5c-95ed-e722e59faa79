<template>
  <bs-dialog
    :title="$t('pa.data.option.addOption')"
    :visible.sync="visible"
    :before-close="closeDialog"
    confirm-loading
    @close="closeDialog(false)"
    @confirm="submit"
  >
    <el-form ref="ruleForm" :model="formData" :rules="rules" :label-width="isEn ? '120px' : '100px'">
      <el-form-item :label="$t('pa.data.option.optionName')" prop="itemName">
        <el-input
          v-model="formData.itemName"
          :placeholder="$t('pa.placeholder.name')"
          :value="formData.itemName"
          maxlength="30"
          show-word-limit
          @input="(e) => (formData.itemName = e.replace(/[^\w\d]/g, ''))"
        />
      </el-form-item>
      <el-form-item :label="$t('pa.data.option.optionType')" prop="itemType">
        <el-select
          v-model="formData.itemType"
          :placeholder="$t('pa.data.option.placeholder.optionTypePlaceholder')"
          style="width: 100%"
        >
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getItemTypes, addItem } from '@/apis/dataApi';
@Component
export default class OptionAdd extends Vue {
  @Prop({ default: false }) visible!: boolean;

  typeList: any = [];
  formData: any = {
    itemType: '',
    itemName: ''
  };
  // 表单校验规则
  rules: any = {
    itemType: [{ required: true, message: this.$t('pa.data.option.placeholder.optionTypePlaceholder'), trigger: 'blur' }],
    itemName: [{ required: true, validator: this.validatePass, trigger: 'blur' }]
  };

  created() {
    this.getTypeList();
  }

  async getTypeList() {
    this.typeList = [];
    const { success, data, msg } = await getItemTypes();
    if (!success) return this.$message.error(msg);
    const newArr = Array.isArray(data) ? data : [];
    this.typeList = newArr.map(({ itemType: value, itemTypeName: label }) => ({ value, label }));
  }

  validatePass(rule, value, callback) {
    const re = /^[1-9]+[0-9]*]*$/;
    if (value === '') {
      callback(new Error(this.$t('pa.placeholder.name')));
    } else if (value !== '' && re.test(value[0])) {
      callback(new Error(this.$t('pa.data.option.validateTip')));
    } else {
      callback();
    }
  }
  closeDialog(needFresh = false) {
    this.$emit('close', needFresh);
  }
  submit(done: any) {
    const form: any = this.$refs['ruleForm'];
    form.validate(async (valid: any) => {
      if (valid) {
        const { success, msg = '' } = await addItem(this.formData);
        if (success) {
          this.$emit('close', true);
        } else if (!success && msg) {
          this.$message.error(msg);
        }
        done();
      } else {
        done();
      }
    });
  }
}
</script>
