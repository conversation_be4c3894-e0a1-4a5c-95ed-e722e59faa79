<template>
  <pro-grid v-loading="loading" type="info" :title="title">
    <!-- main -->
    <div class="query-block-body">
      <!-- form -->
      <el-form ref="formRef" :model="formData" :rules="formRule" :label-width="labelWidth" class="query-block-form">
        <el-form-item v-for="it in formItem" :key="it.value" :label="`${it.label}:`" :prop="it.value">
          <!-- input -->
          <el-input v-if="it.type === 'input'" v-model="formData[it.value]" v-bind="it.props" />
          <!-- number -->
          <el-input-number v-if="it.type === 'number'" v-model="formData[it.value]" v-bind="it.props" />
        </el-form-item>
        <!--  submit-->
        <div class="query-block-form__submit">
          <el-button type="primary" @click="handleSubmit">{{ $t('pa.action.search') }}</el-button>
        </div>
      </el-form>
      <!--  result-->
      <div class="query-block-result">
        <span>{{ $t('pa.result') }}</span>
        <span>:</span>
        <el-tooltip effect="light" placement="right-end">
          <div slot="content">{{ result }}</div>
          <div class="query-block-result__content">{{ result }}</div>
        </el-tooltip>
      </div>
    </div>
  </pro-grid>
</template>
<script lang="ts">
import { Component, Prop, Ref, Vue } from 'vue-property-decorator';
import ElForm from 'bs-ui-pro/packages/form/index.js';

@Component
export default class QueryBlock extends Vue {
  @Prop({ default: '' }) title!: string;
  @Prop({ default: '' }) result!: string;
  @Prop({ default: false }) loading!: boolean;
  @Prop({ default: () => ({}) }) formRule!: any;
  @Prop({ default: () => ({}) }) formData!: any;
  @Prop({ default: () => [] }) formItem!: any[];
  @Prop({ default: '200px' }) labelWidth!: string;
  @Ref('formRef') readonly form!: ElForm;

  async handleSubmit() {
    try {
      await this.form.validate();
      this.$emit('submit');
    } catch {
      this.$message.error(this.$t('pa.tip.checkMessage'));
      throw new Error(this.$t('pa.tip.checkMessage'));
    }
  }
}
</script>
<style lang="scss" scoped>
.query-block {
  &-body {
    display: flex;
    width: 100%;
    height: 100%;
  }
  &-form {
    flex: 1;
    width: 0;
    height: 100%;
    ::v-deep .el-input-number {
      width: 100%;
    }
    &__submit {
      padding-bottom: 10px;
      text-align: right;
    }
  }
  &-result {
    padding: 10px 20px;
    flex: 2;
    width: 0;
    height: 100%;
    box-sizing: border-box;
    &__content {
      padding: 0px 30px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 7;
      max-height: 12em;
    }
  }
}
</style>
