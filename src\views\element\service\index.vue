<template>
  <pro-page v-loading="loading" :title="$t('pa.serviceMgr')" :fixed-header="false">
    <bs-split style="height: 100%" :size="size" :min="min" :max="max" :disabled="!isCollapse" :on-drag-end="handleDragEnd">
      <!-- left -->
      <div ref="divRef" slot="left" class="service-mgr-left">
        <!-- header -->
        <div class="service-mgr-header">
          <span class="service-mgr-header__text">{{ $t('pa.serviceType') }}</span>
          <el-button type="text" icon="el-icon-arrow-left" class="service-mgr-button" @click="isCollapse = !isCollapse" />
        </div>
        <!-- body -->
        <div class="service-mgr-left__body">
          <li
            v-for="it in serviceList"
            :id="it.type"
            :key="it.label"
            :title="it.label"
            :class="['service-mgr-item', activeLabel === it.label ? 'active' : '']"
            @click="setActiveData(it)"
          >
            <div class="service-mgr-item__icon" v-html="it.icon"></div>
            <div class="service-mgr-item__some">
              <div class="service-mgr-item__label">{{ it.label }}</div>
              <span>({{ it.count }})</span>
            </div>
          </li>
        </div>
      </div>
      <!-- right -->
      <service-table
        ref="serviceTableRef"
        slot="right"
        :is-collapse.sync="isCollapse"
        :title="activeLabel"
        :res-type="activeType"
        @refresh="getServiceList(true)"
      />
    </bs-split>
  </pro-page>
</template>
<script lang="ts">
import { Component, Vue, Ref, Inject } from 'vue-property-decorator';
import { getServiceConf } from '@/apis/serviceApi';
import ServiceTable from './service-table.vue';
import { safeArray } from '@/utils';
import { getIconByType } from './utils';

@Component({ components: { ServiceTable } })
export default class Service extends Vue {
  @Inject('enableSql') enableSql;
  @Ref('serviceTableRef') readonly serviceTable!: ServiceTable;
  @Ref('divRef') readonly div!: HTMLDivElement;

  loading = false;
  activeLabel = '';
  activeType = '';
  isCollapse = true;
  serviceList: any[] = [];
  curSize = 0;
  curType = '';
  saveType = '';

  get size() {
    return this.isCollapse ? this.curSize || 260 : 0;
  }
  get min() {
    return this.isCollapse ? 200 : 0;
  }
  get max() {
    return this.isCollapse ? 400 : 0;
  }

  created() {
    this.getServiceList();
  }
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (to.params.type) vm.curType = to.params.type;
      if (vm.serviceList.length) {
        vm.setActiveData();
      }
    });
  }

  /* 获取服务列表 */
  async getServiceList(isSave = false) {
    try {
      this.loading = true;
      isSave && (this.curType = this.activeType);
      const { success, data, error } = await getServiceConf();
      if (!success) return this.$message.error(error);
      this.serviceList = safeArray(data)
        .map((it) => {
          if (!this.enableSql && ['daemon', 'dts'].includes(it.label)) return null;
          return {
            label: it.label,
            type: it.type,
            count: it.count,
            icon: it.iconByte ? getIconByType(it.iconByte, it.type, 24) : null
          };
        })
        .filter(Boolean);
      this.setActiveData();
    } finally {
      this.loading = false;
    }
  }
  setActiveData(row = null) {
    const target = row ? row : !this.curType ? this.serviceList[0] : this.serviceList.find((it) => it.type === this.curType);
    this.activeLabel = target?.label || '';
    this.activeType = target?.type || '';
    this.curType = this.activeType;
    this.showActiveService();
  }
  regService() {
    this.serviceTable.handleReg();
  }
  handleDragEnd() {
    this.curSize = this.div.offsetWidth || 0;
  }
  async showActiveService() {
    await this.$nextTick();
    const el = document.querySelector(`#${this.activeType}`);
    el && el.scrollIntoView({ behavior: 'smooth' });
  }
}
</script>
<style lang="scss" scoped>
::v-deep .service-mgr-button {
  width: 16px;
  height: 32px;
  background-color: #f1f1f1;
}
.service-mgr {
  &-left {
    height: 100%;
    overflow: hidden;
    background-color: #fff;
    &__body {
      padding: 10px 20px;
      height: calc(100% - 61px);
      box-sizing: border-box;
      overflow-x: hidden;
      overflow-y: auto;
    }
  }
  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    color: #444444;
    font-weight: 500;
    border-bottom: 1px solid rgba(241, 241, 241, 1);
    &__text {
      margin-left: 36px;
    }
  }
  &-item {
    display: flex;
    margin-bottom: 10px;
    height: 40px;
    border: 1px solid rgba(241, 241, 241, 1);
    border-radius: 4px;
    color: #444444;
    cursor: pointer;
    overflow: hidden;
    &.active {
      background-color: rgba(55, 124, 255, 0.1);
      border: 1px solid rgba(55, 124, 255, 1);
      font-weight: 600;
    }
    &:last-of-type {
      margin-bottom: 0;
    }
    &__icon {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      margin-right: 12px;
      width: 40px;
      height: 40px;
      overflow: hidden;
      border-radius: 4px;
      background-color: #f2f4f7;
    }
    &__some {
      display: flex;
      align-items: center;
      padding: 12px 7px;
      width: calc(100% - 62px);
    }
    &__label {
      margin-right: 12px;
      max-width: calc(100% - 36px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
