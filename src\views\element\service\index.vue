<template>
  <pro-page title="服务管理" :fixed-header="viewType === 'LIST' ? false : true">
    <div slot="operation">
      <el-button type="primary" icon="el-icon-sort" @click="changeViewType">
        {{ viewType === 'GRID' ? '全局视图' : '列表视图' }}
      </el-button>
      <el-button v-if="viewType !== 'LIST'" icon="el-icon-refresh-right" @click="getServices()">
        刷新
      </el-button>
    </div>
    <div v-show="viewType === 'GRID'" class="sevice-content">
      <!-- 全局视图 -->
      <el-row :gutter="18">
        <el-col v-for="item in services" :key="item.label" type="card" :span="6">
          <div class="sevice-item" @click="toClusters(item)">
            <div class="sevice-item__img">
              <svg-icon :name="item.icon" style="width: 40px; height: 40px" />
            </div>
            <div class="sevice-item__info">
              <span class="name">{{ item.label }}</span>
              <span class="num">{{ item.count }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 列表视图 -->
    <div v-show="viewType === 'LIST'" class="tableList">
      <div class="left">
        <div class="left__header">服务</div>
        <ul class="left__ul">
          <li
            v-for="item in services"
            :key="item.label"
            :class="[
              'service-left__item',
              activeService && activeService.label === item.label ? 'active' : ''
            ]"
            @click="setActiveService(item)"
          >
            {{ item.label }}
          </li>
        </ul>
      </div>
      <div class="right">
        <div class="right__title">
          <span>{{ activeService && activeService.label }}</span>
          <el-button
            v-if="hasReg"
            v-access="regAuthCode"
            type="primary"
            size="small"
            @click="registerService"
          >
            注册
          </el-button>
          <el-button
            v-if="hasAdd"
            v-access="addAuthCode"
            type="primary"
            size="small"
            @click="addService"
          >
            新建
          </el-button>
        </div>
        <div v-if="activeService">
          <ServiceCustom ref="clusters" :current-service="activeService" />
        </div>
      </div>
    </div>
  </pro-page>
</template>
<script lang="ts">
import { Component, Vue, Ref, Inject } from 'vue-property-decorator';
import { getTypeList } from '@/apis/serviceApi';
import { hasPermission } from '@/utils';
import ServiceCustom from './custom/index.vue';
@Component({
  name: 'Service',
  components: { ServiceCustom }
})
export default class Service extends Vue {
  @Ref() clusters: any;
  @Inject('enableSql') enableSql;
  // 服务列表
  services = [];
  // 视图类型
  viewType: 'GRID' | 'LIST' = 'GRID';
  // 列表视图当前选中service
  activeService: any = null;
  data = {
    search: '',
    sortData: { updateTime: 'DESC' }
  };
  serviceType = 'HOST';
  get placeholder() {
    return this.listConfig.searchPlaceholder;
  }
  // 对应服务类型的列表操作按钮权限配置
  get listConfig() {
    return (this.activeService && this.activeService.listConf) || {};
  }
  get hasReg() {
    return this.activeService && this.activeService.hasReg;
  }
  get hasAdd() {
    return this.activeService && this.activeService.type === 'HOST' && this.activeService.hasAdd;
  }
  get regAuthCode() {
    return this.listConfig['regAuthCode'];
  }
  get addAuthCode() {
    return this.listConfig['addAuthCode'];
  }
  created() {
    this.getServices();
  }
  // 获取服务列表
  async getServices() {
    let { data = [] } = await getTypeList();
    // 权限过滤
    data = data.filter((item) => hasPermission(item.listConf.menuAuthCode));
    this.services = data.map((item) => {
      const { label, type, listConf, count, hasAdd, hasReg } = item;
      return {
        label,
        icon: type.toLowerCase(),
        type,
        count,
        listConf,
        hasAdd,
        hasReg
      };
    });
    !this.enableSql &&
      (this.services = this.services.filter((el: any) => !['daemon', 'dts'].includes(el.label)));
    this.activeService = this.services[0];
  }
  // 切换视图类型
  changeViewType() {
    this.viewType = this.viewType === 'GRID' ? 'LIST' : 'GRID';
  }
  // 跳转至集群列表
  toClusters({ label, type }) {
    this.$router.push({
      path: `/element/clusters/${type}`,
      query: {
        type,
        title: label
      }
    });
  }
  setActiveService(item) {
    this.activeService = null;
    this.$nextTick(() => {
      this.activeService = item;
    });
  }
  registerService() {
    // 注册服务
    (this.$refs.clusters as any).reg();
  }
  addService() {
    // 新建服务
    (this.$refs.clusters as any).add();
  }
}
</script>
<style lang="scss" scoped>
.sevice-content {
  padding-top: 20px;
  overflow-x: hidden;
}
.sevice-content ::v-deep .el-card {
  border: 1px solid #f5f5f5;
  border-radius: 4px;
}

.sevice-item {
  display: flex;
  align-items: center;
  height: 140px;
  background: #fff;
  cursor: pointer;
  margin-bottom: 20px;
}
.sevice-item__img {
  width: 72px;
  height: 72px;
  margin: 0 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: $--bs-color-background-page;
  border-radius: 23px;
}
.sevice-item__info {
  display: flex;
  flex-direction: column;
  padding-right: 20px;
  & .name {
    font-size: 16px;
    line-height: 20px;
    color: $--bs-color-text-secondary;
  }
  & .num {
    font-size: 32px;
    line-height: 46px;
  }
}
.tableList {
  display: flex;
  height: calc(100vh - 176px);
  overflow: hidden;
  background: #fff;
  .left {
    width: 240px;
    border-right: 1px solid #f1f1f1;
    &__header {
      line-height: 50px;
      text-align: center;
      font-weight: 500;
      border-bottom: 1px solid #f1f1f1;
    }
    &__ul {
      padding: 10px;
      margin: 0;
      height: calc(100vh - 240px);
      overflow-y: auto;
    }
  }
  .right {
    width: calc(100% - 240px);
    &__title {
      display: flex;
      align-items: center;
      line-height: 50px;
      padding: 0 20px;
      border-bottom: 1px solid #f1f1f1;
      > span {
        flex: 1;
      }
    }
    &__title::before {
      content: ' ';
      position: relative;
      left: 0;
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 8px;
      background: #ff9e2b;
      border-radius: 2px;
    }
  }
}

// 列表视图相关
.header {
  display: flex;
  align-items: center;
  height: 100%;
  font-weight: 500;
}
::v-deep .service-left__header {
  @extend .header;
  justify-content: center;
  font-weight: 500;
}
::v-deep .bs-el-col__header {
  @extend .header;
  padding: 0 20px;
}
.service-left__ul {
  padding: 0;
  margin: 0;
}
.service-left__item {
  height: 40px;
  line-height: 40px;
  border-radius: 6px;
  list-style: none;
  padding-left: 20px;
  cursor: pointer;
}
.service-left__item.active {
  background: $--bs-color-background-base;
}
::v-deep .el-icon-sort {
  transform: rotate(90deg);
}
</style>
