<!--
 * @Description:
 * @Autor: magicyang
 * @Date: 2020-07-15 09:29:56
 * @LastEditors: magicyang
 * @LastEditTime: 2020-07-22 20:04:12
-->
<template>
  <bs-dialog
    id="join-dialog"
    :title="data.nodeName + '组件配置'"
    :visible.sync="visible"
    :before-close="closeDialog"
    width="60%"
    append-to-body
  >
    <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="120px">
      <div style="display: flex">
        <div style="width: 50%">
          <el-divider content-position="left">左流配置</el-divider>
          <el-form-item label="key字段" align="left" prop="leftKey">
            <bs-select
              v-model="formData.leftKey"
              placeholder="请填写左侧流的join字段"
              clearable
              filterable
              multiple
              show-all
              collapse-tags
              confirm-when-deleting
              style="width: calc(100% - 30px)"
              :options="myLeftInputs"
            />
          </el-form-item>
          <el-form-item
            v-if="formData.timeCharacteristic === 'Event'"
            label="时间字段"
            align="left"
            prop="leftTimeKey"
          >
            <el-select
              v-model="formData.leftTimeKey"
              placeholder="请填写左侧流的时间字段"
              clearable
              filterable
              style="width: calc(100% - 30px)"
            >
              <el-option
                v-for="item in myLeftInputs"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="输出字段前缀" align="left" prop="leftOutputFieldPrefix">
            <el-input
              v-model="formData.leftOutputFieldPrefix"
              placeholder="请填写左侧流的输出字段的前缀"
              clearable
              style="width: calc(100% - 30px)"
            />
          </el-form-item>

          <el-form-item label="输出字段" align="left" prop="leftOutputField">
            <bs-select
              v-model="formData.leftOutputField"
              placeholder="请填写左侧流的输出字段"
              multiple
              clearable
              filterable
              show-all
              collapse-tags
              confirm-when-deleting
              style="width: calc(100% - 30px)"
              :options="myLeftInputs"
            />
            <!-- <bs-multiple-select-setting
              :multiple-select="formData.leftOutputField"
              :options="myLeftInputs"
              :name-prop="'label'"
              :value-prop="'value'"
              align="left"
              @setSelectItem="setLeftOutputItem"
            /> -->
          </el-form-item>
        </div>
        <div style="width: 50%">
          <el-divider content-position="left">右流配置</el-divider>
          <el-form-item label="key字段" align="left" prop="rightKey">
            <bs-select
              v-model="formData.rightKey"
              placeholder="请填写右侧流的join字段"
              clearable
              filterable
              multiple
              show-all
              collapse-tags
              confirm-when-deleting
              :options="myRightInputs"
              style="width: calc(100% - 30px)"
            />
          </el-form-item>
          <el-form-item
            v-if="formData.timeCharacteristic === 'Event'"
            label="时间字段"
            align="left"
            prop="rightTimeKey"
          >
            <el-select
              v-model="formData.rightTimeKey"
              placeholder="请填写右侧流的时间字段"
              clearable
              filterable
              style="width: calc(100% - 30px)"
            >
              <el-option
                v-for="item in myRightInputs"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="输出字段前缀" align="left" prop="rightOutputFieldPrefix">
            <el-input
              v-model="formData.rightOutputFieldPrefix"
              placeholder="请填写右侧流的输出字段的前缀"
              clearable
              style="width: calc(100% - 30px)"
            />
          </el-form-item>
          <el-form-item label="输出字段" align="left" prop="rightOutputField">
            <bs-select
              v-model="formData.rightOutputField"
              placeholder="请填写右侧流的输出字段"
              multiple
              clearable
              filterable
              show-all
              collapse-tags
              confirm-when-deleting
              style="width: calc(100% - 30px)"
              :options="myRightInputs"
            />
            <!-- <bs-multiple-select-setting
              :multiple-select="formData.rightOutputField"
              :options="myLeftInputs"
              :name-prop="'label'"
              :value-prop="'value'"
              align="left"
              @setSelectItem="setRightOutputItem"
            /> -->
          </el-form-item>
        </div>
      </div>
      <el-divider content-position="left">其他配置</el-divider>
      <el-form-item label="时间类型" align="left" prop="timeCharacteristic">
        <el-radio-group v-model="formData.timeCharacteristic">
          <el-radio label="Processing">处理时间</el-radio>
          <el-radio label="Event">事件时间</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="join方式" align="left" prop="joinType">
        <el-select
          v-model="formData.joinType"
          placeholder="请选择流的join方式"
          clearable
          filterable
          style="width: calc(100% - 30px)"
        >
          <el-option
            v-for="item in [
              {
                name: '内连接',
                type: 'inner'
              },
              {
                name: '左连接',
                type: 'left'
              },
              {
                name: '右连接',
                type: 'right'
              }
            ]"
            :key="item.type"
            :label="item.name"
            :value="item.type"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="窗口类型" align="left" prop="windowType">
        <el-select
          v-model="formData.windowType"
          placeholder="请选择窗口类型"
          style="width: calc(100% - 30px)"
        >
          <el-option
            v-for="item in [
              { name: '滑动窗口', value: 'Sliding' },
              { name: '滚动窗口', value: 'Tumbling' }
            ]"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="窗口大小" align="left" prop="windowSize">
        <el-input-number
          v-model="formData.windowSize"
          :min="1"
          placeholder="请输入时间窗口大小，单位秒"
          style="width: calc(100% - 30px)"
        />
        秒
      </el-form-item>

      <el-form-item
        v-if="formData.windowType === 'Sliding'"
        label="滑动步长"
        align="left"
        prop="slidingStep"
      >
        <el-input-number
          v-model="formData.slidingStep"
          :min="1"
          placeholder="请输入滑动步长，单位秒"
          style="width: calc(100% - 30px)"
        />
        秒
      </el-form-item>
      <el-form-item
        v-if="formData.timeCharacteristic === 'Event'"
        label="允许乱序时间"
        align="left"
        prop="orderlessTime"
      >
        <el-input-number
          v-model="formData.orderlessTime"
          :min="1"
          placeholder="允许乱序的时间"
          style="width: calc(100% - 30px)"
        />
        秒
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <div style="display: flex">
        <div style="flex: 1">
          <el-switch
            v-model="printLog"
            style="display: block; float: left"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="开启日志"
            inactive-text="关闭日志"
            :disabled="disabled"
          />
        </div>
        <el-button @click="closeDialog(false)">取消</el-button>
        <el-button v-if="!disabled" type="primary" @click="submit('ruleForm')">确定</el-button>
      </div>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import * as _ from 'lodash';

const _DEFAULT_FORMDATA = {
  leftInputField: [],
  rightInputField: [],
  leftKey: '',
  rightKey: '',
  timeCharacteristic: 'Processing',
  leftTimeKey: '',
  rightTimeKey: '',
  joinType: '',
  windowType: 'Tumbling',
  leftOutputField: [],
  rightOutputField: [],
  leftOutputFieldPrefix: 'l',
  rightOutputFieldPrefix: 'r'
};
@Component({
  components: {
    'bs-multiple-select-setting': () => import('@/components/bs-multiple-select-setting.vue')
  }
})
export default class NodeJoin extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: false }) disabled!: boolean;

  formData: any = {};
  rules: any = {
    leftKey: [
      {
        required: true,
        message: '请填写左侧流的join字段',
        trigger: 'blur'
      }
    ],
    leftTimeKey: [
      {
        required: true,
        message: '请填写左侧流的时间字段',
        trigger: 'blur'
      }
    ],
    leftOutputFieldPrefix: [
      {
        required: true,
        message: '请填写左侧流的输出字段的前缀',
        trigger: 'blur'
      }
    ],
    leftOutputField: [
      {
        required: true,
        message: '请填写左侧流的输出字段',
        trigger: 'blur'
      }
    ],
    rightKey: [
      {
        required: true,
        message: '请填写右侧流的join字段',
        trigger: 'blur'
      }
    ],
    rightTimeKey: [
      {
        required: true,
        message: '请填写右侧流的时间字段',
        trigger: 'blur'
      }
    ],
    rightOutputFieldPrefix: [
      {
        required: true,
        message: '请填写右侧流的输出字段的前缀',
        trigger: 'blur'
      }
    ],
    rightOutputField: [
      {
        required: true,
        message: '请填写右侧流的输出字段',
        trigger: 'blur'
      }
    ],
    timeCharacteristic: [
      {
        required: true,
        message: '请选择时间类型',
        trigger: 'blur'
      }
    ],
    joinType: [
      {
        required: true,
        message: '请选择流的join方式',
        trigger: 'blur'
      }
    ],
    windowType: [
      {
        required: true,
        message: '请选择窗口类型',
        trigger: 'blur'
      }
    ],
    windowSize: [
      {
        required: true,
        message: '请输入时间窗口大小，单位秒',
        trigger: 'blur'
      }
    ],
    slidingStep: [
      {
        required: true,
        message: '请输入滑动步长，单位秒',
        trigger: 'blur'
      }
    ],
    orderlessTime: [
      {
        required: true,
        message: '允许乱序的时间,单位秒',
        trigger: 'blur'
      }
    ]
  };

  myLeftInputs: any = [];
  myRightInputs: any = [];
  leftStartNodeId = '';
  rightStartNodeId = '';
  printLog = false;
  created() {
    this.printLog = this.data.printLog;
    // 获取左右端点
    (this.data.pointIn || []).forEach((point, idx) => {
      this[idx === 0 ? 'myLeftInputs' : 'myRightInputs'] = this.transformToOptions(point.data);
    });
    this.leftStartNodeId = this.data.leftStartNodeId;
    this.rightStartNodeId = this.data.rightStartNodeId;
    this.formData = _.cloneDeep(this.data.properties ? this.data.properties : _DEFAULT_FORMDATA);
  }
  submit(formName) {
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        const nodeDto = _.cloneDeep(this.data);
        const properties = _.cloneDeep(this.formData);
        for (const pKey of Object.keys(properties)) {
          if (properties[pKey] === undefined) {
            delete properties[pKey];
          }
        }
        nodeDto.printLog = this.printLog;
        nodeDto.inputFields = _.concat(this.myLeftInputs, this.myRightInputs);

        const outputs: any = [];
        this.formData.leftOutputField.forEach((n) => {
          const rec = _.find(this.myLeftInputs, { value: n });
          if (rec) {
            outputs.push({
              name: this.formData.leftOutputFieldPrefix + n,
              outputable: true,
              type: rec.type
            });
          }
        });
        this.formData.rightOutputField.forEach((n) => {
          const rec = _.find(this.myRightInputs, { value: n });
          if (rec) {
            outputs.push({
              name: this.formData.rightOutputFieldPrefix + n,
              outputable: true,
              type: rec.type
            });
          }
        });
        nodeDto.outputFields = outputs;
        nodeDto.properties = properties;
        nodeDto.properties.leftStartNodeId = this.leftStartNodeId;
        nodeDto.properties.rightStartNodeId = this.rightStartNodeId;
        nodeDto.properties.inputPointFields = {
          left: this.myLeftInputs,
          right: this.myRightInputs
        };
        nodeDto.leftStartNodeId = this.leftStartNodeId;
        nodeDto.rightStartNodeId = this.rightStartNodeId;

        this.closeDialog(true, nodeDto);
      } else {
        this.$tip.error('请检查输入内容');
        return false;
      }
    });
  }

  setLeftSelectItem(result) {
    this.formData.leftInputField = result.values;
  }
  setRightSelectItem(result) {
    this.formData.rightInputField = result.values;
  }
  setLeftOutputItem(result) {
    this.formData.leftOutputField = result.values;
  }
  setRightOutputItem(result) {
    this.formData.rightOutputField = result.values;
  }
  transformToOptions(data) {
    return data.map((item) => ({ label: item.name, value: item.name, type: item.type }));
  }
  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    this.formData = {};
    this.myLeftInputs = [];
    this.myRightInputs = [];
    this.leftStartNodeId = '';
    this.rightStartNodeId = '';
    return { needUpdate, jobNode };
  }

  private getStartNodeId(uuid: string) {
    const rec = _.find(this.jobData.content.edges, {
      endNodePoint: uuid
    });
    if (rec) {
      return rec.startNode;
    }
    return '';
  }

  private getNode(nodeId: string) {
    return _.find(this.jobData.content.nodes, {
      nodeId: nodeId
    });
  }

  private getInputFields(outputFields: any) {
    return _.filter(outputFields, {
      outputable: true
    }).map((m) => {
      return { value: m.name, label: m.name, type: m.type };
    });
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-select {
  width: 100%;
}
</style>
