<template>
  <pro-page :title="$t('pa.menu.sqlClipLib')" :fixed-header="false" class="sql-content">
    <div slot="operation" class="sql-content__header">
      <bs-search
        v-model="search"
        :class="{ 'sql-content__search': isEn }"
        :placeholder="$t('pa.placeholder.keyPlaceholder')"
        @search="getListData()"
      />
      <el-button v-access="'PA.ASSETS.SQL_CODE.ADD'" type="primary" style="margin-left: 10px" @click="addSqlClip">
        {{ $t('pa.action.add') }}
      </el-button>
      <el-button v-access="'PA.ASSETS.SQL_CODE.IMPORT'" type="primary" style="margin-left: 10px" @click="importFile">
        {{ $t('pa.action.import') }}
      </el-button>
    </div>
    <bs-table
      v-loading="tableLoading"
      class="sql-content__main"
      :height="selectedIds.length ? 'calc(100vh - 344px)' : 'calc(100vh - 288px)'"
      :data="tableData"
      :column-data="columnData"
      :page-data="pageData"
      :selection="true"
      @page-change="handlePageChange"
      @selection-change="handleSelectionChange"
      @refresh="getListData(true)"
    >
      <div slot="headerOperator">
        <el-button-group>
          <el-button
            v-for="item in topConfigButtons"
            :key="item.label"
            v-access="item.authCode"
            size="small"
            @click="topOperateHandler(item.event, selectedIds)"
          >
            {{ item.label }}
          </el-button>
        </el-button-group>
      </div>

      <template slot="operator" slot-scope="{ row }">
        <el-tooltip
          v-for="item in tableConfigButtons"
          :key="item.label"
          :content="item.label"
          effect="light"
          class="sql-content__main--tooltip"
        >
          <i
            v-if="hasAuthority(row.dataLevelType, item)"
            :class="item.class"
            class="sql-content__main--icon"
            @click="operateHandler(item.event, row)"
          ></i>
        </el-tooltip>
      </template>
    </bs-table>
    <import-modal :visible="impVisible" @close="closeDialog" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { getAllSql, exportSql, delSql } from '@/apis/sqlApi';
import dayjs from 'dayjs';
import { hasPermission } from '@/utils';
@Component({
  components: {
    'import-modal': () => import('./modals/import-sql.vue')
  }
})
export default class SqlClip extends Vue {
  tableLoading = false;
  search = ''; //筛选信息
  impVisible = false; //是否显示导入弹窗
  pageData = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 };
  private tableConfigButtons = [
    {
      label: this.$t('pa.action.view'),
      class: 'iconfont icon-chakan',
      event: 'viewDetail',
      authCode: 'PA.ASSETS.SQL_CODE.VIEW_DETAIL'
    },
    {
      label: this.$t('pa.action.edit'),
      class: 'iconfont icon-bianji',
      event: 'goEdit',
      authCode: 'PA.ASSETS.SQL_CODE.EDIT'
    },
    {
      label: this.$t('pa.action.del'),
      class: 'iconfont icon-shanchu',
      event: 'delItems',
      authCode: 'PA.ASSETS.SQL_CODE.DELETE'
    }
  ];
  //顶部操作按钮组
  private topConfigButtons = [
    {
      label: this.$t('pa.action.export'),
      event: 'exportData',
      authCode: 'PA.ASSETS.SQL_CODE.EXPORT'
    },
    {
      label: this.$t('pa.action.del'),
      event: 'delItems',
      authCode: 'PA.ASSETS.SQL_CODE.DELETE'
    }
  ];
  tableData: any = [];
  columnData = [];
  selectedIds: string[] = [];
  activated() {
    this.getListData();
  }
  async getListData(refresh = false) {
    const params = { pageData: this.pageData, search: this.search, sortData: {} };
    this.tableLoading = true;
    const { data } = await getAllSql(params);
    data.columnData.push({ label: this.$t('pa.action.action'), value: 'operator', width: 140, fixed: 'right' });
    data.columnData.forEach((el) => {
      if (el.prop) {
        el.value = el.prop;
        delete el.prop;
      }
    });
    data.columnData[0].fixed = 'left';
    this.columnData = data.columnData;
    this.tableData = data.tableData;
    this.tableData.forEach((el) => {
      el.updateTime = dayjs(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
      el.createTime = dayjs(el.createTime).format('YYYY-MM-DD HH:mm:ss');
    });
    this.pageData.total = data.pageData.total;
    refresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    this.tableLoading = false;
  }
  // 表格页码修改回调
  handlePageChange(page: number, size: number) {
    this.pageData.currentPage = page;
    this.pageData.pageSize = size;
    this.getListData();
  }
  // 判断是否有编辑、删除权限
  hasAuthority(dataLevelType: string, { authCode, event }: any) {
    return event === 'viewDetail'
      ? hasPermission(authCode)
      : ['SELF', 'CHILD'].includes(dataLevelType) && hasPermission(authCode);
  }
  //新建
  addSqlClip() {
    this.$router.replace({
      path: '/clip/sqlEdit',
      query: { title: '：' + this.$t('pa.action.add'), status: 'add' }
    });
  }
  // 表格多选回调
  handleSelectionChange(sel: any) {
    this.selectedIds = sel;
  }
  //顶部操作按钮组
  topOperateHandler(event, selectedIds) {
    if (!selectedIds.length) {
      this.$message.warning(this.$t('pa.resource.sql.dataTip'));
      return;
    }
    const ids: any = [];
    selectedIds.forEach((item) => ids.push(item.id));
    this[event](ids);
  }
  //导入
  importFile() {
    this.impVisible = true;
  }

  //导出
  async exportData(val) {
    await exportSql(val);
  }
  // 操作按钮触发
  operateHandler(event, row) {
    this[event](row);
  }
  //查看
  viewDetail(row) {
    if (!hasPermission('PA.ASSETS.SQL_CODE.VIEW_DETAIL')) return;
    this.$router.push({
      path: '/clip/sqlEdit',
      query: { id: row.id, title: this.$t('pa.action.detail'), status: 'detail' }
    });
  }
  //编辑
  goEdit(row) {
    this.$router.replace({
      path: '/clip/sqlEdit',
      query: {
        id: row.id,
        title: '：' + this.$t('pa.action.edit'),
        status: 'edit'
      }
    });
  }
  //删除
  delItems(ids) {
    ids = !ids.length ? ids.id.split() : ids;
    this.$confirm(this.$t('pa.resource.sql.delTip'), this.$t('pa.prompt'), {
      confirmButtonText: this.$t('pa.action.makeSure'),
      cancelButtonText: this.$t('pa.action.cancel'),
      type: 'warning'
    }).then(async () => {
      const { success, error } = await delSql(ids);
      if (success) {
        this.getListData();
        this.$message.success(this.$t('pa.tip.delSuccess'));
        return;
      }
      this.$message.error(error);
    });
  }
  //关闭弹窗
  closeDialog(val) {
    this.impVisible = false;
    if (!val) this.getListData();
  }
}
</script>
<style lang="scss" scoped>
.sql-content {
  &__search {
    width: 235px !important;
    ::v-deep .el-input {
      width: 235px !important;
    }
  }
  &__main {
    &--tooltip {
      cursor: pointer;
    }
    &--icon {
      margin: 0 5px;
    }
  }
}
</style>
