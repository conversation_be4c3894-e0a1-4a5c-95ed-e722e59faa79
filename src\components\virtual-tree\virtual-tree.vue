<template>
  <div class="virtual-tree" @scroll="handleScroll">
    <div class="virtual-tree-complete-height" :style="{ height: totalHeight + 'px' }"></div>
    <div
      v-if="visibleData.length"
      ref="showList"
      :key="key"
      class="virtual-tree-visible"
      :style="{ transform: transform }"
    >
      <div
        v-for="data in visibleData"
        :key="data.id"
        :class="[
          'virtual-tree-item',
          currentNodekey === data.id ? 'virtual-tree-item--active' : ''
        ]"
        :style="{
          paddingLeft: (data.level - 1) * 15 + 'px',
          margin: data.children.length === 0 ? '0 12px' : '0 12px'
        }"
      >
        <span class="virtual-tree-item-icon-box" @click="handleExpanded(data)">
          <i
            v-if="data.children && data.children.length"
            :class="[
              data.expanded ? 'expanded' : '',
              'el-icon-caret-right',
              'virtual-tree-item-icon'
            ]"
          ></i>
        </span>
        <el-checkbox
          v-if="showCheckbox"
          v-model="data.check"
          :indeterminate="data.isIndeterminate"
          @change="handleCheckChange(data)"
        />
        <VirtualTreeItemPrefix :node="data" />
        <el-tooltip
          v-hide="data.label"
          :content="data.label"
          :open-delay="500"
          effect="light"
          placement="top"
        >
          <span
            :class="['virtual-tree-item-label']"
            @click="handelLabelClick(data)"
            @mouseenter="showTip && handelLabelMouseenter($event, data)"
            @mouseleave="showTip && handelLabelMouseleave(data)"
          >
            {{ data.label }}
          </span>
        </el-tooltip>
        <VirtualTreeItemContent :node="data" />
      </div>
    </div>
    <div v-if="visibleData.length === 0" class="virtual-tree-no-data">暂无数据</div>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" scoped src="./index.scss"></style>
