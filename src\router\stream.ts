const router = [
  {
    path: '/streamCube&/sc_cluster&/sc_charts',
    name: 'streamCube&',
    meta: {
      title: '流立方',
      icon: 'iconfont icon-liulifang',
      access: 'STREAMCUBE'
    },
    children: [
      {
        path: '/streamCube',
        name: 'streamCube',
        meta: {
          title: '指标计算',
          access: 'STREAMCUBE.SCRIPT' // 权限码
        },
        children: [
          {
            path: '/streamCube/fastSearch',
            name: 'streamCubeFastSearch',
            meta: {
              title: '快速搜索'
            }
          },
          {
            path: '/streamCube/pkgList',
            name: 'streamCubePkgList',
            meta: {
              title: '脚本包',
              access: 'STREAMCUBE.SCRIPT.PKG' // 权限码
            }
          },
          {
            path: '/streamCube/imExList',
            name: 'streamCubeImExList',
            meta: {
              title: '导入导出',
              access: 'STREAMCUBE.SCRIPT.IMP' // 权限码
            }
          },
          {
            path: '/streamCube/indexSearch',
            name: 'streamCubeIndexSearch',
            meta: {
              title: '指标查询',
              access: 'STREAMCUBE.SCRIPT.INDEX' // 权限码
            }
          },
          {
            path: '/streamCube/recycleBin',
            name: 'streamCubeRecycleBin',
            meta: {
              title: '回收站',
              access: 'STREAMCUBE.SCRIPT.RECl' // 权限码
            }
          }
        ]
      },
      {
        path: '/sc_cluster',
        name: 'scCluster',
        meta: {
          title: '集群监控',
          access: 'STREAMCUBE.CLUSTER' // 权限码
        },
        children: [
          {
            path: '/sc_cluster/cacheMonitorTab',
            name: 'scClusterCache',
            meta: {
              title: '缓存监控',
              access: '' // 权限码
            }
          },
          {
            path: '/sc_cluster/systemMonitorTab',
            name: 'scClusterSystem',
            meta: {
              title: '系统监控',
              access: '' // 权限码
            }
          }
        ]
      },
      {
        path: '/sc_charts',
        name: 'scCharts',
        meta: {
          title: '报表管理',
          access: 'STREAMCUBE.CHARTS' // 权限码
        },
        children: [
          {
            path: '/sc_charts/charts',
            name: 'scChartsCharts',
            meta: {
              title: '图表',
              access: '' // 权限码
            }
          },
          {
            path: '/sc_charts/view',
            name: 'scChartsView',
            meta: {
              title: '视图',
              access: '' // 权限码
            }
          }
        ]
      }
    ]
  }
];

export { router };
