<template>
  <div>
    <table-block
      selection
      paging-front
      :height="height"
      :title="$t('pa.action.resourceAllocate')"
      :loading="loading"
      :page-data="pageData"
      :table-data="tableData"
      :column-data="columnData"
      @page-change="handlePageChange"
      @selection-change="handleSelectionChange"
    >
      <div slot="operation">
        <!-- queue -->
        <bs-select v-if="!isStandalone" v-model="queue" :options="queueList" @focus="getQueueList" @change="handSearch()" />
        <!-- orgName -->
        <bs-search
          v-model="orgName"
          :placeholder="$t('pa.placeholder.orgName')"
          style="width: 280px"
          @change="handSearch()"
        />
        <!-- action -->
        <el-button v-for="it in actionList" :key="it.value" type="primary" @click="handleClick(it.value)">
          {{ it.label }}
        </el-button>
      </div>
    </table-block>
    <!-- 新增 -->
    <add-asset-dialog v-if="showAddAssetDialog" :show.sync="showAddAssetDialog" @confirm="handleConfirm" />
    <!-- 编辑 -->
    <allocate-asset-dialog
      v-if="showAllocateAssetDialog"
      :id="id"
      is-edit
      :org-id="orgId"
      :queue="curQueue"
      :org-list="orgList"
      :is-per-job="isPerJob"
      :show.sync="showAllocateAssetDialog"
      @refresh="handleRefresh"
    />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { canAllocate, getQueueList, getAllocateList, checkRecycleOrg, realRecycle } from '@/apis/serviceApi';
import { includesPro, safeArray } from '@/utils';

@Component({
  components: {
    TableBlock: () => import('../components/table-block.vue'),
    AddAssetDialog: () => import('../components/add-asset-dialog.vue'),
    AllocateAssetDialog: () => import('../components/allocate-asset-dialog/index.vue')
  }
})
export default class FlinkResAllocate extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: () => ({}) }) params!: any;

  loading = false;
  id = '';
  orgId = '';
  queue = '';
  orgName = '';
  canAllocate = true; //控制新增/编辑/回收按钮的显隐
  tableData: any[] = [];
  rawTableData: any[] = [];
  columnData: any[] = [];
  checkedRows: any = [];
  pageData = { pageSize: this.$store.getters.pageSize || 25, currentPage: 1, total: 0 };
  queueList: any = [];

  showAddAssetDialog = false; //新增--机构/队列设置
  showAllocateAssetDialog = false;
  curQueue = '';
  orgList: any[] = [];

  get height() {
    return this.tableData.length ? this.params?.height || '300px' : '120px';
  }
  get isPerJob() {
    const { clusterType } = JSON.parse(this.data.resProperty);
    return ['YARN_PER_JOB', 'YARN_APPLICATION'].includes(clusterType);
  }
  get isStandalone() {
    return (this.$route.query.clusterType as string) === 'STANDALONE';
  }
  get actionList() {
    return this.canAllocate
      ? [
          { value: 'addResource', label: this.$t('pa.action.add') },
          {
            value: 'editResource',
            label: this.$t('pa.action.edit')
          },
          {
            value: 'recycleResource',
            label: this.$t('pa.action.recycle')
          }
        ]
      : [];
  }

  async created() {
    try {
      this.loading = true;
      this.id = this.$route.query.id as string;
      this.orgId = this.data.orgId;
      this.canAllocate = await canAllocate(this.id);
      this.getQueueList();
      this.getTableData();
    } finally {
      this.loading = false;
    }
  }
  async getQueueList() {
    const { success, data, error } = await getQueueList(this.id);
    if (!success) this.$message.error(error);
    this.queueList = [
      { label: this.$t('pa.allQueue'), value: '' },
      ...safeArray(data).map((value) => ({ value, label: value }))
    ];
  }
  async getTableData() {
    const { success, data, error } = await getAllocateList(this.id);
    if (!success) return this.$message.error(error);
    this.columnData = safeArray(data?.columnData).map((it) => {
      it.value = it.prop;
      return it;
    });
    this.rawTableData = safeArray(data?.tableData);
    this.handSearch();
    this.pageData.total = this.tableData.length;
  }
  handSearch() {
    this.tableData = this.rawTableData.filter((it) => {
      return includesPro(it.orgName, this.orgName) && includesPro(it.queue, this.queue);
    });
    this.pageData.currentPage = 1;
    this.pageData.total = this.tableData.length;
  }
  handlePageChange(currentPage: number, pageSize: number) {
    this.pageData!.currentPage = this.pageData.pageSize !== pageSize ? 1 : currentPage;
    this.pageData!.pageSize = pageSize;
  }
  handleSelectionChange(value: any[]) {
    this.checkedRows = value;
  }
  handleClick(type: string) {
    if (type !== 'addResource' && this.checkedRows.length === 0) return this.$message.warning(this.$t('pa.tip.choseData'));
    type === 'addResource' && (this.showAddAssetDialog = true);
    type === 'recycleResource' && this.recycleResource();
    type === 'editResource' && this.editResource();
  }
  handleConfirm({ queue }: any, orgList) {
    this.curQueue = queue;
    this.orgList = orgList;
    this.showAllocateAssetDialog = true;
  }
  /* 回收 */
  async recycleResource() {
    const orgIds = this.checkedRows.map(({ id }) => id);
    const { success, error } = await checkRecycleOrg(this.id, orgIds);
    if (!success) return this.$message.error(error);
    await this.$confirm(this.$t('pa.tip.recycle'), this.$t('pa.action.tip'), { type: 'warning' });
    await this.realRecycle(orgIds);
  }
  /* 真正回收 */
  async realRecycle(orgIds: string[]) {
    const { success, msg, error } = await realRecycle(this.id, orgIds);
    if (!success) return this.$message.error(error);
    this.$message.success(msg);
    this.getTableData();
    this.$emit('refresh');
  }
  /* 资源编辑 */
  async editResource() {
    const queueList = [...new Set(this.checkedRows.map(({ queue }: any) => queue))];
    if (queueList.length > 1) return this.$message.warning(this.$t('pa.tip.sameQueueOrgData'));
    this.curQueue = queueList[0] as string;
    this.orgList = this.checkedRows;
    this.showAllocateAssetDialog = true;
  }
  handleRefresh() {
    this.getTableData();
    this.$emit('refresh');
  }
}
</script>
<style lang="scss" scoped>
::v-deep .bs-select {
  width: 140px;
}
::v-deep .bs-search {
  margin: 0 10px;
  width: 207px;
}
</style>
