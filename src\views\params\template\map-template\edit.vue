<template>
  <pro-page :title="mapTemplateTitle" fixed-header class="map">
    <div slot="operation">
      <el-button type="primary" size="small" @click="save">{{ $t('pa.action.save') }}</el-button>
    </div>
    <pro-grid class="map-grid" direction="column">
      <pro-grid type="info" :title="$t('pa.params.template.detail.baseInfo')">
        <el-form ref="form" class="map-grid--form" :model="form" :rules="formRules" label-width="100px">
          <el-form-item :label="$t('pa.params.template.detail.templateName')" prop="mappingTemplateName">
            <el-input
              v-model="form.mappingTemplateName"
              maxlength="50"
              :placeholder="$t('pa.params.template.name')"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item :label="$t('pa.notes')">
            <el-input
              v-model="form.memo"
              type="textarea"
              :rows="4"
              style="width: 60%"
              :placeholder="$t('pa.placeholder.notesPlaceholder')"
              maxlength="1024"
            />
          </el-form-item>
        </el-form>
      </pro-grid>
      <pro-grid type="info" :title="$t('pa.params.template.detail.fieldList')" class="map-grid--list">
        <div class="select-row-style">
          <el-button v-if="!this.isEditing" type="text" icon="el-icon-edit" class="map-grid--add" @click="isEditing = true">
            {{ $t('pa.params.template.detail.addField') }}
          </el-button>
          <div v-if="this.isEditing" class="map-grid__header">
            <span class="select-label">{{ $t('pa.params.template.detail.systemNumber') }}</span>
            <el-select
              v-model="selectedSystem"
              class="marR16"
              filterable
              :placeholder="$t('pa.placeholder.select')"
              @change="handleSystemChange"
            >
              <el-option v-for="item in systemLists" :key="item" :label="item" :value="item" />
            </el-select>
            <span class="select-label">{{ $t('pa.params.template.detail.mapField') }}</span>
            <el-select
              v-model="selectedFields"
              class="marR16"
              popper-class="mapping-template-edit-field-popper"
              collapse-tags
              filterable
              multiple
              :placeholder="$t('pa.placeholder.select')"
            >
              <el-option
                v-for="(item, index) in allMappingFields"
                :key="item.id"
                :label="item.mappingFieldName"
                :value="index"
              >
                <span style="float: left">{{ item.mappingFieldName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px; padding-right: 16px">
                  {{ item.sourceCode }}
                  {{ item.sourceCodeComment ? '(' + item.sourceCodeComment + ')' : '' }}
                </span>
              </el-option>
            </el-select>
            <el-button @click="cancle">{{ $t('pa.action.cancel') }}</el-button>
            <el-button type="primary" @click="comfirm">{{ $t('pa.action.confirm') }}</el-button>
          </div>
        </div>
        <bs-table
          paging-front
          :data="mappingFields"
          :column-data="columnData"
          :column-settings="false"
          :page-data="pageData"
        >
          <template slot="operator" slot-scope="{ row }">
            <el-button type="text" @click="delField(row.id)">{{ $t('pa.action.del') }}</el-button>
          </template>
        </bs-table>
      </pro-grid>
    </pro-grid>
  </pro-page>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { get, post } from '@/apis/utils/net';
@Component({
  name: 'ElementMapTemplateEdit'
})
export default class ElementRuleTemplateDetail extends PaBase {
  id = '';
  orgId = this.$store.getters.orgId;
  userName = this.$store.getters.userName;
  form: any = {
    mappingTemplateName: '',
    memo: ''
  };
  formRules = {
    mappingTemplateName: [{ required: true, message: this.$t('pa.params.template.name'), trigger: 'change' }]
  };
  detail: any = {};
  // 模板下的字段
  mappingFields: any = [];
  // 所有可选的字段库
  allMappingFields: any = [];
  // select选中的字段id
  selectedFields: any = [];
  isEditing = false;
  // 系统列表
  systemLists = [];
  // 已选择的系统编号
  selectedSystem = '';
  pageData = {
    pageSize: this.$store.getters.pageSize,
    currentPage: 1,
    total: this.mappingFields?.length || 0,
    layout: 'prev, pager, next'
  };
  columnData = [
    { value: 'systemId', label: this.$t('pa.params.template.detail.systemNumber') },
    { value: 'systemName', label: this.$t('pa.params.template.detail.systemName') },
    { value: 'mappingFieldName', label: this.$t('pa.params.template.detail.mapField') },
    { value: 'mappingFieldType', label: this.$t('pa.params.template.detail.fieldType') },
    { value: 'cnName', label: this.$t('pa.params.template.detail.cnName') },
    { value: 'sourceCode', label: this.$t('pa.params.template.detail.sourceCode') },
    { value: 'standardCode', label: this.$t('pa.params.template.detail.standardCode') },
    { value: 'sourceCodeComment', label: this.$t('pa.params.template.detail.sourceCodeComment') },
    { value: 'standardCodeComment', label: this.$t('pa.params.template.detail.standardCodeComment') },
    { value: 'serialNumber', label: this.$t('pa.params.template.detail.serialNumber') },
    { value: 'serialName', label: this.$t('pa.params.template.detail.serialName') },
    { value: 'memo', label: this.$t('pa.notes') },
    { value: 'operator', label: this.$t('pa.action.action'), fixed: 'right', width: '100' }
  ];

  get mapTemplateTitle() {
    return this.id ? this.$t('pa.params.template.editTemplate') : this.$t('pa.params.template.addTemplate');
  }

  async created() {
    this.id = this.$route.query.id as string;
    if (this.id) {
      this.getDetail(this.id);
      this.getMappingFieldsByTemplate(this.id);
    }
    this.getSystemLists();
  }
  // 获取详情
  async getDetail(id) {
    const { data = {} } = await get('/rs/pa/mappingGroup/details', { id });
    this.detail = data;
    // 表单信息获取
    this.form = {
      mappingTemplateName: data.mappingTemplateName,
      memo: data.memo
    };
  }
  // 获取模板下的字段信息
  async getMappingFieldsByTemplate(id) {
    const { data = [] } = await get('/rs/pa/mappingGroup/listGroupMapping', {
      id
    });
    this.mappingFields = data;
    this.pageData.currentPage = 1;
    this.pageData.total = this.mappingFields?.length || 0;
  }
  // 获取字段
  async getMappingFields(systemId) {
    const { data = [] } = await get('/rs/pa/mappingWarehouse/getFieldList', {
      systemId
    });
    this.allMappingFields = data;
  }
  // 获取系统编号列表
  async getSystemLists() {
    const { data = [] } = await get('/rs/pa/mappingWarehouse/getSystemIdList');
    this.systemLists = data;
  }
  handleSystemChange() {
    // 清空已选的字段
    this.selectedFields = [];
    this.getMappingFields(this.selectedSystem);
  }
  // 保存路由模板
  save() {
    (this.$refs['form'] as any).validate(async (valid) => {
      if (valid) {
        const params = {
          dataLevelType: 'PARENT',
          id: this.id,
          mappingIdList: this.mappingFields.map((i) => i.id).join(','),
          orgName: '',
          version: 0,
          ...this.form
        };
        // 调用新建保存接口
        const url = this.id ? '/rs/pa/mappingGroup/update' : '/rs/pa/mappingGroup/add';
        const { data, msg, success } = await post(url, params);
        if (success) {
          this.$message({ message: msg, type: 'success' });
          if (url === '/rs/pa/mappingGroup/add') {
            const oldFullPath = this.$route.fullPath;
            this.$nextTick(() => {
              try {
                const tabsNavInstance = (this as any).$tabNav;
                tabsNavInstance.handleTabsDelete(oldFullPath);
                // 跳转至新的详情页
                this.$router.replace({
                  path: 'mapTemplateEdit',
                  query: { id: data, title: this.form.mappingTemplateName }
                });
              } catch (err) {}
            });
          }
        } else {
          this.$message({ message: msg, type: 'error' });
        }
      }
    });
  }
  cancle() {
    this.selectedSystem = '';
    this.selectedFields = [];
    this.isEditing = false;
  }
  comfirm() {
    this.isEditing = false;
    this.mappingFields = this.mappingFields.concat(this.selectedFields.map((idx) => this.allMappingFields[idx]));
    this.selectedSystem = '';
    this.selectedFields = [];
    // 跳转到最后一页
    this.pageData.currentPage = Math.ceil(this.mappingFields.length / this.$store.getters.pageSize);
  }
  // 删除字段
  delField(id) {
    const index = this.mappingFields.findIndex((i) => i.id === id);
    this.mappingFields.splice(index, 1);
    this.pageData.total = this.mappingFields?.length || 0;
  }
  // 表格页码修改回调
  handlePageChange(page: number, size: number) {
    this.pageData.currentPage = page;
    this.pageData.pageSize = size;
  }
}
</script>

<style lang="scss" scoped>
.map-grid {
  margin-top: 18px;
  &--list {
    background-color: #fff;
    margin-top: 20px;
  }
  &--add {
    margin-left: 20px;
  }
  &--form {
    padding: 15px 0;
  }
  &__header {
    margin-left: 20px;
  }
}
.rule-detail {
  height: calc(100vh - 107px);
  &-content {
    height: calc(100% - 50px);
    overflow: auto;
    padding: 20px;
    background: #fff;
    border-top: 1px solid #f1f1f1;
  }
  .title-slot {
    width: 100%;
    padding: 0 20px;
    display: flex;
    align-items: center;
    .title-slot-content {
      font-size: 14px;
      font-weight: bold;
      flex: 1;
    }
  }
  .title-slot-table {
    border: 1px solid #ebeef5;
    border-bottom: none;
  }
}
.no-data {
  line-height: 200px;
  height: 300px;
  text-align: center;
  font-size: 15px;
  color: #606266;
}
.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  &-title {
    flex: auto;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 700;
    font-size: 15px;
  }
}

.rule-header {
  height: 50px;
  background: #ffffff;
  border-left: none;
  border-right: none;
  padding: 0 20px;
  font-size: 16px;
  display: flex;
  align-items: center;
}
.rule-header-operate {
  flex: 1;
  text-align: right;
}
.select-row-style {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.table-style {
  border: 1px solid #ebeef5;
  border-bottom: none;
}
.select-label {
  margin-right: 10px;
  color: #666;
}
.marR16 {
  margin-right: 16px;
}
.mapping-template-edit-field-popper {
  width: 260px;
}
</style>
