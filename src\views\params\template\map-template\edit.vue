<template>
  <pro-page :title="mapTemplateTitle" fixed-header class="map">
    <div slot="operation">
      <el-button type="primary" size="small" @click="save">保存</el-button>
    </div>
    <pro-grid class="map-grid" direction="column">
      <pro-grid type="info" title="基本配置">
        <el-form
          ref="form"
          class="map-grid--form"
          :model="form"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="模板名称" prop="mappingTemplateName">
            <el-input
              v-model="form.mappingTemplateName"
              maxlength="50"
              placeholder="请输入模板名称"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="form.memo"
              type="textarea"
              :rows="4"
              style="width: 60%"
              placeholder="请输入备注"
              maxlength="1024"
            />
          </el-form-item>
        </el-form>
      </pro-grid>
      <pro-grid type="info" title="字段列表" class="map-grid--list">
        <div class="select-row-style">
          <el-button
            v-if="!this.isEditing"
            type="text"
            icon="el-icon-edit"
            class="map-grid--add"
            @click="isEditing = true"
          >
            添加字段
          </el-button>
          <div v-if="this.isEditing" class="map-grid__header">
            <span class="select-label">系统编号</span>
            <el-select
              v-model="selectedSystem"
              class="marR16"
              filterable
              placeholder="请选择"
              @change="handleSystemChange"
            >
              <el-option v-for="item in systemLists" :key="item" :label="item" :value="item" />
            </el-select>
            <span class="select-label">映射字段</span>
            <el-select
              v-model="selectedFields"
              class="marR16"
              popper-class="mapping-template-edit-field-popper"
              collapse-tags
              filterable
              multiple
              placeholder="请选择"
            >
              <el-option
                v-for="(item, index) in allMappingFields"
                :key="item.id"
                :label="item.mappingFieldName"
                :value="index"
              >
                <span style="float: left">{{ item.mappingFieldName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px; padding-right: 16px">
                  {{ item.sourceCode }}
                  {{ item.sourceCodeComment ? '(' + item.sourceCodeComment + ')' : '' }}
                </span>
              </el-option>
            </el-select>
            <el-button @click="cancle">取消</el-button>
            <el-button type="primary" @click="comfirm">确认</el-button>
          </div>
        </div>
        <div v-if="mappingFields.length === 0" class="no-data">暂无数据</div>
        <div v-if="mappingFields.length">
          <el-table class="table-style" :data="showMappingFields" style="width: 100%">
            <el-table-column prop="systemId" label="系统编号" />
            <el-table-column prop="systemName" label="系统名称" />
            <el-table-column prop="mappingFieldName" label="映射字段" />
            <el-table-column prop="mappingFieldType" label="字段类型" />
            <el-table-column prop="cnName" label="中文名称" />
            <el-table-column prop="sourceCode" label="源代码值" />
            <el-table-column prop="standardCode" label="标准代码值" />
            <el-table-column prop="sourceCodeComment" label="源代码含义" />
            <el-table-column prop="standardCodeComment" label="标准代码含义" />
            <el-table-column prop="serialNumber" label="标准编码" />
            <el-table-column prop="serialName" label="标准名称" />
            <el-table-column prop="memo" label="备注" />
            <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="{ row }">
                <el-button type="text" @click="delField(row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            style="float: right; margin: 10px 0"
            layout="prev, pager, next"
            :total="mappingFields.length"
            :current-page="currentPage"
            @current-change="handleCurrentChange"
          />
        </div>
      </pro-grid>
    </pro-grid>
  </pro-page>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { get, post } from '@/apis/utils/net';
import { vm } from '@/main';
@Component({
  name: 'ElementMapTemplateEdit'
})
export default class ElementRuleTemplateDetail extends PaBase {
  id = '';
  orgId = this.$store.state.userInfo.orgId;
  userName = this.$store.state.userInfo.userName;
  form: any = {
    mappingTemplateName: '',
    memo: ''
  };
  formRules = {
    mappingTemplateName: [{ required: true, message: '请输入模板名称', trigger: 'change' }]
  };
  detail: any = {};
  // 模板下的字段
  mappingFields: any = [];
  // 列表中分页展示的字段
  showMappingFields: any = [];
  // 所有可选的字段库
  allMappingFields: any = [];
  // select选中的字段id
  selectedFields: any = [];
  isEditing = false;
  currentPage = 1;
  // 系统列表
  systemLists = [];
  // 已选择的系统编号
  selectedSystem = '';
  get mapTemplateTitle() {
    return this.id ? '编辑模板' : '新建模板';
  }
  async created() {
    this.id = this.$route.query.id as string;
    if (this.id) {
      this.getDetail(this.id);
      this.getMappingFieldsByTemplate(this.id);
    }
    this.getSystemLists();
  }
  // 获取详情
  async getDetail(id) {
    const { data = {} } = await get('/rs/pa/mappingGroup/details', { id });
    this.detail = data;
    // 表单信息获取
    this.form = {
      mappingTemplateName: data.mappingTemplateName,
      memo: data.memo
    };
  }
  // 获取模板下的字段信息
  async getMappingFieldsByTemplate(id) {
    const { data = [] } = await get('/rs/pa/mappingGroup/listGroupMapping', {
      id
    });
    this.mappingFields = data;
    this.showMappingFields = this.mappingFields.slice(0, 10);
  }
  // 获取字段
  async getMappingFields(systemId) {
    const { data = [] } = await get('/rs/pa/mappingWarehouse/getFieldList', {
      systemId
    });
    this.allMappingFields = data;
  }
  // 获取系统编号列表
  async getSystemLists() {
    const { data = [] } = await get('/rs/pa/mappingWarehouse/getSystemIdList');
    this.systemLists = data;
  }
  handleSystemChange() {
    // 清空已选的字段
    this.selectedFields = [];
    this.getMappingFields(this.selectedSystem);
  }
  // 表格分页
  handleCurrentChange(val) {
    this.currentPage = val;
    this.showMappingFields = this.mappingFields.slice((val - 1) * 10, val * 10);
  }
  // 保存路由模板
  save() {
    (this.$refs['form'] as any).validate(async (valid) => {
      if (valid) {
        const params = {
          dataLevelType: 'PARENT',
          id: this.id,
          mappingIdList: this.mappingFields.map((i) => i.id).join(','),
          orgName: '',
          version: 0,
          ...this.form
        };
        // 调用新建保存接口
        const url = this.id ? '/rs/pa/mappingGroup/update' : '/rs/pa/mappingGroup/add';
        const { data, msg, success } = await post(url, params);
        if (success) {
          this.$message({ message: msg, type: 'success' });
          if (url === '/rs/pa/mappingGroup/add') {
            const oldFullPath = this.$route.fullPath;
            this.$nextTick(() => {
              try {
                const tabsNavInstance = vm.$children[0].$refs['tabsNav'];
                tabsNavInstance.handleTabsDelete(oldFullPath);
                // 跳转至新的详情页
                this.$router.replace({
                  path: 'mapTemplateEdit',
                  query: { id: data, title: this.form.mappingTemplateName }
                });
              } catch (err) {}
            });
          }
        } else {
          this.$message({ message: msg, type: 'error' });
        }
      }
    });
  }
  cancle() {
    this.selectedSystem = '';
    this.selectedFields = [];
    this.isEditing = false;
  }
  comfirm() {
    this.isEditing = false;
    this.mappingFields = this.mappingFields.concat(
      this.selectedFields.map((idx) => this.allMappingFields[idx])
    );
    this.selectedSystem = '';
    this.selectedFields = [];
    // 跳转到最后一页
    this.currentPage = Math.ceil(this.mappingFields.length / 10);
    this.showMappingFields = this.mappingFields.slice(
      (this.currentPage - 1) * 10,
      this.currentPage * 10
    );
  }
  // 删除字段
  delField(id) {
    const index = this.mappingFields.findIndex((i) => i.id === id);
    this.mappingFields.splice(index, 1);
    this.showMappingFields = this.mappingFields.slice(
      (this.currentPage - 1) * 10,
      this.currentPage * 10
    );
  }
}
</script>

<style lang="scss" scoped>
.map-grid {
  margin-top: 18px;
  &--list {
    background-color: #fff;
    margin-top: 20px;
  }
  &--add {
    margin-left: 20px;
  }
  &--form {
    padding: 15px 0;
  }
  &__header {
    margin-left: 20px;
  }
}
.rule-detail {
  height: calc(100vh - 107px);
  &-content {
    height: calc(100% - 50px);
    overflow: auto;
    padding: 20px;
    background: #fff;
    border-top: 1px solid #f1f1f1;
  }
  .title-slot {
    width: 100%;
    padding: 0 20px;
    display: flex;
    align-items: center;
    .title-slot-content {
      font-size: 14px;
      font-weight: bold;
      flex: 1;
    }
  }
  .title-slot-table {
    border: 1px solid #ebeef5;
    border-bottom: none;
  }
}
.no-data {
  line-height: 200px;
  height: 300px;
  text-align: center;
  font-size: 15px;
  color: #606266;
}
.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  &-title {
    flex: auto;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 700;
    font-size: 15px;
  }
}

.rule-header {
  height: 50px;
  background: #ffffff;
  border-left: none;
  border-right: none;
  padding: 0 20px;
  font-size: 16px;
  display: flex;
  align-items: center;
}
.rule-header-operate {
  flex: 1;
  text-align: right;
}
.select-row-style {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.table-style {
  border: 1px solid #ebeef5;
  border-bottom: none;
}
.select-label {
  margin-right: 10px;
  color: #666;
}
.marR16 {
  margin-right: 16px;
}
.mapping-template-edit-field-popper {
  width: 260px;
}
</style>
