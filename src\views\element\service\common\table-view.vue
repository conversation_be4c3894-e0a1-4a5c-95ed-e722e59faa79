<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">{{ title }}</div>
      <div class="bs-detail__header-operation">
        <el-button :disabled="!canRunFlag" type="primary" @click="runSql">运行</el-button>
      </div>
    </div>
    <div class="tab-content">
      <div class="content">
        <div class="left">
          <div>
            <bs-search
              v-model="search"
              style="margin: 0 10px 10px 10px; width: 230px"
              placeholder="请输入表名"
              @search="searchTable"
            />
            <el-button @click="getTables">刷新</el-button>
          </div>
          <bs-table
            v-loading="tableLoading"
            :data="tableData"
            :column-data="columnData"
            :page-data="pageData"
            :column-settings="false"
            :height="540"
            highlight-current-row
            @page-change="handleCurrentChange"
            @row-click="rowClick"
          >
            <template v-slot:operator="{ row }">
              <el-tooltip effect="light" content="查看字段" placement="top">
                <span class="iconfont icon-xinxichaxun" @click="showFields(row)"></span>
              </el-tooltip>
            </template>
          </bs-table>
        </div>
        <div class="right">
          <bs-code
            ref="codeRef"
            :extra-style="{ height: '120px' }"
            :value="sql"
            language="sql"
            :title="'查询语句（' + sourceTitle + '）'"
            :read-only="false"
            :formatter="type === 'ELASTICSEARCH'"
            @change="canRun"
          />
          <div v-if="resIsStr">
            <bs-code
              v-if="sqlResult"
              v-loading="runSqlLoading"
              class="marT15"
              :extra-style="{ height: '385px' }"
              :value="sqlResult"
              language="json"
              title="查询结果"
              :read-only="true"
              :formatter="type !== 'HBASE'"
            />
            <!--这里借用了表格无数据时的样式，单纯是为了展示无数据的情况：未运行无数据则查询结果不显示内容，已运行无数据查询结果展示暂无数据-->
            <div v-else>
              <div class="marT10 table-result">查询结果</div>
              <bs-table
                class="right-table"
                :page-data="false"
                :column-settings="false"
                :height="409"
                empty-text=" "
              />
            </div>
          </div>
          <div v-else>
            <div class="marT10 table-result">查询结果</div>
            <bs-table
              v-loading="runSqlLoading"
              class="right-table"
              :data="resultTableData"
              :column-data="resultColumnData"
              :page-data="false"
              :column-settings="false"
              :height="409"
            />
          </div>
        </div>
      </div>
    </div>
    <!--字段窗口-->
    <selectFields
      v-if="showFieldDialog"
      :visible.sync="showFieldDialog"
      :table-name="selectTableName"
      :request="getFieldsRequest()"
      @confirm="handlefieldConfirm"
    />
  </div>
</template>
<script lang="ts">
import { Component, Inject, Ref } from 'vue-property-decorator';
import { trimEnd } from 'lodash';
import {
  getDatabaseTable,
  runSql,
  getHiveTable,
  getHbaseTable,
  getEsTable,
  getHiveTableFields,
  getHbaseTableFields,
  getSqlColumn,
  runHiveSQL,
  runHbaseSQL,
  getEsTableFields,
  runEsSQL
} from '@/apis/serviceApi';
import { PaBase } from '@/common/pipeace-base';
import * as _ from 'lodash';
import selectFields from './select-fields.vue';
@Component({
  components: {
    selectFields
  }
})
export default class TableView extends PaBase {
  @Ref('codeRef') readonly codeRef!: any;
  runSqlLoading = false;
  // 执行代码
  sql = '';
  selectTableName = '';
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  search = '';
  // 左侧表列表数据
  tableData = [];
  columnData = [];
  tableLoading = false;
  sqlResult = '';
  pageData: any = {
    pageSize: 10,
    currentPage: 1,
    total: 0,
    layout: 'total, prev, pager, next',
    pagerCount: 5
  };
  // sql查询表格数据
  resultTableData = [];
  resultColumnData: any = [];
  // 显示字段信息弹窗
  showFieldDialog = false;

  private sqlLimitStr = {
    mysql: ' LIMIT 20',
    oracle: ' WHERE ROWNUM <= 20',
    gbase: ' LIMIT 20 OFFSET 0',
    db2: ' FETCH FIRST 10 ROWS ONLY',
    hive: ' limit 0,20',
    hadoopCliHive: ' limit 20',
    habse: ' LIMIT => 20}',
    postgresql: ' LIMIT 20'
  };

  canRunFlag = false;
  // 服务信息
  get resData() {
    return this.comDetailRecord ? this.comDetailRecord.val : {};
  }
  // 服务类型
  get type() {
    return this.resData.resType;
  }
  // 服务ID
  get id() {
    return this.resData.id;
  }
  // 运行结果展示成表格 or 字符串
  get resIsStr() {
    return this.type === 'HBASE' || this.type === 'ELASTICSEARCH';
  }

  get title() {
    return this.type === 'ELASTICSEARCH' ? '文档查询' : '表信息';
  }

  get ishadoopCli() {
    return this.resData?.proxyType === 'HADOOP_CLI';
  }
  get databaseName() {
    try {
      const target = JSON.parse(this.resData.resProperty);
      return target?.databaseName || '';
    } catch {
      return '';
    }
  }

  // sql语句为空时的提示文案
  get sourceTitle() {
    const titleMap = {
      JDBC: '请选择左侧表名或表字段，最多查询20条数据',
      HIVE: '请点击左侧表名或表字段，最多查询20条数据',
      HBASE: '请点击左侧表名查询，最多查询20条数据',
      ELASTICSEARCH: '请点击左侧index查询，最多查询20条数据'
    };
    return titleMap[this.resData.resType];
  }

  created() {
    this.getTables();
  }

  // 打开字段选择弹窗
  showFields(row) {
    this.showFieldDialog = true;
    this.selectTableName = row.name;
  }

  // 字段选择回调
  handlefieldConfirm(fields: string) {
    fields && this.getSql(fields);
  }

  // 选择表
  rowClick(row) {
    this.selectTableName = row.name;
    this.resultColumnData = [];
    this.sqlResult = '';
    this.getSql('*');
  }

  canRun(value) {
    this.canRunFlag = !!value;
  }

  // 生成sql查询语句
  getSql(fields) {
    this.sql = 'SELECT ' + fields + ' FROM ' + this.selectTableName;
    if (this.type === 'HIVE') {
      const databaseName = this.databaseName ? `${this.databaseName}.` : '';
      this.sql = 'SELECT ' + fields + ' FROM ' + databaseName + this.selectTableName;
      this.sql += this.ishadoopCli ? this.sqlLimitStr.hadoopCliHive : this.sqlLimitStr.hive;
    }
    if (this.type === 'JDBC') {
      if (this.resData.url.indexOf('mysql') > 0) {
        this.sql += this.sqlLimitStr.mysql;
      }
      if (this.resData.url.indexOf('oracle') > 0) {
        this.sql += this.sqlLimitStr.oracle;
      }
      if (this.resData.url.indexOf('db2') > 0) {
        this.sql += this.sqlLimitStr.db2;
      }
      if (this.resData.url.indexOf('gbase') > 0) {
        this.sql += this.sqlLimitStr.gbase;
      }
      if (this.resData.url.indexOf('postgresql') > 0) {
        const name = _.split(this.selectTableName, '.');
        this.sql =
          'SELECT ' + fields + ' FROM "' + name[0] + '".' + name[1] + this.sqlLimitStr.postgresql;
      }
    }
    if (this.type === 'HBASE') {
      if (fields === '*') {
        this.sql = `scan '${this.selectTableName}', {${this.sqlLimitStr.habse}`;
      } else {
        let fieldsStr = '';
        fields.split(',').forEach((el) => {
          fieldsStr += `'${el}',`;
        });
        fieldsStr = trimEnd(fieldsStr, ',');
        this.sql = `scan '${this.selectTableName}', {COLUMNS => [${fieldsStr}], ${this.sqlLimitStr.habse}`;
      }
    }

    if (this.type === 'ELASTICSEARCH') {
      if (fields === '*') {
        this.sql = `{"index": "${this.selectTableName}", "from": 0, "size": 20}`;
      } else {
        let fieldsStr = '';
        fields.split(',').forEach((el) => {
          fieldsStr += `"${el}",`;
        });
        fieldsStr = trimEnd(fieldsStr, ',');
        this.sql = `{"index": "${this.selectTableName}", "from": 0, "size": 20, "fields": [${fieldsStr}]}`;
      }
    }
  }

  // 处理分页
  handleCurrentChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    this.getTables();
  }

  searchTable() {
    this.pageData.currentPage = 1;
    this.getTables();
  }
  // 获取表列表
  async getTables() {
    this.tableLoading = true;
    const request = this.getResquest();
    const {
      data: { tableData = [], columnData = [], pageData = {} } = {},
      success,
      msg
    } = (await request(this.id, {
      search: this.search,
      pageData: Object.assign({}, this.pageData, { layout: undefined })
    })) || {};
    this.tableLoading = false;
    if (success) {
      this.tableData = tableData;
      this.columnData = columnData.concat({
        label: '操作',
        value: 'operator',
        width: 60
      });
      this.pageData.total = pageData.total;
      return;
    }
    this.tableData = [];
    this.$message.error(msg);
  }

  // 执行sql语句
  async runSql() {
    this.sql = this.codeRef.getValue();
    this.runSqlLoading = true;
    const params = {
      id: this.id,
      sql: this.sql
    };
    const request = this.getRunRequest();
    const { data, success, msg } = (await request(params)) || {};
    this.runSqlLoading = false;
    if (success) {
      this.resultColumnData = [];
      if (this.resIsStr) {
        this.sqlResult = data === '[]' || !data ? '暂无数据' : data;
      } else {
        (data.headers || []).forEach((el: any) => {
          const upperLength = el.replace(/[a-z]/g, '').length;
          const minWidth = upperLength * 10 + (el.length - upperLength) * 9 + 30;
          this.resultColumnData.push({
            label: el,
            value: el,
            minWidth: minWidth > 50 ? minWidth : 50
          });
        });
        // 处理空值显示-
        data.datas.forEach((item) => {
          Object.keys(item).forEach(
            (key) => (item[key] === undefined || item[key] === '') && (item[key] = '-')
          );
        });
        this.resultTableData = data.datas;
      }
      return;
    }
    this.sqlResult = '';
    this.resultColumnData = [];
    this.resultTableData = [];
    this.$message.error(msg);
  }
  // 根据服务类型获取 对应接口和参数
  getResquest() {
    const requests = {
      JDBC: getDatabaseTable,
      HIVE: getHiveTable,
      HBASE: getHbaseTable,
      ELASTICSEARCH: getEsTable
    };
    return requests[this.type];
  }
  getFieldsRequest() {
    const requests = {
      JDBC: getSqlColumn,
      HIVE: getHiveTableFields,
      HBASE: getHbaseTableFields,
      ELASTICSEARCH: getEsTableFields
    };
    return requests[this.type];
  }
  getRunRequest() {
    const requests = {
      JDBC: runSql,
      HIVE: runHiveSQL,
      HBASE: runHbaseSQL,
      ELASTICSEARCH: runEsSQL
    };
    return requests[this.type];
  }
}
</script>
<style lang="scss" scoped>
.el-scrollbar .el-scrollbar__wrap {
  overflow-x: hidden;
}
.el-tree > .el-tree-node {
  display: inline-block;
  min-width: 100%;
}
.bs-detail__header {
  border-bottom: 1px solid $--bs-color-border-lighter;
}
.content {
  width: 100%;
  height: 643px;
  display: flex;
  padding: 0;
  .left {
    width: 340px;
    flex-shrink: 0;
    border-right: 1px solid $--bs-color-border-lighter;
    padding-top: 10px;
    ::v-deep .bs-table {
      border-top: 1px solid $--bs-color-border-lighter;
    }
  }
  .right {
    flex: 1;
    padding: 10px;
    overflow: hidden;
    .right-table {
      border: 1px solid $--bs-color-border-lighter;
      border-bottom: none;
      margin-top: 10px;
    }
    ::v-deep .el-table thead th.el-table__cell > .cell {
      width: max-content;
      white-space: nowrap;
    }
  }
  .table-result {
    font-weight: bolder;
    &:before {
      content: ' ';
      position: relative;
      left: 0;
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 6px;
      background: #ff9c00;
    }
  }
}
</style>
