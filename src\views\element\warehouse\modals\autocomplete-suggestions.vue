<template>
  <transition name="el-zoom-in-top" @after-leave="doDestroy">
    <div
      v-show="showPopper"
      class="el-autocomplete-suggestion el-popper"
      :class="{ 'is-loading': !parent.hideLoading && parent.loading }"
      :style="{ width: dropdownWidth }"
      role="region"
    >
      <div tag="ul" class="el-autocomplete-suggestion__wrap,el-autocomplete-suggestion__list">
        <li v-if="!parent.hideLoading && parent.loading">
          <i class="el-icon-loading"></i>
        </li>
        <slot v-else> </slot>
      </div>
    </div>
  </transition>
</template>
<script>
import Popper from 'bs-ui-pro/lib/utils/vue-popper';
import Emitter from 'bs-ui-pro/lib/mixins/emitter';

export default {
  mixins: [Popper, Emitter],

  componentName: 'ElAutocompleteSuggestions',

  props: {
    id: String
  },

  data() {
    return {
      parent: this.$parent,
      dropdownWidth: ''
    };
  },

  updated() {
    this.$nextTick(() => {
      return this.popperJS && this.updatePopper();
    });
  },

  mounted() {
    this.$parent.popperElm = this.popperElm = this.$el;
    this.referenceElm =
      this.$parent.$refs.input.$refs.input || this.$parent.$refs.input.$refs.textarea;
    this.referenceList = document.querySelector('.el-autocomplete-suggestion__list');
    // this.referenceList.setAttribute('role', 'listbox');
    // this.referenceList.setAttribute('id', this.id);
  },

  created() {
    this.$on('visible', (val, inputWidth) => {
      this.dropdownWidth = inputWidth + 'px';
      this.showPopper = val;
    });
  },

  methods: {
    select(item) {
      this.dispatch('ElAutocomplete', 'item-click', item);
    }
  }
};
</script>
