import type { CytoscapeOptions } from 'cytoscape';
import style from './style';
export const layout = {
  name: 'cola',
  animate: true,
  maxSimulationTime: 4000,
  ungrabifyWhileSimulating: false,
  fit: true,
  padding: 30,
  nodeDimensionsIncludeLabels: true,
  randomize: true,
  avoidOverlap: true,
  handleDisconnected: true,
  convergenceThreshold: 0.01,
  centerGraph: true,
  edgeLength: 200,
  refresh: 10,
  edgeSymDiffLength: 1000,
  edgeJaccardLength: 1000
};

const config: CytoscapeOptions = {
  style,

  pan: { x: 0, y: 0 },
  pixelRatio: 'auto',
  elements: [],
  // wheelSensitivity: 0.05,
  motionBlur: true,
  motionBlurOpacity: 0.2
};

export default config;
