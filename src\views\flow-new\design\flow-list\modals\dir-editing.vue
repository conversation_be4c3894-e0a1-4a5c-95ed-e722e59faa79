<template>
  <pro-form
    ref="proForm"
    :value="formData"
    :form-items="formItems"
    style="margin-top: 36px"
    :options="{
      labelWidth: '130px'
    }"
    @change="handleChange"
  >
    <bs-cascader
      slot="parentId"
      v-model="formData.parentId"
      :options="dirOptions"
      :props="{ checkStrictly: true, emitPath: false }"
      :disabled="!!id"
      filterable
      popper-class="dir-cascader"
      style="width: 100%"
    >
      <div slot-scope="{ node, data }" class="dir-node">
        <span :title="data.label">{{ data.label }}</span>
        <bs-tag v-if="node.level === 1" style="margin-left: 4px">{{ $t('pa.flow.rDir') }}</bs-tag>
      </div>
    </bs-cascader>
  </pro-form>
</template>
<script lang="ts">
import { addDir, getProjectTree, updateDir } from '@/apis/flowNewApi';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class DirEditing extends Vue {
  @Prop() id!: string;
  @Prop() projectName!: string;
  @Prop() parentId!: string;
  @Prop() projectId!: string;

  dirOptions = [];
  formData: any = {
    projectName: this.projectName,
    parentId: this.parentId || this.projectId
  };
  get level() {
    return this.$store.getters.projectLevelsNumber;
  }
  get formItems() {
    return [
      {
        type: 'input',
        prop: 'projectName',
        label: this.$t('pa.flow.name'),
        componentProps: {
          maxlength: 30,
          placeholder: this.$t('pa.flow.placeholder43')
        },
        rules: [{ required: true, message: this.$t('pa.flow.placeholder7'), trigger: 'blur' }]
      },
      {
        type: 'custom',
        label: this.$t('pa.flow.fDir'),
        prop: 'parentId',
        tooltip: this.$t('pa.flow.msg249', [this.level]),
        rules: [{ required: true, message: this.$t('pa.flow.placeholder44'), trigger: 'blur' }]
      }
    ];
  }
  async created() {
    // 获取项目下的目录树
    const { data = {} } = await getProjectTree({ rootProjectId: this.projectId, filterLast: true });
    const transform = (data: any[]) => {
      return (Array.isArray(data) ? data : []).map(({ nodeId, nodeName, children }) => ({
        value: nodeId,
        label: nodeName,
        children: Array.isArray(children) && children.length ? transform(children) : undefined
      }));
    };
    this.dirOptions = transform([data]);
  }
  handleChange(data) {
    this.formData = data;
  }
  // 配合表单确认按钮执行方法
  public confirm(done) {
    (this.$refs.proForm as any).validate(async (vaild) => {
      if (!vaild) return done(false);
      // 新建目录和新建项目共用接口
      const { projectName, parentId } = this.formData;
      const req = this.id ? updateDir : addDir;
      const { success, msg } = await req({ id: this.id, projectName, parentId });
      this.$message[success ? 'success' : 'error'](msg);
      done(success);
    });
  }
}
</script>

<style lang="scss" scoped>
.dir-node {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 200px;
  & > span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
