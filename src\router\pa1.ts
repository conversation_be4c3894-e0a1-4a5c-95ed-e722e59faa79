const router = [
  {
    path: '/dataAccess',
    name: 'dataAccess',
    meta: {
      title: '一代数据接入'
      // access: 'PA.ORG_AST.MENU'
    },
    children: [
      {
        path: 'daemon',
        name: '代理管理',
        meta: {
          access: 'PA.ELE.SERVICE.DAEMON.MENU', // 权限信息
          resType: 'DAEMON'
        },
        component: () => import('../views/dataAccess/daemon/index.vue')
      }
    ]
  }
];

export { router };
