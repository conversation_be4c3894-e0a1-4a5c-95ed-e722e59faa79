/*
 * @Description: 监控管理路由
 * @Author: ranran
 * @Date: 2020-04-13 10:17:23
 * @LastEditTime: 2020-04-19 15:27:54
 * @LastEditors: ranran
 */

const router = [
  {
    path: '/monitor',
    name: 'monitor',
    meta: { access: 'PA.MONITOR', icon: 'iconfont icon-ji<PERSON><PERSON><PERSON><PERSON><PERSON>', title: '智能监控' },
    component: () => import('../views/monitor/index.vue'),
    children: [
      {
        path: '/monitor/service',
        name: 'monitorService',
        meta: {
          access: 'PA.MONITOR.SERVICE.MENU',
          title: '服务监控'
        },
        component: () => import('../views/monitor/service/index.vue')
      },
      {
        path: '/monitor/clusters/:type',
        name: 'monitorClusters',
        beforeEnter: (to, from, next) => {
          // 获取动态路由标题
          to.meta.title = to.query.title;
          to.meta.resType = to.query.type;
          next();
        },
        component: () => import('@/views/monitor/service/custom/index.vue')
      },
      {
        path: '/monitor/flow',
        name: 'flowMonitor',
        meta: {
          access: 'PA.MONITOR.FLOW.MENU',
          title: '流程监控'
        },
        component: () => import('../views/monitor/flow/index.vue')
      },
      {
        path: '/monitor/warningRule',
        name: 'monitorWarningRule',
        meta: {
          access: 'PA.MONITOR.WARN.MENU',
          title: '监控预警'
        },
        component: () => import('../views/monitor/warning-rule/index.vue')
      },
      {
        path: '/monitor/flow/detail',
        name: 'flowMonitorDetail',
        beforeEnter: (to, from, next) => {
          to.meta.title = '流程详情：' + to.query.title;
          next();
        },
        component: () => import('../views/flow-new/design/index.vue')
      }
    ]
  }
];
export { router };
