<template>
  <bs-dialog
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="title"
    :visible.sync="visible"
    size="medium"
    :before-close="closeDialog"
  >
    <el-form
      ref="ruleForm"
      v-loading="formLoading"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
      :element-loading-text="$t('pa.tip.loading')"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('pa.encoding')" prop="code">
            <el-input v-model="formData.code" :placeholder="$t('pa.encodingPlaceholder')" maxlength="50" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('pa.parentEncoding')" prop="parentCode">
            <el-input
              v-model="formData.parentCode"
              :placeholder="$t('pa.parentEncodingPlaceholder')"
              disabled
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item :label="$t('pa.name')" prop="title">
          <el-input v-model="formData.title" :placeholder="$t('pa.placeholder.name')" maxlength="30" show-word-limit />
        </el-form-item>
      </el-row>
      <el-form-item :label="$t('pa.value1')">
        <bs-code
          v-if="isIncodes"
          ref="bsCode1"
          :value="formData.value1"
          :extra-style="{ height: '300px' }"
          :read-only="false"
        />
        <el-input
          v-else
          v-model="formData.value1"
          type="textarea"
          row="5"
          :placeholder="$t('pa.value1Placeholder')"
          maxlength="2048"
        />
      </el-form-item>
      <el-form-item v-if="!isIncodes" :label="$t('pa.value2')">
        <el-input
          v-model="formData.value2"
          type="textarea"
          row="5"
          :placeholder="$t('pa.value2Placeholder')"
          maxlength="2048"
        />
      </el-form-item>
      <el-form-item v-if="!isIncodes" :label="$t('pa.value3')">
        <el-input
          v-model="formData.value3"
          type="textarea"
          row="5"
          :placeholder="$t('pa.value3Placeholder')"
          maxlength="2048"
        />
      </el-form-item>
      <el-form-item v-if="!isIncodes" :label="$t('pa.value4')">
        <el-input
          v-model="formData.value4"
          type="textarea"
          row="5"
          :placeholder="$t('pa.value4Placeholder')"
          maxlength="2048"
        />
      </el-form-item>
      <el-form-item :label="$t('pa.notes')">
        <el-input
          v-model="formData.memo"
          type="textarea"
          row="5"
          :placeholder="$t('pa.placeholder.notesPlaceholder')"
          maxlength="255"
        />
      </el-form-item>
    </el-form>
    <el-alert :title="$t('pa.systemPlaceholder')" center type="success" :description="$t('pa.systemPath')" />
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">{{ $t('pa.action.close') }}</el-button>
      <el-button type="primary" :loading="loading" @click="submit('ruleForm')">{{ $t('pa.action.makeSure') }}</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Watch, Emit } from 'vue-property-decorator';
import { URL_DIC_ADD, URL_DIC_UPDATE } from '@/apis/commonApi';
import { cloneDeep } from 'lodash';
import { PaBase } from '@/common/pipeace-base';
import { post, put } from '@/apis/utils/net';
import i18n from '@/i18n';
@Component
export default class AddEdit extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: i18n.t('pa.action.add') }) title!: string;
  @Prop({ default: {} }) data!: any;
  @Prop({ default: false }) formLoading!: boolean;

  formData: any = {};
  loading = false;

  rules: any = {
    code: [
      { required: true, message: this.$t('pa.encodingPlaceholder'), trigger: 'blur' },
      {
        validator: this.validSameCode,
        trigger: 'blur'
      }
    ],
    title: [
      { required: true, message: this.$t('pa.placeholder.name'), trigger: 'blur' },
      { min: 2, max: 30, message: this.$t('pa.length2to30'), trigger: 'blur' }
    ]
  };
  codes = ['tableHead'];
  get isIncodes() {
    return this.codes.includes(this.formData.parentCode);
  }
  validSameCode(rule: any, value: any, callback: any) {
    if (this.formData.code === this.formData.parentCode) {
      callback(new Error(this.$t('pa.differentEncoding')));
    } else {
      callback();
    }
  }
  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  private closeDialog(needFresh: boolean) {
    this.formData = {};
    this.loading = false;
    this.formLoading = false;
  }

  submit(formName: string) {
    if (this.isIncodes) {
      for (let i = 1; i < 5; i++) {
        this.formData['value' + i] = this.$refs['bsCode' + i] && (this.$refs['bsCode' + i] as any).getValue();
      }
    }
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        this.loading = true;
        if (this.formData.id) {
          put(URL_DIC_UPDATE, this.formData).then((resp: any) => {
            this.parseResp(resp);
          });
        } else {
          post(URL_DIC_ADD, this.formData).then((resp: any) => {
            this.parseResp(resp);
          });
        }
      } else {
        this.$message.error(this.$t('pa.tip.checkMessage'));
        return false;
      }
    });
  }

  parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.closeDialog(true);
    });
  }

  @Watch('data')
  onDataChange() {
    this.formData = cloneDeep(this.data);
  }
}
</script>
