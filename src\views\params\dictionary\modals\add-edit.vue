<template>
  <bs-dialog
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="title"
    :visible.sync="visible"
    size="medium"
    :before-close="closeDialog"
  >
    <el-form
      ref="ruleForm"
      v-loading="formLoading"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
      element-loading-text="数据正在加载中..."
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="编码" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入编码"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上级编码" prop="parentCode">
            <el-input
              v-model="formData.parentCode"
              placeholder="请输入上级编码"
              disabled
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item label="名称" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="请输入名称"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
      </el-row>
      <el-form-item label="值1">
        <bs-code
          v-if="isIncodes"
          ref="bsCode1"
          :value="formData.value1"
          :extra-style="{ height: '300px' }"
          :read-only="false"
        />
        <el-input
          v-else
          v-model="formData.value1"
          type="textarea"
          row="5"
          placeholder="请输入值1内容"
          maxlength="2048"
        />
      </el-form-item>
      <el-form-item v-if="!isIncodes" label="值2">
        <el-input
          v-model="formData.value2"
          type="textarea"
          row="5"
          placeholder="请输入值2内容"
          maxlength="2048"
        />
      </el-form-item>
      <el-form-item v-if="!isIncodes" label="值3">
        <el-input
          v-model="formData.value3"
          type="textarea"
          row="5"
          placeholder="请输入值3内容"
          maxlength="2048"
        />
      </el-form-item>
      <el-form-item v-if="!isIncodes" label="值4">
        <el-input
          v-model="formData.value4"
          type="textarea"
          row="5"
          placeholder="请输入值4内容"
          maxlength="2048"
        />
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model="formData.memo"
          type="textarea"
          row="5"
          placeholder="请输入备注"
          maxlength="255"
        />
      </el-form-item>
    </el-form>
    <el-alert
      title="系统内置占位符"
      center
      type="success"
      description="安装路径的占位符${install_dir_key},端口的占位符${port_key}"
    />
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" :loading="loading" @click="submit('ruleForm')">确 定</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Watch, Emit } from 'vue-property-decorator';
import { URL_DIC_ADD, URL_DIC_UPDATE } from '@/apis/commonApi';
import { cloneDeep } from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component
export default class AddEdit extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: '新建' }) title!: string;
  @Prop({ default: {} }) data!: any;
  @Prop({ default: false }) formLoading!: boolean;

  formData: any = {};
  loading = false;

  rules: any = {
    code: [
      { required: true, message: '请输入编码', trigger: 'blur' },
      {
        validator: this.validSameCode,
        trigger: 'blur'
      }
    ],
    title: [
      { required: true, message: '请输入名称', trigger: 'blur' },
      { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
    ]
  };
  codes = ['tableHead'];
  get isIncodes() {
    return this.codes.includes(this.formData.parentCode);
  }
  validSameCode(rule: any, value: any, callback: any) {
    if (this.formData.code === this.formData.parentCode) {
      callback(new Error('编码和上级编码不能相同'));
    } else {
      callback();
    }
  }
  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  private closeDialog(needFresh: boolean) {
    this.formData = {};
    this.loading = false;
    this.formLoading = false;
  }

  submit(formName: string) {
    if (this.isIncodes) {
      for (let i = 1; i < 5; i++) {
        this.formData['value' + i] =
          this.$refs['bsCode' + i] && (this.$refs['bsCode' + i] as any).getValue();
      }
    }
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        this.loading = true;
        if (this.formData.id) {
          this.doPut(URL_DIC_UPDATE, this.formData).then((resp: any) => {
            this.parseResp(resp);
          });
        } else {
          this.doPost(URL_DIC_ADD, this.formData).then((resp: any) => {
            this.parseResp(resp);
          });
        }
      } else {
        this.$message.error('请检查输入内容');
        return false;
      }
    });
  }

  parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.closeDialog(true);
    });
  }

  @Watch('data')
  onDataChange() {
    this.formData = cloneDeep(this.data);
  }
}
</script>
