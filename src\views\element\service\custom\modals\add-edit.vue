<template>
  <bs-dialog
    :title="title"
    :visible="visible"
    size="medium"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
  >
    <div style="margin-bottom: 50px">
      <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="110px">
        <el-alert
          v-show="dependTextShow"
          title="当前资源存在引用关系，修改后可能会受到影响"
          type="success"
        />
        <el-form-item
          v-for="(formItem, index) in formConfig.forms"
          v-show="show(formItem)"
          :key="index"
          :ref="formItem.model"
          :label="formItem.label"
          :prop="formItem.model"
          style="margin-top: 20px"
        >
          <div v-if="show(formItem)" style="display: flex">
            <div style="width: 90%">
              <el-select
                v-if="formItem.type === 'select'"
                v-model="formData[formItem.model]"
                style="width: 100%"
                :placeholder="formItem.placeholder"
                clearable
                :multiple="formItem.componentCfg.multiple"
                :filterable="formItem.componentCfg.filterable"
                :disabled="disableItem(formItem)"
                @change="selChangeHandle($event, formItem)"
                @visible-change="visibleChangeHandle($event, formItem)"
              >
                <span v-if="!formItem.componentCfg.groupable">
                  <el-option
                    v-for="selItem in formItem.componentCfg.options"
                    :key="selItem[formItem.componentCfg.valueField]"
                    :label="selItem[formItem.componentCfg.labelField]"
                    :value="selItem[formItem.componentCfg.valueField]"
                  />
                </span>
                <span v-if="formItem.componentCfg.groupable">
                  <el-option-group
                    v-for="group in formItem.componentCfg.options"
                    :key="group.label"
                    :label="group.label"
                  >
                    <el-option
                      v-for="selItem in group.options"
                      v-show="selItem[formItem.componentCfg.valueField] !== ''"
                      :key="selItem[formItem.componentCfg.valueField]"
                      :label="selItem[formItem.componentCfg.labelField]"
                      :value="selItem[formItem.componentCfg.valueField] + getOptionId(selItem)"
                    />
                  </el-option-group>
                </span>
              </el-select>
              <el-input
                v-if="formItem.type === 'input'"
                v-model="formData[formItem.model]"
                style="width: 100%"
                :placeholder="formItem.placeholder"
                :maxlength="formItem.maxLength"
                :readonly="formItem.readonly"
                :clearable="true"
                show-word-limit
                :disabled="disableItem(formItem)"
              />
              <el-input-number
                v-if="formItem.type === 'number'"
                v-model="formData[formItem.model]"
                style="width: 100%"
                :placeholder="formItem.placeholder"
                :readonly="formItem.readonly"
                number
                :min="formItem.min"
                :max="formItem.max"
                :disabled="disableItem(formItem)"
              />
              <el-input
                v-if="formItem.type === 'password'"
                v-model="formData[formItem.model]"
                style="width: 100%"
                type="password"
                :placeholder="formItem.placeholder"
                :maxlength="formItem.maxLength"
                :readonly="formItem.readonly"
                :clearable="true"
                show-word-limit
                :disabled="disableItem(formItem)"
              />
              <el-input
                v-if="formItem.type === 'textarea'"
                v-model="formData[formItem.model]"
                style="width: 100%"
                type="textarea"
                :placeholder="formItem.placeholder"
                :maxlength="formItem.maxLength"
                :readonly="formItem.readonly"
                :autosize="{ minRows: formItem.rows, maxRows: formItem.rows }"
                :disabled="disableItem(formItem)"
              />
              <el-autocomplete
                v-if="formItem.type === 'autocomplete'"
                v-model="formData[formItem.model]"
                style="width: 100%"
                :placeholder="formItem.placeholder"
                :maxlength="formItem.maxLength"
                :readonly="formItem.readonly"
                :fetch-suggestions="querySearchHandle"
                :clearable="true"
                :placement="formItem.componentCfg.placement"
                :disabled="disableItem(formItem)"
                @focus="inputFocusHandle(formItem)"
              >
                <template slot-scope="{ item }">
                  <span style="font-weight: bold">{{ item.value }}</span>
                  <span style="float: right">
                    {{ item[formItem.componentCfg.labelField] }}
                  </span>
                </template>
              </el-autocomplete>
              <el-select
                v-if="formItem.type === 'selectByInput'"
                v-model="formData[formItem.model]"
                style="width: 100%"
                :placeholder="formItem.placeholder"
                clearable
                :multiple="formItem.componentCfg.multiple"
                :filterable="formItem.componentCfg.filterable"
                :disabled="disableItem(formItem)"
              >
                <el-option
                  v-for="selItem in input"
                  :key="selItem[formItem.componentCfg.valueField]"
                  :value="selItem[formItem.componentCfg.valueField]"
                />
              </el-select>
            </div>
            <div>
              <el-tooltip
                v-if="formItem.desc !== undefined"
                class="item"
                effect="light"
                :content="formItem.desc"
                placement="bottom"
              >
                <span class="iconfont icon-wenhao" style="marginleft: 5px"></span>
              </el-tooltip>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" :loading="loading" @click="submit('ruleForm')">确 定</el-button>
      <el-button
        v-if="isHost || this.formData.belongType === 'REG'"
        type="primary"
        @click="testConnect('ruleForm')"
      >
        测试连接
      </el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import * as _ from 'lodash';
import { Base64 } from 'js-base64';
import { PaBase } from '@/common/pipeace-base';
import {
  URL_HOST_UPDATE,
  URL_HOST_ADD,
  URL_RES_ADD,
  URL_RES_UPDATE,
  URL_HAS_RELATION_LIST,
  URL_HOST_FINDDEPENDS,
  URL_RES_TESTCONNECT,
  URL_HOST_TESTCONNECT
} from '@/apis/commonApi';
const _PRIMARY_ID = '_primaryId';
import { post, put } from '@/apis/utils/net';
@Component
export default class AddEdit extends PaBase {
  @Prop() visible!: boolean;
  @Prop() formData!: any; // formData
  @Prop({ default: () => ({ forms: [] }) }) formConfig!: any; // 服务注册还是内置
  @Prop() operation!: 'ADD' | 'EDIT'; // 新建还是编辑
  rules: any = {};
  loading = false;
  dependTextShow = false;
  get isHost() {
    return this.formData && this.formData.resType === 'HOST';
  }
  get title() {
    return this.operation === 'ADD' ? (this.isHost ? '新建' : '注册') : '编辑';
  }
  created() {
    if (this.operation === 'EDIT') {
      if (this.formData.resType === 'HOST') {
        this.findHostDepends(this.formData.title, this.formData.resType);
      } else {
        this.findResDepends(this.formData.resType, this.formData.id);
      }
      try {
        const resProperty = JSON.parse(this.formData.resProperty);
        for (const p of Object.keys(resProperty)) {
          this.$set(this.formData, p, resProperty[p]);
        }
      } catch (e) {
        // do nothing
      }
    }
    // 获取formConf
    if (this.formConfig && this.formConfig.forms) {
      this.formConfig.forms.forEach((n) => {
        // 填充校验规则
        if (n.rules !== undefined && n.rules.length) {
          n.rules.forEach((r) => {
            if (r.pattern) {
              r.pattern = eval(r.pattern);
            }
            if (r.validator) {
              r.validator = eval(r.validator);
            }
          });
          this.$set(this.rules, n.model, n.rules);
        }
        // 设置默认值
        if (this.operation === 'ADD') {
          if (this.formData[n.model] === undefined) {
            if (n.defaultVal !== undefined) {
              this.$set(this.formData, n.model, n.defaultVal);
            } else if (n.type === 'select' && n.componentCfg.multiple) {
              this.$set(this.formData, n.model, []);
            }
          }
        } else {
          // 编辑模式下，password类型的进行解密
          if (n.type === 'password') {
            this.formData[n.model] = Base64.decode(this.formData[n.model] || '');
          }
        }
        // 初始化
        // 若select未选择，如果存在switchEl配置需要隐藏对应的文本框
        if (n.type === 'select') {
          const selVal = _.toString(this.formData[n.model]);
          if (selVal === '') {
            this.hideAllEl(n);
          } else {
            this.hideEl(n, selVal);
          }
        }
      });
    }
    this.$nextTick(() => {
      const form: any = this.$refs.ruleForm;
      if (form !== undefined) {
        form.clearValidate();
      }
    });
  }
  selChangeHandle(val2, item) {
    // 设置一个变量用于存放下拉框数据的id
    const array = _.split(val2, ',');
    let val = '';
    if (array.length === 2) {
      const idModel = item.model + _PRIMARY_ID;
      // 格式:数据值,id
      this.formData[idModel] = val2;
      val = array[1];
    } else {
      val = array[0];
    }
    if (item.componentCfg.fill.length > 0) {
      const p = {};
      if (array.length === 2) {
        const _ID = 'id';
        p[_ID] = val;
      } else {
        p[item.componentCfg.valueField] = val;
      }
      let data = [];
      if (item.componentCfg.groupable) {
        item.componentCfg.options.forEach((o) => {
          data = _.union(data, o.options);
        });
      } else {
        data = item.componentCfg.options;
      }
      const rec = _.find(data, p);
      if (rec !== undefined) {
        item.componentCfg.fill.forEach((n) => {
          this.$set(this.formData, n.toModel, rec[n.fromField]);
        });
        // 显示和隐藏
        this.hideEl(item, val);
      }
    } else {
      this.hideEl(item, val);
    }
    // 查询其他文本框是否依赖于当前下拉框，数据重置
    this.formConfig.forms.forEach((n) => {
      if (n.componentCfg && n.componentCfg.dependsOn) {
        n.componentCfg.dependsOn.forEach((m) => {
          if (m === item.model && item.model !== n.model) {
            delete this.formData[n.model];
          }
        });
      }
    });
  }

  findHostDepends(title: string, resType: string) {
    if (!resType) {
      return;
    }
    this.doGet(URL_HOST_FINDDEPENDS, {
      params: {
        resType: resType,
        title
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.dependTextShow = resp.data.tableData.length > 0;
      });
    });
  }
  findResDepends(resType: string, id: string) {
    if (!resType) {
      return;
    }
    this.dependTextShow = false;
    this.doGet(`${URL_HAS_RELATION_LIST}?id=${id}`).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.dependTextShow = resp.data > 0;
      });
    });
  }
  getSaveUrl() {
    if (this.formData.resType === 'HOST') {
      return this.operation === 'ADD' ? URL_HOST_ADD : URL_HOST_UPDATE;
    } else {
      return this.operation === 'ADD' ? URL_RES_ADD : URL_RES_UPDATE;
    }
  }
  async formValidate(showMsg = false) {
    try {
      return await (this.$refs?.ruleForm as any)?.validate();
    } catch (e) {
      showMsg && this.$message.error('请检查输入内容');
      throw new Error(e);
    }
  }
  getParams() {
    const obj = _.cloneDeep(this.formData);
    this.formConfig.forms.forEach((n) => {
      if (n.type === 'password' && _.toString(obj[n.model]) !== '') {
        obj[n.model] = Base64.encode(obj[n.model]);
      }
    });
    obj.resProperty = JSON.stringify(obj);
    obj.resProperty = JSON.stringify(obj);
    return obj;
  }
  async preValidate(params) {
    const isHost = params.resType === 'HOST';
    const url = isHost ? URL_HOST_TESTCONNECT : URL_RES_TESTCONNECT;
    const { success, data, msg } = await post(url, params);
    if (!success || (data && data.fail > 0)) {
      this.$message.error(`连接失败:${msg}`);
      throw new Error(msg);
    }
  }
  async submit() {
    try {
      await this.formValidate(true);
      this.loading = true;
      const params = this.getParams();
      await this.preValidate(params);
      const fn = this.operation === 'ADD' ? post : put;
      const { success, data, msg } = await fn(this.getSaveUrl(), params);
      if (!success) return this.$message.error(msg);
      this.$message.success(msg);
      this.closeDialog(true);
    } finally {
      this.loading = false;
    }
  }
  submit2(formName: string) {
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        this.loading = true;
        // password类型的进行加密
        const obj = _.cloneDeep(this.formData);
        this.formConfig.forms.forEach((n) => {
          if (n.type === 'password' && _.toString(obj[n.model]) !== '') {
            obj[n.model] = Base64.encode(obj[n.model]);
          }
        });
        obj.resProperty = JSON.stringify(obj);
        if (this.operation === 'ADD') {
          this.doPost(this.getSaveUrl(), obj).then((resp: any) => {
            this.parseResp(resp);
          });
        } else {
          this.doPut(this.getSaveUrl(), obj).then((resp: any) => {
            this.parseResp(resp);
          });
        }
      } else {
        this.$message.error('请检查输入内容');
        return false;
      }
    });
  }
  testConnect(formName: string) {
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        this.$message.info('开始连接测试...');
        // 密码加密
        const obj = _.cloneDeep(this.formData);
        this.formConfig.forms.forEach((n) => {
          if (n.type === 'password' && obj[n.model] !== undefined) {
            obj[n.model] = Base64.encode(obj[n.model]);
          }
        });
        obj.resProperty = JSON.stringify(obj);
        if (this.formData.resType === 'HOST') {
          this.doPost(URL_HOST_TESTCONNECT, obj).then((resp: any) => {
            if (resp.data.success > 0) {
              this.$message.success('连接成功');
            } else {
              this.$message.error('连接失败:' + resp.msg);
            }
          });
        } else {
          this.doPost(URL_RES_TESTCONNECT, obj).then((resp: any) => {
            this.parseResponse(resp, () => {
              const { success, data } = resp || {};
              if (success && data.fail < 1) {
                this.$message.success('连接成功');
              } else {
                const urls = Array.isArray(data.failUrls) ? data.failUrls : [];
                this.$message.error(`${urls.join(', ')}连接失败`);
              }
            });
          });
        }
      }
    });
  }
  parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.closeDialog(true);
    });
  }
  makeReqParam(cfg) {
    const params = {};
    if (cfg.reqParamsField !== undefined) {
      cfg.reqParamsField.forEach((n) => {
        if (n === 'orgId') {
          if (this.formData.orgId === undefined) {
            this.formData.orgId = this.$store.state.userInfo.orgId;
          }
        }
        this.$set(params, n, this.formData[n]);
      });
    }
    return params;
  }
  visibleChangeHandle(event, item) {
    if (event) {
      const cfg = item.componentCfg;
      if (cfg.options.length === 0 && _.toString(cfg.remoteUrl) !== '') {
        // 请求接口
        const params = this.makeReqParam(item.componentCfg);
        this.doGet(cfg.remoteUrl, { params: params }).then((resp: any) => {
          if (resp.success) {
            cfg.options = resp.data;
            // 分组
            if (_.toString(cfg.groupBy) !== '') {
              const groupOpts: any = [];
              const groupBy = cfg.groupBy;
              const groupObj = _.groupBy(cfg.options, groupBy);
              for (const key of Object.keys(groupObj)) {
                if (_.filter(groupObj[key], { subName: '' }).length === 0) {
                  groupOpts.push({
                    label: key,
                    options: groupObj[key]
                  });
                }
              }
              this.$set(cfg, 'options', groupOpts);
              this.$set(cfg, 'groupable', true);
            } else {
              this.$set(cfg, 'groupable', false);
            }
          }
        });
      }
      if (cfg.useInputAndTarget) {
        // 把配置字段作为下拉选项
        const ref: any = this.$refs.NodeFieldList;
        if (ref !== undefined) {
          this.$set(cfg, 'options', ref.getAllField());
        }
      }
      if (cfg.useTarget) {
        // 把配置字段作为下拉选项
        const ref: any = this.$refs.NodeFieldList;
        if (ref !== undefined) {
          this.$set(cfg, 'options', ref.getTableData());
        }
      }
    }
  }
  getOptionId(selItem) {
    if (selItem.id !== undefined) {
      return ',' + selItem.id;
    }
    return '';
  }
  show(item) {
    if (item.show === undefined) {
      return true;
    }
    return item.show;
  }

  private hideEl(item, val) {
    if (item.componentCfg.hideEl && item.componentCfg.hideEl[val] !== undefined) {
      // 先都置为true
      for (const k of Object.keys(item.componentCfg.hideEl)) {
        item.componentCfg.hideEl[k].forEach((el) => {
          const cur: any = _.find(this.formConfig.forms, { model: el });
          this.$set(cur, 'show', true);
          if (cur.rules) {
            cur.rules.forEach((r) => {
              this.$set(r, 'required', true);
            });
          }
        });
      }
      this.hide(item, val);
    }
  }
  private hide(item, val) {
    if (item.componentCfg.hideEl) {
      item.componentCfg.hideEl[val].forEach((el) => {
        const rec: any = _.find(this.formConfig.forms, { model: el });
        if (rec) {
          this.$set(rec, 'show', false);
          // rules设置为失效
          if (rec.rules) {
            rec.rules.forEach((r) => {
              this.$set(r, 'required', false);
            });
          }
          // 找到对应的item，数据清除
          delete this.formData[rec.model];
        }
      });
    }
  }
  private hideAllEl(item) {
    if (item.componentCfg.hideEl) {
      for (const el of Object.keys(item.componentCfg.hideEl)) {
        this.hide(item, el);
      }
    }
  }
  /**
   * 编辑模式下把editable=true设置为不能编辑
   */
  private disableItem(item) {
    if (item.editable === undefined) {
      return false;
    }
    return !item.editable && this.operation === 'EDIT';
  }
  closeDialog(needFresh: boolean) {
    this.$emit('close', needFresh);
  }
}
</script>
