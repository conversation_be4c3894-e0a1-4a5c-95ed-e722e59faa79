<template>
  <div style="height: 100%">
    <div v-loading="divLoading" class="bs-detail-block">
      <div class="bs-detail__header padA20">
        <div class="bs-detail__header-title">图形</div>
        <div class="bs-page__header-operation">
          <el-button size="small" type="primary" @click="getPieData">刷新</el-button>
        </div>
      </div>
      <div class="tab-content">
        <div ref="main" class="tab-content-no-data">暂无数据</div>
      </div>
    </div>
    <div v-loading="divLoading" class="bs-detail-block">
      <div class="bs-detail__header padA20">
        <div class="bs-detail__header-title">节点信息</div>
      </div>
      <div class="tab-content">
        <bs-table :data="nodeData" :column-data="getType(true)" :column-settings="false" />
      </div>
    </div>
    <div v-loading="divLoading" class="bs-detail-block">
      <div class="bs-detail__header padA20">
        <div class="bs-detail__header-title">namespace信息</div>
      </div>
      <div class="tab-content">
        <bs-table :data="nsData" :column-data="getType()" :column-settings="false" />
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject } from 'vue-property-decorator';
import { URL_RES_DETAIL_AEROSPIKE_CACHEINFO } from '@/apis/commonApi';
import echarts from 'echarts';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {}
})
export default class CacheInfo extends PaBase {
  pieData: any[] = [];
  nodeData: any[] = [];
  nsData: any[] = [];
  charts: any;
  divLoading = false;
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  nodeColumns = [
    {
      label: '主机名',
      value: 'host',
      width: 180
    },
    {
      label: '端口',
      value: 'port'
    },
    {
      label: '对象数量(主)',
      value: 'masterObjects',
      width: 120
    },
    {
      label: '淘汰对象',
      value: 'evictedObjects'
    },
    {
      label: '过期对象',
      value: 'expiredObjects'
    },
    {
      label: '总内存',
      value: 'memoryTotalDes'
    },
    {
      label: '内存使用',
      value: 'memoryUsedDes'
    },
    {
      label: '内存剩余',
      value: 'memoryRemainDes'
    },
    {
      label: '读',
      value: 'readCount'
    },
    {
      label: '读错误',
      value: 'readErrorCount'
    },
    {
      label: '写',
      value: 'writeCount'
    },
    {
      label: '写错误',
      value: 'writeErrorCount'
    }
  ];

  getType(type) {
    const list = this.nodeColumns.slice(2);
    return type ? this.nodeColumns : [{ label: '命名空间', value: 'host' }, ...list];
  }

  created() {
    this.getPieData();
  }

  getPieData() {
    this.divLoading = true;
    this.doGet(URL_RES_DETAIL_AEROSPIKE_CACHEINFO, {
      params: {
        id: this.$route.query.id
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.pieData = [];
        this.pieData.push({
          name: '已使用',
          value: resp.data.memoryUsed,
          des: resp.data.memoryUsedDes,
          total: resp.data.memoryTotalDes
        });
        this.pieData.push({
          name: '未使用',
          value: resp.data.memoryRemain,
          des: resp.data.memoryRemainDes,
          total: resp.data.memoryTotalDes
        });
        if (resp.data.allNodes) {
          this.$nextTick(() => {
            this.drawPie('main');
            this.nodeData = resp.data.allNodes;
            this.nsData = resp.data.allNamespace;
          });
        }
      });
      this.divLoading = false;
    });
  }

  /**
   * 画饼
   */
  // eslint-disable-next-line no-unused-vars
  drawPie(id: string) {
    this.charts = echarts.init(this.$refs.main as HTMLCanvasElement);
    this.charts.setOption({
      tooltip: {
        trigger: 'item',
        formatter(params: any) {
          return (
            params.seriesName +
            '</br>' +
            params.data.name +
            ': ' +
            params.data.des +
            '(' +
            params.percent +
            '%)' +
            '</br>总共: ' +
            params.data.total
          );
        }
      },
      legend: {
        orient: 'vertical',
        x: 'left',
        data: ['已使用', '剩余']
      },
      series: [
        {
          name: 'AS',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            normal: {
              show: false,
              position: 'center'
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '30',
                fontWeight: 'bold'
              }
            }
          },
          labelLine: {
            normal: {
              show: false
            }
          },
          data: this.pieData
        }
      ]
    });
  }
}
</script>
<style scoped>
.my-card_body ::v-deep .el-card__body {
  padding: 0px 0px 0px 0px;
}
.my-wrap-row {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
}
.my-wrap-col {
  border: 1px solid #000;
}
.my-wrap-tag {
  width: 350px;
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tab-content {
  font-size: 12px;
  color: #666666;
  border-top: 1px solid #f1f1f1;
}
.tab-content-no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  padding: 20px 20px 0;
  color: #aaa;
}
</style>
