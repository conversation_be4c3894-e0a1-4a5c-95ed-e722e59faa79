<template>
  <bs-dialog
    :width="isEn ? '690px' : '490px'"
    :title="title"
    :visible.sync="visible"
    :before-close="closeDialog"
    confirm-loading
  >
    <pro-form
      ref="ruleForm"
      :value="formData"
      :options="options"
      :form-items="formItems"
      @change="handleChange"
      @item-change="handleItemChange"
    />
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">{{ $t('pa.action.close') }}</el-button>
      <el-button type="primary" :loading="loading" @click="submit">{{ $t('pa.action.makeSure') }}</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Ref } from 'vue-property-decorator';
import Elform from 'bs-ui-pro/lib/form';
import { addCatalog, updateCatalog, getServerType, getServerNameList } from '@/apis/dataApi';
@Component
export default class CatalogAddEdit extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: {} }) catalogInfo!: any;
  @Ref('ruleForm') readonly form!: Elform;

  loading = false;
  options = {
    labelWidth: this.isEn ? 120 : 100
  };
  formData: any = {
    catalogName: '',
    resType: '',
    resId: ''
  };
  resTypeList: any = []; //类型筛选项
  serviceList: any = []; //服务筛选项
  serviceLabelMap = new Map(); //服务为分享撤回的时用到的映射
  get formItems() {
    return [
      {
        type: 'input',
        prop: 'catalogName',
        label: this.$t('pa.cataLog.text1'),
        componentProps: {
          clearable: true,
          maxlength: 30,
          placeholder: this.$t('pa.placeholder.name'),
          disabled: Number(this.formData.relationNum) > 0
        },
        rules: [{ required: true, validator: this.validateName, trigger: 'blur' }]
      },
      {
        type: 'select',
        prop: 'resType',
        label: this.$t('pa.data.udf.detail.type'),
        componentProps: {
          clearable: true,
          placeholder: this.$t('pa.data.udf.detail.placeholder.typePlaceholder'),
          options: this.resTypeList
        },
        rules: [{ required: true, message: this.$t('pa.data.udf.detail.placeholder.typePlaceholder'), trigger: 'change' }]
      },
      {
        type: 'select',
        prop: 'resId',
        label: this.$t('pa.home.service'),
        componentProps: {
          clearable: true,
          placeholder: this.$t('pa.data.table.detail.placeholder.servicePlaceholder1'),
          options: this.serviceList,
          labelMap: this.serviceLabelMap
        },
        rules: [{ required: true, validator: this.validateService, trigger: 'change' }]
      }
    ];
  }

  get title() {
    return this.catalogInfo.id ? this.$t('pa.action.edit') : this.$t('pa.action.register');
  }

  async created() {
    //获取服务类型
    this.getResTypeList();
    if (this.catalogInfo.id) {
      this.formData = { ...this.catalogInfo };
      this.queryService(this.formData.resType);
      this.serviceLabelMap.set(this.catalogInfo.resId, this.catalogInfo.resName);
    }
  }

  // catalog名称校验
  validateName(rule, value, callback) {
    const re = /^[a-zA-Z]{1}\w*$/;
    if (!value) return callback(new Error(this.$t('pa.cataLog.text2')));
    if (value && !re.test(value)) {
      return callback(new Error(this.$t('pa.cataLog.text3')));
    }
    callback();
  }

  //服务判断是否在下拉列表中
  validateService(rule, value, callback) {
    if (!value) return callback(new Error(this.$t('pa.data.table.detail.placeholder.servicePlaceholder1')));
    if (!this.serviceList.map(({ value }) => value).includes(value))
      return callback(new Error(this.$t('pa.data.table.detail.tips.noServePermissions')));
    callback();
  }

  async getResTypeList() {
    const { data, success, msg } = await getServerType();
    if (!success) return this.$message.error(msg);
    const newArray = Array.isArray(data) ? data : [];
    this.resTypeList = newArray.map((value) => ({ value, label: value }));
  }

  // 查询服务
  async queryService(resType: any = false) {
    const { data, success } = await getServerNameList(resType);
    if (!success) return;
    this.serviceList = (Array.isArray(data) ? data : []).map(({ resId: value, resName: label }) => {
      if ((this.catalogInfo || {}).resName === label) {
        this.form.updateModel('resId', value);
      }
      return { value, label };
    });
  }
  //表单切换变动
  handleChange(data) {
    this.formData = data;
  }
  //表单各项切换变动
  handleItemChange(prop, value) {
    if (prop === 'resType') {
      this.form.updateModel('resId', '');
      //清空服务筛选内容
      this.serviceList = [];
      //服务为空时不调用接口
      value && this.queryService(value);
    }
  }
  //关闭弹窗
  closeDialog() {
    this.loading = false;
    this.$emit('update:visible', false);
  }
  //提交
  submit() {
    this.form.validate().then(async (vaild: boolean) => {
      if (!vaild) return;
      try {
        this.loading = true;
        const api = this.catalogInfo.id ? updateCatalog : addCatalog;
        const { success, msg, error } = await api({
          catalogName: this.formData.catalogName,
          resId: this.formData.resId,
          ...(this.catalogInfo.id && { id: this.catalogInfo.id })
        });
        if (!success) return this.$message.error(error);
        this.closeDialog();
        this.$message.success(msg);
        this.$emit('close');
      } finally {
        this.loading = false;
      }
    });
  }
}
</script>

<style scoped></style>
