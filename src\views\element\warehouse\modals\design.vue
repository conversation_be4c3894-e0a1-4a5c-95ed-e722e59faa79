<template>
  <bs-dialog
    title="表单设计"
    :visible.sync="visible"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    style="margin-top: -10vh"
  >
    <div class="form-design-content">
      <div class="code-content">
        <codemirror
          ref="myCmCase"
          class="code-mirror"
          :code="jsonCfg"
          :options="options"
          :origin-code="jsonCfg"
          @saveCode="saveCode"
        />
      </div>
      <div class="params-content">
        <node-form
          ref="NodeForm"
          :data="param.data"
          :job-data="param.jobData"
          :org-id="this.$store.state.userInfo.orgId"
        />
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" @click="transform">转 换</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Emit } from 'vue-property-decorator';
import { cloneDeep } from 'lodash';
import { PaBase } from '@/common/pipeace-base';
const _DEFAULT: any = {
  data: {
    operateType: 'SOURCE',
    formConf: {}
  },
  jobData: {},
  versionComponentList: []
};
@Component({
  components: {
    codemirror: () => import('@/components/codemirror/codemirror.vue'),
    'node-form': () => import('./node-form.vue')
  }
})
export default class EditIcon extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  private options: any = {
    mode: { name: 'javascript', json: true },
    readOnly: false,
    lineNumbers: true,
    line: true,
    indentUnit: 4,
    lineWrapping: true,
    foldGutter: true,
    gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter']
  };
  private jsonCfg = '';
  private param: any = cloneDeep(_DEFAULT);

  format() {
    const beautify = require('js-beautify');
    this.jsonCfg = beautify.js_beautify(this.jsonCfg);
  }
  saveCode(val) {
    this.jsonCfg = val;
  }
  transform() {
    this.format();
    try {
      (this.$refs.NodeForm as any).renderForm(JSON.parse(this.jsonCfg));
    } catch (e) {
      // donothing
    }
  }
  show(val) {
    this.jsonCfg = val;
    this.transform();
  }
  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  private closeDialog(needFresh: boolean) {
    this.jsonCfg = '';
    this.param = cloneDeep(_DEFAULT);
    (this.$refs.NodeForm as any).renderForm(JSON.parse('{}'));
  }
}
</script>

<style scoped lang="scss">
.form-design-content {
  height: 520px;
  display: flex;
  overflow-y: visible;
  .code-content {
    height: 520px;
    width: 40%;
    text-align: left;
    border-right: 10px solid #ffffff;
  }
  .params-content {
    height: 520px;
    width: 60%;
    overflow-y: scroll;
  }
}
</style>
