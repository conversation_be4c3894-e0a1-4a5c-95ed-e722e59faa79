<template>
  <div class="result__container">
    <div class="result-header">测试结果</div>
    <div class="result-content">
      <bs-table
        height="240px"
        :data="data.tableData"
        :column-data="data.columnData"
        :column-settings="false"
        @row-click="rowClick"
      >
        <template slot="operator" slot-scope="{ row }">
          <div>
            <el-tooltip content="结果查看" placement="top" effect="light">
              <i class="iconfont icon-chakan result-content__icon" @click="$emit('view', row)"></i>
            </el-tooltip>
          </div>
        </template>
      </bs-table>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import $ from 'jquery';
@Component
export default class TestResult extends Vue {
  @Prop() data!: any;
  @Prop({ default: true }) isFlinkSql!: boolean;

  rowClick(data) {
    // TODO: 双击结果时，组件高量，需kqq配合
    if (!this.isFlinkSql) {
      const parentRef: any = this.$parent;
      const conns = parentRef.instance.getAllConnections();
      $('.danhua-node').removeClass('danhua-node');
      $('.danhua-link').removeClass('danhua-link');
      this.data.content.nodes.forEach((node) => {
        if (!data.graphPath.includes(node.nodeId)) {
          $('#' + node.nodeId).addClass('danhua-node');
          for (const pointIn of node.pointIn) {
            $(parentRef.instance.getEndpoint(pointIn.uuid).canvas).addClass('danhua-node');
          }
          for (const pointOut of node.pointOut) {
            $(parentRef.instance.getEndpoint(pointOut.uuid).canvas).addClass('danhua-node');
          }
        }
      });
      conns.forEach((conn) => {
        if (!data.graphPath.includes(conn.targetId)) {
          $(conn.canvas.children[1]).addClass('danhua-link');
          $(conn.canvas.children[2]).addClass('danhua-node');
        }
      });
    }
  }
}
</script>
<style lang="scss" scoped>
.result {
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;
    line-height: 32px;
  }
  &-content {
    margin-top: 10px;
    height: calc(100% - 52px);
    border: 1px solid #d4dce2;
    background: #fff;
    &__icon {
      cursor: pointer;
      padding: 0 5px;
    }
  }
}
</style>
