<template>
  <div class="result__container">
    <div class="result-header">{{ $t('pa.flow.testResult') }}</div>
    <div class="result-content">
      <bs-table
        height="240px"
        :data="data.tableData"
        :column-data="data.columnData"
        :column-settings="false"
        @row-click="rowClick"
      >
        <template slot="operator" slot-scope="{ row }">
          <div>
            <el-tooltip :content="$t('pa.flow.resultView')" placement="top" effect="light">
              <i class="iconfont icon-chakan result-content__icon" @click="$emit('view', row)"></i>
            </el-tooltip>
          </div>
        </template>
      </bs-table>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component
export default class TestResult extends Vue {
  @Prop() data!: any;
  @Prop({ default: true }) isFlinkSql!: boolean;
  rowClick(data) {
    if (!this.isFlinkSql && Array.isArray(data.graphPath) && data.graphPath.length) {
      this.$parent.$parent.$emit('highlight', data.graphPath);
    }
  }
}
</script>
<style lang="scss" scoped>
.result {
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;
    line-height: 32px;
  }
  &-content {
    margin-top: 10px;
    height: calc(100% - 52px);
    border: 1px solid #d4dce2;
    background: #fff;
    &__icon {
      cursor: pointer;
      padding: 0 5px;
    }
  }
}
</style>
