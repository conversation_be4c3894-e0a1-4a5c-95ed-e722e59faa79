<template>
  <pro-page v-loading="udfPageLoading" :title="$t('pa.menu.udf')" :fixed-header="false" class="udf-content">
    <!-- 头部搜索、操作项 -->
    <div slot="operation" class="udf-content__header">
      <!-- UDF类型选择 -->
      <el-select
        v-model="udfSearchParams.search.udfType"
        :placeholder="$t('pa.data.udf.placeholder.udfPlaceholder')"
        size="small"
        clearable
        @change="debounceSearch()"
      >
        <el-option v-for="item in udfTypeList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <!-- UDF名、中文名及功能说明搜索 -->
      <bs-search
        v-for="item in headerSearchInputList"
        :key="item.class"
        v-model="udfSearchParams.search[item.model]"
        :placeholder="item.placeholder"
        size="small"
        maxlength="20"
        :class="item.class"
        @input="debounceSearch()"
      />
      <!-- 新建、重置按钮 -->
      <div class="udf-content__header--btns">
        <el-button
          v-for="item in headerButtonList"
          :key="item.label"
          v-access="item.authCode"
          type="primary"
          size="small"
          class="udf-content__header--button"
          @click="item.event"
        >
          {{ item.label }}
        </el-button>
      </div>
    </div>
    <!-- 主体内容（UDF列表） -->
    <main class="udf-content__main">
      <bs-table
        ref="table"
        v-loading="udfTableLoading"
        selection
        :height="selectedList.length ? 'calc(100vh - 344px)' : 'calc(100vh - 288px)'"
        :data="tableData.tableData"
        :column-data="tableData.columnData"
        :page-data="tableData.pageData"
        @row-dblclick="dblclick"
        @page-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
        @refresh="getListData(true)"
      >
        <!-- 头部——UDF批量删除 -->
        <el-button slot="headerOperator" v-access="'PA.DATA.UDF.DELETE'" size="small" @click="delItems()">
          {{ $t('pa.action.del') }}
        </el-button>
        <!-- 引用数量 -->
        <template slot="header-udfReference">
          <div class="udf-content__main--relations">
            <span> {{ $t('pa.data.text1') }} </span>
            <el-tooltip effect="light" :content="$t('pa.data.text2')">
              <i class="iconfont icon-wenhao"></i>
            </el-tooltip>
          </div>
        </template>
        <!-- UDF分享 -->
        <div slot="udfName" slot-scope="{ row }" class="viewName-slot">
          <el-tooltip :content="row.udfName" effect="light" placement="top">
            <span class="viewName">{{ row.udfName }}</span>
          </el-tooltip>
          <el-tag v-if="isShare(row)" size="mini" class="udf-content__tag"> {{ $t('pa.action.share') }} </el-tag>
        </div>
        <template slot="shareFlag" slot-scope="{ row }">
          <span>{{ row.shareFlag ? $t('pa.action.share') : $t('pa.action.buildOneself') }}</span>
        </template>
        <!-- UDF提交状态（已提交/开发中） -->
        <template slot="udfStateName" slot-scope="{ row }">
          <el-tag v-if="isSubmit(row)" size="mini" type="success">{{ $t('pa.data.udf.submit') }}</el-tag>
          <el-tag v-else size="mini" type="danger">{{ $t('pa.status.underDdevelopment') }}</el-tag>
        </template>
        <!-- UDF项操作按钮（编辑、查看、下载、删除、分享） -->
        <template slot="operator" slot-scope="{ row }">
          <el-tooltip
            v-for="item in getButtons(row.shareFlag)"
            :key="item.label"
            v-access="item.authCode"
            :content="item.label"
            effect="light"
            class="udf-content__main--tooltip"
          >
            <i :class="item.class" class="udf-content__main--icon" @click="operateHandler(item.event, row)"></i>
          </el-tooltip>
        </template>
      </bs-table>
    </main>
    <share-dialog
      v-if="showDeptRoleDialog"
      ref="ShareDialog"
      :visible.sync="showDeptRoleDialog"
      :data="shareData"
      type="udf"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { URL_UDF_LIST, URL_UDF_DOWLOAD } from '@/apis/commonApi';
import { delItems } from '@/apis/udfApi';
import { cloneDeep, debounce } from 'lodash';
import { post, download } from '@/apis/utils/net';
import CommonDelete from '@/utils/mixins/common-delete';
import dayjs from 'dayjs';
import { hasPermission } from '@/utils';

@Component({
  components: {
    ShareDialog: () => import('@/components/share-dialog/index.vue')
  },
  mixins: [CommonDelete]
})
export default class UDFManage extends Vue {
  private udfTableLoading = false;
  private udfTypeList = [
    { value: 'UDF', label: 'UDF' },
    { value: 'UDAF', label: 'UDAF' },
    { value: 'UDTF', label: 'UDTF' },
    { value: 'ASYNCUDF', label: 'ASYNCUDF' }
  ];
  private udfSearchParams = {
    pageData: { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 },
    search: {
      udfType: '',
      udfName: '',
      udfExplain: ''
    }
  };
  private tableKey = Date.now();
  private tableData: ITableData = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  private udfTableConfigButtons = [
    {
      label: this.$t('pa.action.edit'),
      class: 'iconfont icon-bianji',
      event: 'goEdit',
      authCode: 'PA.DATA.UDF.EDIT'
    },
    {
      label: this.$t('pa.action.view'),
      class: 'iconfont icon-chakan',
      event: 'viewDetail',
      authCode: 'PA.DATA.UDF.VIEW_DETAIL',
      shareFlag: true
    },
    {
      label: this.$t('pa.action.download'),
      class: 'iconfont icon-xiazai',
      event: 'download',
      authCode: 'PA.DATA.UDF.DOWN',
      shareFlag: true
    },
    {
      label: this.$t('pa.action.del'),
      class: 'iconfont icon-shanchu',
      event: 'delItems',
      authCode: 'PA.DATA.UDF.DELETE'
    },
    {
      label: this.$t('pa.action.share'),
      class: 'iconfont icon-fenxiang',
      event: 'shareUdf',
      authCode: 'PA.DATA.UDF.SHARE'
    }
  ];
  // 批量删除选中UDF
  private selectedList: any[] = [];
  private showDeptRoleDialog = false;
  private shareData: any = {};
  private debounceSearch = debounce(this.getListData, 500);
  // 全屏Loading，下载UDF时触发
  private udfPageLoading = false;
  private headerSearchInputList = [
    {
      model: 'udfName',
      placeholder: this.$t('pa.data.udf.placeholder.udfNamePlaceholder'),
      class: 'udf-content__header--name'
    },
    {
      model: 'udfExplain',
      placeholder: this.$t('pa.data.udf.placeholder.udfExplainPlaceholder'),
      class: 'udf-content__header--explain'
    }
  ];
  private headerButtonList = [
    {
      authCode: 'PA.DATA.UDF.ADD',
      event: this.goEdit,
      label: this.$t('pa.data.udf.addUdf')
    }
  ];

  created() {
    this.getListData();
  }

  activated() {
    (this.$refs.table as any).$children[1].doLayout();
  }
  getButtons(shareFlag) {
    return shareFlag ? this.udfTableConfigButtons.filter((item) => item.shareFlag) : this.udfTableConfigButtons;
  }
  handleCurrentChange(currentPage, pageSize) {
    this.udfSearchParams.pageData.currentPage = currentPage;
    this.udfSearchParams.pageData.pageSize = pageSize;
    this.getListData();
  }

  // UDF提交状态
  isSubmit({ udfStateName }) {
    return udfStateName === '已提交';
  }

  // UDF是否被分享
  isShare({ shareFlag }) {
    return shareFlag;
  }

  // 下载UDF
  async download({ id }) {
    this.udfPageLoading = true;
    try {
      await download(`${URL_UDF_DOWLOAD}?id=${id}`, '', null);
    } finally {
      this.udfPageLoading = false;
    }
  }

  // UDF操作按钮触发
  operateHandler(event, row) {
    this[event](row);
  }

  shareUdf(row) {
    this.shareData = row;
    this.showDeptRoleDialog = true;
  }

  // UDF删除及删除校验
  delItems(row: any) {
    const ids = !row ? this.selectedList : { id: row.id, name: row.udfName, reference: row.udfReference };
    this['commonDel'](ids, async (delIds) => {
      const { success, msg, error } = await delItems(delIds);
      if (success) {
        this.getListData();
        this.$message.success(msg);
      } else {
        this.$message.error(error);
      }
    });
  }

  dblclick(row) {
    if (!hasPermission('PA.DATA.UDF.VIEW_DETAIL')) return;
    this.$router.push({
      path: '/data/udfEdit',
      query: { id: row.id, status: 'detail', title: `UDF：${row.udfName}` }
    });
  }

  // 查看详情
  viewDetail(row: any) {
    this.$router.push({
      path: '/data/udfEdit',
      query: { id: row.id, status: 'detail', title: `UDF：${row.udfName}` }
    });
  }

  // 编辑UDF
  goEdit(row: any) {
    const query = row.id
      ? { id: row.id, status: 'edit', title: `UDF：${row.udfName}` }
      : { status: 'add', title: this.$t('pa.data.udf.addUdf') };
    this.$router.push({
      path: '/data/udfEdit',
      query
    });
  }

  async getListData(refresh = false) {
    this.udfTableLoading = true;
    const udfSearchParams = cloneDeep(this.udfSearchParams);
    udfSearchParams.search.udfName = udfSearchParams.search.udfName.trim();
    udfSearchParams.search.udfExplain = udfSearchParams.search.udfExplain.trim();
    const searchParams = {
      pageData: {},
      search: '',
      sortData: { updateTime: 'DESC' }
    };
    searchParams.pageData = udfSearchParams.pageData;
    searchParams.search = JSON.stringify(udfSearchParams.search);
    try {
      const { success, msg, data } = await post(URL_UDF_LIST, searchParams);
      if (!success) throw msg;
      const { columnData, tableData, pageData } = data;
      columnData.forEach((el) => {
        if (el.value === 'udfName') {
          el.width = 160;
          el.showOverflowTooltip = false;
        }
        if (el.value === 'udfStateName') el.width = 160;
        if (el.value === 'udfReference' && this.isEn) el.width = 200;
      });
      columnData.push({ label: this.$t('pa.action.action'), value: 'operator', fixed: 'right', minWidth: '180' });
      tableData.forEach((el) => {
        el.updateTime = dayjs(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
      });
      this.tableData = {
        columnData,
        tableData,
        pageData
      };
      refresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
      this.udfTableLoading = false;
    } catch (err) {
      this.$tip.error({ message: err, duration: 5000 });
      this.udfTableLoading = false;
    }
  }

  // 批量删除选择
  handleSelectionChange(selections: any) {
    this.selectedList = [];
    selections.forEach((m) => {
      this.selectedList.push({ id: m.id, name: m.udfName, reference: m.udfReference });
    });
  }

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // 通过 `vm` 访问组件实例
      if (from.name === 'udfEdit') {
        vm.getListData();
      }
    });
  }
}
</script>
<style scoped lang="scss">
.viewName-slot {
  display: flex;
  align-items: center;
}
.viewName {
  display: inline-block;
  max-width: 180px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.udf-content {
  height: calc(100vh - 114px);
  background: #fff;
  &__tag {
    margin-left: 8px;
  }
  &__header {
    height: 50px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    &--btns {
      text-align: right;
      margin-right: -20px;
    }
    &--name,
    &--explain {
      // width: 270px !important;
      margin-left: 10px;
    }
    &--explain {
      margin-right: 10px;
    }
  }
  &__main {
    background: #fff;
    &--relations {
      display: flex;
      align-items: center;
      .icon-wenhao {
        margin-left: 5px;
      }
    }
    &--tooltip {
      cursor: pointer;
    }
    &--icon {
      margin: 0 5px;
    }
  }
}
</style>
