<template>
  <pro-page v-loading="udfPageLoading" title="UDF管理" :fixed-header="false" class="udf-content">
    <!-- 头部搜索、操作项 -->
    <div slot="operation" class="udf-content__header">
      <!-- UDF类型选择 -->
      <el-select
        v-model="udfSearchParams.search.udfType"
        placeholder="请选择UDF类型"
        size="small"
        clearable
        @change="debounceSearch"
      >
        <el-option
          v-for="item in udfTypeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <!-- UDF名、中文名及功能说明搜索 -->
      <bs-search
        v-for="item in headerSearchInputList"
        :key="item.class"
        v-model="udfSearchParams.search[item.model]"
        :placeholder="item.placeholder"
        size="small"
        maxlength="20"
        :class="item.class"
        @input="debounceSearch"
      />
      <!-- 新建、重置按钮 -->
      <div class="udf-content__header--btns">
        <el-button
          v-for="item in headerButtonList"
          :key="item.label"
          v-access="item.authCode"
          type="primary"
          size="small"
          class="udf-content__header--button"
          @click="item.event"
        >
          {{ item.label }}
        </el-button>
      </div>
    </div>
    <!-- 主体内容（UDF列表） -->
    <main class="udf-content__main">
      <bs-table
        ref="table"
        v-loading="udfTableLoading"
        selection
        height="calc(100vh - 300px)"
        :data="tableData.tableData"
        :column-data="tableData.columnData"
        :page-data="tableData.pageData"
        @row-dblclick="dblclick"
        @page-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
        @refresh="getListData"
      >
        <!-- 头部——UDF批量删除 -->
        <el-button
          slot="headerOperator"
          v-access="'PA.DATA.UDF.DELETE'"
          size="small"
          @click="delItems()"
        >
          删除
        </el-button>
        <!-- UDF分享 -->
        <div slot="udfName" slot-scope="{ row }" class="viewName-slot">
          <el-tooltip :content="row.udfName" effect="light" placement="top">
            <span class="viewName">{{ row.udfName }}</span>
          </el-tooltip>
          <el-tag v-if="isShare(row)" size="mini" class="udf-content__tag"> 分享 </el-tag>
        </div>
        <template slot="shareFlag" slot-scope="{ row }">
          <span>{{ row.shareFlag ? '分享' : '自建' }}</span>
        </template>
        <!-- UDF提交状态（已提交/开发中） -->
        <template slot="udfStateName" slot-scope="{ row }">
          <el-tag v-if="isSubmit(row)" size="mini" type="success">已提交</el-tag>
          <el-tag v-else size="mini" type="danger">开发中</el-tag>
        </template>
        <!-- UDF项操作按钮（编辑、查看、下载、删除、分享） -->
        <template slot="operator" slot-scope="{ row }">
          <el-tooltip
            v-for="item in getButtons(row.shareFlag)"
            :key="item.label"
            v-access="item.authCode"
            :content="item.label"
            effect="light"
            class="udf-content__main--tooltip"
          >
            <i
              :class="item.class"
              class="udf-content__main--icon"
              @click="operateHandler(item.event, row)"
            ></i>
          </el-tooltip>
        </template>
      </bs-table>
    </main>
    <!-- 分享树组件 -->
    <share-tree
      v-if="showShareTreeDialog"
      ref="ShareTree"
      :visible.sync="showShareTreeDialog"
      :data="shareData"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { URL_UDF_LIST, URL_UDF_DOWLOAD } from '@/apis/commonApi';
import { delItems } from '@/apis/udfApi';
import { cloneDeep, debounce } from 'lodash';
import { post } from '@/apis/utils/net';
import { PaBase } from '@/common/pipeace-base';
import ShareTree from '@/components/share-tree.vue';
import CommonDelete from '@/utils/mixins/common-delete';
import moment from 'moment';

@Component({
  components: {
    ShareTree
  },
  mixins: [CommonDelete]
})
export default class UDFManage extends PaBase {
  private udfTableLoading = false;
  private udfTypeList = [
    { value: 'UDF', label: 'UDF' },
    { value: 'UDAF', label: 'UDAF' },
    { value: 'UDTF', label: 'UDTF' },
    { value: 'ASYNCUDF', label: 'ASYNCUDF' }
  ];
  private udfSearchParams = {
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    search: {
      udfType: '',
      udfName: '',
      udfExplain: ''
    }
  };
  private tableKey = Date.now();
  private tableData: ITableData = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  private udfTableConfigButtons = [
    {
      label: '编辑',
      class: 'iconfont icon-bianji',
      event: 'goEdit',
      authCode: 'PA.DATA.UDF.EDIT'
    },
    {
      label: '查看',
      class: 'iconfont icon-chakan',
      event: 'viewDetail',
      authCode: 'PA.DATA.UDF.MENU',
      shareFlag: true
    },
    {
      label: '下载',
      class: 'iconfont icon-xiazai',
      event: 'download',
      authCode: 'PA.DATA.UDF.MENU',
      shareFlag: true
    },
    {
      label: '删除',
      class: 'iconfont icon-shanchu',
      event: 'delItems',
      authCode: 'PA.DATA.UDF.DELETE'
    },
    {
      label: '分享',
      class: 'iconfont icon-fenxiang',
      event: 'shareUdf',
      authCode: 'PA.DATA.UDF.SHARE'
    }
  ];
  // 批量删除选中UDF
  private selectedList: any[] = [];
  private showShareTreeDialog = false;
  private shareData: any = {};
  private debounceSearch = debounce(this.getListData, 500);
  // 全屏Loading，下载UDF时触发
  private udfPageLoading = false;
  private headerSearchInputList = [
    {
      model: 'udfName',
      placeholder: '请输入UDF名，中文名',
      class: 'udf-content__header--name'
    },
    {
      model: 'udfExplain',
      placeholder: '请输入功能说明',
      class: 'udf-content__header--explain'
    }
  ];
  private headerButtonList = [
    {
      authCode: 'PA.DATA.UDF.ADD',
      event: this.goEdit,
      label: '新建UDF'
    }
  ];

  created() {
    this.getListData();
  }

  activated() {
    (this.$refs.table as any).$children[1].doLayout();
  }
  getButtons(shareFlag) {
    return shareFlag
      ? this.udfTableConfigButtons.filter((item) => item.shareFlag)
      : this.udfTableConfigButtons;
  }
  handleCurrentChange(currentPage, pageSize) {
    this.udfSearchParams.pageData.currentPage = currentPage;
    this.udfSearchParams.pageData.pageSize = pageSize;
    this.getListData();
  }

  // UDF提交状态
  isSubmit({ udfStateName }) {
    return udfStateName === '已提交';
  }

  // UDF是否被分享
  isShare({ shareFlag }) {
    return shareFlag;
  }

  // 下载UDF
  async download({ id }) {
    this.udfPageLoading = true;
    try {
      const res: any = await this.doGet(`${URL_UDF_DOWLOAD}?id=${id}`, {
        responseType: 'blob'
      });
      if (res.blob) {
        const { blob, fileName } = res;
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        this.udfPageLoading = false;
        return;
      } else if (!res.success) {
        this.$message.error(res.msg);
        this.udfPageLoading = false;
      }
    } catch {
      this.udfPageLoading = false;
    }
  }

  // UDF操作按钮触发
  operateHandler(event, row) {
    this[event](row);
  }

  shareUdf(row) {
    this.shareData = row;
    this.showShareTreeDialog = true;
  }

  // UDF删除及删除校验
  delItems(row: any) {
    const ids = !row
      ? this.selectedList
      : { id: row.id, name: row.udfName, reference: row.udfReference };
    this['commonDel'](ids, async (delIds) => {
      const { success, msg } = await delItems(delIds);
      if (success) {
        this.getListData();
        this.$message.success(msg);
      }
    });
  }

  dblclick(row) {
    this.$router.push({
      path: '/data/udfEdit',
      query: { id: row.id, status: '1', title: `UDF：${row.udfName}` }
    });
  }

  // 查看详情
  viewDetail(row: any) {
    this.$router.push({
      path: '/data/udfEdit',
      query: { id: row.id, status: '1', title: `UDF：${row.udfName}` }
    });
  }

  // 编辑UDF
  goEdit(row: any) {
    const query = row.id
      ? { id: row.id, status: '2', title: `UDF：${row.udfName}` }
      : { status: '0', title: '新建UDF' };
    this.$router.push({
      path: '/data/udfEdit',
      query
    });
  }

  getListData() {
    this.udfTableLoading = true;
    const udfSearchParams = cloneDeep(this.udfSearchParams);
    udfSearchParams.search.udfName = udfSearchParams.search.udfName.trim();
    udfSearchParams.search.udfExplain = udfSearchParams.search.udfExplain.trim();
    const searchParams = {
      pageData: {},
      search: '',
      sortData: { updateTime: 'DESC' }
    };
    searchParams.pageData = udfSearchParams.pageData;
    searchParams.search = JSON.stringify(udfSearchParams.search);
    post(URL_UDF_LIST, searchParams).then((resp: any) => {
      this.parseResponse(resp, () => {
        const { columnData, tableData, pageData } = resp.data;
        columnData.forEach((el) => {
          if (el.value === 'udfName') {
            el.width = 160;
            el.showOverflowTooltip = false;
          }
        });
        columnData.push({ label: '操作', value: 'operator', fixed: 'right', minWidth: '180' });
        tableData.forEach((el) => {
          el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
        });
        this.tableData = {
          columnData,
          tableData,
          pageData
        };
      });
      this.udfTableLoading = false;
    });
  }

  // 批量删除选择
  handleSelectionChange(selections: any) {
    this.selectedList = [];
    selections.forEach((m) => {
      this.selectedList.push({ id: m.id, name: m.udfName, reference: m.udfReference });
    });
  }

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // 通过 `vm` 访问组件实例
      if (from.name === 'udfEdit') {
        vm.getListData();
      }
    });
  }
}
</script>
<style scoped lang="scss">
.viewName-slot {
  display: flex;
  align-items: center;
}
.viewName {
  display: inline-block;
  max-width: 180px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.udf-content {
  height: calc(100vh - 114px);
  background: #fff;
  &__tag {
    margin-left: 8px;
  }
  &__header {
    height: 50px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    &--btns {
      text-align: right;
      margin-right: -20px;
    }
    &--name,
    &--explain {
      width: 180px;
      margin-left: 10px;
    }
    &--explain {
      margin-right: 10px;
    }
  }
  &__main {
    background: #fff;
    &--tooltip {
      cursor: pointer;
    }
    &--icon {
      margin: 0 5px;
    }
  }
}
</style>
