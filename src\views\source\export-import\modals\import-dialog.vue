<template>
  <bs-dialog :title="$t('pa.action.import')" :visible.sync="show" size="large" :before-close="closeDialog">
    <bs-steps class="import-step" :active="activeStep" :options="stepOptions" />
    <keep-alive>
      <component
        :is="curStepComponent"
        v-loading="loading"
        table-height="calc(70vh - 250px)"
        :data="data"
        :cannot-imp-overiew-msg="cannotImpOveriewMsg"
        :cannot-import-data="cannotImpData"
        :repeat-imp-overiew-msg="repeatImpOveriewMsg"
        :repeat-import-data="repeatImpData"
        :loading.sync="loading"
      />
    </keep-alive>
    <span slot="footer" class="dialog-footer">
      <!-- 数据预览页脚 -->
      <el-button v-if="isFirstStep" @click="closeDialog">{{ $t('pa.action.cancel') }}</el-button>
      <el-button v-if="isFirstStep" type="primary" :loading="nextLoading" @click="nextStep">
        {{ $t('pa.data.text36') }}
      </el-button>
      <el-button v-if="!isFirstStep" @click="preStep">{{ $t('pa.data.text37') }}</el-button>
      <!-- 不可导入校验页脚 -->
      <el-button v-if="isSecondStep" type="primary" @click="nextStep">{{ $t('pa.action.skip') }}</el-button>
      <!-- 重复性校验页脚 -->
      <el-button v-if="isThirdStep" :loading="loading" type="primary" @click="submit">
        {{ $t('pa.action.import') }}
      </el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, PropSync, Prop, Vue } from 'vue-property-decorator';
import { getCannotAndRepeatData, importSubmit } from '@/apis/impExpApi';
import PreviewData from './preview-data.vue';
import CannotImportData from './cannot-import-data.vue';
import RepeatImportData from './repeat-import-data.vue';
import { IPreviewData } from '../interface';
@Component({
  name: 'ImportSource',
  components: {
    PreviewData,
    CannotImportData,
    RepeatImportData
  }
})
export default class ImportSource extends Vue {
  @PropSync('visible') show!: boolean;
  @Prop() data!: IPreviewData;
  activeStep = 0;
  stepOptions = [this.$t('pa.dataPreview'), this.$t('pa.data.text34'), this.$t('pa.data.text35')];
  cannotImpOveriewMsg = '';
  cannotImpData: any = [];
  repeatImpOveriewMsg = '';
  repeatImpData: any = [];
  canImport: any = [];
  loading = false;
  nextLoading = true;
  get isFirstStep() {
    return this.activeStep === 0;
  }
  get isSecondStep() {
    return this.activeStep === 1;
  }
  get isThirdStep() {
    return this.activeStep === 2;
  }
  //动态计算要渲染的组件
  get curStepComponent() {
    return { 0: 'PreviewData', 1: 'CannotImportData', 2: 'RepeatImportData' }[this.activeStep];
  }

  async created() {
    const { success, data, msg, error } = await getCannotAndRepeatData(this.data.impId);
    this.nextLoading = false;
    if (!success) return this.$message.error(error || msg);
    const { canImport, existList, notImpStatisticsDetails, repeatStatisticsDetails, unImport } = data;
    this.canImport = canImport;
    this.cannotImpData = unImport;
    this.repeatImpData = existList;
    this.cannotImpOveriewMsg = notImpStatisticsDetails;
    this.repeatImpOveriewMsg = repeatStatisticsDetails;
  }
  preStep() {
    this.activeStep -= 1;
  }
  nextStep() {
    this.activeStep += 1;
  }
  async submit() {
    this.loading = true;
    const params = { impId: this.data.impId, canImport: this.canImport, existList: this.repeatImpData };
    const { success, msg, error } = await importSubmit(params);
    this.loading = false;
    if (!success) return this.$message.error(error || msg);
    this.$message.success(msg);
    this.closeDialog();
  }
  closeDialog() {
    this.show = false;
  }
}
</script>

<style lang="scss" scoped>
.import-step {
  padding: 20px 104px;
}
::v-deep .el-dialog__body,
::v-deep .bs-dialog .el-dialog__body {
  padding: 0;
  max-height: 60vh;
}
</style>
