const rules: any = {
  resType: [{ required: true, message: '节点类型未选择', trigger: 'blur' }],
  serviceType: [{ required: true, message: '项目名称未选择', trigger: 'blur' }],
  serviceName: [{ required: true, message: '流程类型未选择', trigger: 'blur' }],
  flowType: [{ required: true, message: '服务资源类型未选择', trigger: 'blur' }],
  serviceId: [{ required: true, message: '节点名称未选择', trigger: 'blur' }],
  resourceList: [{ required: true, message: '请填写资源名称', trigger: 'blur' }]
};

const flowType: any = [
  {
    value: 'SQL',
    label: 'SQL流程'
  },
  {
    value: 'PROCESSFLOW',
    label: 'Datestream流程'
  }
];
const data = {
  serviceType: '',
  serviceName: '',
  flowType: '',
  serviceId: '',
  resourceList: ''
};

export { rules, flowType, data };

export const labelMapping = {
  JOB: '流程名称',
  SERVICE: '服务名称',
  TABLE: '表名称',
  VIEW: '视图名称'
};
