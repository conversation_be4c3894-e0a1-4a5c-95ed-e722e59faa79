<template>
  <div class="header__container">
    <!-- 左侧 方法 -->
    <el-form-item :rules="methodRules" class="header-funcName" label-width="40px" prop="funcName">
      <div slot="label" class="header-funcName__name">{{ formData.name }}</div>
      <el-select
        v-model="formData.funcName"
        filterable
        placeholder="请选择方法"
        @change="$emit('change', index)"
      >
        <el-option-group v-for="group in methodList" :key="group.label" :label="group.label">
          <el-option
            v-for="method in group.children"
            :key="method.value"
            :label="method.label"
            :value="method.value"
          >
            <div :key="sourceCode[method.value]" class="header-option">
              <span>{{ method.label }}</span>
              <el-popover
                v-if="method.type !== 'DEFAULT'"
                placement="right"
                trigger="hover"
                width="360"
                @show="getMethodSourceCode(method)"
              >
                <div v-loading="!Boolean(sourceCode[method.value])" style="min-height: 35px">
                  <div v-highlight>
                    <code class="java">{{ sourceCode[method.value] }}</code>
                  </div>
                </div>
                <span slot="reference" class="header-option__code">源码</span>
              </el-popover>
            </div>
          </el-option>
        </el-option-group>
      </el-select>
      <!-- 表达式 -->
      <div v-if="!formData.expand" :key="key" class="header-expression">
        <span>表达式：</span>
        <el-tooltip v-hide="expression" effect="light" placement="top">
          <div slot="content" v-html="expression"></div>
          <div class="header-expression__main" v-html="expression"></div>
        </el-tooltip>
      </div>
    </el-form-item>
    <!-- 右侧 按钮组 -->
    <div class="header-btnGroup">
      <el-button :disabled="disabled" type="text" @click="click('copy')"> 复制 </el-button>
      <el-button :disabled="disabled" type="text" @click="click('delete')"> 删除 </el-button>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { debounce } from 'lodash';
import { get } from '@/apis/utils/net';
import { highlight, hide, sha256 } from '../utils';
import { URL_GET_FUNC_BODY } from '@/apis/commonApi';

@Component({ directives: { highlight, hide } })
export default class LogicHeader extends Vue {
  @Prop({ default: 0 }) index!: number;
  @Prop({ default: '' }) uuid!: string;
  @Prop({ default: false }) disabled!: boolean;
  @PropSync('data', { default: () => ({}) }) formData!: any;
  @PropSync('catch', { type: Object, default: () => ({}) }) store!: any;

  private methodRules: any[] = [
    {
      required: true,
      message: '请选择方法',
      trigger: 'change'
    }
  ];
  private click = debounce(this.handleClick, 1200);

  get key() {
    return sha256(this.expression);
  }
  get expression() {
    return (this.store.expressionMapping || {})[this.uuid] || ''; // 逻辑优化
  }
  get methodList() {
    return this.store.methodList || [];
  }
  get sourceCode() {
    return this.store.sourceCode || {};
  }
  get showExpression() {
    return (this.store.showExpressions || {})[this.uuid] || false; // 逻辑优化
  }
  // get showExpression() {
  //   return !this.activeNames.length > 0;
  // }
  /* 获取方法源码 */
  async getMethodSourceCode({ value: name, type: funcType }) {
    try {
      if (!this.store.sourceCode) this.$set(this.store, 'sourceCode', {});
      if (this.sourceCode[name]) return;
      const { success, data, error } = await get(URL_GET_FUNC_BODY, { name, funcType });
      if (success) {
        this.$set(this.store.sourceCode, name, data);
        return;
      }
      this.$message.error(error);
    } catch (e) {
      console.log(e);
    }
  }
  /* 处理点击事件 */
  handleClick(type: string) {
    this.$emit(type, this.index);
  }
}
</script>
<style lang="scss" scoped>
.header {
  &__container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
  }
  &-funcName {
    width: calc(100% - 100px);

    ::v-deep .el-form-item {
      &__content {
        display: flex;
        align-items: center;
      }
      &__label {
        &::before {
          display: none;
        }
      }
    }

    ::v-deep .el-select {
      width: 312px;
    }

    &__name {
      width: 32px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      color: #fff;
      background: #377cff;
    }
  }
  &-expression {
    display: flex;
    align-items: center;
    margin-left: 20px;
    width: calc(100% - 345px);
    font-size: 14px;
    font-weight: 400;
    color: #444444;
    &__main {
      width: calc(100% - 56px);
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
  &-btnGroup {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 76px;
  }
}
.header-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  &__code {
    display: none;
    color: #377cff;
    font-weight: normal !important;
  }

  &:hover {
    .header-option__code {
      display: inline-block;
    }
  }
}
</style>
