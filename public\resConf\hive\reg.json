{"forms": [{"type": "input", "prop": "title", "label": "名称", "componentProps": {"maxlength": 30, "showWordLimit": true, "placeholder": "请输入名称"}, "rules": [{"required": true, "message": "请输入名称", "trigger": "blur"}, {"min": 2, "max": 30, "message": "长度在 2 到 30 个字符", "trigger": "blur"}]}, {"type": "select", "prop": "databaseType", "label": "库类型", "componentProps": {"options": [{"label": "hive", "value": "hive"}, {"label": "hudi", "value": "hudi"}], "placeholder": "请选择"}, "rules": [{"message": "请选择库类型", "required": true, "trigger": "change"}], "defaultVal": "hive"}, {"type": "input", "prop": "databaseName", "label": "数据库名称", "componentProps": {"maxlength": 30, "showWordLimit": true, "placeholder": "请输入数据库名称"}, "rules": [{"required": true, "message": "请输入数据库名称", "trigger": "blur"}]}, {"type": "textarea", "prop": "url", "label": "服务地址", "componentProps": {"rows": 3, "maxlength": 1024, "placeholder": "请输入服务地址，多个地址以逗号分隔，兼容Ipv4和Ipv6"}, "rules": [{"required": true, "message": "请输入服务地址，多个地址以逗号分隔，兼容Ipv4和Ipv6", "trigger": "blur"}, {"validator": "validateResUrl", "trigger": "blur"}]}, {"type": "input", "prop": "jdbcStr", "label": "JDBC连接串", "componentProps": {"maxlength": 100, "showWordLimit": true, "placeholder": "***************************** 或 ********************************************/<EMAIL>"}}, {"type": "input", "prop": "username", "label": "用户名", "componentProps": {"maxlength": 30, "showWordLimit": true, "placeholder": "用户名，非必填"}}, {"type": "password", "prop": "password", "label": "密码", "componentProps": {"maxlength": 30, "placeholder": "密码，非必填"}}, {"type": "input", "prop": "hiveVersion", "label": "hive版本号", "componentProps": {"maxlength": 50, "showWordLimit": true, "placeholder": "请输入hive版本号"}, "rules": [{"required": true, "message": "请输入hive版本号", "trigger": "blur"}], "defaultVal": "3.1.0"}, {"type": "textarea", "prop": "hiveSite", "label": "hiveSite", "componentProps": {"rows": 5, "placeholder": "请输入hive-site.xml文件内容"}, "rules": [{"message": "请输入hive-site.xml文件内容", "required": true, "trigger": "blur"}]}, {"type": "textarea", "prop": "hadoopSite", "label": "hadoopSite", "componentProps": {"rows": 5, "placeholder": "请输入hadoop配置内容\n(core-site.xml、hdfs-site.xml、mapred-site.xml、yarn-site.xml)"}, "rules": [{"message": "请输入hadoop配置文件内容", "required": true, "trigger": "blur"}]}, {"type": "radio-group", "prop": "authN<PERSON>ed", "label": "是否鉴权", "componentProps": {"options": [{"label": "是", "value": "yes"}, {"label": "否", "value": "no"}], "placeholder": "请选择"}, "rules": [{"message": "请选择是否鉴权", "required": true, "trigger": "blur"}], "defaultVal": "no"}, {"type": "input", "prop": "principalName", "label": "principal名称", "deps": ["authN<PERSON>ed"], "visible": "(scope) => scope.authNeeded === 'yes'", "componentProps": {"maxlength": 64, "showWordLimit": true, "placeholder": "请输入principalName名称"}, "rules": [{"required": true, "message": "请输入principalName", "trigger": "blur"}]}, {"type": "input", "prop": "krb5ConfPath", "label": "krb5.conf地址", "deps": ["authN<PERSON>ed"], "visible": "(scope) => scope.authNeeded === 'yes'", "componentProps": {"maxlength": 256, "showWordLimit": true, "placeholder": "请输入krb5.conf地址"}, "rules": [{"required": true, "message": "请输入krb5.conf地址", "trigger": "blur"}]}, {"type": "input", "prop": "kadm5KeytabPath", "label": "keytab地址", "deps": ["authN<PERSON>ed"], "visible": "(scope) => scope.authNeeded === 'yes'", "componentProps": {"maxlength": 256, "showWordLimit": true, "placeholder": "请输入kadm5.keytab地址"}, "rules": [{"required": true, "message": "请输入kadm5.keytab地址", "trigger": "blur"}]}, {"type": "radio-group", "prop": "proxyType", "label": "是否代理", "componentProps": {"options": [{"label": "是", "value": "HADOOP_CLI"}, {"label": "否", "value": "NO_PROXY"}]}, "rules": [{"message": "请选择是否代理", "required": true, "trigger": "blur"}], "defaultVal": "NO_PROXY"}, {"type": "textarea", "prop": "proxyAddress", "label": "代理服务地址", "deps": ["proxyType"], "visible": "(scope) => scope.proxyType === 'HADOOP_CLI'", "componentProps": {"rows": 3, "maxlength": 1024, "placeholder": "请输入代理地址的IP和端口，如nameNode1:8020"}, "rules": [{"required": true, "message": "请输入代理地址的IP和端口，如nameNode1:8020", "trigger": "blur"}]}, {"type": "textarea", "prop": "memo", "label": "备注", "componentProps": {"rows": 5, "maxlength": 255, "placeholder": "请输入备注"}}]}