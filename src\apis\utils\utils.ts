import CryptoJS from 'crypto-js';

function getKey() {
  const strArr = [
    '＼u0031＼u0063＼u0062＼u0063',
    '＼u0063＼u0037＼u0033＼u0065',
    '＼u0034＼u0035＼u0065＼u0036',
    '＼u0031＼u0037＼u0061＼u0030'
  ];
  return strArr.map((s) => unescape(s.replace(/＼u/g, '%u'))).join('');
}
// 加密
export function Encrypt(word) {
  let encrypted;
  const key = CryptoJS.enc.Utf8.parse(getKey());
  try {
    const srcs = CryptoJS.enc.Utf8.parse(word);
    encrypted = CryptoJS.AES.encrypt(srcs, key, {
      // iv:iv,
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
  } catch (error) {
    console.log(error);
  }
  return encrypted.toString();
}

// 解密
export function Decrypt(word) {
  const key = CryptoJS.enc.Utf8.parse(getKey());
  let decryptedStr;
  try {
    const decrypt = CryptoJS.AES.decrypt(word, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    // console.log(decrypt);
    decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    // et resData=decryptResult.toString(CryptoJS.enc.Utf8).toString();
  } catch (error) {
    console.log(error);
  }
  return decryptedStr.toString();
}

// 判断是不是FormData
export function isFormData(val) {
  return typeof FormData !== 'undefined' && val instanceof FormData;
}

// 获取url去除参数的部分
export function getOrigin(url) {
  const index = url.indexOf('?');
  if (index > -1) {
    return url.slice(0, index);
  } else {
    return url;
  }
}

export function canParse(str) {
  if (typeof str !== 'string') {
    return false;
  }
  if (str.includes('"') || str === '{}' || str === '[]') {
    return true;
  }
}
