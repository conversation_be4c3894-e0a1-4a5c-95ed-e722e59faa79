<template>
  <bs-dialog title="分享" width="640px" :visible.sync="visible" :before-close="closeDialog">
    <div v-if="!topicShare && !isFuseMode" style="margin: 0 0 20px 20px; text-align: left">
      自动分享：
      <el-switch
        v-model="data.autoShare"
        active-value="YES"
        inactive-value="NO"
        @change="autoShareChange"
      />
    </div>
    <bs-transfer
      v-model="value"
      :data="treeData"
      :props="props"
      :titles="titles"
      :filterable="filterable"
      :check-strictly="true"
      check-change
      @change="handleChange"
    />
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" :loading="loading" @click="submit()">确 定</el-button>
    </span>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Watch, InjectReactive } from 'vue-property-decorator';
import {
  URL_PORTAL_ALLORGS,
  URL_PORTAL_LISTSUBORGS,
  URL_RES_SHARE_KAFKA_LIST_ORGS,
  URL_RES_SHARE_KAFKA_TOPIC,
  URL_RESNOTUSE_ADD,
  URL_RESNOTUSE_CHANGE_AUTO_SHARE,
  URL_RESNOTUSE_LISTCANUSE
} from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';

@Component
export default class DeptRole extends PaBase {
  @Prop({ default: false, type: Boolean }) visible!: boolean;
  @Prop() data!: any;
  @Prop({ default: () => ({}) }) topicData!: any;
  @InjectReactive('isFuseMode') isFuseMode!: boolean;

  titles: any[] = ['可选', '已选'];
  filterable: any[] = [true, false];
  value: any[] = [];
  checkId: any[] = [];

  handleChange([data]) {
    this.checkId = data;
  }

  treeData: any[] = [];
  tableRecord!: any;
  props: any = {
    label: 'name',
    key: 'id'
  };
  orgIdArr: any = [];
  loading = false;
  treeLoading = false;
  filterText = '';
  // kafka分享/topic分享标识
  topicShare = false;
  valueData = [];

  created() {
    if (this.topicData.id !== undefined || Array.isArray(this.topicData)) {
      this.topicShare = true;
    }
    this.loadData(this.data);
  }

  autoShareChange() {
    this.doGet(URL_RESNOTUSE_CHANGE_AUTO_SHARE, {
      params: {
        id: this.data.id,
        resType: this.data.resType,
        autoShareType: this.data.autoShare
      }
    }).then((resp: any) => {
      this.parseResponse(resp);
    });
  }

  select(data, checked) {
    if (this.topicShare) {
      if (data.disable || !data.editable) {
        return;
      }
    }
    data.checked = checked;
    this.shareAllChildren(data, checked);
  }

  shareAllChildren(data, checked) {
    if (data.children.length) {
      data.children.forEach((item: any) => {
        item.checked = checked;
        if (item.children.length) {
          return this.shareAllChildren(item, checked);
        }
      });
    }
  }

  /**
   * 加载数据
   */
  async loadData(data: any) {
    this.tableRecord = data;
    this.treeLoading = true;
    // 判断是否为多选topic
    if (Array.isArray(this.topicData) && this.topicShare) {
      let tempArr: any = [];
      let valueData = [];
      for (let i = 0; i < this.topicData.length; i++) {
        await Promise.all([this.getOrgs(data), this.getCheckedOrgs(this.topicData[i].id)]).then(
          (values) => {
            if (i === 0) {
              tempArr = values[1].data;
            } else {
              tempArr = new Set(tempArr);
              tempArr = [...new Set(values[1].data as any)].filter((x) => tempArr.has(x));
            }
            length = data.orgPath.split('.').length;
            valueData = values[0].data;
            const res = values[0].data.filter((el) => {
              return el.level !== 0;
            });
            const minLevel = Math.min.apply(
              null,
              res.map((el) => {
                return el.level;
              })
            );
            this.treeData = this.getTreeData(minLevel, valueData, tempArr);
          }
        );
      }
      this.valueData = valueData;
      this.treeLoading = false;
    } else {
      Promise.all([
        this.getOrgs(data),
        this.topicShare ? this.getCheckedOrgs(this.topicData.id) : this.getCanUseOrg(data.id)
      ]).then((values) => {
        const res = values[0].data.filter((el) => {
          return el.level !== 0;
        });
        const minLevel = Math.min.apply(
          null,
          res.map((el) => {
            return el.level;
          })
        );
        this.treeData = this.getTreeData(
          this.topicShare ? minLevel : data.orgPath.split('.').length,
          values[0].data,
          values[1].data
        );
        this.valueData = values[0].data;
        this.treeLoading = false;
      });
    }
  }

  /**
   * 获取能使用资源的机构
   */
  getCanUseOrg(resId: string) {
    return this.doGet(URL_RESNOTUSE_LISTCANUSE, { params: { resId } });
  }

  getCheckedOrgs(topicId: string) {
    return this.doPost(URL_RES_SHARE_KAFKA_LIST_ORGS, [topicId]);
  }

  submit() {
    this.loading = true;
    const topics: string[] = [];
    if (Array.isArray(this.topicData) && this.topicShare) {
      for (const item of this.topicData) {
        const { id, topic } = item;
        topics.push(`${id}#${topic}`);
      }
      this.doPost(URL_RES_SHARE_KAFKA_TOPIC, {
        topics,
        orgIds: this.value
      }).then((resp: any) => {
        this.parseResp(resp);
      });
    } else {
      if (this.topicShare) {
        const { id, topic } = this.topicData;
        topics.push(`${id}#${topic}`);
        this.doPost(URL_RES_SHARE_KAFKA_TOPIC, {
          topics,
          orgIds: this.value
        }).then((resp: any) => {
          this.parseResp(resp);
        });
      } else {
        this.doPost(URL_RESNOTUSE_ADD + '?resId=' + this.tableRecord.id, this.value).then(
          (resp: any) => {
            this.parseResp(resp);
          }
        );
      }
    }
  }

  /**
   * 解析返回结果
   */
  private parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.closeDialog();
    });
  }

  // 处理树数据
  getTreeData(currentLevel: number, data, canData) {
    const treeData: any[] = [];
    const maps = new Map();
    const useOrgIds = this.topicShare ? canData : canData.map(({ useOrgId }) => useOrgId);
    data.forEach((item: any) => {
      item.children = [];
      item.checked = useOrgIds.includes(item.id);
      if (item.disable) {
        item.checked = true;
      }
      if (item.level === currentLevel) {
        treeData.push(item);
      }
      if (!maps.has(item.parentId)) {
        maps.set(item.parentId, [item]);
      } else {
        maps.set(item.parentId, maps.get(item.parentId).concat(item));
      }
    });
    const findNodes = (datas: any[]) => {
      datas.forEach((item) => {
        item.children = maps.get(item.id) || [];
        return findNodes(item.children);
      });
    };
    findNodes(treeData);
    this.value = this.handleSelcetData(treeData);
    return treeData;
  }

  handleSelcetData(data: any[], arr = []) {
    return data.reduce((pre, next) => {
      if (next.checked) {
        pre.push(next.id);
      }
      if (Array.isArray(next.children)) {
        return this.handleSelcetData(next.children, pre);
      }
      return pre;
    }, arr);
  }

  // 处理dropdown下拉的情况
  handleCommand(command: any, data: any) {
    this[command.clickfn](data);
  }

  public getOrgs(data: any) {
    return this.topicShare
      ? this.doGet(URL_PORTAL_ALLORGS, {
          params: { orgId: data.orgId, resId: data.id }
        })
      : this.doGet(URL_PORTAL_LISTSUBORGS, {
          params: { orgId: data.orgId }
        });
  }

  // 全部选择
  private allCheck() {
    if (!this.topicShare) {
      this.treeData.forEach((element: any) => {
        element.checked = true;
        this.shareAllChildren(element, true);
      });
      return;
    }
    this.valueData.forEach((el: any): true | undefined => {
      if (el.disable || !el.editable) {
        return true;
      }
      el.checked = true;
    });
  }

  // 全部取消选择
  private allUncheck() {
    if (!this.topicShare) {
      this.treeData.forEach((element: any) => {
        element.checked = false;
        this.shareAllChildren(element, false);
      });
      return;
    }
    this.valueData.forEach((el: any) => {
      if (el.disable || !el.editable) {
        return true;
      }
      el.checked = false;
    });
  }

  private filterNode(value, data) {
    if (!value) {
      return true;
    }
    return data.name.indexOf(value) !== -1;
  }

  private closeDialog() {
    this.loading = false;
    this.$emit('update:visible', false);
  }

  @Watch('filterText')
  watchFilterText(val) {
    (this.$refs.deptTree as any).filter(val);
  }
}
</script>
<style scoped>
.marR8 {
  margin-right: 8px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
}

.custom-tree-node-label {
  text-align: left;
  flex: 1;
}

.custom-tree-node-label.disabled {
  color: #ddd;
}

.custom-tree-node-icon {
  font-size: 18px;
}
</style>
