<template>
  <pro-page title="监控预警" :fixed-header="false">
    <div slot="operation" class="operate-box">
      <el-select v-model="resType" clearable>
        <el-option
          v-for="item in resTypeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <bs-search
        v-model="searchObj.search"
        placeholder="请输入资源名称"
        style="width: 210px; margin-left: 10px"
        maxlength="30"
        @input="fetchList"
      />
      <el-button type="primary" style="margin-left: 10px" @click="getListData()"> 查询 </el-button>
      <el-button
        v-access="'PA.MONITOR.WARN.OFF'"
        type="primary"
        style="margin-left: 10px"
        @click="changeStateBatch('OFF')"
      >
        关闭
      </el-button>
      <el-button
        v-access="'PA.MONITOR.WARN.ON'"
        type="primary"
        style="margin-left: 10px"
        @click="changeStateBatch('ON')"
      >
        启用
      </el-button>
    </div>
    <bs-table
      v-loading="tableLoading"
      selection
      :height="selectedList.length ? 'calc(100vh - 347px)' : 'calc(100vh - 290px)'"
      :data="tableData.tableData"
      :column-data="tableData.columnData"
      :page-data="searchObj.pageData"
      @page-change="handleCurrentChange"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
      @refresh="getListData(true)"
    >
      <template slot="operator" slot-scope="{ row }">
        <el-tooltip content="编辑" effect="light">
          <i
            v-if="hasAuthority('PA.MONITOR.WARN.EDIT', row)"
            class="iconfont icon-bianji"
            @click="edit(row)"
          ></i>
        </el-tooltip>
        <el-tooltip content="详情" effect="light">
          <i
            v-if="hasAuthority('PA.MONITOR.WARN.RECORD_LIST', row)"
            class="iconfont icon-chakan"
            @click="detail(row)"
          ></i>
        </el-tooltip>
        <el-tooltip content="启用" effect="light">
          <i
            v-if="hasAuthority('PA.MONITOR.WARN.ON', row)"
            class="iconfont icon-qidong"
            @click="changeState(row, 'ON')"
          ></i>
        </el-tooltip>
        <el-tooltip content="关闭" effect="light">
          <i
            v-if="hasAuthority('PA.MONITOR.WARN.OFF', row)"
            class="iconfont icon-guanbi"
            @click="changeState(row, 'OFF')"
          ></i>
        </el-tooltip>
      </template>
    </bs-table>
    <rule-edit
      :visible="dialogVisible"
      :title="dialogTitle"
      :data="recordData"
      :form-loading="formLoading"
      @close="closeDialog"
    />
    <warn-record-list :visible="wrDialogVisible" :data="wrRecordData" @close="wrCloseDialog" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Watch, Vue } from 'vue-property-decorator';
import {
  URL_WARNRULE_LIST,
  URL_WARNRULE_FIND,
  URL_WARNRULE_UPDATESTATE,
  URL_RESCONF_GETALLTYPELIST
} from '@/apis/commonApi';
import * as _ from 'lodash';
import { cloneDeep, debounce } from 'lodash';
import moment from 'moment';
import { post, get, put } from '@/apis/utils/net';
import { hasPermission } from '@/utils';
@Component({
  components: {
    'rule-edit': () => import('@/views/monitor/warning-rule/modals/edit.vue'),
    'warn-record-list': () => import('@/views/monitor/warning-record/modals/list.vue')
  }
})
export default class WarningRule extends Vue {
  formLoading = false;
  dialogVisible = false;
  dialogTitle = '编辑';
  recordData: any = {};
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: {
      recordNoReadCount: 'DESC'
    }
  };
  tableData: ITableData = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  fetchList: any = debounce(this.getListData, 500);
  resType = '';
  resTypeList: any = [];
  selectedList: string[] = [];
  wrDialogVisible = false;
  wrRecordData: any = {};
  isSearch = false;
  typeList = {
    CLOSING: '关闭中',
    OFF: '关闭',
    ON: '启用',
    OPENING: '开启中'
  };
  handleCurrentChange(currentPage, pageSize) {
    this.searchObj.pageData.currentPage = currentPage;
    this.searchObj.pageData.pageSize = pageSize;
    this.fetchList();
  }

  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.fetchList();
  }
  @Watch('$route')
  routerChange(router) {
    if (router.path === '/monitor/warningRule') {
      (this.searchObj.search as any) = this.$route.query.resTitle ? this.$route.query.resTitle : '';
      if (!this.$route.query.type) {
        this.getListData();
      }
    }
  }
  async getListData(refresh = false) {
    try {
      this.tableLoading = true;
      let api = URL_WARNRULE_LIST;
      if (_.toString(this.$route.query.resType) !== '') {
        this.resType = _.toString(this.$route.query.resType);
      }
      if (this.resType !== '') {
        api = URL_WARNRULE_LIST + '?resType=' + this.resType;
      }
      if (this.isSearch) {
        api = `${URL_WARNRULE_LIST}/${this.$route.query.type}`;
        this.isSearch = false;
      }
      const searchObj = cloneDeep(this.searchObj);
      if (searchObj.search !== undefined) searchObj.search = searchObj.search.trim();
      const resp = await post(api, searchObj);
      if (resp.success) {
        const { tableData, columnData, pageData } = resp.data;
        columnData.forEach((el) => {
          el.value = el.prop;
        });
        columnData.push({ label: '操作', value: 'operator', width: 140, fixed: 'right' });
        tableData.forEach((el) => {
          el.stateType = this.typeList[el.stateType];
          el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
        });
        this.searchObj.pageData = pageData;
        this.tableData = {
          columnData,
          tableData
        };
        refresh && this.$message.success('刷新成功');
        this.tableLoading = false;
      } else {
        this.tableLoading = false;
        this.$message.error(resp.error || resp.msg);
      }
    } catch (e) {
      this.tableLoading = false;
    }
  }
  getResTypeList() {
    this.resTypeList = [];
    get(URL_RESCONF_GETALLTYPELIST).then((resp: any) => {
      resp.data.forEach((n) => {
        if (n.listConf === undefined) {
          this.resTypeList.push({
            value: n.type,
            label: n.label
          });
        } else {
          if (hasPermission(n.listConf.menuAuthCode)) {
            this.resTypeList.push({
              value: n.type,
              label: n.label
            });
          }
        }
      });
    });
  }
  hasAuthority(hasRole: string, row: any) {
    return (
      !_.isEmpty(_.toString(row.dataLevelType)) &&
      row.dataLevelType !== 'PARENT' &&
      hasPermission(hasRole)
    );
  }

  async edit(row: any) {
    try {
      this.dialogTitle = '编辑';
      this.formLoading = true;
      const params = { id: row.id };
      const resp = await get(URL_WARNRULE_FIND, params);
      if (resp.success) {
        this.dialogVisible = true;
        this.recordData = resp.data;
        this.formLoading = false;
      } else {
        this.formLoading = false;
        this.$message.error(resp.error || resp.msg);
      }
    } catch (e) {
      this.formLoading = false;
    }
  }
  detail(row: any) {
    this.wrDialogVisible = true;
    this.wrRecordData = row;
  }
  async changeStateBatch(stateType: string) {
    try {
      const array: any = [];
      this.selectedList.forEach((n) => {
        array.push({
          id: n,
          stateType: stateType
        });
      });
      if (array.length === 0) {
        this.$message.warning('请选择记录');
        return;
      }
      this.tableLoading = true;
      const resp = await put(URL_WARNRULE_UPDATESTATE, array);
      if (resp.success) {
        array.forEach((item) => {
          const row: any = _.find(this.tableData.tableData, { id: item.id });
          this.$set(row, 'stateType', this.midState(stateType));
        });
        if (resp) {
          this.selectedList = [];
          this.getListData();
        }
        this.$message.success(resp.msg);
        this.tableLoading = false;
      } else {
        this.tableLoading = false;
        this.$message.error(resp.error || resp.msg);
      }
    } catch (e) {
      this.tableLoading = false;
    }
  }
  changeState(row: any, stateType: string) {
    this.tableLoading = true;
    put(URL_WARNRULE_UPDATESTATE, [
      {
        id: row.id,
        stateType
      }
    ]).then((resp: any) => {
      if (resp.success) {
        this.$set(row, 'stateType', this.midState(stateType));
        this.$message.success(resp.msg);
      } else {
        this.$message.error(resp.error || resp.msg);
      }
      this.tableLoading = false;
    });
  }
  midState(stateType: string) {
    if (stateType === 'OFF') {
      return 'OFF';
    } else if (stateType === 'ON') {
      return 'ON';
    }
  }
  handleSelectionChange(sel: any) {
    this.selectedList = [];
    sel.forEach((m) => {
      if (m.dataLevelType !== 'PARENT') {
        this.selectedList.push(m.id);
      }
    });
  }

  closeDialog(needFresh: any) {
    if (needFresh === true) {
      this.getListData();
    }
    this.dialogVisible = false;
  }
  wrCloseDialog() {
    this.wrDialogVisible = false;
  }
  setRouteType() {
    if (this.$route.query.type) this.isSearch = true;
  }
  activated() {
    this.setRouteType();
    this.getListData();
    this.getResTypeList();
    (this.searchObj.search as any) = this.$route.query.resTitle ? this.$route.query.resTitle : '';
  }
}
</script>
<style scoped lang="scss">
.iconfont {
  cursor: pointer;
}
.iconfont + .iconfont {
  margin-left: 10px;
}
</style>
