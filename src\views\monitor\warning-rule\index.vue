<template>
  <pro-page :title="$t('pa.menu.monitorWarn')" :fixed-header="false">
    <div slot="operation" class="operate-box">
      <!-- 预警状态 -->
      <bs-select
        v-model="stateType"
        class="operate-box__warning marR10"
        :class="{ 'operate-box__status--us': isEn }"
        size="small"
        :options="stateTypeOptions"
        clearable
        :placeholder="$t('pa.monitor.text23')"
        @change="fetchList()"
      />
      <!-- 预警规则 -->
      <bs-select
        v-model="ruleTypes"
        class="operate-box__warning marR10"
        :class="{ 'operate-box__warning--us': isEn }"
        size="small"
        :options="warningRuleOptions"
        multiple
        show-all
        clearable
        collapse-tags
        :placeholder="$t('pa.monitor.text24')"
        @change="fetchList()"
      />
      <!-- 资源类型 -->
      <bs-cascader
        v-model="resTypes"
        class="operate-box__type marR10"
        :options="resTypeOptions"
        :props="resTypeProps"
        collapse-tags
        filterable
        clearable
        :placeholder="$t('pa.monitor.text25')"
        @change="fetchList()"
      />
      <bs-search
        v-model.trim="searchObj.search"
        class="operate-box__name"
        :class="{ 'operate-box__search--us': isEn }"
        :placeholder="this.$t('pa.monitor.warningRule.resNamePlaceholder')"
        maxlength="30"
        @change="fetchList()"
      />
    </div>
    <bs-table
      v-loading="tableLoading"
      selection
      :height="selectedList.length ? 'calc(100vh - 347px)' : 'calc(100vh - 290px)'"
      :data="tableData.tableData"
      :column-data="tableData.columnData"
      :page-data="searchObj.pageData"
      @page-change="handleCurrentChange"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
      @refresh="getListData(true)"
    >
      <div slot="headerOperator">
        <el-button-group>
          <el-button v-access="'PA.MONITOR.WARN.OFF'" size="small" @click="handleBatchChangeState('OFF')">
            {{ $t('pa.action.close') }}
          </el-button>
          <el-button v-access="'PA.MONITOR.WARN.ON'" size="small" @click="handleBatchChangeState('ON')">
            {{ $t('pa.action.enable') }}
          </el-button>
        </el-button-group>
      </div>
      <a slot="resTitle" slot-scope="{ row }" href="javascript:void(0);" @click="openResDetail(row)">{{ row.resTitle }} </a>
      <template slot="header-cron" slot-scope="{ row }">
        <div class="header-cell-container">
          <span class="text-with-ellipsis" :class="{ 'text-with-ellipsis--en': isEn }" :title="row.label">{{
            row.label
          }}</span>
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              {{ $t('pa.monitor.text26') }}<br />
              {{ $t('pa.monitor.text27') }}
              {{ $t('pa.monitor.text28') }}
            </div>
            <i class="bs-pro-form-item__icon bs-icon-wenti"></i>
          </el-tooltip>
        </div>
      </template>
      <template slot="footer-expand">
        <el-checkbox v-model="filterUnResolvedWarnings" @change="getListData(false)">
          {{ $t('pa.monitor.text29') }}
        </el-checkbox>
      </template>
      <template slot="operator" slot-scope="{ row }">
        <el-tooltip v-if="hasAuthority('PA.MONITOR.WARN.EDIT')" :content="$t('pa.flow.edit')" effect="light">
          <i class="iconfont icon-bianji" @click="handleEditRule(row)"></i>
        </el-tooltip>
        <el-tooltip v-if="hasAuthority('PA.MONITOR.WARN.RECORD_LIST')" :content="$t('pa.action.detail')" effect="light">
          <i class="iconfont icon-chakan" @click="handleViewDetail(row)"></i>
        </el-tooltip>
        <el-tooltip v-if="hasAuthority('PA.MONITOR.WARN.ON')" :content="$t('pa.action.enable')" effect="light">
          <i class="iconfont icon-qidong" @click="handleChangeState(row, 'ON')"></i>
        </el-tooltip>
        <el-tooltip v-if="hasAuthority('PA.MONITOR.WARN.OFF')" :content="$t('pa.action.close')" effect="light">
          <i class="iconfont icon-guanbi" @click="handleChangeState(row, 'OFF')"></i>
        </el-tooltip>
      </template>
    </bs-table>
    <!-- 预警编辑弹窗 -->
    <warn-rule-edit-dialog
      v-if="showEditDialog"
      :id="curRecordId"
      :org-id="curRecordOrgId"
      :show.sync="showEditDialog"
      @close="getListData"
    />
    <!-- 预警详情弹窗 -->
    <warn-record-dialog v-if="showRecordDialog" :show.sync="showRecordDialog" :data="wrRecordData" @refresh="getListData" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Watch, Vue } from 'vue-property-decorator';
import { URL_WARNRULE_UPDATESTATE } from '@/apis/commonApi';
import { getRuleTypeList, searchWarnings, getResourceTypes } from '@/apis/warnRuleApi';
import * as _ from 'lodash';
import { cloneDeep, debounce } from 'lodash';
import dayjs from 'dayjs';
import { put } from '@/apis/utils/net';
import { hasPermission, hasServiceDetailAccess } from '@/utils';
@Component({
  components: {
    WarnRuleEditDialog: () => import('@/views/monitor/components/warn-rule-edit-dialog.vue'),
    WarnRecordDialog: () => import('@/views/monitor/components/warn_record_dialog.vue')
  }
})
export default class WarningRule extends Vue {
  showEditDialog = false;
  curRecordId = '';
  curRecordOrgId = '';
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 0 },
    sortData: {
      recordNoReadCount: 'DESC'
    }
  };
  tableData: ITableData = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  fetchList: any = debounce(this.getListData, 500);
  resType = '';
  resTypeList: any = [];
  selectedList: string[] = [];
  showRecordDialog = false;
  wrRecordData: any = {};
  ruleTypes = [];
  warningRuleOptions: any = [];
  resTypeProps = { multiple: true, showAll: true, checkStrictly: false, emitPath: false };
  filterUnResolvedWarnings = false;
  stateType = '';
  resTypes: any = [];
  resTypeOptions: any = [];
  stateTypeOptions: any = [
    {
      label: this.$t('pa.action.enable'),
      value: 'ON'
    },
    {
      label: this.$t('pa.action.close'),
      value: 'OFF'
    }
  ];
  enumsDataMap: any = new Map();

  @Watch('$route')
  routerChange(router) {
    if (router.path === '/monitor/warningRule') {
      (this.searchObj.search as any) = this.$route.query.resTitle ? this.$route.query.resTitle : '';
      if (!this.$route.query.type) {
        this.getListData();
      }
    }
  }

  async activated() {
    this.searchObj.search = this.$route.query.resTitle ? this.$route.query.resTitle : '';
    await this.getResTypeList();
    this.getRuleTypeList();
    this.getListData();
  }

  async getListData(refresh = false) {
    try {
      this.tableLoading = true;
      if (_.toString(this.$route.query.resType) !== '') {
        this.resType = _.toString(this.$route.query.resType);
      }
      const searchObj = cloneDeep(this.searchObj);
      const { data, success, error, msg } = await searchWarnings({
        stateType: this.stateType,
        ruleTypes: this.ruleTypes,
        requestSearchParam: searchObj,
        resourceTypes: {
          ...(this.resTypeOptions.filter((el) => el.value === 'flow').length && {
            allFlow: this.$route.query.type === 'flow' || this.resTypes.includes('flow') || !this.resTypes.length
          }),
          allRes: this.$route.query.type === 'service' || !this.resTypes.length,
          ...(this.resTypeOptions.filter((el) => el.value === 'table').length && {
            allTable: !this.resTypes.length || this.resTypes.includes('table')
          }),
          resList: this.resTypes.filter((el) => !['flow', 'table'].includes(el))
        },
        filterThanZero: this.filterUnResolvedWarnings
      });
      if (!success) return this.$message.error(error || msg);
      const { tableData, columnData, pageData } = data;
      columnData.forEach((el) => {
        el.value = el.prop;
        el.dataType === 'Enum' && this.enumsDataMap.set(el.prop, el.enumData);
        if (el.prop === 'cron') {
          el.className = 'no-wrap-column';
          el.width = this.isEn ? 130 : el.width;
        }
      });

      tableData.forEach((el) => {
        el.stateType = this.enumsDataMap.get('stateType')[el.stateType];
        el.sendEnable = this.enumsDataMap.get('sendEnable')[el.sendEnable];
        el.updateTime = dayjs(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
      });
      this.searchObj.pageData = pageData;
      this.tableData = {
        columnData: [...columnData, { label: this.$t('pa.action.action'), value: 'operator', width: 140, fixed: 'right' }],
        tableData
      };
      refresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    } finally {
      this.tableLoading = false;
    }
  }

  // 获取资源类型列表
  async getResTypeList() {
    const { data, success, error, msg } = await getResourceTypes();
    if (!success) return this.$message.error(error || msg);
    data &&
      (this.resTypeOptions = [
        'flow' in data && { label: this.$t('pa.flowName'), value: 'flow' },
        {
          label: this.$t('pa.home.service'),
          value: 'service',
          children: data.res.map((el) => ({ label: el.label, value: el.type }))
        },
        'table' in data && { label: this.$t('pa.table'), value: 'table' }
      ].filter(Boolean));
    this.handleJumpFromHome();
  }

  // 获取预警规则列表
  async getRuleTypeList() {
    const { data } = await getRuleTypeList();
    this.warningRuleOptions = Array.isArray(data) ? data : [];
  }

  handleCurrentChange(currentPage: number, pageSize: number) {
    this.searchObj.pageData.currentPage = currentPage;
    this.searchObj.pageData.pageSize = pageSize;
    this.fetchList();
  }

  handleSortChange(sortType: string) {
    this.searchObj.sortData = sortType;
    this.fetchList();
  }

  // 从首页跳转到监控预警页面，调整列表筛选项
  handleJumpFromHome() {
    if (!this.$route.query.type) return;
    // 从首页待处理预警数跳转时，选中【仅看未处理预警>0】
    this.$set(this, 'filterUnResolvedWarnings', true);
    const resTypeMap = {
      // 【流程】跳转：资源类型=流程 且 仅看未处理预警＞0选中
      flow: ['flow'],
      // 【服务】跳转：资源类型=服务下的全部 且 仅看未处理预警＞0选中
      service: this.resTypeOptions.filter((el) => el.value === 'service')[0].children.map((el) => el.value)
    };
    this.resTypes = resTypeMap[this.$route.query.type as string];
  }

  hasAuthority(hasRole: string) {
    return hasPermission(hasRole);
  }

  // 编辑预警
  async handleEditRule({ id, orgId }: any) {
    this.showEditDialog = true;
    this.curRecordId = id;
    this.curRecordOrgId = orgId;
  }

  // 查看预警详情
  handleViewDetail(row: any) {
    this.showRecordDialog = true;
    this.wrRecordData = row;
  }

  // 批量更新预警状态
  async handleBatchChangeState(stateType: string) {
    const array: any = this.selectedList.map((el) => ({ id: el, stateType }));
    if (!array.length) return this.$message.warning(this.$t('pa.monitor.flow.action.leastOne'));
    this.tableLoading = true;
    const resp = await put(URL_WARNRULE_UPDATESTATE, array);
    this.tableLoading = false;
    if (!resp.success) return this.$message.error(resp.error || resp.msg);
    array.forEach((item) => {
      const row: any = _.find(this.tableData.tableData, { id: item.id });
      this.$set(row, 'stateType', this.enumsDataMap.get('stateType')[stateType]);
    });
    if (resp) {
      this.selectedList = [];
      this.getListData();
    }
    this.$message.success(resp.msg);
  }

  // 更新预警状态
  async handleChangeState(row: any, stateType: string) {
    try {
      this.tableLoading = true;
      const data = await put(URL_WARNRULE_UPDATESTATE, [
        {
          id: row.id,
          stateType
        }
      ]);
      if (!data.success) return this.$message.error(data.error || data.msg);
      this.$set(row, 'stateType', this.enumsDataMap.get('stateType')[stateType]);
      this.$message.success(data.msg);
    } finally {
      this.tableLoading = false;
    }
  }

  handleSelectionChange(selections: any) {
    this.selectedList = selections.map((el) => el.id);
  }
  // 根据资源类型跳转到不同的详情
  openResDetail({ hasAccess, resType, paTable, paJob, paRes }: any) {
    if (!hasAccess) return this.$message.error(this.$t('pa.monitor.text30'));
    // 根据不同的资源类型，跳转到不同的详情页：表详情、流程详情、服务详情
    switch (resType) {
      case 'SQL_TABLE':
        this.toTableDetail(paTable);
        break;
      case 'PROCESSFLOW':
        this.toFlowCanvas(paJob);
        break;
      default:
        this.toServiceDetail(paRes);
        break;
    }
  }
  toTableDetail(row) {
    if (!hasPermission('PA.DATA.TABLE.VIEW_DETAIL')) return;
    this.$router.push({
      path: '/data/sheetDetail',
      query: { id: row.tableId, title: row.tableName }
    });
  }
  toFlowCanvas({ projectId, jobId, jobName }) {
    if (
      (this as any).$tabNav.getAllTabs().find((item) => item.title === jobName && item.value.split('flowId=')[1] === jobId)
    ) {
      const value = (this as any).$tabNav
        .getAllTabs()
        .find((item) => item.title === jobName && item.value.split('flowId=')[1] === jobId).value;
      localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId: jobId }));
      this.$router.push({
        path: value
      });
    } else {
      this.$router.push({
        path: '/flow',
        query: {
          id: projectId,
          title: jobName,
          state: 'ALL',
          flowId: jobId
        }
      });
    }
  }
  toServiceDetail({ resId, resType, resName, clusterType }) {
    if (!hasServiceDetailAccess()) return;
    const path = `/element/service/${resType}/detail`;
    this.$router.push({
      path,
      query: { id: resId, resType: resType, title: resName, clusterType: clusterType || '' }
    });
  }
}
</script>
<style scoped lang="scss">
.iconfont {
  cursor: pointer;
}
.iconfont + .iconfont {
  margin-left: 10px;
}
.operate-box {
  &__warning {
    ::v-deep .el-input {
      width: 165px !important;
    }
    &--us {
      width: 260px !important;
      ::v-deep .el-input {
        width: 260px !important;
      }
    }
  }
  &__status {
    &--us {
      width: 130px !important;
      ::v-deep .el-input {
        width: 130px !important;
      }
    }
  }
  &__name {
    width: 200px !important;
    ::v-deep .el-input {
      width: 200px !important;
    }
  }
  &__search {
    &--us {
      width: 122px !important;
      ::v-deep .el-input {
        width: 122px !important;
      }
    }
  }

  &__type {
    ::v-deep .el-input {
      width: 300px !important;
    }
    ::v-deep .el-input__inner {
      max-height: 32px;
    }
  }
}

.header-cell-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.text-with-ellipsis {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 90px;
  margin-right: 5px;

  &--en {
    max-width: 70px;
    width: 70px;
  }
}

::v-deep .bs-icon-wenti {
  flex-shrink: 0;
  margin-left: 3px;
}

/* 表头不换行全局样式 */
::v-deep {
  /* 针对英文状态下的表头样式覆盖 */
  .header-cell-container.header-cell-container--us {
    display: flex !important;
    width: 100% !important;
    padding-right: 0 !important; /* 移除右侧内边距以确保文本足够空间 */
  }

  /* 针对特定列换行 */
  .no-wrap-column {
    .cell {
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      max-width: 100% !important;

      & > div.header-cell-container {
        width: 100% !important;
      }
    }
  }
}
</style>
