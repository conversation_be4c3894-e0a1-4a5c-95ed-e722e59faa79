<template>
  <bs-dialog
    v-loading="loading"
    :title="$t('pa.action.import')"
    :visible.sync="display"
    width="490px"
    @confirm="handleConfirm"
  >
    <div class="tips">
      <i class="iconfont icon-tishi" style="font-size: 12px; color: inherit"></i>
      {{ $t('pa.data.udf.detail.tips.importTip') }}
    </div>
    <div class="center">
      <div class="first_title">
        <span>{{ $t('pa.data.udf.detail.tips.downloadSteps1') }}</span>
        <el-tooltip effect="light" placement="bottom" :content="$t('pa.data.text7')">
          <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer"></i>
        </el-tooltip>
      </div>
      <div>
        <el-button class="btn" icon="iconfont icon-xiazai" @click="handleDownload">
          {{ $t('pa.data.udf.detail.downloadFile') }}
        </el-button>
      </div>
      <div class="first_title">
        <span>{{ $t('pa.data.text5') }}</span>
        <el-tooltip effect="light" placement="bottom" :content="$t('pa.data.text7')">
          <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer"></i>
        </el-tooltip>
      </div>
      <!-- form -->
      <el-form ref="formRef" :model="formData" :rules="formRule" :element-loading-text="$t('pa.loadingText')">
        <el-form-item prop="pkg">
          <el-tooltip effect="light" :content="fileName" :disabled="isDisabled" placement="bottom-start">
            <el-upload
              ref="upload"
              action
              :limit="1"
              accept=".xlsx,.xls"
              :multiple="false"
              :auto-upload="false"
              :on-exceed="handleExceed"
              :on-change="handleChange"
              :on-remove="handleChange"
            >
              <el-button class="btn" icon="iconfont icon-shangchuan"> {{ $t('pa.data.udf.detail.selectFile') }}</el-button>
            </el-upload>
          </el-tooltip>
        </el-form-item>
      </el-form>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import { tableDownload, tableImport } from '@/apis/dataApi';
import ElForm from 'bs-ui-pro/packages/form/index.js';

@Component
export default class UploadDialog extends Vue {
  @PropSync('show', { required: true, default: false }) display!: boolean;
  @Prop({ default: '' }) resType!: string;
  @Ref('formRef') readonly form!: ElForm;

  loading = false;
  fileName = '';
  formData: any = { pkg: null };

  get formRule() {
    return {
      pkg: {
        required: true,
        validator: (rule: any, value: any, callback: any) => {
          if (!value) return callback(new Error(this.$t('pa.tip.addFile')));
          const isLimit = value.size / 1024 / 1024 > 10;
          if (isLimit) return callback(new Error(this.$t('pa.data.text4')));
          callback();
        },
        trigger: 'blur'
      }
    };
  }

  get isDisabled() {
    return this.fileName === '' || this.fileName.length < 50;
  }

  handleDownload() {
    tableDownload(this.resType);
  }
  handleExceed(files: any) {
    this.$message.warning(this.$t('pa.tip.fileCount', [files.length]));
  }
  handleChange(file, fileList: any[]) {
    this.formData.pkg = fileList[0]?.raw || null;
    this.form.validate();
  }

  async handleConfirm() {
    try {
      if (!this.formData.pkg) return this.$message.warning(this.$t('pa.tip.selectFile'));
      await this.form.validate();
      await this.$confirm(this.$t('pa.data.udf.detail.tips.importCoverTip'), this.$t('pa.prompt'), { type: 'warning' });
      const params = new FormData();
      params.append('file', this.formData.pkg);
      const { success, data, error } = await tableImport(this.resType, params);
      if (!success) return this.$message.error(error);
      this.$emit('confirm', data);
      this.display = false;
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 12px 20px;
  text-align: left;
}
::v-deep .el-upload {
  display: block;
  width: 100%;
}
::v-deep .iconfont {
  color: inherit;
}
.btn,
.el-upload {
  width: 100%;
}
.tips {
  font-size: 12px;
  background: #fff5ea;
  color: #ff9e2b;
  border-radius: 16px;
  line-height: 36px;
  padding: 0 20px;
}
.center {
  padding: 0 20px;
}
.first_title {
  margin: 8px 0;
  line-height: 30px;
  font-size: 14px;
  color: black;
  font-weight: bold;
  display: flex;
  align-items: center;
}
</style>
