<template>
  <div class="unit-field">
    <el-input-number
      v-model="innerValue"
      :placeholder="placeholder"
      :min="min"
      :max="max"
      :step="step"
      :precision="precision"
      @change="handleChange"
    />
    <span v-if="units.length === 1" class="unit-field__span">{{ units[0].label }}</span>
    <bs-select v-if="units.length > 1" v-model="unit" :options="units" class="unit-field__select" @change="handleChange" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';

// 存储流程报错信息
@Component({ name: 'TypeUnit' })
export default class TypeUnit extends Vue {
  // 表单配置信息
  @Prop() config!: any;
  @Prop() disabled!: boolean;
  @Prop() value!: string | number;
  unit = '';
  innerValue: undefined | number = 0;
  get units() {
    const { units } = this.config;
    return (Array.isArray(units) ? units : [units]).map((v) => ({ label: v, value: v }));
  }
  get componentProps() {
    return this.config.componentProps || {};
  }
  get placeholder() {
    return this.componentProps.placeholder;
  }
  get min() {
    return this.componentProps.min;
  }
  get max() {
    return this.componentProps.max;
  }
  get step() {
    return this.componentProps.step || 1;
  }
  get precision() {
    return this.componentProps.precision;
  }
  @Watch('value', { immediate: true })
  handleValueChage() {
    this.unit = this.units[0] ? this.units[0].value : '';
    if (typeof this.value === 'number') {
      this.innerValue = Number(this.value);
    } else if (this.value) {
      const REG = /^(\d+)(.+)$/;
      const [, num, unit] = this.value.match(REG) || [];
      this.unit = unit;
      this.innerValue = Number(num);
    }
  }
  handleChange() {
    // 是否需要拼接单位
    this.$emit('change', this.config.spliceUnit ? this.innerValue + this.unit : this.innerValue);
  }
}
</script>

<style lang="scss" scoped>
.unit-field {
  display: flex;
  .el-input-number {
    flex: 1;
    margin-right: 10px;
  }
  .unit-field__select {
    width: 90px;
    ::v-deep .el-select .el-input__inner {
      padding-left: 10px;
      padding-right: 20px;
    }
    ::v-deep .el-input__suffix {
      right: 0px;
    }
  }
  .unit-field__span {
    color: $--bs-color-text-secondary;
  }
}
</style>
