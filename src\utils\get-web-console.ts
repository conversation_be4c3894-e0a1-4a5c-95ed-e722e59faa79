import Vue from 'vue';
export default (key: string) => {
  const [baseUrl, socketUrl] = [
    `http://${window.location.hostname}:${window.location.port}`,
    `/sockjs/webSocketServer?websocketKey=${key}`
  ];
  if (process.env.NODE_ENV === 'development') {
    return `${baseUrl}${window.location.port}${Vue.axios.defaults.baseURL}${socketUrl}`;
  }
  return `${baseUrl}${window.location.pathname.replace('/pipeace.html', '')}${socketUrl}`;
};
