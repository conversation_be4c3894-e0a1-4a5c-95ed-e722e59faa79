import {
  URL_SYS_ORG,
  URL_KAFKA_SYS_ORG,
  URL_SHARE_RESOURCE_BATCH,
  URL_UDF_SYS_ORG,
  URL_SHARE_SQL_UDF_BATCH,
  URL_SHARE_SQL_TABLE_BATCH,
  URL_TABLE_SYS_ORG,
  URL_SHARE_TOPIC_BATCH,
  URL_LIST_SHARE_CATALOG,
  URL_LIST_SHARE_CATALOG_BATCH
} from '@/apis/commonApi';
export const includesPro = (data = '', queryStr = '') => {
  return data.toLowerCase().includes(queryStr.toLowerCase());
};

export const getOrgMap = {
  //获取机构
  table: URL_TABLE_SYS_ORG,
  udf: URL_UDF_SYS_ORG,
  server: URL_SYS_ORG,
  kafka: URL_KAFKA_SYS_ORG,
  catalog: URL_LIST_SHARE_CATALOG
};

export const patchMap = {
  //批量新增或删除
  table: URL_SHARE_SQL_TABLE_BATCH,
  udf: URL_SHARE_SQL_UDF_BATCH,
  server: URL_SHARE_RESOURCE_BATCH,
  kafka: URL_SHARE_TOPIC_BATCH,
  catalog: URL_LIST_SHARE_CATALOG_BATCH
};
