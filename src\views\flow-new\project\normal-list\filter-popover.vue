<template>
  <div>
    <el-popover
      ref="popover"
      v-model="showPopover"
      placement="bottom"
      width="300"
      trigger="click"
      class="filter-popover"
    >
      <div v-for="item in filterList" :key="item.label" class="filter-popover__selections">
        <span class="filter-popover__selections--label">{{ item.label }}</span>
        <el-select v-model="item.value" class="filter-popover__selections--select">
          <el-option
            v-for="el in item.options"
            :key="el.value"
            :label="el.label"
            :value="el.value"
          />
        </el-select>
      </div>
      <div class="filter-popover__popover--header">
        <el-button @click="handleSearch('clear')">重置</el-button>
        <el-button type="primary" @click="handleSearch('confirm')">查询</el-button>
      </div>
    </el-popover>
    <el-button
      v-access="'PA.FLOW.FLOW_MGR.FILTER'"
      v-popover:popover
      size="small"
      icon="iconfont icon-guolv"
    >
      筛选
    </el-button>
  </div>
</template>

<script lang="ts">
import { Vue, Component, PropSync } from 'vue-property-decorator';
@Component
export default class FilterPopover extends Vue {
  @PropSync('visible') showPopover!: boolean;
  filterList = [
    {
      label: '状态',
      options: [
        { label: '全部', value: 'ALL' },
        { label: '已发布', value: 'PUB' },
        { label: '已上线', value: 'PROD' },
        { label: '开发', value: 'DEV' }
      ],
      value: 'ALL'
    },
    {
      label: '运行状态',
      options: [
        { label: '全部', value: 'ALL' },
        { label: '失败', value: 'FAILED' },
        { label: '运行', value: 'RUNNING' },
        { label: '未运行', value: 'NONE' },
        { label: '完成', value: 'FINISHED' },
        { label: '未知', value: 'UNKNOWN' },
        { label: '其他', value: 'OTHER' }
      ],
      value: 'ALL'
    },
    {
      label: '模式',
      options: [
        { label: '全部', value: 'ALL' },
        { label: '流模式', value: 'stream' },
        { label: '批模式', value: 'batch' }
      ],
      value: 'ALL'
    },
    {
      label: '集群类型',
      options: [
        { label: '全部', value: 'ALL' },
        { label: 'yarn_session', value: 'YARN_SESSION' },
        { label: 'standalone', value: 'STANDALONE' },
        { label: 'yarn_per_job', value: 'YARN_PER_JOB' },
        { label: 'yarn_application', value: 'YARN_APPLICATION' }
      ],
      value: 'ALL'
    }
  ];
  get selectType() {
    const filterParams = {
      jobStatus: 'ALL',
      jobRunTimeStatus: 'ALL',
      mode: 'ALL',
      clusterType: 'ALL'
    };
    const paramsMap = {
      '0': 'jobStatus',
      '1': 'jobRunTimeStatus',
      '2': 'mode',
      '3': 'clusterType'
    };
    this.filterList.forEach(({ value }: any, index) => {
      filterParams[paramsMap[Number(index)]] = value;
    });
    return filterParams;
  }
  handleSearch(type) {
    if (type === 'clear') {
      this.filterList.forEach((el: any) => {
        el.value = 'ALL';
      });
      return;
    }
    type === 'confirm' && (this.showPopover = false);
    this.$emit('search', this.selectType);
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-icon-sort {
  transform: rotate(90deg);
}
::v-deep .el-popover {
  padding-top: 0px;
}
.filter-popover {
  &__selections {
    display: flex;
    flex-direction: column;
    &--label {
      font-size: 14px;
      font-weight: 400;
      color: #444444;
      line-height: 20px;
      margin-bottom: 10px;
      margin-top: 14px;
    }
    &--select {
      margin-bottom: 10px;
    }
    & > div {
      display: flex;
    }
  }
  &__popover {
    &--header {
      text-align: right;
      margin-top: 14px;
    }
  }
}
</style>
