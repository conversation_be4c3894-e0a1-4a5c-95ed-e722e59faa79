import { Component, Prop, Provide, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { debounce, throttle } from 'lodash';
import VirtualTreeItemContent from './virtual-tree-item-content';
import VirtualTreeItemPrefix from './virtual-tree-item-prefix';

interface BaseListItem {
  label: string;
  id: string;
  expanded: boolean;
  isIndeterminate: boolean;

  [property: string]: any;
}

interface ListItem extends BaseListItem {
  children: ListItem[];
}

const treeMaps = new Map();
const sourceTreeMaps = new Map();
@Component({
  components: { VirtualTreeItemContent, VirtualTreeItemPrefix },
  directives: {
    hide: {
      inserted(el, bind, vnode) {
        if (el.clientWidth === el.scrollWidth) {
          (vnode as any).componentInstance.$destroy();
        }
      }
    }
  }
})
export default class VirtualTree extends PaBase {
  @Prop({ default: [] }) data!: ListItem[];
  @Prop({}) currentNodekey!: string;
  @Prop({ type: String, default: 'id' }) nodeKey!: string;
  @Prop({ type: String, default: 'label' }) labelKey!: string;
  @Prop({ default: [] }) expandedKeys!: Array<number | string>;
  @Prop({ default: false }) showCheckbox!: boolean;
  @Prop({ default: true }) showTip!: boolean;
  // showCheck状态下需要返回的key，默认和nodeKey一致
  @Prop({ default: '' }) selectKey!: string;
  @Provide('sourceTreeMaps')
  sourceTreeMaps = sourceTreeMaps;
  // 一行高度
  lineHeight = 40;
  startIndex = 0;
  endIndex = 0;
  startOffset = 0;
  // 可是区域高度
  visibleHeight = 0;
  // 完整的数据
  sourceTreeData: ListItem[] = [];
  // 当前需要显示的完整数据
  treeData: ListItem[] = [];
  selectIds: any[] = [];
  selectDatas: any[] = [];
  key = Date.now();
  get totalHeight() {
    return this.treeData.length * this.lineHeight;
  }

  get visibleCount() {
    return Math.ceil(this.visibleHeight / this.lineHeight);
  }

  // 可视区域显示的数据
  get visibleData() {
    // 前后多加载10条数据，处理滚动时空白
    return this.treeData.slice(this.startIndex, this.endIndex + 1);
  }

  get transform() {
    return `translate3d(0,${this.startOffset}px,0)`;
  }

  @Watch('currentNodekey')
  handler() {
    this.key = Date.now();
  }

  // TODO:节流效果不好，暂时不处理
  handleScroll: any = throttle(this.handleScrollEvent, 0);
  // mouseenter的防抖事件
  handelLabelMouseenter: any = debounce(this.handelLabelMouseenterFun, 300, {
    leading: true,
    trailing: true
  });

  created() {
    // 数据源不需要进行响应式处理
    this.sourceTreeData = Object.freeze(this.transformData(this.data));
    // 显示checkbox且selectKey未传入，默认取nodekey的功能
    if (!this.selectKey && this.showCheckbox) {
      this.selectKey = this.nodeKey;
    }
    // 存在currentNodekey自动展开父节点
    if (this.currentNodekey) {
      const parentId = treeMaps.get(this.currentNodekey).parentId;
      this.handleExpanded(treeMaps.get(parentId));
    }
  }

  mounted() {
    this.visibleHeight = this.$el.clientHeight;
    this.startIndex = 0;
    this.endIndex = this.startIndex + this.visibleCount;
    window.addEventListener('resize', this.handelResize);
  }

  destroyed() {
    window.removeEventListener('resize', this.handelResize);
  }

  @Watch('data')
  dateChange(val) {
    this.resetData();
    this.$nextTick(() => {
      this.sourceTreeData = Object.freeze(this.transformData(val));
    });

    // 通知外部重置选中数据
    this.emitCheckChange();
  }

  // 数据转换
  transformData(data, level = 1, parentId: any = null) {
    if (Array.isArray(data)) {
      return data.map((item) => {
        const itemdata = {
          id: item[this.nodeKey],
          label: item[this.labelKey],
          check: false,
          level,
          isSearch: item.isSearch,
          isIndeterminate: false,
          expanded: false,
          parentId,
          children: item.children
            ? this.transformData(item.children, level + 1, item[this.nodeKey])
            : []
        };
        // 此处只处理二级树的情况
        if (level === 1) {
          this.treeData.push(itemdata);
          // 如果id包含在expandedKeys中则默认把children都添加进treeData中
          if (this.expandedKeys.includes(itemdata.id) || itemdata.isSearch) {
            itemdata.expanded = true;
            this.treeData.push(...itemdata.children);
          }
        }
        treeMaps.set(itemdata.id, itemdata);
        // 元数据存储
        sourceTreeMaps.set(itemdata.id, item);
        return itemdata;
      });
    } else {
      return [];
    }
  }

  // 处理select回调事件 （只处理两级状态，只返回二级节点的id，之后再优化！）
  handleCheckChange(item: ListItem) {
    const treeItem: ListItem = treeMaps.get(item.id);
    // const sourceItem = sourceTreeMaps.get(item.id);
    if (item.level === 1) {
      // 重置父节点的半选状态
      item.isIndeterminate = false;
      if (item.check) {
        this.selectIds.push(...treeItem.children.map((s) => s.id));
        // 修改treeItem的check状态
        treeItem.children.forEach((c) => {
          c.check = true;
        });
      } else {
        const childrenIds: any = [];
        treeItem.children.forEach((c) => {
          c.check = false;
          childrenIds.push(c.id);
        });
        this.selectIds = this.selectIds.filter((e) => !childrenIds.includes(e));
      }
    } else if (item.level === 2) {
      if (item.check) {
        this.selectIds.push(item.id);
      } else {
        this.selectIds.splice(this.selectIds.indexOf(item.id), 1);
      }
      // 确认父节点的选中状态
      const parentTreeItem: ListItem = treeMaps.get(item.parentId);
      const childrenCheckLen = parentTreeItem.children.filter((c) => c.check).length;
      parentTreeItem.isIndeterminate =
        childrenCheckLen < parentTreeItem.children.length && childrenCheckLen > 0;
      parentTreeItem.check = parentTreeItem.children.length === childrenCheckLen;
    }
    this.emitCheckChange();
  }

  // check emit
  emitCheckChange() {
    // 去重
    this.selectIds = [...new Set(this.selectIds)];
    const selectKeys: any[] = [];
    // 获取对应的元数据
    const selectDatas = this.selectIds.map((id) => {
      const target = sourceTreeMaps.get(id);
      selectKeys.push(target[this.selectKey]);
      return target;
    });
    this.$emit('checkChange', selectKeys, selectDatas);
  }

  // 处理展开点击事件
  handleExpanded(item: ListItem) {
    item.expanded = !item.expanded;
    const index = this.treeData.findIndex((t) => t.id === item.id);
    // 展开操作 把子集加入近列表
    if (item.expanded) {
      const sourceItem = treeMaps.get(item.id);
      this.treeData.splice(index + 1, 0, ...sourceItem.children);
      // tips:后期有需要可以抛出一个事件。此处先简单处理
      this.$emit('update:expandedKeys', this.expandedKeys.concat([item.id]));
    } else {
      this.treeData.splice(index + 1, item.children.length);
      this.$emit(
        'update:expandedKeys',
        this.expandedKeys.filter((e) => e !== item.id)
      );
    }
  }

  // 处理页面滚动
  handleScrollEvent() {
    const scrollTop = this.$el.scrollTop;
    this.startIndex = Math.ceil(scrollTop / this.lineHeight);
    this.endIndex = this.startIndex + this.visibleCount;
    this.startOffset = scrollTop - (scrollTop % this.lineHeight);
  }

  // 处理label点击事件
  handelLabelClick(item: ListItem) {
    const sourceItemData = sourceTreeMaps.get(item.id);
    this.$emit('update:currentNodekey', item.id);
    this.$emit('nodeClick', sourceItemData);
  }

  // 处理label mounseenter事件
  handelLabelMouseenterFun($event: any, item: ListItem) {
    const sourceItemData = sourceTreeMaps.get(item.id);
    this.$emit('nodeEnter', $event, sourceItemData);
  }

  // 处理label mounseleave事件
  handelLabelMouseleave() {
    this.$emit('nodeLeave');
  }

  // 处理窗口更改事件
  handelResize() {
    this.visibleHeight = this.$el.clientHeight;
    this.handleScrollEvent();
  }

  // resetMaps
  resetData() {
    sourceTreeMaps.clear();
    treeMaps.clear();
    this.treeData = [];
    this.selectIds = [];
  }

  /* 根据返回数据id，对数据进行反选 */
  changeSelectedStatus(list: any[] = []) {
    const ids = list.map(({ id }: any) => id);
    const treeIds = this.selectIds.filter((id) =>
      ids.includes(sourceTreeMaps.get(id).id) ? id : null
    );
    treeIds.forEach((id: any) => {
      const item = this.treeData.find((el: any) => el.id === id);
      if (item) {
        item.check = !item.check;
        this.handleCheckChange(item);
      }
    });
  }
}
