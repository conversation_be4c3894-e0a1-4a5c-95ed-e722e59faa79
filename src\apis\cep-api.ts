import { del, post, get, put } from './utils/net';

// 删除cep模板
export const deleteCeps = (data: unknown) => {
  return del('rs/pa/flink/cep/deleteBatch', {}, { data });
};

// 获取cep列表
export const getCeps = (data: unknown) => {
  return post('rs/pa/flink/cep/list', data);
};

// 查看是否可以编辑
export const canUpdate = (id: string) => {
  return get('rs/pa/flink/cep/canUpdate?id=' + id);
};

// 获取基本信息
export const getInfo = (id: string) => {
  return get('rs/pa/flink/cep/get?id=' + id);
};

// 获取历史版本的基本信息
export const getHistoryById = (id: string) => {
  return get('rs/pa/flink/cep/getHistoryById?id=' + id);
};

export const getDdById = (id: string) => {
  return get('rs/pa/flink/cep/getDdById?id=' + id);
};

// 获取历史记录列表
export const getHistorys = (id: string) => {
  return get('rs/pa/flink/cep/getHistory?id=' + id);
};

// 历史记录：回滚
export const rollBack = (historyId: string) => {
  return put('rs/pa/flink/cep/rollBack?historyId=' + historyId);
};

// 获取引用关系列表
export const getRelations = (data: unknown) => {
  return post('rs/pa/flink/cep/getAllRefList', data);
};

export const URL_GET_CEP_DETAIL = 'rs/pa/flink/cep/get'; // 获取cep详细信息
export const URL_GET_CEP_ADD = 'rs/pa/flink/cep/add'; // 新建cep模版
export const URL_GET_CEP_UPDATE = 'rs/pa/flink/cep/update'; // 更新cep模版
export const URL_GET_DDID_OPTIONS = 'rs/pa/flink/cep/getAllDd'; // 获取数据定义对应options
