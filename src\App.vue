<template>
  <div id="app">
    <bs-menu
      v-if="!contentIsFullScreen"
      :default-active="active"
      :title="$store.getters.platformName"
      :data="menuListData"
      :collapse="isCollapse"
      :logo="getAction()"
      router
    >
      <div slot="footer" class="app-version">
        {{ isCollapse ? '' : `V${Version.version} ${Version.buildDate}` }}
      </div>
    </bs-menu>
    <main v-if="!contentIsFullScreen" v-loading="loading">
      <bs-header
        :show-language="showLanguage"
        :collapse="isCollapse"
        :is-open="!isCollapse"
        :username="userName"
        :user-info-list="userInfoList"
        :show-help="false"
        :language="language"
        @language-change="handleLanguageChange"
        @toogle-menu="changeCollapse"
        @user-click="userClick"
      >
        <bs-search
          v-show="curTabTitle"
          slot="extension"
          v-model.trim="keywords"
          class="header-search"
          :placeholder="$t('pa.placeholder.input')"
          :auto-search="false"
          @search="handleSearch"
          @keyup.enter.native="handleSearch"
        />
      </bs-header>
      <div class="bs-layout">
        <!-- header -->
        <bs-tab-nav
          ref="tabsNav"
          :exclude="['pkgList', 'CommonServiceCustom', 'Refresh']"
          :default-tab="defaultTab"
          @reload="reload"
          @del-tab="handleDelTab"
        />
        <bs-wujie
          v-if="hasPortalAuth"
          name="portal"
          :props="{ userInfo: $store.getters.userInfo, previewPath: '/bs/portal' }"
        />
        <bs-wujie
          v-if="hasAssetsAuth"
          name="assets"
          :props="{ userInfo: $store.getters.userInfo, baseURL: '/bs/portal', menuInfo: {} }"
        />
      </div>
    </main>
    <router-view v-if="contentIsFullScreen" />
    <system-log v-if="visible" :visible.sync="visible" :log="logContent" />
  </div>
</template>

<script lang="ts">
import { Component, Watch, Vue, ProvideReactive, Provide } from 'vue-property-decorator';
import { Route } from 'vue-router';
import { SET_IS_COLLAPSE } from '@/store/event-name';
import { get } from '@/apis/utils/net';
import { baseUrl } from '@/config';
import SystemLog from '@/components/system-log.vue';
import getWebConsole from '@/utils/get-web-console';
import { portalAppMenu, assetsAppMenu } from '@/utils';
import { emitEvents } from '@bs/wujie';
import { State } from 'vuex-class';
import { getLanguage, setLanguage } from '@/i18n';
import { getAboutVersion } from '@/apis/portalApi';
// 引入全局样式
import './style/style.scss';
import '@/assets/iconfonts/iconfont.css';
@Component({ components: { SystemLog } })
export default class App extends Vue {
  @Provide('enableSql') enableSql = this.$store.getters.enableSql;
  @Provide('enableJar') enableJar = this.$store.getters.enableJar;

  @State((state) => state.app.isCollapse)
  isCollapse!: boolean;

  routePath = '';
  levelFirstPath = '';
  levelSecondPath = '';
  visible = false;
  loading = false;
  logContent = '';
  socket: any = null;
  keywords = '';
  defaultTab = {
    title: this.$t('pa.homePage'),
    value: '/',
    closable: false
  };
  active = '/';
  Version = { buildDate: '', version: '' };
  language = 'zh-CN';
  showLanguage = false;

  @ProvideReactive()
  get isCloud() {
    return Boolean(this.$store.getters.isCloud);
  }
  get authorities() {
    return this.$store.getters.authorities;
  }
  get userName() {
    return this.$store.getters.userName;
  }
  get menuListData() {
    return this.$store.getters.menu;
  }
  get userInfoList() {
    return [
      !this.isCloud && { key: 'userCenter', value: this.$t('pa.userCenter') },
      { key: 'logout', value: this.$t('pa.logout') }
    ];
  }
  get curTabTitle() {
    return /^(?!\/globalSearch).*$/.test(this.active);
  }
  get contentIsFullScreen() {
    return ['/flowVersion', '/flowFullScreen'].includes(this.$route.path);
  }
  get hasPortalAuth() {
    return portalAppMenu.some(({ access }) => access.some((it: string) => this.authorities.includes(it)));
  }
  get hasAssetsAuth() {
    return assetsAppMenu.some(({ access }) => access.some((it: string) => this.authorities.includes(it)));
  }

  created() {
    this.getLanguage();
    this.getAppVersion();
  }
  mounted() {
    Vue.prototype.$tabNav = this.$refs.tabsNav;
    this.connection(this.$store.getters.userName);
    // 增加ctrl+i打开窗口查看日志
    document.onkeydown = (e) => {
      if (e.ctrlKey && e.key === 'i') {
        this.visible = true;
      }
    };
  }

  getAction() {
    return baseUrl.prev + '/rs/pa/portal/logo/get?type=2&orderBy=1' || require('./assets/bs-menu-logo.png');
  }

  // 切换用户信息下拉数据
  userClick(command: string) {
    const url = process.env.NODE_ENV === 'development' ? '/' : './';
    switch (command) {
      case 'logout':
        this.logout();
        break;
      case 'userCenter':
        this.$router.push('/portal/sysconf/userProfile');
        break;
      default:
        window.open(url + command);
    }
  }

  //搜索
  handleSearch() {
    if (!this.keywords) {
      this.$message.error(this.$t('pa.placeholder.keyWord'));
    } else {
      this.$router.push({
        path: '/globalSearch',
        query: { keywords: this.keywords }
      });
    }
  }

  async logout() {
    const Url = this.isCloud ? '/rs/pa/logout' : 'j_spring_security_logout';
    await get(Url);
    window.location.href = this.$store.getters.loginUrl || './login.html';
  }

  @Watch('$route')
  routeChanged(val: Route) {
    const index = val.fullPath.indexOf('/detail?');
    if (index > -1) {
      this.routePath = val.fullPath.slice(0, index);
    } else {
      this.routePath = val.path;
    }
    this.levelFirstPath = '/' + this.routePath.split('/')[1];
    this.levelSecondPath = this.levelFirstPath + '/' + this.routePath.split('/')[2];
    // TODO:流程设计详情链接刷新后对应菜单栏高亮
    this.active = this.levelSecondPath === '/element/clusters' ? '/element/service' : this.levelSecondPath;
  }

  onMessage(ret) {
    if (this.logContent.length > 10000) {
      this.logContent = '';
    }
    this.logContent = this.logContent + JSON.parse(ret.data).content;
  }

  initWebSocket(userId: string) {
    if (this.socket == null) {
      this.connection(userId);
    } else {
      this.disconnect();
      this.connection(userId);
    }
  }

  connection(userId: string) {
    // 建立连接对象
    const SockJS = require('sockjs-client');
    this.socket = new SockJS(getWebConsole(userId));
    this.socket.onmessage = this.onMessage;
  }

  disconnect() {
    try {
      this.socket.close();
    } catch (e) {
      // do nothing
    }
    this.socket = null;
  }

  destroyed() {
    this.disconnect();
  }

  async getAppVersion() {
    const { success, data, msg } = await get('/rs/pa/portal/getVersionAndDate');
    if (!success) return this.$message.error(msg);
    this.Version = { ...this.Version, ...data };
  }

  changeCollapse(val) {
    this.$store.commit(SET_IS_COLLAPSE, val);
  }
  reload() {
    emitEvents.reload(this.$route.fullPath);
  }
  handleDelTab(val: any) {
    emitEvents.delTab(val);
  }
  //语言切换
  handleLanguageChange(value) {
    this.language = value;
    this.$i18n.locale = value;
    setLanguage(value);
    window.location.reload();
  }

  async getLanguage() {
    this.language = getLanguage();
    Vue.prototype.isEn = this.language === 'en-US';
    this.isEn && document.body.classList.add('pa-en');
    // 是否展示国际化切换
    const { useI18n } = (await getAboutVersion()) || {};
    this.showLanguage = useI18n;
  }
}
</script>

<style lang="scss" scoped>
::v-deep .bs-header {
  line-height: unset;
}
.app-version {
  text-align: center;
  line-height: 48px;
  font-size: 12px;
  font-weight: 400;
  color: rgba(199, 204, 218, 0.5);
}

.header-search {
  margin-right: 15px;
}
</style>
