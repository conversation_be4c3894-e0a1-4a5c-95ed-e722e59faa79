<template>
  <div id="app">
    <bs-menu
      :default-active="active"
      :title="platformName"
      :data="menuListData"
      :collapse="isCollapse"
      :logo="getAction()"
      :background-color="menuBackgroundColor"
      :active-color="menuActiveColor"
      router
    >
      <div slot="footer" class="app-version">
        {{ isCollapse ? '' : `V${Version.version} ${Version.buildDate}` }}
      </div>
    </bs-menu>
    <main v-loading="loading">
      <bs-header
        :collapse="isCollapse"
        :is-open="!isCollapse"
        :username="userName"
        :user-info-list="userInfoList"
        :help-list="helpList"
        @help-click="helpClick"
        @toogle-menu="changeCollapse"
        @user-click="userClick"
      >
        <bs-search
          v-show="curTabTitle"
          slot="extension"
          v-model.trim="keywords"
          class="header-search"
          placeholder="请输入"
          :auto-search="false"
          @search="handleSearch"
          @keyup.enter.native="handleSearch"
        />
      </bs-header>
      <div class="bs-layout">
        <bs-tab-nav
          ref="tabsNav"
          :exclude="['pkgList', 'MonitorFlow', 'CommonServiceCustom', 'Refresh']"
          :default-tab="defaultTab"
        />
      </div>
    </main>
    <system-log v-if="visible" :visible.sync="visible" :log="logContent" />
  </div>
</template>

<script lang="ts">
import { Component, Watch, Vue, ProvideReactive, Provide } from 'vue-property-decorator';
import { Route } from 'vue-router';
import { State } from 'vuex-class';
import { UPDATE_COLLAPSE, RESIZE_CHARTS } from '@/store/event-names/mutations';
import { generateMenudata } from './utils';
import { get } from '@/apis/utils/net';
import { baseUrl } from '@/config';
import SystemLog from '@/components/system-log.vue';
import getWebConsole from '@/utils/get-web-console';

// 引入全局样式
import './style/style.scss';
import '@/assets/iconfonts/iconfont.css';
@Component({ components: { SystemLog } })
export default class App extends Vue {
  @State((state) => state.userInfo.authorities)
  authorities!: string;
  @State((state) => state.userInfo.userName)
  userName!: string;
  @State((state) => state.others.isCollapse)
  isCollapse!: boolean;
  @Provide('enableSql') enableSql = this.$store.state.others.enableSql;
  menuListData: any = [];
  platformName = '邦盛科技';
  routePath = '';
  levelFirstPath = '';
  levelSecondPath = '';
  visible = false;
  loading = false;
  logContent = '';
  socket: any = null;
  keywords = '';
  defaultTab = {
    title: '主页',
    value: '/',
    closable: false
  };
  active = '/';
  Version = { buildDate: '', version: '' };
  helpList = [{ key: 'previewText', value: '文档' }];

  @ProvideReactive()
  get isFuseMode() {
    return Boolean(this.$store.state?.others?.isFuseMode);
  }

  get userInfoList() {
    return [
      !this.isFuseMode && { key: 'userCenter', value: '用户中心' },
      { key: 'logout', value: '退出登录' }
    ];
  }

  get curTabTitle() {
    return /^(?!\/globalSearch).*$/.test(this.active);
  }

  get menuBackgroundColor() {
    return this.$store.getters.menuBackgroundColor;
  }
  get menuActiveColor() {
    return this.$store.getters.menuActiveColor;
  }

  created() {
    this.menuListData = generateMenudata();
    this.getPlatformName();
    this.getAppVersion();
  }

  mounted() {
    Vue.prototype.$tabsNav = this.$refs.tabsNav;
    this.connection(this.$store.state.userInfo.userName);
    // 增加ctrl+i打开窗口查看日志
    document.onkeydown = (e) => {
      if (e.ctrlKey && e.key === 'i') {
        this.visible = true;
      }
    };
  }

  getPlatformName() {
    get('/rs/pa/conf/name').then((res) => {
      this.platformName = res.data || this.platformName;
      document.title = res.data || this.platformName;
    });
  }

  getAction() {
    return (
      baseUrl.prev + '/rs/pa/portal/logo/get?type=2&orderBy=1' ||
      require('./assets/bs-menu-logo.png')
    );
  }

  // 切换用户信息下拉数据
  userClick(command: string) {
    const url = process.env.NODE_ENV === 'development' ? '/' : './';
    switch (command) {
      case 'logout':
        this.logout();
        break;
      case 'userCenter':
        window.open('main.html#/sysconf/userProfile');
        break;
      default:
        window.open(url + command);
    }
  }

  //帮助点击
  helpClick(value: string) {
    value && this[value]();
  }

  // 预览文档
  async previewText() {
    this.loading = true;
    try {
      const res = await get('/rs/pa/installationPackage/downloadPdf', null, {
        responseType: 'blob'
      });
      this.loading = false;
      if (res.blob) {
        const { blob, fileName } = res;
        const url = window.URL.createObjectURL(new Blob([blob], { type: 'application/pdf' }));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.open(url, '_blank');
        window.URL.revokeObjectURL(url);
      }
    } catch (e) {
      this.loading = false;
    }
  }

  //搜索
  handleSearch() {
    if (!this.keywords) {
      this.$message.error('请输入搜索关键字');
    } else {
      this.$router.push({
        path: '/globalSearch',
        query: { keywords: this.keywords }
      });
    }
  }

  async logout() {
    const Url = this.isFuseMode ? '/rs/pa/logout' : 'j_spring_security_logout';
    await get(Url);
    sessionStorage.removeItem('token');
    window.location.href = this.$store.state.app.loginUrl || './login.html';
  }

  @Watch('$route')
  routeChanged(val: Route) {
    const index = val.fullPath.indexOf('/detail?');
    if (index > -1) {
      this.routePath = val.fullPath.slice(0, index);
    } else {
      this.routePath = val.path;
    }
    this.levelFirstPath = '/' + this.routePath.split('/')[1];
    this.levelSecondPath = this.levelFirstPath + '/' + this.routePath.split('/')[2];
    // TODO:流程设计详情链接刷新后对应菜单栏高亮
    this.active =
      this.levelSecondPath === '/element/clusters' ? '/element/service' : this.levelSecondPath;
  }

  onMessage(ret) {
    if (this.logContent.length > 10000) {
      this.logContent = '';
    }
    this.logContent = this.logContent + JSON.parse(ret.data).content;
  }

  initWebSocket(userId: string) {
    if (this.socket == null) {
      this.connection(userId);
    } else {
      this.disconnect();
      this.connection(userId);
    }
  }

  connection(userId: string) {
    // 建立连接对象
    const SockJS = require('sockjs-client');
    this.socket = new SockJS(getWebConsole(userId));
    this.socket.onmessage = this.onMessage;
  }

  disconnect() {
    try {
      this.socket.close();
    } catch (e) {
      // do nothing
    }
    this.socket = null;
  }

  destroyed() {
    this.disconnect();
  }

  getAppVersion() {
    get('/rs/pa/portal/getVersionAndDate').then((res) => {
      this.Version = res.data;
    });
  }

  changeCollapse(val) {
    this.$store.commit(UPDATE_COLLAPSE, val);
    this.$store.commit(RESIZE_CHARTS, !val);
  }
}
</script>

<style lang="scss" scoped>
.app-version {
  text-align: center;
  line-height: 48px;
  font-size: 12px;
  font-weight: 400;
  color: rgba(199, 204, 218, 0.5);
}

.header-search {
  margin-right: 15px;
  ::v-deep .el-input--suffix {
    height: 100%;
    display: flex;
    align-items: center;
  }
  ::v-deep .el-input__suffix-inner {
    display: flex;
    justify-content: center;
  }
}
</style>
