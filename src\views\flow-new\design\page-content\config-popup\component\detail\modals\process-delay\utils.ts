import { FormData } from './type';

export const sha256 = require('sha256');

export const generateFormData: () => FormData = () => ({
  delayTime: 1,
  keyByModel: '',
  paTimeCharacteristic: '',
  timeModel: '',
  orderlessTime: 1,
  conditionA: {
    logicType: '',
    expr: '',
    resultExpression: '',
    conditions: [
      {
        name: 'A',
        funcName: '',
        funcId: '',
        funcType: '',
        funcArgs: []
      }
    ]
  },
  conditionB: {
    logicType: '',
    expr: '',
    resultExpression: '',
    conditions: [
      {
        name: 'A',
        funcName: '',
        funcId: '',
        funcType: '',
        funcArgs: []
      }
    ]
  }
});

export default {};

export const isString = (data, defaultValue = '') => (typeof data === 'string' && data.length > 0 ? data : defaultValue);
export const isArray = (data, defaultValue = []) => (Array.isArray(data) ? data : defaultValue);
export const isObject = (data, defaultValue = {}) =>
  Object.prototype.toString.call(data) === '[object Object]' ? data : defaultValue;

export const isValidType = (type) => ['PRIVATE', 'SHARE', 'DEFAULT'].includes(type);
export const hasSourceCode = (type) => ['PRIVATE', 'SHARE'].includes(type);
export const isDefaultFunc = (type) => 'DEFAULT' === type;
