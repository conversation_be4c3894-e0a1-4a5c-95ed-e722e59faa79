<template>
  <div class="operate__container">
    <el-tooltip
      v-for="el in config.options"
      :key="el.type"
      effect="light"
      placement="top"
      :content="el.tooltip"
    >
      <div
        :class="handleOperClass(el, scopeIndex)"
        class="form-table-icon"
        @click="click(el.type, index, scopeIndex)"
      ></div>
    </el-tooltip>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { debounce } from 'lodash';

@Component
export default class OperateType extends Vue {
  @Prop({ type: Number }) index!: number;
  @Prop({ type: Number }) scopeIndex!: number;
  @Prop({ type: Number }) length!: number;
  @Prop({ type: Object, default: () => ({}) }) config!: any;

  private click = debounce(this.handleClick, 600);

  /* 处理操作符class */
  handleOperClass({ icon, type }: any, index: number) {
    const classArr = ['iconfont', icon, 'operate-icon'];
    if ((index === 0 && type === 'up') || (index === this.length - 1 && type === 'down')) {
      classArr.push('operate-icon--disabled');
    }
    return classArr;
  }
  handleClick(type: string, index: number, $index: number) {
    this.$emit('click', ...[type, index, $index]);
  }
}
</script>

<style lang="scss" scoped>
.operate {
  &__container {
    display: flex;
    align-content: center;
    height: 40px;
  }
  /* 操作符 */
  &-icon {
    display: inline-block;
    margin-right: 17px;
    padding: 5px;
    width: 14px;
    height: 14px;
    color: #377cff;
    cursor: pointer;
    box-sizing: border-box;
    &--disabled {
      color: #aaaaaa;
      cursor: not-allowed;
    }
  }
}
</style>
