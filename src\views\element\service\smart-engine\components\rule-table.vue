<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">规则信息</div>
      <div class="bs-detail__header-operation">
        <el-button @click="getListData">刷新</el-button>
      </div>
    </div>
    <div class="tab-content">
      <base-table
        v-loading="tableLoading"
        :height="'100%'"
        :table-data="tableData"
        :table-config="tableConfig"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Component } from 'vue-property-decorator';
import { URL_RES_DETAIL_SDM_RULE } from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {
    'base-table': () => import('@/components/base-table.vue')
  }
})
export default class ScriptTable extends PaBase {
  tableLoading = false;
  tableData: ITableData = {
    columnData: [],
    tableData: []
  };
  tableConfig: ITableConfig = {
    width: 150,
    columnsExtend: {}
  };

  getListData() {
    this.tableLoading = true;
    this.doGet(URL_RES_DETAIL_SDM_RULE, {
      params: {
        id: this.$route.query.id
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.tableData = {
          ...resp.data
        };
      });
      this.tableLoading = false;
    });
  }

  mounted() {
    this.getListData();
  }
}
</script>
<style scoped>
.my-card_body ::v-deep .el-card__body {
  padding: 0px 0px 0px 0px;
}
.my-wrap-row {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
}
.my-wrap-col {
  border: 1px solid #000;
}
.my-wrap-tag {
  width: 350px;
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
