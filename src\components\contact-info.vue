<template>
  <bs-dialog
    title="人员信息"
    width="490px"
    :visible.sync="showUserInfo"
    :before-close="
      () => {
        showUserInfo = false;
      }
    "
  >
    <div class="info">
      <div class="info-row">
        <span class="info-row__label">用户名</span>
        <span class="info-row__content">{{ info.username }}</span>
      </div>
      <div class="info-row">
        <span class="info-row__label">手机号</span>
        <span class="info-row__content">{{ info.mobile }}</span>
      </div>
      <div class="info-row">
        <span class="info-row__label">姓名</span>
        <span class="info-row__content">{{ info.realname }}</span>
      </div>
      <div class="info-row">
        <span class="info-row__label">邮箱</span>
        <span class="info-row__content">{{ info.email || '--' }}</span>
      </div>
      <div class="info-row">
        <span class="info-row__label">归属机构</span>
        <span class="info-row__content">{{ info.orgName }}</span>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="showUserInfo = false">关 闭</el-button>
    </span>
  </bs-dialog>
</template>
<script lang="ts">
import { Component } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { URL_PORTAL_GETUSERINFO } from '@/apis/commonApi';
@Component({
  components: {}
})
export default class ContactInfo extends PaBase {
  showUserInfo = false;

  info: any = {};

  open(names: any) {
    this.showUserInfo = true;
    this.getUserInfo(names[0]);
  }
  getUserInfo(username: string) {
    this.info = {};
    this.showUserInfo = true;
    this.doGet(URL_PORTAL_GETUSERINFO, {
      params: {
        username
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.info = resp.data;
      });
    });
  }
}
</script>
<style lang="scss" scoped>
.info {
  padding: 0 30px;
  &-row {
    margin-bottom: 20px;
  }
  &-row__label {
    display: inline-block;
    width: 80px;
    padding-right: 20px;
    text-align: right;
    color: $--bs-color-text-primary;
  }
  &-row__content {
    color: $--bs-color-text-secondary;
  }
}
</style>
