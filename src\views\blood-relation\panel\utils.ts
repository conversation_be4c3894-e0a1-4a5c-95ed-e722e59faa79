import i18n from '@/i18n';
export const GetRightArray = (data: any) => (Array.isArray(data) ? data : []);
export const GetBaseInfoList = (list = []) => {
  return GetRightArray(list).map((el: any) => {
    return {
      hasAccess: el.hasAccess, // 是否有跳转权限
      id: el.id || null, // 唯一表示 + 显示标识
      label: el.key,
      value: el.value,
      type: el.serviceType, // 资源类型
      kind: el.resType || null, // 资源大类型
      projectId: el.projectRootId || null,
      tag: el.dynamic ? 'dynamic' : el.depUpstream ? 'dep' : '' // 是否为动态资源或依赖上游
    };
  });
};

// 标签信息 名称/背景色/
export const TAG_MAPS = {
  dynamic: [i18n.t('pa.blood.dynamic'), 'red'],
  dep: [i18n.t('pa.blood.dep'), 'orange']
};

export const GetRelationInfoList = (data = {}) => {
  return Object.keys(data)
    .sort()
    .map((label) => ({ label, value: data[label] }));
};
export const GetTableInfo = ({ columnData = [], tableData = [] } = {}) => {
  return {
    columnData: GetRightArray(columnData)
      .map((el: any) => {
        return {
          kind: el.dataType,
          label: el.label,
          value: el.prop,
          valueKey: el.propertyField,
          typeKey: el.value
        };
      })
      .concat([{ value: 'action', label: i18n.t('pa.action.action'), fixed: 'right' }] as any[]),
    tableData: GetRightArray(tableData)
  };
};
export interface Table {
  columnData: any[];
  tableData: any[];
}
