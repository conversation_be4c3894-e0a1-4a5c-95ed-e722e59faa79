<template>
  <div style="height: 100%; padding-bottom: 20px; background: #fff">
    <div class="tab-title tab-bg">
      <div class="title-text">
        引用关系
        <el-select v-model="selectType" class="depend-table__select" @change="getDependList(true)">
          <el-option
            v-for="item in relationTypeList"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
        <bs-select
          v-if="selectType === 'JOB'"
          v-model="flowStatus"
          :options="flowStatusOptions"
          placeholder="全部状态"
          clearable
          class="marL10"
          @change="getDependList(true)"
        />
      </div>
      <el-button type="primary" style="display: none" size="small" @click="viewBatchInfo">
        批量操作信息
      </el-button>
      <el-button type="primary" size="small" @click="downloadRelationList">下载引用关系</el-button>
    </div>
    <div class="tab-content">
      <bs-table
        ref="table"
        v-loading="tableLoading"
        :height="height"
        :page-data="pageData"
        :data="dependTableData"
        :column-data="columnData"
        :crossing="selectType === 'JOB'"
        @refresh="getDependList"
        @selection-change="handleSelectionChange"
        @page-change="handleCurrentChange"
      >
        <div v-if="selectType === 'JOB'" slot="headerOperator" class="table-header-operator">
          <template v-for="el in buttonList">
            <el-button
              v-if="el.type === 'ElButton'"
              :key="el.label"
              size="small"
              @click="headerOperateHandler('', el.event)"
            >
              {{ el.label }}
            </el-button>
            <el-dropdown
              v-if="el.type === 'ElDropdown'"
              :key="el.label"
              @command="headerOperateHandler($event, el.event)"
            >
              <el-button size="small">{{ el.label }}</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="item in el.options"
                  :key="item.command"
                  :command="item.command"
                >
                  {{ item.text }}
                  <el-tooltip effect="light" placement="top" :content="item.content">
                    <i :class="item.icon"></i>
                  </el-tooltip>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </div>
      </bs-table>
      <!-- 批量操作信息 -->
      <batch-info-dialog
        v-if="showBatchInfo"
        ref="batchInfoDialog"
        :visible.sync="showBatchInfo"
        :data="batchInfo"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject } from 'vue-property-decorator';
import { URL_EXPORT_SERVICE_RELATION_LIST, URL_RES_FINDDEPENDS } from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
import { get } from '@/apis/utils/net';
import { hasPermission } from '@/utils';
import { batchOperate, batchOperateDetails, perPublishFlows, preOnline } from '@/apis/flowNewApi';
import dayjs from 'dayjs';
const BATCH_EVENT_TYPE = {
  CANCEL_PUB: 'DEV',
  PUB: 'PUB',
  ONLINE: 'ONLINE',
  OFFLINE: 'OFFLINE'
};
@Component({
  components: {
    'batch-info-dialog': () => import('./modals/batch-info-dialog.vue')
  }
})
export default class DependTable extends PaBase {
  height = '400px';
  tableLoading = false;
  dependTableData = [];
  columnData = [];
  pageData = {
    pageSize: 20,
    currentPage: 1,
    total: 0
  };
  relationTypeList = [
    {
      label: '流程',
      value: 'JOB'
    },
    {
      label: '服务',
      value: 'SERVER'
    },
    {
      label: '表',
      value: 'TABLE'
    }
  ];
  selectType = 'JOB';
  flowStatus = '';
  flowStatusOptions = [
    { label: '已发布', value: 'PUB' },
    { label: '已上线', value: 'PROD' },
    { label: '开发', value: 'DEV' }
  ];
  jobStatusMap = {
    DEV: '开发',
    INPUB: '发布中',
    PUB: '已发布',
    INPROD: '上线中',
    PROD: '已上线'
  };
  checkedRows: any[] = [];
  batchInfo = {};
  showBatchInfo = false;
  @Inject('comParams') comParams;
  @Inject('enableSql') enableSql;
  @Inject('comDetailRecord') comDetailRecord;
  get id() {
    return this.comDetailRecord.val.id || '';
  }
  get buttonList() {
    // 权限过滤
    return [
      {
        type: 'ElDropdown',
        label: '停止',
        event: BATCH_EVENT_TYPE.OFFLINE,
        access: 'PA.FLOW.FLOW_MGR.OFFLINE',
        options: [
          {
            command: false,
            text: '停止'
          },
          {
            content: '即flink savepoint，用于暂停流程，流程重新启动，保证精准一次语义。',
            icon: 'el-icon-warning-outline',
            command: true,
            text: '停止并保留状态'
          }
        ]
      },
      {
        type: 'ElButton',
        label: '取消发布',
        event: BATCH_EVENT_TYPE.CANCEL_PUB,
        access: 'PA.FLOW.FLOW_MGR.CANCEL_PUBLISH'
      },
      {
        type: 'ElButton',
        label: '发布',
        event: BATCH_EVENT_TYPE.PUB,
        access: 'PA.FLOW.FLOW_MGR.PUBLISH'
      },
      {
        type: 'ElDropdown',
        label: '启动',
        event: BATCH_EVENT_TYPE.ONLINE,
        access: 'PA.FLOW.FLOW_MGR.ONLINE',
        options: [
          {
            content: '流程首次启动，或者修改kafka consumer group等信息后需要从头开始消费数据。',
            icon: 'el-icon-warning-outline',
            command: false,
            text: '无状态启动'
          },
          {
            content:
              '流程重启，需要接着上次checkpoint记录的位置，如kafka上次的offset位置继续消费。',
            icon: 'el-icon-warning-outline',
            command: true,
            text: '基于上次状态启动'
          }
        ]
      }
    ].filter((item) => hasPermission(item.access));
  }
  created() {
    this.getDependList();
    !this.enableSql && this.relationTypeList.pop();
  }
  async getDependList(refresh = false) {
    if (!this.$route.query.resType) return;
    if (refresh) {
      (this.$refs.table as any)?.clearSelection();
    }
    this.tableLoading = true;
    const { data } = await this.doPost(`${URL_RES_FINDDEPENDS}/${this.selectType}`, {
      search: this.id,
      jobStatus: this.selectType === 'JOB' ? this.flowStatus : undefined,
      pageData: {
        pageSize: this.pageData.pageSize,
        currentPage: refresh ? 1 : this.pageData!.currentPage || 1,
        total: 0
      }
    });
    const { tableData = [], columnData = [], pageData } = data || {};
    this.height = tableData.length === 0 ? '170px' : '400px';
    this.dependTableData = tableData.sort(this.sortCreateTime('createTime')).map((i) => ({
      ...i,
      status: this.jobStatusMap[i.status] || i.status,
      createTime: dayjs(i.createTime).format('YYYY-MM-DD HH:mm:ss')
    }));
    this.columnData = columnData.map((item) => ({ ...item, value: item.value || item.prop }));
    this.pageData.total = pageData.total;
    this.tableLoading = false;
  }

  handleCurrentChange(val, size) {
    if (this.pageData) {
      this.pageData.currentPage = val;
      this.pageData.pageSize = size;
    }
    this.getDependList();
  }
  // 根据创建时间进行排序
  sortCreateTime(property) {
    return function (a, b) {
      const value1 = a[property];
      const value2 = b[property];
      return value2 - value1;
    };
  }
  // 下载引用关系
  async downloadRelationList() {
    const res: any = await get(
      `${URL_EXPORT_SERVICE_RELATION_LIST}/${this.selectType}?id=${this.$route.query.id}`,
      null,
      {
        responseType: 'blob'
      }
    );
    if (res.blob) {
      const { blob, fileName } = res;
      const url = window.URL.createObjectURL(new Blob([blob]));
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', decodeURI(fileName));
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      return;
    }
  }
  // 查看批量信息
  async viewBatchInfo() {
    const {
      data = {},
      success,
      msg
    } = (await batchOperateDetails({ batchEventResId: this.id })) || {};
    if (!success) return this.$message.error(msg);
    this.batchInfo = data;
    this.showBatchInfo = true;
    setTimeout(() => {
      let maskDom;
      const dialogInstance: any = this.$refs.batchInfoDialog;
      const zIndex = Number(dialogInstance.$el.style.zIndex);
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // DOM元素进入视口时的操作 处理在弹窗内部跳转页面 回到原页面时弹窗会消失的情况
            if (document.body.querySelectorAll('.v-modal').length === 0) {
              maskDom = document.createElement('div');
              maskDom.style.zIndex = zIndex - 1 + '';
              maskDom.setAttribute('class', 'v-modal');
              document.body.appendChild(maskDom);
            }
          } else {
            const removeMaskDom = document.getElementsByClassName('v-modal')[0];
            removeMaskDom && document.body.removeChild(removeMaskDom);
          }
        });
      });
      observer.observe(dialogInstance.$el);
    }, 100);
  }
  // 处理多选
  handleSelectionChange(selection) {
    this.checkedRows = selection;
  }
  headerOperateHandler(state: boolean, eventType: string) {
    this.batchOperate(eventType, state);
  }
  getBatchParams(type: string, state?: boolean) {
    return {
      jobIds: this.checkedRows.map((el: any) => el.quoterId),
      batchEventType: type,
      batchEventResId: this.id,
      // 上线是否基于状态启动
      fromState: type === BATCH_EVENT_TYPE.ONLINE ? state : false,
      // 下线是否保存状态
      saveState: type === BATCH_EVENT_TYPE.OFFLINE ? state : false
    };
  }
  async batchOperate(type: string, state?: boolean) {
    if (this.checkedRows.length === 0) return this.$tip.warning('请选择要操作的流程');
    const loading = this.$loading({ lock: true });
    const params = this.getBatchParams(type, state);
    this.$confirm('确定执行批量操作吗？', '提示')
      .then(async () => {
        // 发布需要执行预发布/预启动
        if (type === BATCH_EVENT_TYPE.PUB || type === BATCH_EVENT_TYPE.ONLINE) {
          const params = {
            relationBatchTag: true,
            jobs: this.checkedRows.map((el: any) => el.quoterId)
          };
          const { success, msg } =
            type === BATCH_EVENT_TYPE.PUB ? await perPublishFlows(params) : await preOnline(params);
          if (!success) {
            this.$tip.error({ message: msg, duration: 5000 });
            return loading.close();
          }
        }
        const { success, msg } = await batchOperate(params);
        if (success) {
          this.$tip.success('操作成功');
          this.getDependList();
          (this.$refs.table as any)?.clearSelection();
        } else {
          this.$tip.error({ message: msg, duration: 5000 });
        }
        loading.close();
      })
      .catch(() => loading.close());
  }
}
</script>
<style lang="scss" scoped>
.tab-bg {
  background: #fff;
}
.depend-table {
  &__select {
    margin-left: 10px;
  }
}
.table-header-operator {
  display: none;
  .el-dropdown:first-child .el-button {
    border-right: 0;
    border-radius: 3px 0 0 3px;
  }
  > .el-button {
    margin: 0;
    border-right: 0;
    border-radius: 3px 0 0 3px;
  }
}
</style>
