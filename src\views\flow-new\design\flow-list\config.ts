export const flowActionList = [
  {
    type: 'ElTooltip',
    value: 'add',
    content: '新建流程',
    icon: 'iconfont icon-xinjianxiangmu',
    access: 'PA.FLOW.FLOW_MGR.ADD'
  },
  {
    type: 'ElTooltip',
    value: 'batch',
    content: '批量操作',
    icon: 'iconfont icon-piliang'
  },
  {
    type: 'ElPopover',
    value: 'filter',
    content: '筛选',
    icon: 'iconfont icon-shaixuan',
    access: 'PA.FLOW.FLOW_MGR.FILTER',
    options: [
      {
        value: 'jobType',
        label: '流程类型',
        children: [
          {
            value: 'ALL',
            label: '全部'
          },
          {
            value: 'FLINK_SQL',
            label: 'SQL'
          },
          {
            value: 'PROCESSFLOW',
            label: 'DataStream'
          }
        ]
      },
      {
        value: 'jobStatus',
        label: '流程状态',
        children: [
          {
            label: '全部',
            value: 'ALL'
          },
          {
            label: '已上线',
            value: 'PROD'
          },
          {
            label: '已发布',
            value: 'PUB'
          },
          {
            label: '开发',
            value: 'DEV'
          }
        ]
      },
      {
        label: '运行状态',
        value: 'jobRunTimeStatus',
        children: [
          { label: '全部', value: 'ALL' },
          { label: '失败', value: 'FAILED' },
          { label: '运行', value: 'RUNNING' },
          { label: '未运行', value: 'NONE' },
          { label: '完成', value: 'FINISHED' },
          { label: '未知', value: 'UNKNOWN' },
          { label: '其他', value: 'OTHER' }
        ]
      },
      {
        value: 'mode',
        label: '流程模式',
        children: [
          {
            value: 'ALL',
            label: '全部'
          },
          {
            value: 'stream',
            label: '流模式'
          },
          {
            value: 'batch',
            label: '批模式'
          }
        ]
      }
    ]
  },
  {
    type: 'ElTooltip',
    value: 'search',
    content: '搜索',
    icon: 'iconfont icon-sousuo',
    access: 'PA.FLOW.FLOW_MGR.FILTER'
  }
];

export const batchActionList = [
  {
    type: 'ElTooltip',
    value: 'publish',
    content: '发布',
    icon: 'iconfont icon-fabu',
    access: 'PA.FLOW.FLOW_MGR.PUBLISH'
  },
  {
    type: 'ElTooltip',
    value: 'cancelPublish',
    content: '取消发布',
    icon: 'iconfont icon-quxiaofabu',
    access: 'PA.FLOW.FLOW_MGR.CANCEL_PUBLISH'
  },
  {
    type: 'ElDropdown',
    name: 'online',
    icon: 'iconfont icon-qidong1',
    access: 'PA.FLOW.FLOW_MGR.ONLINE',
    options: [
      {
        content: '流程首次启动，或者修改kafka consumer group等信息后需要从头开始消费数据。',
        icon: 'el-icon-warning-outline',
        command: false,
        text: '无状态启动'
      },
      {
        content: '流程重启，需要接着上次checkpoint记录的位置，如kafka上次的offset位置继续消费。',
        icon: 'el-icon-warning-outline',
        command: true,
        text: '基于上次状态启动'
      }
    ]
  },
  {
    type: 'ElDropdown',
    name: 'offline',
    icon: 'iconfont icon-tingzhi',
    access: 'PA.FLOW.FLOW_MGR.OFFLINE',
    options: [
      {
        command: false,
        text: '停止'
      },
      {
        content: '即flink savepoint，用于暂停流程，流程重新启动，保证精准一次语义。',
        icon: 'el-icon-warning-outline',
        command: true,
        text: '停止并保留状态'
      }
    ]
  },
  {
    type: 'splitTag',
    value: '|'
  },
  {
    type: 'ElTooltip',
    value: 'copy',
    content: '复制流程',
    icon: 'iconfont icon-fuzhi',
    access: 'PA.FLOW.FLOW_MGR.COPY'
  },
  {
    type: 'ElTooltip',
    value: 'move',
    content: '移动流程',
    icon: 'iconfont icon-yidong',
    access: 'PA.FLOW.FLOW_MGR.MOVE'
  },
  {
    type: 'ElTooltip',
    value: 'export',
    content: '导出流程',
    icon: 'iconfont icon-daochu1',
    access: 'PA.FLOW.FLOW_MGR.EXPORT'
  },
  {
    type: 'ElTooltip',
    value: 'delete',
    content: '删除流程',
    icon: 'iconfont icon-shanchu',
    access: 'PA.FLOW.FLOW_MGR.DELETE'
  }
];
