<template>
  <div class="engine">
    <div class="bs-detail-block engine__content">
      <div class="bs-detail__header">
        <div class="bs-detail__header-title">{{ $t('pa.home.engineMatter') }}</div>
        <!-- 下拉级联单选 -->
        <el-cascader
          :placeholder="$t('pa.all')"
          :options="clusterList"
          filterable
          clearable
          size="small"
          @change="handleClusterChange"
        />
      </div>
      <data-handler :request="request" @get-data="getData">
        <template v-slot:content>
          <chart :option="option" name="engine" />
        </template>
      </data-handler>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { EngineChart } from '../charts/charts';
import { getClustersData, getEngineData } from '@/apis/homeApi';
import Chart from '../components/chart.vue';
import DataHandler from './data-handler.vue';
import { AxiosPromise } from 'axios';
import '../index.scss';
@Component({
  components: {
    Chart,
    DataHandler
  }
})
export default class Engine extends Vue {
  @Prop({ default: '' }) orgId!: string;
  private chart: any = null;
  private chartDom: HTMLDivElement | null = null;
  private option = EngineChart.option;
  private request: AxiosPromise[] = [];
  // 级联单选
  private clusterList: object[] = [];
  @Watch('orgId', { immediate: true })
  handleOrgIdChange(ids) {
    this.request = [getClustersData(ids), getEngineData(undefined, this.orgId)];
  }
  // 节点选中
  handleClusterChange(row) {
    this.request = [getEngineData(row[0], this.orgId)];
  }
  getData(data: any) {
    if (data) {
      if (Array.isArray(data)) {
        const isSuccess = data.every((el) => el.success);
        if (!data[0].success) this.clusterList = [];
        if (isSuccess) {
          this.clusterList = [];
          data[0].data.forEach(({ clusterId: value, clusterName: label }) => {
            this.clusterList.push({ label, value });
          });
          this.setChart(data[1].data);
        }
      } else {
        this.setChart(data.data);
      }
    }
  }
  setChart(data) {
    const { residualCpu, residualMemory, residualSlots, usedCpu, usedMemory, usedSlots } = data;
    const getPercent = (num, total) => {
      num = parseFloat(num);
      total = parseFloat(total);
      return total <= 0 ? 0 : Math.round((num / total) * 10000) / 100;
    };
    const formatUnit = (num) => {
      return parseFloat((num / Math.pow(1024, Math.floor(Math.log(num) / Math.log(1024)))).toFixed(2));
    };
    EngineChart.setOption({
      used: [
        { number: usedSlots, value: getPercent(usedSlots, usedSlots + residualSlots) },
        { number: usedCpu, value: getPercent(usedCpu, residualCpu + usedCpu) },
        {
          number: formatUnit(usedMemory),
          value: getPercent(usedMemory, residualMemory + usedMemory)
        }
      ],
      residual: [
        { number: residualSlots, value: getPercent(residualSlots, usedSlots + residualSlots) },
        { number: residualCpu, value: getPercent(residualCpu, residualCpu + usedCpu) },
        {
          number: formatUnit(residualMemory),
          value: getPercent(residualMemory, residualMemory + usedMemory)
        }
      ]
    });
  }
}
</script>

<style lang="scss" scoped>
.engine {
  ::v-deep .el-input__inner {
    width: 120px;
    height: 32px;
  }
}
.bs-detail__content {
  padding-right: 0px;
}
</style>
