<template>
  <pro-form
    v-if="isInit"
    ref="proForm"
    :value="formData"
    :options="formOptions"
    :disabled="disabled"
    :form-items="formItems"
    :default-expanded-keys="defaultExpandedKeys"
    @item-change="handleItemChange"
    @change="handleChange"
  >
    <!-- 自定义字段分组 -->
    <custom-group
      slot="group-custom"
      :config="customGroup"
      :disabled="disabled"
      :value="formData[customPorp]"
      @change="handleCustomGroupChange"
    />
    <!-- 输出字段分组 -->
    <p slot="group-title-output" slot-scope="group">
      {{ group.title }} (
      <span class="bule-text">{{ outputFields.length }}</span>
      )
      <el-tooltip class="item" effect="light" placement="bottom">
        <span slot="content">
          {{ $t('pa.flow.msg289') }}+{{ customFieldLabel }}<br />
          {{ $t('pa.flow.msg290') }}>{{ customFieldLabel }}
        </span>
        <span class="iconfont icon-wenhao group-output-tip"></span>
      </el-tooltip>
    </p>
    <output-group
      slot="group-output"
      :config="outputGroup"
      :disabled="disabled"
      :input-options="inputOptions"
      :output-fields="outputFields"
      @change="handleOutputGroupChange"
      @custom-label-change="(val) => (customFieldLabel = val)"
    />
    <!-- 内置类型: serve -->
    <bs-select
      slot="resId"
      v-model="formData.resId"
      clearable
      :placeholder="$t('pa.flow.msg128')"
      :options="serveOptions"
      :disabled="disabled"
      @change="handleServeChange"
    />
    <!-- 内置类型: table -->
    <el-button :slot="tableFieldProps" type="text" @click="setFieldsMapping(tableFieldProps)">
      {{ disabled ? $t('pa.flow.view') : $t('pa.flow.config1') }}
    </el-button>
    <!-- 内置类型: field -->
    <type-field
      v-for="prop in specialTypeProps['field']"
      :key="prop"
      :slot="prop"
      :value="formData[prop]"
      :config="formItemMaps.get(prop)"
      :disabled="disabled"
      :form-data="formData"
      @change="(val) => updateModel(prop, val)"
    />
    <!-- 内置类型: unit -->
    <type-unit
      v-for="prop in specialTypeProps['unit']"
      :key="prop"
      :slot="prop"
      :value="formData[prop]"
      :config="formItemMaps.get(prop)"
      :disabled="disabled"
      @change="(val) => updateModel(prop, val)"
    />
    <!-- 内置类型: output -->
    <type-output
      v-for="prop in specialTypeProps['output']"
      :key="prop"
      :slot="prop"
      :value="formData[prop]"
      :config="formItemMaps.get(prop)"
      :disabled="disabled"
      @change="(val) => updateModel(prop, val)"
    />
  </pro-form>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref, Provide } from 'vue-property-decorator';
import { Group, FormItem } from './interface';
import {
  getOutputField,
  runRequest,
  transform,
  transformOutputItem,
  transformServeItem,
  transformTableItem,
  validateForms
} from './utils';
import CustomGroup from './components/custom-group.vue';
import OutputGroup from './components/output-group/index.vue';
import TypeField from './components/type-field.vue';
import TypeUnit from './components/type-unit.vue';
import TypeOutput from './components/type-output.vue';
import { cloneDeep } from 'lodash';
import { openFieldsMappingDialog } from '.';
// 内置分组
const INNER_GROUPS = {
  CUSTOM: 'custom',
  OUTPUT: 'output'
};

// 内置表单类型
const INNER_TYPE = {
  TABLE: 'table',
  FIELD: 'field',
  SERVE: 'serve',
  UNIT: 'unit',
  OUTPUT: 'output'
};
const DEFAULT_LABEL_WIDTH = 130;
// 存储流程报错信息
@Component({
  name: 'FlowList',
  components: { CustomGroup, OutputGroup, TypeField, TypeUnit, TypeOutput }
})
export default class NodeConfig extends Vue {
  @Ref('proForm') proForm: any;
  // 节点信息
  @Prop() data!: any;
  // 当前流程信息
  @Prop() jobData!: any;
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: false }) isDesign!: boolean;
  @Prop() designCode!: string;
  @Provide('labelWidth') labelWidth = this.isEn ? 240 : DEFAULT_LABEL_WIDTH;
  isInit = false;
  // 上游输入字段
  inputFields: any[] = [];
  outputFields: any[] = [];
  // 分组类型
  groupType: false | 'divider' | 'collapse' = 'collapse';
  // 分组信息
  groups: Group[] = [];
  // 表单信息
  formItems: FormItem[] = [];
  // 表单数据
  formData: Record<string, any> = {};
  formItemMaps = new Map();
  // 未进行转换前的表单配置
  originalFormsItems: FormItem[] = [];
  // 特殊类型对应的props
  specialTypeProps = {};
  // 自定义分组字段
  nodeConfig: any = {};
  outputs?: string[] | 'all';
  // 服务列表
  serveOptions: any[] = [];
  // 表单项依赖关系之间的映射关系
  depMaps: Map<string, string[]> = new Map();
  // 输出字段分组中自定义字段的label
  customFieldLabel = '';
  get orgId() {
    return this.$store.getters.orgId;
  }
  get isProcess() {
    return this.nodeConfig.operateType === 'PROCESS';
  }
  get formOptions() {
    const { groupType, groups } = this;
    return {
      labelWidth: this.isEn ? 240 : DEFAULT_LABEL_WIDTH,
      groupType,
      groupItems: groups.map(({ name, title }) => ({ name, title })),
      groupProps: { value: groups[0] ? [groups[0].name] : [] }
    };
  }
  get customGroup() {
    return this.groups.find(({ name }) => name === INNER_GROUPS.CUSTOM);
  }
  get customPorp() {
    return (this.customGroup || {}).prop || 'customData';
  }
  get outputGroup() {
    return this.groups.find(({ name }) => name === INNER_GROUPS.OUTPUT);
  }
  get tableFieldProps() {
    return (this.originalFormsItems.find((item) => item.type === INNER_TYPE.TABLE) || {}).fieldProp;
  }
  // 将上游输入字段转换成value/label形式
  get inputOptions() {
    return this.inputFields.map(({ name, type }, index) => ({
      label: name,
      value: name,
      type: type,
      sort: index
    }));
  }
  get inputMaps() {
    return this.inputOptions.reduce((res, item, index) => {
      res.set(item.value, item);
      return res;
    }, new Map());
  }
  // 默认展开的分组
  get defaultExpandedKeys() {
    const { groups, outputGroup } = this;
    // 默认开展第一个分组
    const keys = groups.length ? [this.groups[0].name] : [];
    if (outputGroup && !keys.includes(outputGroup.name)) {
      keys.push(outputGroup.name);
    }
    return keys;
  }
  created() {
    this.init();
  }
  init() {
    this.inputFields = this.data.inputFields || [];
    this.outputFields = this.data.outputFields || [];
    // this.inputFields = [
    //   { name: 'id', type: 'String', targetable: true },
    //   { name: 'name', type: 'String', targetable: true },
    //   { name: 'aaa', type: 'String', targetable: true },
    //   { name: 'bbb', type: 'String', targetable: true }
    // ];
    // this.outputFields = [
    //   { name: 'id', type: 'String', targetable: true },
    //   { name: 'name', type: 'String', targetable: true },
    //   { name: 'aaa', type: 'String', targetable: true },
    //   { name: 'bbb', type: 'String', targetable: true }
    // ];
    // 表单配置项处理
    const { groupType = 'collapse', groups = [], forms = [], outputs } = this.getComponentConfig();
    // const { groupType = 'collapse', groups = [], forms = [], outputs } = this.getTestConfig();
    this.originalFormsItems = Object.freeze(forms);
    // 获取分组信息
    this.groupType = groupType;
    this.groups = this.getGroups(groups);
    // 获取表单信息
    this.specialTypeProps = this.setSpecialTypeProps(forms);
    this.formItems = this.getForms(forms);
    this.formItemMaps = this.setFormMaps(this.formItems);
    // 获取输出字段
    this.outputs = outputs;
    // 数据初始化
    this.initFormData();
    this.isInit = true;
    if (this.specialTypeProps[INNER_TYPE.SERVE].length) {
      this.getServeOptions();
    }
  }
  // 表单数据初始化
  initFormData() {
    // 需要在表单项进行转换后处理初始表单数据
    this.setDefaultVal();
    this.formData = Object.assign(this.formData, { ...(this.data.properties || {}) });
  }
  // 设置表单默认值
  setDefaultVal() {
    this.formItems.forEach((item) => {
      if (item.defaultVal && item.prop) {
        this.formData[item.prop] = item.defaultVal;
      }
    });
  }
  // 测试数据
  // getTestConfig(): any {
  //   return {
  //     labelWidth: 130,
  //     groupType: 'collapse',
  //     groups: [
  //       { title: '基础字段', name: 'base' },
  //       // { title: '高级字段', name: 'advance' },
  //       // { title: '自定义参数', name: 'custom', prop: 'customData' },
  //       { title: '输出字段', name: 'output' }
  //     ],
  //     outputs: ['targetField', 'path'],
  //     forms: [
  //       {
  //         type: 'serve',
  //         group: 'base',
  //         request: {
  //           url: '/rs/pa/portal/listQuoteRes/CLICKHOUSE',
  //           params: ['orgId']
  //         }
  //       },
  //       {
  //         type: 'select',
  //         label: '巴拉巴拉',
  //         prop: 'aaa',
  //         useInput: true,
  //         group: 'base',
  //         // defaultVal: ['id'],
  //         componentProps: {
  //           options: [],
  //           multiple: true,
  //           filterable: true,
  //           allowCreate: true
  //         }
  //       },
  //       {
  //         type: 'cascader',
  //         label: '级联数据',
  //         prop: 'bbb',
  //         useInput: true,
  //         group: 'base',
  //         defaultVal: ['id'],
  //         componentProps: {
  //           options: [],
  //           multiple: true,
  //           filterable: true
  //         },
  //         request: {
  //           url: '/rs/pa/portal/getSubList/CLICKHOUSE',
  //           params: ['orgId', 'resTitle', 'resId'],
  //           props: { value: 'subName', label: 'subName' }
  //         }
  //       },
  //       {
  //         type: 'page-select',
  //         label: '带分页下拉',
  //         prop: 'ccc',
  //         group: 'base',
  //         // defaultVal: ['id'],
  //         request: {
  //           url: '/rs/pa/portal/getSubList/CLICKHOUSE',
  //           params: ['orgId', 'resTitle', 'resId'],
  //           props: { value: 'subName', label: 'subName' }
  //         }
  //       },
  //       {
  //         type: 'field',
  //         label: '接口路径',
  //         prop: 'path',
  //         group: 'base',
  //         required: true,
  //         componentProps: {
  //           pageSize: 5
  //         },
  //         request: {
  //           url: '/rs/pa/res/detail/clickhouse/columnComponent',
  //           params: ['subName', 'resId']
  //         }
  //         // componentProps: {
  //         //   placeholder: '接口路径,如:/a/b/c'
  //         // }
  //       },
  //       {
  //         type: 'table',
  //         label: '表',
  //         prop: 'subName',
  //         fieldProp: 'outputColums',
  //         group: 'base',
  //         // 表列表数据请求
  //         request: {
  //           url: '/rs/pa/portal/getSubList/CLICKHOUSE',
  //           params: ['orgId', 'resTitle', 'resId'],
  //           props: { value: 'subName' }
  //         },
  //         // 获取表字段请求
  //         fieldRequest: {
  //           url: '/rs/pa/res/detail/clickhouse/columnComponent',
  //           params: ['resId', 'subName']
  //         }
  //       },
  //       {
  //         type: 'select',
  //         label: '请求方式',
  //         prop: 'method',
  //         tooltip: '请求方式暂时只支持：GET,POST',
  //         defaultVal: 'GET',
  //         group: 'base',
  //         required: true,
  //         options: [
  //           { label: 'GET', value: 'GET' },
  //           { label: 'POST', value: 'POST' }
  //         ]
  //       },
  //       {
  //         type: 'unit',
  //         label: '时间间隔',
  //         prop: 'intervalTime',
  //         group: 'base',
  //         units: '秒',
  //         defaultVal: 1000,
  //         componentProps: {
  //           placeholder: 'http 访问时间间隔',
  //           max: 2147483647,
  //           min: 1
  //         },
  //         rules: [
  //           {
  //             required: true,
  //             message: '请输入 http 访问时间间隔，单位毫秒',
  //             trigger: 'blur'
  //           }
  //         ]
  //       },
  //       {
  //         type: 'input',
  //         prop: 'targetField',
  //         label: '输出字段',
  //         tooltip: '从http中读取的数据放入该字段中，并传入流程中的下一个节点',
  //         group: 'base',
  //         defaultVal: 'message',
  //         required: true
  //       },
  //       {
  //         type: 'input-number',
  //         prop: 'maxTotal',
  //         label: '连接池最大连接数',
  //         tooltip: '连接池里的最大连接数，取值范围1~500，默认20',
  //         required: true,
  //         group: 'advance',
  //         defaultVal: 20,
  //         componentProps: {
  //           min: 1,
  //           max: 500
  //         }
  //       },
  //       {
  //         type: 'input-number',
  //         prop: 'maxPreRoute',
  //         label: '路由最大连接数',
  //         tooltip: '往每台机器的最大连接数，取值范围1~500，默认10',
  //         required: true,
  //         group: 'advance',
  //         defaultVal: 10,
  //         componentProps: {
  //           min: 1,
  //           max: 500
  //         }
  //       },
  //       {
  //         type: 'input-number',
  //         prop: 'retryNum',
  //         label: '重试次数',
  //         tooltip: '请求失败重试次数，取值范围1~10，默认3次',
  //         required: true,
  //         group: 'advance',
  //         defaultVal: 3,
  //         componentProps: {
  //           min: 1,
  //           max: 10
  //         }
  //       }
  //     ]
  //   };
  // }
  // 获取组件配置
  getComponentConfig() {
    if (this.isDesign) return JSON.parse(this.designCode || '{}');
    return this.$store.getters.componentListMap.get(this.data.className) || {};
  }
  // 获取分组信息
  getGroups(groups: Group[]): Group[] {
    // 非处理组件 过滤输出分组
    if (!this.isProcess && this.outputGroup) {
      return groups.filter(({ name }) => name !== INNER_GROUPS.OUTPUT);
    }
    return groups;
  }
  // 获取特殊字段的props合集
  setSpecialTypeProps(forms: FormItem[]) {
    const specialTypeProps = Object.values(INNER_TYPE).reduce((res, k) => {
      res[k] = [];
      return res;
    }, {});
    forms.forEach(({ type, prop }) => {
      if (Array.isArray(specialTypeProps[type])) {
        specialTypeProps[type].push(type === INNER_TYPE.SERVE ? 'resId' : prop);
      }
    });
    return specialTypeProps;
  }
  // 设置源配置数据的映射
  setFormMaps(forms: FormItem[]) {
    return forms.reduce((map, item: FormItem) => {
      map.set(item.prop, item);
      Array.isArray(item.children) &&
        item.children.forEach((cItem) => {
          map.set(cItem.prop, cItem);
        });
      return map;
    }, new Map());
  }
  // 获取表单配置项
  getForms(forms: FormItem[]): FormItem[] {
    const transformedForms: any[] = [];
    // 用于接口请求时传入formData以外的数据，比如jobData中的数据
    const extraData = { orgId: this.orgId };
    // 依赖字段关系
    const depMaps = new Map();
    // 校验表单配置数据
    const errors = validateForms(this.groupType, this.groups, forms);
    if (this.isDesign && errors.length) {
      this.$message.error({
        dangerouslyUseHTMLString: true,
        message: errors.join('<br>') as string,
        duration: 0,
        showClose: true
      });
    } else {
      console.log(errors);
    }
    forms.forEach((item: FormItem) => {
      item = cloneDeep(item);
      if (INNER_TYPE.FIELD === item.type || INNER_TYPE.UNIT === item.type) {
        item.type = 'custom';
        transformedForms.push(transform(item, extraData, this.isDesign));
      } else if (INNER_TYPE.SERVE === item.type) {
        transformedForms.push(...transformServeItem(item));
      } else if (INNER_TYPE.TABLE === item.type) {
        transformedForms.push(transformTableItem(item, extraData, this.isDesign));
      } else if (INNER_TYPE.OUTPUT === item.type) {
        transformedForms.push(transformOutputItem(item, extraData, this.isDesign));
      } else {
        transformedForms.push(transform(item, extraData, this.isDesign));
      }
    });
    transformedForms.forEach((item) => {
      this.handleUseInput(item);
      this.handleCustomRules(item);
      if (Array.isArray(item.deps)) {
        item.deps.forEach((dep) => {
          if (!depMaps.has(dep)) {
            depMaps.set(dep, []);
          }
          depMaps.set(dep, [...new Set([...depMaps.get(dep), item.prop])]);
        });
      }
    });
    this.depMaps = depMaps;
    return transformedForms;
  }
  // 处理使用上有输入字段作为数据源的表单项
  handleUseInput(item) {
    if (item.useInput) {
      (item.componentProps || {}).options = this.inputOptions;
    }
  }
  // 处理自定义校验规则
  handleCustomRules(item) {
    if (!Array.isArray(item.rules)) return;
    item.rules.forEach((rItem) => {
      try {
        /* eslint-disable-next-line */
        const fn = eval(rItem.validator);
        if (typeof rItem.validator === 'string' && typeof fn === 'function') {
          rItem.validator = (rule, value, callback) => {
            fn(rule, value, callback, this.formData);
          };
        }
      } catch (err) {
        const message = this.$t('pa.flow.msg291', [item.label, err]);
        this.isDesign ? this.$message.error({ message, duration: 0, showClose: true }) : console.log(message);
      }
    });
  }
  // 处理表单数据变更
  handleChange(data) {
    Object.assign(this.formData, data);
  }
  // 处理单个表单项配置值变更回调
  handleItemChange(prop, value) {
    const item = this.formItemMaps.get(prop);
    if (item && item.useInput && Array.isArray(value)) {
      value.sort((pre, val) => this.inputMaps.get(pre).sort - this.inputMaps.get(val).sort); // 根据输入字段的顺序进行排序
      this.updateModel(prop, value);
    }
    // 处理服务依赖字段变更
    if ((this.depMaps.get(prop) || []).includes('resId')) {
      this.getServeOptions();
      this.handleServeChange('');
    }
    // 如果该字段作为其他表单项的字段需要重置值
    (this.depMaps.get(prop) || []).forEach((p) => this.resetModel(p));
  }
  // 处理自定义字段分组更改
  handleCustomGroupChange(data) {
    const { customPorp } = this;
    customPorp && this.proForm.updateModel(customPorp, data);
  }
  // 处理内置输出字段分组的更改
  handleOutputGroupChange(data, custom) {
    this.outputFields = data;
    Object.assign(this.formData, custom);
  }
  // 处理服务下拉回调
  handleServeChange(val) {
    const serve = this.serveOptions.find((item) => item.value === val);
    this.proForm.updateModel('resId', serve ? serve.id : '');
    this.proForm.updateModel('resTitle', serve ? serve.title : '');
    this.proForm.updateModel('address', serve ? serve.address : '');
    // 如果该字段作为其他表单项的字段需要重置值
    (this.depMaps.get('resId') || [])!.forEach((p) => this.resetModel(p));
  }
  // 获取服务下拉回调
  async getServeOptions() {
    if (!this.formItemMaps.get('resId')) return;
    const { data } = await runRequest(this.formItemMaps.get('resId').request, { orgId: this.orgId, ...this.formData });
    this.serveOptions = (data || []).map((item) => Object.assign(item, { value: item.id, label: item.title }));
  }
  // 更新表单字段
  updateModel(prop, value) {
    this.proForm.updateModel(prop, value);
  }
  // 重置表单字段
  resetModel(prop) {
    const item = this.formItemMaps.get(prop);
    this.updateModel(prop, item.defaultVal || (Array.isArray(this.formData[prop]) ? [] : ''));
  }
  // type=table 打开表字段配置弹窗
  async setFieldsMapping(fieldProp) {
    const tableProp = this.specialTypeProps[INNER_TYPE.TABLE];
    const { disabled, inputOptions } = this;
    const data = await openFieldsMappingDialog({
      value: this.formData[fieldProp] || [],
      formData: this.formData,
      tableName: this.formData[tableProp],
      disabled,
      inputOptions,
      config: this.formItemMaps.get(fieldProp)
    });
    this.formData[fieldProp] = data;
  }
  // 获取表单参数
  getProperties() {
    const properties = { ...this.formData };
    return properties;
  }
  // 获取输入字段
  getOutputFields() {
    // 存在输出分组
    const { outputGroup, outputs, formData } = this;
    if (outputGroup) return this.outputFields;
    if (outputs === 'all') return this.inputFields;
    if (Array.isArray(outputs)) {
      return outputs
        .reduce((res: string[], item) => {
          const data = formData[item];
          if (!data) return res;
          res.push(...(Array.isArray(data) ? data : [data]));
          return res;
        }, [])
        .map((item) => getOutputField(item));
    }
    return [];
  }
  // 配合弹窗的确认点击事件
  public confirm(done: (bol: boolean, data?: any) => void) {
    this.proForm.validate((valid: any) => {
      if (!valid) return done(false);
      const nodeData = cloneDeep(this.data);
      nodeData.properties = this.getProperties();
      nodeData.outputFields = this.getOutputFields();
      console.log(nodeData);
      done(true, nodeData);
    });
  }
  // 重新渲染用于组件的设计
  public renderForm() {
    if (this.isDesign) {
      this.isInit = false;
      this.formData = {};
      this.groups = [];
      setTimeout(() => {
        this.init();
      }, 100);
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  margin-left: -10px;
  margin-right: -10px;
}
.bule-text {
  color: #377bff;
}
.group-output-tip {
  vertical-align: bottom;
  margin-left: 10px;
}
</style>
