<template>
  <el-form v-if="show" ref="form" :model="form" :rules="rules" :disabled="disabled">
    <el-collapse v-model="activeName" class="connector-container">
      <el-collapse-item title="连接器配置" name="connector">
        <el-table
          :key="key"
          border
          stripe
          :height="height"
          :data="tableData"
          class="connector-table"
        >
          <!-- 属性 -->
          <el-table-column prop="name" label="属性" min-width="55%" align="center">
            <template v-slot="{ row }">
              <span class="connector-name">{{ row.name }}</span>
              <el-tooltip effect="light" :content="row.keyExplain" placement="top">
                <i class="iconfont icon-wenhao connector-icon"></i>
              </el-tooltip>
            </template>
          </el-table-column>
          <!-- 值 -->
          <el-table-column prop="value" label="值" min-width="45%" align="center">
            <template slot-scope="scope">
              <el-form-item :prop="getProp(scope)" class="connector-table__item">
                <el-select
                  v-if="isSelect(scope)"
                  v-model="form[getProp(scope)]"
                  size="mini"
                  clearable
                  @change="handleFormChange"
                >
                  <el-option
                    v-for="item in getOptions(scope)"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
                <el-input
                  v-if="isInput(scope)"
                  v-model="form[getProp(scope)]"
                  size="mini"
                  clearable
                  @input="handleFormChange"
                />
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-collapse-item>
    </el-collapse>
  </el-form>
</template>

<script lang="ts">
import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator';
import { uniqueId } from 'lodash';
import Form from 'bs-ui-pro/lib/form';

@Component
export default class ConnectorConfig extends Vue {
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop() originData!: any;
  @Prop({ type: Number, default: 270 }) height!: number;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: String, default: '_' }) nodeName!: string;
  @Prop({ type: String, default: 'id' }) propName!: string;
  @Prop({ type: String, default: 'items' }) optionName!: string;
  @Ref('form') readonly formRef!: Form;
  private activeName: any[] = []; // 折叠面板
  private tableData: any[] = []; // 表格数据
  private form: any = {}; // 表单
  private rules = {}; // 规则
  private key = Date.now(); //
  /* 显示规则 */
  get show() {
    return this.data.length > 0;
  }

  /* 组件序号 */
  get CID() {
    return this.nodeName.split('_')[1];
  }

  /* 用户名 */
  get UN() {
    return this.$store.state.userInfo.userName;
  }

  /* 流程名 */
  get FN() {
    return this.jobData.jobName;
  }

  /* 项目名 */
  get PN() {
    return this.jobData.projectName;
  }

  @Watch('data', { immediate: true, deep: true })
  handleDataChange() {
    this.form = {};
    this.rules = {};
    this.generateTableData();
  }

  @Watch('tableData', { immediate: true, deep: true })
  handleDataChange1() {
    this.handleFormChange();
  }

  /* 生成表格数据 */
  generateTableData() {
    const filterArr = this.data.filter(({ overwrite }: any) => overwrite);
    this.tableData = filterArr.map((item: any) => {
      const { propName } = this;
      const { name, required, pageType } = item;
      item.defaultValue = this.handleDefaultValue(item);
      item[propName] = uniqueId();
      this.$set(this.form, item[propName], item.defaultValue);
      const isSelect = pageType === 'SELECT_TYPE';
      this.rules[item[propName]] = [
        {
          required: typeof required === 'boolean' ? required : false,
          message: `请${isSelect ? '选择' : '填写'}${name}`,
          trigger: isSelect ? 'change' : 'blur'
        }
      ];
      return item;
    });

    this.key = Date.now();
  }

  /* 处理默认值 */
  handleDefaultValue({ name, frontendInfo, defaultValue = '' }: any) {
    if (typeof frontendInfo === 'string' && frontendInfo) {
      return this.originData[name] || frontendInfo.replace(/PN|FN|UN|CID/g, (el) => this[el]);
    }
    return this.originData[name] || defaultValue;
  }

  getProp(data: any) {
    return data.row[this.propName];
  }

  getOptions(data: any) {
    return data.row[this.optionName] || [];
  }

  isSelect(data: any) {
    return data.row.pageType === 'SELECT_TYPE';
  }

  isInput(data: any) {
    return data.row.pageType === 'INPUT_TYPE';
  }

  async validate() {
    try {
      await this.formRef.validate();
    } catch (e) {
      if (!this.activeName.includes('connector')) {
        this.activeName.push('connector');
      }
      throw new Error(e);
    }
  }

  handleFormChange() {
    const { form, tableData, propName } = this;
    const data = tableData.reduce((pre: any, next: any) => {
      const prop = next[propName];
      if (prop in form) {
        pre[next.name] = form[prop];
      }
      return pre;
    }, {});
    this.$emit('change', data);
  }
}
</script>
<style lang="scss" scoped>
.connector {
  &-table {
    &__item {
      margin: 22px 0 !important;

      ::v-deep .el-form-item {
        &__content {
          margin: 0 25px !important;
          line-height: 20px;
        }

        &__error {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  &-name {
    display: inline-block;
    width: 280px;
    overflow: hidden;
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
  }

  &-icon {
    margin: 0 0 0 5px;
    vertical-align: middle;
  }
}
</style>
