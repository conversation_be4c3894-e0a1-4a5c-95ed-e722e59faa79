<template>
  <div>
    <header class="header">
      <div class="header-info" :title="cannotImpOveriewMsg">{{ cannotImpOveriewMsg }}</div>
      <div v-if="hideOper" class="header-oper">
        <el-button type="primary" size="small" @click="download">{{ $t('pa.action.download') }}</el-button>
      </div>
    </header>
    <bs-table
      size="small"
      height="calc(70vh - 250px)"
      :column-settings="false"
      :data="cannotImportData"
      :column-data="columnData"
      :page-data="pageData"
      paging-front
      @page-change="handlePageChange"
    >
      <template slot="type" slot-scope="{ row }">
        {{ enumData[row.type] }}
      </template>
    </bs-table>
  </div>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { unImportDataDownload } from '@/apis/impExpApi';
import { download } from '@/utils';
import { ASSETS_TPYE_ENUMS } from './utils';
@Component
export default class ImportCannotData extends Vue {
  @Prop() cannotImportData!: any;
  @Prop() cannotImpOveriewMsg!: string;
  @PropSync('loading') downloadLoading!: boolean;
  columnData = [
    { label: this.$t('pa.resource.importExport.assetId'), value: 'id' },
    { label: this.$t('pa.resource.importExport.assetName'), value: 'name' },
    { label: this.$t('pa.resource.importExport.assetType'), value: 'type', width: 100 },
    { label: this.$t('pa.addOrg'), value: 'orgName', width: 100 },
    { label: this.$t('pa.data.text11'), value: 'mesg' }
  ];
  pageData = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 };
  enumData = ASSETS_TPYE_ENUMS;

  get hideOper() {
    return !!this.cannotImportData.length; // 有数据的时候显示操作栏
  }

  created() {
    this.pageData.total = this.cannotImportData.length;
    this.pageData.currentPage = 1;
  }

  // 表格页码修改回调
  handlePageChange(page: number, size: number) {
    this.pageData.currentPage = page;
    this.pageData.pageSize = size;
  }

  // 下载不可导入数据
  async download() {
    try {
      this.downloadLoading = true;
      const data = await unImportDataDownload(this.cannotImportData);
      if (data.type === 'application/json') {
        data.text().then((text) => {
          const { success, msg, error } = JSON.parse(text);
          if (!success) this.$message.error(msg || error);
        });
      } else {
        download(data);
      }
      this.downloadLoading = false;
    } catch {
      this.downloadLoading = false;
    }
  }
}
</script>
<style scoped lang="scss">
.header {
  display: flex;
  height: 38px;
  justify-content: space-between;
  align-items: center;
  padding: 0 34px 10px;

  &-info {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &-oper {
    margin-left: 10px;
  }
}
</style>
