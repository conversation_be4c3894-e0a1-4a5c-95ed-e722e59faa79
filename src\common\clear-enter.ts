import { Vue, Component } from 'vue-property-decorator';
@Component
export default class ClearEnter extends Vue {
  mounted() {
    window.addEventListener('copy', this.handleCopy);
  }
  beforeDestroyed() {
    document.removeEventListener('copy', this.handleCopy);
  }
  async handleCopy(e) {
    //取消默认复制
    e.preventDefault();
    const content = window.getSelection()?.toString().trim();
    if (!content) return;
    // 给剪切板赋值
    e.clipboardData.setData('text', content);
  }
}
