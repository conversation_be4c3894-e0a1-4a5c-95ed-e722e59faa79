<template>
  <ul class="list__container">
    <!-- 个体模式 -->
    <li v-for="(el, index) in patterns" :key="index" class="list-item list-flex">
      <!-- 左侧 -->
      <div class="list-item__left list-flex">
        <el-form-item
          label="个体模式名称"
          :rules="modelNameRule"
          :prop="generateProp(index)"
          label-width="120px"
          class="list-item__left__name"
        >
          <el-input
            v-model="formData.singletonCepPatterns[index].singletonModeName"
            class="list-item__left-input"
            clearable
            size="small"
            :maxlength="30"
            :minlength="1"
            placeholder="请输入"
            @input="handleBlur(index)"
          />
        </el-form-item>
        <!-- 结果表达式 -->
        <el-form-item
          label="结果表达式："
          :rules="resultExpression"
          :prop="generateProp(index, 'resultExpression')"
          label-width="120px"
          class="list-item__left__expression"
        >
          <el-tooltip
            :key="generateKey(el.resultExpression)"
            v-hide="el.resultExpression"
            effect="light"
            placement="top"
          >
            <div slot="content" v-html="el.resultExpression"></div>
            <div class="list-item__left-resultExpression" v-html="el.resultExpression"></div>
          </el-tooltip>
        </el-form-item>
      </div>
      <!-- 右侧 -->
      <div class="list-item__right list-flex">
        <el-button type="text" @click="$emit('config', index)">添加条件</el-button>
        <el-button type="text" @click="deteteModel(index)">删除模式</el-button>
      </div>
    </li>
  </ul>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { hide, sha256 } from '../../util';

@Component({ directives: { hide } })
export default class ModelList extends Vue {
  @Prop({ default: () => [] }) allName!: any;
  @PropSync('modelList', { default: () => [] }) list!: any[];
  @PropSync('data', { default: () => ({}) }) formData!: any;
  private resultExpression = {
    required: true,
    message: '请配置条件',
    trigger: 'change'
  };

  get modelNameRule() {
    return {
      required: true,
      hasList: this.allName,
      validator({ hasList }: any, value, callback) {
        if (!value) return callback(new Error('请输入模式名称'));
        if (value.includes(':')) return callback(new Error('模式名称不允许带:'));
        const index = hasList.indexOf(value);
        const lastIndex = hasList.lastIndexOf(value);
        if (index > -1 && lastIndex > -1 && index !== lastIndex) {
          return callback(new Error('该模式名称已存在，不能重复添加'));
        }
        if (value.length > 30) return callback(new Error('超过不允许输入'));
        return callback();
      },
      trigger: 'blur'
    };
  }
  get patterns() {
    return this.formData.singletonCepPatterns;
  }
  /* 生成prop */
  generateProp(index: number, key = 'singletonModeName') {
    return `singletonCepPatterns[${index}].${key}`;
  }

  /* 删除模式 */
  async deteteModel(index: number) {
    await this.$confirm('确认则删除该行数据', '提示');
    this.formData.singletonCepPatterns.splice(index, 1);
    this.list.splice(index, 1);
    this.$message.success('删除成功');
  }
  handleBlur(index: number) {
    this.$emit('change', ...[index, this.patterns[index].singletonModeName]);
  }
  generateKey(str = 'null') {
    return sha256(str);
  }
}
</script>

<style lang="scss" scoped>
.list {
  &__container {
    display: block;
    ::v-deep .el-form-item {
      margin-bottom: 0 !important;
    }
  }
  &-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-item {
    margin: 0 auto 10px;
    width: calc(100% - 80px);
    height: 66px;
    background: #f9f9f9;
    border-radius: 4px;
    &__left {
      width: calc(100% - 192px);
      &__name {
        width: 400px;
      }
      &__expression {
        width: calc(100% - 400px);
      }
      &-input {
        width: 276px;
      }
      &-resultExpression {
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    &__right {
      margin-right: 20px;
      width: 136px;
    }
  }
}
</style>
