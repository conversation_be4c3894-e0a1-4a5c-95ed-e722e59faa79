<template>
  <div
    v-if="display && menus.length"
    :style="{ ...position }"
    class="right__container"
    @click.stop
    @contextmenu.stop.prevent
  >
    <div v-if="showRoundMenus" class="right-round">
      <div class="custom-svg"></div>
    </div>
    <div v-else class="right-normal" @click.stop>
      <div
        v-for="el in menus"
        v-show="el.visible"
        :key="el.value"
        class="right-normal__item"
        @click.stop="handleClick(el)"
      >
        <i :class="`iconfont ${el.icon}`"></i>
        <span>{{ el.label }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, InjectReactive, ModelSync, Watch, Vue } from 'vue-property-decorator';
import { getZoom, generateNodeMenus, generateEdgeMenus, RadiusMenu } from './util';
interface Config {
  zoom: number;
  x: number;
  y: number;
  type: string;
  id: string;
  trigger: string;
  isDynamic: boolean;
}
@Component
export default class RightMenu extends Vue {
  @Prop() graph!: any;
  @Prop() config!: Config;
  @InjectReactive() canUseSql!: boolean;
  @ModelSync('show', 'input', { type: Boolean }) display!: boolean;

  private nodePostion = { x: 0, y: 0 };

  get menus() {
    return this.config.trigger === 'node'
      ? generateNodeMenus(this.config, this.canUseSql)
      : generateEdgeMenus();
  }
  get showRoundMenus() {
    return this.config.trigger === 'node' && this.menus.length > 2;
  }
  get position() {
    let { x, y } = this.config;
    if (this.showRoundMenus) {
      const target = this.graph.getElementById(this.config.id);
      x = (this.nodePostion.x || target.renderedPosition().x) - 150;
      y = this.nodePostion.y || target.renderedPosition().y - 150;
    } else {
      x = x + 270;
      y = y + 100;
    }
    return { left: `${x}px`, top: `${y}px`, transform: `scale(${getZoom(this.config.zoom)})` };
  }

  @Watch('config', { deep: true })
  handler() {
    if (!this.showRoundMenus) return;
    this.$nextTick(() => {
      this.nodePostion = { x: 0, y: 0 };
      const target = this.graph.getElementById(this.config.id);
      target.on('position', () => (this.nodePostion = target.renderedPosition()));
      this.graph.on('pan', () => (this.nodePostion = target.renderedPosition()));
      const RM = new RadiusMenu({ el: '.custom-svg', menuItems: this.menus });
      RM.drawSvg();
      RM.click((el: any) => this.handleClick(el));
    });
  }
  created() {
    document.body.addEventListener('click', () => this.display && (this.display = false));
  }

  handleClick(item: any) {
    this.$emit('click', { ...item, id: this.config.id });
    this.display = false;
  }
}
</script>
<style lang="scss" scoped>
.right {
  &__container {
    position: absolute;
    z-index: 2;
  }
  &-round {
    .custom-svg {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 300px;
      height: 300px;
    }
  }
  &-normal {
    padding: 8px 0;
    background-color: #737278;
    box-shadow: 0px 0px 10px 0px rgba(23, 32, 47, 0.06);
    opacity: 0.8;
    border-radius: 4px;
    &__item {
      padding: 6px 22px 6px 14px;
      color: #fff;
      font-size: 14px;
      cursor: pointer;
      transition: 0.1s;
      i {
        margin-right: 8px;
      }
      &:hover {
        background-color: #58575d;
      }
    }
  }
}
::v-deep .custom-svg {
  .bg {
    opacity: 0.2;
    animation: bgAni 0.1s cubic-bezier(0.33, 0, 0.67, 1);
    backface-visibility: hidden;
    perspective: 1000;
  }
  @keyframes bgAni {
    from {
      transform: scale(0.2);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 0.2;
    }
  }
  .circle-dot {
    animation: rotateAni 4s linear infinite;
    backface-visibility: hidden;
    perspective: 1000;

    .dot-g {
      opacity: 1;
      animation: scaleAni 0.1s cubic-bezier(0.33, 0, 0.67, 1);
      backface-visibility: hidden;
      perspective: 1000;
    }
  }
  @keyframes rotateAni {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  @keyframes scaleAni {
    from {
      transform: scale(0.2);
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  .menu-item {
    animation: menuAni 0.02s cubic-bezier(0.35, 0, 0.65, 1);
    backface-visibility: hidden;
    perspective: 1000;

    path {
      transition: all 0.1s;
    }
    &:hover {
      cursor: pointer;
      path {
        fill: #58575d;
      }
    }
    text {
      user-select: none;
    }
  }
  .menu-item-first {
    animation: firstMenuAni 0.02s cubic-bezier(0.35, 0, 0.65, 1);
    backface-visibility: hidden;
    perspective: 1000;
  }
  @keyframes firstMenuAni {
    from {
      transform: scale(0.2);
    }
    to {
      transform: scale(1);
    }
  }
  @keyframes menuAni {
    from {
      transform: scale(0.9);
    }
    to {
      transform: scale(1);
    }
  }
  .sub-menu-item {
    animation: subAni1 0.05s cubic-bezier(0.33, 0, 0.66, 1);
    backface-visibility: hidden;
    perspective: 1000;
    &:hover {
      path {
        fill: #38383d;
      }
    }
  }
  @keyframes subAni1 {
    from {
      transform: scale(0);
    }
    to {
      transform: scale(1);
    }
  }
}
</style>
