<template>
  <pro-page title="视图管理" :fixed-header="false">
    <div slot="operation" class="operate-box">
      <el-select
        v-model="search.resType"
        clearable
        placeholder="请选择服务类型"
        @change="handleSearchChange"
      >
        <el-option
          v-for="item in resTypeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <bs-search
        v-model="search.viewName"
        placeholder="请输入视图名，中文名"
        size="small"
        maxlength="30"
        style="margin-left: 10px"
        @search="handleSearchChange"
      />
      <bs-search
        v-model="search.fieldName"
        placeholder="请输入字段名，中文名"
        size="small"
        maxlength="30"
        style="margin-left: 10px"
        @search="handleSearchChange"
      />
      <el-button
        v-access="'PA.DATA.VIEW.ADD'"
        type="primary"
        style="margin-left: 10px"
        @click="editView(null)"
      >
        新建视图
      </el-button>
    </div>
    <bs-table
      :height="selectedRows.length ? 'calc(100vh - 344px)' : 'calc(100vh - 288px)'"
      :data="tableData"
      :column-data="columnData"
      :page-data="pageData"
      :selection="true"
      @page-change="handlePageChange"
      @selection-change="handleSelectionChange"
      @row-dblclick="handleDblclick"
      @refresh="getListData"
    >
      <el-button
        slot="headerOperator"
        v-access="'PA.DATA.VIEW.DELETE'"
        size="small"
        @click="delView(null)"
      >
        删除
      </el-button>
      <div slot="viewName" slot-scope="{ row }" class="viewName-slot">
        <el-tooltip :content="row.viewName" effect="light" placement="top">
          <div class="viewName">{{ row.viewName }}</div>
        </el-tooltip>
        <el-tag v-if="row.shareFlag" size="mini" style="margin-left: 8px"> 分享 </el-tag>
      </div>
      <!-- <template slot="viewName" slot-scope="{ row }" class="viewName-slot">
        <span class="viewName">{{ row.viewName }}</span>
        <el-tag v-if="row.shareFlag" size="mini" style="margin-left: 8px">分享</el-tag>
      </template> -->
      <template slot="shareFlag" slot-scope="{ row }">
        <span>{{ row.shareFlag ? '分享' : '自建' }}</span>
      </template>
      <template slot="action" slot-scope="{ row }">
        <el-tooltip content="编辑" effect="light">
          <i
            v-if="hasAuthority(row.dataLevelType, 'PA.DATA.VIEW.EDIT') && !row.shareFlag"
            class="iconfont icon-bianji"
            @click="editView(row)"
          ></i>
        </el-tooltip>
        <el-tooltip content="删除" effect="light">
          <i
            v-if="hasAuthority(row.dataLevelType, 'PA.DATA.VIEW.DELETE')"
            class="iconfont icon-shanchu"
            @click="delView(row)"
          ></i>
        </el-tooltip>
        <el-tooltip content="查看" effect="light">
          <i class="iconfont icon-chakan" @click="detail(row)"></i>
        </el-tooltip>
        <el-tooltip content="分享" effect="light">
          <i
            v-if="hasAuthority(row.dataLevelType, 'PA.DATA.VIEW.SHARE') && !row.shareFlag"
            class="iconfont icon-fenxiang"
            @click="deptRole(row)"
          ></i>
        </el-tooltip>
      </template>
    </bs-table>
    <ShareTree
      v-if="showShareTreeDialog"
      ref="ShareTree"
      :visible.sync="showShareTreeDialog"
      :data="deptData"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import ShareTree from '@/components/share-tree.vue';
import { delViews, getResList, getViewList } from '@/apis/dataApi';
import { hasPermission } from '@/utils';
import CommonDelete from '@/utils/mixins/common-delete';
import moment from 'moment';
import { cloneDeep } from 'lodash';
@Component({
  components: {
    ShareTree
  }
})
export default class ViewManage extends Mixins(CommonDelete) {
  tableLoading = false;
  // 筛选条件
  search = {
    fieldName: '', // 字段名称
    resType: '', // 服务类型
    viewName: ''
  };
  pageData = { pageSize: 20, currentPage: 1, total: 1 };
  sortData: any = { updateTime: 'DESC' };
  tableData = [];
  columnData = [];
  // 服务列表
  resTypeList = [];
  // 表格多选数据
  selectedRows = [];
  // 分享弹窗相关
  showShareTreeDialog = false;
  deptData: any = {};
  created() {
    this.getListData();
    this.getResTypeList();
  }
  deptRole(row) {
    this.deptData = row;
    this.showShareTreeDialog = true;
  }
  // 跳转详情
  detail(row: any) {
    this.$router.push({
      path: '/data/viewDetail',
      query: { id: row.id, status: '2', title: row.viewName }
    });
  }
  // 跳转编辑
  editView(row: any) {
    const isNew = !row;
    this.$router.push({
      path: '/data/viewEdit',
      query: isNew
        ? { status: '0', title: '新建视图' }
        : { id: row.id, status: '1', title: row.viewName }
    });
  }
  // 删除视图 TODO:
  async delView(row: any) {
    const ids = !row
      ? this.selectedRows.map((e: any) => ({
          id: e.id,
          name: e.viewName,
          reference: e.relationNum
        }))
      : { id: row.id, name: row.viewName, reference: row.relationNum };
    this.commonDel(ids, async (delIds) => {
      const { success, msg } = await delViews(delIds);
      if (success) {
        this.getListData();
        this.$message.success(msg);
      }
    });
  }
  // 获取服务列表
  getResTypeList() {
    getResList().then((resp: any) => {
      this.resTypeList = (resp.data || []).map((item) => {
        return {
          value: item,
          label: item
        };
      });
    });
  }
  // 获取列表数据
  getListData() {
    const { pageData, sortData } = this;
    const search = cloneDeep(this.search);
    search.fieldName = search.fieldName.trim();
    search.viewName = search.viewName.trim();
    const params = {
      sortData,
      search,
      pageData
    };
    getViewList(params).then((resp: any) => {
      const { columnData, tableData, pageData } = resp.data;
      tableData.forEach((item: any) => {
        item.updateTime = moment(item.updateTime).format('YYYY-MM-DD HH:mm:ss');
      });
      columnData.forEach((el) => {
        if (el.value === 'viewName') {
          el.width = 200;
          el.showOverflowTooltip = false;
        }
      });
      this.tableData = tableData;
      this.columnData = columnData;
      this.pageData.total = pageData.total || 1;
    });
  }
  // 处理筛选项回调
  handleSearchChange() {
    this.pageData.currentPage = 1;
    this.getListData();
  }
  // 双击表格行
  handleDblclick(row) {
    this.detail(row);
  }
  // 表格页码修改回调
  handlePageChange(page: number, size: number) {
    this.pageData.currentPage = page;
    this.pageData.pageSize = size;
    this.getListData();
  }
  // 表格多选回调
  handleSelectionChange(sel: any) {
    this.selectedRows = sel;
  }
  // 权限判断
  hasAuthority(dataLevelType: string, accessCode: string) {
    return ['SELF', 'CHILD'].includes(dataLevelType) && hasPermission(accessCode);
  }
  // viewEdit
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // 通过 `vm` 访问组件实例
      if (from.name === 'viewEdit') {
        vm.getListData();
      }
    });
  }
}
</script>
<style scoped lang="scss">
.viewName-slot {
  display: flex;
  align-items: center;
}
.viewName {
  display: inline-block;
  max-width: 90px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.operate-box {
  display: flex;
}
.iconfont {
  cursor: pointer;
}
.iconfont + .iconfont {
  margin-left: 10px;
}
</style>
