<template>
  <div class="detail_content">
    <div>
      <div class="bs-detail-block">
        <!--表单-->
        <div class="bs-detail__header padA20">
          <div class="bs-detail__header-title">基本信息</div>
        </div>
        <div class="bs-detail__content">
          <div v-for="(row, rowIndex) in formRow" :key="rowIndex" class="bs-detail-info">
            <div v-for="(col, colIndex) in row" :key="colIndex" class="bs-detail-info__item col-3">
              <span class="bs-detail-info__label">{{ col.label }}</span>
              <span
                v-if="col.key && (col.key === 'createdBy' || col.key === 'updatedBy')"
                class="bs-detail-info__content"
              >
                <a style="cursor: pointer" @click="getUserInfo(col.value)">{{ col.value }}</a>
              </span>
              <span v-else class="bs-detail-info__content"> {{ col.value }}</span>
            </div>
          </div>
          <el-descriptions
            :column="1"
            label-class-name="detail__label"
            content-class-name="detail__content"
          >
            <el-descriptions-item
              v-for="row in formArray.textAreaData"
              :key="row.label"
              :label="row.label.replace(':', '')"
            >
              {{ getContent(row) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>
    <contact-info ref="ContactInfo" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import contactInfo from '@/components/contact-info.vue';
@Component({ components: { contactInfo } })
export default class BaseInfoContent extends PaBase {
  @Prop({ default: {} }) formArray!: any;
  @Prop({ default: {} }) detailRecord!: any;

  formRow: any = [];

  getUserInfo(username: string) {
    const ContactInfo: any = this.$refs.ContactInfo;
    ContactInfo.open([username]);
  }

  @Watch('formArray')
  onFormArrayChange(val) {
    const p = 100 / val.width;
    let index = 0;
    let array: any = [];
    if (val.rowData) {
      val.rowData.forEach((n) => {
        let value;
        if (n.method) {
          if (n.subKey) {
            value = this[n.method](this.detailRecord[n.key])[n.subKey];
          } else {
            value = this[n.method](this.detailRecord[n.key]);
          }
        } else {
          value = this.detailRecord[n.key];
        }
        array.push({
          label: n.label,
          key: n.key,
          value: value
        });
        index++;
        if (index === p) {
          this.formRow.push(array);
          array = [];
          index = 0;
        }
      });
      if (array.length !== 0 && array.length < p) {
        const len: any = p - array.length;
        for (let i = 0; i < len; i++) {
          array.push({
            label: '',
            value: ''
          });
        }
      }
      this.formRow.push(array);
    }
  }

  getContent({ key, subKey }) {
    if (!key) return;
    if (!subKey) return this.detailRecord[key];
    try {
      const result = this.detailRecord[key];
      const data = typeof result === 'string' ? JSON.parse(result) : result;
      return data[subKey] || '';
    } catch {
      return;
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .detail {
  &__label {
    font-size: 14px;
    color: #777;
  }
  &__content {
    font-size: 12px;
    color: #444;
    line-height: 22px;
  }
}
</style>
