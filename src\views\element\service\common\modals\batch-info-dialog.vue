<template>
  <bs-dialog
    title="批量操作信息"
    width="800px"
    :visible.sync="visible"
    :before-close="closeDialog"
    :append-to-body="false"
  >
    <el-descriptions :column="2">
      <el-descriptions-item label="批量操作状态">{{ batchEventStatus }} </el-descriptions-item>
      <el-descriptions-item label="批量操作类型">{{ batchEventType }}</el-descriptions-item>
      <el-descriptions-item label="批量操作提交时间">
        {{ batchEventSubmitTime }}
      </el-descriptions-item>
      <el-descriptions-item label="批量操作完成时间">
        {{ batchEventFinishTime }}
      </el-descriptions-item>
    </el-descriptions>
    <bs-table
      row-key="jobId"
      :data="flowData"
      :column-data="columnData"
      :page-data="pageData"
      paging-front
      :column-settings="false"
    >
      <template slot="jobStatus" slot-scope="{ row }">
        <bs-tag size="mini" :color="getStatusColor(row.jobStatus)">
          <i :class="getStatusIcon(row.jobStatus)"></i>
          {{ jobStatusMap[row.jobStatus] }}
        </bs-tag>
      </template>
      <el-button slot="action" slot-scope="{ row }" type="text" @click="toFlowDetail(row)">
        查看流程详情
      </el-button>
    </bs-table>
    <div slot="footer">
      <el-button @click="closeDialog">关闭</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import dayjs from 'dayjs';
import { Component, Vue, Prop } from 'vue-property-decorator';
const BATCH_EVENT_TYPE_MAP = {
  DEV: '开发',
  PUB: '发布',
  ONLINE: '启动',
  OFFLINE: '停止'
};
@Component
export default class BatchInfoDialog extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: () => {} }) private data!: any;
  // 批操作状态
  batchEventStatus = '';
  // 批量操作类型
  batchEventType = '';
  // 批操作提交时间
  batchEventSubmitTime = '';
  // 批操作完成时间
  batchEventFinishTime = ' ';
  flowData: any[] = [{}];
  columnData = [
    {
      label: '流程名称',
      value: 'jobName'
    },
    {
      label: '流程状态',
      value: 'jobStatus'
    },
    {
      label: '操作是否成功',
      value: 'operationSuccessText'
    },
    {
      label: '操作',
      value: 'action'
    }
  ];
  pageData = {
    currentPage: 1,
    pageSize: 10,
    total: 0
  };
  jobStatusMap = {
    DEV: '开发',
    INPUB: '发布中',
    PUB: '已发布',
    INPROD: '上线中',
    PROD: '已上线'
  };

  created() {
    const { data = {} } = this;
    this.batchEventStatus = data.batchEventStatus;
    this.batchEventType = BATCH_EVENT_TYPE_MAP[data.batchEventType];
    this.batchEventSubmitTime = dayjs(data.batchEventSubmitTime).format('YYYY-MM-DD HH:mm:ss');
    this.batchEventFinishTime = data.batchEventFinishTime
      ? dayjs(data.batchEventFinishTime).format('YYYY-MM-DD HH:mm:ss')
      : '';
    this.flowData = (data.batchEventOperationJobs || []).map((item) => {
      return {
        ...item,
        // 操作完成才显示操作是否成功
        operationSuccessText: item.operationFinished ? (item.operationSuccess ? '是' : '否') : '-'
      };
    });
    this.pageData.total = (data.batchEventOperationJobs || []).length;
  }
  // 流程状态tag颜色
  getStatusColor(status: string) {
    return (
      {
        PROD: 'green',
        INPUB: 'purple',
        DEV: 'purple'
      }[status] || ''
    );
  }
  getStatusIcon(status: string) {
    return {
      INPUB: 'iconfont icon-shangxianzhong',
      INPROD: 'iconfont icon-shangxianzhong'
    }[status];
  }
  // 跳转流程详情
  toFlowDetail(row: any) {
    this.$router.push({
      path: '/flow',
      query: {
        id: row.projectId,
        title: row.jobName,
        flowId: row.jobId
      }
    });
  }
  // 关闭弹窗
  closeDialog() {
    this.$emit('update:visible', false);
  }
}
</script>
<style lang="scss" scoped>
.icon-shangxianzhong {
  font-size: 12px;
}
</style>
