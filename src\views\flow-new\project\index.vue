<template>
  <div class="flow-project">
    <div class="flow-project-content">
      <!-- 卡片样式列表 -->
      <card-list
        v-if="showCardList"
        @switch="switchList($event)"
        @showDrawer="showFullSearchDrawer"
      />
      <!-- 常规样式列表 -->
      <normal-list
        v-if="!showCardList"
        @switch="switchList($event)"
        @showDrawer="showFullSearchDrawer"
      />
      <!-- 全量搜索抽屉 -->
      <search-drawer class="search-drawer" :visible.sync="showSearch" />
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import CardList from './card-list/index.vue';
import SearchDrawer from './components/search-drawer.vue';
import NormalList from './normal-list/index.vue';
import { hasPermission } from '@/utils';
@Component({
  components: {
    CardList,
    NormalList,
    SearchDrawer
  }
})
export default class FlowProject extends Vue {
  showSearch = false;
  showCardList = true;
  created() {
    this.showCardList = hasPermission('PA.FLOW.PROJECT_MGR.VIEW');
  }
  // 列表样式切换
  switchList(isCard) {
    this.showCardList = isCard;
  }
  showFullSearchDrawer() {
    this.showSearch = true;
  }
}
</script>
<style lang="scss" scoped>
.search-drawer {
  top: 158px;
  bottom: 15px;
  right: 20px;
}
</style>
