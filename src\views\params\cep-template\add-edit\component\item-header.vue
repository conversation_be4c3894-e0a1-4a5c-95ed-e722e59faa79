<template>
  <!-- 头部 -->
  <div class="form-header__container">
    <!-- 左边 -->
    <div class="form-header__left">
      <!-- 名称 -->
      <el-form-item v-if="name" :label="name" prop="groupName" :rules="nameRule" label-width="95px">
        <el-input
          v-model="formData.groupName"
          clearable
          size="small"
          maxlength="30"
          minlength="1"
          class="form-header__left__name"
          placeholder="请输入"
          @input="handleBlur"
        />
      </el-form-item>
      <!-- 逻辑关系 -->
      <el-form-item
        label="逻辑关系："
        class="form-header__left__expression"
        :class="{ 'form-header__left__expression--name': name }"
      >
        <el-tooltip
          :key="key"
          v-hide="expression"
          :content="expression"
          effect="light"
          placement="top"
        >
          <div class="form-header__left__expression__main">{{ expression }}</div>
        </el-tooltip>
      </el-form-item>
    </div>
    <!-- 右边 -->
    <div v-if="isSequence">
      <el-button size="mini" type="primary" @click="click('add', index)">添加模式(组)</el-button>
      <el-button size="mini" @click="click('view', index)">查看序列</el-button>
    </div>
    <!-- 右边 -->
    <div v-else>
      <el-button size="mini" type="primary" @click="click('add', index)">添加模式</el-button>
      <el-button size="mini" @click="click('delete', index)">删除模式组</el-button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Watch, Vue } from 'vue-property-decorator';
import { debounce } from 'lodash';
import { hide, sha256, generateAllRelationship } from '../../util';

@Component({ directives: { hide } })
export default class ItemHeader extends Vue {
  @Prop({ type: Number }) index!: number;
  @Prop({ default: () => [] }) allName!: any;
  @Prop({ type: String, default: '' }) name!: string;
  @Prop({ type: Boolean, default: false }) isSequence!: boolean;
  @PropSync('data', { type: Object, default: () => ({}) }) formData!: any;

  private labelWidth = '';
  private click = debounce(this.handleClick, 600);
  get expression() {
    return this.formData.logicalRelationship || '';
  }
  get key() {
    return sha256(this.expression);
  }

  get nameRule() {
    return {
      required: true,
      name: this.name,
      hasList: this.allName,
      validator({ name, hasList }: any, value, callback) {
        if (!value) return callback(new Error(`请输入${name}`));
        if (value.includes(':')) return callback(new Error('模式组名称不允许带:'));
        const index = hasList.indexOf(value);
        const lastIndex = hasList.lastIndexOf(value);
        if (index > -1 && lastIndex > -1 && index !== lastIndex) {
          return callback(new Error('该模式名称已存在，不能重复添加'));
        }
        if (value.length > 30) return callback(new Error('超过不允许输入'));
        return callback();
      },
      trigger: 'blur'
    };
  }

  @Watch('formData.groupCepPatternConditionList', { deep: true })
  handler(value: any[]) {
    this.$set(this.formData, 'logicalRelationship', generateAllRelationship(value));
  }
  handleBlur() {
    this.$emit('change', ...[this.index, this.formData.groupName]);
  }
  handleClick(type: string, index: number, $index: number) {
    this.$emit('click', ...[type, index, $index]);
  }
}
</script>

<style lang="scss" scoped>
.form-header {
  &__container {
    display: flex;
    justify-content: space-between;
    align-content: center;
  }
  &__left {
    display: flex;
    align-content: center;
    width: calc(100% - 200px);
    &__name {
      width: 270px;
    }
    &__expression {
      width: calc(100% - 240px);
      ::v-deep .el-form-item__label {
        padding-right: 0 !important;
        width: 70px;
      }
      &--name {
        margin-left: 40px;
        width: calc(100% - 340px);
      }
      &__main {
        // background: pink;
        width: 60%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: left;
      }
    }
  }
}
</style>
