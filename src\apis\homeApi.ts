import { get, post } from './utils/net';
// 查询超时处理
const timeoutSetting = { timeout: 0, timeoutErrorMessage: '查询超时，请刷新重试' };
// 机构列表
export const getOrgList = (orgId) => {
  return get(`/rs/pa/portal/getOrgInfo?orgId=${orgId}`, null, timeoutSetting);
};
// 待处理预警
export const getWarningData = (ids) => {
  return post(`/rs/pa/portal/warnInfoForHome`, ids, timeoutSetting);
};
// 引擎资源使用情况
export const getEngineData = (clusterId, data) => {
  const id = clusterId ? `&clusterId=${clusterId}` : '';
  return post(`/rs/pa/portal/clusterMsg?${id}`, data, timeoutSetting);
};
// 引擎资源使用情况-集群信息
export const getClustersData = (ids) => {
  return post(`/rs/pa/portal/getCluster`, ids, timeoutSetting);
};
// 流程状态/数量分布
export const getFlowDistribution = (ids) => {
  return post(`/rs/pa/portal/getFlowInfo`, ids, timeoutSetting);
};
// 流程数据处理量统计信息
export const getFlowDataCount = (ids) => {
  return post(`/rs/pa/portal/getFlowDataCount`, ids, timeoutSetting);
};
// 服务注册量
export const getServiceData = (ids) => {
  return post(`/rs/pa/portal/resRegistrationCount`, ids, timeoutSetting);
};
