<template>
  <bs-dialog
    :title="title"
    :visible.sync="visible"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    class="batch-upload-dialog"
  >
    <el-form
      ref="ruleForm"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
    >
      <el-form-item label="文件" style="text-align: left" prop="pkg">
        <el-upload
          ref="upload2"
          :action="getAction()"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :file-list="formData.fileList"
          multiple
          :on-success="handleSuccess"
          :before-upload="handleBeforeUpload"
          accept=".jar"
        >
          <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
          <span slot="tip" class="el-upload__tip" style="padding-left: 16px">
            只能上传jar文件，且不超过60mb
          </span>
        </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">关闭</el-button>
      <el-button
        :disabled="hasFile || index !== formData.fileList.length"
        type="primary"
        @click="submitUpload"
      >
        确认
      </el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import { URL_COMPONENT_BATCH_SAVE } from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
import { baseUrl } from '@/config';
@Component
export default class AddEdit extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: '批量上传(目前只针对不存在的组件)' }) title!: string;
  formData: any = { fileList: [] };
  needFresh = false;
  rules: any = {
    pkg: [{ required: true, validator: this.validateFile, trigger: 'blur' }]
  };
  index = 0;
  validateFile(rule: any, value: any, callback: any) {
    if (this.formData.fileList === undefined || this.formData.fileList.length === 0) {
      if (this.formData.id) {
        callback();
      } else {
        callback(new Error('请添加文件'));
      }
    } else {
      callback();
    }
  }
  get hasFile() {
    return this.formData.fileList.length === 0;
  }
  getAction() {
    return baseUrl.prev + URL_COMPONENT_BATCH_SAVE;
  }
  submitUpload() {
    const form: any = this.$refs.ruleForm;
    form.validate((valid: any) => {
      if (valid) {
        this.needFresh = true;
        this.formData.fileList = [];
        this.$emit('close', true);
      }
    });
  }
  handleRemove(file, fileList) {
    this.formData.fileList = fileList;
    this.index = fileList.length;
  }
  handlePreview(file) {
    if (!file.response.success) {
      this.$message.error(file.response.msg);
    }
  }
  handleSuccess(response, file, fileList) {
    this.index = this.index + 1;
    if (!response.success) {
      file.status = 'error';
      file.name = `（上传失败）${file.name}`;
    }
    this.formData.fileList = fileList;
  }
  handleBeforeUpload(file) {
    if (!this.beforeUpload(file)) {
      return false;
    }
    (this.$refs.ruleForm as any).clearValidate();
  }

  closeDialog() {
    this.formData.fileList = [];
    (this.$refs.ruleForm as any).clearValidate();
    this.$emit('close', false);
  }
  beforeUpload(file) {
    const name = file.name;
    const isJar = name.indexOf('.jar') >= 0;
    const isLt60M = file.size / 1024 / 1024 < 60;
    if (!isJar) {
      this.$message.error('只能上传jar类型的文件!');
    }
    if (!isLt60M) {
      this.$message.error('不能上传超过60MB的文件!');
    }
    return isJar && isLt60M;
  }
}
</script>

<style lang="scss">
.batch-upload-dialog .el-upload-list__item.is-error .el-upload-list__item-name {
  color: #ff5353;
}
</style>
