<template>
  <bs-dialog
    :title="title"
    :visible.sync="visible"
    :width="width"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    class="batch-upload-dialog"
  >
    <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="100px" class="demo-ruleForm">
      <el-form-item :label="$t('pa.file')" style="text-align: left" prop="pkg">
        <el-upload
          ref="upload2"
          :action="getAction()"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :file-list="formData.fileList"
          multiple
          :with-credentials="true"
          :on-success="handleSuccess"
          :before-upload="handleBeforeUpload"
          accept=".jar"
          :headers="headers"
        >
          <el-button slot="trigger" size="small" type="primary">{{ $t('pa.choseFile') }}</el-button>
          <span slot="tip" class="el-upload__tip" style="padding-left: 16px"> {{ $t('pa.tip.onlyJar250MB') }} </span>
        </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">{{ $t('pa.action.close') }}</el-button>
      <el-button :disabled="hasFile || index !== formData.fileList.length" type="primary" @click="submitUpload">
        {{ $t('pa.action.makeSure') }}
      </el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import { URL_COMPONENT_BATCH_SAVE } from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
import { baseUrl } from '@/config';
import { getToken } from '@/utils';
import i18n from '@/i18n';
@Component
export default class AddEdit extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: i18n.t('pa.uploadBatch') }) title!: string;
  formData: any = { fileList: [] };
  needFresh = false;
  rules: any = {
    pkg: [{ required: true, validator: this.validateFile, trigger: 'blur' }]
  };
  index = 0;
  headers = {
    Authorization: getToken()
  };
  validateFile(rule: any, value: any, callback: any) {
    if (this.formData.fileList === undefined || this.formData.fileList.length === 0) {
      if (this.formData.id) {
        callback();
      } else {
        callback(new Error(this.$t('pa.tip.addFile')));
      }
    } else {
      callback();
    }
  }
  get hasFile() {
    return this.formData.fileList.length === 0;
  }
  get width() {
    return this.isEn ? '653px' : '601px';
  }

  getAction() {
    return baseUrl.prev + URL_COMPONENT_BATCH_SAVE;
  }
  submitUpload() {
    const form: any = this.$refs.ruleForm;
    form.validate((valid: any) => {
      if (valid) {
        this.needFresh = true;
        this.formData.fileList = [];
        this.$emit('close', true);
      }
    });
  }
  handleRemove(file, fileList) {
    this.formData.fileList = fileList;
    this.index = fileList.length;
  }
  handlePreview(file) {
    if (!file.response.success) {
      this.$message.error(file.response.msg);
    }
  }
  handleSuccess(response, file, fileList) {
    this.index = this.index + 1;
    if (!response.success) {
      file.status = 'error';
      file.name = this.$t('pa.uploadError', [file.name]);
    }
    this.formData.fileList = fileList;
  }
  handleBeforeUpload(file) {
    if (!this.beforeUpload(file)) {
      return false;
    }
    (this.$refs.ruleForm as any).clearValidate();
  }

  closeDialog() {
    this.formData.fileList = [];
    (this.$refs.ruleForm as any).clearValidate();
    this.$emit('close', false);
  }
  beforeUpload(file) {
    const name = file.name;
    const isJar = name.indexOf('.jar') >= 0;
    const isLtSize = file.size / 1024 / 1024 < 250;
    if (!isJar) {
      this.$message.error(this.$t('pa.tip.onlyJar'));
    }
    if (!isLtSize) {
      this.$message.error(this.$t('pa.tip.size250MB'));
    }
    return isJar && isLtSize;
  }
}
</script>

<style lang="scss">
.batch-upload-dialog .el-upload-list__item.is-error .el-upload-list__item-name {
  color: #ff5353;
}
</style>
