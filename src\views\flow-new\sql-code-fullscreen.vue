<template>
  <div v-loading="loading" class="sql-code">
    <div class="sql-code-left">
      <header class="sql-code-header">
        <header-info :data="baseInfo" :online-success="true" />
        <div v-if="autoSaveDate" class="flow-code-save">
          <span :class="[isAutoSaving ? 'el-icon-refresh' : 'el-icon-success']"></span>
          <span class="flow-code-save__text">{{ saveText }}</span>
        </div>
        <div>
          <el-tooltip
            v-for="el in handerBtns"
            :key="el.name"
            v-access="el.access"
            effect="light"
            placement="bottom"
            :content="el.content"
          >
            <i :class="el.icon" @click="handleBtnClick(el.name)"></i>
          </el-tooltip>
        </div>
      </header>
      <div class="sql-code-left-content">
        <data-set slot="dataSet" :flow-status="flowStatus" @click="handleDataSetClick" />
        <flow-sql-code
          ref="sqlCode"
          style="width: calc(100% - 300px)"
          :content="content"
          :flow-id="flowId"
          :flow-status="flowStatus"
          :is-running="isRunning"
          :is-full-screen="true"
          @content-change="handleContentChange"
          @real-test="handleRealTest"
        />
        <!-- 会话启动的时候展示该图标 -->
        <div v-if="isRunning" class="test-reslt--oper" @click="showTestResultDrawer = true">
          <el-tooltip effect="light" :content="$t('pa.realTestResult')" placement="bottom">
            <i class="iconfont icon-ceshijieguo"></i>
          </el-tooltip>
        </div>
        <!-- 真实输入测试抽屉 -->
        <sql-test-result-drawer
          v-if="showTestResultDrawer"
          full-screen
          :visible.sync="showTestResultDrawer"
          :is-running="isRunning"
          :test-log="testLog"
          :active-name="testResultActiveTab"
          :column-data="testResultColumns"
          :table-data="testResultTableData"
        />
      </div>
    </div>
    <div class="sql-code-right-bar">
      <span v-access="'PA.FLOW.FLOW_MGR.SESSION'" @click="showSession">{{ $t('pa.flow.session') }}</span>
      <span @click="showCodeSnippet">{{ $t('pa.flow.codeLibrary') }}</span>
    </div>
    <!-- 模拟输入测试 -->
    <flow-test
      v-if="showFlowTest"
      class="flow-test"
      :show.sync="showFlowTest"
      :data="rawData"
      :sql-table="sqlTable"
      :is-change="isChange"
    />
    <session-drawer
      v-if="sessionVisible"
      :show.sync="sessionVisible"
      full-screen
      :readonly="disabled"
      @sessionConn="handleSessionConn"
      @sessionStopConn="handleSessionStopConn"
    />
    <code-snippet-drawer
      v-if="snippetLibraryVisible"
      full-screen
      :show.sync="snippetLibraryVisible"
      @update="handleCodeDrawerUpdate"
    />
  </div>
</template>
<script lang="ts">
import { compileFlow, getFlowById, getSqlSaveInterval, preCompileFlow, updateFlow } from '@/apis/flowNewApi';
import { Vue, Component, Ref } from 'vue-property-decorator';
import HeaderInfo from './design/header-info.vue';
import FlowSqlCode from './design/page-content/sql-code/index.vue';
import DataSet from './design/data-set/index.vue';
import { JobStatus, MsgType, IColumn } from './design/interface';
import FlowStore from './design/store/flow-store';
import FlowTest from './design/page-content/test/index.vue';
import { transformLineErrorInfo } from './design/utils';
import BeforeLeave from './design/mixins/before-leave';
import SqlRealTest from './design/mixins/sql-real-test';
import { getSqlFields } from '@/apis/flowTestApi';
import SessionDrawer from './design/page-content/session-drawer.vue';
import CodeSnippetDrawer from './design/page-content/code-snippet-drawer/index.vue';
import dayjs from 'dayjs';
@Component({
  components: {
    HeaderInfo,
    FlowSqlCode,
    DataSet,
    FlowTest,
    SessionDrawer,
    SqlTestResultDrawer: () => import('./design/page-content/sql-test-result-drawer.vue'),
    CodeSnippetDrawer
  },
  mixins: [BeforeLeave, SqlRealTest]
})
export default class SqlCodeFullScreen extends Vue {
  @Ref('sqlCode') sqlCode: any;
  loading = false;
  onlineSuccess = true;
  flowStore: any = null;
  flowStatus: keyof typeof JobStatus = JobStatus.DEV;
  content: any = ''; // 流程源码内容
  baseInfo: any = {}; // 流程基本信息
  staticData: any = {};
  rawData: any = {};
  handerBtns = [
    {
      name: 'flowSave',
      content: this.$t('pa.flow.save'),
      access: 'PA.FLOW.FLOW_MGR.ADD',
      icon: `iconfont icon-baocun`
    },
    {
      name: 'flowCompile',
      content: this.$t('pa.flow.compile'),
      access: 'PA.FLOW.FLOW_MGR.COMPILE',
      icon: `iconfont icon-bianyi`
    },
    {
      name: 'flowTest',
      content: this.$t('pa.flow.mockTest'),
      access: 'PA.FLOW.FLOW_MGR.TEST',
      icon: `iconfont icon-ceshi`
    }
  ];
  // 代码内容是否发生变更
  isChange = false;
  // 用于自动保存处理是否变更
  isChangeForAutoSaving = false;
  // 控制测试代码显隐
  showFlowTest = false;
  sqlTable: any = {};
  sessionVisible = false; /* SQL流程测试相关 start */
  showTestResultDrawer = false;
  testResultActiveTab = 'RESULT';
  testResultColumns: IColumn[] = [{ label: '', value: '' }];
  testResultTableData: any = [];
  ws: any;
  testLog: any = [];
  isRunning = false; /* SQL流程测试相关 end */
  snippetLibraryVisible = false;

  // 是否在自动保存中
  isAutoSaving = false; // 是否在自动保存中
  autoSaveDate = ''; // 自动保存的时间
  saveTimer: number | null = null;
  get flowId() {
    return this.$route.query.flowId;
  }
  get disabled() {
    return this.flowStatus !== JobStatus.DEV;
  }
  get saveText() {
    return this.isAutoSaving ? this.$t('pa.flow.autoSaving') : this.$t('pa.flow.autoSaved') + this.autoSaveDate;
  }
  created() {
    // 获取当前流程信息
    this.getCurFlow();
    // 注册自动保存的轮询
    this.registerInterval();
  }
  beforeDestory() {
    // 清除自动保存的定时器
    this.saveTimer && clearInterval(this.saveTimer);
  }
  async getCurFlow() {
    this.loading = true;
    const { data } = await getFlowById({ id: this.flowId, isMonitor: false });
    data.content = this.$store.getters.decrypt(data.content);
    this.flowStore = new FlowStore(data);
    this.flowStatus = this.flowStore.baseInfo.jobStatus;
    this.baseInfo = this.flowStore.baseInfo;
    this.content = this.flowStore.content;
    this.staticData = this.flowStore.staticData;
    this.loading = false;
  }
  async registerInterval() {
    if (this.disabled) return;
    const { data } = await getSqlSaveInterval();
    this.saveTimer = setInterval(() => {
      this.handleFlowSave(null, true);
    }, data * 1000);
  }
  handleContentChange(e) {
    this.isChange = Boolean(e);
    this.isChangeForAutoSaving = Boolean(e);
  }
  handleDataSetClick(data) {
    this.sqlCode.insertCode(data);
  }
  handleBtnClick(type) {
    this[type]();
  }
  showSession() {
    if (this.isChange) {
      this.$tip.warning(this.$t('pa.flow.msg1'));
      return;
    }
    this.snippetLibraryVisible = false;
    this.sessionVisible = true;
  }
  showCodeSnippet() {
    this.sessionVisible = this.showTestResultDrawer = false;
    this.snippetLibraryVisible = true;
  }
  // 保存
  async flowSave() {
    if (this.isChange) {
      const saveInfo = await this.$saveConfirm.show({
        isFlow: true,
        title: this.$t('pa.flow.msg26', [this.baseInfo.jobName])
      });
      console.log(saveInfo);
      this.handleFlowSave(saveInfo);
      return;
    }
    this.$tip.warning(this.$t('pa.flow.msg2'));
  }
  getSaveParams(saveInfo) {
    const flowData = { ...this.staticData, ...saveInfo };
    flowData.content = this.sqlCode.getContent();
    flowData.content = this.$store.getters.decrypt(flowData.content);
    delete flowData.showOverlay;
    delete flowData.properties;
    return flowData;
  }
  async handleFlowSave(saveInfo: object | null, isAutoSave = false) {
    // 自动更新时 未更改时不进行自动保存
    if (!this.isChangeForAutoSaving && isAutoSave) return;
    this.isAutoSaving = isAutoSave;
    const loading = !isAutoSave ? this.$loading({ lock: true, text: this.$t('pa.flow.msg3') }) : null;
    try {
      const params = this.getSaveParams(saveInfo);
      params.content = this.$store.getters.encrypt(params.content);
      const { success, msg, error } = await updateFlow(params, isAutoSave);
      loading && loading.close();

      if (success) {
        if (isAutoSave) {
          this.isChangeForAutoSaving = false;
          this.updateSaveTime(success);
        } else {
          this.isChange = false;
          this.getCurFlow();
        }
        this.$tip.success(msg);
        return loading && loading.close();
      }
      this.$tip.error(error);
    } catch {
      loading && loading.close();
    }
  }
  // 测试
  async flowTest() {
    if (this.isChange) {
      this.$tip.warning(this.$t('pa.flow.msg4'));
      return;
    }
    const { success, error, data, msgType } = await getSqlFields(this.flowId as any);
    if (!success) {
      if (msgType === MsgType.LINE_MESSAGE) {
        const lineErrors = JSON.parse(error || '[]');
        this.sqlCode.setCodeError(transformLineErrorInfo(lineErrors));
        return this.$tip.error(this.$t('pa.flow.msg24'));
      } else {
        return this.$tip.error(error);
      }
    } else {
      // 此处信息用外部传入主要是因为节约代码解析时间
      this.sqlTable = data;
    }
    this.rawData = { ...this.staticData, content: this.sqlCode.getContent() };
    this.showFlowTest = true;
  }
  // 编译
  async flowCompile() {
    const getParams = () => {
      const _params = { ...this.staticData, content: this.sqlCode.getContent() };
      delete _params.properties;
      return _params;
    };
    const loading = this.$loading({ text: this.$t('pa.flow.msg30') });
    try {
      const params = getParams();
      params.content = this.$store.getters.encrypt(params.content);
      const { success, error } = await preCompileFlow(params);
      if (!success) throw error;
      const { success: realSuccess, error: realError, msg, msgType, data } = await compileFlow(params);
      realSuccess && this.$message.success(msg);
      if (!realSuccess && msgType === MsgType.SIMPLE) {
        throw realError;
      }
      if (!realSuccess && msgType === MsgType.LINE_MESSAGE) {
        const lineErrors = JSON.parse(realError || '[]');
        this.sqlCode.setCodeError(transformLineErrorInfo(lineErrors));
        throw data.errorInfo;
      }
      this.sqlCode.setCodeError([]);
      loading.close();
    } catch (error: any) {
      loading.close();
      this.$message.error(error);
    }
  }
  // 代码片段库更新后
  handleCodeDrawerUpdate() {
    this.sqlCode.getAllSqlCode();
  }
  updateSaveTime(success) {
    success && (this.autoSaveDate = dayjs().format('YYYY-MM-DD HH:mm:ss'));
    this.isAutoSaving = false;
  }
}
</script>
<style scoped lang="scss">
.sql-code {
  position: relative;
  display: flex;
  width: 100%;
  height: 100vh;
  background: #fff;
  &-left {
    flex: 1;
    overflow: hidden;
  }
  &-left-content {
    display: flex;
    width: 100%;
    height: calc(100vh - 50px);
    .data-set {
      width: 300px;
      border-right: 1px solid $--bs-color-border-lighter;
      ::v-deep .drawer__container {
        top: 50px;
        bottom: 0px;
      }
    }
  }
  &-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    border-bottom: 1px solid $--bs-color-border-lighter;
    padding: 0 20px;
    & ::v-deep .iconfont {
      font-size: 15px;
      margin-left: 20px;
      color: #666666;
      cursor: pointer;
    }
  }
  &-right-bar {
    display: flex;
    align-items: center;
    width: 50px;
    height: 100vh;
    border-left: 1px solid $--bs-color-border-lighter;
    padding: 14px 0;
    color: $--bs-color-text-secondary;
    line-height: 18px;
    writing-mode: vertical-rl;
    box-sizing: border-box;
    & > span {
      margin: 0 auto;
      padding-bottom: 14px;
      border-bottom: 1px solid #f1f1f1;
      writing-mode: vertical-rl;
      cursor: pointer;
      &:not(:first-child) {
        padding-top: 14px;
      }
    }
  }
  .test-reslt--oper {
    position: absolute;
    right: 65px;
    bottom: 15px;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #fff;
    box-shadow: 0px 4px 6px 0px rgba(8, 8, 8, 0.1);
    border-radius: 50%;
    cursor: pointer;
  }
}
.flow-test {
  position: absolute;
  right: 0px;
  bottom: 0px;
  left: 300px;
}
.flow-code-save {
  display: flex;
  align-items: center;
  flex: 1;
  color: $--bs-color-text-placeholder;
  margin-left: 20px;
  .el-icon-refresh,
  .el-icon-success {
    font-size: 18px;
  }
  .el-icon-success {
    color: $--bs-color-green;
  }
  &__text {
    line-height: 18px;
    margin-left: 10px;
  }
}
</style>
