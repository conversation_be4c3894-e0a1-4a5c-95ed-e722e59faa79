<template>
  <transition name="drawer">
    <div v-if="display" v-loading="loading" class="drawer__container" :style="{ width, left }">
      <!-- header -->
      <div class="drawer-header">
        <el-tabs v-show="isTable" v-model="activeTabName">
          <el-tab-pane :label="$t('pa.data.table.detail.tableInfo')" name="tableInfo" />
          <el-tab-pane :label="$t('pa.flow.fieldInfo')" name="fieldInfo" />
        </el-tabs>
        <span v-show="!isTable">{{ $t('pa.flow.fieldInfo') }}</span>
        <i class="drawer__close el-icon el-icon-close" @click="handleCloseEvent"></i>
      </div>
      <!-- body -->
      <div ref="drawerBodyRef" class="drawer-body">
        <!-- 字段信息 -->
        <bs-table
          v-if="showFieldInfo"
          paging-front
          row-key="name"
          :page-data="pageData"
          :column-settings="false"
          :height="fieldInfo.height"
          :data="fieldInfo.tableData"
          :column-data="fieldInfo.columnData"
        >
          <template v-slot:name="{ row }">
            <div class="drawer-cell">
              <el-tooltip v-hide effect="light" placement="top" :content="row.name">
                <div class="drawer-cell__name">{{ row.name }}</div>
              </el-tooltip>
              <el-tag v-if="row.isAdvField" size="mini">{{ $t('pa.flow.hignLevel') }}</el-tag>
            </div>
          </template>
        </bs-table>
        <!-- 表信息 -->
        <table-info v-if="showTableInfo" :data="tableInfo" />
      </div>
    </div>
  </transition>
</template>

<script lang="ts">
import { Component, PropSync, Prop, Ref, Watch, Vue } from 'vue-property-decorator';
import { URL_GET_TABLE_INFO } from '@/apis/commonApi';
import { get, post } from '@/apis/utils/net';
import debounce from 'lodash/debounce';
import { GetAllParentAttr, FIELD_COLUMN_DATA, GetTableInfoData, GetFieldInfoUrl, GetFieldTableData } from './utils';
import type { FieldInfo, TableInfo } from '../../../interface/table-info-drawer';
import { getCatalogTableFields } from '@/apis/dataApi';

@Component({
  components: { TableInfo: () => import('./table-info.vue') }
})
export default class TableInfoDrawer extends Vue {
  @PropSync('show', { default: false }) display!: boolean;
  @Prop({ type: Boolean, default: false }) isTable!: boolean;
  @Prop({ default: '430px' }) width!: string;
  @Prop({ default: '' }) id!: string;
  @Prop({ default: '' }) name!: string;
  @Prop({ default: '' }) type!: string;
  @Prop({ default: '' }) left!: string;
  @Ref('drawerBodyRef') readonly drawerBody!: HTMLDivElement;

  loading = false;
  activeTabName = 'tableInfo';
  tableInfo: TableInfo = {
    tableName: '', // 表名
    tableNameCn: '', // 中文名
    businessExplain: '', // 业务口径
    serviceType: '', // 服务类型
    serviceName: '', // 服务
    serviceAddress: '', // 服务地址
    serviceLabel: '', // 数据表、topic、''
    serviceValue: '', // label 对应的值
    connectorName: '', //  连接器名称
    baseConnector: [], // 基础连接器
    advancedConnector: [], // 高级连接器
    fieldInfo: [] // 字段信息
  }; // 表信息
  fieldInfo: FieldInfo = { height: '100%', columnData: FIELD_COLUMN_DATA, tableData: [] }; // 字段信息
  watcher: any = null;
  init = debounce(this.handleInit, 300);

  get pageData() {
    return {
      pageSize: this.$store.getters.pageSize,
      currentPage: 1,
      layout: 'total,pager',
      total: this.fieldInfo.tableData.length
    };
  }
  get showFieldInfo() {
    return this.activeTabName === 'fieldInfo';
  }
  get showTableInfo() {
    return this.isTable && this.activeTabName === 'tableInfo';
  }

  @Watch('display')
  handleDisplayChange() {
    if (!this.display) return;
    this.init();
    this.$nextTick(() => {
      this.fieldInfo.height = this.drawerBody?.clientHeight - 50;
    });
  }
  @Watch('isTable', { immediate: true })
  handleIsTableChange(val) {
    if (this.watcher) this.watcher();
    this.watcher = this.$watch(val ? 'id' : 'name', (val) => this.display && val && this.init());
  }
  created() {
    document.addEventListener('click', this.handleAllClickEvent);
  }
  beforeDestroyed() {
    document.removeEventListener('click', this.handleAllClickEvent);
  }

  handleAllClickEvent(e: any) {
    if (!this.display) return;
    const classList = GetAllParentAttr(e);
    if (classList.includes('drawer__container') || classList.includes('table-view-icon')) {
      return;
    }
    this.handleCloseEvent();
  }
  async handleInit() {
    try {
      this.tableInfo = GetTableInfoData();
      this.fieldInfo.tableData = GetFieldTableData();
      this.loading = true;
      this.activeTabName = this.isTable ? 'tableInfo' : 'fieldInfo';
      this.isTable ? await this.getTableInfo() : await this.getFieldInfo();
    } finally {
      this.loading = false;
    }
  }
  /* 获取表信息 */
  async getTableInfo() {
    const { success, data, msg } = await get(URL_GET_TABLE_INFO, { tableId: this.id });
    if (!success) return this.$message.error(msg);
    this.tableInfo = GetTableInfoData(data);
    this.fieldInfo.tableData = GetFieldTableData(data?.fieldInfo);
  }
  /* 获取字段信息 */
  async getFieldInfo() {
    const resultPromise = () => {
      if (this.type.toLocaleLowerCase() === 'hive') {
        return getCatalogTableFields({ catalogId: this.id, tableName: this.name });
      } else {
        const URL = GetFieldInfoUrl(this.type, this.id, this.name);
        if (!URL) return;
        return post(URL, { search: '', pageData: null });
      }
    };
    const { success, data, msg } = await resultPromise();
    if (!success) return this.$message.error(msg);
    this.fieldInfo.tableData = GetFieldTableData(data?.tableData);
  }
  /* 处理关闭事件 */
  handleCloseEvent() {
    this.activeTabName = 'tableInfo';
    this.display = false;
    this.$emit('close');
  }
}
</script>

<style lang="scss" scoped>
$headerHeight: 40px;
.drawer {
  &__container {
    position: fixed;
    top: 144px;
    bottom: 10px;
    left: 0;
    z-index: 1999;
    overflow: hidden;
    background: #fff;
    box-sizing: border-box;
    border: 1px solid #ebebeb;
  }
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    height: $headerHeight;
    box-sizing: border-box;
    border-bottom: 1px solid #ebebeb;
    ::v-deep .el-tabs {
      &__header {
        margin: 0 !important;
      }
      &__nav {
        margin: unset !important;
      }
      &__item {
        padding: 0 30px 0 0 !important;
        height: 40px !important;
        line-height: 40px !important;
      }
      &--top {
        height: auto !important;
      }
    }
    ::v-deep .el-tab-pane {
      height: auto !important;
    }
  }
  &__close {
    cursor: pointer;
  }
  &-body {
    height: calc(100% - $headerHeight);
    overflow-y: auto;
    overflow-x: hidden;
  }
  &-cell {
    display: flex;
    align-items: center;
    &__name {
      padding-right: 8px;
      max-width: 106px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      box-sizing: border-box;
    }
  }
}
.bs-table ::v-deep .el-table {
  border-top: none;
}

@keyframes drawer-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.drawer-enter-active {
  animation: drawer-in 0.3s;
}
.drawer-leave-active {
  animation: drawer-in 0.3s reverse;
}
</style>
