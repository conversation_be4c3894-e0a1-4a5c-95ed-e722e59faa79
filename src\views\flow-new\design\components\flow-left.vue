<template>
  <div class="flow-left" :class="{ 'flow-left__us': isEn }">
    <header class="flow-left-header">
      <!-- projectName 处理projectList未请求到时显示id的问题 -->
      <bs-select
        v-access="'PA.FLOW.FLOW_MGR.FILTER'"
        :value="projectList.length ? projectId : projectName"
        :options="projectList"
        filterable
        default-first-option
        :class="['flow-left-header__select', isFocusing ? 'hasBorder' : 'noBorder']"
        @visible-change="handleVisibleChange"
        @change="(val) => $emit('project-change', val)"
      />
    </header>
    <el-tabs :value="activeTab" :class="{ 'flow-left__tabs--us': isEn }">
      <el-tab-pane :label="$t('pa.flow.mgr')" :name="TABS.FLOW">
        <slot name="flowList"></slot>
      </el-tab-pane>
      <el-tab-pane v-if="isSql" :label="$t('pa.flow.dataMap')" :name="TABS.DATA_SET" :disabled="!flowId">
        <slot name="dataSet"></slot>
      </el-tab-pane>
      <el-tab-pane v-else :label="$t('pa.flow.component')" :name="TABS.ELEMENT" :disabled="!flowId">
        <slot name="eleList"></slot>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { JobType } from '../interface';
@Component
export default class FlowLeft extends Vue {
  @Prop() flowId!: string;
  // 选中的项目
  @Prop() projectId!: string;
  @Prop({ default: JobType.PROCESSFLOW }) jobType!: JobType;

  // 项目列表
  @Prop() projectList!: any[];
  get projectName() {
    return this.$route.query.name;
  }
  // TAB配置 流程管理\流程组件\数据集（sql流程显示数据集，其他显示流程组件）
  TABS = {
    FLOW: 'flowMgr',
    ELEMENT: 'eleMgr',
    DATA_SET: 'dataSet'
  };
  activeTab = this.TABS.FLOW;
  // 下拉框是否获得焦点
  isFocusing = false;

  get isSql() {
    return this.flowId && this.jobType === JobType.FLINK_SQL;
  }
  get isDs() {
    return this.flowId && this.jobType === JobType.PROCESSFLOW;
  }
  // 下拉框显隐回调
  handleVisibleChange(val) {
    this.isFocusing = val;
  }
}
</script>

<style lang="scss" scoped>
.flow-left {
  width: 100%;
  height: calc(100vh - 106px);
  &-header {
    display: flex;
    align-items: center;
    height: 50px;
    padding: 0 10px 0 20px;
    border-bottom: 1px solid #f1f1f1;
    &__select {
      width: 100%;
      ::v-deep .el-select {
        width: 100%;
      }
    }
    .el-dropdown-link {
      color: $--bs-color-text-placeholder;
      cursor: pointer;
    }
    &__oper {
      flex: 1;
      text-align: right;
      cursor: pointer;
      i {
        color: #757d86;
      }
    }
  }
  ::v-deep .el-tabs {
    &--top {
      height: calc(100% - 50px);
    }
    &__header {
      margin: unset;
    }
    &__nav {
      margin: 0 calc((100% - 200px) / 2);
    }
    &__item {
      padding: 0 43px;
      height: 40px !important;
      line-height: 40px !important;
    }
    &__content {
      height: calc(100% - 40px);
      overflow: unset !important;
    }
  }
  ::v-deep .el-tab-pane {
    height: 100%;
    & > div {
      height: 100%;
    }
    .bs-tree__container {
      height: calc(100% - 50px);
    }
  }
  &__us {
    ::v-deep .el-tabs {
      &__item {
        padding: 0 10px;
      }
    }
  }
  &__tabs {
    &--us {
      ::v-deep .el-tabs__nav {
        margin: 0 calc((100% - 235px) / 2);
      }
    }
  }
}
.hasBorder {
  ::v-deep .el-input__inner {
    border: 1px solid #e5e5e5;
  }
}
.noBorder {
  ::v-deep .el-input__inner {
    border: none;
  }
}
</style>
