<template>
  <bs-dialog :visible.sync="visible" :before-close="closeDialog" width="60%">
    <div slot="title" class="relation-title">
      <span>{{ '引用关系(' + topic.topic + ')' }}</span>
      <bs-select
        v-if="type !== 'PULSAR'"
        v-model="relationType"
        :options="relationOptions"
        style="width: 120px"
        @change="handleRelationTypeChange"
      />
    </div>
    <bs-table
      height="400px"
      :data="tableData"
      :column-data="columnData"
      :page-data="pageData"
      :column-settings="false"
      @page-change="pageChange"
    >
      <template slot="status" slot-scope="{ row }">
        <bs-tag :color="getStatusColor(row.status)">{{ row.status }}</bs-tag>
      </template>
    </bs-table>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="exportRelationList"> 下载 </el-button>
      <el-button @click="closeDialog">关闭</el-button>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import moment from 'moment';
import {
  pulsarViewRelation,
  kafkaViewRelation,
  kafkaDownloadRelation,
  pulsarExportRelation
} from '@/apis/serviceApi';

@Component({
  name: 'ViewRelation'
})
export default class ViewRelation extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: '' }) resId!: string;
  @Prop() topic!: any;

  columnData: any = [];
  tableData: any = [];
  pageData = { pageSize: 25, currentPage: 1, total: 0 };
  relationType = 'JOB';
  relationOptions = [
    { value: 'JOB', label: '流程' },
    { value: 'TABLE', label: '表' }
  ];

  closeDialog() {
    this.$emit('update:visible', false);
  }

  get type() {
    return this.$route.query.resType;
  }

  async fetchList() {
    const params = {
      pageData: this.pageData,
      search: JSON.stringify(this.topic)
    };
    const {
      data: { tableData, columnData, pageData },
      success,
      msg
    } = await pulsarViewRelation(this.resId, params);
    if (success) {
      tableData.forEach((el) => {
        el.createTime = moment(el.createTime).format('YYYY-MM-DD HH:mm:ss');
      });
      this.tableData = tableData;

      this.columnData = columnData;
      this.pageData = pageData;
      return;
    }
    this.$message.error(msg);
  }

  // 获取引用关系
  async getRelationList() {
    const params = {
      pageData: this.pageData
    };
    const sortCreateTime = (property) => {
      return (a, b) => {
        const value1 = a[property];
        const value2 = b[property];
        return value2 - value1;
      };
    };
    const { data } = await kafkaViewRelation(this.relationType, params);
    const values: string[] = [];
    this.columnData = data.columnData.map((item) => {
      const value = item.value || item.prop;
      values.push(value);
      return {
        ...item,
        value: `${value}_${this.relationType}`
      };
    });
    data.tableData.sort(sortCreateTime('createTime'));
    // 处理表格表头数据缓存问题
    data.tableData.forEach((el) => {
      el.createTime = moment(el.createTime).format('YYYY-MM-DD HH:mm:ss');
      Object.keys(el).forEach((k) => {
        values.includes(k) && (el[`${k}_${this.relationType}`] = el[k]);
      });
    });
    this.tableData = data.tableData;
    this.pageData.total = data.pageData.total;
  }

  pageChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    this.type === 'PULSAR' ? this.fetchList() : this.getRelationList();
  }
  handleRelationTypeChange() {
    this.pageData.currentPage = 1;
    this.getRelationList();
  }

  // 导出引用关系(excel格式)
  async exportRelationList() {
    if (this.type === 'PULSAR') {
      await pulsarExportRelation(this.resId, this.topic);
    } else {
      await kafkaDownloadRelation(this.relationType, this.topic.id);
    }
  }

  getStatusColor(status: string) {
    return {
      上线: 'yellow',
      上线中: 'yellow',
      发布: 'green',
      发布中: 'green',
      开发: ''
    }[status];
  }

  created() {
    if (this.type === 'PULSAR') {
      this.fetchList();
    } else {
      this.getRelationList();
    }
  }
}
</script>

<style lang="scss" scoped>
.relation-title {
  width: calc(100% - 30px);
  display: flex;
  align-items: center;
  & > span {
    flex: 1;
    font-weight: 700;
  }
  & > span::before {
    content: ' ';
    position: relative;
    left: 0px;
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 4px;
    box-sizing: content-box;
    border-width: 3px;
    border-style: solid;
    border-color: rgb(255, 156, 0);
    border-image: initial;
    background: rgb(255, 255, 255);
    border-radius: 50%;
  }
}
</style>
