import { get, del, post, put, file } from './utils/net';
// 获取某个项目下的所有流程（带筛选项）
export const getFlowList = (data: unknown) => {
  return post('rs/pa/job/searchJob', data);
};

// 根据流程id获取流程详情
export const getFlowById = (data: unknown) => {
  return get('/rs/pa/job/findById', data);
};

// 根据id获取流程版本信息
export const getFlowVersionId = (data: unknown) => {
  return get('/rs/pa/job/getJobByJobVersionId', data);
};

//流程版本回滚
export const versionBack = (data: unknown) => {
  return get('/rs/pa/job/rollBack', data);
};

// 新建流程
export const addFlow = (data: unknown) => {
  return post('/rs/pa/job/add', data);
};
// 更新流程基本信息
export const editFlow = (data: unknown) => {
  return post('/rs/pa/job/edit', data);
};

// 更新流程
export const updateFlow = (data: unknown, isAutoSave = false) => {
  return put(`/rs/pa/job/update?isAutoSave=${isAutoSave}`, data);
};

// 资源配置-更新流程资源配置（单个）
export const updateResourceConfig = (data: any) => {
  return file('rs/pa/job/resourceConfig', data, {
    method: 'put',
    headers: { ContentType: 'multipart/form-data' }
  });
};

// 资源配置-更新流程资源配置（批量）
export const updateResList = (data: unknown) => {
  return put('/rs/pa/job/updateResList', data);
};

// 资源配置-获取集群列表
export const getFlinkList = (data: unknown) => {
  return get('/rs/pa/res/flink', data);
};

// 项目列表搜索接口
export const getProjectList = (data: unknown) => {
  return post(`/rs/pa/project/projectList`, data);
};
// 删除项目
export const deleteProject = (data: unknown) => {
  return del('/rs/pa/project/deleteById', data);
};

// 删除目录
export const delDir = (data: unknown) => {
  return del('/rs/pa/project/catalogue/deleteById', data);
};
// 批量删除流程
export const deleteFlows = (data: unknown) => {
  return del('rs/pa/job/deleteByIds', {}, { data });
};

// 获取某个项目下的所有流程（带筛选项、项目接口）
export const getFlowListBy = (data: unknown) => {
  return post('/rs/pa/job/listJob', data);
};

// 流程复制
export const batchCopy = (data: unknown) => {
  return post('/rs/pa/job/batchCopy', data);
};

// 流程移动
export const batchMove = (data: unknown) => {
  return post('/rs/pa/job/batchMove', data);
};

// 流程校验：compile\publish\test\code
export const validateComponent = (data: unknown) => {
  return post('/rs/pa/job/validateComponent', data);
};

// DS流程发布校验
export const validateComponentById = (jobId: string) => {
  return get(`/rs/pa/job/validateComponentById?jobId=${jobId}`);
};

// SQL流程发布编译校验
export const compileCodeById = (jobId: string) => {
  return get(`/rs/pa/job/compileCodeById?jobId=${jobId}`);
};

// 新建项目
export const addProject = (data: unknown) => {
  return post('/rs/pa/project/add', data);
};

// 新建目录
export const addDir = (data: unknown) => {
  return post('/rs/pa/project/catalogue/add', data);
};

// 获取项目详情
export const getProjectById = (data: unknown) => {
  return get('/rs/pa/project/findById', data);
};

// 获取目录详情
export const getDirById = (data: unknown) => {
  return get('/rs/pa/project/catalogue/findById', data);
};

// 更新项目
export const updateProject = (data: unknown) => {
  return put('/rs/pa/project/update', data);
};

// 更新目录
export const updateDir = (data: unknown) => {
  return put('/rs/pa/project/catalogue/update', data);
};

// 获取流程节点的引用关系
export const getComponentDataGlobalRef = (data: unknown) => {
  return get('/rs/pa/job/componentDataGlobalRef', data);
};

// 获取hive组件配置参数
export const getHiveInfo = ({ resId, level1 }) => {
  return get(`/rs/pa/res/hiveInfo?resId=${resId}&tableName=${level1}`);
};

// 全量搜索接口
export const getFullSearchList = (data: unknown, type: string) => {
  return post(`/rs/pa/project/fullSearch?projectSelectType=${type}`, data);
};

// 流程预编译
export const preCompileFlow = (data: unknown) => {
  return post('/rs/pa/job/preCompile', data);
};

// 流程编译
export const compileFlow = (data: unknown) => {
  return post('/rs/pa/job/compileCode', data);
};

// 流程预发布
export const prePublishFlow = (ids: unknown) => {
  return post('/rs/pa/job/prePublish', ids);
};

// 流程发布
export const publishFlow = (ids: unknown) => {
  return post('/rs/pa/job/publish', ids);
};

// 取消发布流程
export const cancelPublishFlow = (data: unknown) => {
  return post('/rs/pa/job/cancelPublish', data);
};

// 流程预启动
export const preOnlineFlow = (data: unknown) => {
  return post('/rs/pa/job/preOnline', data);
};

// 流程启动
export const onlineFlow = (data: unknown) => {
  return post('/rs/pa/job/online', data);
};

// 批模式流程启动
export const batchOnlineFlow = (data: unknown) => {
  return post('/rs/pa/job/sqlBatchJobOnline', data);
};

// 流程预停止
export const preOfflineFlow = (data: unknown) => {
  return post('/rs/pa/job/preOffline', data);
};

// 流程停止
export const offlineFlow = (force: boolean, data: unknown) => {
  return post(`/rs/pa/job/offline?force=${force}`, data);
};

// 批量操作
export const saveBatch = (data: unknown) => {
  return post('/rs/pa/batch/saveBatch', data);
};

// flink-sql 会话-获取阈值
export const getThreshold = () => {
  return get(`/rs/pa/sql/real/test/getMaximumThreshold`);
};
// flink-sql 会话-获取会话信息
export const getRealTestconfig = (data: unknown) => {
  return get(`/rs/pa/sql/real/test/findByJobId?jobId=${data}`);
};
// flink-sql 会话-更新会话
export const updateRealTestconfig = (data: unknown) => {
  return post(`/rs/pa/sql/real/test/upsert`, data);
};
// flink-sql 会话-停止会话，处理ws实例不存在的情况
export const closeRealTestSession = (data: unknown) => {
  return get(`/rs/pa/sql/real/test/closeSession?jobId=${data}`);
};

// flink-sql 会话-启动会话前进行权限校验
export const validateTest = (jobId: string) => {
  return get(`/rs/pa/sql/real/test/validate?jobId=${jobId}`);
};

// 获取字段类型
export const getDataType = () => {
  return get('/rs/pa/component/common/convertDataType');
};

// 获取flinkSql流程自动保存的时间间隔
export const getSqlSaveInterval = () => {
  return get('/rs/pa/dic/getSqlSaveInterval');
};

// 【ClickHouse】组件配置 - 获取表的字段
export const getClickhouseTableFields = (resId: string, tableName: string) => {
  return get(`/rs/pa/res/detail/clickhouse/columnComponent?resId=${resId}&subName=${tableName}`);
};

// 【Hudi】 - 获取表的字段
export const getHudiTableFields = (resId: string, tableName: string) => {
  return get(`/rs/pa/res/detail/hudi/table?resId=${resId}&subName=${tableName}`);
};

// 获取数据库类型和对应类型下的数据库服务
export const getJdbcRes = () => {
  return get('/rs/pa/res/detail/jdbc/getJdbcRes');
};

// 获取数据库下的表列表
export const getJdbcTables = (data) => {
  return get('/rs/pa/portal/getSubList/JDBC', data);
};
/* 流程运行信息 */
export const getJobRunInfo = (jobId: string | null = null, historyId: string | null = null) => {
  return get('/rs/pa/job/jobMetrics', { jobId, historyId });
};
/*获取数据定义列表 */
export const getDdList = () => {
  return get('/rs/pa/dd/get-dd-list');
};
/*获取数据定义列表 */
export const getDdDetail = (className: string) => {
  return get('/rs/pa/dd/get-dd-by-name', { className });
};
/*获取数据定义状态 */
export const getDdStatus = (jobId: string, addDdName = true) => {
  return get('/rs/pa/job/validate-dd-mapping', { jobId, addDdName });
};
/* dd校验 */
export const validateDd = (jobIdList: string[]) => {
  return post('/rs/pa/job/validate-dd-mappings', jobIdList);
};
// 获取项目下的目录树
export const getProjectTree = (data: unknown) => {
  return get('/rs/pa/project/project_tree', data);
};

// 获取所有项目及目录
export const getAllProjectTree = (data: unknown) => {
  return get('/rs/pa/project/all_project_tree_search', data);
};

export const extractJson = (jsonValue: string) => {
  return post('/rs/pa/component/extract/json', { jsonValue });
};

// 获取服务资源列表
export const getServiceResList = () => {
  return get('/rs/pa/resConf/resList');
};

// 获取批量操作列表
export const getBatchOperationList = (data: unknown) => {
  return post('/rs/pa/batch/batchList', data);
};

// 获取批量操作详情列表
export const getBatchOperationDetailList = (data: unknown) => {
  return post('/rs/pa/batch/detailList', data);
};

// 强制停止批量操作
export const killBatchOperation = (batchId: string) => {
  return get(`/rs/pa/batch/kill?batchId=${batchId}`);
};

// 取消批量操作
export const cancelBatchOperation = (batchId: string) => {
  return get(`/rs/pa/batch/cancel?batchId=${batchId}`);
};
