import Vue from 'vue';
import Vuex from 'vuex';
import userInfo from '@/store/modules/user-info';
import job from '@/store/modules/job';
import others from '@/store/modules/others';
import asset from '@/store/modules/asset';
import { UPDATE_FULLPATH_LIST, SET_LOGIN_URL, SET_TOKEN } from '@/store/event-names/mutations';

Vue.use(Vuex);
const state: any = {
  app: {
    fullPathList: [],
    environment: 'pipeace',
    loginUrl: null,
    token: ''
  }
};
export default new Vuex.Store({
  state,
  mutations: {
    [UPDATE_FULLPATH_LIST](state: IState, payload: any) {
      state.app.fullPathList = payload;
    },
    [SET_LOGIN_URL](state: IState, payload: any) {
      state.app.loginUrl = payload;
    },
    [SET_TOKEN](state: IState, payload: any) {
      state.app.token = payload;
    }
  },
  modules: { userInfo, job, asset, others }
});
