import Vue from 'vue';
import Vuex from 'vuex';
import app from './modules/app';
import job from '@/store/modules/job';
import paUser from '@/store/modules/user';
import asset from '@/store/modules/asset';

Vue.use(Vuex);
const state: any = {};
export default new Vuex.Store({
  state,
  modules: { app, paUser, job, asset },
  mutations: {
    updateTitle(...args) {
      console.log('🚀 ~ updateTitle ~ args:', args);
    }
  }
});
