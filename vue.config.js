/*
 * @Description:
 * @Author: ranran
 * @Date: 2020-01-13 10:29:00
 * @LastEditTime: 2022-0 6-28 13:43:45
 * @LastEditors: Please set LastEditors
 */
const path = require('path');
const resolve = (dir) => path.join(__dirname, dir);
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
// const target = 'http://************:10016/'; //测试环境
const target = 'http://************:32017/';

module.exports = {
  lintOnSave: 'error',
  productionSourceMap: false,
  css: {
    sourceMap: process.env.NODE_ENV === 'production' ? false : true,
    extract: false,
    loaderOptions: {
      scss: {
        // 向全局sass样式传入共享的全局变量
        prependData: `@import "@/style/bsui-variables.scss";`
      }
    }
  },
  configureWebpack: {
    cache: {
      type: 'filesystem'
    },
    plugins: [
      new MonacoWebpackPlugin({
        languages: ['java', 'javascript', 'json', 'sql']
      }),
      new CopyWebpackPlugin([
        {
          from: 'node_modules/bs-ui-pro/lib/theme-chalk/fonts',
          to: 'fonts/',
          toType: 'dir'
        },
        {
          from: 'node_modules/bs-ui-pro/lib/theme-chalk/default-index.css'
        }
      ])
    ]
  },

  chainWebpack(config) {
    config.module.rule('svg').exclude.add(resolve('src/assets/svg')).end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/svg'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end();
    if (process.env.NODE_ENV === 'production') {
      config.plugin('CompressionPlugin').use('compression-webpack-plugin', [
        {
          algorithm: 'gzip',
          test: /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i,
          threshold: 10240,
          minRatio: 0.8
        }
      ]);
    }
  },
  devServer: {
    client: {
      overlay: false
    },
    proxy: {
      '/api': {
        target,
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/api': '/pipe'
        }
      },
      '/rs': {
        target,
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/rs': '/pipe/rs'
        }
      },
      '/bs/portal': {
        target: target,
        ws: true,
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/bs/portal': '/bs/portal'
        }
      }
    }
  },
  publicPath: './',
  outputDir: 'dist/static',
  indexPath: 'pipeace.html'
};
