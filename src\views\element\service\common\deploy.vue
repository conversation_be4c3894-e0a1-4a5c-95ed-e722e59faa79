<template>
  <bs-dialog
    title="部署"
    :visible.sync="visible"
    width="40%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
  >
    <el-form
      ref="ruleForm"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
    >
      <el-form-item label="主机名称" prop="hostTitle">
        <el-select
          v-model="formData.hostTitle"
          placeholder="请选择"
          style="width: 100%"
          @change="handleHostChange"
        >
          <el-option
            v-for="item in hostList"
            :key="item.id"
            :label="hostLabelFormatter(item)"
            :value="item.title"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="根路径" prop="rootDir">
        <el-input
          v-model="formData.rootDir"
          placeholder="请输入应用根路径"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
      <el-form-item v-if="showPort" label="端口" prop="port">
        <el-input-number
          v-model="formData.port"
          placeholder="请输入应用运行端口"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="安装包" prop="pkgTitle">
        <el-select
          v-model="formData.pkgTitle"
          placeholder="请选择"
          style="width: 100%"
          @change="handlePkgChange"
        >
          <el-option
            v-for="item in pkgList"
            :key="item.id"
            :label="pkgLabelFormatter(item)"
            :value="item.title"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="安装命令">
        <el-input
          v-model="formData.installCmd"
          type="textarea"
          placeholder="请输入安装命令"
          maxlength="2048"
        />
      </el-form-item>
      <el-form-item v-if="showStartCmd" label="启动命令">
        <el-input
          v-model="formData.startCmd"
          type="textarea"
          placeholder="请输入启动命令"
          maxlength="2048"
        />
      </el-form-item>
      <el-form-item v-if="showStopCmd" label="停止命令">
        <el-input
          v-model="formData.stopCmd"
          type="textarea"
          placeholder="请输入停止命令"
          maxlength="2048"
        />
      </el-form-item>
    </el-form>
    <pre v-show="logText !== ''" style="overflow: auto; height: 200px; text-align: left">{{
      logText
    }}</pre>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button
        v-show="showSaveBtn"
        type="primary"
        :loading="loading"
        @click="submit('ruleForm', false)"
      >
        保存
      </el-button>
      <el-button
        v-show="showSaveBtn"
        type="primary"
        :loading="loading"
        @click="submit('ruleForm', true)"
      >
        保存并部署
      </el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Emit } from 'vue-property-decorator';
import {
  URL_RES_NODE_ADD,
  URL_RES_NODE_UPDATE,
  URL_RES_NODE_INSTALL,
  URL_HOST_LISTQUOTERES,
  URL_INSTALLATIONPACKAGE_LIST
} from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component
export default class Deploy extends PaBase {
  @Prop()
  visible!: boolean;
  @Prop({ default: true })
  showPort!: boolean;
  @Prop({ default: true })
  showStartCmd!: boolean;
  @Prop({ default: true })
  showStopCmd!: boolean;

  formData: any = {};
  loading = false;
  rules: any = {
    hostTitle: [{ required: true, message: '请输入主机', trigger: 'blur' }],
    port: [{ required: true, message: '请输入应用运行端口', trigger: 'blur' }],
    rootDir: [{ required: true, message: '请输入应用根路径', trigger: 'blur' }]
  };
  hostList: any[] = [];
  hostId = '';
  pkgList: any[] = [];
  pkgId = '';
  logText = '';
  stompClient: any;
  uid = '';
  showSaveBtn = true;

  hostLabelFormatter(item: any) {
    return item.title + '(' + item.ip + ')(' + item.username + ')';
  }
  pkgLabelFormatter(item: any) {
    return item.title + '(' + item.versionInfo + ')';
  }
  handleHostChange(val: any) {
    const rec = _.find(this.hostList, { title: val });
    this.formData.ip = rec.ip;
  }
  handlePkgChange(val: any) {
    const rec = _.find(this.pkgList, { title: val });
    this.$set(this.formData, 'installCmd', rec.installCmd);
    this.$set(this.formData, 'startCmd', rec.startCmd);
    this.$set(this.formData, 'stopCmd', rec.stopCmd);
    this.formData.versionInfo = rec.versionInfo;
    this.formData.pkgTitle = val;
    this.formData.pkgId = rec.id;
  }
  /**
   * 初始化，用于初始化数据和页面属性
   */
  init() {
    this.formData = {};
    this.loading = false;
    this.logText = '';
    this.showSaveBtn = true;
  }
  /**
   * 表单数据加载
   */
  loadData(data: any) {
    this.init();
    this.uid = 'test' + _.now();
    this.websocket();
    this.$nextTick(function () {
      this.formData = data;
      this.getHostList();
      this.getPkgList(data.resType);
    });
  }
  /**
   * 获取主机列表
   */
  getHostList() {
    this.doGet(URL_HOST_LISTQUOTERES, {
      params: {
        orgId: this.formData.orgId
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.hostList = [];
        this.hostList = resp.data;
      });
    });
  }
  /**
   * 获取安装包列表
   */
  getPkgList(pkgType: string) {
    this.doPost(URL_INSTALLATIONPACKAGE_LIST + '/' + pkgType, {}).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.pkgList = [];
        this.pkgList = resp.data.tableData;
      });
    });
  }
  websocket() {
    const Stomp = require('stompjs');
    const SockJS = require('sockjs-client');
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this;
    const socket = new SockJS(this.getEndpointOyzc());
    this.stompClient = Stomp.over(socket);
    this.stompClient.connect({}, function () {
      that.stompClient.subscribe(
        '/user/' + that.uid + '/queue/getResponse',
        function (response: any) {
          const json = JSON.parse(response.body);
          if (json.msg !== '') {
            that.logText = json.msg;
          }
          if (json.finished) {
            that.loading = false;
          }
        }
      );
    });
  }
  public disconnect() {
    try {
      this.stompClient.disconnect();
    } catch (e) {
      // do nothing
    }
  }
  public beforeDestroy() {
    this.disconnect();
  }
  /**
   * 表单提交
   */
  submit(formName: string, isDeploy: boolean) {
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        this.loading = true;
        if (this.formData.id) {
          this.doPut(URL_RES_NODE_UPDATE, this.formData).then((resp: any) => {
            this.parseResponse(resp, () => {
              if (isDeploy) {
                this.install();
              } else {
                this.loading = false;
                this.closeDialog(true);
              }
            });
            if (!resp.success) {
              this.loading = false;
            }
          });
        } else {
          this.doPost(URL_RES_NODE_ADD, this.formData).then((resp: any) => {
            this.parseResponse(resp, () => {
              this.formData.id = resp.data;
              if (isDeploy) {
                this.install();
              } else {
                this.loading = false;
                this.closeDialog(true);
              }
            });
            if (!resp.success) {
              this.loading = false;
            }
          });
        }
      } else {
        this.$message.error('请检查输入内容');
        return false;
      }
    });
  }

  install() {
    this.$nextTick(() => {
      this.$message.info('正在执行安装请勿关闭页面');
      this.doPost(URL_RES_NODE_INSTALL + '?sessionId=' + this.uid, this.formData).then(
        (resp: any) => {
          this.parseResponse(resp, () => {
            this.loading = false;
          });
        }
      );
    });
  }

  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  private closeDialog(isShow: boolean) {
    this.disconnect();
  }
}
</script>

<style scoped></style>
