<template>
  <pro-grid title="模式" type="info" class="modal__container">
    <!-- 添加模式 -->
    <el-button slot="operation" type="primary" size="small" @click="addNewModel">
      添加模式
    </el-button>
    <!-- 主体内容 -->
    <el-form ref="formRef" :model="formData" :disabled="disabled" class="modal__container__main">
      <!-- 数据定义 -->
      <el-form-item label="数据定义" prop="ddId" :rules="dataRule" label-width="120px">
        <el-select
          v-model="formData.ddId"
          clearable
          filterable
          size="small"
          class="modal-select"
          placeholder="请选择数据定义"
          @change="handleDdIdChange"
        >
          <el-option
            v-for="el in dataOptions"
            :key="el.value"
            :label="el.label"
            :value="el.value"
          />
        </el-select>
        <el-tooltip v-if="showTooltip" effect="light" placement="top" :content="content">
          <i class="modal-icon el-icon-warning-outline"></i>
        </el-tooltip>
      </el-form-item>
      <!-- 个体模式列表 -->
      <model-list
        :data.sync="formData"
        :all-name="allName"
        :model-list.sync="list"
        @change="handleGroupNameChange"
        @config="openModelConfigDialog"
      />
      <!-- 模式条件配置 -->
      <model-config
        v-if="showModelConfigDialog"
        :show.sync="showModelConfigDialog"
        :index="currentModelIndex"
        :data.sync="currentModelData"
        :field-list="fieldList"
        :field-type-dict="fieldTypeDict"
        :method-source-code.sync="methodSourceCode"
        @change="handleConfigChange"
      />
    </el-form>
  </pro-grid>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Watch, Vue } from 'vue-property-decorator';
import { debounce } from 'lodash';
import { get } from '@/apis/utils/net';
import { URL_GET_DDID_OPTIONS } from '@/apis/cep-api';
import elForm from 'bs-ui-pro/packages/form/index.js';
import ModelList from './list.vue';
import ModelConfig from '../../components/model-config/index.vue';

@Component({ components: { ModelList, ModelConfig } })
export default class Model extends Vue {
  @Prop({ default: () => [] }) allName!: any;
  @Prop({ default: false }) disabled!: boolean;
  @PropSync('data', { default: () => ({}) }) formData!: any;
  @PropSync('modelList', { default: () => [] }) list!: any[];
  @Ref('formRef') readonly form!: elForm;

  private dataOptions: any[] = [];
  private fieldInfoMapping = {};
  private dataRule: any = {
    required: true,
    message: '请选择数据定义',
    trigger: 'change'
  };
  private addNewModel = debounce(this.handleAddNewModel, 1200);
  /* 配置弹窗相关数据 start */
  private showModelConfigDialog = false; // 是否现在弹窗
  private currentModelIndex = 0; // 当前模式下标
  private currentModelData: any[] = []; // 当前模式配置数据
  private methodSourceCode: any = {}; // 方法源码
  private fieldList: any[] = []; // 字段列表
  private fieldTypeDict: any = {}; // 字段类型字典
  /* 配置弹窗相关数据 end */

  get showTooltip() {
    return (
      this.formData.ddId && !this.dataOptions.some(({ value }) => value === this.formData.ddId)
    );
  }
  get content() {
    return `${this.formData.ddId || '字段'}已经被删除`;
  }
  @Watch('formData.ddId')
  hanlder() {
    this.handleDdIdChange();
  }

  created() {
    this.getDataOptions();
  }

  async getDataOptions() {
    try {
      const { success, data, error } = await get(URL_GET_DDID_OPTIONS);
      if (success) {
        return this.handleDataOptions(Array.isArray(data) ? data : []);
      }
      this.$message.error(error);
    } catch (e) {
      console.log(e);
    }
  }
  handleDataOptions(data: any[] = []) {
    this.dataOptions = data.map(({ id: value, name: label, fields }: any) => {
      this.fieldInfoMapping[value] = fields;
      return { label, value };
    });
    this.handleDdIdChange();
  }
  /* 添加模式 */
  handleAddNewModel() {
    this.formData.singletonCepPatterns.push({
      singletonModeName: '',
      logicRelation: '',
      singletonCondition: []
    });
  }
  /* 处理ddId变化 */
  handleDdIdChange() {
    if (this.fieldInfoMapping[this.formData.ddId]) {
      this.generateFieldData();
    }
  }
  /* 生成字段相关数据 */
  generateFieldData() {
    const data = this.fieldInfoMapping[this.formData.ddId] || [];
    this.fieldList = data.map(({ name }: any) => name);
    this.fieldTypeDict = data.reduce((pre, { type, name: label }: any) => {
      if (!pre[type]) pre[type] = [];
      pre[type].push({ label, value: label, type });
      return pre;
    }, {});
  }
  /* 处理模式组名称变化 */
  handleGroupNameChange(index: number, label: string) {
    this.$set(this.list, index, {
      label,
      value: label,
      type: 'model',
      disabled: false
    });
  }
  /* 打开模式配置弹窗 */
  openModelConfigDialog(index: number) {
    if (!this.formData.ddId) return this.$message.error('请选择数据定义');
    if (this.fieldList.length < 1) return this.$message.error('该数据定义无字段列表，请重新选择');
    this.currentModelIndex = index;
    this.currentModelData = this.formData.singletonCepPatterns[index];
    this.showModelConfigDialog = true;
  }
  /* 处理配置变化事件 */
  handleConfigChange(data: any, index: number) {
    this.$set(this.formData.singletonCepPatterns, index, data);
    this.validate();
  }
  validate() {
    return this.form.validate();
  }
}
</script>

<style lang="scss" scoped>
.modal {
  &__container {
    &__main {
      width: 100%;
      // height: 600px;
      // overflow-x: hidden;
      // overflow-y: auto;
    }
  }
  &-select {
    width: 450px;
  }
  &-icon {
    display: inline-block;
    margin-left: 8px;
    width: 16px;
    height: 16px;
    font-weight: bolder;
    color: #ff5353;
  }
}
</style>
