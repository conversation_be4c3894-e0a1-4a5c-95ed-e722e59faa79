<template>
  <bs-dialog
    v-loading="loading"
    width="90%"
    :title="$t('pa.codeCompare')"
    :visible.sync="dipaly"
    class="compare-dialog"
    :append-to-body="false"
    :footer-visible="false"
    :element-loading-text="$t('pa.tip.codeLoading')"
  >
    <div class="compare-content">
      <!-- 源码比对-头部信息 -->
      <div class="compare-header">
        <p class="compare-header__left">{{ $t('pa.selectVersion', [currentVersionData.jobVersion]) }}</p>
        <div class="compare-header__right">
          <span>{{ $t('pa.compareVersion', ['']) }}</span>
          <el-select
            v-model="rightJobVersion"
            :class="{ 'compare-header__right--us': isEn }"
            :placeholder="$t('pa.tip.compareVersion')"
            @change="handleVersionChange"
          >
            <el-option v-for="el in validVersionList" :key="el.id" :label="el.jobVersion" :value="el.jobVersion" />
          </el-select>
        </div>
      </div>
      <!-- 源码比对-主体内容 -->
      <bs-code :value="code" language="sql" :read-only="true" :is-diff="true" :operatable="false" />
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Vue, Component, PropSync, Prop } from 'vue-property-decorator';
import { URL_GET_HIS_SOURCE_CODE } from '@/apis/commonApi';
import { post } from '@/apis/utils/net';
@Component
export default class CodeVersionCompareDialog extends Vue {
  // 弹窗显隐
  @PropSync('visible') dipaly!: boolean;
  // 选中的版本信息
  @Prop({ default: '' }) currentVersionData!: any;
  // 当前流程下的所有版本信息
  @Prop({ default: [] }) versionData!: any;
  // 版本比对HTML代码
  private compareHTML = null;
  private loading = true;
  // 左侧当前版本代码
  private leftCurrentCode = '';
  // 右侧选中版本代码
  private rightSelectedCode = '';
  // 右侧选中版本
  private rightJobVersion = null;
  // 所有版本信息深拷贝
  private getRightCodeSuccess = false;

  get code() {
    return [this.leftCurrentCode, this.rightSelectedCode];
  }
  get validVersionList() {
    return this.versionData.filter((el) => el.jobVersion !== this.currentVersionData.jobVersion);
  }
  async created() {
    await this.getCurrentVersionCode();
  }
  async getCurrentVersionCode() {
    const { success, data, error } = await post(URL_GET_HIS_SOURCE_CODE, this.currentVersionData);
    this.loading = false;
    if (success) {
      return (this.leftCurrentCode = this.$store.getters.decrypt(data));
    }
    this.$message.error(this.$t('pa.selectVersion', [error]));
    this.rightSelectedCode = '';
  }

  // 监听版本号变化
  async handleVersionChange(version: string) {
    this.getRightCodeSuccess = false;
    this.loading = true;

    const item = this.validVersionList.find((el) => el.jobVersion === version);
    if (item) {
      try {
        const { success, data, error } = await post(URL_GET_HIS_SOURCE_CODE, item);
        if (success) {
          this.rightSelectedCode = this.$store.getters.decrypt(data);
          this.getRightCodeSuccess = true;
          if (this.rightSelectedCode === this.leftCurrentCode) {
            this.$message.warning(this.$t('pa.tip.sameCode', [this.currentVersionData.jobVersion, this.rightJobVersion]));
          }
          this.loading = false;
          return;
        }
        this.$tip.error(this.$t('pa.compareVersion', [error]));
        this.rightSelectedCode = '';
        this.loading = false;
      } catch {
        this.loading = false;
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.compare {
  &-dialog {
    display: flex;
    justify-content: center;
    align-items: center;
    ::v-deep .el-dialog {
      margin-top: 0 !important;
      &__body {
        height: 600px;
        overflow: hidden;
      }
    }
  }
  &-content {
    height: 100%;
    overflow: hidden;
    // background: pink;
    ::v-deep .monaco-diff-editor {
      .mtk4 {
        color: #008000;
      }
      .mtk8 {
        color: #aaaaaa;
      }
    }
  }
  &-header {
    display: flex;
    align-items: center;
    padding-bottom: 8px;
    &__left {
      flex: 1;
    }
    &__right {
      flex: 1;
      display: inline-flex;
      align-items: center;
      &--us {
        width: 285px;
      }
    }
  }
  &-main {
    height: calc(100% - 40px);
    border: 2px solid #e1e1e1;
    overflow-x: hidden;
    overflow-y: auto;
    ::v-deep .d2h {
      &-moved-tag {
        display: none;
      }
      &-file &-ins {
        background-color: #d6f0ff;
        border-color: #80cefe;
      }
      &-code {
        &-line ins,
        &-side {
          &-linenumber {
            position: relative;
          }
          &-line {
            ins {
              background-color: #80cefe;
            }
          }
        }
      }
      &-file {
        &-header {
          display: none;
        }
        &-wrapper {
          border: 0;
        }
      }
    }
  }
}
</style>
