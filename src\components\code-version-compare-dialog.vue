<template>
  <bs-dialog
    v-loading="loading"
    width="90%"
    title="源码比对"
    :visible.sync="dipaly"
    class="compare-dialog"
    :append-to-body="false"
    :footer-visible="false"
    element-loading-text="源码加载中"
  >
    <div class="compare-content">
      <!-- 源码比对-头部信息 -->
      <div class="compare-header">
        <p class="compare-header__left">选中版本：{{ currentVersionData.jobVersion }}</p>
        <div class="compare-header__right">
          <span>对比版本：</span>
          <el-select
            v-model="rightJobVersion"
            placeholder="请选择需要对比的版本"
            @change="handleVersionChange"
          >
            <el-option
              v-for="el in validVersionList"
              :key="el.id"
              :label="el.jobVersion"
              :value="el.jobVersion"
            />
          </el-select>
        </div>
      </div>
      <!-- 源码比对-主体内容 -->
      <div class="compare-main" v-html="compareHTML"></div>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Vue, Component, PropSync, Prop } from 'vue-property-decorator';
import { URL_GET_HIS_SOURCE_CODE } from '@/apis/commonApi';
import { post } from '@/apis/utils/net';
import 'diff2html/bundles/css/diff2html.min.css';
const jsDiff = require('diff');
const Diff2html = require('diff2html');
@Component
export default class CodeVersionCompareDialog extends Vue {
  // 弹窗显隐
  @PropSync('visible') dipaly!: boolean;
  // 选中的版本信息
  @Prop({ default: '' }) currentVersionData!: any;
  // 当前流程下的所有版本信息
  @Prop({ default: [] }) versionData!: any;
  // 版本比对HTML代码
  private compareHTML = null;
  private loading = true;
  // 左侧当前版本代码
  private leftCurrentCode = '';
  // 右侧选中版本代码
  private rightSelectedCode = '';
  // 右侧选中版本
  private rightJobVersion = null;
  // 所有版本信息深拷贝
  private getRightCodeSuccess = false;

  get validVersionList() {
    return this.versionData.filter((el) => el.jobVersion !== this.currentVersionData.jobVersion);
  }
  async created() {
    await this.getCurrentVersionCode();
    this.compareCode();
  }
  async getCurrentVersionCode() {
    const { success, data, error } = await post(URL_GET_HIS_SOURCE_CODE, this.currentVersionData);
    if (success) return (this.leftCurrentCode = data);
    this.$message.error(`选中版本：${error}`);
    this.rightSelectedCode = '';
  }
  compareCode() {
    const compareResult = jsDiff.createTwoFilesPatch(
      this.rightJobVersion !== null ? `当前版本号：${this.currentVersionData.jobVersion}` : '',
      this.rightJobVersion !== null ? `选中版本号：${this.rightJobVersion}` : '',
      this.leftCurrentCode,
      this.rightSelectedCode,
      '',
      '',
      {
        context: 100000
      }
    );
    // 比对结果渲染为html代码
    const diffJson = Diff2html.parse(compareResult);
    if (
      Array.isArray(diffJson[0].blocks) &&
      diffJson[0].blocks.length === 0 &&
      this.rightJobVersion &&
      this.getRightCodeSuccess
    ) {
      this.$message.warning(
        `版本号${this.currentVersionData.jobVersion}与选中版本号${this.rightJobVersion}源码一致`
      );
    }
    this.compareHTML = Diff2html.html(diffJson, {
      drawFileList: false,
      matching: 'none',
      outputFormat: 'side-by-side'
    });
    this.loading = false;
  }

  // 监听版本号变化
  async handleVersionChange(version: string) {
    this.getRightCodeSuccess = false;
    this.loading = true;

    const item = this.validVersionList.find((el) => el.jobVersion === version);
    if (item) {
      try {
        const { success, data, error } = await post(URL_GET_HIS_SOURCE_CODE, item);
        if (success) {
          this.rightSelectedCode = data;
          this.getRightCodeSuccess = true;
          this.compareCode();
          return;
        }
        this.$tip.error(`对比版本：${error}`);
        this.rightSelectedCode = '';
        this.loading = false;
      } catch {
        this.loading = false;
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.compare {
  &-dialog {
    display: flex;
    justify-content: center;
    align-items: center;
    ::v-deep .el-dialog {
      margin-top: 0 !important;
      &__body {
        height: 600px;
        overflow: hidden;
      }
    }
  }
  &-content {
    height: 100%;
    overflow: hidden;
    // background: pink;
  }
  &-header {
    display: flex;
    align-items: center;
    padding-bottom: 8px;
    &__left {
      flex: 1;
    }
    &__right {
      flex: 1;
      display: inline-flex;
      align-items: center;
    }
  }
  &-main {
    height: calc(100% - 40px);
    border: 2px solid #e1e1e1;
    overflow-x: hidden;
    overflow-y: auto;
    ::v-deep .d2h {
      &-moved-tag {
        display: none;
      }
      &-file &-ins {
        background-color: #d6f0ff;
        border-color: #80cefe;
      }
      &-code {
        &-line ins,
        &-side {
          &-linenumber {
            position: relative;
          }
          &-line {
            ins {
              background-color: #80cefe;
            }
          }
        }
      }
      &-file {
        &-header {
          display: none;
        }
        &-wrapper {
          border: 0;
        }
      }
    }
  }
}
</style>
