<template>
  <bs-dialog
    :title="`${title || ''}`"
    size="large"
    append-to-body
    :visible.sync="display"
    class="config-dialog"
    :before-close="closeDialog"
    @confirm="handleConfirm"
  >
    <div class="batch__btns">
      <el-button @click="deleteData">{{ $t('pa.flow.del') }}</el-button>
      <el-button type="primary" @click="addNewData">{{ $t('pa.flow.new1') }}</el-button>
    </div>
    <el-form ref="configForm" :model="configForm">
      <bs-table
        stripe
        :border="true"
        :data="configForm.tableData"
        :column-data="columnData"
        :column-settings="false"
        :cell-class-name="'select-cell'"
        :show-overflow-tooltip="false"
        size="mini"
        selection
        :checked-rows="selectedData"
        @selection-change="handleSelectionChange"
      >
        <template slot="indicatorName" slot-scope="scope">
          <el-form-item
            :prop="'tableData.' + scope.$index + '.indicatorName'"
            :rules="rules['indicatorName']"
            class="batch__formItem"
          >
            <el-tooltip
              effect="light"
              placement="top"
              :content="scope.row.indicatorName"
              :disabled="scope.row.isEditor || !scope.row.indicatorName"
            >
              <el-select
                v-model="scope.row.indicatorName"
                filterable
                :disabled="!scope.row.isEditor"
                :placeholder="$t('pa.flow.placeholder21')"
                @change="handleChange($event, scope.$index)"
              >
                <el-option
                  v-for="item in dimensionalityData"
                  :key="item"
                  :label="item"
                  :value="item"
                  :style="optionsStyle"
                />
              </el-select>
            </el-tooltip>
          </el-form-item>
        </template>
        <template slot="indicatorId" slot-scope="scope">
          <el-form-item
            :prop="'tableData.' + scope.$index + '.indicatorId'"
            :rules="rules['indicatorId']"
            class="batch__formItem"
          >
            <el-tooltip
              effect="light"
              placement="top"
              :content="scope.row.indicatorId"
              :disabled="scope.row.isEditor || !scope.row.indicatorId"
            >
              <el-input
                v-model="scope.row.indicatorId"
                :disabled="!scope.row.isEditor"
                maxlength="120"
                :placeholder="$t('pa.flow.placeholder0')"
              />
            </el-tooltip>
          </el-form-item>
        </template>
        <template slot="udf" slot-scope="{ row }">
          <el-tooltip effect="light" placement="top" :content="row.udf" :disabled="row.isEditor || !row.udf">
            <el-select v-model="row.udf" :disabled="!row.isEditor" filterable :placeholder="$t('pa.flow.placeholder21')">
              <el-option v-for="item in udfData" :key="item" :label="item" :value="item" :style="optionsStyle" />
            </el-select>
          </el-tooltip>
        </template>
        <template slot="deadline" slot-scope="{ row }">
          <el-tooltip effect="light" placement="top" :content="row.deadline" :disabled="row.isEditor || !row.deadline">
            <el-select v-model="row.deadline" :disabled="!row.isEditor" filterable clearable>
              <el-option
                v-for="item in fieldList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :style="optionsStyle"
              />
            </el-select>
          </el-tooltip>
        </template>
        <template slot="timeToRollForward" slot-scope="{ row }">
          <el-input-number
            v-model="row.timeToRollForward"
            :disabled="!row.isEditor"
            controls-position="right"
            number
            clearable
            :min="0"
            style="width: 90px"
          />
          <el-select
            v-model="row.unit"
            :disabled="!row.isEditor"
            style="width: 65px; margin-left: 5px"
            :placeholder="$t('pa.flow.placeholder21')"
          >
            <el-option v-for="item in unitOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </template>
        <template slot="operate" slot-scope="{ $index, row }">
          <el-button v-for="el in operateList" :key="el.name" type="text" @click="handleClick(el.name, $index, row)">
            {{ el.label }}
          </el-button>
        </template>
      </bs-table>
    </el-form>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Vue, Prop, PropSync } from 'vue-property-decorator';
import * as _ from 'lodash';
import { get } from '@/apis/utils/net';
import i18n from '@/i18n';

@Component({ components: {} })
export default class LoConfig extends Vue {
  @Prop() title!: string;
  @Prop({ default: () => [] }) fieldList!: any[];
  @Prop({ default: () => ({}) }) formData!: any;
  @PropSync('show', { default: false }) display!: boolean;
  private udfData: any = [];
  private dimensionalityData: any = {};
  private unitOptions = [
    i18n.t('pa.flow.s'),
    i18n.t('pa.flow.m'),
    i18n.t('pa.flow.h'),
    i18n.t('pa.flow.d'),
    i18n.t('pa.flow.mm'),
    i18n.t('pa.flow.y')
  ];
  private columnData = [
    {
      label: this.$t('pa.flow.indicatorName'),
      value: 'indicatorName',
      formProps: {
        rules: { required: true, message: this.$t('pa.flow.msg106'), trigger: 'blur' }
      }
    },
    {
      label: this.$t('pa.flow.indicatorId'),
      value: 'indicatorId',
      formProps: {
        rules: { required: true, message: this.$t('pa.flow.msg107'), trigger: 'blur' }
      }
    },
    {
      label: 'UDF',
      value: 'udf'
    },
    {
      label: this.$t('pa.flow.deadline'),
      value: 'deadline',
      width: 140
    },
    {
      label: this.$t('pa.flow.timeToRollForward'),
      value: 'timeToRollForward',
      width: 190
    },
    {
      label: this.$t('pa.action.action'),
      value: 'operate',
      width: 140
    }
  ];
  private operateList: any[] = [
    {
      label: this.$t('pa.flow.del'),
      name: 'delete'
    },
    {
      label: this.$t('pa.flow.edit'),
      name: 'editor'
    },
    {
      label: this.$t('pa.flow.copy'),
      name: 'copy'
    }
  ];
  private rules: any = {
    indicatorId: {
      required: true,
      validator: this.validateId,
      trigger: 'blur'
    },
    indicatorName: {
      required: true,
      message: this.$t('pa.flow.msg106'),
      trigger: 'blur'
    }
  };
  private configForm = { tableData: [] as any[] };
  private selectedData = [];
  optionsStyle = {
    maxWidth: '300px'
  };
  async created() {
    this.configForm.tableData = _.cloneDeep(this.formData['indicatorConfiguration']);
    await get('/rs/pa/streamCube/subscribed', {
      url: this.formData['serviceAddress']
    }).then((resp: any) => {
      const subscribedData = JSON.parse(resp.data);
      this.udfData = subscribedData.udf;
      const dimensionalList = subscribedData.scripts[this.formData['namespace']];
      this.dimensionalityData = dimensionalList[this.formData['dimensionality']];
    });
    if (this.configForm.tableData.length === 0) {
      this.addNewData();
    }
  }
  validateId(rule, value, callback) {
    const extraction = (value) => {
      let flag = 0;
      for (const item of this.configForm.tableData) {
        if (item.indicatorId === value) {
          flag++;
        }
      }
      return flag > 1;
    };

    if (value === '') {
      callback(new Error(this.$t('pa.flow.msg107')));
    } else if (extraction(value)) {
      callback(new Error(this.$t('pa.flow.msg108')));
    } else {
      callback();
    }
  }
  async handleConfirm() {
    try {
      await (this.$refs['configForm'] as any).validate();
      this.$emit('config', this.configForm.tableData);
      this.closeDialog();
    } catch (e) {
      console.log(e);
    }
  }
  handleSelectionChange(rows) {
    this.selectedData = rows;
  }
  // 批量删除
  deleteData() {
    if (this.selectedData.length === 0) {
      this.$message.warning(this.$t('pa.flow.msg109'));
      return;
    }
    this.$confirm(this.$t('pa.flow.delMsg'), this.$t('pa.flow.tip'), {
      confirmButtonText: this.$t('pa.flow.confirm'),
      cancelButtonText: this.$t('pa.flow.cancel'),
      type: 'warning'
    }).then(() => {
      const { selectedData, configForm } = this;
      // 全选直接清空
      if (selectedData.length === configForm.tableData.length) {
        this.configForm.tableData = [];
        return;
      }
      selectedData.forEach((item) => {
        configForm.tableData.splice(configForm.tableData.indexOf(item), 1);
      });
    });
  }
  handleChange(val: any, index: number) {
    this.configForm.tableData[index].indicatorId = val;
  }
  //操作
  handleClick(type: string, index: number, row: any) {
    const fn = this[`${type}EventHandler`];
    return typeof fn === 'function' ? fn(index, row) : null;
  }
  //复制
  copyEventHandler(index, row: any) {
    const temp = _.cloneDeep(row);
    this.configForm.tableData.forEach((item) => {
      item.isEditor = false;
    });
    temp.isEditor = true;
    this.configForm.tableData.splice(this.configForm.tableData.length, 0, temp);
    return;
  }
  //删除
  deleteEventHandler(index: number) {
    this.$confirm(this.$t('pa.flow.delMsg1'), this.$t('pa.flow.tip'));
    this.configForm.tableData.splice(index, 1);
  }
  //编辑
  editorEventHandler(index, row: any) {
    const editor = !row.isEditor;
    this.configForm.tableData.forEach((item) => {
      item.isEditor = false;
    });
    row.isEditor = editor;
  }

  addNewData() {
    this.configForm.tableData.forEach((item) => {
      item.isEditor = false;
    });
    this.configForm.tableData.push({
      isEditor: true,
      indicatorId: '',
      indicatorName: '',
      udf: '',
      deadline: '',
      unit: this.$t('pa.flow.s')
    });
  }
  closeDialog() {
    this.configForm.tableData = [];
    this.display = false;
  }
}
</script>
<style lang="scss" scoped>
.batch {
  &__btns {
    padding: 0 0 10px;
    text-align: right;
  }
  &__formItem {
    margin: 16px 0;
  }
}
</style>
