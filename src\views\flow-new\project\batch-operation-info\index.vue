<template>
  <pro-page class="batch-operation-info" :title="$t('pa.flow.batchOperationInfo')" :fixed-header="false">
    <!-- 页面头部操作区域 -->
    <div slot="operation" class="batch-operation-info__header">
      <!-- 搜索框 -->
      <bs-search
        v-model.trim="keywords"
        :placeholder="$t('pa.flow.placeholder50')"
        maxlength="30"
        class="batch-operation-info__search"
        @search="handleSearch"
      />

      <!-- 时间范围筛选 -->
      <el-date-picker
        v-model="timeRange"
        type="datetimerange"
        :start-placeholder="$t('pa.monitor.warningRule.edit.startTime')"
        :end-placeholder="$t('pa.monitor.warningRule.edit.endTime')"
        format="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss"
        class="batch-operation-info__date-picker"
        @change="handleTimeRangeChange"
      />

      <!-- 操作类型筛选 -->
      <bs-select
        v-model="searchParams.actions"
        multiple
        collapse-tags
        clearable
        :placeholder="$t('pa.flow.operationType')"
        :options="actionOptions"
        class="batch-operation-info__filter batch-operation-info__filter--action"
        @change="handleFilter"
      />

      <!-- 运行状态筛选 -->
      <bs-select
        v-model="searchParams.states"
        multiple
        collapse-tags
        clearable
        :placeholder="$t('pa.flow.runStatus')"
        :options="stateOptions"
        class="batch-operation-info__filter batch-operation-info__filter--status"
        @change="handleFilter"
      />
    </div>

    <!-- 表格内容 -->
    <div class="batch-operation-info__content">
      <bs-table
        v-loading="tableLoading"
        :data="tableData.tableData"
        class="batch-operation-info__table"
        height="calc(100vh - 285px)"
        :column-data="tableData.columnData"
        :page-data="tableData.pageData"
        row-key="id"
        @page-change="handleCurrentChange"
        @sort-change="handleSortChange"
        @refresh="refreshList"
      >
        <!-- 运行状态 -->
        <template slot="state" slot-scope="{ row }">
          <el-tag size="mini" :type="getStatusTagType(row.state)">
            {{ getStatusText(row.state) }}
          </el-tag>
        </template>

        <!-- 操作类型 -->
        <template slot="action" slot-scope="{ row }">
          <el-tag size="mini" :type="getActionTagType(row.action)">
            {{ getActionText(row.action) }}
          </el-tag>
        </template>

        <!-- 操作栏 -->
        <template slot="operation" slot-scope="{ row }">
          <el-tooltip effect="light" :content="$t('pa.action.detail')" placement="top">
            <i
              class="batch-operation-info__icon batch-operation-info__icon--view iconfont icon-chakan"
              @click="viewDetail(row)"
            ></i>
          </el-tooltip>

          <!-- 强制停止按钮 -->
          <el-tooltip effect="light" :content="$t('pa.flow.batchKill')" placement="top">
            <i
              class="batch-operation-info__icon batch-operation-info__icon--kill iconfont icon-tingzhi"
              :class="{ 'batch-operation-info__icon--disabled': !canKillBatch(row) }"
              @click="canKillBatch(row) ? handleKillBatch(row) : null"
            ></i>
          </el-tooltip>

          <!-- 取消按钮 -->
          <el-tooltip effect="light" :content="$t('pa.flow.batchCancel')" placement="top">
            <i
              class="batch-operation-info__icon batch-operation-info__icon--cancel iconfont icon-close"
              :class="{ 'batch-operation-info__icon--disabled': !canCancelBatch(row) }"
              @click="handleCancelBatch(row)"
            ></i>
          </el-tooltip>
        </template>
      </bs-table>
    </div>

    <!-- 详情弹框 -->
    <detail-dialog :visible.sync="detailDialogVisible" :batch-data="selectedBatchData" />
  </pro-page>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { getBatchOperationList, killBatchOperation, cancelBatchOperation } from '@/apis/flowNewApi';
import DetailDialog from './detail-dialog.vue';

interface IBatchSearchParams {
  startTime: string; // 时间范围开始时间 (yyyy-MM-dd HH:mm:ss)
  endTime: string; // 时间范围结束时间 (yyyy-MM-dd HH:mm:ss)
  states: string[]; // 状态集合 Array[String]
  actions: string[]; // 操作类型集合 Array[String]
  batchName: string; // 批任务名称
}

@Component({
  components: {
    DetailDialog
  }
})
export default class BatchOperationInfo extends Vue {
  keywords = '';
  tableLoading = false;
  detailDialogVisible = false;
  selectedBatchData: any = null;
  timeRange: string[] = [];
  isFirstEnter = true; // 标识是否首次进入页面

  pageSize = this.$store.getters.pageSize || 25;

  // 表格配置
  tableData: any = {
    columnData: [],
    tableData: [],
    pageData: { pageSize: this.pageSize, currentPage: 1, total: 0 }
  };

  searchParams: IBatchSearchParams = {
    startTime: '', // 时间范围开始时间 (yyyy-MM-dd HH:mm:ss)
    endTime: '', // 时间范围结束时间 (yyyy-MM-dd HH:mm:ss)
    states: [], // 状态集合 Array[String]
    actions: [], // 操作类型集合 Array[String]
    batchName: this.keywords // 批任务名称
  };

  sortData: any = { order: 'DESC', prop: 'createTime' };

  // 状态和操作类型的枚举数据
  statusEnumData: any = {};
  actionEnumData: any = {};

  // 计算属性：操作类型选项
  get actionOptions() {
    return Object.keys(this.actionEnumData).map((key) => ({
      label: this.actionEnumData[key],
      value: key
    }));
  }

  // 计算属性：状态选项
  get stateOptions() {
    return Object.keys(this.statusEnumData).map((key) => ({
      label: this.statusEnumData[key],
      value: key
    }));
  }

  created() {
    this.getBatchOperationList();
  }

  activated() {
    if (!this.isFirstEnter) {
      this.refreshList();
    } else {
      this.isFirstEnter = false;
    }
  }

  // 获取批量操作信息列表
  async getBatchOperationList(isRefresh = false) {
    try {
      this.tableLoading = true;

      const params = {
        pageData: this.tableData.pageData,
        search: this.searchParams,
        sortData: this.sortData
      };

      const { data, success, msg, error } = await getBatchOperationList(params);

      if (success) {
        const { columnData, tableData, pageData } = data;

        // 处理列配置，确保使用 value 而不是 prop
        const processedColumnData = columnData.map((col: any) => ({
          ...col,
          value: col.value || col.prop,
          showOverflowTooltip: true
        }));

        // 提取状态和操作类型的枚举数据
        const stateColumn = columnData.find((col: any) => col.value === 'state');
        const actionColumn = columnData.find((col: any) => col.value === 'action');

        if (stateColumn && stateColumn.enumData) {
          this.statusEnumData = stateColumn.enumData;
        }

        if (actionColumn && actionColumn.enumData) {
          this.actionEnumData = actionColumn.enumData;
        }

        // 检查是否已经存在操作栏，如果不存在则添加
        const hasOperationColumn = processedColumnData.some((col: any) => col.value === 'operation');
        if (!hasOperationColumn) {
          processedColumnData.push({
            label: this.$t('pa.action.action'),
            value: 'operation',
            width: 140, // 操作栏宽度
            fixed: 'right',
            showOverflowTooltip: false
          });
        }

        this.tableData = {
          columnData: processedColumnData,
          tableData,
          pageData
        };

        if (isRefresh) {
          this.$message.success(this.$t('pa.tip.refreshSuccess') as string);
        }
      } else {
        this.$message.error(error || msg);
      }

      this.tableLoading = false;
    } catch (e) {
      this.tableLoading = false;
      this.$message.error(this.$t('pa.flow.getBatchInfoFailed') as string);
    }
  }

  // 搜索处理
  handleSearch() {
    this.searchParams.batchName = this.keywords;
    (this.tableData.pageData as any).currentPage = 1;
    this.getBatchOperationList();
  }

  // 时间范围变化处理
  handleTimeRangeChange(value: string[]) {
    if (value && value.length === 2) {
      this.searchParams.startTime = value[0];
      this.searchParams.endTime = value[1];
    } else {
      this.searchParams.startTime = '';
      this.searchParams.endTime = '';
    }
    (this.tableData.pageData as any).currentPage = 1;
    this.getBatchOperationList();
  }

  // 筛选处理
  handleFilter() {
    (this.tableData.pageData as any).currentPage = 1;
    this.getBatchOperationList();
  }

  // 刷新列表
  refreshList() {
    this.getBatchOperationList(true);
  }

  // 分页处理
  handleCurrentChange(currentPage: number, pageSize: number) {
    (this.tableData.pageData as any).currentPage = currentPage;
    (this.tableData.pageData as any).pageSize = pageSize;
    this.getBatchOperationList();
  }

  // 排序处理
  handleSortChange(val: any) {
    this.sortData = {
      order: val.order === 'ascending' ? 'ASC' : 'DESC',
      prop: val.prop
    };
    this.getBatchOperationList();
  }

  // 查看详情
  viewDetail(row: any) {
    this.selectedBatchData = row;
    this.detailDialogVisible = true;
  }

  // 判断是否可以强制停止 - 只有RUNNING状态可以强制停止
  canKillBatch(row: any): boolean {
    return row.state === 'RUNNING';
  }

  // 判断是否可以取消 - 只有ACCEPTED状态可以取消
  canCancelBatch(row: any): boolean {
    return row.state === 'ACCEPTED';
  }

  // 获取状态文本
  getStatusText(status: string): string {
    if (!status) return '';
    return this.statusEnumData[status] || status;
  }

  // 获取操作类型文本
  getActionText(action: string): string {
    if (!action) return '';
    return this.actionEnumData[action] || action;
  }

  // 获取状态tag类型
  getStatusTagType(status: string): string {
    const typeMap = {
      ACCEPTED: 'primary',
      RUNNING: 'warning',
      FINISHED: 'success',
      FAILED: 'danger',
      CANCELED: 'info',
      KILLED: 'danger'
    };
    return typeMap[status] || 'info';
  }

  // 获取操作类型tag类型
  getActionTagType(action: string): string {
    const typeMap = {
      PUBLISH: 'primary',
      CANCEL_PUBLISH: 'warning',
      ONLINE: 'success',
      OFFLINE: 'danger'
    };
    return typeMap[action] || 'info';
  }

  // 强制停止批量操作
  async handleKillBatch(row: any) {
    try {
      await this.$confirm(this.$t('pa.flow.batchKillConfirm') as string, this.$t('pa.prompt') as string, {
        confirmButtonText: this.$t('pa.action.confirm') as string,
        cancelButtonText: this.$t('pa.action.cancel') as string,
        type: 'warning'
      });
      const { success, msg, error } = await killBatchOperation(row.id || row.batchId);
      if (success) {
        this.$message.success(this.$t('pa.flow.batchKillSuccess') as string);
        this.refreshList();
      } else {
        this.$message.error(error || msg || (this.$t('pa.flow.batchKillFailed') as string));
      }
    } catch (error) {
      // 用户取消或其他错误
      if (error !== 'cancel') {
        this.$message.error(this.$t('pa.flow.batchKillFailed') as string);
      }
    }
  }

  // 取消批量操作
  async handleCancelBatch(row: any) {
    if (!this.canCancelBatch(row)) {
      return;
    }

    try {
      await this.$confirm(this.$t('pa.flow.batchCancelConfirm') as string, this.$t('pa.prompt') as string, {
        confirmButtonText: this.$t('pa.action.confirm') as string,
        cancelButtonText: this.$t('pa.action.cancel') as string,
        type: 'warning'
      });
      const { msg, error, data } = await cancelBatchOperation(row.id || row.batchId);
      if (data) {
        this.$message.success(this.$t('pa.flow.batchCancelSuccess') as string);
        this.refreshList();
      } else {
        this.$message.error(error || msg || (this.$t('pa.flow.batchCancelFailed') as string));
        this.refreshList();
      }
    } catch (error) {
      // 用户取消或其他错误
      if (error !== 'cancel') {
        this.$message.error(this.$t('pa.flow.batchCancelFailed') as string);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.batch-operation-info {
  // 头部操作区域
  &__header {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
    padding: 0;
  }

  // 搜索框
  &__search {
    width: 250px;
    flex-shrink: 0;
  }

  // 日期选择器
  &__date-picker {
    width: 300px;
    flex-shrink: 0;
  }

  // 筛选器
  &__filter {
    width: 190px;
    flex-shrink: 0;
  }

  // 内容区域
  &__content {
    height: calc(100vh - 250px);
  }

  // 表格
  &__table {
    width: 100%;
  }

  // 操作图标
  &__icon {
    cursor: pointer;
    font-size: 16px;

    &--view {
      color: #409eff;

      &:hover {
        color: #66b1ff;
      }
    }

    &--kill {
      margin-left: 12px;
      color: #f56c6c;

      &:hover {
        color: #f78989;
      }
    }

    &--cancel {
      margin-left: 12px;
      color: #909399;

      &:hover {
        color: #a6a9ad;
      }
    }

    &--disabled {
      color: #c0c4cc !important;
      cursor: not-allowed !important;

      &:hover {
        color: #c0c4cc !important;
      }
    }
  }

  // 深度选择器 - 覆盖 pro-page 的默认样式
  ::v-deep .bs-pro-page__header .bs-pro-page__header-operation {
    .el-input {
      width: auto;
    }

    .bs-search {
      width: auto !important;
    }
  }
}
</style>
