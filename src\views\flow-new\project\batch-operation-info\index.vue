<template>
  <pro-page class="batch-operation-info" title="批量操作信息" :fixed-header="false">
    <!-- 页面头部操作区域 -->
    <div slot="operation" class="batch-operation-info__header">
      <!-- 时间范围筛选 -->
      <el-date-picker
        v-model="timeRange"
        type="datetimerange"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        format="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss"
        style="width: 350px"
        @change="handleTimeRangeChange"
      />

      <!-- 批操作类型筛选 -->
      <bs-select
        v-model="searchParams.batchEventType"
        clearable
        placeholder="批操作类型"
        :options="batchEventTypeOptions"
        class="batch-operation-info__filter batch-operation-info__filter--type"
        @change="handleFilter"
      />

      <!-- 批操作状态筛选 -->
      <bs-select
        v-model="searchParams.batchEventStatus"
        clearable
        placeholder="批操作状态"
        :options="batchEventStatusOptions"
        class="batch-operation-info__filter batch-operation-info__filter--status"
        @change="handleFilter"
      />
    </div>

    <!-- 表格内容 -->
    <div class="batch-operation-info__content">
      <bs-table
        v-loading="tableLoading"
        :data="tableData.tableData"
        class="batch-operation-info__table"
        height="calc(100vh - 285px)"
        :column-data="tableData.columnData"
        :page-data="tableData.pageData"
        row-key="id"
        @page-change="handleCurrentChange"
        @refresh="refreshList"
      >
        <!-- 批操作状态 -->
        <template slot="batchEventStatus" slot-scope="{ row }">
          <el-tag size="mini" :type="getStatusTagType(row.batchEventStatus)">
            {{ getStatusText(row.batchEventStatus) }}
          </el-tag>
        </template>

        <!-- 批操作类型 -->
        <template slot="batchEventType" slot-scope="{ row }">
          <el-tag size="mini" :type="getActionTagType(row.batchEventType)">
            {{ getActionText(row.batchEventType) }}
          </el-tag>
        </template>

        <!-- 操作栏 -->
        <template slot="operation" slot-scope="{ row }">
          <el-tooltip effect="light" content="查看详情" placement="top">
            <i class="iconfont icon-chakan" @click="viewDetail(row)"></i>
          </el-tooltip>
          <el-tooltip effect="light" content="强制停止" placement="top">
            <i
              class="iconfont icon-tingzhi operation-icon"
              :class="{ 'disabled-icon': !canForceStop(row) }"
              @click="canForceStop(row) ? handleForceStop(row) : null"
            ></i>
          </el-tooltip>
        </template>
      </bs-table>
    </div>

    <!-- 详情弹框 -->
    <detail-dialog :visible.sync="detailDialogVisible" :batch-data="selectedBatchData" />
  </pro-page>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { getBatchOperationList, forceStopBatchEvent } from '@/apis/flowNewApi';
import { dateFormat } from '@/common/utils';
import DetailDialog from './detail-dialog.vue';

interface IBatchSearchParams {
  batchEventType?: string; // 批操作类型
  batchEventStatus?: string; // 批操作状态
  batchEventSubmitTimeStart?: string; // 批操作提交时间开始
  batchEventSubmitTimeEnd?: string; // 批操作提交时间结束
}

@Component({
  components: {
    DetailDialog
  }
})
export default class BatchOperationInfo extends Vue {
  tableLoading = false;
  detailDialogVisible = false;
  selectedBatchData: any = null;
  timeRange: string[] = [];

  pageSize = this.$store.getters.pageSize || 25;

  // 表格配置
  tableData: any = {
    columnData: [],
    tableData: [],
    pageData: { pageSize: this.pageSize, currentPage: 1, total: 0 }
  };

  searchParams: IBatchSearchParams = {};

  // 从接口动态获取的选项数据
  batchEventTypeOptions: any[] = [];
  batchEventStatusOptions: any[] = [];

  // 标记是否已经初始化过，避免重复调用
  private isInitialized = false;

  created() {
    this.getBatchOperationList();
    this.isInitialized = true;
  }

  activated() {
    if (!this.isInitialized) {
      this.refreshList();
    }
    this.isInitialized = false;
  }

  // 获取批量操作信息列表
  async getBatchOperationList(isRefresh = false) {
    try {
      this.tableLoading = true;

      // 构建搜索参数，只传递有值的参数
      const search: any = {};
      if (this.searchParams.batchEventType) {
        search.batchEventType = this.searchParams.batchEventType;
      }
      if (this.searchParams.batchEventStatus) {
        search.batchEventStatus = this.searchParams.batchEventStatus;
      }
      if (this.searchParams.batchEventSubmitTimeStart) {
        search.batchEventSubmitTimeStart = this.searchParams.batchEventSubmitTimeStart;
      }
      if (this.searchParams.batchEventSubmitTimeEnd) {
        search.batchEventSubmitTimeEnd = this.searchParams.batchEventSubmitTimeEnd;
      }

      const params = {
        pageData: this.tableData.pageData,
        search
      };

      const { data, success, msg, error } = await getBatchOperationList(params);

      if (success) {
        const { columnData, tableData, pageData } = data;

        // 处理时间格式化（保留枚举原始值，由模板处理显示）
        const processedTableData = tableData.map((row: any) => {
          const processedRow = { ...row };
          columnData.forEach((column: any) => {
            if (
              column.dataType === 'Date' &&
              processedRow[column.value] !== undefined &&
              processedRow[column.value] !== null
            ) {
              // 将时间戳转换为日期时间格式
              processedRow[column.value] = dateFormat(processedRow[column.value]);
            }
            // 枚举值保持原始值，由模板中的slot处理显示
          });
          return processedRow;
        });

        // 设置时间列的宽度并提取枚举数据
        columnData.forEach((column: any) => {
          // 提取枚举数据用于筛选选项
          if (column.dataType === 'Enum' && column.enumData) {
            if (column.value === 'batchEventType') {
              this.batchEventTypeOptions = Object.keys(column.enumData).map((key) => ({
                label: column.enumData[key],
                value: key
              }));
            } else if (column.value === 'batchEventStatus') {
              this.batchEventStatusOptions = Object.keys(column.enumData).map((key) => ({
                label: column.enumData[key],
                value: key
              }));
            }
          }
        });

        // 添加操作列
        columnData.push({
          label: '操作',
          value: 'operation',
          width: 100,
          fixed: 'right',
          showOverflowTooltip: false
        });

        this.tableData = {
          columnData,
          tableData: processedTableData,
          pageData
        };
        isRefresh && this.$message.success('刷新成功');
      } else {
        this.$message.error(error || msg);
      }

      this.tableLoading = false;
    } catch (e) {
      this.tableLoading = false;
      this.$message.error('获取批量操作信息失败');
    }
  }

  // 时间范围变化处理
  handleTimeRangeChange(value: string[]) {
    if (value && value.length === 2) {
      // 转换为时间戳格式
      this.searchParams.batchEventSubmitTimeStart = new Date(value[0]).getTime().toString();
      this.searchParams.batchEventSubmitTimeEnd = new Date(value[1]).getTime().toString();
    } else {
      delete this.searchParams.batchEventSubmitTimeStart;
      delete this.searchParams.batchEventSubmitTimeEnd;
    }
    (this.tableData.pageData as any).currentPage = 1;
    this.getBatchOperationList();
  }

  // 筛选处理
  handleFilter() {
    // 清理空值参数
    if (!this.searchParams.batchEventType) {
      delete this.searchParams.batchEventType;
    }
    if (!this.searchParams.batchEventStatus) {
      delete this.searchParams.batchEventStatus;
    }

    (this.tableData.pageData as any).currentPage = 1;
    this.getBatchOperationList();
  }

  // 刷新列表
  refreshList() {
    this.getBatchOperationList(true);
  }

  // 分页处理
  handleCurrentChange(currentPage: number, pageSize: number) {
    (this.tableData.pageData as any).currentPage = currentPage;
    (this.tableData.pageData as any).pageSize = pageSize;
    this.getBatchOperationList();
  }

  // 查看详情
  viewDetail(row: any) {
    this.selectedBatchData = row;
    this.detailDialogVisible = true;
  }

  // 判断是否可以强制停止
  canForceStop(row: any): boolean {
    // 只有运行中状态的批操作才能强制停止
    if (row.batchEventStatus !== 'RUNNING') {
      return false;
    }

    // 还需要机构相同才能点击强制停止
    const currentUserOrgId = this.$store.state.userInfo.orgId;
    return currentUserOrgId === row.orgId;
  }

  // 强制停止批量操作
  handleForceStop(row: any) {
    this.$confirm(`该操作不可回退，请确认要强制停止该批量操作吗？`, '强制停止确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          const { success, msg, error } = await forceStopBatchEvent(row.batchEventId);
          if (success) {
            this.$message.success(msg || '强制停止成功');
            // 刷新列表
            this.getBatchOperationList();
          } else {
            this.$message.error(msg || error || '强制停止失败');
          }
        } catch (err) {
          this.$message.error('强制停止操作失败，请稍后重试');
        }
      })
      .catch(() => {});
  }

  // 获取批操作状态文本（从动态数据中获取）
  getStatusText(status: string): string {
    const statusOption = this.batchEventStatusOptions.find((option) => option.value === status);
    return statusOption ? statusOption.label : status;
  }

  // 获取批操作类型文本（从动态数据中获取）
  getActionText(action: string): string {
    const actionOption = this.batchEventTypeOptions.find((option) => option.value === action);
    return actionOption ? actionOption.label : action;
  }

  // 获取状态tag类型
  getStatusTagType(status: string): string {
    const typeMap = {
      SUBMITTED: 'primary',
      RUNNING: 'warning',
      FINISHED: 'success',
      FAILED: 'danger'
    };
    return typeMap[status] || 'info';
  }

  // 获取操作类型tag类型
  getActionTagType(action: string): string {
    const typeMap = {
      PUB: 'primary',
      DEV: 'warning',
      ONLINE: 'success',
      OFFLINE: 'danger'
    };
    return typeMap[action] || 'info';
  }
}
</script>

<style lang="scss" scoped>
.batch-operation-info {
  &__header {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
    min-height: 40px;
    padding: 0 !important;

    .el-date-editor {
      margin: 0 !important;
      width: 350px !important;
      flex-shrink: 0;
    }
  }

  // 筛选器
  &__filter {
    width: 200px !important;
    flex-shrink: 0;
    margin: 0 !important;

    ::v-deep .el-select {
      width: 100% !important;
    }

    ::v-deep .el-input {
      width: 100% !important;
    }

    ::v-deep .el-input__inner {
      width: 100% !important;
    }
  }

  &__content {
    height: calc(100vh - 250px);
  }

  &__table {
    width: 100%;
  }

  .iconfont {
    cursor: pointer;
    &.disabled-icon {
      color: #c0c4cc;
      cursor: not-allowed;
    }

    &.operation-icon {
      margin-left: 18px;
    }
  }
}

.batch-cancel-btn {
  margin-left: 12px;
  font-size: 14px;
}
</style>
