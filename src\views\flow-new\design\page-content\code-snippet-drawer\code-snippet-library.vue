<template>
  <div class="code-snippet-list">
    <!-- 操作栏 -->
    <div class="code-snippet-list__header">
      <!-- 公用SQL片段搜索栏 -->
      <bs-search
        v-if="public"
        v-model.trim="publicSnippetKeyword"
        :placeholder="$t('pa.flow.placeholder4')"
        @search="getSqlSnippetList(false)"
      />

      <!-- 新增/搜索 -->
      <div v-else class="code-snippet-list__operations">
        <div v-if="!public">
          <bs-search
            v-model.trim="privateSnippetKeyword"
            :placeholder="$t('pa.flow.placeholder4')"
            class="code-snippet-list__operations--search"
            @search="getSqlSnippetList(false)"
          />
        </div>
      </div>
      <div v-if="!public">
        <el-tooltip :content="$t('pa.flow.new1')" effect="light">
          <i class="iconfont icon-xinjianxiangmu" @click="$emit('add')"></i>
        </el-tooltip>
      </div>
      <el-tooltip :content="$t('pa.flow.refresh')" effect="light">
        <i class="iconfont icon-shuaxin" @click="debounceGetSqlSnippetList(true)"></i>
      </el-tooltip>
    </div>
    <!-- 列表区域 -->
    <ul v-if="!showEmpty" v-loading="listLoading" :style="{ height: calcListHeight }" class="code-snippet-list__content">
      <li
        v-for="(item, index) in codeSnippetList"
        :key="item.id"
        class="code-snippet-list__content--item"
        @mouseenter="handleSnippetHover(index)"
        @mouseleave="handleSnippetLeave"
        @dblclick="handleCodeCopy(item.body)"
      >
        <el-tooltip effect="light" :content="`${$t('pa.flow.codeKey')}：${item.shortCode}`" placement="bottom-start">
          <div class="code-snippet-list__content--tooltip">
            <div class="code-snippet-list__content--left">
              <div class="code-snippet-list__content--svg">
                <svg-icon class="code-snippet-list__content--icon" name="sqlSnippet" />
              </div>
              <span class="code-snippet-list__content--name">
                {{ item.name }}
              </span>
            </div>
            <div v-if="index === currentIndex" class="code-snippet-list__content--right">
              <el-tooltip effect="light" :content="$t('pa.action.view')">
                <i class="iconfont icon-chakan" @click="editViewSnippet(false, item)"></i>
              </el-tooltip>
              <el-tooltip v-if="!public" effect="light" :content="$t('pa.action.edit')">
                <i class="iconfont icon-bianji" @click="editViewSnippet(true, item)"></i>
              </el-tooltip>
            </div>
          </div>
        </el-tooltip>
      </li>
    </ul>
    <bs-empty v-else :style="{ height: calcListHeight }" :image-size="200" />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { getSqlByType } from '@/apis/sqlApi';
import VueClipboard from 'vue-clipboard2';
import debounce from 'lodash/debounce';
Vue.use(VueClipboard);
@Component
export default class CodeSnippetLibrary extends Vue {
  // 是否为公用SQL片段库
  @Prop({ type: Boolean }) public!: boolean;
  @Prop({ default: null }) data!: object | null;
  @Prop({ type: Boolean, default: false }) fullScreen!: boolean;
  // 公用SQL片段库查询关键字
  publicSnippetKeyword = '';
  // 个人SQL片段库查询关键字
  codeSnippetList = [];
  privateSnippetKeyword = '';
  // 展示暂无数据字样
  showEmpty = false;
  listLoading = false;
  // 列表计算高度
  calcListHeight = '';
  currentIndex = null;
  debounceGetSqlSnippetList = debounce(this.getSqlSnippetList, 500);

  mounted() {
    this.getHeight();
  }

  @Watch('public', { immediate: true })
  handleTabChange() {
    this.getSqlSnippetList();
  }

  // 根据屏幕大小设定列表高度
  getHeight() {
    const calcListHeight = `${document.documentElement.clientHeight - (this.fullScreen ? 175 : 276)}px`;
    this.calcListHeight = calcListHeight;
    window.onresize = () => {
      this.calcListHeight = calcListHeight;
    };
  }

  // 获取公有/个人代码库列表
  async getSqlSnippetList(isRefresh = false) {
    if (isRefresh) this.public ? (this.publicSnippetKeyword = '') : (this.privateSnippetKeyword = '');
    try {
      this.listLoading = true;
      const { data, success, msg, error } = await getSqlByType({
        type: Number(this.public),
        search: this.public ? this.publicSnippetKeyword : this.privateSnippetKeyword
      });
      if (success) {
        this.codeSnippetList = data.map((el) => {
          el.body = this.$store.getters.decrypt(el.body);
          return el;
        });
        this.showEmpty = !Boolean(this.codeSnippetList.length);
        isRefresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
        return;
      }
      this.showEmpty = true;
      this.$message.error(error || msg);
    } finally {
      this.listLoading = false;
    }
  }

  // 双击复制
  async handleCodeCopy(code) {
    this.$copyText(code);
    this.$message.success(this.$t('pa.flow.copyed'));
  }

  // 鼠标悬浮
  handleSnippetHover(index) {
    this.currentIndex = index;
  }

  // 鼠标离开
  handleSnippetLeave() {
    this.currentIndex = null;
  }

  editViewSnippet(isEdit, detail) {
    this.$emit('handle-detail', { isEdit, detail });
  }
}
</script>

<style lang="scss" scoped>
.code-snippet-list {
  .el-icon-refresh-right,
  .iconfont {
    font-size: 16px;
    cursor: pointer;
  }
  .icon-xinjianxiangmu {
    color: #377cff;
    margin-right: 16px;
  }
  .icon-sousuo {
    margin-left: 16px;
  }
  &__header {
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    padding: 0 21px;
    ::v-deep .bs-search {
      width: 312px !important;
      margin-right: 16px;
    }
  }
  &__content {
    height: calc(90vh - 200px) !important;
    overflow: scroll;
    margin-left: 22px;
    margin-top: 11px;
    padding-left: 0;
    &--svg {
      width: 38px;
      height: 38px;
      background: #f9f9f9;
      border-radius: 3px 0px 0px 3px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &--name {
      display: inline-block;
      width: 250px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      margin-left: 12px;
    }
    &--icon {
      width: 24px;
      height: 25px;
    }
    &--item {
      min-width: 330px;
      height: 40px;
      margin-bottom: 10px;
      margin-right: 25px;
      border-radius: 4px;
      border: 1px solid #f1f1f1;
    }
    &--left {
      display: flex;
      align-items: center;
    }
    &--right {
      .iconfont {
        margin-right: 10px;
      }
    }
    &--tooltip {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
    }
  }
  &__operations {
    margin-left: 7px;
    ::v-deep .bs-search {
      width: 280px !important;
    }
    &--search {
      margin-left: -7px;
    }
  }
  &__cancel {
    font-size: 14px;
    color: $--bs-color-text-placeholder;
    cursor: pointer;
    margin-left: 15px;
  }
}
</style>
