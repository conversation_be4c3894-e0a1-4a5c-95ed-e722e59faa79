<template>
  <bs-dialog size="large" :modal="false" :title="title" :visible.sync="display" :footer-visible="false" class="error-dialog">
    <div class="error-content">{{ content }}</div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import i18n from '@/i18n';

@Component
export default class ErrorDialog extends Vue {
  display = false;
  errorList: ErrorProItem[] = [];
  title = i18n.t('pa.errorDetail') as string;
  get content() {
    return [this.errorList].flat(2).reduce((pre, { errorInfo } = { errorInfo: '' }) => {
      pre += `${errorInfo}\n`;
      return pre;
    }, '');
  }
}
</script>
<style lang="scss" scoped>
.error {
  &-dialog {
    z-index: 3002 !important;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.5);
  }

  &-content {
    height: 400px;
    word-break: break-all;
    overflow: auto;
    white-space: pre-wrap;
  }
}
</style>
