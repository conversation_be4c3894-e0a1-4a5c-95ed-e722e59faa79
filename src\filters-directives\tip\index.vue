<template>
  <bs-dialog
    size="large"
    :modal="false"
    title="错误信息详情"
    :visible.sync="dispaly"
    :footer-visible="false"
    :class="{ 'error-dialog': true, 'error-dialog--noData': !hasData }"
  >
    <el-tabs v-if="hasData" v-model="activeName">
      <el-tab-pane
        v-for="el in renderList"
        :key="el[0]"
        :label="el[0]"
        :name="el[0]"
        class="error-pane"
      >
        <pre>{{ el[1] }}</pre>
      </el-tab-pane>
    </el-tabs>
    <div v-else>暂无数据</div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Watch, Vue } from 'vue-property-decorator';
@Component
export default class ErrorDialog extends Vue {
  private renderList: any[] = [];
  detail: any = {};
  dispaly = false;
  private activeName = '';

  get hasData() {
    return this.renderList.length > 0;
  }

  @Watch('detail', { deep: true, immediate: true })
  handler() {
    const result = Object.entries(this.detail || {});
    if (result[0]) {
      this.activeName = result[0][0];
    }
    this.renderList = result;
  }
}
</script>
<style lang="scss" scoped>
.error {
  &-dialog {
    z-index: 3002 !important;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.5);
    &--noData {
      ::v-deep .el-dialog__body {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 400px;
      }
    }
  }
  &-pane {
    height: 400px;
    word-break: break-all;
    overflow: auto;
    white-space: pre-wrap;
  }
}
</style>
