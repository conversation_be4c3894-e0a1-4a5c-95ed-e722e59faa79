<template>
  <div class="normal-list__content">
    <bs-table
      v-loading="tableLoading"
      :data="tableData.tableData"
      class="full-table"
      :height="checkedRows.length ? 'calc(100vh - 342px)' : 'calc(100vh - 285px)'"
      :column-data="tableData.columnData"
      :page-data="tableData.pageData"
      row-key="id"
      selection
      crossing
      @page-change="handleCurrentChange"
      @sort-change="handleSortChange"
      @refresh="handleRefresh"
      @selection-change="handleSelectionChange"
    >
      <template slot="headerOperator">
        <div class="table-header-operator">
          <el-button-group>
            <template v-for="el in buttonList">
              <el-button
                v-if="el.type === 'ElButton'"
                :key="el.label"
                size="small"
                @click="headerOperateHandler('', el.event)"
              >
                {{ el.label }}
              </el-button>
              <el-dropdown v-if="el.type === 'ElDropdown'" :key="el.label" @command="headerOperateHandler($event, el.event)">
                <el-button size="small">
                  {{ el.label }}
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item in el.options" :key="item.command" :command="item.command">
                    {{ item.text }}
                    <el-tooltip effect="light" placement="top" :content="item.content">
                      <i :class="item.icon"></i>
                    </el-tooltip>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-button-group>
        </div>
      </template>
      <div slot="jobName" slot-scope="{ row }" class="jobName-slot">
        <el-tag size="mini" effect="dark">
          <span>{{ enumData.jobType[row.jobType] }}</span>
        </el-tag>
        <el-tooltip :content="row.jobName" effect="light" placement="top">
          <div class="jobName" @click="toFlowCanvas(row)">
            {{ row.jobName }}
          </div>
        </el-tooltip>
      </div>
      <template slot="jobStatus" slot-scope="{ row }">
        <bs-tag size="mini" :color="getStatusColor(row.jobStatus)">
          <i :class="getStatusIcon(row.jobStatus)"></i>
          {{ enumData.jobStatus[row.jobStatus] }}
        </bs-tag>
      </template>
      <template slot="jobRunTimeStatus" slot-scope="{ row }">
        <div v-if="row.jobRunTimeStatus" class="jobRunTimeStatus">
          <div :class="getStatusType(row.jobRunTimeStatus)"></div>
          <span class="info" @click="toFlowMonitor(row)">
            {{ enumData.jobRunTimeStatus[row.jobRunTimeStatus] }}
          </span>
        </div>
        <div v-else>-</div>
      </template>
      <template slot="mode" slot-scope="{ row }">
        {{ enumData.mode[row.mode] }}
      </template>
      <template slot="operator" slot-scope="{ row }">
        <el-tooltip v-access="'PA.FLOW.FLOW_MGR.VIEW'" :content="$t('pa.flow.design')" effect="light">
          <i class="iconfont icon-liuchengsheji1" @click="toFlowCanvas(row)"></i>
        </el-tooltip>
      </template>
    </bs-table>

    <!-- 批量操作弹框 -->
    <bs-dialog
      :visible.sync="batchDialogVisible"
      :title="batchDialogTitle"
      width="500px"
      @confirm="handleBatchConfirm"
      @cancel="handleBatchCancel"
    >
      <el-form ref="batchForm" :model="batchForm" :rules="batchRules" :label-width="120">
        <el-form-item :label="$t('pa.flow.batchOperationName')" prop="batchName">
          <el-input
            v-model="batchForm.batchName"
            :placeholder="$t('pa.flow.batchOperationNamePlaceholder')"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item :label="$t('pa.flow.memo')" prop="memo">
          <el-input
            v-model="batchForm.memo"
            type="textarea"
            :placeholder="$t('pa.flow.memoPlaceholder')"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </bs-dialog>

    <!-- 资源配置弹框 -->
    <batch-resource-config-dialog :show.sync="resourceConfigDialogVisible" @close="handleResourceConfigClose" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Watch, Vue } from 'vue-property-decorator';
import { getFlowListBy, saveBatch } from '@/apis/flowNewApi';
import { hasPermission } from '@/utils';
import dayjs from 'dayjs';
import { removeErrorInfo, setErrorInfo } from '../../design/flow-list/service';

const BATCH_EVENT_TYPE = {
  CANCEL_PUB: 'DEV',
  PUB: 'PUB',
  ONLINE: 'ONLINE',
  OFFLINE: 'OFFLINE',
  RESTART: 'RESTART'
};
@Component({
  components: {
    BatchResourceConfigDialog: () => import('@/components/resource-config/batch-resource-config-new-dialog.vue')
  },
  directives: {
    hide: {
      inserted(el, bind, vnode) {
        if (el.clientWidth === el.scrollWidth) {
          (vnode as any).componentInstance.$destroy();
        }
      }
    }
  }
})
export default class NormalListContent extends Vue {
  @Prop() params!: any;
  @Prop() selectedProject!: string;
  @Prop() projectTree!: any[]; // 项目树数据，用于查找根目录ID
  tableLoading = true; // 初始状态为loading，避免先显示"暂无数据"
  statusFlag = false;
  currentId = '';
  currentRootId = ''; // 当前目录所在的根目录id
  jobData: any = {};

  // 批量操作弹框相关
  batchDialogVisible = false;
  batchDialogTitle = '';
  batchForm = {
    batchName: '',
    memo: ''
  };
  batchRules = {
    batchName: [
      { required: true, message: this.$t('pa.flow.batchOperationNameRequired'), trigger: 'blur' },
      { min: 1, max: 50, message: this.$t('pa.flow.batchOperationNameLength'), trigger: 'blur' }
    ]
  };

  // 当前批量操作的参数
  currentBatchOperation: {
    type: string;
    flowIds: string[];
    extraParams?: any;
  } = {
    type: '',
    flowIds: []
  };

  // 资源配置弹框相关
  resourceConfigDialogVisible = false;
  resourceConfigData: any = null;
  // 表格配置
  tableData: ITableData = {
    columnData: [{ prop: '', label: '' }],
    tableData: [],
    pageData: { pageSize: this.pageSize, currentPage: 1, total: 0 }
  };
  enumData: any = {
    jobStatus: {},
    jobRunTimeStatus: {},
    mode: {},
    jobType: {}
  };
  searchParams: any = {
    name: '',
    projectId: '',
    jobStatus: 'ALL',
    jobRunTimeStatus: 'ALL',
    mode: 'ALL',
    clusterType: 'ALL',
    jobType: 'ALL'
  };
  sortData: any = { order: 'DESC', prop: 'updateTime' };
  checkedRows: any[] = []; // 选中的行数据
  private debounceTimer: any = null;

  get pageSize() {
    return this.$store.getters.pageSize;
  }

  get buttonList() {
    // 权限过滤
    return [
      {
        type: 'ElDropdown',
        label: this.$t('pa.action.offline'),
        event: BATCH_EVENT_TYPE.OFFLINE,
        access: 'PA.FLOW.FLOW_MGR.OFFLINE',
        options: [
          {
            command: 'stop',
            text: this.$t('pa.action.offline')
          },
          {
            content: this.$t('pa.monitor.flow.action.retainTooltip'),
            icon: 'el-icon-warning-outline',
            command: 'retain',
            text: this.$t('pa.monitor.flow.action.retainOffline')
          },
          {
            command: 'force',
            text: this.$t('pa.monitor.flow.action.forceOffline')
          }
        ]
      },
      {
        type: 'ElButton',
        label: this.$t('pa.flow.cancelPublish'),
        event: BATCH_EVENT_TYPE.CANCEL_PUB,
        access: 'PA.FLOW.FLOW_MGR.CANCEL_PUBLISH'
      },
      {
        type: 'ElButton',
        label: this.$t('pa.flow.publish'),
        event: BATCH_EVENT_TYPE.PUB,
        access: 'PA.FLOW.FLOW_MGR.PUBLISH'
      },
      {
        type: 'ElButton',
        label: this.$t('pa.monitor.flow.action.restartForce'),
        event: BATCH_EVENT_TYPE.RESTART,
        access: 'PA.FLOW.FLOW_MGR.ONLINE'
      },
      {
        type: 'ElDropdown',
        label: this.$t('pa.action.online'),
        event: BATCH_EVENT_TYPE.ONLINE,
        access: 'PA.FLOW.FLOW_MGR.ONLINE',
        options: [
          {
            content: this.$t('pa.monitor.flow.action.statelessTooltip'),
            icon: 'el-icon-warning-outline',
            command: false,
            text: this.$t('pa.monitor.flow.action.statelessOnline')
          },
          {
            content: this.$t('pa.monitor.flow.action.basedLastTooltip'),
            icon: 'el-icon-warning-outline',
            command: true,
            text: this.$t('pa.monitor.flow.action.basedLastOnline')
          }
        ]
      }
    ].filter((item) => hasPermission(item.access));
  }

  mounted() {
    // 如果初始化时没有params，设置loading为false
    if (!this.params || Object.keys(this.params).length === 0) {
      this.tableLoading = false;
    }
  }

  @Watch('params', { deep: true })
  handleParamsChange(val: any) {
    this.searchParams = { ...val };
    (this.tableData.pageData as any).currentPage = 1;
    this.getFlowList();
  }

  @Watch('selectedProject')
  handleSelectedProjectChange(val: string) {
    if (val !== this.currentId) {
      this.currentId = val;
      (this.tableData.pageData as any).currentPage = 1;
    }
  }

  // 根据项目ID查找对应的根目录ID
  findRootId(projectId: string): string {
    if (!projectId || !this.projectTree) {
      return projectId;
    }

    const findInTree = (nodes: any[], targetId: string): string | null => {
      for (const node of nodes) {
        if (node.nodeId === targetId) {
          return node.rootId || node.nodeId;
        }
        if (node.children) {
          const found = findInTree(node.children, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    return findInTree(this.projectTree, projectId) || projectId;
  }

  beforeDestroy() {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
  }

  toFlowCanvas(row: any) {
    const title = row.paJob.jobName;
    if (
      (this as any).$tabNav
        .getAllTabs()
        .find((item: any) => item.title === title && item.value.split('flowId=')[1] === row.paJob.id)
    ) {
      const value = (this as any).$tabNav
        .getAllTabs()
        .find((item: any) => item.title === title && item.value.split('flowId=')[1] === row.paJob.id).value;
      localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId: row.paJob.id }));
      this.$router.push({
        path: value
      });
    } else {
      // 根据当前行的项目ID动态查找根目录ID
      const rootId = this.findRootId(row.paJob.projectId);

      this.$router.push({
        path: 'flow',
        query: {
          id: rootId,
          name: row.paJob.projectName,
          title,
          state: 'ALL',
          flowId: row.paJob.id
        }
      });
    }
  }

  toFlowMonitor(row: any) {
    if (row.jobStatus === 'DEV') {
      return;
    }
    this.$router.push({
      path: 'monitor/flow',
      query: {
        name: row.jobName,
        runStatus: row.jobRunTimeStatus
      }
    });
  }

  getStatusColor(status: string) {
    return {
      PROD: 'green',
      INPROD: '',
      PUB: '',
      INPUB: 'purple',
      DEV: 'purple'
    }[status];
  }
  getStatusType(status: string) {
    return {
      FINISHED: 'successColor',
      RUNNING: 'primaryColor',
      NONE: 'dangerColor',
      UNKNOWN: 'infoColor',
      OTHER: 'infoColor',
      FAILED: 'infoColor'
    }[status];
  }
  getStatusIcon(status: string) {
    return {
      INPUB: 'iconfont icon-shangxianzhong',
      INPROD: 'iconfont icon-shangxianzhong',
      INOFF: 'iconfont icon-shangxianzhong',
      UNPUB: 'iconfont icon-shangxianzhong'
    }[status];
  }

  async getFlowList() {
    try {
      this.tableLoading = true;
      // 处理搜索参数，将空值转换为 'ALL'
      const processedSearchParams = { ...this.searchParams };
      const fieldsToProcess = ['clusterType', 'jobRunTimeStatus', 'jobStatus', 'jobType', 'mode'];
      fieldsToProcess.forEach((field) => {
        if (!processedSearchParams[field] || processedSearchParams[field] === '') {
          processedSearchParams[field] = 'ALL';
        }
      });

      const params = {
        pageData: this.tableData.pageData,
        search: processedSearchParams,
        sortData: this.sortData
      };
      const { data, success, msg, error } = await getFlowListBy(params);
      if (success) {
        const { columnData, tableData, pageData } = data;

        columnData.push({ label: this.$t('pa.action.action'), value: 'operator', width: 110, fixed: 'right' });
        tableData.forEach((el: any) => {
          el.createTime = dayjs(el.createTime).format('YYYY-MM-DD HH:mm:ss');
          el.updateTime = dayjs(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
          el.id = el.paJob.id;
        });
        columnData[1].showOverflowTooltip = false;
        this.enumData = {
          jobStatus: columnData[2].enumData,
          jobRunTimeStatus: columnData[3].enumData,
          jobType: columnData[4].enumData,
          mode: columnData[5].enumData
        };
        columnData.splice(3, 1);

        // 使用 Vue.nextTick 来确保 DOM 更新的平滑性
        this.$nextTick(() => {
          this.tableData = {
            columnData,
            tableData,
            pageData
          };
          this.tableLoading = false;
        });
      } else {
        this.tableLoading = false;
        this.$message.error(error || msg);
      }
    } catch (e) {
      this.tableLoading = false;
    }
  }
  fetchList() {
    this.getFlowList();
  }

  handleCurrentChange(currentPage: any, pageSize: any) {
    (this.tableData.pageData as any).currentPage = currentPage;
    (this.tableData.pageData as any).pageSize = pageSize;
    this.getFlowList();
  }
  handleSortChange(val: any) {
    this.sortData = {
      order: val.order === 'ascending' ? 'ASC' : 'DESC',
      prop: val.prop
    };
    this.getFlowList();
  }

  // 处理表格选择变化
  handleSelectionChange(selection: any[]) {
    this.checkedRows = selection;
  }

  async handleRefresh() {
    await this.getFlowList();
    this.$message.success(this.$t('pa.tip.refreshSuccess') as string);
  }

  // 头部操作按钮处理
  headerOperateHandler(command: any, eventType: string) {
    if (this.checkedRows.length === 0) {
      this.$message.warning(this.$t('pa.flow.msg32') as string);
      return;
    }

    const flowIds = this.checkedRows.map((row: any) => row.id);

    switch (eventType) {
      case BATCH_EVENT_TYPE.OFFLINE:
        this.batchOffline(flowIds, command);
        break;
      case BATCH_EVENT_TYPE.CANCEL_PUB:
        this.batchCancelPublish(flowIds);
        break;
      case BATCH_EVENT_TYPE.PUB:
        this.batchPublish(flowIds);
        break;
      case BATCH_EVENT_TYPE.ONLINE:
        this.batchOnline(flowIds, command);
        break;
      case BATCH_EVENT_TYPE.RESTART:
        this.batchRestart(flowIds);
        break;
    }
  }

  // 批量停止
  async batchOffline(flowIds: string[], command: string) {
    let confirmText = '';
    let savepoint = false;
    let force = false;

    switch (command) {
      case 'retain':
        confirmText = this.$t('pa.flow.msg44', [this.$t('pa.monitor.flow.action.retainOffline'), flowIds.length]) as string;
        savepoint = true;
        break;
      case 'force':
        confirmText = this.$t('pa.flow.msg44', [this.$t('pa.monitor.flow.action.forceOffline'), flowIds.length]) as string;
        force = true;
        break;
      default:
        // 'stop'
        confirmText = this.$t('pa.flow.msg44', [this.$t('pa.action.offline'), flowIds.length]) as string;
        break;
    }

    try {
      await this.$confirm(confirmText, this.$t('pa.prompt') as string, {
        confirmButtonText: this.$t('pa.action.confirm') as string,
        cancelButtonText: this.$t('pa.action.cancel') as string,
        type: 'warning'
      });

      // 显示批量操作弹框
      this.showBatchDialog('OFFLINE', flowIds, { savepoint, force });
    } catch (error) {
      // 用户取消确认
    }
  }

  // 执行批量停止
  async executeBatchOffline(flowIds: string[], savepoint: boolean, force: boolean, batchName: string, memo: string) {
    try {
      this.tableLoading = true;

      const batchData = {
        batchName,
        jobList: flowIds,
        action: 'OFFLINE',
        memo,
        jobConfigs: {
          clusterId: '',
          savepoint: savepoint,
          force: force
        }
      };

      // 执行停止
      const result = await saveBatch(batchData);
      if (result.success) {
        this.$message.success(this.$t('pa.flow.batchOperationSuccess') as string);
        this.getFlowList();
      } else {
        this.$message.error(
          `${this.$t('pa.flow.batchOperationFailed')}：${result.error || result.msg || this.$t('pa.flow.unknownError')}`
        );
      }
    } finally {
      this.tableLoading = false;
    }
  }

  // 批量取消发布
  async batchCancelPublish(flowIds: string[]) {
    try {
      await this.$confirm(
        this.$t('pa.flow.msg44', [this.$t('pa.flow.cancelPublish'), flowIds.length]) as string,
        this.$t('pa.prompt') as string,
        {
          confirmButtonText: this.$t('pa.action.confirm') as string,
          cancelButtonText: this.$t('pa.action.cancel') as string,
          type: 'warning'
        }
      );

      // 显示批量操作弹框
      this.showBatchDialog('CANCEL_PUBLISH', flowIds);
    } catch (error) {
      // 用户取消确认
    }
  }

  // 执行批量取消发布
  async executeBatchCancelPublish(flowIds: string[], batchName: string, memo: string) {
    try {
      this.tableLoading = true;

      const batchData = {
        batchName,
        jobList: flowIds,
        action: 'CANCEL_PUBLISH',
        memo,
        jobConfigs: {
          clusterId: '',
          savepoint: false
        }
      };

      const result = await saveBatch(batchData);
      if (result.success) {
        this.$message.success(this.$t('pa.flow.batchOperationSuccess') as string);
        this.getFlowList();
      } else {
        this.$message.error(
          `${this.$t('pa.flow.batchOperationFailed')}：${result.error || result.msg || this.$t('pa.flow.unknownError')}`
        );
      }
    } finally {
      this.tableLoading = false;
    }
  }

  // 批量发布
  async batchPublish(flowIds: string[]) {
    try {
      await this.$confirm(
        this.$t('pa.flow.msg44', [this.$t('pa.flow.publish'), flowIds.length]) as string,
        this.$t('pa.prompt') as string,
        {
          confirmButtonText: this.$t('pa.action.confirm') as string,
          cancelButtonText: this.$t('pa.action.cancel') as string,
          type: 'warning'
        }
      );

      // 显示批量操作弹框
      this.showBatchDialog('PUBLISH', flowIds);
    } catch (error) {
      // 用户取消确认
    }
  }

  // 执行批量发布
  async executeBatchPublish(flowIds: string[], batchName: string, memo: string) {
    try {
      this.tableLoading = true;

      const batchData = {
        batchName,
        jobList: flowIds,
        action: 'PUBLISH',
        memo,
        jobConfigs: {
          clusterId: '',
          savepoint: false
        }
      };

      // 执行发布
      const result = await saveBatch(batchData);
      if (result.success) {
        this.$message.success(this.$t('pa.flow.batchOperationSuccess') as string);
        this.getFlowList();
      } else {
        this.$message.error(
          `${this.$t('pa.flow.batchOperationFailed')}：${result.error || result.msg || this.$t('pa.flow.unknownError')}`
        );
      }
    } finally {
      this.tableLoading = false;
    }
  }

  // 批量重启
  async batchRestart(flowIds: string[]) {
    const confirmText = this.$t('pa.flow.msg44', [this.$t('pa.monitor.flow.action.restartForce'), flowIds.length]) as string;

    try {
      await this.$confirm(confirmText, this.$t('pa.prompt') as string, {
        confirmButtonText: this.$t('pa.action.confirm') as string,
        cancelButtonText: this.$t('pa.action.cancel') as string,
        type: 'warning'
      });

      // 显示批量操作弹框
      this.showBatchDialog('RESTART', flowIds, { savepoint: true });
    } catch (error) {
      // 用户取消确认
    }
  }

  // 检查所有流程是否都已配置加工引擎
  checkAllFlowsConfigured(flowIds: string[]): boolean {
    const selectedFlows = this.checkedRows.filter((row: any) => flowIds.includes(row.paJob.id));
    return selectedFlows.every((row: any) => row.paJob.properties && Object.keys(row.paJob.properties).length > 0);
  }

  // 批量启动
  async batchOnline(flowIds: string[], fromLastCheckpoint: boolean) {
    const confirmText = fromLastCheckpoint
      ? this.$t('pa.flow.msg44', [this.$t('pa.monitor.flow.action.basedLastOnline'), flowIds.length])
      : this.$t('pa.flow.msg44', [this.$t('pa.monitor.flow.action.statelessOnline'), flowIds.length]);

    try {
      await this.$confirm(confirmText as string, this.$t('pa.prompt') as string, {
        confirmButtonText: this.$t('pa.action.confirm') as string,
        cancelButtonText: this.$t('pa.action.cancel') as string,
        type: 'warning'
      });

      // 显示批量操作弹框
      this.showBatchDialog('ONLINE', flowIds, { fromLastCheckpoint });
    } catch (error) {
      // 用户取消确认
    }
  }

  // 执行批量启动
  async executeBatchOnline(flowIds: string[], fromLastCheckpoint: boolean, batchName: string, memo: string) {
    try {
      this.tableLoading = true;

      const batchData = {
        batchName,
        jobList: flowIds,
        action: 'ONLINE',
        memo,
        jobConfigs: {
          clusterId: '',
          savepoint: fromLastCheckpoint,
          force: false
        }
      };

      // 执行启动
      const result = await saveBatch(batchData);
      if (result.success) {
        this.$message.success(this.$t('pa.flow.batchOperationSuccess') as string);
        this.getFlowList();
      } else {
        this.$message.error(
          `${this.$t('pa.flow.batchOperationFailed')}：${result.error || result.msg || this.$t('pa.flow.unknownError')}`
        );
      }
    } finally {
      this.tableLoading = false;
    }
  }

  // 执行批量重启
  async executeBatchRestart(flowIds: string[], batchName: string, memo: string) {
    try {
      this.tableLoading = true;

      const batchData = {
        batchName,
        jobList: flowIds,
        action: 'RESTART',
        memo,
        jobConfigs: {
          clusterId: '',
          savepoint: true,
          force: false
        }
      };

      // 执行重启
      const result = await saveBatch(batchData);
      if (result.success) {
        this.$message.success(this.$t('pa.flow.batchOperationSuccess') as string);
        this.getFlowList();
      } else {
        this.$message.error(
          `${this.$t('pa.flow.batchOperationFailed')}：${result.error || result.msg || this.$t('pa.flow.unknownError')}`
        );
      }
    } finally {
      this.tableLoading = false;
    }
  }

  // 执行带资源配置的批量启动
  async executeBatchOnlineWithConfig(
    flowIds: string[],
    fromLastCheckpoint: boolean,
    batchName: string,
    memo: string,
    configData: any
  ) {
    try {
      this.tableLoading = true;

      // 解析 properties 字符串获取实际的配置数据
      let actualConfigData = {};
      try {
        if (configData.pajob && configData.pajob.properties && typeof configData.pajob.properties === 'string') {
          actualConfigData = JSON.parse(configData.pajob.properties);
        } else if (configData.properties && typeof configData.properties === 'string') {
          actualConfigData = JSON.parse(configData.properties);
        } else {
          actualConfigData = configData;
        }
      } catch (error) {
        // JSON 解析失败时使用原始数据
        actualConfigData = configData;
      }

      // 将资源配置数据合并到 jobConfigs 中
      const batchData = {
        batchName,
        jobList: flowIds,
        action: 'ONLINE',
        memo,
        jobConfigs: {
          ...actualConfigData,
          savepoint: fromLastCheckpoint,
          force: false
        }
      };

      // 执行启动
      const result = await saveBatch(batchData);
      if (result.success) {
        this.$message.success(this.$t('pa.flow.batchOperationSuccess') as string);
        this.getFlowList();
      } else {
        this.$message.error(
          `${this.$t('pa.flow.batchOperationFailed')}：${result.error || result.msg || this.$t('pa.flow.unknownError')}`
        );
      }
    } catch (error) {
      // 错误已通过 API 响应处理
    } finally {
      this.tableLoading = false;
    }
  }

  operateHandler(event: string, row: any) {
    event && this[event](row);
  }

  // 生成默认批量操作名称
  generateDefaultBatchName(type: string): string {
    const now = new Date();
    const timestamp =
      now.getFullYear().toString() +
      (now.getMonth() + 1).toString().padStart(2, '0') +
      now.getDate().toString().padStart(2, '0') +
      now.getHours().toString().padStart(2, '0') +
      now.getMinutes().toString().padStart(2, '0') +
      now.getSeconds().toString().padStart(2, '0');

    const typeMap = {
      OFFLINE: this.$t('pa.flow.batchOffline'),
      RESTART: this.$t('pa.flow.batchRestart'),
      ONLINE: this.$t('pa.flow.batchOnline')
    };

    const typeName = typeMap[type] || this.$t('pa.flow.batchOperation');
    return `${typeName}_${timestamp}`;
  }

  showBatchDialog(type: string, flowIds: string[], extraParams?: any) {
    const typeMap = {
      OFFLINE: this.$t('pa.flow.batchOffline'),
      CANCEL_PUBLISH: this.$t('pa.flow.batchCancelPublish'),
      PUBLISH: this.$t('pa.flow.batchPublish'),
      ONLINE: this.$t('pa.flow.batchOnline'),
      RESTART: this.$t('pa.flow.batchRestart')
    };

    this.batchDialogTitle = typeMap[type] || this.$t('pa.flow.batchOperation');
    this.batchForm.batchName = this.generateDefaultBatchName(type);
    this.batchForm.memo = '';
    this.currentBatchOperation = {
      type,
      flowIds,
      extraParams
    };
    this.batchDialogVisible = true;
  }

  // 批量操作弹框确认
  async handleBatchConfirm() {
    try {
      await (this.$refs.batchForm as any).validate();

      const { type, flowIds, extraParams } = this.currentBatchOperation;
      const { batchName, memo } = this.batchForm;

      // 根据操作类型执行相应的批量操作
      switch (type) {
        case 'OFFLINE':
          await this.executeBatchOffline(flowIds, extraParams.savepoint, extraParams.force, batchName, memo);
          break;
        case 'CANCEL_PUBLISH':
          await this.executeBatchCancelPublish(flowIds, batchName, memo);
          break;
        case 'PUBLISH':
          await this.executeBatchPublish(flowIds, batchName, memo);
          break;
        case 'ONLINE':
          // 检查是否所有流程都已配置加工引擎
          if (this.checkAllFlowsConfigured(flowIds)) {
            // 所有流程都已配置，直接启动
            await this.executeBatchOnline(flowIds, extraParams.fromLastCheckpoint, batchName, memo);
            this.batchDialogVisible = false;
          } else {
            // 有流程未配置，显示资源配置弹框
            this.showResourceConfigDialog(flowIds, extraParams.fromLastCheckpoint, batchName, memo);
          }
          break;
        case 'RESTART':
          await this.executeBatchRestart(flowIds, batchName, memo);
          break;
      }

      // 非启动操作直接关闭弹框
      if (type !== 'ONLINE') {
        this.batchDialogVisible = false;
      }
    } catch (error) {
      // 表单验证错误已通过其他方式处理
    }
  }

  // 批量操作弹框取消
  handleBatchCancel() {
    this.batchDialogVisible = false;
    this.currentBatchOperation = { type: '', flowIds: [] };
  }

  // 显示资源配置弹框
  showResourceConfigDialog(flowIds: string[], fromLastCheckpoint: boolean, batchName: string, memo: string) {
    // 保存启动参数，供资源配置弹框关闭时使用
    this.resourceConfigData = {
      flowIds,
      fromLastCheckpoint,
      batchName,
      memo
    };

    // 关闭批量操作弹框，延迟显示资源配置弹框以避免闪现
    this.batchDialogVisible = false;
    setTimeout(() => {
      this.resourceConfigDialogVisible = true;
    }, 100);
  }

  // 资源配置弹框关闭处理
  async handleResourceConfigClose(configData?: any) {
    this.resourceConfigDialogVisible = false;

    if (configData && this.resourceConfigData) {
      // 用户点击了保存，执行批量启动
      const { flowIds, fromLastCheckpoint, batchName, memo } = this.resourceConfigData;
      await this.executeBatchOnlineWithConfig(flowIds, fromLastCheckpoint, batchName, memo, configData);
    }

    // 清理资源配置数据
    this.resourceConfigData = null;
  }

  // 将批量操作 返回单个流程对应的错误信息存储
  setFlowErrorInfo(data: any, type: any) {
    data.forEach(({ jobId, success, errorInfo }: any) => {
      success ? removeErrorInfo(jobId) : setErrorInfo(jobId, JSON.stringify({ type, errorInfo }));
    });
  }
}
</script>
<style lang="scss" scoped>
.normal-list__content {
  height: calc(100vh - 180px);
  .table-header-operator {
    display: flex;

    .el-button-group {
      .el-button {
        margin-left: 0;
      }

      .el-dropdown {
        display: inline-block;

        .el-button {
          margin-left: 0;
          border-radius: 0;

          &:hover {
            z-index: 1;
          }
        }
      }

      // 确保下拉按钮与其他按钮在按钮组中正确对齐
      .el-dropdown:first-child .el-button {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }

      .el-dropdown:last-child .el-button {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
  }
  .full-table {
    width: 100%;
    .iconfont {
      cursor: pointer;
    }
    .iconfont + .iconfont {
      margin-left: 10px;
    }
    .el-dropdown {
      position: static;
    }

    .info {
      margin-left: 8px;
      cursor: pointer;
    }

    .jobName-slot {
      display: flex;
      align-items: center;
      .el-tag--dark {
        width: 40px;
        text-align: center;
      }
      .jobName {
        margin-left: 8px;
        display: inline-block;
        max-width: 180px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        cursor: pointer;
      }
      .jobName:hover {
        color: #2d8cf0;
      }
    }

    .jobRunTimeStatus {
      display: flex;
      align-items: center;
      .successColor {
        width: 8px;
        height: 8px;
        background: #54c9584d;
        border: 1px solid #54c958cc;
        border-radius: 2px;
      }
      .primaryColor {
        width: 8px;
        height: 8px;
        background: #377cff4d;
        border: 1px solid #377cffcc;
        border-radius: 2px;
      }
      .dangerColor {
        width: 8px;
        height: 8px;
        background: #ff53534d;
        border: 1px solid #ff5353cc;
        border-radius: 2px;
      }
      .infoColor {
        width: 8px;
        height: 8px;
        background: #f1f1f1;
        border: 1px solid #aaaaaa;
        border-radius: 2px;
      }
      .info:hover {
        color: #2d8cf0;
      }
    }

    ::v-deep .is-dot {
      top: -2px;
      border-radius: 0;
    }

    ::v-deep .bs-tag--light {
      .icon-shangxianzhong:before {
        font-size: 10px;
        color: $--bs-color-primary;
      }
    }
    ::v-deep .bs-tag--purple {
      .icon-shangxianzhong:before {
        color: #8c6bd6;
      }
    }
  }
}
</style>
