<template>
  <div class="normal-list__content">
    <bs-table
      v-loading="tableLoading"
      :data="tableData.tableData"
      class="full-table"
      :height="checkedRows.length ? 'calc(100vh - 364px)' : 'calc(100vh - 307px)'"
      :column-data="tableData.columnData"
      :page-data="tableData.pageData"
      :checked-rows="checkedRows"
      selection
      crossing
      row-key="id"
      @selection-change="selectionChange"
      @refresh="getFlowList(true)"
      @page-change="handleCurrentChange"
      @sort-change="handleSortChange"
    >
      <template slot="headerOperator">
        <el-button-group>
          <template v-for="el in buttonList">
            <el-button
              v-if="el.type === 'ElTooltip'"
              :key="el.label"
              size="small"
              @click="headerOperateHandler('', el.event)"
            >
              {{ el.label }}
            </el-button>
            <el-dropdown
              v-if="el.type === 'ElDropdown'"
              :key="el.label"
              @command="headerOperateHandler($event, el.event)"
            >
              <el-button size="small">{{ el.label }}</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="item in el.options"
                  :key="item.command"
                  :command="item.command"
                >
                  {{ item.text }}
                  <el-tooltip effect="light" placement="top" :content="item.content">
                    <i :class="item.icon"></i>
                  </el-tooltip>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-button-group>
      </template>
      <template slot="parentProject" slot-scope="{ row }">
        <el-tooltip :content="row.parentProject" effect="light" placement="top">
          <div class="project-name">
            {{ row.parentProject }}
          </div>
        </el-tooltip>
      </template>
      <div slot="jobName" slot-scope="{ row }" class="jobName-slot">
        <el-tag size="mini" effect="dark">
          <span>{{ enumData.jobType[row.jobType] }}</span>
        </el-tag>
        <el-tooltip :content="row.jobName" effect="light" placement="top">
          <div class="jobName" @click="toFlowCanvas(row)">
            {{ row.jobName }}
          </div>
        </el-tooltip>
      </div>
      <template slot="jobStatus" slot-scope="{ row }">
        <bs-tag size="mini" :color="getStatusColor(row.jobStatus)">
          <i :class="getStatusIcon(row.jobStatus)"></i>
          {{ enumData.jobStatus[row.jobStatus] }}
        </bs-tag>
      </template>
      <template slot="jobRunTimeStatus" slot-scope="{ row }">
        <div v-if="row.jobRunTimeStatus" class="jobRunTimeStatus">
          <div :class="getStatusType(row.jobRunTimeStatus)"></div>
          <span class="info" @click="toFlowMonitor(row)">
            {{ enumData.jobRunTimeStatus[row.jobRunTimeStatus] }}
          </span>
        </div>
        <div v-else>-</div>
      </template>
      <template slot="mode" slot-scope="{ row }">
        {{ enumData.mode[row.mode] }}
      </template>
      <template slot="operator" slot-scope="{ row }">
        <template v-for="el in buttonList">
          <el-tooltip
            v-if="el.type === 'operator'"
            :key="el.label"
            :content="el.label"
            effect="light"
          >
            <i :class="el.icon" @click="operateHandler(el.event, row)"></i>
          </el-tooltip>
        </template>
      </template>
    </bs-table>
    <!-- 批量流程配置 -->
    <batch-online-dialog
      v-if="showBatchFlowConfig"
      :show.sync="showBatchFlowConfig"
      :status="statusFlag"
      :list="selectedData"
      :from-normal-list="true"
      @close="closeDialog"
    />
    <!-- 单个流程资源配置 -->
    <resource-single-dialog
      v-if="showFlowConfig"
      :show.sync="showFlowConfig"
      :flow-id="jobData.id"
      @close="closeDialog"
    />
    <!-- 流程复制/移动 -->
    <batch-copy-dialog
      v-if="showCopyMove"
      :list="checkedRows"
      :is-copy="isCopy"
      :show.sync="showCopyMove"
      @close="closeDialog('isMove')"
    />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Watch, Vue } from 'vue-property-decorator';
import { hasPermission } from '@/utils';
import {
  getFlowListBy,
  deleteFlows,
  exportFlows,
  perPublishFlows,
  batchOperate
} from '@/apis/flowNewApi';
import moment from 'moment';
@Component({
  directives: {
    hide: {
      inserted(el, bind, vnode) {
        if (el.clientWidth === el.scrollWidth) {
          (vnode as any).componentInstance.$destroy();
        }
      }
    }
  },
  components: {
    'batch-online-dialog': () => import('@/components/batch-online-dialog/index.vue'),
    'batch-copy-dialog': () => import('../components/batch-copy-dialog.vue'),
    'resource-single-dialog': () =>
      import('@/components/single-flow-config/resource-single-dialog.vue')
  }
})
export default class NormalListContent extends Vue {
  @Prop() params!: any;
  tableLoading = true;
  showFlowConfig = false;
  showCopyMove = false;
  showBatchFlowConfig = false;
  statusFlag = false;
  isCopy = true;
  jobData: any = {};
  // 表格配置
  tableData: ITableData = {
    columnData: [{ prop: '', label: '' }],
    tableData: [],
    pageData: { pageSize: 20, currentPage: 1, total: 0 }
  };
  enumData: any = {
    jobStatus: {},
    jobRunTimeStatus: {},
    mode: {},
    jobType: {}
  };
  checkedRows: any[] = [];
  selectedData = [];
  searchParams: any = {
    name: '',
    projectId: 'ALL',
    jobStatus: 'ALL',
    jobRunTimeStatus: 'ALL',
    mode: 'ALL',
    clusterType: 'ALL',
    resId: 'ALL'
  };
  sortData: any = { order: 'DESC', prop: 'updateTime' };

  get buttonList() {
    // 权限过滤
    return [
      // { type: 'ElTooltip', label: '导出', event: 'exportFlows', access: 'PA.FLOW.FLOW_MGR.EXPORT' },
      { type: 'ElTooltip', label: '复制', event: 'copyFlows', access: 'PA.FLOW.FLOW_MGR.COPY' },
      { type: 'ElTooltip', label: '移动', event: 'moveFlows', access: 'PA.FLOW.FLOW_MGR.MOVE' },
      { type: 'ElTooltip', label: '删除', event: 'deleteFlows', access: 'PA.FLOW.FLOW_MGR.DELETE' },
      {
        type: 'ElTooltip',
        label: '发布',
        event: 'publishFlows',
        access: 'PA.FLOW.FLOW_MGR.PUBLISH'
      },
      {
        type: 'ElTooltip',
        label: '取消发布',
        event: 'cancelPublish',
        access: 'PA.FLOW.FLOW_MGR.CANCEL_PUBLISH'
      },
      {
        type: 'ElDropdown',
        label: '启动',
        event: 'handleOnlineCommand',
        access: 'PA.FLOW.FLOW_MGR.ONLINE',
        options: [
          {
            content: '流程首次启动，或者修改kafka consumer group等信息后需要从头开始消费数据。',
            icon: 'el-icon-warning-outline',
            command: false,
            text: '无状态启动'
          },
          {
            content:
              '流程重启，需要接着上次checkpoint记录的位置，如kafka上次的offset位置继续消费。',
            icon: 'el-icon-warning-outline',
            command: true,
            text: '基于上次状态启动'
          }
        ]
      },
      {
        type: 'ElDropdown',
        label: '停止',
        event: 'offline',
        access: 'PA.FLOW.FLOW_MGR.OFFLINE',
        options: [
          {
            command: false,
            text: '停止'
          },
          {
            content: '即flink savepoint，用于暂停流程，流程重新启动，保证精准一次语义。',
            icon: 'el-icon-warning-outline',
            command: true,
            text: '停止并保留状态'
          }
        ]
      },
      {
        type: 'operator',
        label: '流程设计',
        event: 'toFlowCanvas',
        icon: 'iconfont icon-liuchengsheji1',
        access: 'PA.FLOW.FLOW_MGR.VIEW'
      },
      {
        type: 'operator',
        label: '流程配置',
        event: 'sourceConfig',
        icon: 'iconfont icon-liuchengpeizhi',
        access: 'PA.FLOW.FLOW_MGR.CONF'
      }
    ].filter((item) => hasPermission(item.access));
  }

  @Watch('params', { deep: true, immediate: true })
  handleParamsChange(val: any) {
    if (!val) {
      return;
    }

    const { name, jobStatus, jobRunTimeStatus, mode, clusterType, projectId, resId } = val;

    this.searchParams.name = name;
    this.searchParams.jobStatus = jobStatus;
    this.searchParams.jobRunTimeStatus = jobRunTimeStatus;
    this.searchParams.mode = mode;
    this.searchParams.clusterType = clusterType;
    this.searchParams.projectId = projectId;
    this.searchParams.resId = resId;

    (this.tableData.pageData as any).currentPage = 1;
    this.getFlowList();
  }

  toFlowCanvas(row) {
    const title = row.paJob.jobName;
    if (
      (this as any).$tabsNav
        .getAllTabs()
        .find((item) => item.title === title && item.value.split('flowId=')[1] === row.paJob.id)
    ) {
      const value = (this as any).$tabsNav
        .getAllTabs()
        .find(
          (item) => item.title === title && item.value.split('flowId=')[1] === row.paJob.id
        ).value;
      localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId: row.paJob.id }));
      this.$router.push({
        path: value
      });
    } else {
      this.$router.push({
        path: 'flow',
        query: {
          id: row.paJob.projectId,
          name: row.paJob.projectName,
          title,
          state: 'ALL',
          flowId: row.paJob.id
        }
      });
    }
  }

  toFlowMonitor(row) {
    if (row.jobStatus === 'DEV') {
      return;
    }
    this.$router.push({
      path: 'monitor/flow',
      query: {
        name: row.jobName,
        runStatus: row.jobRunTimeStatus
      }
    });
  }

  getStatusColor(status: string) {
    return {
      PROD: 'green',
      INPROD: '',
      PUB: '',
      INPUB: 'purple',
      DEV: 'purple'
    }[status];
  }
  getStatusType(status: string) {
    return {
      FINISHED: 'successColor',
      RUNNING: 'primaryColor',
      NONE: 'dangerColor',
      UNKNOWN: 'infoColor',
      OTHER: 'infoColor',
      FAILED: 'infoColor'
    }[status];
  }
  getStatusIcon(status: string) {
    return {
      INPUB: 'iconfont icon-shangxianzhong',
      INPROD: 'iconfont icon-shangxianzhong'
    }[status];
  }

  async getFlowList(refresh = false) {
    try {
      this.tableLoading = true;
      const params = {
        pageData: this.tableData.pageData,
        search: {
          name: this.searchParams.name,
          projectId: this.searchParams.projectId,
          jobStatus: this.searchParams.jobStatus,
          jobRunTimeStatus: this.searchParams.jobRunTimeStatus,
          mode: this.searchParams.mode,
          clusterType: this.searchParams.clusterType,
          resId: this.searchParams.resId
        },
        sortData: this.sortData
      };
      const { data, success, msg, error } = await getFlowListBy(params);
      if (success) {
        const { columnData, tableData, pageData } = data;

        columnData.push({ label: '操作', value: 'operator', width: 110, fixed: 'right' });
        tableData.forEach((el) => {
          el.createTime = moment(el.createTime).format('YYYY-MM-DD HH:mm:ss');
          el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
          el.id = el.paJob.id;
        });
        // 设置项目名称列宽度和不显示溢出提示
        columnData[0].width = 120;
        columnData[0].showOverflowTooltip = false;
        // 设置流程名称列不显示溢出提示
        columnData[1].showOverflowTooltip = false;
        this.enumData = {
          jobStatus: columnData[2].enumData, // 流程状态
          jobRunTimeStatus: columnData[3].enumData, // 运行状态
          jobType: columnData[4].enumData, // 流程类型
          mode: columnData[5].enumData // 流程模式
        };
        columnData.splice(4, 1); // 删除流程类型列
        //表格数据变化选中数据也需变化
        if (this.checkedRows.length) {
          for (const i in this.checkedRows) {
            const target: any = tableData.find(({ id }) => id === this.checkedRows[i]?.id);
            if (target) {
              this.checkedRows[i] = { ...target, id: this.checkedRows[i].id };
            }
          }
        }
        this.tableData = {
          columnData,
          tableData,
          pageData
        };

        refresh && this.$message.success('刷新成功');
        this.tableLoading = false;
      } else {
        this.tableLoading = false;
        this.$message.error(error || msg);
      }
    } catch (e) {
      this.tableLoading = false;
      console.log(e);
    }
  }
  fetchList() {
    this.getFlowList();
  }

  handleCurrentChange(currentPage, pageSize) {
    (this.tableData.pageData as any).currentPage = currentPage;
    (this.tableData.pageData as any).pageSize = pageSize;
    this.getFlowList();
  }
  handleSortChange(val) {
    this.sortData = {
      order: val.order === 'ascending' ? 'ASC' : 'DESC',
      prop: val.prop
    };
    this.getFlowList();
  }
  headerOperateHandler(state: string, event: string) {
    event && this[event](state);
  }
  operateHandler(event: string, row) {
    event && this[event](row);
  }
  selectionChange(selection) {
    this.checkedRows = selection;
  }
  //删除流程
  deleteFlows() {
    if (this.checkedRows.length > 0) {
      this.$confirm('确认删除这些流程吗?', '删除流程', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(async () => {
        const ids: any = this.checkedRows.map((e: any) => e.paJob.id);
        const { msg, success, error } = await deleteFlows(ids);
        if (success) {
          this.$tip.success(msg);
          this.checkedRows = [];
          this.getFlowList();
        } else {
          this.$tip.error(error || msg);
        }
      });
    } else {
      this.$tip.warning('请选择要删除的流程');
    }
  }
  //流程导出
  async exportFlows() {
    if (this.checkedRows.length > 0) {
      const ids: string[] = [];
      this.checkedRows.forEach((el: any) => {
        ids.push(el.paJob.id);
      });
      this.tableLoading = true;
      const res: {
        blob?: any;
        fileName?: string;
        msg?: string;
      } = await exportFlows([...ids]);
      this.tableLoading = false;
      if (res.blob && res.fileName) {
        const responseUrl = window.URL.createObjectURL(new Blob([res.blob]));
        const link = document.createElement('a');
        link.href = responseUrl;
        link.setAttribute('download', res.fileName); // 文件名
        link.click();
        window.URL.revokeObjectURL(responseUrl); // 释放掉blob对象);
        this.$tip.success(`成功导出${this.checkedRows.length}个流程`);
      } else {
        if (res instanceof Blob) {
          const reader = new FileReader();
          reader.onload = (e: any) => {
            const { error: message } = JSON.parse(e.target.result);
            this.$tip({
              message,
              type: 'error'
            });
          };
          reader.readAsText(res);
        }
      }
    } else {
      this.$tip.warning('请选择要导出的流程');
    }
  }
  //流程发布
  async publishFlows() {
    if (this.checkedRows.length > 0) {
      const loading = this.$loading({
        lock: true,
        text: '批量发布操作添加中，请稍等...'
      });
      const ids: string[] = [];
      this.checkedRows.forEach((el: any) => {
        ids.push(el.paJob.id);
      });
      const res = await perPublishFlows({ relationBatchTag: true, jobs: ids });
      if (res.success) {
        this.getFlowList();
        const resp = await batchOperate({
          jobIds: ids,
          batchEventType: 'PUB',
          fromState: false,
          saveState: false
        });
        loading.close();
        this.getFlowList();
        if (resp.success) {
          this.$tip.success(resp.msg || '批量发布操作添加成功，可点击批量任务按钮查看详情');
        } else {
          this.$tip.error({ message: resp.msg, duration: 5000 });
        }
      } else {
        loading.close();
        this.$tip.error({ message: res.msg, duration: 5000 });
      }
    } else {
      this.$tip.warning('请选择要发布的流程');
    }
  }

  //流程取消发布
  async cancelPublish() {
    if (this.checkedRows.length > 0) {
      const loading = this.$loading({
        lock: true,
        text: '批量取消发布操作添加中，请稍等...'
      });
      const ids: string[] = [];
      this.checkedRows.forEach((el: any) => {
        ids.push(el.paJob.id);
      });
      const res = await batchOperate({
        jobIds: ids,
        batchEventType: 'DEV',
        fromState: false,
        saveState: false
      });
      loading.close();
      if (res.success) {
        this.$tip.success(res.msg || '批量取消发布操作添加成功，可点击批量任务按钮查看详情');
        this.getFlowList();
      } else {
        this.$tip.error({ message: res.msg, duration: 5000 });
      }
    } else {
      this.$tip.warning('请选择要取消发布的流程');
    }
  }

  //启动流程
  handleOnlineCommand(val) {
    if (this.checkedRows.length > 0) {
      if (val) {
        this.online(val);
      } else {
        // 无状态启动 进行提示
        this.$confirm('确定无状态启动吗？', '提示', {
          type: 'warning'
        })
          .then(() => {
            this.online(val);
          })
          .catch(() => false);
      }
    } else {
      this.$tip.warning('请选择要启动的流程');
    }
  }
  online(val) {
    this.statusFlag = val;
    const selectedData: any = this.checkedRows.map((n: any) => {
      return n.paJob;
    });
    this.selectedData = selectedData;
    this.showBatchFlowConfig = true;
  }
  //停止流程
  async offline(val) {
    if (this.checkedRows.length > 0) {
      const loading = this.$loading({
        lock: true,
        text: '批量停止操作添加中，请稍等...'
      });
      const ids: string[] = [];
      this.checkedRows.forEach((el: any) => {
        ids.push(el.paJob.id);
      });
      const res = await batchOperate({
        jobIds: ids,
        batchEventType: 'OFFLINE',
        fromState: false,
        saveState: val // val参数表示是否保存状态停止
      });
      loading.close();
      if (res.success) {
        this.$tip.success(res.msg || '批量停止操作添加成功，可点击批量任务按钮查看详情');
        this.getFlowList();
      } else {
        this.$tip.error({ message: res.msg, duration: 5000 });
      }
    } else {
      this.$tip.warning('请选择要停止的流程');
    }
  }
  /* 配置按钮点击事件 */
  sourceConfig(row) {
    this.jobData = row.paJob;
    this.showFlowConfig = true;
  }
  //复制流程
  copyFlows() {
    if (this.checkedRows.length > 0) {
      this.showCopyMove = true;
      this.isCopy = true;
    } else {
      this.$tip.warning('请选择要复制的流程');
    }
  }
  //移动流程
  moveFlows() {
    if (this.checkedRows.length > 0) {
      this.showCopyMove = true;
      this.isCopy = false;
    } else {
      this.$tip.warning('请选择要移动的流程');
    }
  }
  closeDialog(type = '') {
    this.showBatchFlowConfig = false;
    this.showCopyMove = false;
    this.showFlowConfig = false;
    if (type === 'isMove' && !this.isCopy) this.checkedRows = []; //批量移动完情况选中
    this.getFlowList();
  }
}
</script>
<style lang="scss" scoped>
.normal-list__content {
  height: calc(100vh - 180px);
  .full-table {
    .iconfont {
      cursor: pointer;
    }
    .iconfont + .iconfont {
      margin-left: 10px;
    }
    .el-dropdown {
      position: static;
    }

    .info {
      margin-left: 8px;
      cursor: pointer;
    }

    .project-name {
      display: flex;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 60px;
    }

    .jobName-slot {
      display: flex;
      align-items: center;
      .el-tag--dark {
        width: 40px;
        text-align: center;
      }
      .jobName {
        margin-left: 8px;
        display: inline-block;
        max-width: 180px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        cursor: pointer;
      }
      .jobName:hover {
        color: #2d8cf0;
      }
    }

    .jobRunTimeStatus {
      display: flex;
      align-items: center;
      .successColor {
        width: 8px;
        height: 8px;
        background: #54c9584d;
        border: 1px solid #54c958cc;
        border-radius: 2px;
      }
      .primaryColor {
        width: 8px;
        height: 8px;
        background: #377cff4d;
        border: 1px solid #377cffcc;
        border-radius: 2px;
      }
      .dangerColor {
        width: 8px;
        height: 8px;
        background: #ff53534d;
        border: 1px solid #ff5353cc;
        border-radius: 2px;
      }
      .infoColor {
        width: 8px;
        height: 8px;
        background: #f1f1f1;
        border: 1px solid #aaaaaa;
        border-radius: 2px;
      }
      .info:hover {
        color: #2d8cf0;
      }
    }

    ::v-deep .is-dot {
      top: -2px;
      border-radius: 0;
    }

    ::v-deep .bs-tag--light {
      .icon-shangxianzhong:before {
        font-size: 10px;
        color: $--bs-color-primary;
      }
    }
    ::v-deep .bs-tag--purple {
      .icon-shangxianzhong:before {
        color: #8c6bd6;
      }
    }
  }
}
</style>
