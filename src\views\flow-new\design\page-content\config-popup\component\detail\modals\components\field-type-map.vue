<!-- 用于转换组件 自定义字段的类型更改 -->
<template>
  <div class="field-type-map">
    <el-checkbox v-model="checkValue" :disabled="disabled" @change="handleCheckChange">{{
      $t('pa.flow.fieldTypeMap')
    }}</el-checkbox>
    <el-form v-if="checkValue" ref="form" :model="{}" :rules="rules" :disabled="disabled" :validate-on-rule-change="false">
      <div v-for="(item, $index) in data" :key="item.type + $index" class="field-type-map__item">
        <el-form-item :prop="$index + ''">
          <div class="field-type-map__content">
            <bs-select
              v-model="item.type"
              :options="typeOptions"
              :placeholder="$t('pa.flow.fieldType')"
              class="field-type-map__type"
              @change="(val) => handleTypeChange(val, $index)"
            />
            <el-input-number
              v-if="showInputNumber(item)"
              v-model="item.params[0]"
              :placeholder="$t('pa.flow.numTotal')"
              :min="item.params[1]"
              :max="65"
            />
            <el-input-number
              v-if="showInputNumber(item)"
              v-model="item.params[1]"
              :placeholder="$t('pa.flow.numTotal1')"
              :min="0"
              :max="Math.min(30, item.params[0])"
            />
            <bs-select
              v-model="item.names"
              :options="fieldOptions"
              multiple
              clearable
              filterable
              collapse-tags
              :placeholder="$t('pa.flow.placeholder19')"
              class="field-type-map__field"
              @change="(val) => handleFieldChange(val, $index)"
            >
              <div slot-scope="scope">
                <span class="field-type-map__field-label">{{ scope.item.label }}</span>
                <span class="field-type-map__field-type">{{ scope.item.type }}</span>
              </div>
            </bs-select>
          </div>
          <span v-if="!disabled" class="iconfont icon-xinjianxiangmu" @click="addItem($index)"></span>
          <span v-if="!disabled" class="iconfont icon-shanchu" @click="delItem($index)"></span>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts">
import { getDataType } from '@/apis/flowNewApi';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
interface Data {
  names: string[];
  type: string;
  params: number[];
}
interface Options {
  value: string;
  label: string;
  type?: string;
}
// 获取初始化数据
const getInitData = () => {
  return {
    names: [],
    type: '',
    params: []
  };
};

const Decimal = 'Decimal';
const String = 'String';
@Component
export default class FieldTypeMap extends Vue {
  @Prop({ default: () => [] }) fields!: string[]; // 已选择的自定义字段
  @Prop() outputFields!: any[]; // 完整的输出字段
  @Prop({ default: false }) disabled!: boolean;
  checkValue = false;
  // 全部类型列表记录
  allTypeOptions: Options[] = [];
  // 字段类型映射数据
  data: Data[] = [getInitData()];
  get rules() {
    const rules = {};
    for (let i = 0; i < this.data.length; i++) {
      rules[i + ''] = [{ validator: this.validateItem, trigger: 'blur' }];
    }
    return rules;
  }
  // 获取自定义参数
  get customFields() {
    // 过滤掉上有输入的字段 或者 类型为String的字段
    return this.outputFields.filter(({ name }) => this.fields.includes(name));
  }
  // 字段列表
  get fieldOptions() {
    // 根据data实时更新下拉字段类型
    const map = new Map();
    this.data.forEach(({ names, type }) => {
      names.reduce((res, i) => {
        res.set(i, type);
        return res;
      }, map);
    });
    return this.fields.map((name) => ({
      label: name,
      value: name,
      type: map.get(name) || '' // string类型不显示
    }));
  }
  // 字段类型列表
  get typeOptions() {
    const types = this.data.map(({ type }) => type);
    return this.allTypeOptions.filter(
      // 过滤掉string类型和已经使用的类型 Decimal字段可以重复
      (i) => i.value !== String && (!types.includes(i.value) || i.value === Decimal)
    );
  }
  // 字段类型列表
  async created() {
    const { data = [] } = (await getDataType()) || {};
    this.allTypeOptions = data.map((i) => ({ value: i, label: i }));
    this.generateData(this.fields, true);
  }
  @Watch('fields')
  handleDataChange(val) {
    // 当字段映射是勾选状态进行数据拼接
    this.generateData(val);
  }
  // 处理checkbox change事件
  handleCheckChange(val) {
    // 选中初始化数据
    val && (this.data = [getInitData()]);
  }
  // 处理字段类型 select change
  handleTypeChange(val, index) {
    // 字段类型变更 清除已选则的字段
    this.data[index].names = [];
    this.data[index].params = [18, 2];
  }
  // 处理字段 select change
  handleFieldChange(val, index) {
    const { names } = this.data[index];
    this.data.some((item, dIndex) => {
      if (dIndex === index) return false;
      // 取字段交集 获取重复设置类型的字段
      const set = new Set(item.names);
      const intersect = Array.from(new Set(names.filter((s) => set.has(s))));
      if (intersect.length === 1) {
        this.$message(this.$t('pa.flow.msg93', [intersect[0], item.type]));
        item.names.splice(item.names.indexOf(intersect[0]), 1);
        return true;
      }
      return false;
    });
    (this.$refs.form as any).validateField(index + '');
  }
  // 根据传入的字段信息初始化data
  generateData(val, isInit = false) {
    const getDecimalKey = (params = [{ value: '' }]) => {
      return Decimal + params[0].value + params[1].value;
    };
    const splitDecimalKey = (key) => {
      if (!key.includes(Decimal)) return key;
      return key.replace(/\d/g, '');
    };
    // 不是初始状态下 未勾选中字段映射 则不进行一下的计算
    if (!isInit && !this.checkValue) return;
    // 初始进来时需要判断下checkValue的值
    if (isInit) {
      const map = new Map();
      this.customFields.forEach(({ name, type, params }) => {
        if (type === String) return;
        // 因为Decimal有多个所以将精度进行拼接 获取唯一标识
        if (type === Decimal) {
          type = getDecimalKey(params);
        }
        if (!map.get(type)) map.set(type, { names: [] });
        const mapVal = map.get(type);
        mapVal.params = params || [];
        mapVal.names.push(name);
      });
      this.data =
        map.size === 0
          ? [getInitData()]
          : [...map.entries()].map(([key, value]) => ({
              type: splitDecimalKey(key),
              names: value.names,
              params: key.includes(Decimal) ? value.params.map((i) => Number(i.value)) : []
            }));
      this.checkValue = map.size > 0;
    } else {
      // 不是初次进去只更新data数据
      this.data.forEach((item) => {
        item.names = item.names.filter((i) => val.includes(i));
      });
    }
  }
  delItem(index) {
    if (this.data.length === 1) {
      return this.$message.warning(this.$t('pa.flow.msg94'));
    }
    this.data.splice(index, 1);
  }
  addItem(index) {
    if (this.data.length === 20) {
      return this.$message.warning(this.$t('pa.flow.msg95'));
    }
    this.data.splice(index + 1, 0, getInitData());
  }
  showInputNumber(item) {
    return item.type === Decimal && Array.isArray(item.params);
  }
  validateItem({ field }, value, callback) {
    const data = this.data[field];
    // 类型及字段都未填写 直接跳过校验
    if (!data.type && data.names.length === 0) {
      return callback();
    }
    if (data.type === Decimal && (data.params[0] === undefined || data.params[1] === undefined)) {
      return callback(new Error(this.$t('pa.flow.msg96')));
    }
    if (!data.type || data.names.length === 0) {
      return callback(new Error(this.$t('pa.flow.msg97')));
    }
    callback();
  }
  // 将配置的字段类型合并到输出字段中
  public mergeOutputs(outputFields) {
    const map = new Map();
    // 未勾选 不进行计算 并将字段重置为String
    if (this.checkValue) {
      this.data.forEach((item) => {
        item.names.forEach((fItem) => {
          map.set(fItem, {
            type: item.type,
            params: item.params
          });
        });
      });
    }
    outputFields.forEach((item) => {
      if (map.has(item.name)) {
        const { type, params } = map.get(item.name);
        item.type = type;
        type === Decimal &&
          (item.params = [
            { type: 'precision', value: params[0] },
            { type: 'scale', value: params[1] }
          ]);
      } else {
        item.type = 'String';
      }
    });
  }
  public async validate() {
    if (!this.checkValue) return true;
    try {
      await (this.$refs.form as any).validate();
    } catch (err) {
      return false;
    }
    return true;
  }
}
</script>
<style lang="scss" scoped>
.field-type-map {
  &__item {
    border-radius: 4px;
    padding: 14px 20px;
    margin-bottom: 10px;
    background: #f9f9f9;
    ::v-deep .el-form-item__content {
      display: flex;
    }
    ::v-deep .el-form-item.is-error {
      padding-bottom: 12px;
    }
    .iconfont {
      margin-right: 10px;
      cursor: pointer;
      color: $--bs-color-primary;
    }
  }
  &__content {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
    margin-right: 20px;
  }
  .el-input-number {
    width: calc((100% - 200px) / 2);
    margin-bottom: 10px;
    margin-right: 10px;
  }
  .el-input-number:nth-child(3) {
    margin-right: 0;
  }
  &__type {
    width: 180px;
    margin-right: 10px;
  }
  &__field {
    flex: 1;
  }
}
</style>
<style lang="scss">
.field-type-map__field-label {
  float: left;
}
.field-type-map__field-type {
  float: right;
  color: $--bs-color-text-placeholder;
}
</style>
