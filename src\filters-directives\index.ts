import Vue from 'vue';
import { dateFormat } from '@/common/utils';
import installDirectives from './directives';
import saveConfirm from './save-confirm/index';
import verifyDd from './verify-dd/index';
import Tip from './tip/index';

export function installFilterAndDirective() {
  Vue.filter('dateFormat', dateFormat);
  installDirectives();
  Vue.use(Tip);
  Vue.use(saveConfirm);
  Vue.use(verifyDd);
}
