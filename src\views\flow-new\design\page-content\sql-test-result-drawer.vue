<template>
  <div v-if="innerVisible" v-dragging class="sql-test" :class="fullScreen ? 'outer-full-screen' : ''" style="height: 300px">
    <div class="sql-test__oper">
      <el-button v-if="isRunning" type="primary" size="small" @click="$emit('stopExcute')">
        {{ $t('pa.flow.stop') }}
      </el-button>
      <span class="iconfont icon-close-small sql-test__oper--icon" @click="handleCancle"></span>
    </div>
    <!-- 头部信息 -->
    <el-tabs v-model="innerActiveName">
      <el-tab-pane :label="$t('pa.flow.testResult')" name="RESULT">
        <bs-table
          paging-front
          row-key="'frontUuid'"
          :column-settings="false"
          :data="tableData"
          :height="tableHeight + 'px'"
          :column-data="columnData"
          :page-data="pageData"
          @page-change="pageChange"
        />
      </el-tab-pane>
      <el-tab-pane :label="$t('pa.flow.runLog')" name="LOG">
        <div class="sql-test__content" :style="{ maxHeight: logMaxHeight + 'px' }">
          <p
            v-for="el in testLog"
            :key="el.id"
            :style="{
              color: el.msgType === 'ERROR_LOG' ? '#FF5353' : ''
            }"
          >
            {{ el.data }}
          </p>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, PropSync, Watch } from 'vue-property-decorator';

@Component
export default class SqlTestResult extends Vue {
  @PropSync('visible', { default: false }) innerVisible!: boolean;
  @PropSync('activeName', { default: 'LOG', required: false }) innerActiveName!: boolean;
  @Prop({ type: Boolean, default: false }) fullScreen!: boolean;
  @Prop() isRunning!: any;
  @Prop() testLog!: any;
  @Prop() columnData!: any;
  @Prop() tableData!: any;

  pageData: IPageData = {
    pageSize: this.$store.getters.pageSize,
    currentPage: 1,
    total: 0
  };
  tableHeight = 205;
  logMaxHeight = 250;
  confirmLoading = false;

  @Watch('tableData')
  handleChange(val) {
    this.pageData.total = val.length;
  }

  // 监听dom高度变化，重新计算表格高度
  mounted() {
    const el: any = this.$el;
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        // 处理高度变化
        const height = (this.$el as any).offsetHeight;
        this.tableHeight = height - 95;
        this.logMaxHeight = height - 50;
      });
    });
    observer.observe(el, {
      attributes: true,
      attributeFilter: ['style']
    });
    this.pageData.total = this.tableData.length;
  }
  pageChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
  }
  handleCancle() {
    this.innerVisible = false;
  }
}
</script>

<style lang="scss" scoped>
.sql-test {
  position: absolute;
  bottom: 0;
  width: calc(100% - 351px);
  background-color: #fff;
  &__oper {
    position: absolute;
    right: 20px;
    top: 15px;
    z-index: 99;
    &--icon {
      margin-left: 20px;
      font-size: 18px;
      cursor: pointer;
    }
  }
  &__content {
    padding: 10px 20px;
    overflow: auto;
    font-size: 12px;
  }
  ::v-deep .el-tabs__nav {
    margin-left: 30px;
    height: 50px;
    line-height: 58px;
  }
  ::v-deep .el-tabs__header {
    margin: inherit;
  }
}
.outer-full-screen {
  width: calc(100% - 350px);
  margin-left: 300px;
}
</style>
