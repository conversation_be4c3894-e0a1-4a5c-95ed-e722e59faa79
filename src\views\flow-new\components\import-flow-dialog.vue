<template>
  <bs-dialog title="流程导入" :visible.sync="display">
    <el-form
      ref="form"
      :key="key"
      :rules="rules"
      :model="formData"
      label-width="80px"
      label-position="left"
      class="form-content"
    >
      <el-form-item label="文件导入" prop="file">
        <el-tooltip
          effect="light"
          :content="fileName"
          :disabled="isDisabled"
          placement="bottom-start"
        >
          <el-upload
            action
            :limit="1"
            accept=".zip"
            :auto-upload="false"
            :on-exceed="exceed"
            :on-change="handleFile"
            :before-remove="removeFile"
            class="form-content-upload"
          >
            <el-button type="primary">选择文件</el-button>
            <div slot="tip" class="el-upload__tip">只能上传zip文件</div>
          </el-upload>
        </el-tooltip>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="display = false">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
    <!-- 重复数据处理 -->
    <repeat-data-dialog
      :show.sync="showRepeatDialog"
      :data="repeatData"
      :project-id="projectId"
      @close="closeAndUpdate"
    />
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Watch, Vue, Emit } from 'vue-property-decorator';
import { debounce } from 'lodash';
import { file } from '@/apis/utils/net';
import { URL_JOB_IMPORT_FILE } from '@/apis/commonApi';

@Component({
  components: {
    'repeat-data-dialog': () => import('./repeat-data-dialog.vue')
  }
})
export default class ImportFlow extends Vue {
  @PropSync('show', { type: Boolean }) display!: boolean;
  @Prop({ default: '' }) projectId!: string;
  private submit = debounce(this.handleSubmit, 1000);
  private showRepeatDialog = false;
  private key: number = Date.now();
  formData: any = { file: null };
  rules: any = { file: [{ required: true, validator: this.validateFile }] };
  private repeatData: any = [];
  private fileName = '';

  get isDisabled() {
    return this.fileName === '' || this.fileName.length < 50;
  }

  @Watch('display')
  handleDisplayChange(val = false) {
    if (!val) {
      this.key = Date.now();
      this.formData.file = null;
      this.$emit('change');
    }
  }

  validateFile(rule, value, callback) {
    value === null ? callback(new Error('请上传文件')) : callback();
  }

  handleFile(data: any) {
    this.formData.file = data.raw;
    (this.$refs.form as any).validate();
    this.fileName = this.formData.file.name;
  }

  removeFile() {
    this.formData.file = null;
    this.fileName = '';
  }

  exceed() {
    this.$message.warning('只能上传一个.zip文件');
  }

  /* 处理提交事件 */
  handleSubmit() {
    (this.$refs.form as any).validate(async (valid: boolean) => {
      if (valid) {
        const { projectId } = this;
        const options = { headers: { ContentType: 'multipart/form-data' } };
        const loading = this.$loading({ lock: true, text: '导入中' });
        const { msg, success, data } = await file(
          URL_JOB_IMPORT_FILE,
          { ...this.formData, projectId },
          options
        );
        loading.close();
        if (success) {
          this.handleRepeatData(data, msg);
        } else {
          this.$message.error(msg);
        }
      }
    });
  }

  /* 处理重复数据 */
  handleRepeatData(data: any = [], msg = '') {
    if (Array.isArray(data) && data.length > 0) {
      this.showRepeatDialog = true;
      this.repeatData = [...data];
      return;
    }
    if (msg) {
      this.$message.success(msg);
    }
    this.closeAndUpdate();
  }

  /* 关闭弹窗并更新数据 */
  @Emit('close')
  closeAndUpdate() {
    this.display = false;
  }
}
</script>

<style lang="scss" scoped>
.form-content {
  &-upload {
    //display: flex;
    text-align: left;
  }

  .el-upload__tip {
    display: inline-block;
    margin-left: 10px;
    margin-top: unset;
  }

  .add-btn {
    display: flex;
    padding: 7px 12px;
    margin-top: 5px;
    width: 416px;
    justify-content: center;
  }

  .contact {
    text-align: left;

    &-delete {
      font-size: 16px;
      cursor: pointer;
    }

    .form-item-input {
      margin-right: 16px;
      width: 200px;
    }
  }
}
</style>
