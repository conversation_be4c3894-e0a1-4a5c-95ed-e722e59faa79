<template>
  <bs-dialog
    title="字段信息"
    :visible="visible"
    size="medium"
    @close="closeDialog"
    @confirm="submit"
  >
    <div class="operate-box" :style="{ marginBottom: select.length ? '' : '10px' }">
      <bs-search
        v-model="search"
        style="margin: 0 10px 10px 10px"
        placeholder="请输入字段名"
        @search="handleSearch"
      />
    </div>
    <bs-table
      v-loading="tableLoading"
      class="table-style"
      :data="tableData"
      :column-data="columnData"
      :column-settings="false"
      :selection="true"
      :page-data="pageData"
      :height="select.length ? 390 : 460"
      crossing
      row-key="columnName"
      @selection-change="selectionChange"
      @page-change="handleCurrentChange"
    />
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import baseTable from '@/components/base-table.vue';
@Component({
  components: { baseTable }
})
export default class SelectFields extends PaBase {
  @Prop({ default: '' }) tableName!: string;
  @Prop({ default: false }) visible!: boolean;
  @Prop() request!: any;
  tableLoading = false;
  search = '';
  columnData = [];
  tableData = [];
  pageData = {
    pageSize: 10,
    currentPage: 1,
    total: 0
  };
  select: any = [];
  total = 0;
  get id() {
    return this.$route.query.id;
  }
  async created() {
    await this.getTableData();
    this.total = this.pageData.total;
  }
  // 分页数据
  handleCurrentChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    this.getTableData();
  }

  // 数据选择回调
  selectionChange(selection) {
    this.select = selection;
  }

  handleSearch() {
    this.pageData.currentPage = 1;
    this.getTableData();
  }

  closeDialog() {
    this.$emit('update:visible', false);
  }

  async getTableData() {
    this.tableLoading = true;
    const {
      success,
      data: { tableData = [], columnData = [], pageData = {} },
      msg
    } = (await this.request(this.id, this.tableName, {
      search: this.search,
      pageData: this.pageData
    })) || {};
    if (!success) return this.$message.error(msg);
    this.tableData = tableData;
    this.columnData = columnData;
    this.pageData.total = pageData.total;
    this.tableLoading = false;
  }

  submit() {
    let str = '*';
    if (this.select.length > 0 && this.select.length < this.total) {
      str = this.select.map((s) => s.columnName).join(',');
    }
    this.$emit('update:visible', false);
    this.$emit('confirm', str);
  }
}
</script>

<style lang="scss" scoped>
.operate-box {
  text-align: right;
}
</style>
