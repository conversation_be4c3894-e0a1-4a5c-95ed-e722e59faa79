<template>
  <div class="page-conten">
    <div class="content">
      <bs-table
        v-loading="tableLoading"
        :height="'calc(100vh - 288px)'"
        :data="tableData.tableData"
        :column-data="tableData.columnData"
        :page-data="tableData.pageData"
        @handleCurrentChange="handleCurrentChange"
        @handleSortChange="handleSortChange"
        @refresh="getListData(true)"
      >
        <template slot="operator" slot-scope="{ row }">
          <el-tooltip v-if="!shareFlag" :content="$t('pa.action.rollBack')" effect="light">
            <i class="iconfont icon-roll-back" @click="rollBack(row)"></i>
          </el-tooltip>
          <el-tooltip :content="$t('pa.action.viewSourceCode')" effect="light">
            <i class="iconfont icon-ziyuan" @click="showCode(row)"></i>
          </el-tooltip>
          <el-tooltip :content="$t('pa.action.versionContrast')" effect="light">
            <i class="iconfont icon-banbenduibi" @click="diffDetail(row)"></i>
          </el-tooltip>
        </template>
      </bs-table>
    </div>
    <sql-preview-dialog
      v-if="showPreviewDialog"
      :title="$t('pa.data.codeView')"
      :show.sync="showPreviewDialog"
      :data="sourceCode"
    />
    <comparison :id="initId" ref="comparison" :title="$t('pa.action.versionContrast')" :choose-version="chooseVersion" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { URL_TABLEHIS, URL_TABLEHIS_ROLLLBACK } from '@/apis/commonApi';
import * as _ from 'lodash';
import dayjs from 'dayjs';
import { get, post, put } from '@/apis/utils/net';
@Component({
  components: {
    SqlPreviewDialog: () => import('../../modals/sql-preview-dialog.vue'),
    comparison: () => import('@/components/version-comparison/index.vue')
  }
})
export default class SheetVersion extends Vue {
  @Prop({ default: false }) shareFlag!: boolean;
  id = '';
  sourceCode: any = {};
  showPreviewDialog = false;
  tableLoading = false;
  searchObj = {
    pageData: { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 },
    sortData: { tableVersion: 'DESC' }
  };
  tableData: any = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  chooseVersion: any = '';
  initId = '';
  created() {
    this.id = this.$route.query.id as string;
    this.getListData();
  }
  activated() {
    this.getListData();
  }
  async showCode(row) {
    try {
      this.tableLoading = true;
      const { data } = await get('/rs/pa/sql/tableHis/sql/sourceCode' + '?resId=' + row.id);
      this.sourceCode = data;
      this.showPreviewDialog = true;
    } finally {
      this.tableLoading = false;
    }
  }
  diffDetail(row) {
    this.initId = row.id;
    this.chooseVersion = row.tableVersion;
    const previewRef: any = this.$refs.comparison;
    previewRef.visible = true;
  }
  async rollBack(row: any) {
    try {
      const api = URL_TABLEHIS_ROLLLBACK;
      await this.$confirm(this.$t('pa.tip.rollBackConfirm'), this.$t('pa.prompt'));
      const { msg } = await put(api, { tableHisId: row.id });
      this.$message.success(msg);
      this.getListData();
    } catch {}
  }
  handleCurrentChange(val) {
    this.searchObj.pageData.currentPage = val;
    this.getListData();
  }

  async getListData(refresh = false) {
    try {
      this.tableLoading = true;
      const api = URL_TABLEHIS;
      const searchObj = _.cloneDeep(this.searchObj);
      const obj = {
        pageData: {},
        search: this.id,
        sortData: {}
      };
      obj.pageData = searchObj.pageData;
      obj.sortData = searchObj.sortData;
      const { data } = await post(api, obj);
      const { columnData, tableData, pageData } = data;
      columnData.forEach((el) => {
        el.value = el.prop;
      });
      columnData.push({ label: this.$t('pa.action.action'), value: 'operator', width: 150 });
      tableData.forEach((el) => {
        el.shareFlag = el.show ? this.$t('pa.action.share') : this.$t('pa.action.buildOneself');
        el.updateTime = dayjs(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
      });
      this.tableData = {
        columnData,
        tableData,
        pageData
      };
      refresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    } finally {
      this.tableLoading = false;
    }
  }
  hasAuthorityDiff(hasRole: any, row: any) {
    return !_.isEmpty(_.toString(row.dataLevelType)) && hasRole && this.tableData.tableData.length > 1;
  }
  hasAuthority(hasRole: any, row: any) {
    return !_.isEmpty(_.toString(row.dataLevelType)) && hasRole;
  }
  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.getListData();
  }
}
</script>

<style scoped lang="scss">
.page-conten {
  overflow: auto;
  background: #fff;
  height: calc(100vh - 176px);
  .content {
    height: 100%;
  }
  .iconfont {
    cursor: pointer;
  }
  .iconfont + .iconfont {
    margin-left: 10px;
  }
}
</style>
