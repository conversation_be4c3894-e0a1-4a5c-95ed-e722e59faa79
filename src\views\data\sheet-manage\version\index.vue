<template>
  <div class="page-conten">
    <div class="content">
      <bs-table
        v-loading="tableLoading"
        :height="'calc(100vh - 327px)'"
        :data="tableData.tableData"
        :column-data="tableData.columnData"
        :page-data="tableData.pageData"
        @handleCurrentChange="handleCurrentChange"
        @handleSortChange="handleSortChange"
        @refresh="getListData"
      >
        <template slot="operator" slot-scope="{ row }">
          <el-tooltip content="回滚" effect="light">
            <i class="iconfont icon-roll-back" @click="rollBack(row)"></i>
          </el-tooltip>
          <el-tooltip content="查看源码" effect="light">
            <i class="iconfont icon-ziyuan" @click="showCode(row)"></i>
          </el-tooltip>
          <el-tooltip content="版本比对" effect="light">
            <i class="iconfont icon-banbenduibi" @click="diffDetail(row)"></i>
          </el-tooltip>
        </template>
      </bs-table>
    </div>
    <preview ref="preview" :title="'源码查看'" :data="sourceCode" />
    <comparison :id="initId" ref="comparison" :title="'版本比对'" :choose-version="chooseVersion" />
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { URL_TABLEHIS, URL_TABLEHIS_ROLLLBACK } from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
import moment from 'moment';
@Component({
  components: {
    preview: () => import('../../modals/flink-sql.vue'),
    comparison: () => import('@/components/version-comparison/index.vue')
  }
})
export default class SheetVersion extends PaBase {
  id = '';
  sourceCode: any = {};
  tableLoading = false;
  searchObj = {
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: { tableVersion: 'DESC' }
  };
  tableData: any = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  chooseVersion: any = '';
  initId = '';
  created() {
    this.id = this.$route.query.id as string;
  }
  activated() {
    this.getListData();
  }
  showCode(row) {
    const api = '/rs/pa/sql/tableHis/sql/sourceCode' + '?resId=' + row.id;
    this.doGet(api).then((resp: any) => {
      this.parseResponse(resp, () => {
        const previewRef: any = this.$refs.preview;
        previewRef.visible = true;
        this.sourceCode = resp.data;
      });
      this.tableLoading = false;
    });
  }
  diffDetail(row) {
    this.initId = row.id;
    this.chooseVersion = row.tableVersion;
    const previewRef: any = this.$refs.comparison;
    previewRef.visible = true;
  }
  rollBack(row: any) {
    const api = URL_TABLEHIS_ROLLLBACK;
    this.$confirm('是否确认回滚?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        this.doPut(api, { tableHisId: row.id }).then((resp: any) => {
          this.parseResponse(resp, () => {
            this.tableLoading = false;
            this.getListData();
          });
        });
      })
      .catch(() => {
        return true;
      });
  }
  handleCurrentChange(val) {
    this.searchObj.pageData.currentPage = val;
    this.getListData();
  }

  getListData() {
    this.tableLoading = true;
    const api = URL_TABLEHIS;

    const searchObj = _.cloneDeep(this.searchObj);
    const obj = {
      pageData: {},
      search: this.id,

      sortData: {}
    };
    obj.pageData = searchObj.pageData;
    obj.sortData = searchObj.sortData;
    this.doPost(api, obj).then((resp: any) => {
      this.parseResponse(resp, () => {
        const { columnData, tableData, pageData } = resp.data;
        columnData.forEach((el) => {
          el.value = el.prop;
        });
        columnData.push({ label: '操作', value: 'operator', width: 150 });
        tableData.forEach((el) => {
          el.shareFlag = el.show ? '分享' : '自建';
          el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
        });
        this.tableData = {
          columnData,
          tableData,
          pageData
        };
      });
      this.tableLoading = false;
    });
  }
  hasAuthorityDiff(hasRole: any, row: any) {
    return (
      !_.isEmpty(_.toString(row.dataLevelType)) && hasRole && this.tableData.tableData.length > 1
    );
  }
  hasAuthority(hasRole: any, row: any) {
    return !_.isEmpty(_.toString(row.dataLevelType)) && hasRole;
  }
  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.getListData();
  }
}
</script>

<style scoped lang="scss">
.page-conten {
  overflow: auto;
  background: #fff;
  height: calc(100vh - 193px);
  .content {
    height: calc(100vh - 193px);
  }
  .iconfont {
    cursor: pointer;
  }
  .iconfont + .iconfont {
    margin-left: 10px;
  }
}
</style>
