<template>
  <el-drawer
    v-drawerDrag
    :size="400"
    :modal="false"
    :with-header="false"
    :visible.sync="display"
    :modal-append-to-body="false"
    @close="handleCloseEvent"
  >
    <div v-loading="loading" class="panel__heart">
      <!-- 基本信息 -->
      <div class="panel__heart__title">
        {{ $t('pa.baseInformation') }}<span class="cancel" @click="handleCloseEvent">×</span>
      </div>
      <item-content
        v-for="(el, index) in baseInfoList"
        :key="index"
        :view-power="el.id"
        :link-power="el.hasAccess"
        :label="el.label"
        :value="el.value"
        :tag="el.tag"
        @click="openNewUrl(el.kind, el.id, el.value, el.type, el.projectId)"
      />
      <!-- 关联信息 -->
      <div>
        <div class="panel__heart__title">{{ $t('pa.blood.relatedInfo') }}</div>
        <item-content v-for="el in relationInfoList" :key="el.label" :label="el.label" :value="el.value" />
      </div>
      <!-- 同源信息 -->
      <div v-if="showSourceInfo">
        <div class="panel__heart__title">
          {{ $t('pa.blood.homologyInfo') }}
          <el-tooltip effect="light" placement="bottom" :content="tip">
            <i class="bs-pro-form-item__icon bs-icon-wenti" style="cursor: pointer"></i>
          </el-tooltip>
        </div>
        <p style="margin: 10px 0">{{ $t('pa.blood.homologyNode') }}：{{ sourceInfo.tableData.length }}</p>
        <!-- sourceInfo -->
        <bs-table
          v-if="sourceInfo.tableData.length"
          :data="sourceInfo.tableData"
          :column-settings="false"
          :column-data="sourceInfo.columnData"
          class="panel__heart__table"
        >
          <span slot="header-action">
            {{ $t('pa.action.action') }}
            <el-tooltip effect="light" placement="bottom" :content="$t('pa.showHomologyGraph')">
              <i class="bs-pro-form-item__icon bs-icon-wenti" style="cursor: pointer"></i>
            </el-tooltip>
          </span>
          <el-button v-if="!row.$isDynamic" slot="action" slot-scope="{ row }" type="text" @click="searchBloold(row)">
            {{ $t('pa.blood.searchblood') }}
          </el-button>
          <template
            v-for="el in sourceInfo.columnData.filter((i) => i.value !== 'action')"
            :slot="el.value"
            slot-scope="{ row }"
          >
            <item-content
              :key="el.value"
              :view-power="el.valueKey"
              :link-power="el.value === 'name' ? row.hasAccess : row.hasServer"
              :value="row[el.value]"
              @click="openNewUrl(el.kind, row[el.valueKey], row[el.value], row[el.typeKey])"
            />
          </template>
        </bs-table>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
import { GET_POINT_DETAIL } from '@/apis/blood-relation';
import { get } from '@/apis/utils/net';
import { Table, GetBaseInfoList, GetRelationInfoList, GetTableInfo } from './utils';

import { hasServiceDetailAccess } from '@/utils';
// 删除一些不需要的字段
const handleParams = (params) => {
  delete params.iconByte;
  delete params.id;
  return params;
};
@Component({
  components: { ItemContent: () => import('./item-content.vue') }
})
export default class Panel extends Vue {
  @Prop({ default: () => ({}) }) params: any;
  @Prop({ default: 'node' }) type!: string;
  @Prop({ default: false }) show!: boolean;
  @Prop({ default: false }) isFullScreen!: boolean;

  loading = false;
  display = false;
  baseInfoList: any[] = []; // 基本信息
  relationInfoList: any[] = []; // 关联信息列表
  sourceInfo: Table = { columnData: [], tableData: [] }; // 同源
  targetInfo: Table = { columnData: [], tableData: [] }; // 其他信息

  // 当节点类型不为流程时 显示同源数据模块
  get showSourceInfo() {
    return this.params.resType != 'JOB';
  }
  get tip() {
    return this.$t('pa.blood.tip10');
  }
  @Watch('show')
  visiable(val) {
    this.display = val;
    if (val) return this.getNodeInfo();
    this.handleData({});
  }

  /* 获取节点信息 */
  async getNodeInfo() {
    try {
      this.loading = true;
      const { success, data, msg } = await get(GET_POINT_DETAIL, handleParams(this.params));
      if (!success) return this.notify(msg);
      this.handleData(data);
    } finally {
      this.loading = false;
    }
  }
  /* 数据处理 */
  handleData(data) {
    this.baseInfoList = GetBaseInfoList(data?.propertiesResource);
    this.relationInfoList = GetRelationInfoList(data?.relationProperties);
    // 同源节点
    this.sourceInfo = GetTableInfo(data?.pageInfo);
  }
  /* 统一消息提示 */
  notify(message, type = 'error') {
    this.$message[type]({ message, el: this.isFullScreen ? '#bloodRelation' : '' });
  }
  /* 处理【关闭】事件 */
  handleCloseEvent() {
    this.handleData({});
    this.$emit('close');
  }
  /* 打开链接 */
  openNewUrl(kind, id, title, type, projectId) {
    let url = '';
    switch (kind) {
      case 'JOB':
        url = `/flow?id=${projectId}&title=${title}&flowId=${id}`;
        break;
      case 'SERVICE':
        url = hasServiceDetailAccess()
          ? `/element/service/${type}/detail?id=${id}&resType=${type}&clusterType=&title=${title}`
          : '';
        break;
      case 'TABLE':
        url = `/data/sheetDetail?id=${id}&status=1&title=${title}&showEdit=false`;
        break;
      case 'VIEW':
        url = `/data/viewDetail?id=${id}&status=2&title=${title}`;
        break;
      case 'CATALOG':
        url = `/data/catalogDetail?title=${title}&id=${id}`;
        break;
      default:
        break;
    }
    url && this.$router.push(url);
  }
  // 同源节点查询血缘关系
  searchBloold({ resType, nameId, serviceType, name, resourceList, hasAccess }) {
    if (!hasAccess) return this.$message.error(this.$t('pa.blood.tip2'));
    this.$router.push({
      path: '/blood-relation',
      query: {
        resType,
        serviceType,
        serviceId: nameId,
        serviceName: name,
        resourceList
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.panel {
  &__heart {
    padding: 0 20px;
    width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    &__title {
      margin-top: 16px;
      font-size: 14px;
      font-weight: 500;
      color: #444444;
      line-height: 20px;
      &:before {
        content: ' ';
        display: inline-block;
        position: relative;
        left: 0;
        top: -1px;
        margin-right: 12px;
        width: 8px;
        height: 8px;
        background: #ff9c00;
      }
    }
    &__table {
      min-width: 400px;
    }
  }
}
.cancel {
  float: right;
  font-size: 16px;
  cursor: pointer;
}
</style>
