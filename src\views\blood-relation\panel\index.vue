<template>
  <el-drawer
    v-drawerDrag
    title="我是标题"
    :visible.sync="drawerShow"
    :size="400"
    :modal="false"
    :modal-append-to-body="false"
    :with-header="false"
    @close="cancel"
  >
    <div v-loading="loading" class="panel__right__detail">
      <div>
        <div class="panel__right__subtitle">
          基本信息<span class="cancel" @click="cancel">×</span>
        </div>
        <div class="base-info">
          <div v-for="(item, index) in data.propertiesResource" :key="index" class="base">
            <span class="left">{{ item.key }}{{ item.key ? ':' : '' }} </span>
            <a
              v-if="item.id"
              style="text-decoration: underline"
              @click="routeChange(item.id, item.value, item.resType, item.serviceType)"
            >
              {{ item.value }}
            </a>
            <span v-else class="right" :title="item.value">{{ item.value }}</span>
          </div>
        </div>
      </div>
      <div v-if="type === 'node'">
        <div class="panel__right__subtitle">关联信息</div>
        <div class="base-info">
          <div v-for="item in Object.keys(data.relationProperties).sort()" :key="item" class="base">
            <span class="left">{{ item }}: </span>
            <span class="right">{{ data.relationProperties[item] }}</span>
          </div>
        </div>
      </div>
      <div v-if="showSource">
        <div class="panel__right__subtitle table">
          {{ type === 'node' ? '同源信息' : '边属性' }}
          <bs-table
            v-if="data.tableData.length"
            :column-data="data.columns"
            :column-settings="false"
            :data="data.tableData"
          >
            <template v-for="title in data.columns" :slot="title.value" slot-scope="{ row }">
              <a
                v-if="title.id"
                :key="title.value"
                @click="
                  routeChange(
                    row[title.id],
                    row[title.value],
                    title.dataType,
                    row[title.serviceValue]
                  )
                "
              >
                {{ row[title.value] }}
              </a>
              <span v-else :key="title.value">{{ row[title.value] }}</span>
            </template>
          </bs-table>
          <bs-table
            v-if="targetTable.tableData.length"
            :column-data="targetTable.columns"
            :column-settings="false"
            :data="targetTable.tableData"
          >
            <template v-for="title in targetTable.columns" :slot="title.value" slot-scope="{ row }">
              <a
                v-if="title.id"
                :key="title.value"
                @click="
                  routeChange(
                    row[title.id],
                    row[title.value],
                    title.dataType,
                    row[title.serviceValue]
                  )
                "
              >
                {{ row[title.value] }}
              </a>
              <span v-else :key="title.value">{{ row[title.value] }}</span>
            </template>
          </bs-table>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
import { GET_POINT_DETAIL, GET_EDGE_DETAIL } from '@/apis/blood-relation';
import { get } from '@/apis/utils/net';

@Component
export default class Panel extends Vue {
  @Prop({ default: () => ({}) }) params!: any;
  @Prop({ default: 'node' }) type!: string;
  @Prop({ default: false }) show!: boolean;
  @Prop({ default: false }) isFullScreen!: boolean;
  loading = false;
  drawerShow = false;
  data = {
    relationProperties: {},
    propertiesResource: [],
    columnData: [],
    tableData: []
  };
  targetTable = {
    columns: [],
    tableData: []
  };

  get showSource() {
    if (this.type === 'node') {
      if (this.params.resType === 'VIEW' || this.params.resType === 'JOB') {
        return false;
      }
    }
    return true;
  }

  @Watch('show')
  visiable(val) {
    this.drawerShow = val;
    if (val) {
      this.type === 'node' ? this.getDetail() : this.getLine();
      this.loading = true;
    } else {
      this.data = {
        relationProperties: {},
        propertiesResource: [],
        columnData: [],
        tableData: []
      };
    }
  }
  cancel() {
    this.data = {
      relationProperties: {},
      propertiesResource: [],
      columnData: [],
      tableData: []
    };
    this.targetTable = {
      columns: [],
      tableData: []
    };
    this.$emit('close');
  }
  // 处理多个表类型
  handle(val) {
    if (Array.isArray(val)) {
    } else {
      return val;
    }
  }
  async getDetail() {
    const { data, success, msg } = await get(GET_POINT_DETAIL, this.params);
    if (success) {
      const { pageInfo, relationProperties } = data;
      this.data = {
        ...data,
        relationProperties: relationProperties || {},
        columns:
          pageInfo &&
          pageInfo.columnData.map((item) => {
            return {
              label: item.label,
              value: item.prop,
              id: item.propertyField,
              dataType: item.dataType,
              serviceValue: item.value
            };
          }),
        tableData: pageInfo ? pageInfo.tableData : []
      };
      this.loading = false;
    } else {
      this.$message.success({
        message: msg,
        el: this.isFullScreen ? '#bloodRelation' : ''
      });
      this.loading = false;
    }
  }

  async getLine() {
    const { source, target } = this.params;
    const { data, success, msg } = await get(GET_EDGE_DETAIL, {
      source: `${source.id} ${source.resType}`,
      target: `${target.id} ${target.resType}`
    });
    if (success) {
      const { pageInfo, relationProperties, targetPageInfo } = data;
      this.data = {
        ...data,
        relationProperties: relationProperties || {},
        columns:
          pageInfo &&
          pageInfo.columnData.map((item) => {
            return {
              label: item.label,
              value: item.prop,
              id: item.propertyField,
              dataType: item.dataType,
              serviceValue: item.value
            };
          }),
        tableData: pageInfo && pageInfo.tableData
      };

      this.targetTable = {
        columns:
          targetPageInfo &&
          targetPageInfo.columnData.map((item) => {
            return {
              label: item.label,
              value: item.prop,
              id: item.propertyField,
              dataType: item.dataType,
              serviceValue: item.value
            };
          }),
        tableData: targetPageInfo ? targetPageInfo.tableData : []
      };
      this.loading = false;
    } else {
      this.$message.success({
        message: msg,
        el: this.isFullScreen ? '#bloodRelation' : ''
      });
      this.loading = false;
    }
  }
  routeChange(id, title, type, serviceType) {
    if (type === 'SERVICE') {
      this.$router.push(
        `/element/clusters/${serviceType}/detail?id=${id}&resType=${serviceType}&clusterType=&title=${title}`
      );
    }
    if (type === 'TABLE') {
      this.$router.push(`/data/sheetDetail?id=${id}&status=1&title=${title}&showEdit=false`);
    }
    if (type === 'VIEW') {
      this.$router.push(`/data/viewDetail?id=${id}&status=2&title=${title}`);
    }
    if (type === 'JOB') {
      this.$router.push(`/flow?title=${title}&flowId=${id}`);
    }
  }
}
</script>

<style lang="scss" scoped>
.panel {
  &__right {
    &__title {
      line-height: 55px;
      border-bottom: 1px solid #f6f6f6;
      box-shadow: 0px 1px 8px 0px rgb(0 0 0 / 5%);
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 14px;
    }
    &__detail {
      padding: 10px 20px;
    }
    &__subtitle {
      margin-bottom: 10px;
    }
    &__subtitle:before {
      content: ' ';
      position: relative;
      left: 0;
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 12px;
      background: #ff9c00;
    }
  }
}
.base {
  width: 300px;
  margin-bottom: 8px;
  display: flex;
}
.left {
  margin-right: 10px;
  min-width: 60px;
}
.cancel {
  float: right;
  font-size: 16px;
  cursor: pointer;
}
.base-info {
  display: flex;
  flex-wrap: wrap;
  padding-left: 22px;
}
.right {
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.table {
  min-width: 400px;
}
</style>
