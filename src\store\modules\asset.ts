import { SET_ASSET_FIELDS, SET_ASSET_FUNCS } from '../event-name';

const assetModule: AssetModule = {
  assetFields: [],
  assetFuncs: []
};
export default {
  state: () => assetModule,
  mutations: {
    [SET_ASSET_FIELDS](state: AssetModule, payload: any[]) {
      state.assetFields = payload;
    },
    [SET_ASSET_FUNCS](state: AssetModule, payload: any[]) {
      state.assetFuncs = payload;
    }
  },
  getters: {
    assetFields: ({ assetFields }: AssetModule) => assetFields,
    assetFuncs: ({ assetFuncs }: AssetModule) => assetFuncs
  }
};
