import { UPDATE_ASSET_FIELDS, UPDATE_ASSET_FUNCS } from '@/store/event-names/mutations';
const assetFields: any = [];
const assetFuncs: any = [];
export default {
  state: () => ({ assetFields, assetFuncs }),
  mutations: {
    [UPDATE_ASSET_FIELDS](state: IState, payload: any) {
      state.assetFields = payload;
    },
    [UPDATE_ASSET_FUNCS](state: IState, payload: any) {
      state.assetFuncs = payload;
    }
  }
};
