.page-content {
  height: calc(100vh - 110px);
  overflow-y: auto;
  .input {
    ::v-deep .el-input__inner {
      border-color: inherit;
    }
  }
  .field-style {
    width: 100%;
    max-height: 442px;
    overflow-y: auto;
    margin-bottom: 10px;
    border: 1px solid #f1f1f1;
    border-bottom: none;
  }
  .sheet-bar {
    background-color: rgb(255, 255, 255);
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #d4dce2;
    border-left: none;
    padding: 0 20px;
    font-size: 14px;
    .span {
      cursor: pointer;
    }
    .blod {
      font-weight: bolder;
    }
  }
  .first_content {
    border: unset;
    background-color: #f6f7f9;
    height: auto;
    .tableName {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
    .detail_content {
      min-height: 0px;
      margin-bottom: 20px;
      .noData {
        height: 60px;
        line-height: 60px;
        color: #aaa;
        text-align: center;
      }
    }
    .no-bg-color-content {
      background-color: #ffffff;
      .tab-title {
        .info {
          width: 100%;

          display: flex;
          align-items: center;
          justify-content: space-between;
          .info-bar {
            display: flex;
            white-space: pre;
            align-items: center;
          }
        }
      }
      .tab-content {
        border-top: 1px solid #f1f1f1;
        padding: 20px 25px;
        .item {
          margin-bottom: 20px;
        }
        .top {
          div {
            width: 50%;
            margin-right: 10px;
          }
        }
        .tabTip {
          color: red;
          float: left;
        }
        .infoDetail {
          display: flex;
          div {
            width: 240px;
            margin-right: 10px;
          }
        }
        .service-row {
          display: flex;
        }
        .last_table {
          width: 100%;
          min-height: 150px;
          max-height: 400px;
          overflow: auto;
          border: 1px solid #f1f1f1;
          border-bottom: none;
        }
        .table-list {
          padding: 1px;
          min-height: 300px;
          background: #fff;
          .bolder {
            font-weight: 700;
          }
          .even:nth-child(even) {
            background: #fafdff;
          }
          .advanced-item {
            border: 1px solid #e2e2e2;
            margin: 10px;
            padding: 15px;
            .item-title {
              display: flex;
              justify-content: space-between;
              margin-bottom: 10px;
            }
          }
          .el-row .grid-content {
            text-align: center;
          }
        }
        .connectList {
          min-height: 100px;
          overflow-y: hidden;
          overflow-x: hidden;
        }
      }
    }
  }
  .el-row-list {
    height: 50px;
    line-height: 50px;
    display: flex;
    justify-content: space-around;

    .grid-content {
      display: flex;
      justify-content: center;
      align-items: center;
      min-width: 210px;
      .data-card-icon {
        font-size: 19px;
      }
    }
  }
  .head-info {
    line-height: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
    .name {
      font-size: 14px;
      font-weight: 500;
    }
  }
  .code-content {
    box-sizing: border-box;
    height: calc(100vh - 179px);
    padding: 20px;
    background: #fff;
    overflow: hidden;
  }
  .infoDetail {
    margin-bottom: 14px;
  }
}
