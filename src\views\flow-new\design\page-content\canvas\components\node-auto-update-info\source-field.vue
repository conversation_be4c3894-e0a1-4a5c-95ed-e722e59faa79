<script lang="tsx">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { OriginFields } from '../../interface';
@Component
export default class SourceField extends Vue {
  @Prop({ default: () => [] }) data!: OriginFields;
  @Prop() type!: 'add' | 'del';
  get label() {
    return {
      add: '增加',
      del: '删除'
    }[this.type];
  }
  get className() {
    return {
      add: 'add-field',
      del: 'del-field'
    }[this.type];
  }
  render() {
    const data = Array.isArray(this.data) ? this.data : [];
    const showText = data.reduce((cur, val) => {
      return (cur += `[${val.name}]、`);
    }, '');
    return (
      <el-tooltip content={showText.slice(0, -1)} placement="top" effect="light">
        <p class="line">
          {this.label}({data.length})：
          {data.map((item, idx) => {
            return (
              <span class="field">
                <i>[</i>
                <i class={this.className}>{item.name}</i>
                <i>]</i>
                {idx < this.data.length - 1 && <i>、</i>}
              </span>
            );
          })}
        </p>
      </el-tooltip>
    );
  }
}
</script>
<style lang="scss" scoped>
.line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.field i {
  font-style: normal;
}
.add-field {
  color: #54c958;
}
.del-field {
  color: #ff5353;
}
</style>
