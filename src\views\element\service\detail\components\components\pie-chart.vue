<template>
  <pro-grid v-loading="loading" type="info" :title="title">
    <el-button slot="operation" type="primary" @click="$emit('refresh')">{{ $t('pa.action.refresh') }}</el-button>
    <div v-show="pieData.length > 0" ref="divRef" class="pie-chart-body"></div>
    <div v-if="pieData.length < 1" class="pie-chart-body pie-chart-body--noData">{{ $t('pa.noData') }}</div>
  </pro-grid>
</template>
<script lang="ts">
import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator';
import echarts from '@/plugins/use-echarts';
const resizer = require('element-resize-detector')();
@Component
export default class PieChart extends Vue {
  @Prop({ default: '' }) title!: string;
  @Prop({ default: '' }) name!: string;
  @Prop({ default: false }) loading!: boolean;
  @Prop({ default: () => [] }) pieData!: any[];
  @Ref('divRef') readonly div!: HTMLDivElement;

  chart: any = null;

  @Watch('pieData', { deep: true })
  onPieDataChange() {
    this.renderChart();
  }

  mounted() {
    this.chart = echarts.init(this.div);
    this.renderChart();
    resizer.listenTo(this.div, this.resizeChart);
  }
  beforeDestroy() {
    resizer.removeListener(this.div, this.resizeChart);
  }

  renderChart() {
    if (!this.chart) return;
    const options = {
      tooltip: {
        trigger: 'item',
        formatter(params: any) {
          return `${params.seriesName}</br>${params.data.name}: ${params.data.des} (${params.percent}%)</br>总共: ${params.data.total}`;
        }
      },
      legend: { orient: 'vertical', x: 'left' },
      series: [
        {
          name: this.name,
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            normal: { show: false, position: 'center' },
            emphasis: {
              show: true,
              textStyle: { fontSize: '30', fontWeight: 'bold' }
            }
          },
          labelLine: { normal: { show: false } },
          data: this.pieData
        }
      ]
    };
    this.chart.setOption(options);
  }
  resizeChart() {
    this.chart && this.chart.resize();
  }
}
</script>
<style lang="scss" scoped>
.pie-chart-body {
  padding: 20px 20px 0;
  width: 100%;
  height: 400px;
  color: #aaa;
  &--noData {
    text-align: center;
    line-height: 400px;
  }
}
</style>
