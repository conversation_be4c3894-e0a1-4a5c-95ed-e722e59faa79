<template>
  <div class="config-container">
    <!-- xia -->
    <el-form
      v-for="(el, index) in conditions"
      :key="el.name"
      ref="form"
      :model="el"
      class="config-item"
      :disabled="disabled"
      :class="{ 'config-item--height': judgeShowRule(el) }"
    >
      <!-- 头部 -->
      <logic-header
        :index="index"
        :disabled="disabled"
        :method-list="methodList"
        :data.sync="conditions[index]"
        :show-expression="showExpressions[el.name]"
        :method-source-code.sync="methodCode"
        :expression="expressionMapping[el.name]"
        @change="handleChange"
        @copy="handleCopy"
        @delete="handleDelete"
      />
      <!-- 配置详情 -->
      <logic-main
        v-if="judgeShowRule(el, true)"
        :index="index"
        :disabled="disabled"
        :field-list="fieldList"
        :data.sync="conditions[index]"
        :field-type-dict="fieldTypeDict"
        @change="setShow"
      />
    </el-form>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import LogicHeader from './header.vue';
import LogicMain from './main.vue';

@Component({ components: { LogicHeader, LogicMain } })
export default class LogicConfig extends Vue {
  @PropSync('data', { default: () => [] }) conditions!: any[];
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => [] }) fieldList!: any[];
  @Prop({ default: () => ({}) }) fieldTypeDict!: any[];
  @Prop({ default: () => ({}) }) expressionMapping!: any;
  @Prop({ type: Array, default: () => [] }) methodList!: any[];
  @PropSync('methodSourceCode', { default: () => ({}) }) methodCode!: any;

  private showExpression = false;
  private showExpressions: any = {};
  private activeNames = {};
  private methodRules: any[] = [
    {
      required: true,
      message: '请选择方法',
      trigger: 'change'
    }
  ];
  // private methodCode: any = {};
  // @Watch('conditions', { immediate: true })
  // handleListChange(value) {
  //   this.activeNames = value.reduce((pre, { name }) => {
  //     pre[name] = Array.isArray(this.activeNames[name]) ? this.activeNames[name] : [name];
  //     return pre;
  //   }, {});
  // }

  judgeShowRule({ funcType, funcArgs }, defaultVal = false) {
    if (['PRIVATE', 'SHARE'].includes(funcType)) {
      return defaultVal ? funcArgs.length : !funcArgs.length;
    }
    return defaultVal;
  }
  /* 处理函数名修改事件 */
  handleChange(index: number) {
    this.$emit('change', index);
    this.setShow(index);
  }
  /* 处理复制条件事件 */
  handleCopy(index: number) {
    this.$emit('copy', index);
  }
  /* 处理删除条件事件 */
  async handleDelete(index: number) {
    const { name } = this.conditions[index];
    await this.$confirm(`您确定要删除配置条件${name}吗?`, '提示', { type: 'warning' });
    this.conditions.splice(index, 1);
    this.conditions.forEach((el, order) => {
      this.$set(this.conditions[order], 'name', String.fromCharCode(order + 65));
    });
  }

  setShow(index: number, list?: string[]) {
    const el = this.conditions[index];
    if (list) {
      this.$set(this.showExpressions, el.name, list.length < 1);
    }
    const result = this.showExpressions[el.name] || this.judgeShowRule(el);
    this.$set(this.showExpressions, el.name, result);
  }
  validate() {
    return Promise.all((this.$refs.form as any).map((el) => el.validate()));
  }
}
</script>
<style lang="scss" scoped>
.config {
  &-container {
    height: 485px;
    overflow-x: hidden;
    overflow-y: auto;
    background: #ffffff;
  }

  &-item {
    padding: 16px 20px;
    margin: 0 auto 16px;
    width: calc(100% - 40px);
    min-height: 100px;
    background: #fafbfc;
    border-radius: 4px;
    border: 1px solid #f1f1f1 !important;
    box-sizing: border-box;

    ::v-deep .el-autocomplete {
      width: 100%;
    }

    &--height {
      padding-bottom: 0;
      min-height: unset;
    }

    ::v-deep .el-form {
      &-item {
        margin: 0;

        &.is-error {
          margin-bottom: 20px;
        }
      }
    }
  }
}
</style>
