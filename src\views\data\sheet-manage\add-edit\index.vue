<template>
  <pro-page :title="title" :loading="loading" class="page-content">
    <div slot="operation">
      <el-button
        v-if="status !== '1'"
        type="primary"
        style="marginleft: 10px"
        @click="submit(true)"
      >
        SQL预览
      </el-button>
      <el-button v-if="status !== '1'" type="primary" style="marginleft: 10px" @click="submit()">
        保存
      </el-button>
      <el-button
        v-if="authStatus && status === '1' && !showEdit"
        type="primary"
        style="marginleft: 10px"
        @click="status = '2'"
      >
        编辑
      </el-button>
    </div>
    <transition name="el-zoom-in-center">
      <div style="margin-top: 20px">
        <div class="first_content">
          <!-- 表信息 -->
          <div class="detail_content">
            <div class="no-bg-color-content">
              <div class="tab-title">
                <div class="title-text">
                  <span>表信息</span>
                </div>
              </div>
              <div class="tab-content" style="border: none">
                <el-row>
                  <el-col :span="20" style="diplay: flex">
                    <el-form
                      v-if="status !== '1'"
                      ref="chartForm"
                      :model="chartInfo"
                      class="data-form-inline"
                      :rules="chartRules"
                      label-width="70px"
                      :label-position="'right'"
                    >
                      <el-row>
                        <el-col style="display: flex">
                          <el-form-item label="表名" prop="tableName">
                            <el-select
                              v-model="chartInfo.tableNamePrefix"
                              placeholder="请选择前缀名"
                              filterable
                              clearable
                            >
                              <el-option
                                v-for="item in chartList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              />
                            </el-select>
                            <el-input
                              maxlength="100"
                              :value="chartInfo.tableName"
                              placeholder="请填写表名"
                              style="width: 193px; margin: 0 10px"
                              @input="(e) => (chartInfo.tableName = e.replace(/[^\w\d]/g, ''))"
                            />
                            <el-select
                              v-model="chartInfo.tableNameSuffix"
                              placeholder="请选择后缀名"
                              filterable
                              clearable
                            >
                              <el-option
                                v-for="item in chartListBack"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              />
                            </el-select>
                          </el-form-item>
                        </el-col>
                      </el-row>

                      <el-form-item label="中文名" prop="tableNameCn" style="diplay: inline">
                        <el-input v-model="chartInfo.tableNameCn" maxlength="20" />
                      </el-form-item>

                      <el-form-item label="业务口径" prop="businessExplain">
                        <el-input
                          v-model="chartInfo.businessExplain"
                          type="textarea"
                          row="5"
                          style="width: 100%"
                          placeholder="请输入业务口径"
                          maxlength="50"
                        />
                      </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
          <!-- 服务信息 -->
          <div class="detail_content">
            <div class="no-bg-color-content">
              <div class="tab-title">
                <div class="title-text">
                  <span>服务信息</span>
                </div>
              </div>
              <div class="tab-content" style="padding: 20px 25px; border: none">
                <el-form
                  v-if="status !== '1'"
                  ref="serviceForm"
                  :inline="true"
                  :model="info"
                  class="data-form-inline"
                  :rules="infoRules"
                >
                  <el-form-item label="服务类型" prop="resType">
                    <el-select
                      v-model="info.resType"
                      placeholder="服务类型"
                      @change="resTypeChange"
                    >
                      <el-option
                        v-for="item in resTypeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="服务" prop="service">
                    <el-select
                      v-model="info.service"
                      placeholder="请选择服务"
                      filterable
                      @change="serviceChange(false)"
                    >
                      <el-option
                        v-for="item in serviceList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                  <!-- 服务信息配置项 level1 -->
                  <el-form-item
                    v-if="configInfo.level1"
                    :label="configInfo.level1.label"
                    :prop="configInfo.level1.model"
                  >
                    <el-input
                      v-if="configInfo.level1.type === 'input'"
                      v-model="info[configInfo.level1.model]"
                    />

                    <el-select
                      v-if="configInfo.level1.type !== 'input'"
                      v-model="info[configInfo.level1.model]"
                      :placeholder="configInfo.placeholder"
                      :loading="selectLoading"
                      filterable
                      @change="level1Change(configInfo.level1)"
                    >
                      <el-option
                        v-for="item in level1List"
                        :key="item.value"
                        :label="item.key"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                  <!-- level2 -->
                  <el-form-item
                    v-if="configInfo.level1 && configInfo.level1.type === 'cascader'"
                    :label="configInfo.level1.componentCfg.level2.label"
                    :prop="configInfo.level1.componentCfg.level2.model"
                    :rules="{
                      required: true,
                      message: configInfo.level1.componentCfg.level2.label + '不能为空',
                      trigger: 'blur'
                    }"
                  >
                    <el-select
                      v-model="info[configInfo.level1.componentCfg.level2.model]"
                      :placeholder="configInfo.level1.componentCfg.level2.placeholder"
                      filterable
                      @change="level2Change($event, configInfo.level1)"
                    >
                      <el-option
                        v-for="item in level2List"
                        :key="item.value"
                        :label="item.key"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-form>
                <div class="service-address">服务地址 {{ serviceUrl }}</div>
              </div>
            </div>
          </div>
          <!-- 字段信息 -->
          <div class="detail_content">
            <div class="no-bg-color-content">
              <div class="tab-title">
                <div class="info">
                  <div class="title-text">表字段</div>
                  <div
                    v-if="info.resType !== 'HIVE'"
                    class="info-bar"
                    style="display: flex; white-space: pre; align-items: center"
                  >
                    <el-checkbox v-model="isSuperChart">高级字段</el-checkbox>
                    <el-tooltip
                      effect="light"
                      placement="top"
                      :content="'flink内置字段，处理时间、水位线等'"
                    >
                      <i
                        class="iconfont icon-wenhao"
                        style="margin-left: 10px; cursor: pointer"
                      ></i>
                    </el-tooltip>
                  </div>
                </div>
              </div>
              <div class="tab-content">
                <div class="head-info">
                  <div
                    class="name"
                    style="display: flex; align-items: center; word-break: keep-all"
                  >
                    基础字段
                    <el-input
                      v-model="searchValue"
                      placeholder="输入字段名模糊搜索"
                      style="margin-left: 10px"
                      @input="searchField"
                    >
                      <i slot="prefix" class="el-input__icon el-icon-search"></i>
                    </el-input>
                  </div>
                  <div style="display: flex; white-space: pre; align-items: center">
                    <el-button
                      :disabled="status === '1'"
                      type="primary"
                      size="small"
                      @click="delChecked"
                    >
                      删除
                    </el-button>
                    <el-button
                      v-if="status !== '0'"
                      type="primary"
                      size="small"
                      style="marginleft: 10px"
                      @click="exportFile"
                    >
                      导出
                    </el-button>
                    <div v-if="status !== '1'">
                      <el-button
                        type="primary"
                        style="margin-left: 10px"
                        size="small"
                        @click="upLoad"
                      >
                        导入
                      </el-button>
                      <div
                        v-if="configInfo.autoSync && !configInfo.autoSync.disable"
                        style="display: inline; margin: 0 10px"
                      >
                        <el-button v-if="!update" type="primary" size="small" @click="autoSync">
                          同步
                        </el-button>
                        <el-tooltip
                          v-if="update"
                          content="表结构变更，请及时同步"
                          effect="light"
                          placement="top"
                          :value="update"
                          :disabled="disabled"
                        >
                          <el-button type="primary" size="small" @click="autoSync">
                            同步
                          </el-button>
                        </el-tooltip>
                      </div>
                      <el-button
                        type="primary"
                        size="small"
                        style="marginleft: 10px"
                        @click="addInfo"
                      >
                        添加字段
                      </el-button>
                    </div>
                  </div>
                </div>
                <el-table
                  :data="tableData"
                  class="field-style"
                  size="mini"
                  @selection-change="handleTableChecked"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column prop="fieldName" min-width="130">
                    <template slot="header">
                      <span style="color: red; margin-right: 4px">*</span>
                      <span>字段名</span>
                    </template>
                    <template slot-scope="{ row }">
                      <el-input
                        :ref="`table${row.id}`"
                        v-model="row.fieldName"
                        maxlength="50"
                        :disabled="status === '1'"
                        class="input"
                        :style="
                          status !== '1' && getrepeat1(row.fieldName, row.columnFamily)
                            ? 'border-color: red'
                            : 'border-color: #dcdfe6'
                        "
                        @input="(e) => (row.fieldName = validSe(e))"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="fieldNameCn" min-width="130">
                    <template slot="header">
                      <span>中文名</span>
                    </template>
                    <template slot-scope="{ row }">
                      <el-input
                        v-model="row.fieldNameCn"
                        maxlength="20"
                        :disabled="status === '1'"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="fieldType" min-width="130">
                    <template slot="header">
                      <span style="color: red; margin-right: 4px">*</span>
                      <span>字段类型</span>
                    </template>
                    <template slot-scope="{ row }">
                      <el-select
                        v-model="row.fieldType"
                        style="width: 100%"
                        placeholder="请选择"
                        allow-create
                        default-first-option
                        filterable
                        :disabled="status === '1'"
                      >
                        <el-option
                          v-for="item in typeEnums"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </template>
                  </el-table-column>
                  <!-- 列蔟 -->
                  <el-table-column
                    v-if="info.resType === 'HBASE'"
                    prop="columnFamily"
                    label="列簇"
                    min-width="130"
                  >
                    <template slot-scope="{ row }">
                      <el-input v-model="row.columnFamily" :disabled="status === '1'" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-if="info.resType !== 'HDFS'"
                    prop="primaryKey"
                    label="主键"
                    min-width="130"
                  >
                    <template slot-scope="{ row }">
                      <el-switch
                        v-model="row.primaryKey"
                        active-value="1"
                        inactive-value="0"
                        :disabled="status === '1'"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-if="info.resType === 'HIVE' || info.resType === 'HDFS'"
                    prop="partition"
                    label="分区"
                    min-width="130"
                  >
                    <template slot-scope="{ row }">
                      <el-switch
                        v-model="row.partition"
                        active-value="1"
                        inactive-value="0"
                        :disabled="status === '1' || info.resType !== 'HIVE'"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="businessExplain" min-width="130">
                    <template slot="header">
                      <span>业务口径</span>
                    </template>
                    <template slot-scope="{ row }">
                      <el-input
                        v-model="row.businessExplain"
                        maxlength="100"
                        name="bus"
                        :disabled="status === '1'"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column v-if="status !== '1'" label="操作" min-width="60">
                    <template slot-scope="{ row }">
                      <i
                        class="el-icon-delete"
                        style="font-size: 18px; cursor: pointer"
                        @click="deleteTableList(row.id, row.fieldName)"
                      ></i>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  class="pagination"
                  background
                  :current-page.sync="pageData.currentPage"
                  :page-size="pageData.pageSize"
                  :page-sizes="[10, 20, 40, 80, 100]"
                  :total="total"
                  :pager-count="5"
                  layout="slot, total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                >
                  <template slot>
                    <div v-if="repeatInfo() !== 0" class="tabTip">
                      列表内存在{{ `${repeatInfo()}` }}个重复字段，请删除不必要的字段
                    </div>
                  </template>
                </el-pagination>
                <SuperField
                  v-show="isSuperChart"
                  ref="superFields"
                  :fields="advanceChartList"
                  :table="cloneTableData"
                  :status="status"
                  :error="error"
                />
              </div>
            </div>
          </div>
          <!-- 连接器 -->
          <div class="detail_content" :style="status !== '1' && 'margin-bottom: 0px'">
            <div class="no-bg-color-content">
              <div class="tab-title">
                <div class="title-text">
                  <span>连接器</span>
                  <el-select
                    v-if="allProperties.length > 1"
                    v-model="ConnectSelectValue"
                    style="margin-left: 15px"
                    :disabled="status === '1'"
                    @change="contentSelect($event)"
                  >
                    <el-option
                      v-for="element in allProperties"
                      :key="element.id"
                      :label="element.componentName"
                      :value="element.id"
                    />
                  </el-select>
                </div>
                <div
                  v-if="info.resType !== 'HIVE'"
                  class="info-bar"
                  style="display: flex; white-space: pre; align-items: center"
                >
                  <el-checkbox v-model="isSuperConnector">高级连接器</el-checkbox>
                  <el-tooltip effect="light" placement="top" :content="'连接器高级属性配置'">
                    <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer"></i>
                  </el-tooltip>
                </div>
              </div>
              <div v-if="connectList.length" class="tab-content" style="overflow: auto">
                <div class="head-info">
                  <div class="name">基础连接器</div>
                </div>
                <el-collapse-transition>
                  <el-row>
                    <el-col :span="24">
                      <div class="table-list connectList">
                        <el-row style="background: #fafdff" class="el-row-list">
                          <el-col :span="6" style="justify-content: center; display: flex">
                            <div class="grid-content bg-purple bolder">属 性</div>
                          </el-col>
                          <el-col :span="18">
                            <div class="grid-content bg-purple bolder">值</div>
                          </el-col>
                        </el-row>
                        <div v-if="noData" class="noData">暂无数据</div>
                        <el-row
                          v-for="item in connectList"
                          :key="item.name"
                          :class="{ even: item.even }"
                        >
                          <div v-if="!item.needHide" class="el-row-list">
                            <el-col :span="6" style="justify-content: center; display: flex">
                              <div class="grid-content bg-purple" style="justify-content: flex-end">
                                <p style="text-align: center; min-width: 150px">
                                  <span v-if="item.required" style="color: red; margin-right: 2px">
                                    *
                                  </span>
                                  {{ item.name }}
                                </p>
                                <el-tooltip
                                  class="item"
                                  effect="light"
                                  :content="item.keyExplain"
                                  placement="top"
                                >
                                  <i
                                    class="iconfont icon-wenhao"
                                    style="margin-left: 10px; cursor: pointer; font-weight: 700"
                                  ></i>
                                </el-tooltip>
                              </div>
                            </el-col>
                            <el-col :span="18">
                              <div class="grid-content bg-purple">
                                <el-input
                                  v-if="item.pageType === 'INPUT_TYPE' && !item.encrypted"
                                  :value="item.defaultValue"
                                  maxlength="128"
                                  :disabled="status === '1' || item.forbidOperate"
                                  @input="
                                    (e) => (item.defaultValue = e.replace(/[\u4E00-\u9FA5]/g, ''))
                                  "
                                />
                                <input
                                  name="yyy"
                                  type="text"
                                  style="display: none; width: 0; height: 0"
                                />
                                <el-input
                                  v-if="item.encrypted"
                                  v-model="item.defaultValue"
                                  auto-complete="off"
                                  type="password"
                                  :disabled="status === '1' || item.forbidOperate"
                                />

                                <el-select
                                  v-if="item.pageType !== 'INPUT_TYPE' && !item.encrypted"
                                  v-model="item.defaultValue"
                                  style="width: 100%"
                                  :disabled="status === '1' || item.forbidOperate"
                                >
                                  <el-option
                                    v-for="element in item.items"
                                    :key="element"
                                    :label="element"
                                    :value="element"
                                  />
                                </el-select>
                              </div>
                            </el-col>
                          </div>
                        </el-row>
                      </div>
                    </el-col>
                  </el-row>
                </el-collapse-transition>
                <div v-show="isSuperConnector">
                  <div class="head-info">
                    <div class="name">高级连接器</div>
                    <div>
                      <el-button
                        v-if="status !== '1'"
                        type="primary"
                        size="small"
                        style="marginleft: 10px"
                        @click="propertyAdd"
                      >
                        添加属性
                      </el-button>
                    </div>
                  </div>
                  <el-table
                    :data="advancedConnect"
                    style="
                      width: 100%;
                      max-height: 400px;
                      overflow: auto;
                      border: 1px solid #f1f1f1;
                      border-bottom: none;
                    "
                    size="mini"
                  >
                    <el-table-column prop="key" label="连接器属性" min-width="130">
                      <template slot-scope="{ row }">
                        <el-input
                          :value="row.key"
                          :disabled="status === '1'"
                          maxlength="100"
                          @input="(e) => (row.key = e.replace(/[\u4E00-\u9FA5]/g, ''))"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column prop="value" label="值" min-width="130">
                      <template slot-scope="{ row }">
                        <el-input
                          :value="row.value"
                          :disabled="status === '1'"
                          maxlength="100"
                          @input="(e) => (row.value = e.replace(/[\u4E00-\u9FA5]/g, ''))"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column v-if="status !== '1'" label="操作" width="80">
                      <template slot-scope="{ $index }">
                        <i
                          class="el-icon-delete"
                          style="font-size: 18px"
                          @click="deleteProperty($index)"
                        ></i>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </div>
          <!-- 引用关系 -->
        </div>
        <reference-relation
          v-if="status === '1'"
          :id="this.$route.query.id"
          :relation-type-list="relationTypeList"
          relation="table"
        />
      </div>
    </transition>
    <upload
      :visible="dialogVisible"
      :title="dialogTitle"
      :res-type="info.resType"
      @close="closeDialog"
    />
    <sync-modal
      v-if="syncModal"
      :res-type="info.resType"
      :params="syncModalParam"
      :show-list="cloneTableData"
      @close="closeSyncModal"
      @handleCheck="syncChartField"
      @checkAll="syncChartField"
    />
    <save-confirm ref="saveConfirm" @saveConfirmCallback="saveConfirmCallback" />
    <preview ref="preview" :title="'SQL预览'" :data="sourceCode" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import * as _ from 'lodash';
import { cloneDeep } from 'lodash';
import {
  URL_CONNECTORINFO,
  URL_FINDBYCODE,
  URL_GETSUBBYPARENT,
  URL_ITEMADD,
  URL_TABLE_EXPORT,
  URL_LISTBYTYPE,
  URL_LISTBYTYPE_SUFFIX,
  URL_SQL_PREVIEW,
  URL_TABLE_FIND_BY_ID,
  URL_TABLE_RESCONF,
  URL_UPDATE,
  URLLISTRES,
  URLTYPE_SQL
} from '@/apis/commonApi';
import { vm } from '@/main';
import ReferenceRelation from '@/views/data/components/reference-relation.vue';
import {
  validateConnect,
  validateField,
  validateAdvancedConnect,
  valideFieldInfo,
  getTitle,
  getrepeat,
  repeatNum,
  isShowFieldColumn
} from './index';
@Component({
  name: 'DataAddEdit',
  components: {
    upload: () => import('./../../modals/upload.vue'),
    'save-confirm': () => import('./../../modals/save-confirm.vue'),
    preview: () => import('../../modals/flink-sql.vue'),
    'sync-modal': () => import('../../modals/sync.vue'),
    SuperField: () => import('./superFiels.vue'),
    ReferenceRelation
  }
})
export default class DataAddEdit extends PaBase {
  resTypeList: any = [];
  statusType = '';
  status: any = '0';
  loading = false;
  level1List: any = [];
  level2List: any = [];
  dialogVisible = false;
  dialogTitle: any = '';
  saveData: any = {};
  authStatus = false;
  sourceCode: any = {};
  selectLoading = false;
  showProperties = true;
  originalConnectList: any = []; // 原始连接器
  // 页面标题
  title = '';
  //待完善的类型
  state = 0;
  // 分享不展示编辑
  showEdit = false;
  info = {
    resType: '', // 服务类型
    service: ''
  };
  chartInfo = {
    tableNamePrefix: '', // 表前缀
    tableNameSuffix: '', // 表后缀
    tableName: '',
    tableNameCn: '',
    businessExplain: ''
  };
  configInfo: any = {};
  id: any;
  ConnectSelectValue = ''; // 连接器下拉框选择值
  typeEnums: any = [];
  connectList: any = []; // 连接器字段
  serviceList: any = [];
  dataSourceList: any = [];
  advanceChartList: any = [];
  advancedConnect: any = [];
  noData = false;
  advancedPropertyList: any = []; // 表属性
  ConnectSelect = []; // 连接器选项下拉框
  showConnent = true;
  infoRules = {
    resType: [{ required: true, message: '请选择服务类型', trigger: 'blur' }],
    service: [{ required: true, message: '请选择服务', trigger: 'blur' }]
  };

  tableData: any = []; // 字段信息

  chartRules = {
    tableName: [{ required: true, message: '请填写表名', trigger: 'blur' }]
  };

  chartList: any = []; // 表前缀列表
  chartListBack: any = []; //表后缀列表
  relationTypeList = [
    {
      label: '流程',
      value: 'JOB'
    },
    {
      label: '视图',
      value: 'VIEW'
    }
  ];
  //自动同步弹框
  syncModal = false;
  syncModalParam = {};
  // 默认字段的ID
  tabId = 0;
  multipleSelectList = [];
  // 添加字段
  isSuperChart = false;
  isSuperConnector = false;
  pageData = {
    currentPage: 1,
    pageSize: 10,
    total: 0
  };
  cloneTableData: any = [];
  // 是否提示表变更信息
  update = false;
  disabled = false;
  // 是否为复制信息
  copy = false;
  // 高级字段校验提示信息
  error: unknown = '';
  // 输入查询字段值
  searchValue = '';
  allTableData: any = [];
  // 查询字段
  searchField(val) {
    if (!this.allTableData.length) {
      this.allTableData = _.cloneDeep(this.cloneTableData);
    }
    this.searchValue = val;
    if (val) {
      this.cloneTableData = this.allTableData.filter((item: any) => {
        return item.fieldName && item.fieldName.indexOf(val) !== -1;
      });
    } else {
      this.cloneTableData = _.cloneDeep(this.allTableData);
    }
    this.handleCurrentChange(1);
  }
  // 根据字段判断redis连接器数据
  @Watch('cloneTableData')
  dataChange(val) {
    if (this.info.resType === 'REDIS') {
      this.connectList = isShowFieldColumn(val, this.connectList);
    }
  }

  get serviceUrl() {
    const { service } = this.info;
    const niu = this.serviceList.find((item) => item.value === service);
    return niu && niu.url;
  }

  created() {
    this.onCreated();
  }
  // 字段名不能有特殊字符
  validSe(s) {
    const pattern = new RegExp(
      "[`~!#$%^&*()=|{}':;',\\[\\].<>/?~！#￥……&*（）——|{}【】‘；：”“'。，、？]"
    );
    let rs = '';
    for (let i = 0; i < s.length; i++) {
      rs = rs + s.substr(i, 1).replace(pattern, '');
    }
    return rs;
  }

  get allName() {
    return (
      this.chartInfo.tableNamePrefix + this.chartInfo.tableName + this.chartInfo.tableNameSuffix
    );
  }
  get total() {
    return this.cloneTableData.length;
  }

  closeSyncModal() {
    this.syncModal = false;
    this.syncModalParam = {};
  }
  handleCurrentChange(val) {
    const length = this.pageData.pageSize || 10;
    this.pageData.currentPage = val;
    this.tableData = this.cloneTableData.slice(length * (val - 1), length * val);
  }
  handleSizeChange(val) {
    this.pageData.pageSize = val;
    this.handleCurrentChange(1);
  }
  handleTableChecked(val) {
    this.multipleSelectList = val;
  }
  delChecked() {
    this.searchValue = '';
    if (this.allTableData.length) {
      this.cloneTableData = _.cloneDeep(this.allTableData);
    }
    if (this.multipleSelectList.length === 0) {
      this.$message.warning('请选择需要删除的字段');
      return;
    }
    let name = '';
    this.multipleSelectList.forEach((item: any) => {
      name += item.fieldName + ',';
    });
    if (name.length > 25) {
      name = name.slice(0, 25) + '...';
    }
    this.$confirm(`你确定要删除${name.replace(/,$/gi, '')}字段吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const array: any = [];
      this.cloneTableData.forEach((item: any) => {
        const has = this.multipleSelectList.find((ele: any) => ele.id === item.id);
        if (has === undefined) {
          array.push(item);
        }
      });
      this.pageData.total = this.cloneTableData.length;
      this.cloneTableData = array;
      this.allTableData = _.cloneDeep(this.cloneTableData);
      this.handleCurrentChange(1);
      this.multipleSelectList = [];
    });
  }
  // 同步字段数据
  syncChartField(list) {
    this.searchValue = '';
    if (this.allTableData.length) {
      this.cloneTableData = _.cloneDeep(this.allTableData);
    }
    this.syncModal = false;
    const saveList = list.map((item, index) => {
      delete item.available;
      return { ...item, id: this.tabId + index + 1 };
    });
    this.tabId += list.length;
    this.cloneTableData = [...this.cloneTableData, ...saveList];
    this.allTableData = _.cloneDeep(this.cloneTableData);
    this.handleCurrentChange(1);
    this.$message.success(`共同步${saveList.length}个字段`);
  }
  // 判断是否是重复字段
  getrepeat1(value, family) {
    return getrepeat(value, family, this.cloneTableData, this.info.resType);
  }
  // 重复的字段数据
  repeatInfo() {
    return repeatNum(this.cloneTableData);
  }
  // 获取的所有服务类型和连接器类型
  connectAll: any = [];
  level1Change(config) {
    this.$forceUpdate(); // 当前实例重新渲染
    if (config.type === 'cascader') {
      this.info[this.configInfo.level1.componentCfg.level2.model] = '';
      this.queryLevel2(config.componentCfg.level2.componentCfg);
    }
    this.getrestinfoKey();
    const serviceCheck: any = this.$refs['serviceForm'];
    serviceCheck.validate();
  }

  // 查询level2的下拉框
  async queryLevel2(componentCfg) {
    const api =
      componentCfg.remoteUrl +
      this.info[this.configInfo.level1.model] +
      '?' +
      componentCfg.reqParamsField[0] +
      '=' +
      this.info.service;
    this.level2List = [];

    await this.doGet(api).then((resp: any) => {
      this.parseResponse(resp, () => {
        resp.data.forEach((element) => {
          this.level2List.push({
            key: element,
            value: element
          });
        });
      });
    });
  }
  upLoad() {
    this.dialogVisible = true;
    this.dialogTitle = '导入';
  }
  async exportFile() {
    window.location.href =
      Vue.axios.defaults.baseURL + URL_TABLE_EXPORT + '/' + this.info.resType + '?id=' + this.id;
  }
  hasAuthority(hasRole: any, row: any) {
    return !_.isEmpty(_.toString(row.dataLevelType)) && row.dataLevelType !== 'PARENT' && hasRole;
  }
  closeDialog(data: any = false) {
    if (typeof data !== 'function' && data.length) {
      // 填充字段信息
      const baseFieldInfo = data;
      this.cloneTableData = [];
      baseFieldInfo.forEach((item, index) => {
        let items: any = {
          id: this.tabId + index + 1,
          fieldName: item.fieldName,
          fieldNameCn: item.fieldNameCn,
          fieldType: item.fieldType,
          primaryKey: item.primaryKey,
          partition: item.partition ? item.partition : '0',
          businessExplain: item.businessExplain
        };
        if (this.info.resType === 'HBASE') {
          items = { ...items, columnFamily: item.columnFamily };
        }
        this.cloneTableData.push(items);
      });
      this.allTableData = _.cloneDeep(this.cloneTableData);
      this.handleCurrentChange(1);
      this.tabId = this.tabId + baseFieldInfo.length;
    }
    this.dialogVisible = false;
  }
  // 自动同步弹窗
  async autoSync() {
    this.searchValue = '';
    if (this.allTableData.length) {
      this.cloneTableData = _.cloneDeep(this.allTableData);
    }
    this.disabled = true;
    const serviceForm: any = this.$refs['serviceForm'];
    serviceForm.validate((valid) => {
      if (valid) {
        const param: any = {};
        param[this.configInfo.level1.model] = this.info[this.configInfo.level1.model];
        param.resId = this.info.service;
        this.syncModalParam = param;
        this.syncModal = true;
      }
    });
  }

  async getResTypeList(data: any = false) {
    this.resTypeList = [];
    let api = URLTYPE_SQL;

    api = URLTYPE_SQL;

    await this.doGet(api).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.connectAll = resp.data;
        resp.data.forEach((n) => {
          this.resTypeList.push({
            value: n,
            label: n
          });
        });
        if (data.id) {
          this.info.resType = data.resType;
          this.resTypeChange(data); // 查询服务和服务信息配置
        } else {
          this.info.resType = this.resTypeList[0].value;
          this.resTypeChange(); // 查询服务和服务信息配置
        }
      });
    });
  }

  serviceListAll: any = [];
  // 查询服务
  async queryService(resId: any = false) {
    let api = URLLISTRES;
    this.serviceList = [];
    api = URLLISTRES + this.info.resType;
    await this.doPost(api).then((resp: any) => {
      this.serviceListAll = resp.data;
      resp.data.forEach((n) => {
        this.serviceList.push({
          value: n.id,
          label: n.title,
          url: n.url
        });
      });
    });
    const has = this.serviceList.find((item) => item.value === resId);
    if (resId && has) {
      this.info.service = resId;
    }
  }
  async serviceChange(data: any = false) {
    if (!data) {
      if (this.configInfo.level1) {
        this.info[this.configInfo.level1.model] = '';
        if (this.configInfo.level1.componentCfg.level2) {
          this.info[this.configInfo.level1.componentCfg.level2.model] = '';
        }
      }
    }

    if (this.configInfo.level1 && this.configInfo.level1.type !== 'input') {
      await this.queryLevel1(this.configInfo.level1.componentCfg);
      // 如果具有编辑传过来的值则进行赋值
      if (data) {
        this.info[this.configInfo.level1.model] = data[this.configInfo.level1.model];
      }
      // 如果有levle2，一并赋值、
      if (this.configInfo.level1.type === 'cascader' && data) {
        await this.queryLevel2(this.configInfo.level1.componentCfg.level2.componentCfg);
        this.info[this.configInfo.level1.componentCfg.level2.model] =
          data[this.configInfo.level1.componentCfg.level2.model];
      }
    } else {
      if (data && this.configInfo.level1) {
        this.info[this.configInfo.level1.model] = data[this.configInfo.level1.model];
      }
    }
    this.getrestinfoKey();
  }
  // 填充连接器属性
  getrestinfoKey() {
    this.connectList = cloneDeep(this.originalConnectList);
    this.connectList.forEach((element) => {
      if (element.resInfoKey) {
        this.serviceListAll.forEach((item) => {
          if (this.info.service === item.id) {
            const resProperty = JSON.parse(item.resProperty);
            if (resProperty[element.resInfoKey]) {
              element.defaultValue = resProperty[element.resInfoKey];
            }
          }
        });
        if (
          this.configInfo.level1 &&
          this.info[this.configInfo.level1.model] &&
          element.resInfoKey === this.configInfo.level1.model
        ) {
          element.defaultValue = this.info[this.configInfo.level1.model];
        }
        // 给连接器1，2 赋值
        if (
          this.configInfo.level1 &&
          this.configInfo.level1.type === 'cascader' &&
          element.resInfoKey === this.configInfo.level1.componentCfg.level2.model
        ) {
          element.defaultValue = this.info[this.configInfo.level1.componentCfg.level2.model];
        }
      }
    });
  }
  propertyAdd() {
    this.advancedConnect.push({
      key: '',
      value: ''
    });
  }
  deleteProperty(i) {
    this.advancedConnect.splice(i, 1);
  }

  deleteTableList(id, name) {
    this.$confirm(`您确定要删除${name || ''}字段吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      this.cloneTableData = this.cloneTableData.filter((item) => item.id !== id);
      this.allTableData = _.cloneDeep(this.cloneTableData);
      this.handleCurrentChange(1);
      this.pageData.total -= 1;
    });
  }

  // 删除字段信息
  deleteFieldList(i) {
    this.tableData.splice(i, 1);
  }
  addInfo() {
    this.searchValue = '';
    let param: any = {
      fieldName: '',
      fieldNameCn: '',
      fieldType: '', // 字段类型
      id: this.tabId + 1,
      primaryKey: '0', // 主键
      partition: '0', // 分区
      businessExplain: '' // 业务口径
    };
    if (this.info.resType === 'HBASE') {
      param = {
        ...param,
        columnFamily: '' // 列簇
      };
    }
    this.cloneTableData = [...this.allTableData, param];
    this.allTableData = [...this.allTableData, param];
    this.pageData.total = this.cloneTableData.length;
    const page = Math.ceil(this.pageData.total / this.pageData.pageSize);
    this.handleCurrentChange(page);
    this.tabId += 1;
    this.$nextTick(() => {
      const dom = this.$refs[`table${this.tabId}`] as any;
      dom && dom.focus();
    });
  }
  // 所有连接器属性
  allProperties: any = [];
  // 连接器下拉框改变回调
  contentSelect(id) {
    this.allProperties.forEach((element) => {
      if (element.id === id) {
        const connectList = JSON.parse(element.properties);
        const showList: any = [];
        connectList.forEach((item) => {
          if (!item.items) {
            this.$set(item, 'items', '');
          }
          if (item.defaultValue === undefined) {
            this.$set(item, 'defaultValue', '');
          }
          if (!item.needHide) {
            showList.push(item);
          }
        });
        showList.forEach((showlist, index) => {
          connectList.forEach((connect) => {
            if (showlist.name === connect.name && (index + 1) % 2 === 0) {
              connect.even = true;
            }
          });
        });
        if (!showList.length) {
          this.noData = true;
        } else {
          this.noData = false;
        }
        this.connectList = [...connectList];
        this.originalConnectList = cloneDeep(this.connectList);
      }
    });

    this.getrestinfoKey();
  }

  // 查询level1的下拉框
  async queryLevel1(componentCfg) {
    const api =
      componentCfg.remoteUrl + '?' + componentCfg.reqParamsField[0] + '=' + this.info.service;
    this.level1List = [];
    this.selectLoading = true;
    await this.doGet(api).then((resp: any) => {
      this.parseResponse(resp, () => {
        resp.data.forEach((element) => {
          this.level1List.push({
            key: element,
            value: element
          });
        });
      });
    });
    this.selectLoading = false;
  }

  // 服务类型值改变时
  async resTypeChange(data: any = false) {
    if (this.state !== 1) {
      this.tableData = [];
      this.cloneTableData = [];
      this.allTableData = [];
      this.multipleSelectList = [];
      this.tabId = 0;
    }
    this.level1List = [];
    this.level2List = [];
    if (!data.id) {
      this.info.service = '';
      this.queryConnect();
    }
    const resType = this.info.resType;
    this.info = {
      resType: resType, // 服务类型
      service: ''
    };
    this.pageData = {
      currentPage: 1,
      pageSize: 10,
      total: 0
    };
    this.$forceUpdate();
    await this.queryService(data.resId);

    // 根据服务类型查询服务信息配置
    const jsonApi = URL_TABLE_RESCONF + this.info.resType.toLowerCase() + '/sql.json';
    await this.doGet(jsonApi).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.configInfo = resp.data;
        // this.configInfo.level1.type = 'input'; // 测试等于input的情况
        if (this.configInfo.level1 && this.configInfo.level1.type === 'cascader') {
          this.$set(this.info, this.configInfo.level1.model, '');
          this.$set(this.info, this.configInfo.level1.componentCfg.level2.model, '');

          this.$set(this.infoRules, this.configInfo.level1.model, [
            {
              required: true,
              message: this.configInfo.level1.label + '不能为空',
              trigger: 'blur'
            }
          ]);
          this.$set(this.infoRules, this.configInfo.level1.componentCfg.level2.model, [
            {
              required: true,
              message: this.configInfo.level1.componentCfg.level2.label + '不能为空',
              trigger: 'blur'
            }
          ]);
        } else if (this.configInfo.level1) {
          this.$set(this.info, this.configInfo.level1.model, '');
          this.infoRules[this.configInfo.level1.model] = [
            {
              required: true,
              message: this.configInfo.level1.label + '不能为空',
              trigger: 'blur'
            }
          ];
        }
        // 动态增加表单项会触发校验，在此处清除校验
        this.$nextTick(() => {
          this.$forceUpdate();
          const form: any = this.$refs['serviceForm'];
          if (form) {
            form.clearValidate();
          }
        });
      });
    });

    // 编辑状态下赋值
    if (data.id) {
      this.queryConnect(data);
      this.serviceChange(data);
      // 填充字段信息
      const baseFieldInfo = JSON.parse(data.baseFieldInfo);
      baseFieldInfo.forEach((item, index) => {
        let items: any = {
          id: this.tabId + index + 1,
          fieldName: item.fieldName,
          fieldNameCn: item.fieldNameCn,
          fieldType: item.fieldType,
          primaryKey: item.primaryKey,
          partition: item.partition ? item.partition : '0',
          businessExplain: item.businessExplain
        };
        if (this.info.resType === 'HBASE') {
          items = { ...items, columnFamily: item.columnFamily };
        }
        this.cloneTableData.push(items);
      });
      this.allTableData = _.cloneDeep(this.cloneTableData);
      this.pageData.total = baseFieldInfo.length;
      this.handleCurrentChange(1);
      this.tabId = baseFieldInfo.length;
    }
  }
  async queryConnect(data: any = false) {
    // 根据服务类型接口查询连接器
    let connectList: any = [];
    const api = URL_CONNECTORINFO + this.info.resType;
    await this.doGet(api).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.allProperties = resp.data;

        if (data) {
          // 编辑时
          this.allProperties.forEach((element) => {
            if (element.id === data.connectorId) {
              connectList = JSON.parse(element.properties);
            }
          });
          this.ConnectSelectValue = data.connectorId;
        } else {
          this.ConnectSelectValue = this.allProperties[0].id;
          connectList = JSON.parse(this.allProperties[0]!.properties);
        }
        const showList: any = [];
        connectList.forEach((element) => {
          if (!element.items) {
            this.$set(element, 'items', []);
          }
          if (element.defaultValue === undefined) {
            this.$set(element, 'defaultValue', '');
          }
          if (!element.needHide) {
            showList.push(element);
          }
          if (data) {
            const connectorInfo = JSON.parse(data.connectorInfo);
            if (connectorInfo && connectorInfo[element.name]) {
              element.defaultValue = connectorInfo[element.name];
            }
          }
        });
        showList.forEach((showlist, index) => {
          connectList.forEach((element) => {
            if (showlist.name === element.name && (index + 1) % 2 === 0) {
              element.even = true;
            }
          });
        });
        if (!showList.length) {
          this.noData = true;
        } else {
          this.noData = false;
        }
        this.connectList = isShowFieldColumn(this.cloneTableData, connectList);
        this.originalConnectList = cloneDeep(this.connectList);
      });
    });
    this.loading = false;
  }
  async chartCheck() {
    const chartForm: any = this.$refs['chartForm'];
    await chartForm.validate();
  }

  // 保存
  async submit(priview: any = false) {
    // 表信息
    if (this.repeatInfo() !== 0) {
      this.$message.error('存在重复字段，请修改后提交');
      return;
    }
    await this.chartCheck();

    const allParam: any = {
      resType: '',
      resId: '',
      resName: '',
      isDataPermission: true,
      tableNamePrefix: '',
      tableName: '',
      tableNameCn: '',
      businessExplain: '',
      label: '',
      baseFieldInfo: [],
      connectorId: '',
      connectorInfo: {},
      advanceFieldInfo: [],
      advanceProperty: [],
      advanceConnectorInfo: []
    };

    allParam.tableNameCn = this.chartInfo.tableNameCn;
    allParam.tableNamePrefix = this.chartInfo.tableNamePrefix;
    allParam.tableNameSuffix = this.chartInfo.tableNameSuffix;
    allParam.tableName =
      allParam.tableNamePrefix + this.chartInfo.tableName + this.chartInfo.tableNameSuffix;
    allParam.businessExplain = this.chartInfo.businessExplain;

    // 校验表信息必填项
    if (!allParam.tableName) {
      return false;
    }
    const serviceForm: any = this.$refs['serviceForm'];
    serviceForm.validate((valid) => {
      if (valid) {
        allParam.resType = this.info.resType;
        allParam.resId = this.info.service;
        if (this.configInfo.level1) {
          allParam[this.configInfo.level1.model] = this.info[this.configInfo.level1.model];
        }

        // 填充连接器器字段
        this.connectList.forEach((element) => {
          if (element.resInfoKey) {
            this.serviceListAll.forEach((item) => {
              if (this.info.service === item.id) {
                const resProperty = JSON.parse(item.resProperty);

                if (resProperty[element.resInfoKey]) {
                  element.defaultValue = resProperty[element.resInfoKey];
                }
              }
            });
            if (this.configInfo.level1 && element.resInfoKey === this.configInfo.level1.model) {
              element.defaultValue = allParam[this.configInfo.level1.model];
            }
            // 给连接器1，2 赋值
            if (
              this.configInfo.level1 &&
              this.configInfo.level1.type === 'cascader' &&
              element.resInfoKey === this.configInfo.level1.componentCfg.level2.model
            ) {
              element.defaultValue = allParam[this.configInfo.level1.componentCfg.level2.model];
            }
          }
        });
        // 获取服务名称
        let resName = '';
        this.serviceList.forEach((element) => {
          if (element.value === this.info.service) {
            resName = element.label;
          }
        });
        allParam.resName = resName;
        // 校验字段信息
        const fieldList = _.cloneDeep(this.cloneTableData);
        const field = validateField(fieldList, this.info.resType);
        if (!field.validate) {
          return;
        }
        // 校验连接器字段
        const Connect = validateConnect(this.connectList);
        // 高级表字段校验
        this.advanceChartList = (this.$refs['superFields'] as any)
          ? (this.$refs['superFields'] as any).list
          : [];
        const FieldInfo = valideFieldInfo(this.advanceChartList);
        if (!FieldInfo.flag) {
          this.error = FieldInfo.error;
          return;
        }
        // 高级连接器属性校验
        const advanceConnectorInfo: any = validateAdvancedConnect(this.advancedConnect);
        if (!advanceConnectorInfo.flag) {
          return;
        }
        if (!this.tableData.length && !this.advanceChartList.length) {
          this.$message.error('字段信息或高级表字段需要至少填写一项');
          return;
        }

        if (field.validate && Connect.validate && FieldInfo.flag && advanceConnectorInfo.flag) {
          allParam.baseFieldInfo = field.param.map((item) => {
            const { id, ...others } = item;
            console.log('id:', id);
            return others;
          });
          allParam.connectorInfo = Connect.params;
          allParam.advanceFieldInfo = FieldInfo.param;
          allParam.advanceConnectorInfo = advanceConnectorInfo.param;
          allParam.connectorId = this.ConnectSelectValue; // 连接器选择id

          const obj = cloneDeep(allParam);
          if (priview) {
            this.doPost(URL_SQL_PREVIEW, obj).then((resp: any) => {
              this.parseResponse(resp, () => {
                if (resp.success) {
                  this.sourceCode = resp.data;
                  const previewRef: any = this.$refs.preview;
                  previewRef.visible = true;
                } else {
                  this.$message.error(resp.data.msg);
                }
              });
            });
            return;
          }
          // 编辑
          if (this.id && !this.copy) {
            const saveConfirmRef: any = this.$refs.saveConfirm;
            saveConfirmRef.visible = true;
            this.saveData = cloneDeep(obj);
          } else {
            this.doPost(URL_ITEMADD, obj).then((resp: any) => {
              this.parseResponse(resp, () => {
                const oidFullPath = this.$route.fullPath;
                const tabsNavInstance = vm.$children[0].$refs['tabsNav'];
                tabsNavInstance.handleTabsDelete(oidFullPath);
              });
            });
          }
        }
      } else {
        return false;
      }
    });
  }
  saveConfirmCallback(data) {
    // 编辑时保存
    if (data.comments) {
      const param = this.saveData;
      param.memo = data.comments;
      param.id = this.id;
      const saveConfirmRef: any = this.$refs.saveConfirm;
      saveConfirmRef.visible = false;
      this.doPut(URL_UPDATE, param).then((resp: any) => {
        this.parseResponse(resp, () => {
          const oidFullPath = this.$route.fullPath;
          const tabsNavInstance = vm.$children[0].$refs['tabsNav'];
          tabsNavInstance.handleTabsDelete(oidFullPath);
        });
      });
    }
  }

  queryChartList() {
    this.chartList = [];
    this.chartListBack = [];
    const api = URL_LISTBYTYPE;

    this.doGet(api).then((resp: any) => {
      this.parseResponse(resp, () => {
        resp.data.forEach((n) => {
          this.chartList.push({
            value: n,
            label: n
          });
        });
      });
    });
    const apisuffix = URL_LISTBYTYPE_SUFFIX;
    this.doGet(apisuffix).then((resp: any) => {
      this.parseResponse(resp, () => {
        resp.data.forEach((n) => {
          this.chartListBack.push({
            value: n,
            label: n
          });
        });
      });
    });
  }
  async queryBottomChart(data: any = false) {
    const api = URL_GETSUBBYPARENT;
    this.advancedPropertyList = [];
    await this.doGet(api).then((resp: any) => {
      this.parseResponse(resp, () => {
        resp.data.forEach((n) => {
          this.advancedPropertyList.push({
            title: n.title,
            value1: n.value1,
            key: n.code,
            value: ''
          });
        });
      });
    });
    if (data.length) {
      const cloneData = cloneDeep(data);
      this.advancedPropertyList.forEach((item) => {
        cloneData.forEach((prop) => {
          if (item.key === Object.keys(prop)[0]) {
            item.value = prop[Object.keys(prop)[0]];
          }
        });
      });
      this.$forceUpdate(); // 当前实例重新渲染
    }
  }
  // 字段信息下拉框
  queryInformation() {
    this.chartList = [];
    const api = URL_FINDBYCODE;

    this.doGet(api).then((resp: any) => {
      this.parseResponse(resp, () => {
        const data = JSON.parse(resp.data.value1);
        data.forEach((n) => {
          this.typeEnums.push({
            value: n.value,
            label: n.label
          });
        });
      });
    });

    this.chartListBack = [];
  }

  detailSource: any = {};

  isDewtail = false;
  async onCreated() {
    this.status = '0';
    this.advanceChartList = [];
    this.advancedConnect = [];
    this.status = this.$route.query.status || '0';
    this.title = getTitle(this.status);
    this.state = +this.$route.query.state || 0;
    if (this.$route.query.showEdit === 'true') {
      this.showEdit = true;
    }

    if (this.$route.query.update === 'true') {
      this.update = true;
    }
    if (this.$route.query.copy) {
      this.copy = true;
    }
    if (this.$route.query.id) {
      this.id = this.$route.query.id as string;

      if (this.status === '1') {
        this.isDewtail = true;
      }
    }

    // 查询表前缀
    this.queryChartList();

    // 查询字段信息下拉框
    this.queryInformation();
    // 编辑
    if (this.id) {
      this.loading = true;
      const api = URL_TABLE_FIND_BY_ID + '?id=' + this.id;
      let data: any = {};
      await this.doGet(api).then((resp: any) => {
        this.parseResponse(resp, () => {
          data = resp!.data;
        });
      });
      this.authStatus =
        data.authStatus === 'YES' && this.hasFeatureAuthority('PA.DATA.TABLE.EDIT') ? true : false;
      this.detailSource = data;
      // 对服务信息编辑时赋值
      this.getResTypeList(data);

      // 表信息
      this.chartInfo.tableNamePrefix = data.tableNamePrefix;
      this.chartInfo.tableNameSuffix = data.tableNameSuffix;
      let tableName = '';
      if (data.tableName.indexOf(data.tableNamePrefix) !== -1) {
        tableName = data.tableName.replace(data.tableNamePrefix, '');
      }
      if (tableName.indexOf(data.tableNameSuffix) !== -1) {
        const reg = RegExp('(.*)' + data.tableNameSuffix);
        tableName = tableName.replace(reg, '$1');
      }

      this.chartInfo.tableName = tableName === '' ? data.tableName : tableName;
      this.chartInfo.tableNameCn = data.tableNameCn;
      this.chartInfo.businessExplain = data.businessExplain;

      // 填充高级 表字段
      const advanceChartList = JSON.parse(data.advanceFieldInfo);
      if (advanceChartList.length) {
        this.isSuperChart = true;
      }
      this.advanceChartList = advanceChartList;
      // advanceChartList.forEach((element) => {
      //   this.advanceChartList.push({
      //     value: element.value
      //   });
      // });
      // 填充高级 连接器
      const advancedConnect = JSON.parse(data.advanceConnectorInfo);
      if (advancedConnect.length) {
        this.isSuperConnector = true;
      }
      advancedConnect.forEach((element) => {
        this.advancedConnect.push({
          key: Object.keys(element)[0],
          value: element[Object.keys(element)[0]]
        });
      });
      // 填充表属性
      this.queryBottomChart(JSON.parse(data.advanceProperty));
      this.loading = false;
    } else {
      // 查询服务类型
      this.getResTypeList();
      // 查询表属性
      this.queryBottomChart();
    }
  }
}
</script>

<style scoped lang="scss">
@import './style.scss';
</style>
