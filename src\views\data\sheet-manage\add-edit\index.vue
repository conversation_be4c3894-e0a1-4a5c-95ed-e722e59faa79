<template>
  <pro-page :title="title" :loading="loading" class="page-content">
    <!-- operation -->
    <div slot="operation">
      <el-button v-if="status === 'add'" type="primary" @click="showParsingDialog = true">
        {{ $t('pa.data.table.detail.sqlParsing') }}
      </el-button>
      <el-button type="primary" @click="handlePreview">{{ $t('pa.data.table.detail.sqlPreview') }}</el-button>
      <el-button type="primary" @click="handleSubmit">{{ $t('pa.action.save') }}</el-button>
    </div>
    <transition name="el-zoom-in-center">
      <pro-grid direction="column" :gutter="18" class="first_content">
        <!-- 表信息 -->
        <table-info ref="tableInfoRef" :data="tableData" :status="status" />
        <!-- 服务信息 -->
        <service-info ref="serviceInfoRef" :data="tableData" @service-type-change="handleServiceTypeChange" />
        <!-- 表字段 -->
        <table-fields
          ref="tableFieldsRef"
          :status="status"
          :data="tableData"
          :auto-sync-config="autoSyncConfig"
          @auto-sync="handleAutoSync"
          @change="handleTableFieldChange"
        />
        <!-- 连接器 -->
        <connector-info ref="connectorInfoRef" :service-info="serviceInfo" :data="tableData" />
      </pro-grid>
    </transition>
    <!-- 保存 -->
    <save-dialog v-if="showSaveDialog" :show.sync="showSaveDialog" @confirm="handleSaveConfirm" />
    <!-- SQL解析 -->
    <sql-parsing-dialog v-if="showParsingDialog" :show.sync="showParsingDialog" @confirm="handleParsingConfirm" />
    <!-- SQL预览 -->
    <sql-preview-dialog v-if="showPreviewDialog" :show.sync="showPreviewDialog" :data="sourceCode" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Ref, Vue } from 'vue-property-decorator';
import TableInfo from './components/table-Info.vue';
import ServiceInfo from './components/service-Info.vue';
import TableFields from './components/table-fields.vue';
import ConnectorInfo from './components/connector-Info.vue';
import { safeParse } from '@/utils';
import { getTableData, sqlPreview, addTable, updateTable } from '@/apis/dataApi';

@Component({
  components: {
    TableInfo,
    ServiceInfo,
    TableFields,
    ConnectorInfo,
    SaveDialog: () => import('./components/save-dialog.vue'),
    SqlParsingDialog: () => import('./components/sql-parsing-dialog.vue'),
    SqlPreviewDialog: () => import('../../modals/sql-preview-dialog.vue')
  }
})
export default class DataAddEdit extends Vue {
  @Ref('tableInfoRef') readonly tableInfo!: TableInfo;
  @Ref('serviceInfoRef') readonly serviceInfo!: ServiceInfo;
  @Ref('tableFieldsRef') readonly tableFields!: TableFields;
  @Ref('connectorInfoRef') readonly connectorInfo!: ConnectorInfo;

  id = '';
  status = 'add';
  state = 0; // ???
  loading = false;
  tableData: any = null;
  autoSyncConfig: any = null;
  showParsingDialog = false;
  sourceCode = '';
  showPreviewDialog = false;
  showSaveDialog = false;

  get title() {
    return {
      add: this.$t('pa.data.table.addTable'),
      edit: this.$t('pa.data.table.editTable'),
      copy: this.$t('pa.data.table.copyTable')
    }[this.status];
  }
  get isCopy() {
    return this.status === 'copy';
  }

  created() {
    this.id = this.$route.query.id as string;
    this.status = (this.$route.query.status as string) || 'add';
    this.state = (+this.$route.query.state as number) || 0;
  }
  async mounted() {
    if (this.id) return await this.getTableData();
    this.serviceInfo.init();
  }

  async getTableData() {
    try {
      this.loading = true;
      const { success, data, error } = await getTableData(this.id);
      if (!success) return this.$message.error(error);
      await this.$nextTick();
      this.handleParsingConfirm(data);
    } finally {
      this.loading = false;
    }
  }
  /* sql解析 */
  async handleParsingConfirm(data: any) {
    data.baseFieldInfo = safeParse(data.baseFieldInfo, []);
    data.advanceFieldInfo = safeParse(data.advanceFieldInfo, []);
    data.connectorInfo = safeParse(data.connectorInfo);
    data.advanceConnectorInfo = safeParse(data.advanceConnectorInfo, []);
    data.advanceProperty = safeParse(data.advanceProperty);
    this.tableData = data;
    await this.$nextTick();
    this.tableInfo.init();
    this.serviceInfo.init();
    this.tableFields.initAdvancedField();
    this.connectorInfo.initAdvancedConnector();
  }
  handleServiceTypeChange(data: any, autoSyncConfig: any) {
    this.autoSyncConfig = autoSyncConfig;
    this.tableFields.init(data);
    this.connectorInfo.init(data);
  }
  handleTableFieldChange(length: number) {
    this.connectorInfo.intLength(length);
  }
  async handleAutoSync() {
    const params = await this.serviceInfo.validate();
    this.tableFields.openSyncDialog(params);
  }
  /* SQL预览 */
  async handlePreview() {
    const params = await this.validate();
    const { data, success, msg } = await sqlPreview(params);
    if (!success) return this.$message.error(msg);
    this.sourceCode = this.$store.getters.decrypt(data);
    this.showPreviewDialog = true;
  }
  async handleSubmit() {
    if (this.id && !this.isCopy) return (this.showSaveDialog = true);
    try {
      this.loading = true;
      const params = await this.validate();
      const { success, data, msg, error } = await addTable(params);
      if (!success) return this.$message.error(error);
      this.$message.success(msg);
      (this as any).$tabNav.handleTabsDelete(this.$route.fullPath);
      this.$router.push({
        path: '/data/sheetDetail',
        query: { id: data.id, title: data.tableName }
      });
    } finally {
      this.loading = false;
    }
  }
  async handleSaveConfirm(data: any) {
    if (!data.memo) return;
    const params = await this.validate();
    params.memo = data.memo;
    params.id = this.id;
    try {
      this.loading = true;
      const { success, msg, error } = await updateTable(params);
      if (!success) return this.$message.error(error);
      this.$message.success(msg);
      (this as any).$tabNav.handleTabsDelete(this.$route.fullPath);
      await this.$router.push({
        path: '/data/sheetDetail',
        query: { id: params.id, title: params.tableName }
      });
    } finally {
      this.loading = false;
    }
  }
  public async validate() {
    const tableInfo = await this.tableInfo.validate();
    const serviceInfo = await this.serviceInfo.validate();
    const tableFields = await this.tableFields.validate();
    const connectorInfo = await this.connectorInfo.validate();
    Object.keys(connectorInfo.connectorInfo).forEach((key) => {
      serviceInfo[key] && (connectorInfo.connectorInfo[key] = serviceInfo[key]);
    });
    return { ...tableInfo, ...serviceInfo, ...tableFields, ...connectorInfo };
  }
}
</script>
