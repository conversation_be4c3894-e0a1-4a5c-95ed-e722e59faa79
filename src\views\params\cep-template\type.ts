interface OperateListItem {
  type: string;
  icon: string;
  tooltip: string;
}
export interface TableHeadItem {
  label: string;
  prop: string;
  width?: string;
  minWidth?: string;
  required?: boolean;
  type?: string;
  optionsKey?: string;
  operateList?: OperateListItem[];
}
interface ModelTableDataItem {
  condition?: string;
  name: string;
  type?: string;
  operator?: string;
  value?: string;
}
export interface ModelConfigItem {
  name: string;
  express?: string;
  tableData: ModelTableDataItem[];
}
interface Autosize {
  minRows?: number;
  maxRows?: number;
}
export interface RenderItem {
  hidden?: boolean;
  label: string;
  name: string;
  type?: string;
  options?: any[];
  autosize?: Autosize;
  placeholder?: string;
  tooltip?: string;
  maxlength?: number;
  minlength?: number;
  disabled?: boolean;
}
export interface BaseInfoRenderItem extends RenderItem {
  children?: RenderItem[];
}
interface MaximumAllowableTimeInterval {
  timeValue: number;
  timeValueType: string;
}
export interface BaseInfo {
  patternName: string;
  memo?: string;
}
export interface ModelGroupItem {
  name: string;
}

interface PatternConditionListItem {
  modelOrGroupName: string;
  circularRuleTimes: string;
  excessRuleTimesList: string[];
  connectionModeType: string;
  index?: number;
  leftInterval: number | string;
  rightInterval: number | string;
  [property: string]: any;
}
interface GroupCepPatterns {
  groupName?: string;
  maximumAllowableTimeInterval?: MaximumAllowableTimeInterval;
  sequenceSkipStrategy?: string;
  groupCepPatternConditionList: PatternConditionListItem[];
  logicalRelationship: string;
}

export interface CepData {
  basicInformation: BaseInfo;
  ddId: string;
  ddName?: string;
  singletonCepPatterns: any[];
  groupCepPatterns: GroupCepPatterns[];
  sequenceCepPatterns: GroupCepPatterns[];
}
