<template>
  <bs-dialog :title="$t('pa.orgOrQueueSetting')" width="420px" :visible.sync="display" @confirm="handleConfirm">
    <!-- form -->
    <el-form ref="formRef" :model="formData" :rules="formRules" :label-width="isEn ? '150px' : '86px'">
      <!-- 队列资源 -->
      <el-form-item v-if="!isStandalone" :label="$t('pa.queueResource')" prop="queue">
        <bs-select
          v-model="formData.queue"
          clearable
          filterable
          style="width: 100%"
          :options="queueList"
          :placeholder="$t('pa.placeholder.queueList')"
          @change="getOrgList"
        />
      </el-form-item>
      <!-- orgIds -->
      <el-form-item :label="$t('pa.targetOrg')" prop="orgIds">
        <bs-cascader
          ref="cascaderRef"
          v-model="formData.orgIds"
          clearable
          collapse-tags
          :options="orgList"
          :placeholder="$t('pa.placeholder.orgIds')"
          :disabled="!isStandalone && !formData.queue"
          :props="{ multiple: true, checkStrictly: true }"
        />
      </el-form-item>
    </el-form>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, PropSync, Ref, Vue } from 'vue-property-decorator';
import { getQueueList, getUnDisOrgList } from '@/apis/serviceApi';
import Cascader from 'bs-ui-pro/packages/calendar/index.js';
import ElForm from 'bs-ui-pro/packages/form/index.js';
import { safeArray } from '@/utils';

@Component
export default class AddAssetDialog extends Vue {
  @PropSync('show', { type: Boolean }) display!: boolean;
  @Ref('formRef') readonly form!: ElForm;
  @Ref('cascaderRef') readonly cascader!: Cascader;

  id = '';
  queueList: any[] = [];
  formData = {
    queue: '',
    orgIds: []
  };
  formRules: any = {
    queue: [{ required: true, message: this.$t('pa.placeholder.queue'), trigger: 'change' }],
    orgIds: [{ required: true, message: this.$t('pa.placeholder.orgIds'), trigger: 'change' }]
  };
  orgList: any[] = [];

  get isStandalone() {
    return (this.$route.query.clusterType as string) === 'STANDALONE';
  }

  created() {
    this.id = (this.$route.query.id as string) || '';
    this.isStandalone ? this.getOrgList() : this.getQueueList();
  }
  async getQueueList() {
    const { success, data, error } = await getQueueList(this.id);
    if (!success) this.$message.error(error);
    this.queueList = safeArray(data).map((value) => ({ value, label: value }));
  }
  async getOrgList() {
    if (!this.isStandalone && !this.formData.queue) return;
    this.formData.orgIds = [];
    const queue = this.isStandalone ? '' : this.formData.queue;
    const { success, data, error } = await getUnDisOrgList(queue, this.id);
    if (!success) this.$message.error(error);
    this.orgList = safeArray(data).map((it) => this.formate(it));
  }
  formate(data: any) {
    const hasChild = Array.isArray(data.children) && data.children.length > 0;
    return {
      label: data.orgName,
      value: data.orgId,
      disabled: data.disabled,
      children: hasChild ? data.children.map((it: any) => this.formate(it)) : null
    };
  }

  async handleConfirm() {
    await this.form.validate();
    const orgList = this.cascader.getCheckedNodes().map((it) => {
      return { orgId: it.value, orgName: it.label, queue: this.isStandalone ? '' : this.formData.queue };
    });
    this.$emit('confirm', this.isStandalone ? { queue: '' } : this.formData, orgList);
    this.display = false;
  }
}
</script>
<style lang="scss" scoped>
::v-deep .bs-select,
.el-cascader {
  width: 100%;
}
::v-deep .el-tag--light {
  max-width: 130px !important;
}
</style>
