<template>
  <div>
    <bs-dialog
      title="字段同步"
      width="60%"
      visible
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="closeSyncModal"
    >
      <div>
        <div class="title">
          <div>
            已选中个
            <span style="color: #32cd32">{{ checkLength }}</span>
            字段
          </div>
          <div>
            <el-input v-model="search" suffix-icon="el-icon-search" placeholder="请输入字段名" />
          </div>
        </div>
        <el-table
          v-loading="loading"
          :data="tableData"
          style="
            width: 100%;
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 10px;
            border: 1px solid #f1f1f1;
            border-bottom: none;
          "
          size="mini"
          @selection-change="handleTableChecked"
        >
          <el-table-column prop="fieldName" min-width="130">
            <template slot="header">
              <span style="color: red; margin-right: 4px">*</span>
              <span>字段名</span>
            </template>
            <template slot-scope="{ row }">
              <span>{{ row.fieldName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="fieldType" min-width="130">
            <template slot="header">
              <span style="color: red; margin-right: 4px">*</span>
              <span>字段类型</span>
            </template>
            <template slot-scope="{ row }">
              <span>{{ row.fieldType }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="resType === 'HBASE'" label="列簇" min-width="130">
            <template slot-scope="{ row }">
              <span>{{ row.columnFamily }}</span>
            </template>
          </el-table-column>
          <el-table-column type="selection" width="55" :selectable="checkAvailable" />
        </el-table>
        <el-pagination
          class="pagination"
          background
          :current-page.sync="pageData.currentPage"
          :page-size="pageData.pageSize"
          :page-sizes="[10, 20, 40, 80, 100]"
          :total="pageData.total"
          :pager-count="5"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="closeSyncModal(false)">关 闭</el-button>
        <el-button type="primary" :disabled="!checkedList.length" @click="handleCheck">
          确 定
        </el-button>
        <el-button type="primary" @click="handleCheckAll"> 全部同步 </el-button>
      </span>
    </bs-dialog>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Emit, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { URL_AUTOSYNC } from '@/apis/commonApi';
import { cloneDeep } from 'lodash';
@Component({})
export default class SyncModal extends PaBase {
  @Prop({ default: '' }) resType!: string;
  @Prop({ default: false }) formLoading!: boolean;
  @Prop({ default: () => {} }) params!: object;
  @Prop({ default: () => [] }) showList!: any[];
  loading = false;
  checkedList = [];
  tableData: any = [];
  cloneTable: any = [];
  pageData = {
    currentPage: 1,
    pageSize: 10,
    total: 0
  };
  // 搜索内容
  search = '';
  // 搜索内容变更
  @Watch('search')
  changeTableInfo(val) {
    const list = this.cloneTable.filter((item) => {
      const name = item.fieldName && item.fieldName.indexOf(val) === 0;
      return name;
    });
    this.tableData = list;
    this.pageData.total = list.length;
  }
  // 选中数据长度
  get checkLength() {
    return this.checkedList.length;
  }
  // 每页展示数量变更
  handleSizeChange(val) {
    this.pageData.pageSize = val;
    this.handleCurrentChange(1);
  }
  // 翻页数据
  handleCurrentChange(val) {
    const length = this.pageData.pageSize || 10;
    this.pageData.currentPage = val;
    this.tableData = this.cloneTable.slice(length * (val - 1), length * val);
  }

  created() {
    this.getTable();
  }

  async getTable() {
    this.loading = true;
    const api = URL_AUTOSYNC + this.resType;
    this.doPost(api, this.params).then((resp: any) => {
      this.parseResponse(resp, () => {
        const baseFieldInfo = resp.data;
        this.tableData = [];
        const table = baseFieldInfo.map((item) => {
          const hasItem = this.showList.find((tar: any) => {
            if (this.resType === 'HBASE') {
              return tar.fieldName === item.fieldName && tar.columnFamily === item.columnFamily;
            } else {
              return tar.fieldName === item.fieldName;
            }
          });

          const temp: any = {
            fieldName: item.fieldName,
            fieldNameCn: item.fieldNameCn,
            fieldType: item.fieldType,
            primaryKey: item.primaryKey,
            partition: item.partition,
            businessExplain: item.businessExplain,
            columnFamily: item.columnFamily,
            available: !!hasItem
          };
          temp['primaryKey'] = temp['primaryKey'] ? temp['primaryKey'] : '0';
          temp['partition'] = temp['partition'] ? temp['partition'] : '0';
          return temp;
        });
        this.tableData = table;
        this.cloneTable = cloneDeep(table);
        this.pageData.total = table.length;
        this.handleCurrentChange(1);
      });
      this.loading = false;
    });
  }

  checkAvailable(row) {
    if (row.available !== false) {
      return false;
    }
    return true;
  }
  // 勾选操作
  handleTableChecked(val) {
    this.checkedList = val;
  }
  // 关闭弹窗
  @Emit('close')
  closeSyncModal() {}

  // 同步部分字段
  @Emit('handleCheck')
  handleCheck() {
    return this.checkedList;
  }
  // 同步全部字段
  @Emit('checkAll')
  handleCheckAll() {
    const filter = this.cloneTable.filter((item) => item.available === false);
    return filter;
  }
}
</script>
<style scoped>
.dialog ::v-deep .el-dialog__body {
  padding: 0;
}
.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
</style>
