import i18n from '@/i18n';
const validators = {
  validateResUrl: (rule, value, callback) => {
    if (/[\u4E00-\u9FA5]+/.test(value)) {
      callback(new Error(i18n.t('pa.tip.notHasChinese') as string));
    }
    if (/[\uff0c]/.test(value)) {
      callback(new Error(i18n.t('pa.tip.notHasChineseComma') as string));
    }
    if (/\s/.test(value)) {
      callback(new Error(i18n.t('pa.tip.notHasSpaceBreak') as string));
    }
    if (/^,/.test(value)) {
      callback(new Error(i18n.t('pa.tip.notStartWithComma') as string));
    }
    if (/,$/.test(value)) {
      callback(new Error(i18n.t('pa.tip.notEndWithComma') as string));
    }
    // 正则匹配逻辑暂时先注释
    // if (value != null && value.length > 0) {
    //   const array = value.split(',');
    //   let j = 0;
    //   for (; j < array.length; j++) {
    //     let str = array[0];
    //     if (!isValidAddress(str)) {
    //       callback(new Error('地址格式不正确'));
    //     }
    //   }
    // }
    // function isValidAddress(address) {
    //   // Check if the address is an IPv4 or IPv6 address with optional port number
    //   const addressRegex =
    //     /^(?:(?:[0-9]{1,3}\.){3}[0-9]{1,3})|(?:\[(?:(?:[0-9]{1,3}\.){3}[0-9]{1,3})|(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4})\]?(?::\d+)?|[A-Za-z]+)$/i;
    //   return addressRegex.test(address);
    //   // return true;
    // }
    callback();
  }
};
export default validators;
