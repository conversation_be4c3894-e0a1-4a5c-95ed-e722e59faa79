<template>
  <pro-page class="card-list" :title="$t('pa.flow.design')">
    <!-- 卡片列表头部信息 -->
    <div slot="operation" class="card-list-header">
      <bs-search
        v-model="keywords"
        v-access="'PA.FLOW.PROJECT_MGR.SEARCH'"
        :placeholder="$t('pa.flow.placeholder1')"
        size="small"
        maxlength="30"
      />
      <el-button
        v-for="item in operateButtonList"
        :key="item.label"
        :icon="item.icon"
        type="primary"
        size="small"
        @click="operateHandler(item.event)"
      >
        {{ item.label }}
      </el-button>
      <!-- 新建项目弹窗 -->
      <add-project v-if="addProjectVisible" :visible.sync="addProjectVisible" @close="refreshList(false)" />
    </div>
    <!-- 卡片列表项目信息 -->
    <card-list-content ref="cardList" :keywords="keywords" />
  </pro-page>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { hasPermission } from '@/utils';
import CardListContent from './card-list-content.vue';
import { openProjectEditingDialog } from '../../design/flow-list/modals';
@Component({
  components: { CardListContent }
})
export default class CardList extends Vue {
  keywords = '';
  refreshContent = false;
  isRefresh = false;
  addProjectVisible = false;

  get operateButtonList() {
    return [
      { label: this.$t('pa.flow.new'), event: 'showAddEditProject', access: 'PA.FLOW.PROJECT_MGR.ADD' },
      { label: this.$t('pa.flow.refresh'), event: 'refreshList', access: 'PA.FLOW.PROJECT_MGR.VIEW' },
      {
        label: this.$t('pa.flow.view1'),
        event: 'swtichToNormalList',
        icon: 'el-icon-sort',
        access: 'PA.FLOW.FLOW_MGR.VIEW'
      }
    ].filter((item) => hasPermission(item.access));
  }

  operateHandler(event: string) {
    event && this[event]();
  }

  // 显示新建项目弹窗
  async showAddEditProject() {
    const needRefresh = await openProjectEditingDialog();
    needRefresh && this.refreshList(false);
  }

  // 卡片列表/流程视图切换
  swtichToNormalList() {
    this.$emit('switch', false);
  }

  // 刷新卡片列表
  refreshList(refresh = true) {
    (this.$refs.cardList as any).getCardProjectList(refresh);
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-icon-sort {
  transform: rotate(90deg);
}
::v-deep .el-input {
  width: 100% !important;
}
.card-list-header {
  & > * {
    margin-left: 10px;
  }
}
.search-drawer {
  top: 158px;
  bottom: 15px;
  right: 20px;
}
</style>
