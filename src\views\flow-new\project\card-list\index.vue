<template>
  <pro-page class="card-list" title="流程设计">
    <!-- 卡片列表头部信息 -->
    <div slot="operation" class="card-list-header">
      <bs-search
        v-model="keywords"
        v-access="'PA.FLOW.PROJECT_MGR.SEARCH'"
        placeholder="请输入项目名称模糊搜索"
        size="small"
        maxlength="30"
      />
      <el-button
        v-access="'PA.FLOW.PROJECT_MGR.SEARCH'"
        plain
        type="primary"
        @click="showFullSearchDrawer"
      >
        全量搜索
      </el-button>
      <el-button
        v-for="item in operateButtonList"
        :key="item.label"
        :icon="item.icon"
        type="primary"
        size="small"
        @click="operateHandler(item.event)"
      >
        {{ item.label }}
      </el-button>
      <!-- 新建项目弹窗 -->
      <add-project
        v-if="addProjectVisible"
        :visible.sync="addProjectVisible"
        @close="refreshList(false)"
      />
    </div>
    <!-- 卡片列表项目信息 -->
    <card-list-content ref="cardList" :keywords="keywords" />
  </pro-page>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { hasPermission } from '@/utils';
import CardListContent from './card-list-content.vue';
import AddProject from '@/views/flow-new/design/flow-list/modals/add-edit-project.vue';
@Component({
  components: {
    CardListContent,
    AddProject
  }
})
export default class CardList extends Vue {
  keywords = '';
  refreshContent = false;
  isRefresh = false;
  addProjectVisible = false;

  get operateButtonList() {
    return [
      { label: '新建', event: 'showAddEditProject', access: 'PA.FLOW.PROJECT_MGR.ADD' },
      { label: '刷新', event: 'refreshList', access: 'PA.FLOW.PROJECT_MGR.VIEW' },
      {
        label: '切换视图',
        event: 'swtichToNormalList',
        icon: 'el-icon-sort',
        access: 'PA.FLOW.FLOW_MGR.VIEW'
      }
    ].filter((item) => hasPermission(item.access));
  }

  operateHandler(event: string) {
    event && this[event]();
  }

  // 显示新建项目弹窗
  showAddEditProject() {
    this.addProjectVisible = true;
  }

  // 打开全量搜索抽屉
  showFullSearchDrawer() {
    this.$emit('showDrawer');
  }

  // 卡片列表/流程视图切换
  swtichToNormalList() {
    this.$emit('switch', false);
  }

  // 刷新卡片列表
  refreshList(refresh = true) {
    (this.$refs.cardList as any).getCardProjectList(refresh);
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-icon-sort {
  transform: rotate(90deg);
}
::v-deep .el-input {
  width: 100% !important;
}
.card-list-header {
  & > * {
    margin-left: 10px;
  }
}
.search-drawer {
  top: 158px;
  bottom: 15px;
  right: 20px;
}
</style>
