import CryptoJS from 'crypto-js';
const { sm4 } = require('sm-crypto');

export const ENCRYPT_MAPPING: any = {
  SM4(text: string, key: string, iv: string) {
    return sm4.encrypt(text, key, { mode: 'cbc', iv });
  },
  BASE64(text: string) {
    return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(text));
  }
};
export const DECRYPT_MAPPING: any = {
  SM4(text: string, key: string, iv: string) {
    return sm4.decrypt(text, key, { mode: 'cbc', iv });
  },
  BASE64(text: string) {
    return CryptoJS.enc.Utf8.stringify(CryptoJS.enc.Base64.parse(text));
  }
};
export const isValidMode = (type: string) => ['SM4', 'BASE64'].includes(type);
export const encode = (text: string, { encryptionMethod = 'BASE64', publicKey = '', iv = '' }: any = {}) => {
  return typeof ENCRYPT_MAPPING[encryptionMethod] === 'function'
    ? ENCRYPT_MAPPING[encryptionMethod](text, publicKey, iv)
    : text;
};
export const decode = (text: string, { encryptionMethod = 'BASE64', publicKey = '', iv = '' }: any = {}) => {
  return typeof DECRYPT_MAPPING[encryptionMethod] === 'function'
    ? DECRYPT_MAPPING[encryptionMethod](text, publicKey, iv)
    : text;
};

export const isValidArray = (raw: any) => Array.isArray(raw) && raw.length > 0;
const sortArray = (data: any[], key: string) => {
  return data
    .reduce((pre, next) => {
      if (isValidArray(next.children)) {
        next.children = sortArray(next.children, key);
      }
      pre.push(next);
      return pre;
    }, [])
    .sort((pre, next) => pre[key] - next[key]);
};
export const getMenu = (raw: any) => (isValidArray(raw) ? sortArray(raw, 'orderNum') : []);
