<template>
  <bs-table
    v-loading="loading"
    height="calc(100vh - 231px)"
    :data="tableData"
    :column-settings="false"
    :column-data="columnData"
    :page-data="pageData"
    @page-change="handlePageChange"
  />
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from 'vue-property-decorator';
import { getCatalogTableFields } from '@/apis/dataApi';
@Component
export default class TableInfo extends Vue {
  @Prop({ default: '' }) id!: string;
  @Prop({ default: () => {} }) tableSearch!: any;
  pageData = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 0 };
  tableData = [];
  columnData = [];
  loading = false;

  @Watch('tableSearch', { deep: true })
  tableSearchChange() {
    this.getTableInfoList();
  }

  // 获取表信息
  async getTableInfoList() {
    try {
      this.loading = true;
      const params: any = {
        catalogId: this.id,
        tableName: this.tableSearch.tableName,
        requestSearchParam: {
          pageData: this.pageData,
          search: this.tableSearch.keyword
        }
      };
      const { data, success, msg } = await getCatalogTableFields(params);
      if (!success) return this.$message.error(msg);
      this.columnData = data.columnData;
      this.tableData = data.tableData;
      this.pageData = data.pageData;
    } finally {
      this.loading = false;
    }
  }
  // 监听分页变换
  handlePageChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    this.getTableInfoList();
  }
}
</script>

<style lang="scss" scoped></style>
