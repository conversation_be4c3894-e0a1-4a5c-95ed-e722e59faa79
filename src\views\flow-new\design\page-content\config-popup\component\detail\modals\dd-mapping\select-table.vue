<template>
  <bs-table
    ref="tableRef"
    stripe
    crossing
    selection
    size="mini"
    paging-front
    :height="height"
    row-key="filedName"
    :data="showTableData"
    :page-data="pageData"
    :selectable="!hasClassName ? false : !disabled"
    :column-settings="false"
    :column-data="columnData"
    :checked-rows="checkedRows"
    class="select-table__container"
    :show-multiple-selection="false"
    @page-change="handlePageChange"
    @selection-change="handleSelectionChange"
  >
    <!-- 选择全部 -->
    <template slot="footer-expand">
      <el-checkbox v-model="checkAll" :disabled="disabled" @change="handleCheckedAll">
        <span>{{ $t('pa.flow.selectAll1') }}</span>
        <span class="config-select__total">{{ $t('pa.flow.msg299', [checkedRows.length]) }}</span>
      </el-checkbox>
    </template>
    <!-- 字段名称 -->
    <template slot="filedName" slot-scope="{ row }">
      <div v-if="row.isNesting || row.derive" class="select-table__nesting">
        <el-tooltip v-hide effect="light" placement="top" :content="row.filedName">
          <div class="select-table__name select-table__name--nesting">{{ row.filedName }}</div>
        </el-tooltip>
        <bs-tag v-if="row.derive" style="margin-left: 8px">{{ $t('pa.flow.ys') }}</bs-tag>
        <bs-tag v-if="row.isNesting" style="margin-left: 8px">{{ $t('pa.flow.qt') }}</bs-tag>
      </div>
      <el-tooltip v-else v-hide effect="light" placement="top" :content="row.filedName">
        <div class="select-table__name">{{ row.filedName }}</div>
      </el-tooltip>
    </template>
    <!-- 输入字段 -->
    <template slot="chooseFieldFromParent" slot-scope="{ row }">
      <div v-if="row.selected">
        <span v-if="row.derive">
          <span v-if="row.deriveDependentStr">{{ $t('pa.flow.msg300', [row.deriveDependentStr]) }}</span>
          <span v-else>{{ $t('pa.flow.msg301', [row.deriveStr]) }}</span>
        </span>
        <bs-select
          v-else
          v-model="row.chooseFieldFromParent"
          filterable
          clearable
          size="small"
          :placeholder="$t('pa.placeholder.select')"
          style="width: 100%"
          :disabled="disabled"
          :options="inputFields"
        />
      </div>
    </template>
  </bs-table>
</template>
<script lang="ts">
import { Component, Prop, Watch, Vue } from 'vue-property-decorator';
import { includesPro } from '@/utils';
import { cloneDeep } from 'lodash';

@Component
export default class SelectTable extends Vue {
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: 285 }) height!: number;
  @Prop({ default: () => [] }) data!: any[];
  @Prop({ default: '' }) keyword!: string;
  @Prop({ default: () => [] }) inputFields!: any[];
  @Prop({ default: false }) hasClassName!: boolean;

  checkAll = false;
  columnData = [
    {
      label: this.$t('pa.flow.label67'),
      value: 'filedName',
      width: 350,
      showOverflowTooltip: false
    },
    {
      label: this.$t('pa.flow.fieldType'),
      value: 'filedType',
      width: 245
    },
    {
      label: this.$t('pa.flow.inputField'),
      value: 'chooseFieldFromParent',
      width: 350,
      showOverflowTooltip: false
    }
  ];
  pageData = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 0 };

  get showTableData() {
    return this.data.filter(({ show }) => show);
  }
  get checkedRows() {
    return this.data.filter(({ selected }) => selected);
  }

  @Watch('keyword')
  filter() {
    if (!Array.isArray(this.data)) return;
    this.data.forEach((it) => this.$set(it, 'show', includesPro(it.filedName, this.keyword)));
    this.pageData.currentPage = 1;
    this.pageData.total = this.showTableData.length;
  }

  /* 处理选择全部事件 */
  handleCheckedAll(checked: boolean) {
    this.data.forEach((it) => this.$set(it, 'selected', checked));
  }
  /* 处理页数变化 */
  handlePageChange(currentPage: number, pageSize: number) {
    this.pageData.currentPage = pageSize !== this.pageData.pageSize ? 1 : currentPage;
    this.pageData.pageSize = pageSize;
  }
  /* 处理选择变化 */
  handleSelectionChange(data: any[] = []) {
    const validFiledNames = data.map(({ filedName }) => filedName);
    this.data.forEach((it) => {
      const has = validFiledNames.includes(it.filedName);
      this.$set(it, 'selected', has);
      this.$set(it, 'chooseFieldFromParent', this.getInitValue(it, has));
    });
    this.checkAll = this.data.length > 0 && this.checkedRows.length === this.data.length;
  }
  getInitValue(it: any, has: boolean) {
    if (!has) return '';
    if (it.chooseFieldFromParent) return it.chooseFieldFromParent;
    const flag = this.inputFields.some(({ value }) => value === it.filedName);
    return flag ? it.filedName : '';
  }
  /* public 表格校验 */
  public validate() {
    return new Promise((resolve, reject) => {
      const throwErr = (msg: string) => {
        msg && this.$tip.error(msg);
        return reject(msg);
      };
      if (this.checkedRows.length < 1) return throwErr(this.$t('pa.flow.msg83'));
      const [relyList, otherList, mapping] = this.getVerifyConfg();
      /* 衍生字段 选中校验 */
      const relyMsg = this.validateRelyField(relyList);
      if (relyMsg) return throwErr(relyMsg);
      /* 其他字段 */
      const otherMsg = this.validateField(otherList, mapping);
      if (otherMsg) return throwErr(otherMsg);
      /* 校验完成 */
      const data = cloneDeep(this.data).map((it) => {
        delete it.show;
        delete it.isNesting;
        delete it.deriveDependentStr;
        return it;
      });
      resolve(data);
    });
  }
  /* 获取校验配置 */
  getVerifyConfg() {
    const result: any[] = [[], [], {}];
    for (let i of this.checkedRows) {
      if (i.derive && i.deriveDependentStr) result[0].push(i);
      if (!i.derive) result[1].push(i);
      result[2][i.filedName] = i.chooseFieldFromParent;
    }
    return Object.freeze(result);
  }
  /* 衍生字段 选中校验 */
  validateRelyField(list: any[]) {
    for (let { filedName, deriveDependentStr } of list) {
      const target = this.data.find((it) => it.filedName === deriveDependentStr);
      if (!target?.selected) {
        return this.$t('pa.flow.msg302', [filedName, deriveDependentStr]);
      }
    }
  }
  /* 字段校验 */
  validateField(list: any[], mapping: any) {
    for (let { filedName } of list) {
      if (!mapping[filedName]) return this.$t('pa.flow.msg303', [filedName]);
    }
  }
}
</script>
<style lang="scss" scoped>
.select-table {
  &__nesting {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  &__name {
    display: inline-block;
    max-width: 322px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    &--nesting {
      max-width: 292px;
    }
  }
}
</style>
