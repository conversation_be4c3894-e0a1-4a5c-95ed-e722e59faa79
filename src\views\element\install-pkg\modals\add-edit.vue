<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="50%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
  >
    <el-form
      ref="ruleForm"
      v-loading="formLoading"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
      element-loading-text="数据正在加载中..."
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="名称" prop="title">
            <el-input
              v-model="formData.title"
              placeholder="请输入名称"
              maxlength="30"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="版本信息" prop="versionInfo">
            <el-input
              v-model="formData.versionInfo"
              placeholder="请输入内容"
              maxlength="30"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="文件" prop="pkg" style="text-align: left">
        <el-upload
          ref="upload"
          action
          :http-request="handleFile"
          :multiple="false"
          :limit="1"
          :on-exceed="handleExceed"
          :before-upload="beforeUpload"
          :file-list="formData.fileList"
        >
          <el-button size="small" type="primary">选择文件</el-button>
        </el-upload>
      </el-form-item>

      <el-form-item label="部署脚本">
        <el-select v-model="value" placeholder="请选择" style="width: 100%" @change="handlePkgCmd">
          <el-option
            v-for="(item, index) in dicList"
            :key="index"
            :label="item.title"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="安装命令" prop="installCmd">
        <el-input
          v-model="formData.installCmd"
          type="textarea"
          row="5"
          placeholder="请输入安装命令"
          :autosize="{ minRows: 4, maxRows: 20 }"
          maxlength="2048"
        />
      </el-form-item>
      <el-form-item label="启动命令" prop="startCmd">
        <el-input
          v-model="formData.startCmd"
          type="textarea"
          row="5"
          placeholder="请输入启动命令"
          :autosize="{ minRows: 4, maxRows: 20 }"
          maxlength="2048"
        />
      </el-form-item>
      <el-form-item label="停止命令" prop="stopCmd">
        <el-input
          v-model="formData.stopCmd"
          type="textarea"
          row="5"
          placeholder="请输入停止命令"
          :autosize="{ minRows: 4, maxRows: 20 }"
          maxlength="2048"
        />
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model="formData.memo"
          type="textarea"
          row="5"
          placeholder="请输入备注"
          maxlength="255"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" :loading="loading" @click="submit('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import {
  URL_INSTALLATIONPACKAGE_ADD,
  URL_INSTALLATIONPACKAGE_UPDATE,
  URL_DIC_GETSUBBYPARENT
} from '@/apis/commonApi';
import { cloneDeep } from 'lodash';
import { PaBase } from '@/common/pipeace-base';
interface TableRecord {
  id: string;
  title: string;
  versionInfo: string;
  installCmd: string;
  startCmd: string;
  stopCmd: string;
  type: string;
  fileList: any[];
  updatedBy: string;
  updateTime: Date;
  orgId: string;
  orgPath: string;
  createdBy: string;
  createTime: Date;
}

@Component
export default class AddEdit extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: '新建' }) title!: string;
  @Prop({ default: {} }) data!: any;
  @Prop({ default: false }) formLoading!: boolean;

  formData: any = {};
  loading = false;

  dicList: any = {};
  value: any = {};
  rules: any = {
    title: [
      { required: true, message: '请输入名称', trigger: 'blur' },
      { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
    ],
    versionInfo: [
      { required: true, message: '请输入版本信息', trigger: 'blur' },
      { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
    ],
    installCmd: [{ required: true, message: '请输入安装命令', trigger: 'input' }],
    startCmd: [{ required: true, message: '请输入启动命令', trigger: 'input' }],
    stopCmd: [{ required: true, message: '请输入停止命令', trigger: 'input' }],
    pkg: [{ required: false, validator: this.validateFile, trigger: 'blur' }]
  };
  validateFile(rule: any, value: any, callback: any) {
    if (this.formData.fileList === undefined || this.formData.fileList.length === 0) {
      if (this.formData.id) {
        callback();
      } else {
        callback(new Error('请添加文件'));
      }
    } else {
      callback();
    }
  }

  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  private closeDialog(needFresh: boolean) {
    (this.$refs.upload as any).clearFiles();
    this.formData = {};
    this.loading = false;
    this.formLoading = false;
  }

  /**
   * 表单提交
   */
  submit(formName: string) {
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        Vue.axios.defaults.timeout = 1500000;
        Vue.axios.defaults.headers.post['Content-Type'] = 'multipart/form-data';

        const submitData = new FormData();
        if (this.formData.id) {
          submitData.append('id', this.formData.id);
        }
        submitData.append('title', this.formData.title);
        submitData.append('versionInfo', this.formData.versionInfo);
        submitData.append('type', this.formData.type);
        submitData.append('installCmd', this.formData.installCmd);
        submitData.append('startCmd', this.formData.startCmd);
        submitData.append('stopCmd', this.formData.stopCmd);
        submitData.append('memo', this.formData.memo === undefined ? '' : this.formData.memo);
        if (this.formData.fileList !== undefined) {
          submitData.append('file', this.formData.fileList[0]);
        }
        this.loading = true;
        if (this.formData.id) {
          this.doPut(URL_INSTALLATIONPACKAGE_UPDATE, submitData).then((resp: any) => {
            this.parseResp(resp);
          });
        } else {
          this.doPost(URL_INSTALLATIONPACKAGE_ADD, submitData).then((resp: any) => {
            this.parseResp(resp);
          });
        }
      } else {
        this.$message.error('请检查输入内容');
        return false;
      }
    });
  }
  /**
   * 解析返回结果
   */
  private parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.closeDialog(true);
    });
  }
  beforeUpload(file) {
    const isLt300M = file.size / 1024 / 1024 < 300;
    if (!isLt300M) {
      this.formData.fileList = [];
      this.$message.error('不能上传超过300MB的文件!');
    }
    return isLt300M;
  }
  /**
   * 处理文件上传
   */
  private handleFile(file: any) {
    this.formData.fileList = [];
    this.formData.fileList.push(file.file);
  }
  /**
   * 判断上传文件个数
   */
  private handleExceed(files: any) {
    this.$message.warning(`最多上传 ${files.length} 个文件`);
  }

  /**
   * 获取安装包字典
   */
  handleDicList(parentCode: string) {
    this.doGet(URL_DIC_GETSUBBYPARENT, {
      params: {
        parentCode
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.dicList = resp.data;
      });
    });
  }
  public handlePkgCmd() {
    this.$set(this.formData, 'installCmd', this.value.value1);
    this.$set(this.formData, 'startCmd', this.value.value2);
    this.$set(this.formData, 'stopCmd', this.value.value3);
  }

  @Watch('data')
  onDataChange() {
    this.formData = cloneDeep(this.data);
    this.handleDicList(this.formData.type);
  }
}
</script>

<style scoped></style>
