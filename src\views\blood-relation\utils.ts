export const fullScreen = (el: HTMLElement | any) => {
  return new Promise((resolve, reject) => {
    try {
      if (el.requestFullscreen) {
        el.requestFullscreen();
      } else if (el.msRequestFullscreen) {
        el.msRequestFullscreen();
      } else if (el.mozRequestFullScreen) {
        el.mozRequestFullScreen();
      } else if (el.webkitRequestFullScreen) {
        el.webkitRequestFullScreen();
      }
      resolve(true);
    } catch (e) {
      reject(e);
    }
  });
};
export const exitFullScreen = () => {
  return new Promise((resolve, reject) => {
    try {
      const doc: any = document;
      if (doc.exitFullscreen) {
        document.exitFullscreen();
      } else if (doc.msExitFullscreen) {
        doc.msExitFullscreen();
      } else if (doc.mozCancelFullScreen) {
        doc.mozCancelFullScreen();
      } else if (doc.webkitCancelFullScreen) {
        doc.webkitCancelFullScreen();
      }
      resolve(true);
    } catch (e) {
      reject(e);
    }
  });
};
