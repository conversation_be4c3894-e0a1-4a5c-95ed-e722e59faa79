import G6 from '@antv/g6';
import type { SourceData, Node, SerachType, SearchParams } from './type';
import i18n from '@/i18n';
import { getLanguage } from '@/i18n';
import { decode } from '@/store/utils';
/**
 * 超长文本的截取
 *
 * @param {string} 字符串内容
 * @param {number} 最大显示宽度
 * @param {number} 字体大小
 * @returns {string}
 */
const fittingString = (str: string, maxWidth: number, fontSize: number) => {
  const ellipsis = '...';
  const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0];
  let currentWidth = 0;
  let res = str;
  const pattern = new RegExp('[\u4E00-\u9FA5]+'); // distinguish the Chinese charactors and letters
  str.split('').forEach((letter, i) => {
    if (currentWidth > maxWidth - ellipsisLength) return;
    if (pattern.test(letter)) {
      // Chinese charactors
      currentWidth += fontSize;
    } else {
      // get the width of single letter according to the fontSize
      currentWidth += G6.Util.getLetterWidth(letter, fontSize);
    }
    if (currentWidth > maxWidth - ellipsisLength) {
      res = `${str.substr(0, i)}${ellipsis}`;
    }
  });
  return res;
};

// 计算cubic的拐点
const getCubicPoints = (startPoint: { x: number; y: any }, endPoint: { x: number; y: any }, isLToR = true) => {
  const deltaY = Math.abs(endPoint.x - startPoint.x);
  const control = Math.floor((deltaY / 3) * 2);
  const v1 = { x: startPoint.x + (isLToR ? control : -control), y: startPoint.y };
  const v2 = { x: endPoint.x + (isLToR ? -control : control), y: endPoint.y };
  return [v1.x, v1.y, v2.x, v2.y];
};

/**
 * 获取节点大小
 *
 * @param {*} cfg
 */
const getNodeConfig = ({ resType }: { resType: 'JOB' | 'SERVICE' | 'TABLE' | 'CATALOG' }) => {
  const size = [300, 60];
  const colorMap = {
    JOB: '#F8B205',
    SERVICE: '#1CA3FE',
    TABLE: '#45BC8A',
    CATALOG: '#E9B900'
  };
  if (resType === 'CATALOG' || resType === 'TABLE') {
    size[1] = 40;
  }
  return { size, color: colorMap[resType] };
};

/**
 * 获取节点类型对应的文案
 *
 * @param {*} resType
 * @returns {*}
 */
const getNodeResType = (resType: 'JOB' | 'SERVICE' | 'TABLE' | 'CATALOG') => {
  return {
    JOB: i18n.t('pa.flowName'),
    SERVICE: i18n.t('pa.service'),
    TABLE: i18n.t('pa.table'),
    CATALOG: 'Catalog'
  }[resType];
};

/**
 * 获取显示tag信息
 *
 * @param {('dynamic' | 'dep')} tag
 * @returns {{}}
 */
const getNodeTag = (tag: 'dynamic' | 'dep') => {
  const isEn = getLanguage() === 'en-US';
  return {
    dynamic: [i18n.t('pa.blood.dynamic'), '#FFEEEE', '#FF5353', isEn ? 62 : 40],
    dep: [i18n.t('pa.blood.dep'), '#FFF5EA', '#FF9E2B', isEn ? 134 : 64]
  }[tag];
};

/**
 * 将svg图片转化成dataUrl
 */
const transSvgToDataURL = (svgContent) => {
  return `data:image/svg+xml;base64,${window.btoa(
    String.fromCharCode.apply(null, new TextEncoder().encode(svgContent) as any)
  )}`;
};
/**
 * 将源数据转化成画布节点数据
 *
 * @param {SourceData} SourceData
 */
export const transformToNodeData: (SourceData: SourceData | SourceData[], isRoot?: boolean) => Node[] = (
  SourceData,
  isRoot = false
) => {
  return (Array.isArray(SourceData) ? SourceData : [SourceData]).map(
    ({
      id,
      resType,
      jobName = '',
      projectName = '',
      serviceName,
      sinkCount = 0,
      sourceCount = 0,
      depUpstream,
      noResource,
      isDynamic,
      iconByte,
      resourceList = '',
      componentType
    }) => {
      const getSubName = () => {
        if (resType === 'JOB') return projectName;
        if (resType === 'CATALOG' || resType === 'TABLE' || depUpstream || noResource) return '';
        return resourceList;
      };
      return {
        id,
        type: '',
        isRoot,
        resType,
        name: serviceName || jobName,
        subName: getSubName(),
        img: iconByte,
        tag: depUpstream ? 'dep' : isDynamic ? 'dynamic' : undefined,
        // 节点下游数量，表示可以向下游扩展查询
        sinkCount,
        // 节点上游数量，表示可以向上游扩展查询
        sourceCount,
        // 表示节点默认上下游不展开 根节点默认展开
        sinkExpand: isRoot ? true : false,
        sourceExpand: isRoot ? true : false,
        componentType
      };
    }
  );
};

/**
 * 获取查找上下游节点的请求参数
 *
 * @param {SourceData} SourceData
 * @param {SerachType} type
 * @returns {{ relatedInformation: string; serviceType: string; serviceId: string; resourceCsv: string; componentType: SerachType; isDynamic: boolean; }}
 */
export const getReqParams: (SourceData: SourceData, type: SerachType) => SearchParams = (SourceData, type) => {
  const { relatedInformation = '', resType = '', serviceId = '', resourceList = '', isDynamic = false } = SourceData;
  return {
    relatedInformation,
    serviceType: resType,
    serviceId,
    resourceList,
    componentType: type,
    isDynamic
  };
};
const getImgUrl = () => {
  return (
    {
      'zh-CN': 'search-node',
      'zh-HK': 'search-node-hk',
      'en-US': 'search-node-en'
    }[getLanguage()] || 'search-node'
  );
};
// 自定义节点
G6.registerNode('blood-relation-node', {
  draw(cfg: any, group) {
    // console.log(cfg);
    const {
      isRoot,
      resType,
      name,
      subName,
      img,
      tag,
      sinkCount,
      sinkExpand,
      sourceCount,
      sourceExpand,
      componentType,
      inCircle
    } = cfg;
    const { size, color } = getNodeConfig(cfg);
    // 外边框宽度
    const borderWidth = isRoot ? 2 : 1;
    // 偏移
    const offsetWidth = isRoot ? 1 : 0.5;
    // 服务图标偏移
    const imageOffset = img ? 50 : 0;
    const serveImg = img ? decode(img) : '';
    // tag存在时的偏移
    let tagOffset = 0;
    const offsetX = isRoot
      ? 10
      : sourceCount && componentType === 'SOURCE'
      ? sourceExpand
        ? 10
        : 34
      : sourceExpand
      ? 10
      : 2;
    const offsetY = resType === 'CATALOG' || resType === 'TABLE' ? 10 : 0;
    const transAttrs = (attr) => {
      attr.x = attr.x + offsetX;
      attr.y = attr.y + offsetY;
      return attr;
    };
    const keyShape = group.addShape('rect', {
      attrs: {
        x: 0,
        y: 0,
        width: 300,
        height: 60,
        stroke: 'transparent',
        fill: 'transparent'
      },
      name: 'rect-wapper-box'
    });
    // 查询节点的标记
    if (isRoot) {
      console.log(getLanguage(), getImgUrl());
      group.addShape('image', {
        attrs: transAttrs({
          x: 0,
          y: -19,
          img: require(`./${getImgUrl()}.svg`),
          width: 84,
          height: 30
        })
      });
    }
    // 节点左侧：类型
    group.addShape('rect', {
      attrs: transAttrs({
        x: 0,
        y: 0,
        width: 60,
        height: size[1],
        radius: [9, 0, 0, 9],
        fill: color
      }),
      name: 'node-left'
    });
    // 节点左侧：类型文本
    group.addShape('text', {
      attrs: transAttrs({
        text: getNodeResType(resType),
        x: borderWidth + 30,
        y: size[1] / 2,
        textAlign: 'center',
        textBaseline: 'middle',
        fill: '#fff',
        fontSize: resType === 'CATALOG' ? 12 : 14
      }),
      name: 'node-left-text'
    });
    // 节点右侧
    group.addShape('rect', {
      attrs: transAttrs({
        x: 60,
        y: 0,
        width: size[0] - 60,
        height: size[1],
        radius: [0, 9, 9, 0],
        fill: '#fff'
      })
    });
    // 节点右侧图片
    if (serveImg) {
      group.addShape('rect', {
        attrs: transAttrs({
          x: borderWidth + 70,
          y: borderWidth + 10,
          width: 40,
          height: 40,
          fill: '#F2F4F7',
          radius: [4, 4, 4, 4]
        })
      });
      group.addShape('image', {
        attrs: transAttrs({
          x: borderWidth + 70,
          y: borderWidth + 10,
          width: 40,
          height: 40,
          img: transSvgToDataURL(serveImg)
        })
      });
    }

    // 节点右侧：节点标题
    group.addShape('text', {
      attrs: transAttrs({
        // 文字边距 20
        text: fittingString(name, size[0] - (80 + imageOffset) - borderWidth * 2, 14),
        x: borderWidth + 70 + imageOffset,
        y: borderWidth + 12 + (!subName && !tag && offsetY === 0 ? 12 : 0), // 无tag和副标题的节点  标题垂直居中
        textAlign: 'start',
        textBaseline: 'top',
        fill: '#444',
        fontSize: 14,
        fontWeight: 500
      })
    });
    // 节点右侧：节点tag
    if (tag) {
      const [text, color, textColor, tagWidth] = getNodeTag(tag);
      tagOffset = (tagWidth as number) + 10;
      group.addShape('rect', {
        attrs: transAttrs({
          x: borderWidth + 70 + imageOffset,
          y: borderWidth + 30,
          radius: [4, 4, 4, 4],
          width: tagWidth as number,
          height: 18,
          fill: color as string,
          fontSize: 14,
          fontWeight: 500
        })
      });
      group.addShape('text', {
        attrs: transAttrs({
          x: borderWidth + 70 + imageOffset + 8,
          y: borderWidth + 34,
          text: text,
          textAlign: 'start',
          textBaseline: 'top',
          fill: textColor as string,
          fontSize: 12
        })
      });
    }
    // 节点右侧：副标题
    group.addShape('text', {
      attrs: transAttrs({
        x: borderWidth + 70 + imageOffset + tagOffset,
        y: borderWidth + 34,
        text: fittingString(subName, size[0] - (60 + imageOffset + tagOffset) - borderWidth * 2, 14),
        textAlign: 'start',
        textBaseline: 'top',
        fill: '#777',
        fontSize: 12
      })
    });
    // 外层的正方形
    group.addShape('rect', {
      attrs: transAttrs({
        x: offsetWidth,
        y: offsetWidth,
        width: size[0] - borderWidth,
        height: size[1] - borderWidth,
        radius: [8, 8, 8, 8],
        stroke: '#C3D8FF',
        lineWidth: borderWidth,
        fill: 'transparent',
        cursor: 'pointer'
      }),
      name: 'rect-wapper'
    });

    // 节点可扩展节点 sinkCount > 0 表示可以向下游（画布右侧）扩展查询, 节点右侧展示
    // 且限制处于下游的节点可以向下游扩展查询
    if (sinkCount && (isRoot || inCircle ? true : componentType === 'SINK')) {
      // 节点为展开状态
      if (sinkExpand) {
        group.addShape('circle', {
          attrs: transAttrs({
            x: size[0] - offsetWidth,
            y: size[1] / 2,
            r: 8,
            fill: inCircle ? '#E5E5E5' : '#fff',
            stroke: inCircle ? '#aaa' : '#377cff',
            cursor: 'pointer'
          }),
          name: 'right-circle'
        });
        group.addShape('rect', {
          attrs: transAttrs({
            x: size[0] - offsetWidth - 4,
            y: size[1] / 2 - 1,
            width: 8,
            height: 2,
            fill: inCircle ? '#aaa' : '#377cff'
          }),
          name: 'right-circle-text'
        });
      } else {
        // 未展开 显示下游节点数量
        group.addShape('rect', {
          attrs: transAttrs({
            x: size[0],
            y: size[1] / 2 - 1,
            width: 8,
            height: 2,
            fill: '#D4DBE9'
          }),
          name: 'right-number-line'
        });
        group.addShape('circle', {
          attrs: transAttrs({
            x: size[0] + 20,
            y: size[1] / 2,
            r: 12,
            fill: '#EBF2FF',
            stroke: '#C3D8FF',
            cursor: 'pointer'
          }),
          name: 'right-number-wapper'
        });
        group.addShape('text', {
          attrs: transAttrs({
            x: size[0] + 20,
            y: size[1] / 2 - 4,
            text: sinkCount,
            textAlign: 'center',
            textBaseline: 'top',
            fill: '#377CFF',
            fontSize: 12,
            cursor: 'pointer'
          }),
          name: 'right-number'
        });
      }
    }
    // 节点可扩展节点 sourceCount > 0 表示可以向上游（画布左侧）扩展查询, 节点左侧展示
    // 且限制处于上游的节点可以向上游扩展查询
    if (sourceCount && (isRoot || inCircle ? true : componentType === 'SOURCE')) {
      // 节点为展开状态
      if (sourceExpand) {
        group.addShape('circle', {
          attrs: transAttrs({
            x: offsetWidth,
            y: size[1] / 2,
            r: 8,
            fill: inCircle ? '#E5E5E5' : '#fff',
            stroke: inCircle ? '#aaa' : '#377cff',
            cursor: 'pointer'
          }),
          name: 'left-circle'
        });
        group.addShape('rect', {
          attrs: transAttrs({
            x: offsetWidth - 4,
            y: size[1] / 2 - 1,
            width: 8,
            height: 2,
            fill: inCircle ? '#aaa' : '#377cff'
          }),
          name: 'left-circle-text'
        });
      } else {
        // 未展开 显示下游节点数量
        group.addShape('rect', {
          attrs: transAttrs({
            x: -8,
            y: size[1] / 2 - 1,
            width: 8,
            height: 2,
            fill: '#D4DBE9'
          }),
          name: 'left-number-line'
        });
        group.addShape('circle', {
          attrs: transAttrs({
            x: -20,
            y: size[1] / 2,
            r: 12,
            fill: '#EBF2FF',
            stroke: '#C3D8FF',
            cursor: 'pointer'
          }),
          name: 'left-number-wapper'
        });
        group.addShape('text', {
          attrs: transAttrs({
            x: -20,
            y: size[1] / 2 - 4,
            text: sourceCount,
            textAlign: 'center',
            textBaseline: 'top',
            fill: '#377CFF',
            fontSize: 12,
            cursor: 'pointer'
          }),
          name: 'left-number'
        });
      }
    }
    return keyShape;
  },
  afterDraw(cfg, group) {
    // group!.find((element) => element.get('name') === 'left-number');
  },
  setState(name, value, item: any) {
    const group = item.getContainer();
    const model = item.getModel();
    const shapes = group.get('children');
    if (name === 'selected' || name === 'active') {
      shapes.forEach((shape) => {
        const name = shape.cfg.name;
        if (name === 'rect-wapper') {
          shape.attr('stroke', value ? '#377CFF' : '#C3D8FF');
          !model.isRoot && shape.attr('lineWidth', value ? 2 : 1);
        }
        if (name === 'right-number-wapper' || name === 'left-number-wapper') {
          shape.attr('stroke', value ? '#377CFF' : '#C3D8FF');
        }
        if (name === 'right-number-line' || name === 'left-number-line') {
          shape.attr('fill', value ? '#377CFF' : '#D4DBE9');
        }
      });
    }
  }
});

// 自定义边
G6.registerEdge('custom-edge', {
  draw(cfg, group) {
    // console.log('1111', cfg);
    const startPoint: any = cfg.startPoint;
    const endPoint: any = cfg.endPoint;
    // 是否从左向右
    const isLToR = startPoint.x < endPoint.x;
    // 是否从下至上
    const isCircle = ((cfg.targetNode as any)._cfg.model.subNodes || []).includes(cfg.source);
    const isSameY = startPoint.y === endPoint.y;
    const yOffset = isCircle ? (isLToR ? 0 : -8) : 0;
    const startPointX = isCircle && !isLToR ? (cfg.sourceNode as any)._cfg.anchorPointsCache[0].x : startPoint.x;
    const endPointX = isCircle && !isLToR ? (cfg.targetNode as any)._cfg.anchorPointsCache[1].x : endPoint.x;
    const startPointY = startPoint.y + yOffset;
    const endPointY = endPoint.y + yOffset;
    const shape = group.addShape('path', {
      attrs: {
        stroke: '#999999', // 连线颜色
        paddingTop: 10,
        path: isSameY
          ? [
              ['M', startPointX, startPointY],
              ['L', endPointX, endPointY]
            ]
          : [
              ['M', startPointX, startPointY],
              [
                'C',
                ...getCubicPoints({ x: startPointX, y: startPointY }, { x: endPointX, y: endPointY }, isLToR),
                endPointX + (isLToR ? -10 : 10),
                endPointY
              ],
              ['L', endPointX, endPointY]
            ],
        // 单向箭头
        startArrow: false,
        endArrow: {
          path: 'M -6,4 L 0,0 L -6,-4',
          fill: '#333'
        },
        lineAppendWidth: 2,
        cursor: 'pointer'
      },
      // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
      name: 'path-shape'
    });
    return shape;
  },
  setState(name, value, item: any) {
    const group = item.getContainer();
    const shape = group.get('children')[0]; // 顺序根据 draw 时确定
    if (name === 'active') {
      if (value) {
        shape.attr('stroke', '#377CFF');
        shape.attr('lineWidth', 2);
      } else {
        shape.attr('stroke', '#999999');
        shape.attr('lineWidth', 1);
      }
    }
  }
});

/**
 * 画布节点tooltip配置
 *
 * @type {*}
 */
export const tooltip = () =>
  new G6.Tooltip({
    offsetX: 10,
    offsetY: 0,
    itemTypes: ['node'],
    shouldBegin: (e: any) => {
      const name = e.target.cfg && e.target.cfg.name;
      return ['rect-wapper', 'left-circle', 'left-circle-text', 'right-circle', 'right-circle-text'].includes(name);
    },
    getContent: (e: any) => {
      const model = e.item.getModel();
      const name = e.target.cfg && e.target.cfg.name;
      const inExpandCircle = ['left-circle', 'left-circle-text', 'right-circle', 'right-circle-text'].includes(name);
      const showTip = model.inCircle && inExpandCircle;
      return `<div style='max-width: 400px;overflow: hidden;word-break: break-all;'>
    ${showTip ? i18n.t('pa.blood.tip12') : ''}
    ${!showTip ? '<p>' + e.item.getModel().name + '</p>' : ''}
    ${!showTip ? '<p>' + e.item.getModel().subName + '</p>' : ''}
    ${
      !showTip
        ? `<br><p style="color: #aaa">${i18n.t('pa.blood.tip13')}${
            model.tag === 'dynamic' || model.isRoot ? '' : i18n.t('pa.blood.tip14')
          }</p>`
        : ''
    }
    </div>`;
    }
  });

/**
 * 全屏
 *
 * @param {(HTMLElement | any)} el
 */
export const fullScreen = (el: HTMLElement | any) => {
  return new Promise((resolve, reject) => {
    try {
      if (el.requestFullscreen) {
        el.requestFullscreen();
      } else if (el.msRequestFullscreen) {
        el.msRequestFullscreen();
      } else if (el.mozRequestFullScreen) {
        el.mozRequestFullScreen();
      } else if (el.webkitRequestFullScreen) {
        el.webkitRequestFullScreen();
      }
      resolve(true);
    } catch (e) {
      reject(e);
    }
  });
};
/**
 * 退出全屏
 */
export const exitFullScreen = () => {
  return new Promise((resolve, reject) => {
    try {
      const doc: any = document;
      if (doc.exitFullscreen) {
        document.exitFullscreen();
      } else if (doc.msExitFullscreen) {
        doc.msExitFullscreen();
      } else if (doc.mozCancelFullScreen) {
        doc.mozCancelFullScreen();
      } else if (doc.webkitCancelFullScreen) {
        doc.webkitCancelFullScreen();
      }
      resolve(true);
    } catch (e) {
      reject(e);
    }
  });
};
