import { AxiosRequestConfig, AxiosResponse, Method } from 'axios';
import axios from './http';

interface ExtraAxiosRequestConfig extends AxiosRequestConfig {
  [propname: string]: unknown;
}

const net = (
  url: string,
  method: Method = 'get',
  params: unknown = {},
  extraConf: ExtraAxiosRequestConfig = {}
): AxiosResponse['data'] => {
  const baseConfig: ExtraAxiosRequestConfig = {
    method,
    url,
    ...extraConf
  };
  if (['get', 'GET', 'delete', 'DELETE', undefined].includes(method)) {
    baseConfig.params = params;
  } else if (['post', 'POST', 'put', 'PUT'].includes(method)) {
    baseConfig.data = params;
  }
  return axios(baseConfig);
};
const get = (url: string, params: unknown = {}, config?: ExtraAxiosRequestConfig) => {
  return net(url, 'get', params, config);
};

const post = (url: string, params: unknown = {}, config?: ExtraAxiosRequestConfig) => {
  return net(url, 'post', params, config);
};
const postFile = (url: string, params: unknown = {}, config?: ExtraAxiosRequestConfig) => {
  const options: ExtraAxiosRequestConfig = {
    responseType: 'blob',
    timeout: 60000
  };
  Object.assign(options, config);
  return net(url, 'post', params, options);
};

const del = (url: string, params: unknown = {}, config?: ExtraAxiosRequestConfig) => {
  return net(url, 'delete', params, config);
};

const put = (url: string, params: unknown = {}, config?: ExtraAxiosRequestConfig) => {
  return net(url, 'put', params, config);
};

const file = (
  url: string,
  params: { [propretyName: string]: unknown } = {},
  config?: ExtraAxiosRequestConfig
) => {
  const formData = new FormData();
  Object.keys(params).forEach((key) => {
    formData.append(key, params[key] as string | Blob);
  });
  const method = config && config.method ? config.method : 'post';
  return net(url, method, formData, config);
};

// props处理返回的数据结果需要再取一层时获取的值的key值
const download = async (
  url: string,
  fileName = '',
  params: unknown = {},
  props = {
    blob: 'blob',
    fileName: 'fileName'
  },
  config?: ExtraAxiosRequestConfig
) => {
  const defaultConfig = {
    method: config && config.method ? config.method : 'get',
    url: config && config.url ? config.url : url,
    responseType: config && config.responseType ? config.responseType : 'blob'
  };
  config = config ? { ...defaultConfig, ...config } : defaultConfig;
  if (params) {
    if (config.method == 'get') {
      config.params = params;
    } else {
      config.data = params;
    }
  }
  const res: any = await axios(config);
  const blobData = res;
  const resFileName = res[props.fileName] || '';
  const responseUrl = window.URL.createObjectURL(new Blob([blobData.blob]));
  const link: any = document.createElement('a');
  link.style.display = 'none';
  link.href = responseUrl;
  link.setAttribute('download', decodeURIComponent(resFileName) || fileName); // 文件名
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

export { get, post, del, put, file, download, postFile };
export default net;
