<template>
  <bs-dialog title="this.$t('pa.dataPreview')" size="large" :visible="visible" @close="closeDialog">
    <bs-select
      :value="value"
      :options="options"
      multiple
      clearable
      filterable
      collapse-tags
      class="bs-select-style"
      @change="handleChange"
    />
    <pro-table
      ref="proTable"
      :columns="columnData"
      :request="tableRequest"
      :options="{ showPage: false, columnSettings: false, height: '514px' }"
    />
    <bs-empty v-if="columnData.length === 0" column-data="514px" />
    <div slot="footer">
      <el-button @click="closeDialog">{{ $t('pa.action.close') }}</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
interface IColumn {
  value: string;
  label: string;
}
import { Component, Vue, Prop } from 'vue-property-decorator';
@Component
export default class DataPreviewDialog extends Vue {
  @Prop() visible!: boolean;
  @Prop() id!: string; // 表id或者视图id
  @Prop() request!: any;
  value = [];
  options: IColumn[] = [];
  columnData: IColumn[] = [{ label: '', value: '' }];
  created() {}
  async tableRequest() {
    const { data = { columnNames: [], sqlJobTestResult: [] }, success, msg } = await this.request({ id: this.id });
    if (!success && msg) this.$message.error(msg);
    this.setcolumnDataAndOptions(data.columnNames);
    return { data: data.sqlJobTestResult };
  }
  setcolumnDataAndOptions(columnNames = []) {
    this.columnData = columnNames.map((item: string) => ({
      label: item,
      value: item,
      minWidth: item.length * 14 + 20
    }));
    this.options = [...this.columnData];
  }
  handleChange(val: string[] = []) {
    if (val.length === 0) {
      this.columnData = [...this.options];
    } else {
      this.columnData = this.options.filter((item) => val.includes(item.value));
    }
    // bugfix 处理列表纵向滚动区域高度问题
    setTimeout(() => {
      this.$refs.proTable && (this.$refs.proTable as any).$refs.elTable.doLayout();
    });
  }
  closeDialog() {
    this.$emit('update:visible', false);
  }
}
</script>
<style lang="scss" scoped>
.bs-select-style {
  display: block !important;
  text-align: right;
  margin-bottom: 16px;
}
::v-deep .bs-pro-table {
  border: 1px solid $--bs-color-border-lighter;
  border-bottom: none;
}
</style>
