import Vue, { VNode } from 'vue';

declare global {
  interface Base {
    [propName: string]: unknown;
  }
  interface MenuItem {
    icon?: string;
    index: string;
    title: string;
    children?: MenuItem[];
  }

  namespace JSX {
    // tslint:disable no-empty-interface
    interface Element extends VNode {}

    // tslint:disable no-empty-interface
    interface ElementClass extends Vue {}

    interface IntrinsicElements {
      [elem: string]: any;
    }
  }

  interface IUserInfo {
    userName: string;
    authorities: string[];
    mobile: string;
    roleNames: string[];
    userDisplayName: string;
    orgId: string;
  }

  interface IJobData {
    clusterId: string;
    content: any;
    createTime: number;
    createdBy: string;
    dataLevelType: string;
    id: string;
    jobName: string;
    jobStatus: string;
    jobType: string;
    jobVersion: string;
    memo: string;
    orgId: string;
    orgPath: string;
    projectId: string;
    projectName: string;
    updateTime: number;
    updatedBy: string;
    properties?: string;
  }

  interface IState {
    others: any;
    /** 用户信息 */
    userInfo: IUserInfo;
    /** 页面loading效果 */
    spinShow: boolean;
    /** 流程详情 */
    job: {
      data: IJobData;
      originJobContent: string;
      errorNodes: any[];
    };
    /** 流程DAG信息，用来比较DAG是否更改 */
    originJobContent: string;
    /** 左侧组件栏 */
    componentList: any;
    /** 可用的字段 */
    assetFields: any;
    /** 可用的方法 */
    assetFuncs: any;
    /** 可用的资源 */
    resTypes: any;
    /* 左侧菜单栏是否展开 */
    isCollapse: boolean;
    /* 流程设计列表批量操作后是否需要重新加载右侧画布 */
    reloadJob: boolean;
    // tab对应的路由
    tabTitles: any;
    // 打开的路由数组
    app: { fullPathList: any; environment: string; loginUrl: string; token: string };
    // 左侧菜单展开时候resize echarts图表
    isResize: boolean;
    enableSql: boolean; // 根据配置文件中的内容判断是否开放SQL流程能力
    isFuseMode: boolean; // 融合模式
  }

  interface IPaginationConfig {
    layout?: string;
  }

  interface ITableConfig {
    type?: string; // 对应列的类型。如果设置了 selection 则显示多选框；如果设置了 index 则显示该行的索引（从 1 开始计算）；如果设置了 expand 则显示为一个可展开的按钮，可不传
    expandProps?: string[];
    width: number;
    columnsExtend?: {
      edit?: Array<{
        iconfont: string;
        tipMessage: string;
        hasAuthority?: boolean | Function;
        handler: Function;
      }>;
    };
  }

  interface ITableData {
    columnData: IColumn[];
    tableData: any[];
    pageData?: IPageData;
  }

  interface IColumn {
    prop: string;
    label: string;
    sortable?: boolean; // 是否需要排序，默认为false，可不传， 不传的时候则不排序
    dataType?: string; // 数据的类型，如果是Date类型，前端将后端返回的时间戳转为‘YYYY-MM-DD HH:mm:ss’的形式，可不传
    width?: number; // 列的宽度，可不传，不传时宽度则为动态的
    show?: boolean; // 是否显示列
  }

  interface IPageData {
    pageSize: number;
    currentPage: number;
    total: number;
  }

  interface ISearchObj {
    search: any;
    startTime?: string;
    endTime?: string;
    pageData: IPageData;
    sortData?: any;
  }

  interface IFormArray {
    width?: number;
    rowData?: Array<{
      label: string;
      key?: string;
      value: string;
    }>;
    textAreaData?: Array<{
      label: string;
      value: string;
    }>;
  }

  type IProTable = (Vue | Element) & {
    runRequest: () => void;
  };
}
