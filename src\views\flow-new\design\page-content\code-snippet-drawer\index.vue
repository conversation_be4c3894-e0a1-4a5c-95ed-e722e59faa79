<template>
  <div>
    <el-drawer
      class="code-snippet-library"
      :class="{ 'outer-full-screen': fullScreen }"
      :modal="false"
      :size="400"
      :modal-append-to-body="false"
      append-to-body
      :visible.sync="snippetLibraryVisible"
      :before-close="handleDrawerClose"
      :wrapper-closable="false"
    >
      <!-- 头部信息 -->
      <div slot="title" class="code-snippet-library__header">
        <span class="code-snippet-library__title">{{ $t('pa.flow.codeLibrary') }}</span>
        <el-tooltip
          class="code-snippet-library__tooltip"
          effect="light"
          :popper-class="isEn ? 'code-snippet-library__tooltip--en' : ''"
        >
          <div slot="content">
            <pre class="code-snippet-library__pre">{{ snippetTip }}</pre>
          </div>
          <i class="iconfont icon-wenhao"></i>
        </el-tooltip>
      </div>
      <!-- 内容区域 -->
      <div class="code-snippet-library__content">
        <el-tabs v-model="activeName" :class="{ 'code-snippet-library__content--us': isEn }">
          <el-tab-pane v-for="item in tabPaneList" :key="item.name" :label="item.label" :name="item.name" />
        </el-tabs>
        <code-snippet-library
          ref="library"
          :full-screen="fullScreen"
          :public="activeName === 'public'"
          @add="showAddEditSnippet"
          @handle-detail="handleDrawerVisible"
        />
      </div>
    </el-drawer>
    <!-- 新建/编辑代码库抽屉 -->
    <add-edit-snippet
      v-if="addSnippetDrawerVisible"
      :data="snippetDetail"
      :public="activeName"
      :full-screen="fullScreen"
      :show.sync="addSnippetDrawerVisible"
      @refresh="getSqlSnippetList"
    />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, PropSync, Watch } from 'vue-property-decorator';
import codeSnippetLibrary from './code-snippet-library.vue';
import addEditSnippet from './add-edit-snippet.vue';
import isEqual from 'lodash/isEqual';
@Component({
  components: { codeSnippetLibrary, addEditSnippet }
})
export default class SnippetLibrary extends Vue {
  @PropSync('show', { default: false }) snippetLibraryVisible!: boolean;
  @Prop({ type: Boolean, default: false }) fullScreen!: boolean;
  activeName = 'public';
  addSnippetDrawerVisible = false;
  snippetDetail = null;
  get tabPaneList() {
    return [
      {
        label: this.$t('pa.flow.publicCodeLibrary'),
        name: 'public'
      },
      {
        label: this.$t('pa.flow.personCodeLibrary'),
        name: 'private'
      }
    ];
  }

  // 代码片段库提示文案
  get snippetTip() {
    return `${this.$t('pa.flow.msg78')}
${this.$t('pa.flow.publicCodeLibrary')}：${this.$t('pa.flow.msg79')}
                            ${this.$t('pa.flow.msg80')}
${this.$t('pa.flow.personCodeLibrary')}：${this.$t('pa.flow.msg81')}`;
  }

  @Watch('$route')
  handleRouteChange() {
    this.snippetLibraryVisible = false;
  }

  // 抽屉关闭回调
  handleDrawerClose() {
    this.snippetLibraryVisible = false;
  }

  showAddEditSnippet() {
    this.snippetDetail = null;
    !this.addSnippetDrawerVisible && (this.addSnippetDrawerVisible = true);
  }

  getSqlSnippetList() {
    (this.$refs.library as any).getSqlSnippetList();
    this.$emit('update');
  }

  // 若新建/编辑/查看抽屉已被打开，再次点击相同的弹窗则抽屉关闭
  handleDrawerVisible(item) {
    if (isEqual(this.snippetDetail, item)) return (this.addSnippetDrawerVisible = !this.addSnippetDrawerVisible);
    this.addSnippetDrawerVisible = true;
    this.snippetDetail = item;
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
  overflow: hidden;
}
::v-deep .el-drawer__header {
  border-bottom: 1px solid #f1f1f1;
  padding-bottom: 20px;
  margin-bottom: 0;
  height: 60px;
  box-sizing: border-box;
}
::v-deep .el-tabs {
  &__nav {
    transform: translateX(47px) !important;
  }
}

.code-snippet-library {
  top: 95px;
  bottom: 11px;
  width: 435px;
  left: calc(100% - 505px);
  z-index: 1999 !important;
  &__header {
    display: flex;
    align-items: center;
  }
  &__title {
    color: #444;
  }
  &__pre {
    width: 520px;
    margin: 0;
    height: 100%;
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial,
      sans-serif;
  }
  &__content {
    ::v-deep .el-tabs__nav-wrap {
      padding: 0px;
    }
    ::v-deep .el-tabs__item {
      padding: 0 5px;
    }
    ::v-deep .el-tabs__nav {
      transform: translateX(10px) !important;
    }
  }
  &__tooltip {
    margin-left: 10px;
    cursor: pointer;
  }
}
.outer-full-screen {
  top: 0;
  left: calc(100% - 485px);
}
</style>

<style lang="scss">
.code-snippet-library__tooltip--en {
  max-width: none !important;
}
</style>
