<template>
  <el-form
    ref="formRef"
    :model="formData"
    :disabled="disabled"
    :label-width="labelWidth"
    class="resource__container"
  >
    <!-- 流程信息 -->
    <flow-info :data="formData" />
    <!-- 基础参数 -->
    <base-config
      ref="baseRef"
      :org-id="orgId"
      :is-cloud="isCloud"
      :parallelism-disabled="parallelismDisabled"
      :data="formData"
      :cluster="cluster"
      :is-batch="isBatch"
    />
    <!-- 高级参数 -->
    <advanced-config :property="formData.property" :is-cloud="isCloud" :data="formData" />
    <!-- 自定义参数 -->
    <custom-config
      ref="customRef"
      :property="formData.property"
      :is-cloud="isCloud"
      :data="formData"
    />
    <!-- 作业模式 -->
    <mode-config :is-flink-sql="isFlinkSql" :data="formData" />
  </el-form>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import { get, put } from '@/apis/utils/net';
import cloneDeep from 'lodash/cloneDeep';
import { URL_JOB_FINDBYID, URL_JOB_RESOURCECONFIG } from '@/apis/commonApi';
import { flowDefaultConfig } from '@/utils';
import ElForm from 'bs-ui-pro/packages/form';
import { NO_CHECK_POINT, MAPPING } from './utils';
@Component({
  components: {
    FlowInfo: () => import('./info.vue'),
    BaseConfig: () => import('./base.vue'),
    AdvancedConfig: () => import('./advanced.vue'),
    CustomConfig: () => import('./custom.vue'),
    ModeConfig: () => import('./mode.vue')
  }
})
export default class ResourceConfig extends Vue {
  @PropSync('title', { default: '资源配置' }) contentTitle!: string;
  @Prop({ default: '' }) flowId!: string;
  @Prop({ default: '' }) projectId!: string;
  @Prop({ default: false }) isBatch!: boolean; //批量资源配置
  @PropSync('data', { type: Object, default: () => flowDefaultConfig() }) form!: any; // 批量资源配置由父组件传值得到
  @Prop({ default: () => [] }) cluster!: any[]; // 批量模式从外部传入 集群列表
  @Ref('formRef') readonly formRef!: ElForm;
  @Ref('baseRef') baseRef: any;
  @Ref('customRef') custom: any;

  private labelWidth = '180px';
  private flowData: any = {};
  private orgId = '';
  private isFlinkSql = false;
  private disabled = false;
  private parallelismDisabled = false; // 非开发状态不可修改并行度
  private formData: any = { ...flowDefaultConfig() };
  jobTypeMap: any = {
    PROCESSFLOW: 'DataStream',
    FLINK_SQL: 'SQL'
  }; // 流程类型

  get isCloud() {
    return this.formData?.clusterType === 'CLOUD';
  }

  created() {
    if (this.isBatch) {
      this.formData = cloneDeep(this.form);
      this.orgId = this.form.orgId;
      this.parallelismDisabled = this.form.jobStatus !== 'DEV';
      this.isFlinkSql = this.formData?.jobType === 'FLINK_SQL';
      this.formData.jobType = this.jobTypeMap[this.formData.jobType];
    } else {
      this.getFlowDetail();
    }
  }

  async getFlowDetail() {
    const loading = this.$loading({ lock: true });
    try {
      const { success, data, msg } = await get(URL_JOB_FINDBYID, { id: this.flowId });
      if (success) {
        this.orgId = data?.orgId;
        this.isFlinkSql = data?.jobType === 'FLINK_SQL';
        this.contentTitle = `流程配置（${data.jobName}）`;
        this.disabled = ['INPROD', 'PROD'].includes(data?.jobStatus);
        this.parallelismDisabled = data?.jobStatus !== 'DEV';
        this.formData = {
          ...this.formData,
          ...JSON.parse(data?.properties || '{}'),
          jobName: data.jobName,
          jobType: this.jobTypeMap[data.jobType],
          memo: data.memo
        };
        return loading.close();
      }
      this.$tip.error(msg);
      loading.close();
    } catch {
      loading.close();
    }
  }
  async handleSubmit() {
    if (!this.custom.save()) return;
    await this.formRef.validate();
    await this.saveProperties(this.flowId, this.projectId, this.getProperties());
  }
  async saveProperties(id: string, projectId: string, properties: any) {
    const loading = this.$loading({ lock: true });
    try {
      const { success, msg, error } = await put(URL_JOB_RESOURCECONFIG, {
        id,
        projectId,
        ...properties
      });
      loading.close();
      if (success) {
        this.$tip.success(msg);
        return;
      }
      this.$tip.error(error);
    } catch {
      loading.close();
    }
  }

  getProperties() {
    const data: any = cloneDeep(this.formData);
    if (!data.enableCheckPoint) NO_CHECK_POINT.forEach((el) => delete data[el]);
    data.configs = this.custom.tableData;
    const list = MAPPING[data.restartStrategy] || MAPPING.all;
    list.push(this.isCloud ? 'queue' : 'namespace');
    list.forEach((key) => delete data[key]);
    const jobInfo = { jobName: this.formData.jobName, memo: this.formData.memo };
    delete data.property;
    delete data.jobName;
    delete data.jobType;
    delete data.memo;
    return { ...jobInfo, properties: JSON.stringify(data) };
  }

  submit() {
    if (this.custom.save()) {
      return new Promise((resolve, reject) => {
        this.formRef.validate((valid, err) => {
          if (!valid) {
            reject(err);
          }
          resolve(this.handleFormData());
        });
      });
    }
  }

  /* 是否开启checkpoint属性：反选后删除checkpoint相关属性 */
  handleEnableCheckPointData() {
    const data: any = cloneDeep(this.formData);
    if (!data.enableCheckPoint) {
      const enableCheckPointProperty = [
        'checkpointInterval',
        'cp_timeout',
        'cp_min_pause',
        'cp_failed',
        'cp_unaligned',
        'stateBackend'
      ];
      enableCheckPointProperty.forEach((el) => {
        delete data[el];
      });
    }
    data.configs = this.custom.tableData;
    return data;
  }

  handleFormData() {
    const temp: any = this.handleEnableCheckPointData();
    const mapping = {
      'failure-rate': [
        'attempts',
        'delay',
        'initialBackoff',
        'maxBackoff',
        'backoffMultiplier',
        'resetBackoffThreshold',
        'jitterFactor'
      ],
      'fixed-delay': [
        'failureRateDelay',
        'failuresPerInterval',
        'failureRateInterval',
        'initialBackoff',
        'maxBackoff',
        'backoffMultiplier',
        'resetBackoffThreshold',
        'jitterFactor'
      ],
      'exponential-delay': [
        'failureRateDelay',
        'attempts',
        'delay',
        'failuresPerInterval',
        'failureRateInterval'
      ],
      all: [
        'attempts',
        'delay',
        'failuresPerInterval',
        'failuresPerInterval',
        'failureRateDelay',
        'initialBackoff',
        'maxBackoff',
        'backoffMultiplier',
        'resetBackoffThreshold',
        'jitterFactor'
      ]
    };
    const list = mapping[temp.restartStrategy] || mapping.all;
    list.push(this.isCloud ? 'queue' : 'namespace');
    list.forEach((key) => delete temp[key]);
    temp.jobType = this.form.jobType;
    return temp;
  }
}
</script>
<style lang="scss" scoped>
.resource {
  &__container {
    ::v-deep .el-collapse {
      border-top: unset;
    }

    ::v-deep .resource-title {
      margin-left: 16px;
      padding-left: 6px;
      width: 100%;
      font-size: 14px;
      font-weight: 500;
      color: #444444;
      line-height: 20px;
      &::before {
        content: '';
        position: relative;
        left: 0;
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 8px;
        background: #ff9e2b;
        border-radius: 2px;
      }
    }
    ::v-deep .el-form-item {
      &__content {
        display: flex;
        align-items: center;

        .resource-item {
          display: inline-block;
          width: calc(100% - 30px);
          .el-select,
          .el-input-number,
          .el-date-editor {
            width: 100%;
          }
          .el-form-item {
            display: inline-block;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>
