<!-- >保存提示弹窗<-->
<template>
  <bs-dialog :title="title" :visible.sync="visible" :before-close="closeDialog" size="medium">
    <div class="code-content">
      <!-- <codemirror
        v-loading="loading"
        ref="myCmCase"
        class="code-mirror"
        :code="Data"
        :options="options"
        :originCode="Data"
      ></codemirror> -->
      <flink-sql-editor
        :code="Data"
        :origin-code="Data"
        :height="editorHeight"
        :options="editorOptions"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="closeDialog">确定</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import FlinkSqlEditor from '@/components/flink-sql-editor/index.vue';
@Component({
  components: {
    FlinkSqlEditor
  }
})
export default class SourceCode extends Vue {
  formLabelWidth = '50px';
  visible = false;
  @Prop({ default: {} }) Data!: any;
  @Prop({ default: '' }) title;
  loading = false;
  formData: any = {};
  editorHeight = '300px'; // 编辑器高度
  editorOptions = {
    mode: 'text/flink-sql',
    indentUnit: 2,
    smartIndent: true,
    lineNumbers: true,
    showCursorWhenSelecting: true,
    lineWiseCopyCut: true,
    autofocus: true,
    lineWrapping: true,
    foldGutter: true,
    readOnly: true
  }; // 编辑器配置
  closeDialog() {
    this.visible = false;
  }

  getCode() {
    // this.Data = sqlFormatter.format(this.Data);
  }
}
</script>
<style lang="scss" scoped>
.code-content {
  margin-top: -1px;
  margin-right: -1px;
}
</style>
