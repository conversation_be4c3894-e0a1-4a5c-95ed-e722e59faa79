<template>
  <pro-table
    ref="proTable"
    :columns="columnData"
    :request="request"
    :options="{ height: '100vh - 169px' }"
  />
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { getRelations } from '@/apis/cep-api';
import { dateFormat } from '@/utils/format';

@Component
export default class Relation extends Vue {
  @Prop({ type: String }) id!: string;
  columnData: any = [];

  async request({ page }: { page: { currentPage: number; pageSize: number } }) {
    const params = {
      pageData: { ...page },
      search: this.id
    };
    const {
      data: {
        columnData,
        tableData,
        pageData: { total }
      }
    } = await getRelations(params);
    this.columnData = columnData;
    tableData.forEach((item) => {
      item.createTime = dateFormat(item.createTime);
    });
    return { data: tableData, total: total };
  }
}
</script>

<style scoped lang="scss">
.roll-back {
  overflow: scroll;
  background: #fff;
  &__icon {
    cursor: pointer;
    margin: 0 5px;
  }
}
</style>
