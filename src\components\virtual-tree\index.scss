.virtual-tree {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  &-complete-height {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    z-index: -1;
  }
  &-visible {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }
  &-item {
    display: flex;
    align-items: center;
    height: 40px;
    border-radius: 6px;
    padding: 0 15px;
    &--active {
      background-color: #f6f6f6;
      margin: 0 12px !important;
    }
    &-icon-box {
      display: flex;
      align-items: center;
      width: 24px;
      height: 24px;
      padding: 6px;
    }
    &-icon {
      cursor: pointer;
      color: #c0c4cc;
      font-size: 12px;
      transform: rotateZ(0);
      transition: transform 0.3s ease-in-out;
    }
    &-icon.expanded {
      transform: rotateZ(90deg);
    }
    ::v-deep .el-checkbox {
      margin-right: 8px;
    }
    &-label {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
    }
    &-label.active {
      color: #2196f3;
    }
  }
  &-no-data {
    text-align: center;
    padding: 20px;
    color: #909399;
  }
}
.virtual-tree::-webkit-scrollbar {
  width: 6px;
}
