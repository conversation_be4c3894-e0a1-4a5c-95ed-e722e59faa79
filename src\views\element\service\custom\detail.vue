<template>
  <pro-page :title="`${title}`" :loading="pageLoading">
    <div slot="operation">
      <el-button v-if="showRefreshSource" type="primary" @click="handleRefreshSource">
        刷新资源使用情况
      </el-button>
    </div>
    <div v-if="hasAuth === 1">
      <div class="first_content" style="margin-top: 20px">
        <el-backtop target=".first_content" />
        <base-info-content
          ref="baseInfoContent"
          :show-share="
            detailConf.showShare && btnHasAuthority(currentResType.listConf.shareAuthCode)
          "
          :show-term="detailConf.showTerm && btnHasAuthority(currentResType.listConf.termAuthCode)"
          :show-scp="detailConf.showScp && btnHasAuthority(currentResType.listConf.scpAuthCode)"
          :form-array="formArray"
          :detail-record="detailRecord"
        />
        <!--其他部分组件-->
        <div v-for="(item, index) in detailComponents" :key="index" class="detail_content">
          <component
            :is="item.component"
            :key="`${commonKey}_${item.name}`"
            :ref="item.name"
            :kafka-data="detailRecord"
            :cluster-type="$route.query.clusterType"
          />
        </div>
        <!--占位用-->
      </div>
    </div>
    <div v-if="hasAuth === 0">
      <h1>您没有权限查看该页面</h1>
    </div>
  </pro-page>
</template>
<script lang="ts">
import { Component, Provide, Vue } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import * as _ from 'lodash';
import {
  URL_HOST_FIND,
  URL_RES_FINDBYID,
  URL_RESCONF_GETFORMCONF,
  URL_RESCONF_GETRESTYPELIST,
  URL_REFRESH_CLUSTER_MSG
} from '@/apis/commonApi';
import { get } from '@/apis/utils/net';
import baseInfoContent from '../common/base-info-content.vue';

@Component({
  name: 'ServiceCustom',
  components: {
    baseInfoContent
  }
})
export default class ServiceCustom extends PaBase {
  hasAuth = 1;
  pageLoading = true;
  detailRecord: any = {};
  detailConf: any = {};
  currentResType: any = {};
  formArray: any = {};
  detailComponents: any = [];
  params: any = {};
  detailRecordProvide: any = {};
  ListConfProvide: any = {};
  title: any = '服务详情';
  @Provide('comParams') comParams = this.params;
  @Provide('comDetailRecord') comDetailRecord = this.detailRecordProvide;
  @Provide('comListConf') comListConf = this.ListConfProvide;
  commonKey: number = Date.now();

  get showRefreshSource() {
    return this.$route.params.type === 'FLINK';
  }
  /* 刷新资源使用情况后数据刷新 */
  async handleRefreshSource() {
    const { id: clusterId } = this.$route.query;
    const res: any = await get(URL_REFRESH_CLUSTER_MSG, { clusterId });
    this.$message.success(res.msg);
    this.commonKey = Date.now();
  }

  created() {
    this.title = this.$route.query.title;
    this.pageLoading = true;
  }

  btnHasAuthority(code: string) {
    return this.hasFeatureAuthority(code, this.detailRecord.dataLevelType);
  }

  toLower(type: string) {
    return _.toLower(type);
  }

  isHost() {
    return this.$route.query.resType === 'HOST';
  }

  makeDetailUrl() {
    return this.isHost() ? URL_HOST_FIND : URL_RES_FINDBYID;
  }

  getDetailConf(file: string) {
    this.doGet(URL_RESCONF_GETFORMCONF, {
      params: {
        file
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.detailConf = JSON.parse(resp.data);
        this.formArray = this.detailConf.forms;

        // 注册组件
        if (this.detailConf.components) {
          this.detailConf.components.forEach((n) => {
            n.children.forEach(async (m) => {
              const res = await import(`../${m.file}`);
              m.component = res.default;
              this.detailComponents.push(m);
              this.params[m.name] = m.params;
            });
          });
        }
      });
    });
  }

  executeLoadData(name, params) {
    this.getComponent(name).loadData(this.detailRecord, params, this.currentResType.listConf);
  }

  mounted() {
    this.detailComponents = [];
    Vue.axios.get(URL_RESCONF_GETRESTYPELIST).then((resp: any) => {
      if (resp.success) {
        this.resEnums = resp.data;
        this.currentResType = this.getResType(this.$route.query.resType as string);
        this.ListConfProvide.val = this.currentResType.listConf;
        const type = this.$route.query.clusterType as string;
        if (type === 'YARN_PER_JOB') {
          this.currentResType.detailConf.file = this.currentResType.detailConf.file.replace(
            /detail.json/,
            'detail-per-job.json'
          );
        } else if (type === 'YARN_SESSION') {
          this.currentResType.detailConf.file = this.currentResType.detailConf.file.replace(
            /detail.json/,
            'detail-yarn-session.json'
          );
        } else if (type === 'CLOUD') {
          this.currentResType.detailConf.file = this.currentResType.detailConf.file.replace(
            /detail.json/,
            'detail-cloud.json'
          );
        } else if (type === 'YARN_APPLICATION') {
          this.currentResType.detailConf.file = this.currentResType.detailConf.file.replace(
            /detail.json/,
            'detail-yarn-application.json'
          );
        }
        if (this.currentResType) {
          this.pageLoading = true;
          if (!this.$route.query.id) {
            return;
          }
          this.doGet(this.makeDetailUrl(), {
            params: {
              id: this.$route.query.id
            }
          }).then((myResp: any) => {
            if (!myResp.success) {
              this.pageLoading = false;
              return false;
            }
            this.parseResponse(myResp, () => {
              this.detailRecord = _.cloneDeep(myResp.data);
              this.detailRecordProvide.val = this.detailRecord;
              // this.hasAuth = this.hasFeatureAuthority(
              //   this.currentResType.listConf.detailAuthCode,
              //   this.detailRecord.dataLevelType
              // )
              //   ? 1
              //   : 0;
              // 都能查看该页面
              this.hasAuth = 1;
              this.pageLoading = false;
              if (this.hasAuth === 1) {
                // 获取详情的配置
                this.getDetailConf(this.currentResType.detailConf.file);
              }
            });
          });
        }
      } else {
        this.pageLoading = false;
      }
    });
  }
}
</script>
<style scoped lang="scss">
.first_content {
  .detail_content {
    min-height: 0px;
    margin-bottom: 20px;
    .noData {
      height: 60px;
      line-height: 60px;
      color: #aaa;
      text-align: center;
    }
  }
}
.first_content > div:last-child {
  margin-bottom: 0px;
}
</style>
