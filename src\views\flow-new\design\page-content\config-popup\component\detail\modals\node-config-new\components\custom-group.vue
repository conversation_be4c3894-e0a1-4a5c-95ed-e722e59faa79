<template>
  <div class="custom-field">
    <div class="custom-field__btn">
      <el-button v-if="!disabled" style="margin-bottom: 16px" @click="addRow">{{ $t('pa.flow.createRow') }}</el-button>
    </div>
    <pro-edit-table
      ref="editTable"
      class="custom-field__table"
      :data="tableData"
      :columns="columns"
      :edit-props="editProps"
      :show-operate-column="!disabled"
      @change="handleChange"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { Group } from '../interface';
import { runRequest } from '../utils';
interface Data {
  name: string;
  value: string;
}
@Component
export default class CustomFieldList extends Vue {
  @Prop() config!: Group;
  @Prop({ default: false }) disabled!: boolean;
  @Prop() value!: Data[];
  tableData: Data[] = [];
  columns = [
    {
      label: this.$t('pa.flow.key'),
      value: 'name',
      formProps: {
        type: 'input',
        rules: [{ pattern: /^[A-Za-z0-9-_!@#￥%&*().]+$/, message: this.$t('pa.flow.msg82') }]
      }
    },
    {
      label: this.$t('pa.flow.val'),
      value: 'value',
      formProps: {
        type: 'input'
      }
    }
  ];
  editProps = {
    multiple: true,
    operateColumnWidth: 120,
    defaultActions: ['edit', 'del']
  };
  // 添加一行
  addRow() {
    (this.$refs.editTable as any).action.create();
  }
  // 数据变更
  handleChange(val) {
    const data = (val || [])
      .map((item) => {
        const { name, value } = item;
        return { name, value };
      })
      .filter((item) => item.name !== '' && item.value !== '');
    this.$emit('change', data);
  }
  async created() {
    const { data, request } = this.config;
    // 已经保存过自定义参数 直接赋值给tableData
    if (Array.isArray(this.value) && this.value.length) {
      return (this.tableData = [...this.value]);
    }
    if (request) {
      const res = await runRequest(request, {});
      this.tableData = [...res.data];
    } else if (Array.isArray(data)) {
      this.tableData = [...data];
    }
  }
}
</script>
<style lang="scss" scoped>
.custom-field {
  &__btn {
    text-align: right;
  }
  &__table {
    border: 1px solid #ebeef5;
    border-bottom: none;
    ::v-deep .cell > div > .el-button {
      margin-right: 10px;
    }
    ::v-deep .cell .el-button--text + .el-button--text {
      margin-left: 0;
    }
  }
}
</style>
