{"forms": [{"type": "input", "prop": "title", "label": "名称", "componentProps": {"maxlength": 30, "placeholder": "请输入名称", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入名称", "trigger": "blur"}, {"min": 2, "max": 30, "message": "长度在 2 到 30 个字符", "trigger": "blur"}]}, {"type": "textarea", "prop": "url", "label": "服务地址", "componentProps": {"rows": 3, "maxlength": 3000, "placeholder": "请输入服务地址，多个地址以逗号分隔，兼容Ipv4和Ipv6"}, "rules": [{"required": true, "message": "请输入服务地址，多个地址以逗号分隔，兼容Ipv4和Ipv6", "trigger": "blur"}, {"min": 1, "max": 3000, "message": "长度不能超过3000个字符", "trigger": "blur"}, {"validator": "validateResUrl", "trigger": "blur"}]}, {"type": "textarea", "prop": "zkUrl", "label": "zookeeper", "componentProps": {"rows": 3, "maxlength": 3000, "placeholder": "请输入zookeeper的IP和端口，多个地址以逗号分隔，兼容Ipv4和Ipv6"}, "rules": [{"min": 0, "max": 3000, "message": "长度不能超过3000个字符", "trigger": "blur"}, {"validator": "validateResUrl", "trigger": "blur"}]}, {"type": "radio-group", "prop": "authN<PERSON>ed", "label": "是否鉴权", "componentProps": {"options": [{"label": "是", "value": "yes"}, {"label": "否", "value": "no"}], "placeholder": "请选择"}, "rules": [{"message": "请选择是否鉴权", "required": true, "trigger": "blur"}], "defaultVal": "no"}, {"type": "select", "prop": "securityProtocol", "label": "安全协议", "deps": ["authN<PERSON>ed"], "visible": "(scope) => scope.authNeeded === 'yes'", "componentProps": {"options": [{"label": "SASL_PLAINTEXT", "value": "SASL_PLAINTEXT"}, {"label": "SASL_TBDS", "value": "SASL_TBDS"}], "placeholder": "请选择"}, "rules": [{"message": "请选择security.protocol", "required": true, "trigger": "blur"}]}, {"type": "select", "prop": "saslMechanism", "label": "认证机制", "deps": ["authN<PERSON>ed"], "visible": "(scope) => scope.authNeeded === 'yes'", "componentProps": {"options": [{"label": "SCRAM-SHA-256", "value": "SCRAM-SHA-256"}, {"label": "GSSAPI", "value": "GSSAPI"}, {"label": "TBDS", "value": "TBDS"}, {"label": "PLAIN", "value": "PLAIN"}], "placeholder": "请选择"}, "rules": [{"message": "请选择sasl.mechanism", "required": true, "trigger": "blur"}]}, {"type": "input", "prop": "kerberosServiceName", "label": "service名称", "deps": ["saslMechanism", "authN<PERSON>ed"], "visible": "(scope) => scope.saslMechanism === 'GSSAPI' && scope.authNeeded === 'yes'", "componentProps": {"maxlength": 64, "placeholder": "请输入kerberosService名称", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入kerberosService名称", "trigger": "blur"}]}, {"type": "input", "prop": "principalName", "label": "principal名称", "deps": ["saslMechanism", "authN<PERSON>ed"], "visible": "(scope) => scope.saslMechanism === 'GSSAPI' && scope.authNeeded === 'yes'", "componentProps": {"maxlength": 64, "placeholder": "请输入kerberos principal", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入kerberos principal", "trigger": "blur"}]}, {"type": "input", "prop": "krb5ConfPath", "label": "krb5.conf地址", "deps": ["saslMechanism", "authN<PERSON>ed"], "visible": "(scope) => scope.saslMechanism === 'GSSAPI' && scope.authNeeded === 'yes'", "componentProps": {"maxlength": 256, "placeholder": "请输入krb5.conf地址", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入krb5.conf地址", "trigger": "blur"}]}, {"type": "input", "prop": "keytab<PERSON><PERSON>", "label": "keytab地址", "deps": ["saslMechanism", "authN<PERSON>ed"], "visible": "(scope) => scope.saslMechanism === 'GSSAPI' && scope.authNeeded === 'yes'", "componentProps": {"maxlength": 256, "placeholder": "请输入keytab地址", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入keytab地址", "trigger": "blur"}]}, {"type": "input", "prop": "username", "label": "用户名", "deps": ["saslMechanism", "authN<PERSON>ed"], "visible": "(scope) => ['SCRAM-SHA-256', 'PLAIN'].includes(scope.saslMechanism) && scope.authNeeded === 'yes'", "componentProps": {"maxlength": 30, "placeholder": "请输入用户名", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入用户名", "trigger": "blur"}]}, {"type": "password", "prop": "password", "label": "密码", "deps": ["saslMechanism", "authN<PERSON>ed"], "visible": "(scope) =>  ['SCRAM-SHA-256', 'PLAIN'].includes(scope.saslMechanism) && scope.authNeeded === 'yes'", "componentProps": {"maxlength": 100, "placeholder": "请输入密码"}, "rules": [{"required": true, "message": "请输入密码", "trigger": "blur"}]}, {"type": "input", "prop": "secretId", "label": "secretId", "deps": ["saslMechanism", "authN<PERSON>ed"], "visible": "(scope) => scope.saslMechanism === 'TBDS' && scope.authNeeded === 'yes'", "componentProps": {"maxlength": 30, "placeholder": "请输入secretId", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入secretId", "trigger": "blur"}]}, {"type": "input", "prop": "secret<PERSON>ey", "label": "secret<PERSON>ey", "deps": ["saslMechanism", "authN<PERSON>ed"], "visible": "(scope) => scope.saslMechanism === 'TBDS' && scope.authNeeded === 'yes'", "componentProps": {"maxlength": 30, "placeholder": "请输入secretKey", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入secretKey", "trigger": "blur"}]}, {"type": "textarea", "prop": "memo", "label": "备注", "componentProps": {"rows": 5, "maxlength": 200, "placeholder": "请输入备注"}}]}