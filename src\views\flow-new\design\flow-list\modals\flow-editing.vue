<template>
  <el-form
    ref="ruleForm"
    class="flow-form"
    :model="formData"
    :rules="rules"
    :label-width="isEn ? '125px' : '80px'"
    @submit.native.prevent
  >
    <el-form-item class="flow-form__name" :label="$t('pa.flow.name')" prop="originalJobName">
      <!-- 流程名称前缀 -->
      <bs-select
        v-model="formData.prefix"
        class="flow-form__name--prefix"
        :placeholder="$t('pa.flow.placeholder45')"
        clearable
        :options="prefixList"
      />
      <!-- 流程名称 -->
      <el-input
        v-model="formData.originalJobName"
        autocomplete="off"
        maxlength="30"
        show-word-limit
        clearable
        :placeholder="$t('pa.flow.placeholder8')"
      />
      <!-- 流程名称后缀 -->
      <bs-select
        v-model="formData.suffix"
        class="flow-form__name--suffix"
        :class="{ 'flow-form__name--suffix-us': isEn }"
        :placeholder="$t('pa.flow.placeholder46')"
        clearable
        :options="suffixList"
      />
    </el-form-item>
    <el-form-item :label="$t('pa.flow.category')" prop="jobType">
      <el-select
        v-model="formData.jobType"
        filterable
        clearable
        :disabled="jobTypeList.length === 1 || !!this.id"
        :placeholder="$t('pa.flow.placeholder9')"
        :popper-append-to-body="false"
      >
        <el-option v-for="el in jobTypeList" :key="el.value" :label="el.label" :value="el.value" />
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('pa.flow.fDir')" prop="projectId">
      <bs-cascader
        v-model="formData.projectId"
        :options="dirOptions"
        :props="{ checkStrictly: true, emitPath: false }"
        :disabled="!!this.id"
        filterable
        popper-class="dir-cascader"
        style="width: 100%"
      >
        <div slot-scope="{ node, data }" class="dir-node">
          <span :title="data.label">{{ data.label }}</span>
          <bs-tag v-if="node.level === 1" style="margin-left: 4px">{{ $t('pa.flow.rDir') }}</bs-tag>
        </div>
      </bs-cascader>
    </el-form-item>
    <el-form-item :label="$t('pa.notes')" prop="memo">
      <el-input
        v-model="formData.memo"
        type="textarea"
        clearable
        rows="5"
        :placeholder="$t('pa.flow.placeholder10')"
        autocomplete="off"
        maxlength="255"
        show-word-limit
      />
    </el-form-item>
  </el-form>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { addFlow, editFlow, getFlowById, getProjectTree } from '@/apis/flowNewApi';
import { getItemListByType } from '@/apis/dataApi';

@Component
export default class AddAndEditFlow extends Vue {
  @Prop() id!: string;
  @Prop() parentId!: string;
  @Prop() projectId!: string;
  formData: any = {
    originalJobName: '',
    jobType: 'PROCESSFLOW',
    memo: '',
    prefix: '',
    suffix: '',
    projectId: this.parentId || this.projectId
  };
  rules: any = {
    originalJobName: [{ required: true, message: this.$t('pa.flow.msg31'), trigger: 'blur' }],
    jobType: [{ required: true, message: this.$t('pa.flow.placeholder9'), trigger: 'change' }],
    projectId: [{ required: true, message: this.$t('pa.flow.placeholder44') }]
  };
  // 名称前缀列表
  prefixList = [];
  // 名称后缀列表
  suffixList = [];
  // 目录列表
  dirOptions = [];
  get enableSql() {
    return this.$store.getters.enableSql;
  }
  get enableJar() {
    return this.$store.getters.enableJar;
  }
  get jobTypeList() {
    return [{ label: 'DataStream', value: 'PROCESSFLOW' }]
      .concat(this.enableSql ? [{ label: 'SQL', value: 'FLINK_SQL' }] : [])
      .concat(this.enableJar ? [{ label: this.$t('pa.flow.customSize'), value: 'UDJ' }] : []);
  }
  async created() {
    this.getPrefixAndSuffixList(true);
    this.getPrefixAndSuffixList();
    this.getProjectTree();
    this.id && this.getFlowDetail();
  }

  // 获取前缀、后缀列表
  async getPrefixAndSuffixList(isPrefix = false) {
    const { data, success, error, msg } = await getItemListByType({ itemType: `JOB_${isPrefix ? 'PRE' : 'SUF'}` });
    if (!success) return this.$message.error(error || msg);
    this[isPrefix ? 'prefixList' : 'suffixList'] = data.map((el) => ({ label: el, value: el }));
  }
  // 获取项目下的目录树
  async getProjectTree() {
    const { data = [] } = await getProjectTree({ rootProjectId: this.projectId });
    const transform = (data) => {
      return data.map(({ nodeId, nodeName, children }) => ({
        value: nodeId,
        label: nodeName,
        children: Array.isArray(children) && children.length ? transform(children) : undefined
      }));
    };
    this.dirOptions = transform([data]);
  }
  // 获取流程详情
  async getFlowDetail() {
    const { data = {} } = await getFlowById({ id: this.id, isMonitor: false });
    const { originalJobName, jobType, memo, prefix, suffix } = data;
    Object.assign(this.formData, { originalJobName, jobType, memo, prefix, suffix });
  }
  // 配合弹窗的确认点击事件
  public confirm(done: (boolean) => void) {
    (this.$refs.ruleForm as any).validate(async (valid: any) => {
      if (!valid) return done(false);
      const req = this.id ? editFlow : addFlow;
      const { success, msg } = await req(Object.assign(this.formData, { id: this.id }));
      this.$message[success ? 'success' : 'error'](msg);
      done(success);
    });
  }
}
</script>
<style lang="scss" scoped>
.flow-form {
  .el-select {
    width: 100%;
    text-align: left;
  }
  &__name {
    ::v-deep .el-form-item__content {
      display: flex;
    }
    &--prefix {
      margin-right: 10px;
    }
    &--suffix {
      margin-left: 10px;
    }
    &--prefix,
    &--suffix {
      width: 250px;
      ::v-deep .el-input__inner {
        border-color: #e5e5e5 !important;
      }
    }
  }
}
.dir-node {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 200px;
  & > span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
