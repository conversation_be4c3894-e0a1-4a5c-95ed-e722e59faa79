import validators from './validator';
import { BaseRequest, BaseRequestProps } from './interface';
import { request } from '@bs/axios';
import { Message } from 'bs-ui-pro';
import i18n from '@/i18n';

/* 执行request，请求远程数据 */
export const runRequest = (config: string | BaseRequest, formData: Record<string, any>) => {
  const isString = typeof config === 'string';
  const handleParams = (params) => {
    return params.reduce((res, p) => {
      res[p] = formData[p];
      return res;
    }, {});
  };
  const hasParamsMissing = (params, data) => {
    return [...params, ...data].some((k) => {
      const v = formData[k];
      return v === undefined || v === '' || (Array.isArray(v) && v.length === 0);
    });
  };
  if (!isString) {
    const { url, method = 'get', params = [], data = [] } = config as BaseRequest;
    // 判断所有参数是否完整 否则不发起请求
    if (hasParamsMissing(params, data)) return {};
    return request(url, {
      method,
      params: handleParams(params),
      data: handleParams(data)
    });
  } else {
    return request(config as string, { method: 'get' });
  }
};

/* 处理request返回的数据 */
export const getRequestData = (data, props: BaseRequestProps = {}) => {
  const { list, value, label } = props;
  // TODO:处理saveParams
  list && (data = data[list]);
  if (!Array.isArray(data)) return [];
  if ((value || label) && Array.isArray(data)) {
    data.forEach((item) => {
      value && item[value] && (item.value = item[value]);
      label && item[label] && (item.label = item[label]);
    });
  }
  return data;
};

/* 获取输出字段 */
export const getOutputField = (name, type = 'String') => {
  return {
    name,
    type,
    outputable: true,
    targetable: true
  };
};

/* 获取默认提示文案 */
const getDefaultMessage = ({ type, label }) => {
  const isInput = (type) => ['input', 'textarea', 'input-number', 'password', 'tag-input', 'output'].includes(type);
  return (isInput(type) ? i18n.t('pa.placeholder.input') : i18n.t('pa.placeholder.select')) + label;
};
/* 获取前端组件的默认配置 */
const getDefaultComponentProps = (item) => {
  return {
    clearable: true,
    placeholder: getDefaultMessage(item)
  };
};

/* 设置组件配置参数 */
const setComponentProps = (item) => {
  item.componentProps = Object.assign(getDefaultComponentProps(item), item.componentProps || {});
  if (item.type === 'select') {
    item.componentProps.filterable && (item.componentProps.searchable = true);
    delete item.componentProps.filterable;
  }
};

/* 设置表单规则 */
const setFormRuels = (item, isDesign) => {
  if (item.required) {
    item.rules = [{ required: true, message: getDefaultMessage(item) }];
    delete item.required;
    return;
  }
  if (!Array.isArray(item.rules)) return;
  item.rules.forEach((rItem) => {
    if (rItem.pattern) {
      try {
        /* eslint-disable-next-line */
        rItem.pattern = eval(rItem.pattern);
      } catch (err) {
        const message = i18n.t('pa.patternError', [item.label || item.prop, err]) as string;
        isDesign ? Message.error({ message, duration: 0, showClose: true }) : console.log(message);
      }
    }
    // 内置方法处理
    if (rItem.validator && validators[rItem.validator]) {
      const validatorFn = validators[rItem.validator];
      rItem.validator = validatorFn;
    }
  });
};
/* 设置非远程请求的下拉数据 */
const setOptions = (item) => {
  if (item.options && Array.isArray(item.options)) {
    item.componentProps.options = item.options;
    delete item.options;
  }
};
/* 设置依赖字段 */
const setDeps = (item) => {
  const deps: string[] = [];
  if (Array.isArray(item.visibleDeps)) {
    deps.push(...item.visibleDeps);
    delete item.visibleDeps;
  }
  if (item.request) {
    ['params', 'data'].forEach((k) => {
      Array.isArray(item.request[k]) && deps.push(...item.request[k]);
    });
  }
  deps.length && (item.deps = [...new Set(deps)]);
};
const setVisible = (item, isDesign) => {
  try {
    /* eslint-disable-next-line */
    if (typeof eval(item.visible) === 'function') {
      /* eslint-disable-next-line */
      item.visible = eval(item.visible);
    }
  } catch (err) {
    const message = i18n.t('pa.visibleError', [item.label || item.prop, err]) as string;
    isDesign ? Message.error({ message, duration: 0, showClose: true }) : console.log(message);
  }
};
/* 设置接口请求相关 */
const setRequest = (item, extraData = {}) => {
  if (typeof item.request === 'function' || !item.request || item.type === 'custom') return;
  const config = { ...item.request };
  item.request = async ({ scope, page, query }) => {
    const { data = [] } = await runRequest(config, { ...scope, ...extraData });
    const _data = config.props ? getRequestData(data, config.props) : data;
    return { data: _data, total: _data.length };
  };
};

/* 基础转换 */
export const transform = (item, extraData?, isDesign?) => {
  setComponentProps(item);
  setFormRuels(item, isDesign);
  setOptions(item);
  setDeps(item);
  setVisible(item, isDesign);
  setRequest(item, extraData);
  return item;
};

/* type为serve转换 */
export const transformServeItem = (item) => {
  return [
    {
      label: i18n.t('pa.flow.serveName'),
      prop: 'resId',
      type: 'custom',
      group: item.group,
      required: true,
      request: item.request
    },
    {
      label: i18n.t('pa.flow.serveAddress'),
      prop: 'address',
      type: 'textarea',
      group: item.group,
      required: true,
      componentProps: {
        readonly: true
      }
    }
  ].map((item) => {
    return transform(item);
  });
};

/* type为table转换 */
export const transformTableItem = (item, extraData?, isDesign?) => {
  const data = transform(item, extraData, isDesign);
  return {
    label: data.label,
    required: true,
    group: data.group,
    gutter: 10,
    style: {
      marginBottom: 0
    },
    children: [
      Object.assign(data, { type: 'select', label: '', colSpan: 22 }),
      { type: 'custom', prop: data.fieldProp, colSpan: 2, request: data.fieldRequest }
    ]
  };
};

/* type为output转换 */
export const transformOutputItem = (item, extraData?, isDesign?) => {
  const data = transform(item, extraData, isDesign);
  const rules = data.rules || [];
  // 输出字段的只有必填校验或者不存在校验  增加字段的校验
  if ((Array.isArray(rules) && rules.length === 1 && rules[0].required === true) || rules.length === 0) {
    rules.push({ validator: validators.validateField, trigger: 'change' });
  }
  return Object.assign(data, {
    type: 'custom',
    rules
  });
};

/* 校验表单配置 */
export const validateForms = (groupType, groups, forms) => {
  const props: string[] = [];
  const groupNames = groups.map(({ name }) => name);
  const errors: string[] = [];
  forms.forEach((item) => {
    // label 不存在
    if (item.type !== 'serve' && !item.label) {
      errors.push(i18n.t('pa.labelError', [item.prop]) as string);
    }
    // prop 不存在
    if (item.type !== 'serve' && !item.prop) {
      errors.push(i18n.t('pa.propError', [item.label]) as string);
    }
    // prop 字段重复
    if (props.includes(item.prop as string)) {
      errors.push(i18n.t('pa.repeatProp', [item.prop]) as string);
    }
    // 不存在分组信息
    if (groupType && !item.group) {
      errors.push(i18n.t('pa.groupError', [item.label]) as string);
    }
    if (groupType && !groupNames.includes(item.group)) {
      errors.push(i18n.t('pa.groupInfoError', [item.label, item.group]) as string);
    }
    props.push(item.prop);
  });
  return errors;
};
