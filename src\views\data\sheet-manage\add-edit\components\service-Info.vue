<template>
  <pro-grid type="info" :title="$t('pa.data.table.detail.serviceInfo')" class="service-Info__container">
    <!-- body -->
    <div class="service-Info-body">
      <el-form ref="formRef" inline :model="formData" :rules="formRule">
        <!-- 服务类型 -->
        <el-form-item :label="$t('pa.data.table.detail.serviceType')" prop="resType">
          <bs-select
            v-model="formData.resType"
            :options="resTypeList"
            :placeholder="$t('pa.data.table.detail.serviceType')"
            @change="handleResTypeChange(false)"
          />
        </el-form-item>
        <!-- 数据库类型 -->
        <el-form-item v-if="isJdbc" :label="$t('pa.data.table.detail.databaseType')" prop="jdbcType">
          <bs-select v-model="formData.jdbcType" filterable clearable :options="jdbcTypeList" @change="handleJdbcTypeList" />
        </el-form-item>
        <!-- 服务 -->
        <el-form-item :label="$t('pa.home.service')" prop="resId">
          <bs-select
            v-model="formData.resId"
            filterable
            :options="serviceList"
            :placeholder="$t('pa.data.table.detail.placeholder.servicePlaceholder1')"
            :label-map="serviceLabelMap"
            @change="handleServiceChange(false)"
          />
        </el-form-item>
        <!-- 配置项 -->
        <el-form-item v-for="it in formItemList" :key="it.model" :label="it.label" :prop="it.model">
          <el-input
            v-if="it.type === 'input' && it.show"
            v-model="formData[it.model]"
            :readonly="it.readonly"
            :placeholder="it.placeholder"
          />
          <bs-select
            v-if="it.type === 'select' && it.show"
            v-model="formData[it.model]"
            filterable
            :multiple="it.multiple"
            :readonly="it.readonly"
            :loading="selectLoading"
            :placeholder="it.placeholder"
            :options="optionsMap[it.model]"
            @change="handleSelectChange(it.model)"
          />
        </el-form-item>
      </el-form>
      <!-- 服务地址 -->
      <div class="service-address">{{ $t('pa.data.table.detail.serviceAddress') }} {{ serviceUrl }}</div>
    </div>
  </pro-grid>
</template>

<script lang="ts">
import { Component, Prop, Ref, Vue } from 'vue-property-decorator';
import { getResList, getJdbcTypeList, getServiceList } from '@/apis/dataApi';
import ElForm from 'bs-ui-pro/packages/form/index.js';
import { cloneDeep, safeArray, safeParse } from '@/utils';
import { get } from '@/apis/utils/net';
@Component({})
export default class ServiceInfo extends Vue {
  @Prop({ default: () => null }) data!: any;
  @Ref('formRef') readonly form!: ElForm;

  id = '';
  formData: any = { resType: '', resId: '', jdbcType: 'All' };
  formRule: any = {
    resType: { required: true, message: this.$t('pa.data.table.placeholder.select'), trigger: 'blur' },
    resId: { required: true, validator: this.serviceListValidator, trigger: 'change' },
    jdbcType: { required: true, message: this.$t('pa.data.table.detail.placeholder.servicePlaceholder2'), trigger: 'blur' }
  };
  resTypeList: any[] = [];
  jdbcTypeList: any[] = [];
  serviceLabelMap = new Map();
  formItemList: any[] = [];
  optionsMap: any = {};

  serviceListAll: any[] = []; // ???
  serviceList: any[] = [];
  configInfo = {};
  selectLoading = false;

  get isJdbc() {
    return this.formData.resType === 'JDBC';
  }
  get serviceUrl() {
    const target = this.serviceList.find((it) => it.value === this.formData.resId);
    return target?.url || '';
  }

  created() {
    this.id = this.$route.query.id as string;
    this.getJdbcTypeList();
  }
  /* 获取数据库类型 */
  async getJdbcTypeList() {
    const { success, data, error } = await getJdbcTypeList();
    if (!success) return this.$message.error(error);
    data.All = data.all;
    delete data.all;
    this.jdbcTypeList = Object.keys(data)
      .sort()
      .map((value) => {
        return {
          value,
          label: value === 'All' ? this.$t('pa.all') : value,
          children: data[value].map((it) => ({
            label: it.title,
            value: it.id,
            url: it.url,
            resProperty: safeParse(it.resProperty)
          }))
        };
      });
  }
  /* 获取服务类型 */
  async getResList() {
    const { success, data, error } = await getResList();
    if (!success) return this.$message.error(error);
    this.resTypeList = safeArray(data).map((it) => {
      return { label: it.resType, value: it.resType, autoSyncConfig: it.autoSyncConfig, children: it.children };
    });
  }
  /* 服务类型变化 */
  handleResTypeChange(isInit = false) {
    this.form.clearValidate();
    for (const i of this.formItemList) {
      delete this.formData[i.model];
      delete this.formRule[i.model];
    }
    const target = this.resTypeList.find((it) => it.value === this.formData.resType);
    this.formItemList = safeArray(target?.children);
    !isInit && (this.formData.resId = '');
    this.getServiceList(isInit);
    this.$emit('service-type-change', cloneDeep(this.formData), target?.autoSyncConfig);
  }
  /* 获取服务 */
  async getServiceList(isInit = false) {
    const { success, data, error } = await getServiceList(this.formData.resType);
    if (!success) return this.$message.error(error);
    this.serviceListAll = data;
    this.serviceList = safeArray(data).map((it) => ({
      value: it.id,
      label: it.title,
      url: it.url,
      resProperty: safeParse(it.resProperty)
    }));
    if (isInit) {
      this.formData.resId = this.data.resId;
      this.handleServiceChange(isInit);
    }
  }
  /* 服务类型变化 */
  handleServiceChange(isInit = false) {
    for (const it of this.formItemList) {
      this.handleItem(it, isInit);
    }
  }
  /* 获取配置项Options */
  async handleItem(it: any, isInit = false) {
    try {
      this.$set(this.formData, it.model, isInit ? this.data[it.model] : '');
      if (it.type !== 'select') return;
      this.selectLoading = true;
      const params = safeArray(it.paramsField).reduce((pre, next) => {
        pre[next] = this.formData[next];
        return pre;
      }, {});
      const { data, success, msg } = await get(it.remoteUrl, params);
      if (!success) return this.$message.error(msg);
      this.optionsMap[it.model] = safeArray(data).map((value) => ({ label: value, value }));
    } finally {
      const rule =
        it.type === 'select'
          ? { required: true, validator: this.selectValidator(it), trigger: 'blur' }
          : { required: true, message: this.$t('pa.data.table.detail.tips.notEmpty', [it.label]), trigger: 'change' };
      this.$set(this.formRule, it.model, rule);
      this.selectLoading = false;
    }
  }
  /* 数据库类型变化 */
  handleJdbcTypeList() {
    const target = this.jdbcTypeList.find(({ value }) => value === this.formData.jdbcType);
    this.serviceList = safeArray(target?.children);
    this.formData.resId = '';
    this.$emit('update-connector');
  }
  serviceListValidator(rule: any, value: string, callback: any) {
    if (!value) return callback(new Error(this.$t('pa.data.table.detail.placeholder.servicePlaceholder1')));
    if (!this.serviceList.map(({ value }) => value).includes(value)) {
      return callback(new Error(this.$t('pa.data.table.detail.tips.noServePermissions')));
    }
    callback();
  }
  selectValidator(it: any) {
    return (rule: any, value: string, callback: any) => {
      if (!value) return callback(new Error(this.$t('pa.data.table.detail.tips.notEmpty', [it.label])));
      const valueList = safeArray(this.optionsMap[it.model]).map(({ value }) => value);
      if (!valueList.includes(value))
        return callback(new Error(this.$t('pa.data.table.detail.tips.noPermissions', [it.label])));
      callback();
    };
  }
  handleSelectChange(key: string) {
    this.form.validateField(key);
  }
  public async init() {
    await this.getResList();
    if (this.data) {
      this.formData.resType = this.data.resType;
      this.formData.jdbcType = this.data.jdbcType;
      this.serviceLabelMap.set(this.data.resId, this.data.resName);
      this.handleResTypeChange(true);
    } else {
      this.formData.resType = this.resTypeList[0].value;
      this.handleResTypeChange(false);
    }
  }
  public getResProperty() {
    const target = this.serviceList.find(({ value }) => value === this.formData.resId);
    return { ...target?.resProperty, ...this.formData };
  }
  public async validate() {
    try {
      await this.form.validate();
      const result = { ...this.formData };
      if (!this.isJdbc) delete result.jdbcType;
      const target = this.serviceList.find(({ value }) => value === result.resId);
      result.resName = target?.label || '';
      return result;
    } catch (e) {
      throw e;
    }
  }
}
</script>

<style lang="scss" scoped>
.service-Info {
  &__container {
    margin-bottom: 20px;
  }
  &-body {
    padding: 20px 25px;
  }
}
</style>
