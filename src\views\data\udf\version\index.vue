<template>
  <div class="page-content">
    <div class="content">
      <bs-table
        v-loading="tableLoading"
        :height="'calc(100vh - 308px)'"
        :data="tableData.tableData"
        :column-data="tableData.columnData"
        :page-data="tableData.pageData"
        @refresh="getListData(true)"
        @page-change="handleCurrentChange"
      >
        <template slot="operator" slot-scope="{ row }">
          <el-tooltip v-if="!shareFlag" effect="light" :content="operatorConfig[0].label">
            <i
              :class="operatorConfig[0].iconfont"
              class="roll-back__icon"
              @click="operateHandler(operatorConfig[0].event, row)"
            ></i>
          </el-tooltip>
          <el-tooltip effect="light" :content="operatorConfig[1].label">
            <i
              :class="operatorConfig[1].iconfont"
              class="roll-back__icon"
              @click="operateHandler(operatorConfig[1].event, row)"
            ></i>
          </el-tooltip>
        </template>
      </bs-table>
      <sql-preview-dialog
        v-if="showPreviewDialog"
        :title="$t('pa.data.codeView')"
        :show.sync="showPreviewDialog"
        :data="sourceCode"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Emit } from 'vue-property-decorator';
import { URL_UDF_UDFHIS, URL_UDF_ROLLBACK } from '@/apis/commonApi';
import * as _ from 'lodash';
import dayjs from 'dayjs';
import { get, post } from '@/apis/utils/net';

@Component({
  components: {
    SqlPreviewDialog: () => import('../../modals/sql-preview-dialog.vue')
  }
})
export default class RollBack extends Vue {
  @Prop({ default: () => ({}) }) data: any;
  @Prop({ default: false }) shareFlag!: boolean;
  dialogVisible = false;
  selectedList: string[] = [];
  wrDialogVisible = false;
  tableLoading = false;
  sourceCode = '';
  searchObj = {
    pageData: { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 },
    sortData: { udfVersion: 'DESC' }
  };
  tableData: any = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  operatorConfig: any = [
    {
      label: this.$t('pa.action.rollBack'),
      event: 'rollBack',
      iconfont: 'iconfont icon-roll-back'
    },
    {
      label: this.$t('pa.action.viewSourceCode'),
      event: 'preview',
      iconfont: 'iconfont icon-ziyuan'
    }
  ];
  showPreviewDialog = false;

  @Emit('versionChangeCallback')
  callback(e) {
    if (e) {
      return true;
    }
  }

  async rollBack(row: any) {
    try {
      await this.$confirm(this.$t('pa.tip.rollBackConfirm'), this.$t('pa.prompt'));
      const api = URL_UDF_ROLLBACK + '?versionId=' + row.id;
      this.tableLoading = true;
      await get(api);
      this.getListData();
    } catch {
    } finally {
      this.tableLoading = false;
    }
  }

  operateHandler(event, row) {
    this[event](row);
  }
  preview(row) {
    this.sourceCode = row.sourceCode || '';
    this.showPreviewDialog = true;
  }
  handleCurrentChange(currentPage, pageSize) {
    this.searchObj.pageData.currentPage = currentPage;
    this.searchObj.pageData.pageSize = pageSize;
    this.getListData();
  }
  async getListData(refresh = false) {
    this.tableLoading = true;
    const searchObj = _.cloneDeep(this.searchObj);
    const obj = { pageData: {}, search: this.data.id, sortData: {} };
    obj.pageData = searchObj.pageData;
    obj.sortData = searchObj.sortData;
    try {
      const { success, msg, data } = await post(URL_UDF_UDFHIS, obj);
      this.tableLoading = false;
      if (!success) throw msg;
      const { columnData, tableData, pageData } = data;
      columnData.push({ label: this.$t('pa.action.action'), value: 'operator', width: 180 });
      tableData.forEach((el) => {
        el.updateTime = dayjs(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
      });
      this.tableData = {
        columnData,
        tableData,
        pageData
      };
      refresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    } catch (err) {
      this.$tip.error({ message: err, duration: 5000 });
      this.tableLoading = false;
    }
  }
  handleSelectionChange(sel: any) {
    this.selectedList = [];
    sel.forEach((m) => {
      if (m.dataLevelType !== 'PARENT') {
        this.selectedList.push(m.id);
      }
    });
  }
  hasAuthority(hasRole: any, row: any) {
    return !_.isEmpty(_.toString(row.dataLevelType)) && hasRole;
  }
  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.getListData();
  }
  closeDialog(needFresh: any) {
    if (needFresh === true) {
      this.getListData();
    }
    this.dialogVisible = false;
  }
  created() {
    this.getListData();
  }
}
</script>

<style scoped lang="scss">
.page-content {
  overflow: auto;
  background: #fff;
  height: calc(100vh - 190px);
  .content {
    height: calc(100vh - 190px);
  }
  .roll-back {
    background: #fff;
    &__icon {
      cursor: pointer;
      margin: 0 5px;
    }
  }
}
</style>
