<template>
  <div class="page-content">
    <div class="content">
      <bs-table
        v-loading="tableLoading"
        :height="'calc(100vh - 307px)'"
        :data="tableData.tableData"
        :column-data="tableData.columnData"
        :page-data="tableData.pageData"
        @refresh="getListData"
        @page-change="handleCurrentChange"
      >
        <template slot="operator" slot-scope="{ row }">
          <el-tooltip effect="light" :content="operatorConfig[0].label">
            <i
              :class="operatorConfig[0].iconfont"
              class="roll-back__icon"
              @click="operateHandler(operatorConfig[0].event, row)"
            ></i>
          </el-tooltip>
          <el-tooltip effect="light" :content="operatorConfig[1].label">
            <i
              :class="operatorConfig[1].iconfont"
              class="roll-back__icon"
              @click="operateHandler(operatorConfig[1].event, row)"
            ></i>
          </el-tooltip>
        </template>
      </bs-table>
      <preview ref="preview" :title="'源码查看'" :data="sourceCode" />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Emit } from 'vue-property-decorator';
import { URL_UDF_UDFHIS, URL_UDF_ROLLBACK } from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
import moment from 'moment';
import Preview from '@/views/data/modals/flink-sql.vue';

@Component({
  components: {
    Preview
  }
})
export default class RollBack extends PaBase {
  @Prop({
    default: () => {
      return {};
    }
  })
  data: any;
  dialogVisible = false;
  selectedList: string[] = [];
  wrDialogVisible = false;
  tableLoading = false;
  sourceCode = '';
  searchObj = {
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: { udfVersion: 'DESC' }
  };
  tableData: any = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  operatorConfig: any = [
    {
      label: '回滚',
      event: 'rollBack',
      iconfont: 'iconfont icon-roll-back'
    },
    {
      label: '查看源码',
      event: 'preview',
      iconfont: 'iconfont icon-ziyuan'
    }
  ];

  @Emit('versionChangeCallback')
  callback(e) {
    if (e) {
      return true;
    }
  }

  rollBack(row: any) {
    const api = URL_UDF_ROLLBACK + '?versionId=' + row.id;
    this.$confirm('是否确认回滚?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        this.tableLoading = true;
        this.doGet(api).then((resp: any) => {
          this.parseResponse(resp, () => {
            this.callback(true);
            this.getListData();
          });
        });
      })
      .catch(() => {
        return true;
      });
  }

  operateHandler(event, row) {
    this[event](row);
  }
  preview(row) {
    this.sourceCode = row.sourceCode || '';
    (this.$refs.preview as any).visible = true;
  }
  handleCurrentChange(currentPage, pageSize) {
    this.searchObj.pageData.currentPage = currentPage;
    this.searchObj.pageData.pageSize = pageSize;
    this.getListData();
  }
  getListData() {
    this.tableLoading = true;
    const searchObj = _.cloneDeep(this.searchObj);
    const obj = {
      pageData: {},
      search: this.data.id,
      sortData: {}
    };
    obj.pageData = searchObj.pageData;
    obj.sortData = searchObj.sortData;
    this.doPost(URL_UDF_UDFHIS, obj).then((resp: any) => {
      this.parseResponse(resp, () => {
        const { columnData, tableData, pageData } = resp.data;
        columnData.forEach((el) => {
          el.align = 'center';
        });
        columnData.push({ label: '操作', value: 'operator', width: 180, align: 'center' });
        tableData.forEach((el) => {
          el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
        });
        this.tableData = {
          columnData,
          tableData,
          pageData
        };
      });
      this.tableLoading = false;
    });
  }
  handleSelectionChange(sel: any) {
    this.selectedList = [];
    sel.forEach((m) => {
      if (m.dataLevelType !== 'PARENT') {
        this.selectedList.push(m.id);
      }
    });
  }
  hasAuthority(hasRole: any, row: any) {
    return !_.isEmpty(_.toString(row.dataLevelType)) && hasRole;
  }
  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.getListData();
  }
  closeDialog(needFresh: any) {
    if (needFresh === true) {
      this.getListData();
    }
    this.dialogVisible = false;
  }
  created() {
    this.getListData();
  }
}
</script>

<style scoped lang="scss">
.page-content {
  overflow: auto;
  background: #fff;
  height: calc(100vh - 173px);
  .content {
    height: calc(100vh - 173px);
  }
  .roll-back {
    background: #fff;
    &__icon {
      cursor: pointer;
      margin: 0 5px;
    }
  }
}
</style>
