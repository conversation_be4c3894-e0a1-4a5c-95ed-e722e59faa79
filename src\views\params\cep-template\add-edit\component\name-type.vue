<template>
  <el-form-item :prop="`${prefix}[${data.prop}]`" :rules="handlerRules(data)">
    <el-select
      v-model="formData.groupCepPatternConditionList[scopeIndex][data.prop]"
      clearable
      filterable
      size="small"
      style="width: 100%"
      :placeholder="`请选择${data.label}`"
      @change="handlerOnChange"
    >
      <el-option
        v-for="(el, index) in elOptions"
        :key="index"
        :label="el.label"
        :value="el.value"
        :disabled="el.disabled"
      />
    </el-select>
  </el-form-item>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class NameType extends Vue {
  @Prop({ type: Number }) index!: number;
  @Prop({ type: Number }) scopeIndex!: number;
  @Prop({ type: Object, default: () => ({}) }) data!: any;
  @Prop({ type: Object, default: () => ({}) }) formData!: any;
  @Prop({ type: Array, default: () => [] }) options!: any;
  @Prop({ type: Array, default: () => [] }) usedModuleList!: any;

  private oldData = '';

  get prefix() {
    return `groupCepPatternConditionList[${this.scopeIndex}]`;
  }

  get modelOrGroupName() {
    return this.formData.groupCepPatternConditionList[this.scopeIndex].modelOrGroupName;
  }
  get isNotConneModeType() {
    return this.formData.groupCepPatternConditionList[this.scopeIndex].isNotConneModeType;
  }

  get elOptions() {
    if (this.usedModuleList.lenght < 1) return [];
    const disabledList = this.usedModuleList.filter((el) => el !== this.modelOrGroupName);
    return this.options.map((el) => {
      const temp = { ...el };
      temp.disabled = disabledList.includes(el.value);
      return temp;
    });
  }
  created() {
    this.$watch(
      'modelOrGroupName',
      (newVal, oldVal) => {
        this.oldData = oldVal || newVal;
      },
      {
        deep: true,
        immediate: true
      }
    );
  }
  handlerRules({ required = false, label }: any) {
    return {
      required,
      message: `请选择${label}`,
      trigger: 'change'
    };
  }
  handlerOnChange() {
    this.$emit('change', ...[this.scopeIndex, this.oldData]);
  }
}
</script>
