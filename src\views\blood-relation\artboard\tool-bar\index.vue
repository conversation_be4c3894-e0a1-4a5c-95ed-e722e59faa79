<template>
  <div class="tool__container" :style="{ height }">
    <el-tooltip
      v-for="el in toolList"
      :key="el.name"
      :content="el.tooltip"
      effect="light"
      placement="right"
    >
      <i :class="el.icon" class="tool-item" @click="click(el.name)"></i>
    </el-tooltip>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class ToolBar extends Vue {
  @Prop() graph!: any;

  private toolList: any[] = [
    {
      name: 'center',
      tooltip: '画布居中',
      icon: 'iconfont icon-huizhongxindian'
    },
    {
      name: 'fit',
      tooltip: '适应所有内容',
      icon: 'iconfont icon-shiyingpingmu'
    }
  ];

  get height() {
    return `${this.toolList.length * 44}px`;
  }
  click(type: string) {
    if (type === 'center') return this.graph?.center();
    if (type === 'fit') return this.graph?.fit();
  }
}
</script>
<style lang="scss" scoped>
.tool {
  &__container {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    align-items: center;
    position: absolute;
    top: 30px;
    right: 30px;
    z-index: 100;
    padding: 20px 0;
    width: 35px;
    background: #ffffff;
    box-shadow: 0px 0px 5px 0px rgba(2, 13, 41, 0.07);
    border-radius: 18px;

    overflow: hidden;
  }
  &-item {
    width: 14px;
    height: 14px;
    line-height: 14px;
    color: #7b838c;

    &:hover {
      background: #f6f6f6;
      color: #377cff;
      cursor: pointer;
    }
    &.focus:focus {
      background: #f6f6f6;
      color: #377cff;
      cursor: pointer;
    }
  }
}
</style>
