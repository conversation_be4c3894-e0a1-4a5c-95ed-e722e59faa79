<template>
  <bs-dialog
    v-loading="loading"
    top="30px"
    width="650px"
    :title="title"
    :before-close="cancel"
    :visible.sync="display"
    :element-loading-text="loadingText"
  >
    <div class="flow-config-container">
      <single-flow-config
        ref="flowConfig"
        :org-id="orgId"
        :data="formData"
        :disabled="disabled"
        :is-flink-sql="isFlinkSql"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submit">{{ canOnline ? '保存' : '确定' }}</el-button>
      <!-- ddd -->
      <div v-if="canOnline" class="flow-config-online">
        <el-tooltip
          effect="light"
          placement="top"
          content="流程首次启动，或者修改kafka consumer group等信息后需要从头开始消费数据。"
        >
          <el-button type="primary" @click="online(false)">无状态启动</el-button>
        </el-tooltip>
        <el-tooltip
          effect="light"
          placement="top"
          content="流程重启，需要接着上次checkpoint记录的位置，如kafka上次的offset位置继续消费。"
        >
          <el-button type="primary" :disabled="!canOnlineByLastState" @click="online(true)">
            基于上次状态启动
          </el-button>
        </el-tooltip>
      </div>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import $ from 'jquery';
import store from '@/store';
import { get, post, put } from '@/apis/utils/net';
import { flowDefaultConfig } from '@/utils';
import {
  URL_JOB_FINDBYID,
  URL_JOB_ONLINE,
  URL_JOB_PREONLINE,
  URL_JOB_RESOURCECONFIG
} from '@/apis/commonApi';
import SingleFlowConfig from '@/components/single-flow-config/index.vue';
import { RELOAD_JOB, UPDATE_JOB_DATA } from '@/store/event-names/mutations';
import { cloneDeep } from 'lodash';

@Component({ components: { SingleFlowConfig } })
export default class FlowConfig extends Vue {
  @Prop() data!: any;
  @Prop({ type: String, default: '流程配置' }) title!: string;
  @Prop({ type: Boolean, default: false }) canOnline!: string;
  @PropSync('show', { type: Boolean, default: false }) display!: boolean;
  @Ref('flowConfig') readonly flowConfig!: FlowConfig;
  private jobData: any = {};
  private formData: any = {};
  private loading = false;
  private loadingText = '';
  private originProperties = '';
  get id() {
    return this.data.id;
  }

  get orgId() {
    return this.data.orgId;
  }

  get canOnlineByLastState() {
    return this.jobData.jobRuntimeId;
  }

  get isFlinkSql() {
    return this.jobData.jobType === 'FLINK_SQL';
  }

  get isDev() {
    return this.jobData.jobStatus === 'DEV';
  }

  get disabled() {
    return ['INPROD', 'PROD'].includes(this.jobData.jobStatus);
  }

  async created() {
    await this.getJobDetail();
  }

  async getJobDetail() {
    this.loading = true;
    try {
      const { success, data, msg } = await get(URL_JOB_FINDBYID, { id: this.id });
      if (success) {
        this.$set(this, 'jobData', data);
        const { properties = '' } = data;
        this.$set(this, 'formData', { ...flowDefaultConfig(), ...JSON.parse(properties) });
        this.originProperties = JSON.stringify(this.formData);
      } else {
        this.$tip.error(msg);
      }
      this.loading = false;
    } catch (e) {
      this.$set(this, 'formData', { ...flowDefaultConfig() });
      this.loading = false;
    }
  }

  /* 取消 */
  cancel() {
    this.display = false;
    this.$emit('close');
  }

  async submit() {
    try {
      this.loadingText = '正在保存配置，请稍后...';
      this.loading = true;
      const res: any = await this.flowConfig.submit();
      const params = cloneDeep(res);
      delete params.property;
      await this.saveFlowConfig(params);
    } catch (e) {
      this.loading = false;
      const [[{ message }]] = Object.values(e);
      this.$tip.error(message || '配置有误，请检查右侧配置');
    }
  }

  async saveFlowConfig(params: object = {}, canOnlineByLastState = false) {
    try {
      const { id } = this;
      const properties = JSON.stringify(params);
      const { success, msg } = await put(URL_JOB_RESOURCECONFIG, { id, properties });
      this.loading = false;
      if (success) {
        store.state.job.data.clusterId = this.formData.clusterId;
        store.state.job.data.properties = properties;
        store.commit(UPDATE_JOB_DATA, store.state.job.data);
        store.commit(RELOAD_JOB, true);
        const { isDev, formData, canOnline } = this;
        if (isDev) {
          this.originProperties = JSON.stringify(formData);
          this.updateDomNodeParallelism();
        }
        if (canOnline) {
          this.originProperties = JSON.stringify(this.formData);
          await this.online(canOnlineByLastState);
          return;
        }
        this.$tip.success(msg);
        this.cancel();
        return;
      }
      this.$tip.error(msg);
    } catch (e) {
      this.loading = false;
    }
  }

  updateDomNodeParallelism() {
    const { parallelism } = this.formData;
    store.state.job.data.content.nodes.forEach((item) => (item.parallelism = parallelism));
    store.commit(UPDATE_JOB_DATA, store.state.job.data);
    $('.nodeInstance .bg-circle').text(parallelism);
  }

  async online(fromLastCheckpoint = false) {
    if (this.originProperties === JSON.stringify(this.formData)) {
      try {
        const { id } = this;
        this.loadingText = '执行中，请稍等...';
        this.loading = true;
        const { success, msg: message } = await post(URL_JOB_PREONLINE, {
          relationBatchTag: false,
          jobs: [id]
        });
        if (success) {
          const res = await post(URL_JOB_ONLINE, [{ id, fromLastCheckpoint }]);
          this.loading = false;
          if (res.success) {
            this.$tip.success(res.msg);
            this.cancel();
            return;
          }
        }
        this.$tip.error({ message, duration: 5000 });
        this.loading = false;
      } catch (e) {
        this.loading = false;
      }
    } else {
      await this.submit();
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .bs-dialog {
  padding: 0;
}
::v-deep .el-dialog__body {
  padding: 0px 0px 30px 20px;
}
.flow-config {
  &-container {
    height: 520px;
    overflow: auto;
  }

  &-online {
    margin: 0 0 0 10px;
    display: inline-block;
  }
}
</style>
