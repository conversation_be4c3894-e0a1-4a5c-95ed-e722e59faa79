export const NO_CHECK_POINT = [
  'checkpointInterval',
  'cp_timeout',
  'cp_min_pause',
  'cp_failed',
  'cp_unaligned',
  'stateBackend'
];
export const MAPPING = {
  'failure-rate': [
    'attempts',
    'delay',
    'initialBackoff',
    'maxBackoff',
    'backoffMultiplier',
    'resetBackoffThreshold',
    'jitterFactor'
  ],
  'fixed-delay': [
    'failureRateDelay',
    'failuresPerInterval',
    'failureRateInterval',
    'initialBackoff',
    'maxBackoff',
    'backoffMultiplier',
    'resetBackoffThreshold',
    'jitterFactor'
  ],
  'exponential-delay': [
    'failureRateDelay',
    'attempts',
    'delay',
    'failuresPerInterval',
    'failureRateInterval'
  ],
  all: [
    'attempts',
    'delay',
    'failuresPerInterval',
    'failuresPerInterval',
    'failureRateDelay',
    'initialBackoff',
    'maxBackoff',
    'backoffMultiplier',
    'resetBackoffThreshold',
    'jitterFactor'
  ]
};
