<?xml version="1.0" encoding="UTF-8"?>
<!-- ~ Copyright (c) 2019. Bangsun Technology.Co.Ltd. All rights reserved. http://www.bsfit.com.cn -->
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.com.bsfit</groupId>
    <artifactId>pipeace-parent</artifactId>
    <!-- 依赖后端的版本号 -->
    <version>2.7.13-17-guangda-SNAPSHOT</version>
  </parent>
  <artifactId>pipeace-mgr-ui</artifactId>
  <name>pipeace-mgr-ui</name>
  <!-- 打包后产物的版本号 -->
  <version>2.7.13-13-guangda-SNAPSHOT</version>
  <packaging>jar</packaging>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <build>
    <resources>
      <resource>
        <directory>dist</directory>
        <filtering>false</filtering>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>buildnumber-maven-plugin</artifactId>
        <version>1.2</version>
        <executions>
          <execution>
            <phase>validate</phase>
            <goals>
              <goal>create</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <doUpdate>false</doUpdate>
          <shortRevisionLength>7</shortRevisionLength>
          <items>
            <item>timestamp</item>
          </items>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.4</version>
        <executions>
          <execution>
            <goals>
              <goal>jar</goal>
            </goals>
            <phase>package</phase>
            <configuration>
              <includes>
                <include>**/*</include>
              </includes>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <archive>
            <forced>true</forced>
            <manifest>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
            </manifest>
            <manifestEntries>
              <Implementation-Build>${buildNumber}</Implementation-Build>
            </manifestEntries>
            <addMavenDescriptor>true</addMavenDescriptor>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>npm install</id>
            <goals>
              <goal>exec</goal>
            </goals>
            <phase>validate</phase>
            <configuration>
              <executable>npm</executable>
              <arguments>
                <argument>install</argument>
              </arguments>
            </configuration>
          </execution>
          <execution>
            <id>npm clean and build</id>
            <goals>
              <goal>exec</goal>
            </goals>
            <phase>validate</phase>
            <configuration>
              <executable>npm</executable>
              <arguments>
                <argument>run</argument>
                <argument>build</argument>
              </arguments>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <environmentVariables>
            <!-- The following parameters create an NPM sandbox for CI -->
            <NPM_CONFIG_TMP>${project.build.directory}/npmtmp</NPM_CONFIG_TMP>
          </environmentVariables>
          <workingDirectory>${basedir}/</workingDirectory>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
