<?xml version="1.0" encoding="UTF-8"?>
<!-- ~ Copyright (c) 2019. Bangsun Technology.Co.Ltd. All rights reserved. http://www.bsfit.com.cn -->
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>cn.com.bsfit</groupId>
  <artifactId>pipeace-mgr-ui</artifactId>
  <name>pipeace-mgr-ui</name>
  <!-- 打包后产物的版本号 -->
  <version>2.13.0-2.2-SNAPSHOT</version>
  <packaging>jar</packaging>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <build>
    <resources>
      <resource>
        <directory>dist</directory>
        <filtering>false</filtering>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>buildnumber-maven-plugin</artifactId>
        <version>1.2</version>
        <executions>
          <execution>
            <phase>validate</phase>
            <goals>
              <goal>create</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <doUpdate>false</doUpdate>
          <shortRevisionLength>7</shortRevisionLength>
          <items>
            <item>timestamp</item>
          </items>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.4</version>
        <executions>
          <execution>
            <goals>
              <goal>jar</goal>
            </goals>
            <phase>package</phase>
            <configuration>
              <includes>
                <include>**/*</include>
              </includes>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <archive>
            <forced>true</forced>
            <manifest>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
            </manifest>
            <manifestEntries>
              <Implementation-Build>${buildNumber}</Implementation-Build>
            </manifestEntries>
            <addMavenDescriptor>true</addMavenDescriptor>
          </archive>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <scm>
    <url>ssh://***********/pipeace/pipeace-mgr-ui-dev.git </url>
    <connection>scm:git:ssh://***********/pipeace/pipeace-mgr-ui-dev.git</connection>
  </scm>
  <distributionManagement>
    <repository>
      <id>ext-release-local</id>
      <name>ext-release-local</name>
      <url>http://************:8082/artifactory/ext-release-local/</url>
    </repository>
    <snapshotRepository>
      <id>ext-snapshot-local</id>
      <name>ext-snapshot-local</name>
      <url>http://************:8082/artifactory/ext-snapshot-local/</url>
    </snapshotRepository>
  </distributionManagement>
</project>
