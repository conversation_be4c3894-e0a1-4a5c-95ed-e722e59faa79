<template>
  <el-form-item
    v-if="!config.hidden"
    :prop="config.name"
    :label="config.label"
    :label-width="labelWidth"
    :class="{ tooltip: config.tooltip }"
  >
    <div
      class="base-form__content base-form__content--one"
      :class="{ 'base-form__content--tooltip': config.tooltip }"
    >
      <!-- input -->
      <el-input
        v-if="config.type === 'input'"
        v-model="formData[config.name]"
        :placeholder="config.placeholder"
        :maxlength="config.maxlength"
        :minlength="config.minlength"
        :disabled="config.disabled"
      />
      <!-- select -->
      <el-select
        v-if="config.type === 'select'"
        v-model="formData[config.name]"
        :placeholder="config.placeholder"
      >
        <el-option
          v-for="el in config.options"
          :key="el.value"
          :label="el.label"
          :value="el.value"
        />
      </el-select>
      <!-- number -->
      <el-input-number
        v-if="config.type === 'number'"
        v-model="formData[config.name]"
        controls-position="right"
        :min="1"
        :placeholder="config.placeholder"
      />
      <!-- textarea -->
      <el-input
        v-if="config.type === 'textarea'"
        v-model="formData[config.name]"
        type="textarea"
        :autosize="config.autosize"
        maxlength="200"
        show-word-limit
        :placeholder="config.placeholder"
      />
    </div>
    <!-- 提示 -->
    <el-tooltip v-if="config.tooltip" effect="light" :content="config.tooltip" placement="bottom">
      <i class="iconfont icon-wenhao"></i>
    </el-tooltip>
  </el-form-item>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import type { BaseInfo, RenderItem } from '../../type';
import { generateBaseInfo, hide } from '../../util';

@Component({ directives: { hide } })
export default class FormItem extends Vue {
  @Prop({ default: '' }) labelWidth!: string;
  @Prop({ default: () => ({}) }) config!: RenderItem;
  @Prop({ default: () => generateBaseInfo() }) formData!: BaseInfo;
}
</script>
