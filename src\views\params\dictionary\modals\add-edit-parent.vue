<template>
  <bs-dialog
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="title"
    :visible.sync="visible"
    width="490px"
    :before-close="closeDialog"
  >
    <el-form
      ref="ruleForm"
      v-loading="formLoading"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
      element-loading-text="数据正在加载中..."
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="编码" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入编码"
              maxlength="50"
              show-word-limit
              :disabled="!!formData.id"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item label="名称" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="请输入名称"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" :loading="loading" @click="submit('ruleForm')">确 定</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Emit } from 'vue-property-decorator';
import { URL_DIC_ADD, URL_DIC_UPDATE } from '@/apis/commonApi';
import { cloneDeep } from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component
export default class AddEditParent extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: '新建' }) title!: string;
  @Prop({ default: {} }) data!: any;
  @Prop({ default: false }) formLoading!: boolean;

  formData: any = {};
  loading = false;

  rules: any = {
    code: [
      { required: true, message: '请输入编码', trigger: 'blur' },
      {
        validator: this.validSameCode,
        trigger: 'blur'
      }
    ],
    title: [
      { required: true, message: '请输入名称', trigger: 'blur' },
      { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
    ]
  };
  created() {
    this.formData = this.data ? cloneDeep(this.data) : { code: '', title: '' };
  }
  validSameCode(rule: any, value: any, callback: any) {
    if (this.formData.code === this.formData.parentCode) {
      callback(new Error('编码和上级编码不能相同'));
    } else {
      callback();
    }
  }
  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  private closeDialog(_needFresh: boolean) {
    this.formData = {};
    this.loading = false;
    this.formLoading = false;
  }

  submit(formName: string) {
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        this.loading = true;
        if (this.formData.id) {
          this.doPut(URL_DIC_UPDATE, this.formData).then((resp: any) => {
            this.parseResp(resp);
          });
        } else {
          this.doPost(URL_DIC_ADD, this.formData).then((resp: any) => {
            this.parseResp(resp);
          });
        }
      }
    });
  }

  parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.closeDialog(true);
    });
  }
}
</script>
