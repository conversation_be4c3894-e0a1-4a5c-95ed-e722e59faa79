<template>
  <bs-dialog
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="title"
    :visible.sync="visible"
    width="490px"
    :before-close="closeDialog"
  >
    <el-form
      ref="ruleForm"
      v-loading="formLoading"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
      :element-loading-text="$t('pa.tip.loading')"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item :label="$t('pa.encoding')" prop="code">
            <el-input
              v-model="formData.code"
              :placeholder="$t('pa.encodingPlaceholder')"
              maxlength="50"
              show-word-limit
              :disabled="!!formData.id"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item :label="$t('pa.flow.name')" prop="title">
          <el-input v-model="formData.title" :placeholder="$t('pa.placeholder.name')" maxlength="30" show-word-limit />
        </el-form-item>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">{{ $t('pa.action.close') }}</el-button>
      <el-button type="primary" :loading="loading" @click="submit('ruleForm')">{{ $t('pa.action.makeSure') }}</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Emit } from 'vue-property-decorator';
import { URL_DIC_ADD, URL_DIC_UPDATE } from '@/apis/commonApi';
import { cloneDeep } from 'lodash';
import { PaBase } from '@/common/pipeace-base';
import { post, put } from '@/apis/utils/net';
import i18n from '@/i18n';
@Component
export default class AddEditParent extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: i18n.t('pa.action.add') }) title!: string;
  @Prop({ default: {} }) data!: any;
  @Prop({ default: false }) formLoading!: boolean;

  formData: any = {};
  loading = false;

  rules: any = {
    code: [
      { required: true, message: this.$t('pa.encodingPlaceholder'), trigger: 'blur' },
      {
        validator: this.validSameCode,
        trigger: 'blur'
      }
    ],
    title: [
      { required: true, message: this.$t('pa.placeholder.name'), trigger: 'blur' },
      { min: 2, max: 30, message: this.$t('pa.length2to30'), trigger: 'blur' }
    ]
  };
  created() {
    this.formData = this.data ? cloneDeep(this.data) : { code: '', title: '' };
  }
  validSameCode(rule: any, value: any, callback: any) {
    if (this.formData.code === this.formData.parentCode) {
      callback(new Error(this.$t('pa.differentEncoding')));
    } else {
      callback();
    }
  }
  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  private closeDialog(_needFresh: boolean) {
    this.formData = {};
    this.loading = false;
    this.formLoading = false;
  }

  submit(formName: string) {
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        this.loading = true;
        if (this.formData.id) {
          put(URL_DIC_UPDATE, this.formData).then((resp: any) => {
            this.parseResp(resp);
          });
        } else {
          post(URL_DIC_ADD, this.formData).then((resp: any) => {
            this.parseResp(resp);
          });
        }
      }
    });
  }

  parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.closeDialog(true);
    });
  }
}
</script>
