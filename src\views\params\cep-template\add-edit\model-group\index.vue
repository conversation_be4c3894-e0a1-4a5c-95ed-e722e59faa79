<template>
  <pro-grid title="模式组" type="info" class="group__container">
    <!-- 添加模式组 -->
    <el-button slot="operation" type="primary" size="small" @click="addNewModeGroup">
      添加模式组
    </el-button>
    <!-- 主体内容 -->
    <div class="group__container__main">
      <config-item
        v-for="(el, index) in formData"
        :key="index"
        ref="formRef"
        name="模式组名称"
        :index="index"
        :all-name="allName"
        :disabled="disabled"
        :name-options="nameOptions"
        :mode-groupl-list.sync="grouplList"
        :used-module-list.sync="moduleList"
        :data.sync="formData[index]"
        :row-data.sync="formData"
      />
    </div>
  </pro-grid>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import ConfigItem from '../component/model-config-item.vue';

@Component({
  components: {
    ConfigItem
  }
})
export default class ModelGroup extends Vue {
  @Prop({ default: () => [] }) allName!: any;
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => [] }) nameOptions!: any;
  @PropSync('data', { default: () => [] }) formData!: any;
  @PropSync('usedModuleList', { default: () => [] }) moduleList!: any;
  @PropSync('modeGrouplList', { default: () => [] }) grouplList!: any;
  @Ref('formRef') readonly form!: ConfigItem;

  /* 添加模式组 */
  addNewModeGroup() {
    this.formData.push({
      groupCepPatternConditionList: [
        {
          index: 1,
          modelOrGroupName: '',
          circularRuleTimes: '',
          excessRuleTimesList: [],
          leftInterval: 0,
          rightInterval: 0,
          connectionModeType: 'NULL'
        }
      ],
      groupName: '',
      logicalRelationship: ''
    });
  }
  validate() {
    if (!this.form) return Promise.resolve(true);
    return Promise.all((this.form as any).map((el) => el.validate()));
  }
}
</script>

<style lang="scss" scoped>
.group {
  &__container {
    &__main {
      width: 100%;
    }
  }
}
</style>
