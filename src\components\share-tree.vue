<template>
  <bs-dialog title="分享" width="652px" :visible.sync="visible" :before-close="closeDialog">
    <bs-transfer
      :value="value"
      :data="treeData"
      :props="props"
      :titles="titles"
      :filterable="filterable"
      :check-strictly="true"
      check-change
      @change="handleChange"
    />
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="submit()">
        确定({{ checkId.length }})
      </el-button>
    </span>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import {
  URL_TABLE_SHARESYSORG,
  URL_TABLE_SHARESYSORGPUT,
  URL_UDF_SHARESYS,
  URL_UDF_UPDATESQLUDF,
  URL_VIEW_SHARESYSORG,
  URL_VIEW_SHARESYSORGPUT
} from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';

@Component
export default class ShareTree extends PaBase {
  @Prop({ default: false, type: Boolean }) visible!: boolean;
  @Prop() data!: any;
  value: any[] = [];
  titles: any[] = ['可选机构', '已选机构'];
  filterable: any[] = [true, false];

  treeData: any[] = [];
  tableRecord!: any;
  props: any = {
    label: 'name',
    key: 'id'
  };
  loading = false;
  treeLoading = false;

  created() {
    this.loadData(this.data);
  }

  /**
   * 加载数据
   */
  loadData(data: any) {
    this.tableRecord = data;
    // this.getOrgs(data);
    this.treeLoading = true;
    this.getOrgs(data).then((values) => {
      this.treeData = this.getTreeData(
        // data.orgPath.split('.').length,
        0,
        values.data
      );
      this.treeLoading = false;
    });
  }

  checkId: any[] = [];

  handleChange(data) {
    this.checkId = data;
  }

  submit() {
    this.loading = true;
    let api = '';
    if (this.data.tableName) {
      api = URL_TABLE_SHARESYSORGPUT + '?resId=' + this.data.id;
    } else if (this.data.viewName) {
      api = URL_VIEW_SHARESYSORGPUT + '?resId=' + this.data.id;
    } else {
      api = URL_UDF_UPDATESQLUDF + '?resId=' + this.data.id;
    }
    const { checkId } = this;
    this.doPut(api, checkId).then((resp: any) => {
      this.parseResp(resp);
    });
  }

  /**
   * 解析返回结果
   */
  private parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.closeDialog();
    });
  }

  // 处理树数据
  getTreeData(currentLevel: number, data) {
    const treeData: any[] = [];
    const maps = new Map();
    // const notData = [];
    // const notUseOrgIds = notData.map((e: any) => e.notUseOrgId);
    data.forEach((item: any) => {
      item.children = [];

      // item.checked = !notUseOrgIds.includes(item.id);
      // item.parentId判断是否为当前机构
      if (item.shareStatus === 'YES' && item.parentId) {
        this.value.push(item.id);
      }
      // if (item.shareType !== 'YES') {
      //   item.disabled = true;
      // }
      if (item.level === currentLevel) {
        treeData.push(item);
      }
      if (!maps.has(item.parentId)) {
        maps.set(item.parentId, [item]);
      } else {
        maps.set(item.parentId, maps.get(item.parentId).concat(item));
      }
    });
    const findNodes = (datas: any[]) => {
      datas.forEach((item) => {
        item.children = maps.get(item.id) || [];
        return findNodes(item.children);
      });
    };
    this.checkId = [...this.value];
    findNodes(treeData);
    return treeData;
  }

  public getOrgs(data: any) {
    let api = '';
    if (data.udfName) {
      api = URL_UDF_SHARESYS;
    } else if (data.tableName) {
      api = URL_TABLE_SHARESYSORG;
    } else {
      api = URL_VIEW_SHARESYSORG;
    }
    return this.doGet(api, {
      params: { resId: data.id }
    });
  }

  private closeDialog() {
    this.loading = false;
    this.$emit('update:visible', false);
  }
}
</script>
<style lang="scss" scoped>
$height: 485px;
::v-deep .el-dialog__body {
  max-height: $height;
  height: $height;
  overflow: hidden;
}
.marR8 {
  margin-right: 8px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
}

.custom-tree-node-label {
  text-align: left;
  flex: 1;
}

.custom-tree-node-label.disabled {
  color: #ddd;
}

.custom-tree-node-icon {
  font-size: 18px;
}
</style>
