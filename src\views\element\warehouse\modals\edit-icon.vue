<template>
  <bs-dialog
    :class="isEn ? 'edit-icon__us' : 'edit-icon'"
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
  >
    <el-form>
      <div style="display: flex">
        <div :style="{ width: isEn ? '85%' : '60%' }">
          <el-form-item :label="$t('pa.iconFile')" style="text-align: left" required>
            <el-upload
              ref="upload"
              action
              :http-request="handleFile"
              :multiple="false"
              :limit="1"
              :on-exceed="handleExceed"
              :before-upload="beforeUpload"
              :file-list="formData.fileList"
              accept=".png,.svg"
            >
              <el-button size="small" type="primary">{{ $t('pa.choseFile') }}</el-button>
              <div slot="tip" class="el-upload__tip">{{ $t('pa.tip.onlyPngSvg20KB') }}</div>
            </el-upload>
          </el-form-item>
        </div>
        <div>
          <img :src="imgBase64" style="max-width: 60px; max-height: 60px" />
        </div>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">{{ $t('pa.action.close') }}</el-button>
      <el-button type="primary" :loading="loading" @click="upload">{{ $t('pa.action.upload') }}</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Watch, Emit } from 'vue-property-decorator';
import { URL_COMPONENT_UPDATE_ICON, URL_COMPONENT_FIND_BY_ID } from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
import { get, put } from '@/apis/utils/net';
import i18n from '@/i18n';

@Component
export default class EditIcon extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: i18n.t('pa.action.editIcon') }) title!: string;
  @Prop({ default: {} }) data!: any;

  formData: any = {};
  action = '';
  imgBase64 = '';
  loading = false;

  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  private closeDialog(needFresh: boolean) {
    this.formData = {};
    this.action = '';
    this.imgBase64 = '';
  }

  upload() {
    if (this.formData.fileList === undefined || this.formData.fileList.length === 0) {
      this.$message.warning(this.$t('pa.tip.selectFile'));
      return;
    }
    const config = {
      timeout: 1500000,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    };
    const submitData = new FormData();
    submitData.append('id', this.data.id);
    submitData.append('file', this.formData.fileList[0]);
    this.loading = true;
    put(URL_COMPONENT_UPDATE_ICON, submitData, config).then((resp: any) => {
      this.parseResp(resp);
    });
  }

  beforeUpload(file) {
    const name = file.name;
    const extension = name.split('.').pop(); // 获取文件名的最后一部分，即扩展名
    const isPngOrSvg = ['png', 'svg'].includes(extension);
    const isLt20K = file.size / 1024 < 20;
    if (!isPngOrSvg) {
      this.$message.error(this.$t('pa.tip.onlyPngSvg'));
    }
    if (!isLt20K) {
      this.$message.error(this.$t('pa.tip.size20KB'));
    }
    return isPngOrSvg && isLt20K;
  }

  /**
   * 解析返回结果
   */
  private parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.imgBase64 = resp.data;
    });
  }

  private handleFile(file: any) {
    this.formData.fileList = [];
    this.formData.fileList.push(file.file);
  }
  /**
   * 判断上传文件个数
   */
  private handleExceed(files: any) {
    this.$message.warning(this.$t('pa.tip.fileCount', [files.length]));
  }

  @Watch('visible')
  onVisibleChange(val) {
    if (val) {
      get(URL_COMPONENT_FIND_BY_ID, {
        id: this.data.id
      }).then((resp: any) => {
        this.parseResponse(resp, () => {
          this.imgBase64 = resp.data.iconBase64;
        });
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-icon {
  &__us {
    ::v-deep .el-dialog__body {
      padding: 18px 15px;
    }
  }
}
</style>
