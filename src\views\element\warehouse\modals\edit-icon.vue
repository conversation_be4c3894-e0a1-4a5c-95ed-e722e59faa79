<template>
  <bs-dialog
    :title="title"
    :visible.sync="visible"
    width="30%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
  >
    <el-form>
      <div style="display: flex">
        <div style="width: 60%">
          <el-form-item label="图标文件" style="text-align: left" required>
            <el-upload
              ref="upload"
              action
              :http-request="handleFile"
              :multiple="false"
              :limit="1"
              :on-exceed="handleExceed"
              :before-upload="beforeUpload"
              :file-list="formData.fileList"
              accept=".png"
            >
              <el-button size="small" type="primary">选择文件</el-button>
              <div slot="tip" class="el-upload__tip">只能上传png文件，且不超过20kb</div>
            </el-upload>
          </el-form-item>
        </div>
        <div>
          <img :src="imgBase64" style="max-width: 60px; max-height: 60px" />
        </div>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" :loading="loading" @click="upload">上 传</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { URL_COMPONENT_UPDATE_ICON, URL_COMPONENT_FIND_BY_ID } from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';

@Component
export default class EditIcon extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: '编辑图标' }) title!: string;
  @Prop({ default: {} }) data!: any;

  formData: any = {};
  action = '';
  imgBase64 = '';
  loading = false;

  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  private closeDialog(needFresh: boolean) {
    this.formData = {};
    this.action = '';
    this.imgBase64 = '';
  }

  upload() {
    if (this.formData.fileList === undefined || this.formData.fileList.length === 0) {
      this.$message.warning('请选择文件');
      return;
    }
    Vue.axios.defaults.timeout = 1500000;
    Vue.axios.defaults.headers.post['Content-Type'] = 'multipart/form-data';
    const submitData = new FormData();
    submitData.append('id', this.data.id);
    submitData.append('file', this.formData.fileList[0]);
    this.loading = true;
    this.doPut(URL_COMPONENT_UPDATE_ICON, submitData).then((resp: any) => {
      this.parseResp(resp);
    });
  }

  beforeUpload(file) {
    const name = file.name;
    const isPng = name.indexOf('.png') >= 0;
    const isLt20K = file.size / 1024 < 20;
    if (!isPng) {
      this.$message.error('只能上传png类型的文件!');
    }
    if (!isLt20K) {
      this.$message.error('不能上传超过20KB的文件!');
    }
    return isPng && isLt20K;
  }

  /**
   * 解析返回结果
   */
  private parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.imgBase64 = resp.data;
    });
  }

  private handleFile(file: any) {
    this.formData.fileList = [];
    this.formData.fileList.push(file.file);
  }
  /**
   * 判断上传文件个数
   */
  private handleExceed(files: any) {
    this.$message.warning(`最多上传 ${files.length} 个文件`);
  }

  @Watch('visible')
  onVisibleChange(val) {
    if (val) {
      this.doGet(URL_COMPONENT_FIND_BY_ID, {
        params: {
          id: this.data.id
        }
      }).then((resp: any) => {
        this.parseResponse(resp, () => {
          this.imgBase64 = resp.data.iconBase64;
        });
      });
    }
  }
}
</script>

<style scoped></style>
