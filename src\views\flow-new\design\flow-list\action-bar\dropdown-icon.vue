<template>
  <span :class="['dropdown-icon', disabled ? 'is-disabled' : '']">
    <el-dropdown placement="bottom-start" @command="$emit('click', $event)">
      <i class="action-bar__icon" :class="icon"></i>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="item in innerOptions" :key="item.command" :command="item.command" :disabled="disabled">
          {{ item.text }}
          <el-tooltip v-if="item.content" effect="light" placement="top" :content="item.content">
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </span>
</template>

<script lang="ts">
import { hasPermission } from '@/utils';
import { Component, Vue, Prop } from 'vue-property-decorator';
@Component
export default class DropdownIcon extends Vue {
  @Prop() icon!: string;
  @Prop() options!: any[];
  @Prop() disabled!: boolean;
  get innerOptions() {
    return this.options.filter((e) => !e.access || hasPermission(e.access));
  }
}
</script>

<style lang="scss" scoped>
.dropdown-icon {
  margin-right: 20px;
  font-size: 16px;
  cursor: pointer;
  color: #757d86;
  &.is-disabled > i {
    color: #c8c8c8;
    cursor: not-allowed;
  }
}
</style>
