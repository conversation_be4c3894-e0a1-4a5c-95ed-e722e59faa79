#!/bin/bash
set -E<PERSON>u pipefail
set +x && . ${NVM_DIR}/nvm.sh && nvm use 16 && set -x
echo "Node.js version: $(node -v)"
echo "NPM version: $(npm -v)"
npm install
npm run build #生成pa的静态资源
mkdir -p ./dist/static/micro-apps/portal ./dist/static/micro-apps/assets #创建微应用的静态资源目录(assets、portal)

#处理portal
portalBranch=feature/3.26.1 #portal默认分支，可支持外部修改
rm -rf portal
git clone git@***********:ui/portal.git
cd portal/bsfit-portal-front
git checkout ${portalBranch}
git pull
npm install
npm run build:wujie
cp -rf ./dist/static/* ../../dist/static/micro-apps/portal && echo "portal copy done!"
rm -rf portal

#处理assets
assetsBranch=v1.17.0 #assets默认分支，可支持外部修改
cd ../../
rm -rf bsfit-assets-ui
git clone git@***********:assets-group/bsfit-assets-ui.git
cd bsfit-assets-ui
git checkout ${assetsBranch}
git pull
npm install
npm run build
cp -rf ./dist/* ../dist/static/micro-apps/assets && echo "assets copy done!"

# maven打包
cd ../
mvn clean deploy

