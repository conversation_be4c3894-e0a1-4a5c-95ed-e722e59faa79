<template>
  <el-dialog
    v-loading="loading"
    width="1120px"
    :title="$t('pa.resourceConfig')"
    append-to-body
    class="batch-online-dialog"
    :visible.sync="display"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :element-loading-text="loadingText"
  >
    <div class="batch-online-content">
      <!-- 流程列表 -->
      <div class="batch-online-flow__out">
        <div class="batch-online-flow__head">{{ $t('pa.flowList') }}</div>
        <ul class="batch-online-flow">
          <li
            v-for="(el, index) in flowList"
            :key="el.id"
            class="batch-online-flow__item"
            :class="{
              'batch-online-flow__item--highLight': activeId === el.id
            }"
            @click="handleFlowClick(el.id, index)"
          >
            <span class="text" :title="el.jobName">{{ el.jobName }}</span>
            <el-tag v-if="batchConfig[el.id].isComplete" size="mini" type="success">{{ $t('pa.home.text12') }}</el-tag>
          </li>
        </ul>
      </div>
      <!-- 配置表单 -->
      <div class="batch-online-config">
        <config-form ref="configRef" :key="`${activeId}${Date.now}`" :data="form" />
      </div>
    </div>
    <!-- 底部 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="display = false">{{ $t('pa.action.cancel') }}</el-button>
      <el-button type="primary" :disabled="disableSync" @click="syncConfig()">{{ $t('pa.syncSetting') }}</el-button>
      <el-button type="primary" :disabled="!canEditFlowList.length" @click="submit(false)">
        {{ $t('pa.action.save') }}
      </el-button>
      <el-button type="primary" :disabled="!canEditFlowList.length" @click="submit(true)">
        {{ $t('pa.action.online') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { Vue, Component, Prop, PropSync, Ref, Watch } from 'vue-property-decorator';
import { cloneDeep, throttle } from 'lodash';
import { onlineFlow, preOnlineFlow, updateResourceConfig } from '@/apis/flowNewApi';
import { FLOW_DEFAULT_CONFIG } from './config';

const MAX_NUM = 3; //  最多可展示的流程数量，超出显示【等】
@Component({
  components: {
    ConfigForm: () => import('./components/index.vue')
  }
})
export default class BatchResourceConfig extends Vue {
  @Prop({ type: Array, default: () => [] }) list!: any[];
  @Prop({ type: Boolean, default: false }) status!: boolean; // 无状态启动false&基于上次状态启动true
  @PropSync('show', { type: Boolean, default: true }) display!: boolean;
  @Ref('configRef') readonly configRef!: any;
  private flowList: any[] = [];
  private activeId = '';
  private batchConfig = {}; // flowId和配置的映射关系

  private loading = false;
  private loadingText = this.$t('pa.checkLoading');
  private submit = throttle(this.handleSubmit, 1200);

  get form() {
    return this.batchConfig[this.activeId];
  }

  get disabled() {
    return ['INPROD', 'PROD'].includes(this.form.jobStatus);
  }

  // 可以编辑的流程
  get canEditFlowList() {
    return this.flowList.filter(({ jobStatus }) => !['INPROD', 'PROD'].includes(jobStatus));
  }

  // 同步配置按钮置灰：只批量操作一个流程or没有可编辑的流程
  get disableSync() {
    return this.flowList.length === 1 || !this.canEditFlowList.length;
  }

  @Watch('list', { immediate: true })
  handleListChange(val: any[]) {
    if (val.length < 1) {
      return;
    }
    this.flowList = cloneDeep(val);
    this.flowList.forEach(
      ({ id, properties, jobName, jobType, memo, orgId, jobStatus, projectId, originalJobName, prefix, suffix }: any) => {
        this.$set(this.batchConfig, id, {
          ...FLOW_DEFAULT_CONFIG(),
          ...{
            ...JSON.parse(properties || '{}'),
            id,
            projectId,
            jobName,
            jobType,
            memo,
            orgId,
            originalJobName,
            prefix,
            suffix,
            isComplete: !!properties,
            jobStatus
          }
        });
      }
    );
    this.activeId = this.flowList[0].id;
  }

  async handleFlowClick(id: string) {
    await this.updateCurFlowConfig();
    this.activeId = id;
  }

  async updateCurFlowConfig() {
    const res = await this.configRef.getFormData(true);
    res.isComplete = true;
    this.$set(this.batchConfig, this.activeId, res);
  }

  // 同步配置
  async syncConfig() {
    await this.updateCurFlowConfig();
    const tipMsg = this.getTipMsg();
    try {
      await this.$confirm(tipMsg, this.$t('pa.prompt'), { customClass: 'sync-config-tip' });
      await this.updateJobConfigs();
      this.$tip.success(this.$t('pa.syncSuccess'));
    } catch (e) {
      console.log(e);
    }
  }
  // 获取点击【同步配置】后的提示信息
  getTipMsg() {
    const getNamesStr = (arr: any[] = [], suffix = '') => {
      let namesStr = '';
      const arrLength = arr.length;
      if (!arrLength) return namesStr;
      if (arrLength <= MAX_NUM) return (namesStr = this.$t('pa.nameTip2', [arr.join('、'), arrLength, suffix]));
      return (namesStr = this.$t('pa.nameTip', [arr.slice(0, MAX_NUM).join('、'), arrLength, suffix]));
    };

    // 可以进行同步配置的流程：过滤掉当前流程
    const allJobArrExceptCur = this.flowList.filter(({ id }) => id !== this.activeId);

    const allJobNameArr = allJobArrExceptCur.map(({ jobName }) => `【${jobName}】`);
    const allJobNamesStr = getNamesStr(allJobNameArr);

    // 不能同步的流程名称数组：上线中和已上线的流程不可更改资源配置
    const notSyncJobNameArr = allJobArrExceptCur
      .filter(({ jobStatus }) => ['INPROD', 'PROD'].includes(jobStatus))
      .map(({ jobName }) => `【${jobName}】`);
    const notSyncJobNames = getNamesStr(notSyncJobNameArr, this.$t('pa.canNotSync'));

    // 可以进行同步配置的流程
    const canSyncJobs = allJobArrExceptCur.filter(({ jobStatus }) => !['INPROD', 'PROD'].includes(jobStatus));

    // 可以进行更新且可以更新模式类型的流程名称合集
    const canUpdateModeJobNamaArr = canSyncJobs
      .filter(({ jobType }) => jobType === 'FLINK_SQL')
      .map(({ jobName }) => `【${jobName}】`);
    const canUpdateModeJobNames = getNamesStr(canUpdateModeJobNamaArr, this.$t('pa.updateModeJobName'));

    // 可以进行更新且不可以更新模式类型的流程名称合集
    const canNotUpdateModeJobNameArr = canSyncJobs
      .filter(({ jobType }) => jobType !== 'FLINK_SQL')
      .map(({ jobName }) => `【${jobName}】`);
    const canNotUpdateModeJobNames = getNamesStr(canNotUpdateModeJobNameArr, this.$t('pa.notUpdateModeJobName'));

    const tipMsg = this.$t('pa.syncMig', [
      this.form.jobName,
      allJobNamesStr,
      notSyncJobNames,
      canUpdateModeJobNames,
      canNotUpdateModeJobNames
    ]);
    return tipMsg;
  }

  updateJobConfigs() {
    this.canEditFlowList.forEach(({ id }) => {
      //非SQL类型的流程-模式不可更新，因为非SQL类型的流程模式只能是【流模式-stream】
      const { id: jobId, jobName, jobType, memo, orgId, mode, originalJobName, prefix, suffix } = this.batchConfig[id];
      const res = {
        ...this.form,
        ...{
          id: jobId,
          jobName,
          jobType,
          memo,
          originalJobName,
          prefix,
          suffix,
          orgId,
          mode: jobType === 'FLINK_SQL' ? this.form.mode : mode
        }
      };
      this.$set(this.batchConfig, id, res);
    });
  }

  async handleSubmit(needRun = false) {
    try {
      await this.updateCurFlowConfig();
      this.loading = true;
      this.loadingText = this.$t('pa.checkLoading');
      await this.verifyFlow(this.canEditFlowList.map(({ id }) => id));
      this.loadingText = this.$t('pa.handleLoading', [
        needRun ? this.$t('pa.monitor.warningRule.detail.handle') : this.$t('pa.action.save')
      ]);
      await this.save(needRun);
      this.loading = false;
    } catch (e) {
      this.loading = false;
    }
  }

  /* 流程批量检测 */
  async verifyFlow(list: any[], index = 0) {
    try {
      this.activeId = list[index];
      await this.sleep(500);
      const res = await this.configRef.getFormData(true);
      this.batchConfig[this.activeId] = cloneDeep(res);
      const count = index + 1;
      return count < list.length ? this.verifyFlow(list, count) : Promise.resolve(true);
    } catch (e) {
      this.activeId = list[index];
      return Promise.reject(e);
    }
  }

  sleep(time: number) {
    return new Promise((resolve) => {
      const timer = setTimeout(() => {
        clearTimeout(timer);
        resolve(true);
      }, time);
    });
  }

  /* 数据保存 */
  async save(needRun = false) {
    try {
      const params = this.canEditFlowList.map(({ id, projectId }) => {
        const temp = cloneDeep(this.batchConfig[id]);
        // 删除无数据权限时，前端拼接的clusterDetail参数
        temp.clusterDetail && delete temp.clusterDetail;
        delete temp.property;
        delete temp.isComplete;
        const { jobName, memo, originalJobName, suffix, prefix, log4jFile } = temp;
        const properties = JSON.stringify(temp);
        return {
          pajob: new Blob([JSON.stringify({ projectId, id, jobName, memo, properties, originalJobName, suffix, prefix })], {
            type: 'application/json'
          }),
          log4jFile: typeof log4jFile === 'string' ? '' : log4jFile
        };
      });
      const results = await Promise.all(
        Array.from({ length: this.canEditFlowList.length }, (v, i) => updateResourceConfig(params[i]))
      );
      const success = results.every((e) => e.success);
      if (success) {
        if (needRun) {
          await this.online();
          return;
        }
        this.$tip.success(this.$t('pa.tip.updateSuccess'));
        this.loading = false;
        this.closeDialog();
        return;
      }
      this.loading = false;
      this.$tip.error(results.map((i) => (i.success ? '' : i.msg)).join('\n'));
    } catch (e) {
      this.loading = false;
    }
  }

  /* 上线 */
  async online() {
    try {
      const flowIds = this.canEditFlowList.map(({ id }) => id);
      const res = await preOnlineFlow(flowIds);
      if (res.success) {
        const params = flowIds.map((id) => {
          return { id, fromLastCheckpoint: this.status };
        });
        const { success, msg, data, msgType } = await onlineFlow(params);
        if (success) {
          this.loading = false;
          this.$tip.success(msg);
          this.closeDialog();
          return;
        }
        this.loading = false;
        this.$tip.errorPro({ msgType, msg, data });
        this.closeDialog(data);
        return;
      }
      this.closeDialog();
      this.loading = false;
      this.$tip.errorPro(res);
    } catch (e) {
      this.loading = false;
    }
  }

  closeDialog(data?) {
    this.$emit('close', data);
    this.display = false;
  }
}
</script>

<style lang="scss" scoped>
.batch-online {
  /* 弹窗 */
  &-dialog {
    top: 50%;
    left: 50%;
    right: unset;
    bottom: unset;
    transform: translate(-50%, -50%);

    ::v-deep .el-dialog {
      margin: auto !important;

      &__body {
        padding: 0 10px;
      }
    }
  }

  /* 内容 */
  &-content {
    display: flex;
    justify-content: space-between;
    width: 1100px;
    height: 450px;
  }

  /* 流程 */
  &-flow__out {
    width: 260px;
    height: 100%;
    border-right: 1px solid #f1f1f1;
    box-sizing: border-box;
  }

  &-flow__head {
    height: 40px;
    line-height: 40px;
    padding-left: 20px;
    border-bottom: 1px solid #f1f1f1;
  }

  &-flow {
    padding: 10px;
    height: calc(100% - 40px);
    overflow-y: auto;
    overflow-x: hidden;

    &__item {
      display: flex;
      align-items: center;
      height: 40px;
      line-height: 40px;
      box-sizing: border-box;
      cursor: pointer;
      border-radius: 5px;
      text-align: left;
      padding: 0 10px 0 16px;

      & > .text {
        flex: 1;
        margin-right: 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &--highLight {
        color: #409eff !important;
        font-weight: normal !important;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }

      &--finish {
        color: #54c958;
        font-weight: bold;
      }
    }
  }

  /* 配置 */
  &-config {
    width: 1030px;
    height: 100%;
    padding-right: 10px;
    overflow: auto;
  }
}
</style>
<style lang="scss">
.el-message-box.sync-config-tip .el-message-box__message p {
  word-break: break-all;
}
</style>
