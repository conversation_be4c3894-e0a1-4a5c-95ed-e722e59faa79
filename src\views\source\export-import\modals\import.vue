<template>
  <bs-dialog
    title="导入"
    :visible.sync="visible"
    width="70%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
  >
    <header style="height: 40px; padding: 0 10px">
      <div class="operate-box">
        <el-input
          v-model="search"
          clearable
          placeholder="输入关键字搜索"
          style="width: 150px; margin-right: 10px"
        />
        <el-button type="primary" @click="setOperType(1)">覆盖</el-button>
        <el-button type="primary" @click="setOperType(0)">忽略</el-button>
      </div>
    </header>
    <div class="content" style="margin: 10px 0px">
      <el-tabs v-model="activeName">
        <el-tab-pane label="流程" :name="getTabType(0)">
          <el-table
            :height="450"
            :data="
              data.job.filter(
                (data) =>
                  !search ||
                  data.name.toLowerCase().includes(search.toLowerCase()) ||
                  data.project.toLowerCase().includes(search.toLowerCase())
              )
            "
            style="width: 100%"
            size="mini"
          >
            <el-table-column prop="project" label="项目" />
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="orgName" label="机构" />
            <el-table-column prop="createdBy" label="创建人" />
            <el-table-column prop="exist" label="存在">
              <template slot-scope="scope">
                <el-tag :type="scope.row.exist ? 'warning' : 'success'" disable-transitions>
                  {{ scope.row.exist ? '存在' : '不存在' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="300">
              <template slot-scope="scope">
                <el-radio-group v-model="scope.row.operType">
                  <el-radio-button :label="1">覆盖</el-radio-button>
                  <el-radio-button :label="0">忽略</el-radio-button>
                </el-radio-group>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane v-if="!isFuseMode" label="数据定义、方法、第三方类库" :name="getTabType(1)">
          <el-table
            :height="450"
            :data="
              data.asset.filter(
                (data) => !search || data.name.toLowerCase().includes(search.toLowerCase())
              )
            "
            style="width: 100%"
            size="mini"
          >
            <el-table-column prop="id" label="名称" />
            <el-table-column prop="type" label="类型" />
            <el-table-column prop="exist" label="存在">
              <template slot-scope="scope">
                <el-tag
                  :type="scope.row.dealType === 'SKIP' ? 'warning' : 'success'"
                  disable-transitions
                >
                  {{ scope.row.dealType === 'SKIP' ? '存在' : '不存在' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane v-if="enableSql" label="表" :name="getTabType(2)">
          <el-table
            :height="450"
            :data="
              data.table.filter(
                (data) => !search || data.name.toLowerCase().includes(search.toLowerCase())
              )
            "
            style="width: 100%"
            size="mini"
          >
            <el-table-column prop="name" label="表名称" />
            <el-table-column prop="nameCn" label="中文名称" />
            <el-table-column prop="orgName" label="机构" />
            <el-table-column prop="createdBy" label="创建人" />
            <el-table-column prop="exist" label="存在">
              <template slot-scope="scope">
                <el-tooltip
                  v-show="scope.row.exist"
                  content="已存在数据不支持导入"
                  placement="top"
                  effect="light"
                >
                  <el-tag type="warning" disable-transitions> 存在 </el-tag>
                </el-tooltip>
                <el-tag v-show="!scope.row.exist" type="success" disable-transitions>
                  不存在
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane v-if="enableSql" label="视图" :name="getTabType(3)">
          <el-table
            :height="450"
            :data="
              data.view.filter(
                (data) => !search || data.name.toLowerCase().includes(search.toLowerCase())
              )
            "
            style="width: 100%"
            size="mini"
          >
            <el-table-column prop="name" label="视图名" />
            <el-table-column prop="nameCn" label="中文名称" />
            <el-table-column prop="orgName" label="机构" />
            <el-table-column prop="createdBy" label="创建人" />
            <el-table-column prop="exist" label="存在">
              <template slot-scope="scope">
                <el-tooltip
                  v-show="scope.row.exist"
                  content="已存在数据不支持导入"
                  placement="top"
                  effect="light"
                >
                  <el-tag type="warning" disable-transitions> 存在 </el-tag>
                </el-tooltip>
                <el-tag v-show="!scope.row.exist" type="success" disable-transitions>
                  不存在
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane v-if="enableSql" label="选项" :name="getTabType(4)">
          <el-table
            :height="450"
            :data="
              data.item.filter(
                (data) => !search || data.name.toLowerCase().includes(search.toLowerCase())
              )
            "
            style="width: 100%"
            size="mini"
          >
            <el-table-column prop="name" label="选项名" />
            <el-table-column prop="type" label="选项类型" />
            <el-table-column prop="orgName" label="机构" />
            <el-table-column prop="createdBy" label="创建人" />
            <el-table-column prop="exist" label="存在">
              <template slot-scope="scope">
                <el-tooltip
                  v-show="scope.row.exist"
                  content="已存在数据不支持导入"
                  placement="top"
                  effect="light"
                >
                  <el-tag type="warning" disable-transitions> 存在 </el-tag>
                </el-tooltip>
                <el-tag v-show="!scope.row.exist" type="success" disable-transitions>
                  不存在
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" :loading="loading" @click="submit">导 入</el-button>
    </span>
    <Result :visible="showResult" :data="resultData" @close="closeResult" />
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Emit, Inject, InjectReactive } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { URL_EXPIMP_IMPORTSUBMIT } from '@/apis/commonApi';
enum TabType {
  job,
  asset,
  table,
  view,
  item
}
@Component({
  components: {
    Result: () => import('./result.vue')
  }
})
export default class Import extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: {} }) data: any;
  @Inject('enableSql') enableSql;
  @InjectReactive('isFuseMode') isFuseMode!: boolean;
  showResult = false;
  resultData: any = {};
  search = '';
  loading = false;
  activeName: string = TabType[0];

  getTabType(index) {
    return TabType[index];
  }

  submit() {
    this.loading = true;
    this.doPost(URL_EXPIMP_IMPORTSUBMIT, this.data).then((resp: any) => {
      this.loading = false;
      const msg = resp.msg.toString();
      if (resp.success) {
        if (resp.data.failedNumber === 0) {
          this.$tip.success('本次成功导入' + resp.data.successNumber + '个数据');
          this.closeDialog();
        } else {
          this.resultData = resp.data;
          this.showResult = true;
        }
      } else {
        if (msg !== '') {
          if (msg === '系统发生空指针异常，请联系管理员处理') {
            this.$message.error('请选择覆盖或忽略操作');
          } else {
            this.$message.error(msg);
          }
        }
      }
    });
  }
  closeResult() {
    this.showResult = false;
  }
  setOperType(operType) {
    if (this.activeName === 'job' || this.activeName === 'function' || this.activeName === 'jar') {
      const list = this.data[this.activeName];
      list.forEach((item) => {
        this.$set(item, 'operType', operType);
      });
    }
  }

  @Emit('close')
  private closeDialog() {
    this.activeName = TabType[0];
  }
}
</script>
<style scoped></style>
