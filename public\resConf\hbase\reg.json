{"forms": [{"type": "input", "prop": "title", "label": "名称", "componentProps": {"maxlength": 30, "placeholder": "请输入名称", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入名称", "trigger": "blur"}, {"min": 2, "max": 30, "message": "长度在 2 到 30 个字符", "trigger": "blur"}]}, {"type": "textarea", "prop": "url", "label": "服务地址", "componentProps": {"rows": 3, "maxlength": 1024, "placeholder": "请输入服务地址，多个地址以逗号分隔，兼容Ipv4和Ipv6"}, "rules": [{"required": true, "message": "请输入服务地址，多个地址以逗号分隔，兼容Ipv4和Ipv6", "trigger": "blur"}, {"validator": "validateResUrl", "trigger": "blur"}]}, {"type": "input", "prop": "ZNode", "label": "ZNode", "componentProps": {"maxlength": 100, "placeholder": "hbase-site.xml中zookeeper.znode.parent路径", "showWordLimit": true}, "rules": [{"required": true, "message": "zookeeper.znode.parent路径，例如:/hbase", "trigger": "blur"}]}, {"type": "input", "prop": "namespace", "label": "namespace", "componentProps": {"maxlength": 255, "placeholder": "请输入namespace名称", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入namespace名称", "trigger": "blur"}], "defaultVal": "default"}, {"type": "radio-group", "prop": "authN<PERSON>ed", "label": "是否鉴权", "componentProps": {"hideEl": {"no": ["user", "krb5ConfPath", "keytab<PERSON><PERSON>", "MPrincipalName", "RSPrincipal"], "yes": []}, "options": [{"label": "是", "value": "yes"}, {"label": "否", "value": "no"}], "placeholder": "请选择"}, "rules": [{"message": "请选择是否鉴权", "required": true, "trigger": "blur"}], "defaultVal": "no"}, {"type": "input", "prop": "user", "label": "user", "deps": ["authN<PERSON>ed"], "visible": "(scope) => scope.authNeeded === 'yes'", "componentProps": {"maxlength": 30, "placeholder": "请输入user", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入user", "trigger": "blur"}]}, {"type": "input", "prop": "krb5ConfPath", "label": "krb5.conf地址", "deps": ["authN<PERSON>ed"], "visible": "(scope) => scope.authNeeded === 'yes'", "componentProps": {"maxlength": 255, "placeholder": "请输入krb5.conf地址", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入krb5.conf地址", "trigger": "blur"}]}, {"type": "input", "prop": "keytab<PERSON><PERSON>", "label": "keytab地址", "deps": ["authN<PERSON>ed"], "visible": "(scope) => scope.authNeeded === 'yes'", "componentProps": {"maxlength": 256, "placeholder": "请输入keytab地址", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入keytab地址", "trigger": "blur"}]}, {"type": "input", "prop": "MPrincipalName", "label": "MPrincipalName", "deps": ["authN<PERSON>ed"], "visible": "(scope) => scope.authNeeded === 'yes'", "componentProps": {"maxlength": 255, "placeholder": "请输入master principal<PERSON><PERSON>", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入master principal<PERSON><PERSON>", "trigger": "blur"}]}, {"type": "input", "prop": "RSPrincipal", "label": "RSPrincipal", "deps": ["authN<PERSON>ed"], "visible": "(scope) => scope.authNeeded === 'yes'", "componentProps": {"maxlength": 255, "placeholder": "请输入regionserver principalName", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入regionserver principalName", "trigger": "blur"}]}, {"type": "textarea", "prop": "memo", "label": "备注", "componentProps": {"maxlength": 255, "placeholder": "请输入备注", "rows": 5}}]}