import { cloneDeep, isObject } from 'lodash';
// import { filterOutputFields } from './field-validate-service';
import { OriginNode, DagNode, OriginEdge, DagEdge } from './interface';
import { getDagPort, getPort } from './port-service';

class CanvasStore {
  dagNodes: DagNode[] = [];
  dagEdges: DagEdge[] = [];
  nodes: OriginNode[] = [];
  edges: OriginEdge[] = [];
  disabled = false;
  private portDataMaps = new Map(); // 端点上对应的数据
  private nodeDataMaps = new Map();
  // private edgeDataMaps = new Map();
  nodeNum: number;
  constructor(data: { edges: OriginEdge[]; nodes: OriginNode[]; nodeNum: number }, disabled) {
    this.nodeNum = data.nodeNum;
    this.disabled = disabled;
    const { nodes, dageNodes } = this.convertToDagNodes(data.nodes || []);
    const { edges, dagEdges } = this.convertToDagEdges(data.edges || []);
    this.nodes = nodes;
    this.edges = edges;
    this.dagNodes = dageNodes;
    this.dagEdges = dagEdges;
  }
  convertToDag() {
    const { dagNodes, dagEdges } = this;
    return { dagNodes, dagEdges };
  }
  // 转换成dag的node数据
  convertToDagNodes(originNodes: OriginNode[]) {
    const nodes: OriginNode[] = [];
    const dageNodes: DagNode[] = [];
    originNodes.map((item) => {
      const { inPort, outPort } = getDagPort(item);
      const { pointIn, pointOut } = getPort(inPort, outPort, item, this.portDataMaps);
      const dagNode = {
        id: item.nodeId,
        nodeName: item.nodeName || item.componentName,
        // 节点状态 老数据统处理
        status: getNodeStatus(item, this.disabled),
        selected: false,
        msg: item.status === 2 && item.errorMsg ? item.errorMsg : '',
        inPort,
        outPort,
        parentNodes: [],
        position: item.position,
        parallelism: item.parallelism || 1
      };
      const originNode = Object.assign(item, { pointIn, pointOut });
      dageNodes.push(dagNode);
      nodes.push(originNode);
      this.nodeDataMaps.set(item.nodeId, originNode);
    });
    return { nodes, dageNodes };
  }
  // 转换成dag的edge数据
  convertToDagEdges(originEdges: OriginEdge[]) {
    const edges: OriginEdge[] = [];
    const dagEdges: DagEdge[] = [];
    originEdges.forEach((item) => {
      dagEdges.push({
        id: item.id || '',
        source: {
          cell: item.startNode,
          port: item.startNodePoint
        },
        target: {
          cell: item.endNode,
          port: item.endNodePoint
        },
        status: 0 // 0 正常 1 异常
      });
      edges.push(item);
    });
    return { edges, dagEdges };
  }
  // 转换成源的edge数据
  convertToOriginEdges(edges: DagEdge[]) {
    return edges.map(
      ({ id = '', target = { cell: '', port: '' }, source = { cell: '', port: '' } }) => ({
        id,
        endNode: target.cell,
        endNodePoint: target.port,
        endNodePointType: '', // 暂未用到该字段，置空
        startNode: source.cell,
        startNodePoint: source.port,
        startNodePointType: '' // 暂未用到该字段，置空
      })
    );
  }
  // 转换成源node数据
  convertToOriginNodes(nodes: DagNode[]) {
    // 原数据模式兼容
    const specialComponentDataHandling = (data) => {
      if (data.type === 'FILTER' || data.type === 'DYNAMIC_FILTER') {
        const _data = cloneDeep(data);
        if (!Array.isArray(_data.pointOut)) return data;
        data.pointOut[0] && (_data.pointOut[0].type = true);
        data.pointOut[1] && (_data.pointOut[1].type = false);
        return _data;
      } else if (data.type === 'JOIN') {
        data.inputFields = [];
        data.pointIn.forEach((item, index) => {
          // 不在其他逻辑中进行双流组件的特殊处理 在此处统一处理
          const edge = this.getEdgesByPortId(item.uuid, 'IN');
          const key = index === 0 ? 'leftStartNodeId' : 'rightStartNodeId';
          if (Array.isArray(edge) && edge[0]) {
            data[key] = edge[0].startNode;
            data.properties && (data.properties[key] = edge[0].startNode);
          }
          data.inputFields.push(
            ...item.data.map(({ name, type }) => ({ value: name, label: name, type }))
          );
        });
        return data;
      } else {
        return data;
      }
    };
    return nodes.map(({ status, position, parallelism, id, parentNodes, msg }) => ({
      ...specialComponentDataHandling(this.nodeDataMaps.get(id) || {}),
      parentNodes,
      status,
      position,
      parallelism,
      errorMsg: status === 2 ? msg : ''
    }));
  }
  convertToOrigin(nodes, edges) {
    // 此处重新从dag获取数据用于更新节点的相关信息
    return {
      edges: this.convertToOriginEdges(edges),
      nodeNum: nodes.length,
      nodes: this.convertToOriginNodes(nodes)
    };
  }
  // 更新连线数据 因为原本的连线没有自己的id
  updateEdgeData(edges: DagEdge[]) {
    if (!Array.isArray(edges)) edges = [edges];
    this.edges = this.convertToOriginEdges(edges);
  }
  addNodeData(node: DagNode, jobId: string) {
    const { pointIn, pointOut } = getPort(
      node.inPort,
      node.outPort,
      { inputFields: [], outputFields: [], type: node.options!.type },
      this.portDataMaps
    );
    const handleProperties = (properties: Base) => {
      if (!isObject(properties)) return undefined;
      // 去除组件配置的几个参数
      ['custom', 'customConf', 'forms'].forEach((key) => {
        delete properties[key];
      });
      return Object.keys(properties).length === 0 ? undefined : properties;
    };
    const {
      type = '',
      className = '',
      componentId = '',
      componentName = '',
      componentVersion,
      jarId,
      operateType,
      printLog,
      ioProperties,
      inEndPoint,
      outEndPoint,
      properties
    } = node.options || {};
    const initData: OriginNode = {
      jobId,
      jarId,
      type,
      status: 0,
      nodeId: node.id,
      nodeName: node.nodeName,
      parallelism: node.parallelism || 1,
      position: node.position,
      inputFields: [],
      outputFields: [],
      parentNodes: [],
      ioProperties,
      printLog,
      pointIn,
      pointOut,
      className,
      componentId,
      componentName,
      componentVersion,
      operateType,
      inEndPoint,
      outEndPoint,
      properties: handleProperties(properties)
    };
    this.nodes.push(initData);
    this.nodeDataMaps.set(initData.nodeId, initData);
  }
  removeNode(nodeId) {
    const idx = this.nodes.findIndex((item) => item.nodeId === nodeId);
    this.nodes.splice(idx, 1);
  }
  getNodeData(id?) {
    return id ? this.nodeDataMaps.get(id) || {} : this.nodes;
  }
  updateNodeData(id, data = {}) {
    // 优化：判断节点的配置信息是否更改
    let isChange = false;
    const updatedData = this.getNodeData(id);
    const vaildateChanged = (oldVal, newVal, needStringify = false) => {
      return needStringify ? JSON.stringify(oldVal) !== JSON.stringify(newVal) : oldVal !== newVal;
    };
    Object.keys(data).forEach((key) => {
      if (vaildateChanged(updatedData[key], data[key], isObject(updatedData[key]))) {
        isChange = true;
        if (Array.isArray(updatedData[key])) {
          updatedData[key] = data[key];
        } else if (isObject(updatedData[key])) {
          updatedData[key] = { ...data[key] };
        } else {
          updatedData[key] = data[key];
        }
      }
    });
    // 更新输出端点上的数据
    (updatedData.pointOut || []).forEach(({ uuid }) => {
      // 获取下游节点
      this.updatePortData(updatedData.nodeId, uuid, 'OUT', updatedData.outputFields);
    });
    // 返回更新完的数据
    return { updatedData, isChange };
  }
  // 获取端点上的数据
  getPortData(id) {
    return this.portDataMaps.get(id);
  }
  // 更新端点上的数据
  updatePortData(nodeId, portId, type, data) {
    const node = this.getNodeData(nodeId);
    const port = node[type === 'OUT' ? 'pointOut' : 'pointIn'].find((item) => item.uuid === portId);
    port && (port.data = data);
    this.portDataMaps.set(portId, data);
  }
  addEdge({ id, source, target }) {
    this.edges.push({
      id,
      endNode: target.cell,
      endNodePoint: target.port,
      endNodePointType: '', // 暂未用到该字段，置空
      startNode: source.cell,
      startNodePoint: source.port,
      startNodePointType: '' // 暂未用到该字段，置空
    });
  }
  // TODO:后续可以直接传递id进行查找
  delEdgeData(startNodeId, endNodeId) {
    const idx = this.edges.findIndex(
      (item) => item.startNode === startNodeId && item.endNode === endNodeId
    );
    this.edges.splice(idx, 1);
  }
  // 根据链接端点获取所有相关的连线
  getEdgesByPortId(portId, type) {
    return this.edges.filter(
      (edge) => edge[type === 'OUT' ? 'startNodePoint' : 'endNodePoint'] === portId
    );
  }
  // 更新当前节点输出端口的数据以及下游节点的输入数据及输入端点上绑定的数据
  updateOutputFieldsAndTargetInputFields(sourceNode, isFlinkSql) {
    (sourceNode.pointOut || []).forEach(({ uuid }) => {
      // 获取下游节点
      this.updatePortData(sourceNode.nodeId, uuid, 'OUT', sourceNode.outputFields);
      const connectedEdges = this.getEdgesByPortId(uuid, 'OUT');
      connectedEdges.forEach((edge) => {
        const targetNode = this.getNodeData(edge.endNode);
        if (isFlinkSql) {
          targetNode.inputFields = updateInputFieldsForSqlNode(
            targetNode.inputFields,
            sourceNode.outputFields
          );
        } else {
          targetNode.inputFields = sourceNode.outputFields.filter((i) => i.outputable);
        }
        // 更新下游输出节点上的数据
        this.updatePortData(edge.endNode, edge.endNodePoint, 'IN', targetNode.inputFields);
      });
    });
  }
}
// 对流程老数据节点状态的填补
const getNodeStatus = (item, disabled) => {
  // 已存在status字段直接返回
  if (disabled) return 1; // 画布为不可编辑状态时 说明流程不是开发状态 直接赋值为已配置状态
  if (item.status === 2) return item.status;
  // 节点配置或者未配置 通过properties进行判断
  return item.properties ? 1 : [0, 1, 2].includes(item.status) ? item.status : 0;
};

// 获取节点的当前最大数值 用于新增节点时，生成带有序号的节点名称
export const getMaxNodeCount = (nodes) => {
  const nums = nodes
    .filter((item) => /\d+/.test(item.nodeName.split('_')[1]))
    .map((item) => Number(item.nodeName.split('_')[1]));
  return nums.length ? Math.max(...nums) : 0;
};

// 是否为空的数组
export const isEmptyArray = (arr) => {
  return Array.isArray(arr) && arr.length === 0;
};

// ========================组件表单配置相关======================

// 对意外丢失的组件类型进行填充
export const getComponentType = (className) => {
  const classNameObj = {
    'cn.com.bsfit.pipeace.component.process.mapping.MappingComponent': 'FILTER',
    'cn.com.bsfit.pipeace.component.filter.EmbeddedFilterComponent': 'FILTER',
    'cn.com.bsfit.pipeace.component.process.join.PaJoinComponent': 'JOIN',
    'cn.com.bsfit.pipeace.component.process.join.PaIntervalJoinComponent': 'JOIN'
  };
  return classNameObj[className] || '';
};

// 获取组件的表单配置
export const getComponentProperties = (componentList: any[]) => {
  const propertiesMaps = {};
  componentList.forEach((item) => {
    item.paJobComponentList.forEach((component) => {
      if (component.properties && JSON.parse(component.properties)) {
        propertiesMaps[component.className] = JSON.parse(component.properties).forms;
      }
    });
  });
  return propertiesMaps;
};

// 判断表单项是否为必填
export const checkFormItemRequired = (item) => {
  // rules不存在 判断为非必填
  if (!item.rules) {
    return false;
  }
  if (Array.isArray(item.rules)) {
    return item.rules.some((rItem) => rItem.required);
  } else {
    return item.rules.required;
  }
};

// ========================FLnkSql特殊处理相关======================

// 更新输入字段
export const updateInputFieldsForSqlNode = (oldVal, newVal) => {
  if (!Array.isArray(oldVal) || !Array.isArray(newVal) || newVal.length === 0) {
    return oldVal;
  }
  const idx = (oldVal || []).findIndex((i) => i.nodeId === newVal[0].nodeId);
  if (idx === -1) {
    oldVal.push(...newVal);
  } else {
    oldVal[idx] = newVal[0];
  }
  return oldVal;
};

export default CanvasStore;
