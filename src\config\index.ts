export const baseUrl = {
  prev: process.env.NODE_ENV === 'development' ? '/api' : '.',
  loginUrl: process.env.NODE_ENV === 'development' ? '/api/login.html' : './login.html'
};

export const common: any = {
  Connector: 'Bezier',
  PaintStyle: {
    strokeWidth: 1,
    stroke: '#7F8081',
    outlineWidth: 2,
    outlineStroke: 'transparent'
  },
  HoverPaintStyle: {
    stroke: '#2196f3',
    strokeWidth: 2
  },
  EndpointHoverStyle: {
    stroke: '#2196f3',
    strokeWidth: 1,
    fill: '#2196f3'
  },

  ConnectionOverlays: [
    [
      'Arrow',
      {
        location: 1,
        visible: true,
        width: 8,
        length: 8,
        id: 'ARROW'
      }
    ]
  ]
};

export const sourceCommon = {
  isSource: true,
  anchors: ['Bottom'],
  endpointStyle: {
    strokeWidth: 1,
    stroke: '#7F8081',
    fill: '#7F8081',
    radius: 5
  },
  endpointHoverStyle: {
    strokeWidth: 5,
    stroke: '#2196f3',
    fill: '#2196f3'
  },
  maxConnections: -1
};

export const targetCommon = {
  endpointStyle: {
    strokeWidth: 1,
    stroke: '#7F8081',
    radius: 5
  },
  dropOptions: { activeClass: 'active' },
  isTarget: true,
  anchors: ['Top'],
  maxConnections: -1
};
