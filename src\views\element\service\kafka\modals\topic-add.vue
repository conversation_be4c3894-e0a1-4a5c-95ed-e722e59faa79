<template>
  <bs-dialog title="创建" :visible.sync="visible" :before-close="closeDialog">
    <el-form
      ref="ruleForm"
      v-loading="formLoading"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
      element-loading-text="数据正在加载中..."
    >
      <el-form-item label="名称" prop="topic">
        <el-input v-model="formData.topic" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item label="副本数" prop="replicationFactor">
        <el-input v-model="formData.replicationFactor" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item label="分片数" prop="numPartitions">
        <el-input v-model="formData.numPartitions" placeholder="请输入内容" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" :loading="loading" @click="submit('ruleForm')">确 定</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Watch, Emit } from 'vue-property-decorator';
import { URL_RES_DETAIL_KAFKA_CREATETOPIC } from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component
export default class TopicAdd extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: {} }) data!: any;
  @Prop({ default: false }) formLoading!: boolean;

  formData: any = {};
  loading = false;

  rules: any = {
    topic: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    replicationFactor: [{ required: true, message: '请输入副本数', trigger: 'blur' }],
    numPartitions: [{ required: true, message: '请输入分片数', trigger: 'blur' }]
  };

  @Emit('close')
  // eslint-disable-next-line no-unused-vars
  private closeDialog(needFresh: boolean) {
    this.formData = {};
    this.loading = false;
    this.formLoading = false;
  }

  submit(formName: string) {
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        this.loading = true;
        this.doGet(URL_RES_DETAIL_KAFKA_CREATETOPIC, {
          params: this.formData
        }).then((resp: any) => {
          this.loading = false;
          this.parseResponse(resp, () => {
            this.closeDialog(true);
          });
        });
      } else {
        this.$message.error('请检查输入内容');
        return false;
      }
    });
  }

  @Watch('visible')
  onVisibleChange(val) {
    if (val) {
      this.formData = _.cloneDeep(this.data);
    }
  }
}
</script>

<style scoped></style>
