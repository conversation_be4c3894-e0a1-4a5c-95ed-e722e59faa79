<template>
  <el-drawer
    v-drawerDrag="size"
    :size="size"
    :modal="false"
    :class="['drawer__container', isFullScreen ? 'drawer__container--fullScreen' : '']"
    :visible.sync="display"
    :append-to-body="false"
    :before-close="handler"
  >
    <div slot="title" class="drawer-title" :title="title">{{ title }}</div>
    <div class="drawer-main">
      <!-- body -->
      <div
        :class="{
          'drawer-main__body': true,
          'drawer-main__body--full': disabled
        }"
      >
        <slot></slot>
      </div>
      <!-- footer -->
      <div v-if="!disabled" class="drawer-main__footer">
        <el-button @click="handler">{{ $t('pa.action.cancel') }}</el-button>
        <el-button type="primary" @click="submit">{{ $t('pa.action.submit') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts">
import { Component, PropSync, Prop, Vue } from 'vue-property-decorator';
import debounce from 'lodash/debounce';

@Component
export default class FlowDrawer extends Vue {
  @PropSync('show', { default: false }) display!: boolean;
  @Prop({ default: '' }) title!: string;
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: false }) isFullScreen!: boolean;
  @Prop({ default: 400 }) size!: number;

  @Prop({}) beforeClose!: any;

  private submit = debounce(this.handerSubmit, 1200);

  handerSubmit() {
    this.$emit('submit');
  }
  async handler() {
    typeof this.beforeClose === 'function' && (await this.beforeClose());
    this.display = false;
  }
}
</script>

<style lang="scss" scoped>
.drawer {
  &__container {
    top: 95px;
    bottom: 11px;
    right: 70px;
    z-index: 1999 !important;
    &--fullScreen {
      top: 0;
      bottom: 12px;
    }
    ::v-deep .el-drawer {
      &__header {
        margin: 0;
        padding: 0 20px;
        height: 60px;
        border-bottom: 1px solid #f2f4f7;
      }
      &__body {
        padding-bottom: 20px;
        height: calc(100% - 80px);
        overflow: hidden;
      }
    }
  }
  &-title {
    width: 100%;
    font-size: 14px;
    font-weight: 500;
    color: #444444;
    line-height: 20px;
    overflow: hidden;
    word-wrap: nowrap;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &-main {
    padding: 0 20px;
    height: 100%;
    &__body {
      height: calc(100% - 48px);
      overflow-x: hidden;
      overflow-y: auto;
      &--full {
        height: 100%;
      }
    }
    &__footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;
      .el-button {
        width: 50%;
      }
    }
  }
}
</style>
