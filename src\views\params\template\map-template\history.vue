<template>
  <pro-page title="历史映射模板" :fixed-header="false" class="map-history">
    <pro-table :request="request" :columns="columnData" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { post } from '@/apis/utils/net';

@Component
export default class ElementRuleTemplate extends Vue {
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 }
  };
  columnData = [];

  get id() {
    return this.$route.query.id || '';
  }
  async request() {
    let newData = {};
    let total = 0;
    try {
      this.tableLoading = true;
      const { data, success, msg } = await post(
        `/rs/pa/mappingGroup/showHistory?mappingTemplateId=${this.id}`,
        this.searchObj
      );
      if (!success) return this.$message.error(msg);
      data.columnData.forEach((el) => {
        el.value = el.prop || el.value;
        if (el.value === 'updateTime') el.valueProps = { type: 'datetime' };
      });
      this.columnData = data.columnData;
      newData = data.tableData;
      total = data.pageData.total;
    } finally {
      this.tableLoading = false;
      return { data: newData, total };
    }
  }
}
</script>

<style lang="scss" scoped>
.rule {
  height: calc(100vh - 107px);
  &-header {
    height: 50px;
    background: #ffffff;
    border-left: none;
    padding: 0 20px;
    font-size: 16px;
    display: flex;
    align-items: center;
  }
  &-header-operate {
    flex: 1;
    text-align: right;
  }
  &-content {
    height: calc(100% - 50px);
    padding-bottom: 20px;
    overflow: hidden;
    background: #fff;
  }
}
</style>
