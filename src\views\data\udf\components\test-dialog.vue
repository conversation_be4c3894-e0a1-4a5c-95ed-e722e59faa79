<template>
  <div>
    <bs-dialog title="测试" :visible.sync="testDialogVisible" class="test-dialog" size="large">
      <div v-loading="testDialogLoading" class="test-dialog__content">
        <!-- 测试数据 -->
        <pro-grid type="info" title="测试数据" class="test-dialog__data">
          <div v-if="showFuncSelect" slot="operation" class="test-dialog__data--select">
            <el-select v-model="selectFunc" size="small" @change="handleSelectFuncChange">
              <el-option
                v-for="(item, index) in testFuncSelections"
                :key="item.label"
                :label="item.label"
                :value="index"
              />
            </el-select>
          </div>
          <!-- 测试数据-头部信息 -->
          <div slot="operation" class="test-dialog__data--header">
            <div class="test-dialog__data--icons">
              <el-button
                v-if="showCode"
                class="test-dialog__data--icon"
                icon="iconfont icon-yuandaima"
                @click="switchDataDisplay(false)"
              >
                模拟环境测试
              </el-button>
              <el-button
                v-else
                icon="iconfont icon-liebiao"
                class="test-dialog__data--icon"
                @click="switchDataDisplay(true)"
              >
                单独方法测试
              </el-button>
            </div>
            <el-button type="primary" @click="execute">执行</el-button>
          </div>
          <!-- 测试数据-主体内容 -->
          <div class="test-dialog__data--content">
            <!-- 列表展示 -->
            <bs-table
              v-if="showTestContentTable"
              height="210px"
              :column-data="testContentColumnData"
              :column-settings="false"
              show-index
              :data="testContentTableData"
              :index="indexMethod"
            >
              <template
                v-for="(item, index) in testContentTableData[0]"
                :slot="testTableData[item.uuid]"
                slot-scope="{ row, $index }"
              >
                <el-input
                  :key="item.type"
                  v-model="testContentTableData[$index][index][testTableData[item.uuid]]"
                  :placeholder="testTypeList[Object.keys(testTypeList)[index]]"
                  size="small"
                />
              </template>
              <template slot="operator" slot-scope="{ row, $index }">
                <el-tooltip content="新建" effect="light" placement="top">
                  <i class="iconfont icon-xinjianxiangmu" @click="handleAddData(row, $index)"></i>
                </el-tooltip>
                <el-tooltip content="复制" effect="light" placement="top">
                  <i class="iconfont icon-fuzhi1" @click="handleCopyData(row, $index)"></i>
                </el-tooltip>
                <el-tooltip content="删除" effect="light" placement="top">
                  <el-popconfirm title="确定删除此行？" @confirm="handleDeleteConfirm(row, $index)">
                    <i slot="reference" class="iconfont icon-shanchu"></i>
                  </el-popconfirm>
                </el-tooltip>
              </template>
            </bs-table>
            <!-- 源码展示 -->
            <el-form v-else ref="testForm" :model="testFormData" :rules="testFormRules">
              <el-form-item prop="data">
                <bs-code
                  language="sql"
                  :value="code"
                  :read-only="false"
                  :formatter="false"
                  class="test-dialog__data--code"
                  :extra-style="codeStyle"
                  @change="handleCodeChange"
                />
              </el-form-item>
            </el-form>
          </div>
        </pro-grid>
        <!-- 测试结果 -->
        <div class="test-dialog__result">
          <el-tabs v-model="activeTab">
            <el-tab-pane
              v-for="(item, index) in tabPaneMap"
              :key="item.name"
              :label="item.label"
              :name="item.name"
            >
              <pre class="test-dialog__result--pre">
                <p class="test-dialog__result--p">{{ item.result }}</p>
                <bs-table
                v-if="showTable&&index===0"
                :column-settings="false"
                :data="tableData.tableData"
                :column-data="tableData.columnData"
                height="210px"
              />
              </pre>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :disabled="testDialogLoading" @click="handleConfirm">
          确认
        </el-button>
      </div>
    </bs-dialog>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, PropSync, Watch } from 'vue-property-decorator';
import {
  URL_UDF_TEST,
  URL_UDF_PARSE_UDF_FUNC,
  URL_UDF_TEST_FUNC,
  URL_UDF_TESTSQL
} from '@/apis/commonApi';
import { now, cloneDeep, uniqueId } from 'lodash';
import { post } from '@/apis/utils/net';
import getWebConsole from '@/utils/get-web-console';
@Component
export default class TestDialog extends Vue {
  @PropSync('visible', { type: Boolean }) testDialogVisible;
  @Prop({ default: {} }) data;
  @Prop({ default: '' }) sql!: string;
  private testContentColumnData: any[] = [];
  private testContentTableData: any[] = [[]];
  private showTestContentTable = true;
  private activeTab = 'result';
  private showTable = false;
  private tabPaneMap = [
    { label: '测试结果', name: 'result', result: '' },
    { label: '执行日志', name: 'log', result: '' }
  ];
  private testResult = `测试结果`;
  private codeStyle = { height: '100%;', minHeight: '170px' };
  private showCode = false;
  private code = '';
  private confirmArr: any = [];
  private testDialogLoading = false;
  private testFormData: any = { data: '' };
  private testListData: any = { data: '' };
  private tableDataClone: any = [];
  private tableData = {
    tableData: null,
    columnData: null
  };
  private testFormRules = {
    data: [{ required: true, message: '请输入用例数据', trigger: 'blur' }]
  };
  private testColumnData = [
    {
      label: '参数1',
      value: 'param1'
    },
    {
      label: '参数2',
      value: 'param2'
    }
  ];
  private testTableData = {};
  private testTypeList = {};
  private socket: any = null;
  private testFuncSelections: any = [];
  private selectFunc = '';
  private confirmTableData: any = [];
  private currentIndex = 0;
  get showFuncSelect() {
    return this.testFuncSelections.length > 1 && this.showTestContentTable;
  }

  @Watch('sql', { immediate: true })
  handleSqlCodeChange(val) {
    this.code = val;
    this.testFormData.data = this.code;
  }

  async created() {
    this.testDialogLoading = true;
    await this.getTestList();
    this.testDialogLoading = false;
  }

  transferTestContentTableData() {
    const tableDataClone = cloneDeep(this.testContentTableData);
    this.confirmArr = [];
    tableDataClone.forEach((el) => {
      this.confirmArr.push(el);
    });
    this.confirmArr.forEach((item, index) => {
      item.forEach((element, i) => {
        for (const key in element) {
          const keySplitArr = key.split('-');
          if (keySplitArr.length > 1) {
            this.confirmArr[index][i] = {
              name: keySplitArr[0],
              type: keySplitArr[1],
              value: element[keySplitArr[0]]
            };
          }
        }
      });
    });
  }

  handleSelectFuncChange(index) {
    this.testContentColumnData = [];
    this.transferTestContentTableData();
    this.confirmTableData = this.confirmTableData.filter((el) => {
      el.methodName !== this.testFuncSelections[this.currentIndex]['value'];
    });
    this.confirmTableData.push({
      methodName: this.testFuncSelections[this.currentIndex]['value'],
      funcParamDtos: this.confirmArr
    });
    this.testContentTableData = [[]];
    this.testTypeList = {};
    this.testTableData = {};
    this.currentIndex = index;
    this.setData(this.testFuncSelections[index].data);
  }

  setData(data) {
    data.forEach((el, index) => {
      this.testContentColumnData.push({
        label: el.name,
        value: el.name,
        minWidth: '200px'
      });
      const stringType = `${el.name}-${el.type}`;
      this.testContentTableData[0].push({
        [stringType]: el.type,
        [el.name]: '',
        uuid: uniqueId()
      });
      this.testTableData[this.testContentTableData[0][index].uuid] = el.name;
      this.testTypeList[stringType] = el.type;
    });
    this.testContentColumnData.push({
      label: '操作',
      value: 'operator',
      fixed: 'right',
      width: 180
    });
  }

  handleDeleteConfirm(row, index) {
    this.handleDeleteData(row, index);
  }

  async getTestList() {
    const {
      createType,
      id,
      udfExplain,
      udfName,
      udfNameCn,
      udfNameMain,
      udfType,
      imports,
      jars,
      memo,
      otherFuncs,
      preImports,
      sourceCode
    } = this.data;
    try {
      const { success, msg, data } = await post(URL_UDF_PARSE_UDF_FUNC, {
        createType,
        id,
        udfExplain,
        udfName,
        udfNameCn,
        udfNameMain,
        udfType,
        imports,
        jars,
        memo,
        otherFuncs,
        preImports,
        sourceCode
      });
      if (success) {
        data.forEach((el) => {
          this.testFuncSelections.push({
            label: el.methodName,
            value: el.methodName,
            data: el.funcParamDtos
          });
        });
        this.selectFunc = this.testFuncSelections[0].value;
        this.setData(this.testFuncSelections[0].data);
      } else {
        this.$message.error(msg);
        this.testContentTableData = [];
      }
    } catch {
      this.testDialogLoading = false;
      this.testContentTableData = [];
    }
  }

  switchDataDisplay() {
    this.tabPaneMap[1].result = '';
    this.tabPaneMap[0].result = '';
    this.showTestContentTable = !this.showTestContentTable;
    this.showCode = !this.showCode;
  }

  handleAddData(row) {
    if (this.testContentTableData.length < 5) {
      const temp = cloneDeep(row);
      temp.forEach((el) => {
        for (const key in el) {
          if (key === 'uuid') {
            el[key] = uniqueId();
          } else if (key.split('-').length > 1) {
            el[key] = this.testTableData[key];
          } else {
            el[key] = '';
          }
        }
      });
      this.testContentTableData.push(temp);
      this.$forceUpdate();
      return;
    }
    this.$message.warning('测试数据不允许超过5个');
  }

  handleCopyData(row: any, index) {
    if (this.testContentTableData.length < 5) {
      const temp = cloneDeep(row);
      this.testContentTableData.splice(index + 1, 0, temp);
      return;
    }
    this.$message.warning('测试数据不允许超过5个');
  }

  handleDeleteData(row, index) {
    if (this.testContentTableData.length <= 1) {
      this.$message.error('必须保留一行测试数据');
      return;
    }
    this.testContentTableData.splice(index, 1);
    this.$message.success('删除成功');
  }

  handleCodeChange(value) {
    this.testFormData.data = value;
  }
  handleClose() {
    this.testDialogVisible = false;
  }
  // 执行测试,并打印测试结果和日志
  async execute() {
    this.tabPaneMap[1].result = '';
    this.transferTestContentTableData();
    if (this.showCode) {
      try {
        await (this.$refs.testForm as HTMLFormElement).validate();
      } catch {
        return;
      }
    }
    this.testResult = '';
    const userId = `key${now()}`;
    await this.initWebSocket(userId);
    const loading = this.$loading({
      lock: true,
      text: '执行中，请稍等...'
    });
    setTimeout(async () => {
      const api = this.showCode ? `${URL_UDF_TEST}` : `${URL_UDF_TEST_FUNC}`;
      const param = {
        createType: this.data.createType,
        data: this.confirmArr,
        id: this.data.id,
        memo: this.data.memo,
        otherFuncs: this.data.otherFuncs,
        sourceCode: this.data.sourceCode,
        sql: this.testFormData.data,
        tempSave: this.data.tempSave,
        udfExplain: this.data.udfExplain,
        udfName: this.data.udfName,
        udfNameCn: this.data.udfNameCn,
        udfNameMain: this.data.udfNameMain,
        udfType: this.data.udfType
      };
      try {
        const res = await post(
          `${api}?websocketKey=${userId}&userName=${this.$store.state.userInfo.userName}`,
          param
        );
        if (res.success) {
          loading.close();
          if (this.showCode) {
            this.tabPaneMap[0].result = '';
            this.tableData.tableData = res.data.result;
            this.tableData.columnData = res.data.columns;
            this.tableData.columnData = res.data.columns.map((el) => {
              return {
                label: el,
                value: el
              };
            });
            this.showTable = true;
          } else {
            this.showTable = false;
            this.tabPaneMap[0].result = '';
            res.data.forEach((el) => {
              this.tabPaneMap[0].result += el + '\n';
            });
          }
        } else {
          this.showTable = false;
          this.tabPaneMap[0].result = '';
          this.tabPaneMap[0].result = res.msg;
        }
      } catch {
        loading.close();
      }
      loading.close();
    }, 600);
  }

  getUniqueObj(arr, type) {
    const res = new Map();
    return arr.filter((a) => !res.has(a[type]) && res.set(a[type], 1));
  }

  async handleConfirm() {
    this.testDialogLoading = true;
    this.transferTestContentTableData();
    this.confirmTableData.forEach((el, index) => {
      if (el.methodName === this.testFuncSelections[this.currentIndex]['value']) {
        this.confirmTableData.splice(index, 1);
      }
    });
    this.confirmTableData.push({
      methodName:
        this.testFuncSelections.length > 0
          ? this.testFuncSelections[this.currentIndex]['value']
          : '',
      funcParamDtos: this.confirmArr
    });
    try {
      const { success, msg } = await post(`${URL_UDF_TESTSQL}`, {
        id: this.data.id,
        data: this.confirmTableData,
        sql: this.testFormData.data
      });
      if (success) {
        this.$message.success(msg);
        this.testDialogLoading = false;
      } else {
        this.$message.error(msg);
      }
      this.testDialogVisible = false;
    } catch {
      this.testDialogLoading = false;
      this.testDialogVisible = false;
    }
  }

  initWebSocket(userId: string) {
    /**
     * 未连接：进行连接。
     * 已连接：断开后，重新连接
     */
    if (this.socket === null) {
      this.connect(userId);
      return;
    }
    this.disconnect();
    this.connect(userId);
  }

  // 输出日志
  onMessage(res) {
    this.tabPaneMap[1].result += JSON.parse(res.data).content;
  }

  indexMethod(index) {
    return index + 1;
  }

  connect(userId: string) {
    const SockJS = require('sockjs-client');
    this.socket = new SockJS(getWebConsole(userId));
    this.socket.onmessage = this.onMessage;
  }

  disconnect() {
    try {
      this.socket.close();
    } finally {
      this.socket = null;
    }
  }
}
</script>

<style lang="scss" scoped>
#text {
  text-align: left;
}
::v-deep .bs-dialog .el-dialog__body {
  padding: 5px 0 0 0;
}
::v-deep .bs-pro-grid__header {
  padding: 0;
}
::v-deep .el-tabs__content {
  height: 300px;
  margin-top: -15px;
}
::v-deep .icon-yuandaima,
::v-deep .icon-liebiao {
  font-size: 12px !important;
  padding: 0 5px 0 0;
}
.test-dialog {
  &__data {
    border-bottom: $--bs-color-border-lighter;
    padding: 0 19px;
    ::v-deep .bs-code.operation {
      padding-top: 10px;
    }
    ::v-deep .bs-pro-grid__header {
      border-bottom: none;
    }
    .iconfont {
      cursor: pointer;
      color: #377cff;
      padding: 0 10px;
    }
    &--header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    &--select {
      margin-right: 415px;
    }
    &--content {
      width: 100%;
    }
    &--right {
      display: flex;
    }
    &--icons {
      display: flex;
      align-items: center;
      margin-left: 15px;
      .icon-yuandaima {
        margin-left: 5px;
      }
    }
    &--icon {
      margin-right: 11px;
    }
    &--border {
      width: 1px;
      height: 20px;
      border: 1px solid #f1f1f1;
    }
    &--code {
      text-align: left;
    }
    ::v-deep .el-table--border {
      border-top: 1px solid #ebeef5;
    }
  }
  &__result {
    &--pre {
      height: 280px;
      white-space: pre-wrap;
      word-wrap: break-word;
      color: #000;
      font-size: 14px;
      text-align: left;
      line-height: 20px;
      padding: 10px;
      border-top: 1px solid #e5e5e5;
      overflow: scroll;
      ::v-deep .bs-table {
        margin-top: -45px;
        margin-left: 10px;
      }
    }
    &--p {
      margin-top: -20px;
      margin-left: 10px;
    }
  }
}
</style>
