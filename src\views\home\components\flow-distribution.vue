<template>
  <div class="flow-distribution">
    <div class="bs-detail-block flow-distribution__content">
      <div class="bs-detail__header">
        <div class="bs-detail__header-title">
          <span>{{ $t('pa.home.distribution') }}</span>
          <el-tooltip effect="light" placement="bottom-start">
            <div slot="content">
              <p style="margin-bottom: 8px">
                {{ $t('pa.home.text4') }}
              </p>
              <p v-for="tip in statusTips" :key="tip" class="content-point">
                <span class="bs-circle"></span>
                {{ tip }}
              </p>
            </div>
            <i class="iconfont icon-weizhi" style="cursor: pointer"></i>
          </el-tooltip>
        </div>
      </div>
      <data-handler :request="request" @get-data="getData">
        <template v-slot:content>
          <charts :option="option" name="distribution" />
        </template>
      </data-handler>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import Charts from '../components/chart.vue';
import { FlowDistributionChart } from '../charts/charts';
import { getFlowDistribution } from '@/apis/homeApi';
import DataHandler from './data-handler.vue';
import { AxiosPromise } from 'axios';
import '../index.scss';
@Component({
  components: {
    Charts,
    DataHandler
  }
})
export default class FlowDistribution extends Vue {
  @Prop({ default: '' }) orgId!: string;
  private option = FlowDistributionChart.option;
  private request: AxiosPromise[] = [];
  statusTips = [
    this.$t('pa.home.text5'),
    this.$t('pa.home.text6'),
    this.$t('pa.home.text7'),
    this.$t('pa.home.text8'),
    this.$t('pa.home.text9'),
    this.$t('pa.home.text10'),
    this.$t('pa.home.text11')
  ];
  @Watch('orgId', { immediate: true })
  handleOrgIdChange(ids) {
    this.request = [getFlowDistribution(ids)];
  }
  getData({ data }) {
    if (data) {
      const { jobStatus } = data;
      FlowDistributionChart.setOption(jobStatus);
    }
  }
}
</script>
<style scoped>
.iconfont {
  padding-left: 4px;
  font-size: 14px;
  font-weight: normal;
  color: rgba(0, 0, 0, 0.25);
}
.content-point {
  display: flex;
  align-items: flex-start;
}
.bs-circle {
  width: 4px;
  height: 4px;
  background: #444;
  margin-right: 8px;
  margin-top: 6px;
}
</style>
