<template>
  <div class="flow-distribution">
    <div class="bs-detail-block flow-distribution__content">
      <div class="bs-detail__header">
        <div class="bs-detail__header-title">流程状态/数量分布</div>
      </div>
      <data-handler :request="request" @get-data="getData">
        <template v-slot:content>
          <charts :option="option" name="distribution" />
        </template>
      </data-handler>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import Charts from '../components/chart.vue';
import { FlowDistributionChart } from '../charts/charts';
import { getFlowDistribution } from '@/apis/homeApi';
import DataHandler from './data-handler.vue';
import { AxiosPromise } from 'axios';
@Component({
  components: {
    Charts,
    DataHandler
  }
})
export default class FlowDistribution extends Vue {
  @Prop({ default: '' }) orgId!: string;
  private option = FlowDistributionChart.option;
  private request: AxiosPromise[] = [];
  @Watch('orgId')
  handleOrgIdChange(ids) {
    this.request = [getFlowDistribution(ids)];
  }
  getData({ data }) {
    if (data) {
      const { jobStatus } = data;
      FlowDistributionChart.setOption(jobStatus);
    }
  }
}
</script>
