{"forms": [{"type": "input", "prop": "title", "label": "名称", "componentProps": {"maxlength": 30, "placeholder": "请输入名称", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入名称", "trigger": "blur"}, {"min": 2, "max": 30, "message": "长度在 2 到 30 个字符", "trigger": "blur"}]}, {"type": "textarea", "prop": "url", "label": "服务地址", "componentProps": {"rows": 3, "maxlength": 1024, "placeholder": "请输入服务地址，多个地址以逗号分隔，兼容Ipv4和Ipv6"}, "rules": [{"required": true, "message": "请输入服务地址，多个地址以逗号分隔，兼容Ipv4和Ipv6", "trigger": "blur"}, {"validator": "validateResUrl", "trigger": "blur"}]}, {"type": "select", "prop": "clusterType", "label": "集群类型", "componentProps": {"searchable": true, "placeholder": "请选择", "options": [{"label": "standalone", "value": "STANDALONE"}, {"label": "yarn_application", "value": "YARN_APPLICATION"}]}, "rules": [{"message": "请选择类型", "required": true, "trigger": "blur"}], "defaultVal": "STANDALONE", "editable": false}, {"type": "input-number", "prop": "deployTimeout", "label": "上线超时时间", "componentProps": {"min": 30, "max": 6000, "placeholder": "请输入上线部署超时时间，单位秒"}, "unit": "s", "defaultVal": 90, "rules": [{"required": true, "message": "请输入上线部署超时时间", "trigger": "blur"}, {"type": "number", "min": 30, "max": 6000, "message": "选择范围在30-6000之间", "trigger": "blur"}]}, {"type": "textarea", "prop": "flinkConf", "label": "flinkConf", "componentProps": {"rows": 5, "placeholder": "请复制flink-conf.yaml文件内容到此处"}, "rules": [{"required": true, "message": "请复制flink-conf.yaml文件内容到此处", "trigger": "blur"}]}, {"type": "input", "label": "queue", "prop": "queue", "deps": ["clusterType"], "visible": "(scope) => ['YARN_PER_JOB', 'YARN_APPLICATION'].includes(scope.clusterType)", "componentProps": {"placeholder": "请输入"}, "rules": [{"message": "请输入", "required": true, "trigger": "blur"}], "tooltip": "yarn通过队列来分配资源给流程使用,默认只有一个名为default的队列,若多个队列名用逗号进行分隔。若调度策略为公平调度，则一个加工引擎只支持配置一个队列，若想使用多个队列请注册多个加工引擎", "defaultVal": "default"}, {"type": "textarea", "prop": "yarnSite", "label": "yarnSite", "deps": ["clusterType"], "visible": "(scope) => scope.clusterType !== 'STANDALONE'", "componentProps": {"rows": 5, "placeholder": "请输入文件yarn-site.xml内容"}, "rules": [{"message": "请输入文件yarn-site.xml内容", "required": true, "trigger": "blur"}]}, {"type": "textarea", "prop": "coreSite", "label": "coreSite", "deps": ["clusterType"], "visible": "(scope) => scope.clusterType !== 'STANDALONE'", "componentProps": {"rows": 5, "placeholder": "请输入文件core-site.xml内容"}, "rules": [{"message": "请输入文件core-site.xml内容", "required": true, "trigger": "blur"}]}, {"type": "textarea", "prop": "hdfsSite", "label": "hdfsSite", "deps": ["clusterType"], "visible": "(scope) => scope.clusterType !== 'STANDALONE'", "componentProps": {"rows": 5, "placeholder": "请输入文件hdfs-site.xml内容"}, "rules": [{"message": "请输入文件hdfs-site.xml内容", "required": true, "trigger": "blur"}]}, {"type": "custom", "prop": "flinkJarFile", "label": "Flink依赖jar包", "deps": ["clusterType"], "visible": "(scope) => scope.clusterType === 'YARN_PER_JOB'", "componentProps": {"accept": ".zip", "textTip": "只支持zip格式"}}, {"type": "custom", "prop": "log4jFile", "label": "log4j配置", "deps": ["clusterType"], "visible": "(scope) => scope.clusterType === 'YARN_PER_JOB'", "componentProps": {"accept": ".properties", "textTip": "只支持propertie格式"}}, {"type": "radio-group", "prop": "proxyType", "label": "是否代理", "deps": ["clusterType"], "visible": "(scope) => scope.clusterType !== 'STANDALONE'", "componentProps": {"options": [{"value": "NO_PROXY", "label": "否"}, {"value": "PROXY", "label": "是"}]}, "rules": [{"message": "请选择是否代理", "required": true, "trigger": "change"}], "defaultVal": "NO_PROXY"}, {"type": "textarea", "prop": "proxyAddress", "label": "代理服务地址", "deps": ["proxyType"], "visible": "(scope) => scope.proxyType === 'PROXY'", "componentProps": {"rows": 3, "maxlength": 1024, "placeholder": "请输入代理地址的IP和端口，如nameNode1:8020"}, "rules": [{"required": true, "message": "请输入代理地址的IP和端口，如nameNode1:8020", "trigger": "blur"}]}, {"type": "textarea", "prop": "memo", "label": "备注", "componentProps": {"rows": 5, "maxlength": 255, "placeholder": "请输入备注"}}]}