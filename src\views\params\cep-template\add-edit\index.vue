<template>
  <pro-page
    :title="title"
    :loading="loading"
    :element-loading-text="loadingText"
    class="cep-container"
  >
    <!-- 提交 -->
    <el-button slot="operation" type="primary" @click="submit">提交</el-button>
    <!-- 基本信息 -->
    <base-info
      ref="baseInfoRef"
      class="cep-top"
      :is-edit="Boolean(id)"
      :disabled="disabled"
      :data.sync="cepData.basicInformation"
    />
    <!-- 模式 -->
    <model
      ref="modelRef"
      class="cep-top"
      :all-name="allName"
      :disabled="disabled"
      :data.sync="cepData"
      :model-list.sync="modelList"
      @change="handleChnage"
    />
    <!-- 模式组 -->
    <model-group
      ref="modelGroupRef"
      class="cep-top"
      :all-name="allName"
      :name-options="modelList"
      :used-module-list.sync="usedModuleList"
      :data.sync="cepData.groupCepPatterns"
      :mode-groupl-list.sync="modeGrouplList"
      :disabled="disabled"
      @change="handleChnage"
    />
    <!-- 模式序列 -->
    <model-sequence
      ref="modelSequenceRef"
      class="cep-top cep-bottom"
      :name-options="nameOptions"
      :used-module-list.sync="usedModuleList"
      :data.sync="cepData.sequenceCepPatterns"
      :group-data="cepData.groupCepPatterns"
      :disabled="disabled"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Ref, Vue } from 'vue-property-decorator';
import BaseInfo from './base-info/index.vue';
import Model from './model/index.vue';
import ModelGroup from './model-group/index.vue';
import ModelSequence from './model-sequence/index.vue';
import type { CepData } from '../type';
import { generateBaseInfo, generateCepData } from '../util';
import { debounce } from 'lodash';
import { URL_GET_CEP_DETAIL, URL_GET_CEP_UPDATE, URL_GET_CEP_ADD } from '@/apis/cep-api';
import { get, put } from '@/apis/utils/net';

@Component({
  components: {
    BaseInfo,
    Model,
    ModelGroup,
    ModelSequence
  }
})
export default class ComplexEventAddAndEdit extends Vue {
  @Ref('baseInfoRef') readonly BaseInfo!: BaseInfo;
  @Ref('modelRef') readonly Model!: ModelGroup;
  @Ref('modelGroupRef') readonly ModelGroup!: ModelGroup;
  @Ref('modelSequenceRef') readonly ModelSequence!: ModelSequence;
  private disabled = false;
  private cepData: CepData = {
    basicInformation: generateBaseInfo(),
    ddId: '',
    singletonCepPatterns: [],
    groupCepPatterns: [],
    sequenceCepPatterns: [
      {
        maximumAllowableTimeInterval: {
          timeValue: 1,
          timeValueType: 'DAYS'
        },
        sequenceSkipStrategy: 'NO_SKIP',
        groupName: '',
        groupCepPatternConditionList: [],
        logicalRelationship: ''
      }
    ]
  };
  private submit = debounce(this.handleSubmit, 1200);
  private loading = false;
  private loadingText = '数据加载中';
  private modelList: any[] = [];
  private modeGrouplList: any[] = [];
  private usedModuleList: any[] = [];

  get id() {
    return this.$route.query.id || '';
  }
  get title() {
    return `${this.id ? '编辑' : '新建'}CEP模板`;
  }
  get nameOptions() {
    return [...this.modelList, ...this.modeGrouplList];
  }
  get allName() {
    return this.nameOptions.map(({ value }) => value);
  }

  beforeRouteEnter(to: any, from, next) {
    to.meta.title = to.query.title || '新建CEP模板';
    next();
  }
  created() {
    this.loadingText = '数据加载中';
    this.loading = true;
    if (this.id) return this.getCepDetail();
    this.parseData(generateCepData());
  }

  /*  获取cep详细信息 */
  async getCepDetail() {
    try {
      const { success, data, error } = await get(URL_GET_CEP_DETAIL, { id: this.id });
      if (success) {
        this.parseData({ ...generateCepData(), ...data });
        return;
      }
      this.$message.error(error);
    } catch (e) {
      this.loading = false;
      console.log(e);
    }
  }
  /* 数据解析 */
  parseData(data: CepData) {
    /* 1，数据更新 */
    this.$set(this.cepData, 'basicInformation', data.basicInformation);
    this.$set(this.cepData, 'ddId', data.ddId);
    this.$set(this.cepData, 'singletonCepPatterns', data.singletonCepPatterns);
    this.$set(this.cepData, 'groupCepPatterns', data.groupCepPatterns);
    this.$set(this.cepData, 'sequenceCepPatterns', data.sequenceCepPatterns);
    /* 2，生成辅助数据 */
    this.generateAuxData();
  }
  /* 生成辅助数据 */
  generateAuxData() {
    this.modelList = this.generateModelList();
    this.modeGrouplList = this.generateModelGroupList();
    this.usedModuleList = this.generateUsedModuleList();
    this.loading = false;
  }
  /* 生成辅助数据ModelList */
  generateModelList() {
    return this.cepData.singletonCepPatterns.map(({ singletonModeName: label }: any) => {
      return {
        label,
        value: label,
        type: 'model',
        disabled: false
      };
    });
  }
  /* 生成辅助数据 */
  generateModelGroupList() {
    return this.cepData.groupCepPatterns.map(({ groupName: label }: any) => {
      return {
        label,
        value: label,
        type: 'group',
        disabled: false
      };
    });
  }
  /* 生成辅助数据 UsedModuleList */
  generateUsedModuleList() {
    const result: string[] = this.getModelOrGroupName(
      this.cepData.sequenceCepPatterns[0].groupCepPatternConditionList
    );
    this.cepData.groupCepPatterns.forEach(({ groupCepPatternConditionList }): any => {
      result.push(...this.getModelOrGroupName(groupCepPatternConditionList));
    });
    return result;
  }
  /* 提取modelOrGroupName */
  getModelOrGroupName(data: any[]) {
    return data.map(({ modelOrGroupName }: any) => modelOrGroupName);
  }
  handleChnage() {}
  /* 处理提交事件 */
  async handleSubmit() {
    try {
      this.loading = true;
      this.loadingText = '';
      /* 1，必填项校验 */
      await this.BaseInfo.validate();
      await this.Model.validate();
      await this.ModelGroup.validate();
      await this.ModelSequence.validate();
      /* 2，数量校验 */
      if (this.cepData.singletonCepPatterns.length < 1) {
        return this.$message.error('至少配置一个模式');
      }
      if (this.cepData.sequenceCepPatterns[0].groupCepPatternConditionList.length < 1) {
        return this.$message.error('模式序列至少配置一个节点');
      }
      /* 3，数据处理 */
      const data = await this.handleSaveData();
      /* 4，数据保存 */
      await this.saveData(data);
      this.loading = false;
      this.loadingText = '数据加载中';
      /* 5，关闭tab */
      (this as any).$tabsNav.deleteTab(this.$route.fullPath);
    } catch (e) {
      console.log(e);
      this.loading = false;
    }
  }
  /* 处理保存数据 */
  async handleSaveData() {
    let changeDescription = '';
    if (this.id) {
      changeDescription = await this.$saveConfirm.show();
    }
    this.cepData.sequenceCepPatterns[0].groupCepPatternConditionList = this.changeIndex(
      this.cepData.sequenceCepPatterns[0].groupCepPatternConditionList
    );
    this.cepData.groupCepPatterns = this.cepData.groupCepPatterns.map((el) => {
      el.groupCepPatternConditionList = this.changeIndex(el.groupCepPatternConditionList);
      return el;
    });
    return {
      changeDescription,
      id: this.id,
      content: { ...this.cepData }
    };
  }
  changeIndex(data: any[]) {
    return data.map((el, index) => {
      el.index = index;
      return el;
    });
  }
  /* 数据保存 */
  async saveData(params: any = {}) {
    const URL = this.id ? URL_GET_CEP_UPDATE : URL_GET_CEP_ADD;
    const { success, msg, error } = await put(URL, params);
    if (success) {
      return this.$message.success(msg);
    }
    this.$message.error(error);
    throw new Error(error);
  }
}
</script>

<style lang="scss" scoped>
.cep {
  &-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #f2f4f7;
    ::v-deep .el-button {
      &--small,
      &--mini {
        padding-top: unset;
        padding-bottom: unset;
      }
    }
  }
  &-top {
    margin-top: 18px;
  }
  &-bottom {
    margin-bottom: 20px;
  }
}
</style>
