::v-deep .el-select {
  width: 100%;
}
.config-form-item {
  width: 90%;
}
.table-input {
  outline: none;
  padding: 0 12px;
  border-radius: 4px;
  color: #666;
}
.trans {
  color: $--bs-color-primary;
  cursor: pointer;
  margin-left: 4px;
  word-break: keep-all;
  white-space: nowrap;
}
.red {
  color: #ff5353;
  font-size: 12px;
}
.nums {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.out-count {
  color: $--bs-color-primary;
}
.iconfont.icon-wenhao {
  margin-left: 10px;
  cursor: pointer;
}
.el-button.el-button--text {
  margin-left: 5px;
}
.page-select-option {
  display: inline-flex;
  justify-content: space-between;
  width: calc(100% - 14px);
  padding-left: 10px;
  .bs-tag {
    margin-left: 6px;
  }
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

::v-deep .el-form-item__error {
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.icon-wenhao {
  height: 100%;
}
