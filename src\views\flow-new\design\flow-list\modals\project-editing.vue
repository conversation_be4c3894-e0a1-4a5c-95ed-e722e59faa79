<template>
  <el-form ref="ruleForm" :model="formData" :rules="formRules" :label-width="isEn ? '70px' : '50px'" @submit.native.prevent>
    <el-form-item :label="$t('pa.flow.name')" prop="projectName">
      <el-input
        v-model="formData.projectName"
        autocomplete="off"
        maxlength="30"
        show-word-limit
        :placeholder="$t('pa.flow.placeholder5')"
      />
    </el-form-item>
    <el-form-item :label="$t('pa.notes')" prop="memo">
      <el-input
        v-model="formData.memo"
        type="textarea"
        rows="5"
        :placeholder="$t('pa.flow.placeholder6')"
        autocomplete="off"
        maxlength="255"
        show-word-limit
      />
    </el-form-item>
  </el-form>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { addProject, getProjectById, updateProject } from '@/apis/flowNewApi';

@Component
export default class ProjectEditing extends Vue {
  @Prop() projectId!: string;
  formData = { projectName: '', memo: '' };
  formRules = {
    projectName: [{ required: true, message: this.$t('pa.flow.placeholder7'), trigger: 'blur' }]
  };
  async created() {
    // 编辑：获取项目信息
    if (this.projectId) {
      const { data = {} } = await getProjectById({ id: this.projectId });
      this.formData = data;
    }
  }
  // 配合弹窗的确认点击事件
  public confirm(done) {
    const form: any = this.$refs.ruleForm;
    form.validate(async (valid: any) => {
      if (!valid) return done(false);
      const req = this.projectId ? updateProject : addProject;
      const { success, msg } = await req(this.formData);
      this.$message[success ? 'success' : 'error'](msg);
      done(success);
    });
  }
}
</script>
<style lang="scss" scoped></style>
