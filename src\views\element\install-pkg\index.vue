<!--
 * @Description: 元件管理-安装包
 * @Author: ranran
 * @Date: 2020-01-13 10:29:00
 * @LastEditTime: 2020-02-28 15:18:29
 * @LastEditors: magicyang
 -->
<template>
  <div class="page-content">
    <header>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>元件管理</el-breadcrumb-item>
        <el-breadcrumb-item>安装包</el-breadcrumb-item>
      </el-breadcrumb>
    </header>
    <div style="overflow-y: scroll; height: 100%">
      <div class="cardHeight">
        <ZookeeperCard v-if="hasFeatureAuthority('PA.ELE.PKG.ZK')" pkg-type="ZOOKEEPER" />
      </div>
      <div class="cardHeight">
        <KafkaCard v-if="hasFeatureAuthority('PA.ELE.PKG.KAFKA')" pkg-type="KAFKA" />
      </div>
      <div class="cardHeight">
        <AerospikeCard v-if="hasFeatureAuthority('PA.ELE.PKG.AS')" pkg-type="AEROSPIKE" />
      </div>
      <div class="cardHeight">
        <StreamCubeCard v-if="hasFeatureAuthority('PA.ELE.PKG.SC')" pkg-type="STREAMCUBE" />
      </div>
      <div class="cardHeight" style="margin: 0px 0px 100px 0px">
        <SmartEngineCard v-if="hasFeatureAuthority('PA.ELE.PKG.SDE')" pkg-type="SMARTENGINE" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {
    ProbeCard: () => import('./component/pkg-card.vue'),
    ZookeeperCard: () => import('./component/pkg-card.vue'),
    KafkaCard: () => import('./component/pkg-card.vue'),
    AerospikeCard: () => import('./component/pkg-card.vue'),
    StreamCubeCard: () => import('./component/pkg-card.vue'),
    SmartEngineCard: () => import('./component/pkg-card.vue')
  }
})
export default class ElementInstallPkg extends PaBase {
  ProbeCard: any;
  ZookeeperCard: any;
  KafkaCard: any;
  AerospikeCard: any;
  StreamCubeCard: any;
  SmartEngineCard: any;
}
</script>
<style lang="scss" scoped>
.cardHeight {
  height: 400px;
}
</style>
