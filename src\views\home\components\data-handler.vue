<template>
  <div v-loading="refresh" class="data-handler">
    <el-skeleton v-if="isLoading" class="data-handler__skeleton" animated :loading="isLoading" />
    <slot v-if="isFinish" name="content"></slot>
    <abnormal-data v-if="isAbnormal" :status="status" @refresh="requestData(true)" />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import AbnormalData from './abnormal-data.vue';
import { cloneDeep } from 'lodash';
@Component({
  components: {
    AbnormalData
  }
})
export default class DataHandler extends Vue {
  // 接口请求数组
  @Prop({ default: () => [] }) request!: any[];
  private status = 'loading';
  get isLoading() {
    return this.status === 'loading';
  }
  get refresh() {
    return this.status === 'refresh';
  }
  get isFinish() {
    return this.status === 'finish';
  }
  get isAbnormal() {
    return this.status === 'timeout' || this.status === 'empty';
  }
  @Watch('request')
  handleRequestChange() {
    if (this.request.some((el) => typeof el === 'boolean')) {
      this.requestData(true);
    } else {
      this.requestData();
    }
  }

  created() {
    this.requestData();
  }

  async requestData(refresh = false) {
    let request = cloneDeep(this.request);
    if (request.length === 0) {
      this.status = 'finish';
    }
    if (refresh) {
      request = request.filter((el) => {
        return typeof el !== 'boolean';
      });
      this.status = 'refresh';
    } else {
      this.status = 'loading';
    }
    if (Array.isArray(request) && request.length > 0) {
      const { length } = request;
      try {
        const resp = length === 1 ? await request[0] : await Promise.all(request);
        this.isEmpty(resp);
        this.$emit('get-data', resp);
      } catch (err) {
        this.status = 'timeout';
      }
    }
  }

  // 判断请求到的数据是否为空
  isEmpty(resp: any) {
    if (Array.isArray(resp) || resp.data) {
      if (Array.isArray(resp)) {
        const isFailed = resp.some((el) => el.success === false);
        if (isFailed) {
          this.status = 'empty';
          return;
        }
      }
      // 正常状态（finish）
      this.status = 'finish';
      // 请求超时状态（timeout）
    } else if (resp === undefined) {
      this.status = 'timeout';
      // 数据为空状态（empty）
    } else {
      this.status = 'empty';
    }
  }
}
</script>

<style lang="scss" scoped>
.data-handler {
  height: 100%;
  &__skeleton {
    width: 100%;
    margin-top: 20px;
  }
  ::v-deep .el-loading-mask {
    background: transparent;
  }
}
</style>
