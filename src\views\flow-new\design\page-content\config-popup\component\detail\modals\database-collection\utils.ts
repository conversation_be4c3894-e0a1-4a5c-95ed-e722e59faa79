import i18n from '@/i18n';
import cloneDeep from 'lodash/cloneDeep';
export const GetFlagByOptions = (options: any[] = []) => {
  const timeFieldOptions = options.filter((el) => el.isTime);
  return {
    hasPrimaryKey: options.some((el) => el.isPrimaryKey),
    timeFieldOptions,
    hasTime: timeFieldOptions.length > 0
  };
};
export const IslegalValue = (value: string, hasPrimaryKey: boolean, hasTime: boolean) => {
  return [hasPrimaryKey && 'primaryKey', hasTime && 'time', 'index', 'none', 'stream'].filter(Boolean).includes(value);
};

export const GetQueryStrategyOptions = (hasPrimaryKey: boolean, hasTime: boolean) => {
  return [
    {
      label: i18n.t('pa.flow.label71'),
      value: 'primaryKey',
      tooltip: i18n.t('pa.flow.msg310'),
      disabled: !<PERSON><PERSON>an(hasPrimaryKey)
    },
    {
      label: i18n.t('pa.flow.label72'),
      value: 'time',
      tooltip: i18n.t('pa.flow.msg311'),
      disabled: !Boolean(hasTime)
    },
    {
      label: i18n.t('pa.flow.label73'),
      value: 'index',
      tooltip: i18n.t('pa.flow.msg312'),
      disabled: false
    },
    {
      label: i18n.t('pa.flow.label74'),
      value: 'none',
      tooltip: i18n.t('pa.flow.msg313'),
      disabled: false
    },
    {
      label: i18n.t('pa.flow.label75'),
      value: 'stream',
      tooltip: i18n.t('pa.flow.msg314'),
      disabled: false
    }
  ];
};

export const RESET_TYPE_OPTIONS = [
  {
    label: i18n.t('pa.flow.wu'),
    value: '-1'
  },
  {
    label: i18n.t('pa.flow.label70'),
    value: '0'
  },
  {
    label: i18n.t('pa.flow.key16'),
    value: '1'
  },
  {
    label: i18n.t('pa.flow.hignMode'),
    value: '2'
  }
];
export const GenerateOption = (num, delFirst = false) => {
  const result = Object.keys(Array.from({ length: num + 1 }))
    .filter(Boolean)
    .map((el) => ({ value: String(el), label: String(el) }));
  delFirst && result.shift();
  return result;
};

export const CRON_CYCLE_OPTIONS = [
  {
    label: i18n.t('pa.flow.m'),
    value: 'min',
    children: GenerateOption(59, true)
  },
  {
    label: i18n.t('pa.flow.h1'),
    value: 'hour',
    children: GenerateOption(23, true)
  },
  {
    label: i18n.t('pa.unit.day'),
    value: 'day',
    children: GenerateOption(30, true)
  }
];

export const TIMING_MODE_OPTIONS = [
  {
    label: i18n.t('pa.flow.s'),
    prop: 'sec_1',
    rules: {
      required: true,
      message: i18n.t('pa.placeholder.select'),
      trigger: 'change'
    },
    options: [
      {
        label: i18n.t('pa.flow.meiS'),
        value: '-1'
      },
      ...GenerateOption(59)
    ]
  },
  {
    label: i18n.t('pa.flow.m1'),
    prop: 'min_1',
    rules: {
      required: true,
      message: i18n.t('pa.placeholder.select'),
      trigger: 'change'
    },
    options: [
      {
        label: i18n.t('pa.flow.meiM'),
        value: '-1'
      },
      ...GenerateOption(59)
    ]
  },
  {
    label: i18n.t('pa.flow.h1'),
    prop: 'hour_1',
    rules: {
      required: true,
      message: i18n.t('pa.placeholder.select'),
      trigger: 'change'
    },
    options: [
      {
        label: i18n.t('pa.flow.meiH'),
        value: '-1'
      },
      ...GenerateOption(23)
    ]
  },
  {
    label: i18n.t('pa.flow.d'),
    prop: 'day_1',
    rules: {
      required: true,
      message: i18n.t('pa.placeholder.select'),
      trigger: 'change'
    },
    options: [
      {
        label: i18n.t('pa.flow.meiD'),
        value: '-1'
      },
      ...GenerateOption(31)
    ]
  }
];
export const SENIOR_MODE_OPTIONS = [
  {
    label: i18n.t('pa.flow.s'),
    prop: 'sec_2',
    rules: {
      required: true,
      message: i18n.t('pa.placeholder.input'),
      trigger: 'blur'
    }
  },
  {
    label: i18n.t('pa.flow.m1'),
    prop: 'min_2',
    rules: {
      required: true,
      message: i18n.t('pa.placeholder.input'),
      trigger: 'blur'
    }
  },
  {
    label: i18n.t('pa.flow.h1'),
    prop: 'hour_2',
    rules: {
      required: true,
      message: i18n.t('pa.placeholder.input'),
      trigger: 'blur'
    }
  },
  {
    label: i18n.t('pa.flow.d'),
    prop: 'day_2',
    rules: {
      required: true,
      message: i18n.t('pa.placeholder.input'),
      trigger: 'blur'
    }
  },
  {
    label: i18n.t('pa.flow.mm'),
    prop: 'month_2',
    rules: {
      required: true,
      message: i18n.t('pa.placeholder.input'),
      trigger: 'blur'
    }
  },
  {
    label: i18n.t('pa.flow.week'),
    prop: 'week_2',
    rules: {
      required: true,
      message: i18n.t('pa.placeholder.input'),
      trigger: 'blur'
    }
  }
];

export const getDispalyList = (index: string | number) => {
  const arr = ['cronCycle', 'timingMode', 'seniorMode'];
  if (!arr[index]) return [[], arr];
  const show = arr.splice(Number(index), 1);
  return [show, arr];
};

const getChangeList = (index: string | number) => {
  const arr = [
    ['cronCycle'],
    ['sec_1', 'min_1', 'hour_1', 'day_1'],
    ['sec_2', 'min_2', 'hour_2', 'day_2', 'month_2', 'week_2']
  ];
  if (!arr[index]) return [[], arr.flat(2)];
  const show = arr.splice(Number(index), 1);
  return [show.flat(2), arr.flat(2)];
};

export const handleResetTypeValue = (formData) => {
  const data = cloneDeep(formData);
  const [setList, delList] = getChangeList(data.resetType);
  for (const key of delList) {
    delete data[key];
  }
  setList.forEach((el) => {
    let defalutval = '';
    if (el.includes('_2')) {
      defalutval = el === 'week_2' ? '?' : '*';
    }
    data[el] = data[el] || defalutval;
  });
  return data;
};

export const getQueryStrategyDispaly = (value: string) => {
  return (
    {
      primaryKey: [['pageSize'], ['timeField', 'queryInterval', 'startTime', 'indexField']],
      time: [
        ['timeField', 'queryInterval', 'startTime'],
        ['pageSize', 'indexField']
      ],
      index: [
        ['indexField', 'pageSize'],
        ['timeField', 'queryInterval', 'startTime']
      ],
      none: [['pageSize'], ['timeField', 'queryInterval', 'startTime', 'indexField']],
      stream: [[], ['timeField', 'pageSize', 'queryInterval', 'startTime', 'indexField']]
    }[value] || [[], []]
  );
};
export const handleQueryStrategyValue = (formData) => {
  const data = cloneDeep(formData);
  const [setList, delList] = getQueryStrategyDispaly(data.queryStrategy);
  for (const key of delList) {
    delete data[key];
  }
  setList.forEach((el) => {
    let defalutval: string | number = 100;
    if (el === 'queryInterval') {
      defalutval = 100 * 1000;
    }
    if (el === 'timeField' || el === 'indexField' || el === 'startTime') {
      defalutval = '';
    }
    data[el] = data[el] || defalutval;
  });
  return data;
};

export const IsValidDate = (time) => {
  if (/^\d+$/.test(time)) time = Number(time);
  return new Date(time).toString() !== 'Invalid Date';
};

export const getDefaultData = () => ({
  jdbcType: '', // 服务类型
  resTitle: '', // 服务名称
  resId: '', // 服务Id
  jdbcUrl: '', //服务地址
  tableName: '', // 选择表
  selectField: [], //输出字段
  userDefinedWhere: '', // while 条件
  queryStrategy: '', // 查询方式
  timeField: '', // 时间字段
  queryInterval: 100000, // 区间大小
  startTime: '', // 起始时间
  indexField: '', // 索引字段
  pageSize: 100, // 分页大小
  waitTime: 60000, // 间隔时间
  resetType: '-1', // 游标重置模式
  cronCycle: '', // 周期模式
  sec_1: '', // 定时模式-秒
  min_1: '', // 定时模式-分钟
  hour_1: '', // 定时模式-小时
  day_1: '', // 定时模式-日
  sec_2: '', // 高级模式-秒
  min_2: '', // 高级模式-分钟
  hour_2: '', // 高级模式-小时
  day_2: '', // 高级模式-日
  month_2: '', // 高级模式-月
  week_2: '' // 高级模式-星期
});
