<template>
  <bs-dialog
    size="medium"
    :title="title"
    class="process-dialog"
    :visible.sync="visible"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    @confirm="handleConfirm"
  >
    <div v-loading="loading" :element-loading-text="$t('pa.flow.comLoading')" class="process-container">
      <!-- 表单 -->
      <main-form ref="formRef" :disabled="disabled" :data.sync="formData" :field-list="fieldList" @config="handleConfig" />
      <!-- 配置明细 -->
      <logic-config
        v-if="showConfigDialog"
        :name="configName"
        :title="configTitle"
        :disabled="disabled"
        :show.sync="showConfigDialog"
        :data.sync="configData"
        :field-list="fieldList"
        :method-list="methodList"
        :method-code="methodCode"
        :func-mapping="funcMapping"
        @change="handleConfigChange"
        @getCode="getMethodCode"
      />
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import { get } from '@/apis/utils/net';
import MainForm from './main-form.vue';
import LogicConfig from './logic-config/index.vue';
import { cloneDeep } from 'lodash';
import { isArray, generateFormData } from './utils';
import { URL_GET_FUNC_LIST, URL_GET_FUNC_BODY } from '@/apis/commonApi';
import type { FormData } from './type';

@Component({
  components: {
    MainForm,
    LogicConfig
  }
})
export default class ProcessDelay extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: () => ({}) }) data!: any;
  @Prop() jobData!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: false }) disabled!: boolean;
  @PropSync('sourceCode', { default: () => ({}) }) methodCode!: any;
  @Ref('formRef') readonly form!: MainForm;
  private loading = false;
  private outputFields: any[] = [];
  private formData: FormData = generateFormData();
  /* about config dialog start*/
  private showConfigDialog = false;
  private configTitle = '';
  private configName = '';
  private configData: any = {};
  private fieldList: any[] = [];
  private methodList: any[] = []; // 方法列表
  /* about config dialog end*/
  private funcMapping = {};

  get title() {
    const { nodeName = '', componentName = '' } = this.data || {};
    return this.$t('pa.flow.msg288', [nodeName, componentName]);
  }

  handleConfig({ name, label }) {
    this.configTitle = label;
    this.configName = name;
    this.configData = this.formData[name];
    this.showConfigDialog = true;
  }

  async created() {
    try {
      this.loading = true;
      this.handleUpstreamField(isArray(this.data.inputFields));
      await this.getMethodList();
      this.formData = { ...generateFormData(), ...this.data.properties };
      this.loading = false;
    } catch (e) {
      this.loading = false;
      console.log(e);
    }
  }
  handleUpstreamField(data: any[] = []) {
    if (data.length < 1) return;
    const fieldList: any[] = [];
    const outputFields: any[] = [];
    data.forEach(({ name, type }) => {
      if (name && type) {
        fieldList.push({
          label: name,
          value: name,
          type
        });
        outputFields.push({
          name,
          type,
          outputable: true
        });
      }
    });
    this.fieldList = fieldList;
    this.outputFields = outputFields;
  }
  async getMethodList() {
    try {
      const { success, data, error } = await get(URL_GET_FUNC_LIST, { orgId: this.orgId });
      if (success) {
        const [mapping, ...list]: any[] = this.handleMethodList(isArray(data), {
          SHARE: [1, this.$t('pa.flow.publicMethod')],
          DEFAULT: [2, this.$t('pa.flow.defaultMethod')]
        });
        this.funcMapping = mapping;
        this.methodList = list;
        return;
      }
      this.$message.error(error);
    } catch (e) {
      console.log(e);
    }
  }

  handleMethodList(list: any[], mapping: any = {}) {
    return list
      .reduce((pre: any, { funcType, funcName, funcArgs, paramsType }) => {
        if (funcType in mapping) {
          const [index, label] = mapping[funcType];
          if (!pre[index]) pre[index] = { label, children: [] };
          pre[index].children.push({
            label: funcName,
            value: funcName,
            type: funcType
          });
        }
        if (!pre[0]) pre[0] = {};
        pre[0][funcName] = {
          funcType,
          funcName,
          funcArgs,
          paramsType,
          isOneParams: funcArgs.length < 1
        };
        return pre;
      }, [])
      .filter(Boolean);
  }

  async getMethodCode(params: any = {}) {
    try {
      const { success, data, error } = await get(URL_GET_FUNC_BODY, params);
      if (success) {
        this.$set(this.methodCode, params.name, data);
        return;
      }
      this.$message.error(error);
    } catch (e) {
      console.log(e);
    }
  }
  handleConfigChange(name: string, data: any) {
    if (name && data) {
      this.formData[name] = data;
    }
  }
  async handleConfirm() {
    try {
      await this.form.validate();
      this.closeDialog(true);
    } catch (e) {
      console.log(e);
    }
  }

  @Emit('close')
  private closeDialog(needUpdate = false) {
    const jobNode = cloneDeep(this.data);
    jobNode.outputFields = cloneDeep(this.outputFields);
    jobNode.properties = cloneDeep(this.formData);
    return { needUpdate, jobNode };
  }
}
</script>
<style lang="scss" scoped>
.process {
  &-dialog {
    ::v-deep .el-dialog {
      &__body {
        padding: 0 10px;
      }
    }
  }

  &-container {
    margin-top: 20px;
    padding: 0 20px;
  }
}
</style>
