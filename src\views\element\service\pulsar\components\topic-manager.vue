<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">Topics</div>
      <div class="bs-page__header-operation">
        <bs-search
          v-model="search.topic"
          class="marR12"
          placeholder="topicName"
          @change="searchList"
        />
        <bs-select
          v-model="search.tenant"
          class="marR12"
          :options="tenantList"
          placeholder="Tenant"
          clearable
          @change="tenantChange"
        />
        <bs-select
          v-model="search.namespace"
          class="marR12"
          :options="namespaceList"
          placeholder="Namespace"
          clearable
          @focus="getNamespaceList(search.tenant)"
        />
        <el-button type="primary" @click="createTopic">创建</el-button>
      </div>
    </div>
    <div class="tab-content">
      <bs-table
        v-loading="loading"
        :data="tableData"
        :column-data="columnData"
        :page-data="pageData"
        :column-settings="false"
        :height="500"
        @page-change="topicPageChange"
      >
        <template v-slot:operator="{ row }">
          <div style="width: 50px">
            <el-tooltip effect="light" placement="bottom" content="数据预览">
              <i class="iconfont icon-chakan bs-table_td--operator" @click="dataPreview(row)"></i>
            </el-tooltip>
            <el-tooltip effect="light" placement="bottom" content="引用关系">
              <i
                class="iconfont icon-yinyongguanxi bs-table_td--operator"
                @click="relationPreview(row)"
              ></i>
            </el-tooltip>
          </div>
        </template>
      </bs-table>
      <topic-add
        v-if="addVisible"
        :visible.sync="addVisible"
        :res-id="resId"
        :tenant-list="tenantList"
        :namespace-list="namespaceList"
        @close="fetchList"
      />
      <topic-preview
        v-if="previewDialogVisible"
        :visible.sync="previewDialogVisible"
        :res-id="resId"
        :topic="curTopic"
      />
      <view-relation
        v-if="relationDialogVisible"
        :visible.sync="relationDialogVisible"
        :res-id="resId"
        :topic="curTopic"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { getTopicList, getPulsarTenantList, getPulsarNamespaceList } from '@/apis/serviceApi';
import { debounce } from 'lodash';
@Component({
  components: {
    'topic-add': () => import('../modals/topic-add.vue'),
    'topic-preview': () => import('../../common/topic-preview.vue'),
    'view-relation': () => import('../../common/view-relation.vue')
  }
})
export default class TopicManager extends Vue {
  tableData: any = [];
  columnData: any = [];
  pageData = { pageSize: 25, currentPage: 1, total: 0 };
  search = { topic: '', tenant: '', namespace: '' };
  tenantList: any = [];
  namespaceList: any = [];
  addVisible = false;
  previewDialogVisible = false;
  relationDialogVisible = false;
  resId: any = '';
  curTopic: any = {};
  searchList = debounce(this.fetchList, 500);
  loading = true;

  tenantChange() {
    this.search.namespace = '';
    this.namespaceList = [];
    this.fetchList();
  }

  dataPreview(row) {
    this.curTopic = row;
    this.previewDialogVisible = true;
  }

  relationPreview(row) {
    this.curTopic = row;
    this.relationDialogVisible = true;
  }

  topicPageChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    this.fetchList();
  }

  // 获取Topic列表
  async fetchList() {
    this.loading = true;
    const {
      data: { tableData, columnData, pageData },
      success,
      msg
    } = await getTopicList(this.resId, {
      pageData: this.pageData,
      search: this.search
    });
    this.loading = false;
    if (success) {
      this.tableData = tableData;
      this.columnData = columnData;
      this.pageData = pageData;
      return;
    }
    this.$message.error(msg);
  }

  createTopic() {
    this.addVisible = true;
  }

  async getTenantList() {
    const { data = [], success, msg } = await getPulsarTenantList(this.resId);
    if (success) {
      data.forEach((el) => {
        this.tenantList.push({ label: el, value: el });
      });
      return;
    }
    this.$message.error(msg);
  }

  async getNamespaceList(tenant) {
    this.namespaceList = [];
    const { data, success, msg } = await getPulsarNamespaceList(this.resId, tenant);
    if (success) {
      data.forEach((el) => {
        this.namespaceList.push({ label: el, value: el });
      });
      return;
    }
    this.$message.error(msg);
  }

  created() {
    this.resId = this.$route.query.id;
    this.fetchList();
    this.getTenantList();
  }
}
</script>
<style scoped lang="scss"></style>
