<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">TaskManager信息</div>
    </div>
    <div class="tab-content" :style="{ height: height }">
      <el-table :data="tableData" :height="'100%'">
        <el-table-column prop="id" label="ID" />
        <el-table-column label="最后心跳时间" width="170">
          <template slot-scope="{ row }">
            {{ timeFormatter(row.timeSinceLastHeartbeat) }}
          </template>
        </el-table-column>
        <el-table-column prop="slotsNumber" label="slot总数" width="200" />
        <el-table-column prop="freeSlots" label="可用slot总数" width="200" />
        <el-table-column prop="hardware.cpuCores" label="CPU核数" width="200" />
        <el-table-column label="物理内存">
          <template slot-scope="{ row }">
            {{ hardware(row, 'physicalMemory') }}
          </template>
        </el-table-column>
        <el-table-column label="JVM堆内存">
          <template slot-scope="{ row }">
            {{ hardware(row, 'freeMemory') }}
          </template>
        </el-table-column>
        <el-table-column label="Flink管理的内存">
          <template slot-scope="{ row }">
            {{ hardware(row, 'managedMemory') }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject } from 'vue-property-decorator';
import { URL_RES_DETAIL_FINK_TASKMANAGERS } from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {}
})
export default class FlinkTaskManagerInfo extends PaBase {
  height = '300px';
  resRecord: any = {};
  tableData: any = [];
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  created() {
    this.loadData(this.comDetailRecord.val || {}, this.comParams.FlinkTaskManagers || {});
  }
  getTaskManagers() {
    this.doGet(URL_RES_DETAIL_FINK_TASKMANAGERS, {
      params: { id: this.$route.query.id }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.tableData = resp.data.taskmanagers;
      });
    });
  }

  hardware(row: any, key: string) {
    return this.calcSize(row.hardware[key]);
  }
  calcSize(size: number) {
    let unit = 'MB';
    let result = size / 1024 / 1024;
    if (result > 1024) {
      unit = 'GB';
      result = result / 1024;
    }
    return _.round(result, 2) + unit;
  }

  async loadData(data: any, params: any) {
    this.resRecord = data;
    this.height = params.height;
    this.getTaskManagers();
  }
}
</script>
<style scoped></style>
