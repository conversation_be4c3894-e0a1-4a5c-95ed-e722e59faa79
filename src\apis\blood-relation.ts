import { SearchParams } from '@/views/blood-relation/type';
import { get, post } from './utils/net';

export const GET_HOMOLOGY_NODES = '/rs/pa/lineage/getHomologousNodes'; // 获取同源节点
export const GET_NEXT_LEVEL_RESOURCES = '/rs/pa/lineage/getNextLevelResources'; // 获取下一级资源, 支持向上向下扩散
export const GET_HEAD_MSG = '/rs/pa/lineage/getHeadMsg'; // 获取初始化的头结点的信息
export const GET_RELATIONSHIP = '/rs/pa/lineage/getRelationship'; // 根据服务ID和资源得到该节点的血缘关系
export const GET_RESOURCE_LIST = '/rs/pa/lineage/getResourceList'; // 根据类型得到对应类型的列表
export const GET_EDGE_DETAIL = '/rs/pa/lineage/edgeDetails'; // 根据服务ID和资源得到该节点的血缘关系
export const GET_POINT_DETAIL = '/rs/pa/lineage/pointDetails'; // 根据服务ID和资源得到该节点的血缘关系

// 获取所有项目下的流程
export const getAllJob = () => {
  return get('/rs/pa/lineage/getAllJob');
};

// 获取初始化的头结点的信息
export const getSearchNode = (data: unknown) => {
  return get('/rs/pa/lineage/getHeadMsg', data);
};

// 获取上/下一级节点的数据
export const getNextLevelResource = (data: SearchParams) => {
  return get('/rs/pa/lineage/getNextLevelResources', data);
};
// 批量获取上/下一级节点的数据
export const getNextLevelResourceList = (data: SearchParams[]) => {
  return post('/rs/pa/lineage/nextLevelResourceList', data);
};
