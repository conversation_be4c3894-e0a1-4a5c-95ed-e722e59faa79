export const GET_HOMOLOGY_NODES = '/rs/pa/lineage/getHomologousNodes'; // 获取同源节点
export const GET_NEXT_LEVEL_RESOURCES = '/rs/pa/lineage/getNextLevelResources'; // 获取下一级资源, 支持向上向下扩散
export const GET_HEAD_MSG = '/rs/pa/lineage/getHeadMsg'; // 获取初始化的头结点的信息
export const GET_RELATIONSHIP = '/rs/pa/lineage/getRelationship'; // 根据服务ID和资源得到该节点的血缘关系
export const GET_RESOURCE_LIST = '/rs/pa/lineage/getResourceList'; // 根据类型得到对应类型的列表
export const GET_EDGE_DETAIL = '/rs/pa/lineage/edgeDetails'; // 根据服务ID和资源得到该节点的血缘关系
export const GET_POINT_DETAIL = '/rs/pa/lineage/pointDetails'; // 根据服务ID和资源得到该节点的血缘关系
