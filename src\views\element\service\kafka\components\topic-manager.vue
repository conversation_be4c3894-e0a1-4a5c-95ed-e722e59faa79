<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">Topics</div>
      <div class="bs-page__header-operation">
        <el-input
          v-model="topicKeyWord"
          placeholder="请输入关键字搜索"
          style="width: 200px; margin-right: 5px"
          @input="search"
        />
        <el-button
          v-if="hasCreateAuth"
          style="margin: 0 5px"
          size="small"
          @click="handleCreateTopic"
        >
          创建
        </el-button>
        <el-button v-if="hasShareAuth" type="primary" size="small" @click="multipleShare">
          分享
        </el-button>
      </div>
    </div>
    <div class="tab-content" :style="{ minHeight: '180px' }">
      <el-table
        v-loading="tableLoading"
        :data="topicData"
        size="mini"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" :selectable="checkAvailable" />
        <el-table-column label="名称" width="800" align="center">
          <template slot-scope="scope">
            <del v-if="!scope.row.available" style="color: red">{{ scope.row.topic }}</del>
            <span v-else>{{ scope.row.topic }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-tooltip
              v-if="!scope.row.available"
              effect="light"
              placement="bottom"
              content="删除"
            >
              <i
                v-if="hasDeleteAuth"
                class="iconfont icon-shanchu"
                style="margin-left: 10px; cursor: pointer"
                @click="handleDeleteTopic(scope.row)"
              ></i>
            </el-tooltip>
            <div v-else>
              <el-tooltip effect="light" placement="bottom" content="数据预览">
                <i
                  class="iconfont icon-chakan"
                  style="margin-left: 10px; cursor: pointer"
                  @click="handlePreview(scope.row)"
                ></i>
              </el-tooltip>
              <el-tooltip effect="light" placement="bottom" content="分享">
                <i
                  v-if="hasShareAuth"
                  class="iconfont icon-fenxiang"
                  style="margin-left: 10px; cursor: pointer"
                  @click="shareTopic(scope.row)"
                ></i>
              </el-tooltip>
              <el-tooltip effect="light" placement="bottom" content="引用关系">
                <i
                  class="iconfont icon-yinyongguanxi"
                  style="margin-left: 10px; cursor: pointer"
                  @click="previewRelation(scope.row)"
                ></i>
              </el-tooltip>
            </div>
            <!-- <el-button size="mini">绑定实体</el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="pagination"
        background
        :current-page.sync="pageData.currentPage"
        :page-size="pageData.pageSize"
        :total="pageData.total"
        :pager-count="5"
        layout="total, prev, pager, next, jumper"
        @current-change="handleCurrentChange"
      />
      <topic-add
        :visible="dialogVisible"
        :data="recordData"
        :form-loading="formLoading"
        @close="closeDialog"
      />
      <topic-preview
        :data="previewRecordData"
        :visible="previewDialogVisible"
        @close="previewCloseDialog"
      />
      <dept-role
        v-if="showDeptRoleDialog"
        ref="DeptRole"
        :visible.sync="showDeptRoleDialog"
        :data="kafkaData"
        :topic-data="rowTopic"
      />
      <!-- 查看引用关系弹窗 -->
      <bs-dialog
        v-if="relationDialogShow"
        :visible="relationDialogShow"
        size="medium"
        @close="closeRelationDialog"
      >
        <div slot="title" class="relation-title">
          <span>{{ '引用关系(' + relationTopicName + ')' }}</span>
          <bs-select
            v-model="relationType"
            :options="relationOptions"
            style="width: 120px"
            @change="handleRelationTypeChange"
          />
        </div>
        <bs-table
          height="400px"
          :data="tableData"
          :column-data="columnData"
          :page-data="rePageData"
          :column-settings="false"
          @page-change="handleListPageChange"
        >
          <template slot="status_JOB" slot-scope="{ row }">
            <bs-tag :color="getStatusColor(row.status)">{{ row.status }}</bs-tag>
          </template>
          <span></span>
        </bs-table>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="exportRelationList">下载</el-button>
          <el-button @click="closeRelationDialog">关闭</el-button>
        </div>
      </bs-dialog>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject, Prop } from 'vue-property-decorator';
import {
  URL_RES_DETAIL_KAFKA_TOPIC,
  URL_RES_DELETE_KAFKA_TOPIC,
  URL_EXPORT_TOPIC_RELATION_LIST,
  URL_GET_TOPIC_RELATION_LIST
} from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
import { get as getAll, del, download, post } from '@/apis/utils/net';
import moment from 'moment';
@Component({
  components: {
    'topic-add': () => import('../modals/topic-add.vue'),
    'topic-preview': () => import('../modals/topic-preview.vue'),
    'dept-role': () => import('@/components/dept-role-1.vue')
  }
})
export default class TopicManager extends PaBase {
  height = '500px';
  formLoading = false;
  dialogVisible = false;
  recordData: any = {};
  tableLoading = false;

  previewDialogVisible = false;
  previewRecordData: any = {};

  topicKeyWord = '';
  topicData: any[] = [];

  rowTopic: any = {};
  listConf: any = {};
  data: any = {};
  selectedTopic!: any;
  // 表格操作项
  tableConfig: any = {
    width: 150,
    columnsExtend: {
      edit: [
        {
          tipMessage: '详情',
          iconfont: 'icon-chakan'
        },
        {
          tipMessage: '通过',
          iconfont: 'icon-state-right'
        },
        {
          tipMessage: '驳回',
          iconfont: 'icon-state-error'
        }
      ]
    }
  };

  showDeptRoleDialog = false;
  pageData: IPageData = {
    currentPage: 1,
    pageSize: 10,
    total: 0
  };
  allTableData: any = [];
  relationTopicName = '';
  relationDialogShow = false;
  topicId = '';
  // 引用关系类型
  relationType = 'JOB';
  relationOptions = [
    { value: 'JOB', label: '流程' },
    { value: 'TABLE', label: '表' }
  ];
  get hasShareAuth() {
    return this.hasFeatureAuthority(this.listConf.shareTopicAuthCode, this.data.dataLevelType);
  }
  get hasDeleteAuth() {
    return this.hasFeatureAuthority(this.listConf.deleteTopicAuthCode, this.data.dataLevelType);
  }
  get hasCreateAuth() {
    return this.hasFeatureAuthority(this.listConf.addTopicAuthCode, this.data.dataLevelType);
  }
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  @Inject('comListConf') comListConf;
  @Prop({ default: () => {} }) kafkaData;
  sourceData: any = [];
  // 引用弹窗相关
  rePageData = {
    layout: 'total, prev, pager, next, jumper',
    pageSize: 10,
    currentPage: 1,
    total: 0
  };
  tableData = [];
  columnData = [];

  get length() {
    return this.topicData.length;
  }
  created() {
    this.loadData(this.comDetailRecord.val || {}, this.comListConf.val || {});
  }

  handleCreateTopic() {
    this.dialogVisible = true;
    this.recordData.resId = this.$route.query.id;
    this.recordData.resType = this.$route.query.resType;
  }

  handlePreview(row: any) {
    this.previewDialogVisible = true;
    this.previewRecordData = row;
  }

  shareTopic(row) {
    this.rowTopic = row;
    this.showDeptRoleDialog = true;
  }

  async handleSearchTopic() {
    this.tableLoading = true;
    if (!this.$route.query.id) {
      return;
    }
    const resp = await getAll(URL_RES_DETAIL_KAFKA_TOPIC, {
      id: this.$route.query.id
    });
    this.parseResponse(resp, () => {
      if (!resp.data) {
        resp.data = [];
      }
      // 初始化
      this.topicData = [];
      this.allTableData = resp.data;
      this.sourceData = _.cloneDeep(this.allTableData);
      this.pageData.total = resp.data.length;
      this.topicData = this.allTableData.slice(0, 10);
    });
    this.tableLoading = false;
  }
  closeDialog(needFresh: any) {
    if (needFresh === true) {
      this.handleSearchTopic();
    }
    this.dialogVisible = false;
  }
  previewCloseDialog() {
    this.previewDialogVisible = false;
  }

  async loadData(data: any, listConf: any) {
    this.listConf = listConf;
    this.data = data;
    await this.handleSearchTopic();
  }

  search() {
    const filterData = this.sourceData.filter(
      (data) =>
        !this.topicKeyWord || data.topic.toLowerCase().includes(this.topicKeyWord.toLowerCase())
    );
    this.allTableData = _.clone(filterData);
    this.topicData = this.allTableData.slice(0, 10);
    this.pageData.currentPage = 1;
    this.pageData.total = this.allTableData.length;
  }

  checkAvailable(row) {
    if (row.available === false) {
      return false;
    }
    return true;
  }

  handleCurrentChange(val) {
    this.pageData.currentPage = val;
    this.topicData = this.allTableData.slice(10 * (val - 1), 10 * val);
  }

  handleDeleteTopic(row) {
    this.$confirm('确定删除该条数据吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(async () => {
      const resp = await del(URL_RES_DELETE_KAFKA_TOPIC, { topicId: row.id });
      if (resp.success) {
        this.$message.success(resp.msg);
        this.loadData(this.comDetailRecord.val || {}, this.comListConf.val || {});
        this.pageData.currentPage = 1;
      } else {
        this.$message.error(resp.msg);
      }
      this.topicKeyWord = '';
    });
  }

  multipleShare() {
    if (this.selectedTopic === undefined) {
      this.$message.error('请至少选择一项topic');
      return;
    }
    this.rowTopic = this.selectedTopic;
    if (!this.rowTopic.length) {
      this.$message.error('请至少选择一项topic');
      return;
    }
    this.showDeptRoleDialog = true;
  }

  handleSelectionChange(selectedData) {
    this.selectedTopic = selectedData;
    this.rowTopic = selectedData;
  }
  previewRelation({ topic, id }) {
    this.topicId = id;
    this.relationTopicName = topic;
    this.relationDialogShow = true;
    this.getRelationList();
  }
  closeRelationDialog() {
    this.relationType = 'JOB';
    this.relationDialogShow = false;
  }
  // 获取引用关系
  async getRelationList() {
    const { pageSize, currentPage, total } = this.rePageData;
    const params = {
      search: this.topicId,
      pageData: {
        pageSize,
        currentPage,
        total
      }
    };
    const api = URL_GET_TOPIC_RELATION_LIST;
    const sortCreateTime = (property) => {
      return (a, b) => {
        const value1 = a[property];
        const value2 = b[property];
        return value2 - value1;
      };
    };
    const { data } = await post(`${api}/${this.relationType}`, params);
    const values: string[] = [];
    this.columnData = data.columnData.map((item) => {
      const value = item.value || item.prop;
      values.push(value);
      return {
        ...item,
        value: `${value}_${this.relationType}`
      };
    });
    data.tableData.sort(sortCreateTime('createTime'));
    // 处理表格表头数据缓存问题
    data.tableData.forEach((el) => {
      el.createTime = moment(el.createTime).format('YYYY-MM-DD HH:mm:ss');
      Object.keys(el).forEach((k) => {
        values.includes(k) && (el[`${k}_${this.relationType}`] = el[k]);
      });
    });
    this.tableData = data.tableData;
    this.rePageData.total = data.pageData.total;
  }
  handleRelationTypeChange() {
    this.rePageData.currentPage = 1;
    this.getRelationList();
  }
  // 导出引用关系(excel格式)
  async exportRelationList() {
    const api = URL_EXPORT_TOPIC_RELATION_LIST;
    await download(`${api}/${this.relationType}?id=${this.topicId}`);
  }
  // 监听分页变换
  handleListPageChange(currentPage, pageSize) {
    this.rePageData.currentPage = currentPage;
    this.rePageData.pageSize = pageSize;
    this.getRelationList();
  }
  getStatusColor(status: string) {
    return {
      上线: 'yellow',
      上线中: 'yellow',
      发布: 'green',
      发布中: 'green',
      开发: ''
    }[status];
  }
}
</script>
<style scoped lang="scss">
.my-card_body ::v-deep .el-card__body {
  padding: 0px 0px 0px 0px;
}

.my-wrap-row {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
}
.my-wrap-col {
  border: 1px solid #000;
}
.my-wrap-tag {
  width: 350px;
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.relation-title {
  width: calc(100% - 30px);
  display: flex;
  align-items: center;
  & > span {
    flex: 1;
    font-weight: 700;
  }
  & > span::before {
    content: ' ';
    position: relative;
    left: 0px;
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 4px;
    box-sizing: content-box;
    border-width: 3px;
    border-style: solid;
    border-color: rgb(255, 156, 0);
    border-image: initial;
    background: rgb(255, 255, 255);
    border-radius: 50%;
  }
}
</style>
