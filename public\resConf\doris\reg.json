{"forms": [{"type": "input", "prop": "title", "label": "名称", "componentProps": {"maxlength": 30, "placeholder": "请输入名称", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入名称", "trigger": "blur"}, {"min": 2, "max": 30, "message": "长度在 2 到 30 个字符", "trigger": "blur"}]}, {"type": "textarea", "prop": "url", "label": "服务地址", "componentProps": {"rows": 3, "maxlength": 1024, "placeholder": "Doris FE http 地址，支持多个地址，使用逗号分隔格式为：${ip}:${port}"}, "rules": [{"required": true, "message": "请输入Doris FE节点地址, 格式为：${ip}:${port}", "trigger": "blur"}, {"pattern": "/^\\S*$/", "message": "Doris FE http 地址中不能有空格或换行", "trigger": "blur"}]}, {"type": "input", "prop": "database", "label": "数据库名称", "componentProps": {"maxlength": 30, "showWordLimit": true, "placeholder": "请输入数据库名称"}, "rules": [{"required": true, "message": "请输入数据库名称", "trigger": "blur"}]}, {"type": "input", "prop": "username", "label": "用户名", "componentProps": {"maxlength": 30, "showWordLimit": true, "placeholder": "请输入用户名"}, "rules": [{"required": true, "message": "请输入用户名", "trigger": "blur"}]}, {"type": "password", "prop": "password", "label": "密码", "componentProps": {"maxlength": 100, "placeholder": "请输入密码"}}, {"type": "textarea", "prop": "memo", "label": "备注", "componentProps": {"rows": 5, "maxlength": 255, "placeholder": "请输入备注"}}]}