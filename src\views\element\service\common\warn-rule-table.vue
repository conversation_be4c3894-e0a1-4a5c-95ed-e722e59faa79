<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">预警规则</div>
      <div class="bs-page__header-operation">
        <el-button type="primary" size="small" @click="getWarnRuleList">刷新</el-button>
      </div>
    </div>
    <div class="tab-content" :style="{ height: height }">
      <base-table
        v-loading="tableLoading"
        :table-data="nodeTableData"
        :table-config="nodeTableConfig"
      />
    </div>
    <rule-edit
      :visible="dialogVisible"
      :title="dialogTitle"
      :data="recordData"
      @close="closeDialog"
    />
    <warn-record-list :visible="wrDialogVisible" :data="wrRecordData" @close="wrCloseDialog" />
  </div>
</template>
<script lang="ts">
import * as _ from 'lodash';
import { Component, Prop, Inject } from 'vue-property-decorator';
import {
  URL_WARNRULE_LISTBYRESID,
  URL_WARNRULE_FIND,
  URL_WARNRULE_UPDATESTATE
} from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
import baseTable from '@/components/base-table.vue';
import ruleEdit from '@/views/monitor/warning-rule/modals/edit.vue';
import warnRecordList from '@/views/monitor/warning-record/modals/list.vue';
@Component({
  components: {
    baseTable,
    ruleEdit,
    warnRecordList
  }
})
export default class WarnRuleTable extends PaBase {
  @Prop({ default: false }) fromFlow!: boolean;
  @Prop({ default: '' }) flowId!: string;
  height = '300px';
  private dialogVisible = false;
  private dialogTitle = '编辑';
  private recordData: any = {};
  private tableLoading = false;
  private nodeTableData: ITableData = {
    columnData: [],
    tableData: []
  };
  private nodeTableConfig: ITableConfig = {
    width: 150,
    columnsExtend: {
      edit: [
        {
          tipMessage: '编辑',
          handler: this.edit.bind(this),
          iconfont: 'icon-bianji',
          hasAuthority: this.hasAuthority.bind(
            this,
            this.hasFeatureAuthority('PA.MONITOR.WARN.EDIT')
          )
        },
        {
          tipMessage: '详情',
          handler: this.detail.bind(this),
          iconfont: 'icon-chakan',
          hasAuthority: this.hasAuthority.bind(
            this,
            this.hasFeatureAuthority('PA.MONITOR.WARN.RECORD_LIST')
          )
        },
        {
          tipMessage: '启用',
          handler: this.changeState.bind(this, 'ON'),
          iconfont: 'icon-qidong',
          hasAuthority: this.hasAuthority.bind(this, this.hasFeatureAuthority('PA.MONITOR.WARN.ON'))
        },
        {
          tipMessage: '关闭',
          handler: this.changeState.bind(this, 'OFF'),
          iconfont: 'icon-guanbi',
          hasAuthority: this.hasAuthority.bind(
            this,
            this.hasFeatureAuthority('PA.MONITOR.WARN.OFF')
          )
        }
      ]
    }
  };
  private wrDialogVisible = false;
  private wrRecordData: any = {};
  private resRecord: any = {};
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  created() {
    if (this.fromFlow) {
      return;
    }
    this.loadData(this.comDetailRecord.val || {}, this.comParams.WarnRuleTable || {});
  }
  async loadData(data: any, params: any) {
    this.resRecord = data;
    this.height = params.height;
    this.getWarnRuleList();
  }

  changeState(stateType: string, row: any) {
    this.tableLoading = true;
    this.doPut(URL_WARNRULE_UPDATESTATE, [
      {
        id: row.id,
        stateType
      }
    ]).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.$set(row, 'stateType', this.midState(stateType));
      });
      this.tableLoading = false;
    });
  }
  midState(stateType: string) {
    if (stateType === 'OFF') {
      return 'OFF';
    } else if (stateType === 'ON') {
      return 'ON';
    }
  }
  hasAuthority(hasRole: any, row: any) {
    return !_.isEmpty(_.toString(row.dataLevelType)) && row.dataLevelType !== 'PARENT' && hasRole;
  }
  edit(row: any) {
    this.dialogTitle = '编辑';
    const params = { id: row.id };
    this.doGet(URL_WARNRULE_FIND, { params: params }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.dialogVisible = true;
        this.recordData = resp.data;
      });
    });
  }
  detail(row: any) {
    this.wrDialogVisible = true;
    this.wrRecordData = row;
  }
  wrCloseDialog() {
    this.wrDialogVisible = false;
  }
  closeDialog(needFresh: any) {
    if (needFresh === true) {
      this.getWarnRuleList();
    }
    this.dialogVisible = false;
  }

  getWarnRuleList() {
    this.tableLoading = true;
    this.doGet(URL_WARNRULE_LISTBYRESID, {
      params: {
        resId: this.flowId || this.$route.query.id
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.nodeTableData = resp.data;
      });
      this.tableLoading = false;
    });
  }
}
</script>
<style scoped>
::v-deep .el-table--fit {
  overflow: auto !important;
}
</style>
