<template>
  <bs-dialog
    v-loading="loading"
    size="medium"
    :title="$t('pa.fieldInfo')"
    class="field-dialog"
    :visible.sync="display"
    @confirm="handleConfirm"
  >
    <!-- search -->
    <div class="field-search" :style="style">
      <bs-search v-model="search" :placeholder="$t('pa.placeholder.fieldName')" @search="handleSearch" />
    </div>
    <!-- table -->
    <bs-table
      crossing
      selection
      :height="height"
      :data="tableData"
      row-key="columnName"
      :page-data="pageData"
      :column-settings="false"
      :column-data="columnData"
      @page-change="handlePageChange"
      @selection-change="handelSelectionChange"
    />
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { getFieldList } from '@/apis/serviceApi';
import { safeArray } from '@/utils';

@Component
export default class FieldDialog extends Vue {
  @PropSync('show', { type: Boolean }) display!: boolean;
  @Prop({ default: '' }) tableName!: string;
  @Prop({ default: '' }) type!: string;
  @Prop({ default: '' }) id!: string;

  loading = false;
  search = '';
  columnData: any[] = [];
  tableData: any[] = [];
  pageData: any = {
    total: 0,
    currentPage: 1,
    pageSize: this.$store.getters.pageSize
  };
  checkedRows: any[] = [];
  total = 0;

  get height() {
    return this.checkedRows.length ? 300 : 370;
  }
  get style() {
    return {
      'margin-bottom': this.checkedRows.length ? '-8px' : '10px'
    };
  }

  created() {
    this.getFieldList();
  }

  async getFieldList() {
    try {
      this.loading = true;
      const { success, data, error } = await getFieldList(this.type, this.id, this.tableName, this.search, this.pageData);
      if (!success) return this.$message.error(error);
      this.columnData = safeArray(data?.columnData);
      this.tableData = safeArray(data?.tableData);
      this.pageData.total = data?.pageData?.total || 0;
      this.total = this.pageData.total;
    } finally {
      this.loading = false;
    }
  }
  handleSearch() {
    this.pageData.currentPage = 1;
    this.getFieldList();
  }
  handlePageChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    this.getFieldList();
  }
  handelSelectionChange(selection: any[]) {
    this.checkedRows = selection;
  }
  handleConfirm() {
    let str = '*';
    if (this.checkedRows.length > 0 && this.checkedRows.length < this.total) {
      str = this.checkedRows.map(({ columnName }) => columnName).join();
    }
    this.$emit('confirm', str);
    this.display = false;
  }
}
</script>

<style lang="scss" scoped>
.field {
  &-dialog {
    ::v-deep .el-dialog__body {
      padding: 0;
    }
  }
  &-search {
    padding: 10px 20px;
    text-align: right;
  }
}
</style>
