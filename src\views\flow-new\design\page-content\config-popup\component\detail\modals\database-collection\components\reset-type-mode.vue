<template>
  <div class="time__container">
    <el-form-item v-for="el in renderList" :key="el.prop" :prop="el.prop" :label="el.label" :rules="el.rules">
      <bs-select
        v-if="el.options"
        v-model="formData[el.prop]"
        class="database-item__select"
        clearable
        filterable
        :options="el.options"
        :placeholder="$t('pa.placeholder.select')"
      />
      <el-input v-else v-model="formData[el.prop]" />
    </el-form-item>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import type { FormData } from '../type';

@Component
export default class ResetTypeMode extends Vue {
  @Prop({ default: () => ({}) }) formData!: FormData;
  @Prop({ default: () => [] }) renderList!: any[];
}
</script>
<style lang="scss" scoped>
.time {
  &__container {
    display: flex;
    align-items: center;
    ::v-deep .el-form-item {
      &__label {
        display: inline-block;
        width: 50px !important;
        float: none;
        text-align: left;
        padding: 0 0 10px;
      }
      &__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
