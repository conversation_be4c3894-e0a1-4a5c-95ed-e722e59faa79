import Vue from 'vue';
import { baseUrl } from '@/config';
import { URL_SESSION_USER } from '@/apis/commonApi';
import { GET_USER_INFO } from '@/store/event-names/actions';
import { ADD_USER_INFO } from '@/store/event-names/mutations';
import changeThemeColor from 'bs-ui-pro/lib/utils/theme-color';

const userInfo: IUserInfo = {
  userName: '',
  authorities: [],
  mobile: '',
  roleNames: [],
  userDisplayName: '',
  orgId: ''
};

export default {
  namespaced: false,
  state: () => userInfo,
  mutations: {
    [ADD_USER_INFO](state: IState, payload: any) {
      const assignList: any[] = [
        {
          key: 'authorities',
          defaultVal: []
        },
        {
          key: 'mobile',
          defaultVal: ''
        },
        {
          key: 'roleNames',
          defaultVal: []
        },
        {
          key: 'username',
          defaultVal: ''
        },
        {
          key: 'realname',
          showKey: 'userDisplayName',
          defaultVal: ''
        },
        {
          key: 'orgId',
          defaultVal: ''
        },
        {
          key: 'themeColor',
          defaultVal: ''
        },
        {
          key: 'backgroundColor',
          defaultVal: ''
        },
        {
          key: 'checkBottomColor',
          defaultVal: ''
        }
      ];
      assignList.forEach(({ key, showKey, defaultVal }: any) => {
        if (key === 'username') {
          state['userName'] = payload[key] || defaultVal;
        } else {
          state[key] = payload[showKey || key] || defaultVal;
        }
      });
    }
  },
  actions: {
    async [GET_USER_INFO]({ commit, rootState }) {
      try {
        const { data } = await Vue.axios.get(URL_SESSION_USER);
        console.log('得到的用户信息为: ', data);
        if (data) {
          commit(ADD_USER_INFO, data);
          rootState.others.isFuseMode && data.themeColor && changeThemeColor(data.themeColor);
          // 处理assets升级后store结构变更的兼容
          rootState.user && (rootState.user.access = data.authorities || []);
          return data;
        }
        location.href = baseUrl.loginUrl;
        return Promise.reject(new Error('the interface named session/user is no content'));
      } catch (e) {
        return Promise.reject(e);
      }
    }
  },
  getters: {
    menuBackgroundColor(state, t, rootState) {
      return rootState.others.isFuseMode ? state.backgroundColor || '#001753' : '#001753';
    },
    menuActiveColor(state, t, rootState) {
      return rootState.others.isFuseMode
        ? state.checkBottomColor || state.themeColor || '#377CFF'
        : '#377CFF';
    }
  }
};
