<template>
  <el-collapse v-model="formData.expand" accordion class="main__container">
    <!-- v-if="judgeShowRule(el, true)" -->
    <el-collapse-item :name="name" class="main-item">
      <!-- 标题 -->
      <div slot="title" class="main-item__title">{{ title }}</div>
      <!-- 表格 -->
      <el-table :data="formData.tableData" border>
        <el-table-column
          v-for="item in formData.tableHead"
          :key="item.prop"
          :label="item.label"
          :min-width="item.minWidth"
          :prop="item.prop"
          :width="item.width"
        >
          <template v-slot="scope">
            <!-- 下拉框 -->
            <select-type
              v-if="item.type === 'select'"
              :data.sync="formData"
              :index="scope.$index"
              :field-list="fieldList"
            />
            <!-- 输入框 -->
            <input-type
              v-else-if="item.type === 'input'"
              :data.sync="formData"
              :row-data="scope.row"
            />
            <!-- default -->
            <div v-else>{{ scope.row[item.prop] }}</div>
          </template>
        </el-table-column>
      </el-table>
    </el-collapse-item>
  </el-collapse>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
// import { debounce } from 'lodash';
import SelectType from './select-type.vue';
import InputType from './input-type.vue';

@Component({ components: { SelectType, InputType } })
export default class LogicMain extends Vue {
  @Prop({ default: 0 }) index!: number;
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => [] }) fieldList!: any[];
  @PropSync('data', { default: () => ({}) }) formData!: any;

  private activeName: any[] = [];

  get name() {
    return this.formData.name || '';
  }

  get isDefaultFn() {
    return this.formData.funcType === 'DEFAULT';
  }

  get title() {
    return this.isDefaultFn ? '条件配置' : '参数映射';
  }

  created() {
    this.formData.expand = this.name;
  }
}
</script>
<style lang="scss" scoped>
.main {
  &__container {
    border-bottom: 0;
    ::v-deep .el-collapse {
      &-item {
        &__content {
          padding: 0;
        }

        &__header {
          background: transparent;
          border: 0;
        }
      }
    }
  }
  &-item {
    &__title {
      font-weight: bold;
      color: #444444;
    }
  }
}
</style>
