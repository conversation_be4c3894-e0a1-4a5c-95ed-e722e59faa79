// 后期引入bs-dialog可以删除
.el-dialog {
  & &__header {
    padding-top: 10px;
  }
  & &__title {
    color: #444;
    line-height: 27px;
    font-size: 14px;
    font-weight: 700;
  }
  & &__footer {
    padding-bottom: 10px;
  }
}

.ivu-message {
  z-index: 2005 !important;
}
.drag-drawer {
  cursor: col-resize;
  height: 100%;
  width: 10px;
  background: rgba(240, 240, 240, 0.7);
  border-radius: 3px;
}
.drag-drawer::after {
  content: '';
  position: absolute;
  pointer-events: none;
  top: 39.7%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 1;
  width: 2px;
  height: 2px;
  border-radius: 100%;
  display: flex;
  flex-direction: column;
  background: currentcolor;
  box-shadow: currentcolor 0px 8px 0px, currentcolor 0px 16px 0px, currentcolor 0px 24px 0px, currentcolor 0px 32px 0px,
    currentcolor 0px 40px 0px;
  transition: background-color 0.3s ease 0s, box-shadow 0.3s ease 0s;
}
.drag-drawer:hover {
  background: rgba(210, 210, 210, 0.922);
  color: #fff;
}
.drag-row--resize {
  position: absolute;
  top: 0;
  width: 100%;
  height: 10px;
  cursor: row-resize;
  border-radius: 3px;
  background: rgba(240, 240, 240, 0.7);
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 2px;
    border-radius: 100%;
    background: currentcolor;
    box-shadow: 0px 0px 0px 0px, 8px 0px 0px 0px, 16px 0px 0px 0px, 24px 0px 0px 0px, 32px 0px 0px 0px, 40px 0px 0px 0px;
  }
  &:hover {
    background: rgba(210, 210, 210, 0.922);
    color: #fff;
  }
}
