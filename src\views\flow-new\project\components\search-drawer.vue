<template>
  <el-drawer
    v-drawerDrag
    :title="$t('pa.flow.searchAll')"
    class="search-drawer"
    size="400px"
    :modal="false"
    :modal-append-to-body="false"
    :visible.sync="drawerVisible"
    :before-close="handleDrawerClose"
    @keyup.native.enter="handleSearch"
  >
    <div class="search-drawer__content">
      <div class="search-drawer__content--header">
        <bs-search
          v-model.trim="params.keyword"
          class="search-drawer__search"
          clearable
          :auto-clear-value="false"
          :placeholder="$t('pa.flow.placeholder0')"
          :select-value="params.queryType"
          :options="optionList"
          @change="handleTypeChange"
        />
      </div>
      <search-result ref="searchResult" :visible="drawerVisible" :params="params" @close="handleDrawerClose" />
    </div>
  </el-drawer>
</template>

<script lang="ts">
import { Vue, Component, PropSync } from 'vue-property-decorator';
import SearchResult from './search-result.vue';
@Component({
  components: {
    SearchResult
  }
})
export default class SearchDrawer extends Vue {
  @PropSync('visible') drawerVisible!: boolean;
  private optionList: object[] = [
    { label: this.$t('pa.flow.all'), value: 'ALL' },
    { label: this.$t('pa.flow.project'), value: 'PROJECT ' },
    { label: this.$t('pa.flow.flow'), value: 'JOB' }
  ];
  private params = {
    queryType: 'ALL',
    keyword: ''
  };
  handleSearch() {
    (this.$refs.searchResult as HTMLFormElement).getSearchResultList(true);
  }
  handleDrawerClose() {
    this.drawerVisible = false;
  }
  handleTypeChange(type) {
    this.params.queryType = Object.keys(type)[0];
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__header {
  font-size: 14px;
  font-weight: 500;
  color: #444444;
  line-height: 20px;
  padding-bottom: 20px;
  padding-left: 25px;
  margin-bottom: 0px;
  border-bottom: 1px solid #f5f7f9;
}
::v-deep .el-input__inner {
  border-radius: 0px;
}
::v-deep .el-drawer__body {
  overflow: hidden;
}
::v-deep .el-input-group {
  width: 100% !important;
}
.search-drawer {
  overflow: hidden;
  &__content {
    display: flex;
    flex-direction: column;
    &--header {
      width: 100%;
      display: flex;
      background: #fff;
      align-items: center;
      margin-left: 20px;
      padding-right: -10px;
      ::v-deep .el-input__clear {
        position: relative;
        top: -32px;
        right: 30px;
      }
    }
  }
  &__search {
    padding: 20px 0 18px 0;
    width: calc(100% - 35px) !important;
  }
  ::v-deep .el-tabs__nav-scroll {
    border-top: 1px solid #f2f4f7;
  }
  ::v-deep .bs-pro-page .el-tabs__header {
    line-height: 45px;
  }
}
</style>
