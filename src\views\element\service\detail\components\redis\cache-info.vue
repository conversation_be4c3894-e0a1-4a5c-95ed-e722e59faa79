<template>
  <pro-grid v-loading="loading" type="info" :title="$t('pa.cacheInfo')">
    <!-- operation -->
    <el-button slot="operation" type="primary" @click="getCacheInfoData">{{ $t('pa.action.refresh') }}</el-button>
    <!-- main -->
    <div class="cache-info-body" :style="{ height }">
      <el-tabs v-if="result" v-loading="loading">
        <el-tab-pane v-for="(value, key) in result" :key="key" :label="key">
          <div style="height: 416px; overflow: auto; flex-wrap: wrap; padding-left: 20px">
            <pre v-for="(valueKey, subKey) in value" :key="subKey">
            <pre style="font-weight:bold">{{ subKey }}:</pre>{{ valueKey }}</pre>
          </div>
        </el-tab-pane>
      </el-tabs>
      <bs-empty v-else />
    </div>
  </pro-grid>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getServerCacheInfo } from '@/apis/serviceApi';

@Component
export default class RedisCacheInfo extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: () => ({}) }) params!: any;

  loading = false;
  id = '';
  result: any = null;

  get height() {
    return this.params?.height || '300px';
  }
  created() {
    this.id = this.$route.query.id as string;
    this.getCacheInfoData();
  }
  async getCacheInfoData() {
    this.loading = true;
    try {
      const { success, data, error } = await getServerCacheInfo('redis', this.id);
      if (!success) return this.$message.error(error);
      this.result = data;
    } finally {
      this.loading = false;
    }
  }
}
</script>
<style lang="scss" scoped>
.cache-info-body {
  padding: 0 20px;
  width: 100%;
}
.my-card_body ::v-deep .el-card__body {
  padding: 0px 0px 0px 0px;
}
.my-wrap-row {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
}
.my-wrap-col {
  border: 1px solid #000;
}
.my-wrap-tag {
  width: 350px;
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
pre {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}
</style>
