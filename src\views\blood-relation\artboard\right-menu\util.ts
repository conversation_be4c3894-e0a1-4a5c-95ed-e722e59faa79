import { G, SVG } from '@svgdotjs/svg.js';
import { debounce } from 'lodash';

export const upMapping = {
  JOB: [
    { value: 'upResource', label: '资源' },
    { value: 'upTable', label: '表' },
    { value: 'upView', label: '视图' }
  ],
  SERVICE: [{ value: 'upFlow', label: '流程' }],
  TABLE: [
    { value: 'upFlow', label: '流程' },
    { value: 'upResource', label: '资源' }
  ],
  VIEW: [{ value: 'upTable', label: '表' }]
};
export const downMapping = {
  JOB: [
    { value: 'downResource', label: '资源' },
    { value: 'downTable', label: '表' }
  ],

  SERVICE: [
    { value: 'downFlow', label: '流程' },
    { value: 'downTable', label: '表' }
  ],
  TABLE: [
    { value: 'downFlow', label: '流程' },
    { value: 'downView', label: '视图' }
  ],
  VIEW: [{ value: 'downFlow', label: '流程' }]
};

const HomologyTypes: string[] = ['SERVICE', 'TABLE'];

const generateNextMenus = (value: string, type: string, canUseSql = false) => {
  let result = value === 'up' ? upMapping[type] : downMapping[type];
  if (!canUseSql)
    result = result.filter(
      ({ value }) => !['upTable', 'upView', 'downTable', 'downView'].includes(value)
    );
  return [result.length > 1 && { value, label: '全部' }, ...result].filter(Boolean);
};
const generateGetHomologyMenu = (type: string, tags: any[] = []) => {
  if (!HomologyTypes.includes(type)) return;
  return { value: 'getHomology', label: `${tags.includes('同') ? '更新' : '获取'}同源` };
};
const generateHideHomologyMenu = (tags: any[] = [], showHomology = false) => {
  if (!tags.includes('同')) return;
  return { value: 'hideHomology', label: `${showHomology ? '显示' : '隐藏'}同源` };
};
export const generateNodeMenus = ({ type, tags, showHomology }: any = {}, canUseSql = false) => {
  return [
    {
      value: 'up',
      label: '向上扩散',
      children: generateNextMenus('up', type, canUseSql)
    },
    {
      value: 'down',
      label: ' 向下扩散',
      children: generateNextMenus('down', type, canUseSql)
    },
    generateGetHomologyMenu(type, tags),
    generateHideHomologyMenu(tags, showHomology),
    { value: 'delete', label: '删除' }
  ].filter(Boolean);
};

export const generateEdgeMenus = () => [
  {
    value: 'detail',
    label: '详情',
    visible: true
  }
];
export const getZoom = (zoom: number) => {
  if (zoom > 2) return 1.2;
  return zoom - 0.6 < 1 ? 1 : zoom - 0.6;
};

interface RadiusMenuOptions {
  el: string; // svg容器
  menuItems: any[]; // 菜单项
  width?: number; // svg的宽
  height?: number; // svg的高
}
export class RadiusMenu {
  $el;
  $menuItems; // 菜单项
  $draw;
  $outR = 120; // 外圆半径
  $innerR = 26; // 内圆半径
  $diffR = 27; // 外圆半径与背景圆半径的差值
  $bgR = this.$outR + this.$diffR; // 背景圆半径
  $sectorSpace = this.$outR * 0.02;
  $sectorCount; // 菜单数量
  $scale = 1; // 缩放倍数
  $clickCallback: any = null;
  $defW = 420; // 默认宽
  $defH = 420; // 默认高
  constructor(opts: RadiusMenuOptions) {
    this.$el = opts.el;
    this.$menuItems = opts.menuItems;
    this.$sectorCount = opts.menuItems.length;
    SVG(this.$el).clear();
    this.$draw = SVG()
      .addTo(this.$el)
      .size(opts.width || this.$defW, opts.height || this.$defH);
  }
  drawSvg(outR?: number, innerR?: number) {
    if (outR) {
      this.$outR = outR;
      this.$bgR = outR + this.$diffR;
    }
    if (innerR) {
      this.$innerR = Number(this.numberToString(innerR * 1.1));
    }
    this.$scale = this.calcScale();
    this.$draw.viewbox(-this.$defW / 2, -this.$defH / 2, this.$defW, this.$defH);
    this.drawBg();
    const angleStep = 360 / this.$sectorCount;
    const angleShift = 180;
    this.drawSector(angleStep, angleShift, 0);
  }
  drawBg() {
    const diffW = this.$bgR - this.$innerR;
    const g = this.$draw.group();
    g.circle()
      .fill('transparent')
      .stroke({
        width: diffW * (1 / this.$scale),
        color: '#737278'
      })
      .attr({
        r: (this.$innerR + diffW / 2) * (1 / this.$scale)
      });
    g.addClass('bg');

    const circleDotG = this.$draw.group();
    circleDotG.svg(
      `<defs>
            <linearGradient x1="100%" y1="73.2762827%" x2="68.4253351%" y2="34.3200252%" id="linearGradient-1">
                <stop stop-color="#FFFFFF" offset="0%"></stop>
                <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="1.82430686%" y1="86.7079192%" x2="63.0152094%" y2="86.7079192%" id="linearGradient-2">
                <stop stop-color="#FFFFFF" offset="0%"></stop>
                <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="54.4185112%" y1="0%" x2="45.5814888%" y2="50%" id="linearGradient-3">
                <stop stop-color="#FFFFFF" offset="0%"></stop>
                <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
            </linearGradient>
        </defs>
        <g id="v4.0_6" class="dot-g" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="6-右击面板" transform="translate(-992.000000, -411.000000)">
                <g id="编组-9" transform="translate(979.000000, 398.000000)">
                    <g id="旋转-圈" transform="translate(13.000000, 13.000000)">
                        <circle id="椭圆形" cx="134" cy="134" r="134"></circle>
                        <g id="编组-12" transform="translate(0.000000, 0.000000)">
                            <path d="M134,0 C208.006156,0 268,59.9938435 268,134 L267.987609,135.695282 C267.995794,135.76715 268,135.840227 268,135.914286 C268,136.971517 267.142945,137.828571 266.085714,137.828571 C265.076539,137.828571 264.249756,137.04766 264.176679,136.057151 L264.171429,135.913325 L264.171429,135.913325 L264.157647,135.913748 C264.166824,135.276928 264.171429,134.639002 264.171429,134 C264.171429,62.1083051 205.891695,3.82857143 134,3.82857143 C109.873386,3.82857143 87.27982,10.3923343 67.9075944,21.8315692 L65.9925227,18.5154828 C85.9281059,6.7503063 109.175744,0 134,0 Z" id="路径" fill="url(#linearGradient-1)"></path>
                            <path d="M267.991895,134.956186 L268,134 C268,208.006156 208.006156,268 134,268 C109.688978,268 86.8900554,261.525929 67.2332407,250.207795 C66.6569149,249.876041 66.267657,249.254458 66.267657,248.541959 C66.267657,247.484728 67.1247119,246.627673 68.1819427,246.627673 C68.5067033,246.627673 68.8125754,246.708545 69.0805189,246.851248 L69.1483035,246.891857 C88.2418074,257.883965 110.386646,264.171429 134,264.171429 C205.572354,264.171429 263.653406,206.408299 264.167983,134.957214 L267.991895,134.956186 Z" id="路径" fill="url(#linearGradient-2)"></path>
                            <path d="M66.9784001,18.2498764 C68.0356309,18.2498764 68.8926858,19.1069313 68.8926858,20.1641621 C68.8926858,20.9139875 68.4615747,21.5631214 67.8336977,21.8772186 L67.9784092,21.7897834 C29.5867685,44.4272041 3.82857143,86.2055274 3.82857143,134 C3.82857143,182.129702 29.9493709,224.158565 68.7881045,246.683723 L66.873793,250.000087 C26.8907107,226.812946 0,183.546804 0,134 C0,84.7886851 26.5277811,41.7732655 66.0634027,18.4736817 C66.3380763,18.3333318 66.6484922,18.2498764 66.9784001,18.2498764 Z" id="路径" fill="url(#linearGradient-3)"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>`
    );
    circleDotG.move(-134, -134);
    circleDotG.transform({
      scale: 1 / this.$scale
    });
    circleDotG.addClass('circle-dot');
  }
  drawSector(angleStep: number, angleShift: number, i: number) {
    const startAngle = angleShift - angleStep * i;
    const endAngle = angleShift - angleStep * (i + 1);
    this.appendSectorPath(startAngle, endAngle, this.$menuItems[i], i);
    if (i + 1 < this.$sectorCount) {
      setTimeout(() => {
        this.drawSector(angleStep, angleShift, i + 1);
      }, 20 + (this.$sectorCount - (i + 1)));
    }
  }
  drawSubSector(
    startAngleDeg: number,
    stepDeg: number,
    outR: number,
    innerR: number,
    fatherG: G,
    subMenus: any[],
    i: number
  ) {
    const startDeg = startAngleDeg + stepDeg * i;
    const endDeg = startAngleDeg + stepDeg * (i + 1);
    this.drawSingleSub(startDeg, endDeg, outR, innerR, fatherG, subMenus[i]);
    if (i + 1 < subMenus.length) {
      setTimeout(() => {
        this.drawSubSector(startAngleDeg, stepDeg, outR, innerR, fatherG, subMenus, i + 1);
      }, 25);
    }
  }
  appendSubSectorPath(startAngleDeg: number, endAngleDeg: number, item: any, fatherG: G) {
    if (item.children) {
      const outR = this.$outR + 70;
      const innerR = this.$outR * (1 / this.$scale);
      const diffDeg = endAngleDeg - startAngleDeg;
      const stepDeg = diffDeg / item.children.length;
      this.drawSubSector(startAngleDeg, stepDeg, outR, innerR, fatherG, item.children, 0);
    }
  }
  drawSingleSub(
    startDeg: number,
    endDeg: number,
    outR: number,
    innerR: number,
    fatherG: G,
    item: any
  ) {
    // const centerPoint = this.getSectorCenter(startDeg, endDeg, outR, innerR)
    const g = this.$draw.group();
    g.attr({
      transform: `scale(${this.$scale})`
    });
    const d = this.createSectorCmds(startDeg, endDeg, outR, innerR);
    g.path(d).fill({
      color: '#737278'
    });
    // if (fatherG.findOne('.line-path')) {
    const ld = this.createLinePath(startDeg, endDeg, outR, innerR);
    g.path(ld)
      .addClass('line-path')
      .stroke({
        color: '#fff',
        width: 2
      })
      .attr({
        opacity: 0
      })
      .animate(50, 50)
      .attr({
        opacity: 1
      });
    // }
    if (item) {
      const labelIconPoint = this.getSectorCenter(startDeg, endDeg, outR + 10, innerR + 10);
      if (item.label) {
        const text = g.text(item.label);
        text
          .attr({
            'text-anchor': 'middle',
            x: this.numberToString(labelIconPoint.x),
            y: this.numberToString(labelIconPoint.y),
            'font-size': 16
          })
          .fill({
            color: '#ffffff'
          });
        if (item.icon) {
          text.transform({
            translate: {
              x: 0,
              y: 12
            }
          });
        } else {
          text.transform({
            translate: {
              x: 5,
              y: 5
            }
          });
        }
        text
          .attr({
            opacity: 0
          })
          .animate(50, 50)
          .attr({
            opacity: 1
          });
      }
      if (item.icon) {
        const use = g.use(item.icon);
        use
          .attr({
            x: this.numberToString(labelIconPoint.x),
            y: this.numberToString(labelIconPoint.y),
            width: 16,
            height: 16
          })
          .fill({
            color: '#ffffff'
          });
        if (item.label) {
          use.transform({
            translate: {
              x: -8,
              y: -18
            }
          });
        } else {
          use.transform({
            translate: {
              x: -5,
              y: -5
            }
          });
        }
      }
    }
    g.click(() => {
      if (this.$clickCallback) {
        this.$clickCallback(item);
      }
    });
    g.attr({
      opacity: 0
    })
      .animate(50, 50)
      .attr({
        opacity: 1
      });
    fatherG.add(g);
    g.addClass('sub-menu-item');
  }
  appendSectorPath(startAngleDeg: number, endAngleDeg: number, item: any, i: number) {
    const centerPoint = this.getSectorCenter(startAngleDeg, endAngleDeg, this.$outR, this.$innerR);
    const translate = {
      x: this.numberToString((1 - this.$scale) * centerPoint.x),
      y: this.numberToString((1 - this.$scale) * centerPoint.y)
    };
    const g = this.$draw.group();
    g.addClass('menu-item');
    if (i === 0) {
      g.addClass('menu-item-first');
    }
    g.attr({
      transform: `translate(${translate.x},${translate.y}) scale(${this.$scale})`
    });
    const d = this.createSectorCmds(startAngleDeg, endAngleDeg, this.$outR, this.$innerR);
    g.path(d).fill({
      color: '#737278'
    });
    g.polyline(this.createPolyline(startAngleDeg, endAngleDeg, this.$outR)).fill({
      color: 'transparent'
    });
    if (item) {
      const labelIconPoint = this.getSectorCenter(
        startAngleDeg,
        endAngleDeg,
        this.$outR + 10,
        this.$innerR + 10
      );
      if (item.label) {
        const text = g.text(item.label);
        text
          .attr({
            'text-anchor': 'middle',
            x: this.numberToString(labelIconPoint.x),
            y: this.numberToString(labelIconPoint.y),
            'font-size': 16
          })
          .fill({
            color: '#ffffff'
          });
        if (item.icon) {
          let x = 0;
          if (item.label.length > 2) {
            x = 6;
          }
          text.transform({
            translate: {
              x,
              y: 14
            }
          });
        } else {
          text.transform({
            translate: {
              x: 0,
              y: 8
            }
          });
        }
      }
      if (item.icon) {
        const use = g.use(item.icon);
        use
          .attr({
            x: this.numberToString(labelIconPoint.x),
            y: this.numberToString(labelIconPoint.y),
            width: 18.76,
            height: 18.76
          })
          .fill({
            color: '#ffffff'
          });
        if (item.label) {
          let x = -9.38;
          if (item.label.length > 2) {
            x = -4.69;
          }
          use.transform({
            translate: {
              x,
              y: -9.38 * 2
            }
          });
        } else {
          use.transform({
            translate: {
              x: -9.38,
              y: -9.38
            }
          });
        }
      }
    }
    if (!item.children) {
      g.click(() => {
        if (this.$clickCallback) {
          this.$clickCallback(item);
        }
      });
    } else {
      g.mouseenter(() => {
        const subG = g.findOne('.sub-menu-item');
        if (!subG) {
          this.appendSubSectorPath(startAngleDeg, endAngleDeg, item, g);
        }
      });
      g.mouseleave(
        debounce(() => {
          const subGs = g.find('.sub-menu-item');
          if (subGs.length) {
            subGs.forEach((sub) => {
              sub
                .attr({
                  opacity: 1
                })
                .animate(30)
                .attr({
                  opacity: 0
                });
              setTimeout(() => {
                sub.remove();
              }, 30);
            });
          }
        }, 100)
      );
    }
    g.attr({
      opacity: 0
    })
      .animate(20)
      .attr({
        opacity: 0.8
      });
  }
  pointToString(point: any) {
    return this.numberToString(point.x) + ' ' + this.numberToString(point.y);
  }
  createSectorCmds(startAngleDeg: number, endAngleDeg: number, outR: number, innerR: number) {
    const outRAfterScale = outR * (1 / this.$scale);

    const radiusDiff = outR - innerR;
    const radiusDelta = (radiusDiff - radiusDiff * this.$scale) / 2;
    const innerRAfterScale = (innerR + radiusDelta) * (1 / this.$scale);

    const initPoint = this.getDegreePos(startAngleDeg, outRAfterScale);

    const path = `M${this.pointToString(
      initPoint
    )} A${outRAfterScale} ${outRAfterScale} 0 0 1 ${this.pointToString(
      this.getDegreePos(endAngleDeg, outRAfterScale)
    )} L${this.pointToString(
      this.getDegreePos(endAngleDeg, innerRAfterScale)
    )} A${innerRAfterScale} ${innerRAfterScale} 0 0 0 ${this.pointToString(
      this.getDegreePos(startAngleDeg, innerRAfterScale)
    )} Z`;
    return path;
  }
  createLinePath(startAngleDeg: number, endAngleDeg: number, outR: number, innerR: number) {
    const outRAfterScale = outR * (1 / this.$scale);

    const radiusDiff = outR - innerR;
    const radiusDelta = (radiusDiff - radiusDiff * this.$scale) / 2;
    const innerRAfterScale = (innerR + radiusDelta) * (1 / this.$scale);
    const initPoint = this.getDegreePos(endAngleDeg, outRAfterScale);
    const path = `M${this.pointToString(initPoint)} L${this.pointToString(
      this.getDegreePos(endAngleDeg, innerRAfterScale)
    )} Z`;
    return path;
  }
  createPolyline(startAngleDeg: number, endAngleDeg: number, outR: number) {
    const diffh =
      this.$outR - this.$outR * Math.abs(Math.cos(((endAngleDeg - startAngleDeg) / 180) * Math.PI));
    const point1 = this.getDegreePos(startAngleDeg, outR);
    const point2 = this.getDegreePos(endAngleDeg, outR);
    const point3 = this.getDegreePos(endAngleDeg, outR + diffh);
    const point4 = this.getDegreePos(startAngleDeg, outR + diffh);
    return `${point1.x},${point1.y} ${point2.x},${point2.y} ${point3.x},${point3.y} ${point4.x},${point4.y}`;
  }
  numberToString(n: any) {
    if (Number.isInteger(n)) {
      return n.toString();
    } else if (n) {
      let r = (+n).toFixed(5);
      if (r.match(/\./)) {
        r = r.replace(/\.?0+$/, '');
      }
      return r;
    }
  }
  getSectorCenter(startAngleDeg: number, endAngleDeg: number, outR: number, innerR: number) {
    return this.getDegreePos((startAngleDeg + endAngleDeg) / 2, innerR + (outR - innerR) / 2);
  }
  getDegreePos(angleDeg: number, r: number) {
    return {
      x: Math.sin(this.degToRad(angleDeg)) * r,
      y: Math.cos(this.degToRad(angleDeg)) * r
    };
  }
  degToRad(deg: number) {
    return deg * (Math.PI / 180);
  }
  calcScale() {
    const totalSpace = this.$sectorSpace * this.$sectorCount;
    const circleLength = Math.PI * 2 * this.$outR;
    const radiusDelta = this.$outR - (circleLength - totalSpace) / (Math.PI * 2);
    return (this.$outR - radiusDelta) / this.$outR;
  }
  click(callback: any) {
    this.$clickCallback = callback;
  }
}
