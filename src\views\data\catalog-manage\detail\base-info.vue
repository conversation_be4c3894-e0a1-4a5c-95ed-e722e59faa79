<template>
  <div class="base-info">
    <div v-for="item in baseInfoList" :key="item.label" class="item">{{ item.label }}：{{ item.value }}</div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
@Component
export default class BaseInfo extends Vue {
  @Prop({ default: () => {} }) baseInfo!: any;
  baseInfoList: any[] = [
    { key: 'catalogName', label: this.$t('pa.catalogName'), value: '' },
    { key: 'resType', label: this.$t('pa.data.udf.detail.type'), value: '' },
    { key: 'resName', label: this.$t('pa.home.service'), value: '' },
    { key: 'databaseName', label: this.$t('pa.databaseName'), value: '' },
    { key: 'orgName', label: this.$t('pa.addOrg'), value: '' },
    { key: 'createBy', label: this.$t('pa.creator'), value: '' },
    { key: 'createTime', label: this.$t('pa.flow.createTime'), value: '' },
    { key: 'updateBy', label: this.$t('pa.flow.updater'), value: '' },
    { key: 'updateTime', label: this.$t('pa.flow.updateTime'), value: '' }
  ];

  @Watch('baseInfo')
  baseInfoChange() {
    this.baseInfoList.forEach((item) => {
      item.value = this.baseInfo[item.key];
    });
  }
}
</script>

<style lang="scss" scoped>
.base-info {
  height: calc(100vh - 181px);
  background: $--bs-color-text-white-primary;
  padding: 20px 25px;
  .item {
    margin-bottom: 20px;
    .required {
      color: red;
      margin-right: 2px;
    }
  }
}
</style>
