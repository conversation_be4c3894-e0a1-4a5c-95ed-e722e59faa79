<template>
  <div class="cycles__container">
    <span class="cycles__span">[</span>
    <!-- 左区间 -->
    <el-form-item :prop="`${prefix}.leftInterval`" :rules="leftIntervalRule">
      <el-input-number
        v-if="!disabled"
        v-model="formData.groupCepPatternConditionList[scopeIndex].leftInterval"
        controls-position="right"
        :min="0"
        class="cycles-item"
      />
      <el-input
        v-else
        v-model="formData.groupCepPatternConditionList[scopeIndex].leftInterval"
        class="cycles-item"
        clearable
        filterable
        disabled
        size="small"
      />
    </el-form-item>
    <span class="cycles__span">，</span>
    <!-- 右区间 -->
    <el-form-item :prop="`${prefix}.rightInterval`" :rules="rightIntervalRule">
      <el-input-number
        v-if="showRightInterval"
        v-model="formData.groupCepPatternConditionList[scopeIndex].rightInterval"
        controls-position="right"
        class="cycles-item"
        :min="1"
        :disabled="disabled"
      />
      <el-input
        v-else
        v-model="formData.groupCepPatternConditionList[scopeIndex].rightInterval"
        class="cycles-item"
        clearable
        filterable
        disabled
        size="small"
      />
    </el-form-item>
    <span class="cycles__span">]</span>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class CyclesType extends Vue {
  @Prop({ type: Number }) scopeIndex!: number;
  @Prop({ type: Object, default: () => ({}) }) formData!: any;

  get leftIntervalRule() {
    return {
      required: !this.disabled,
      message: '请输入',
      trigger: 'blur'
    };
  }
  get rightIntervalRule() {
    return {
      required: !this.disabled,
      min: this.formData.groupCepPatternConditionList[this.scopeIndex].leftInterval || 0,
      validator(rule, value, callback) {
        if (!rule.required) callback();
        if (!value) callback(new Error('请输入'));
        if (value < rule.min) callback(new Error('数值要大于左侧'));
        callback();
      },
      trigger: 'blur'
    };
  }
  get prefix() {
    return `groupCepPatternConditionList[${this.scopeIndex}]`;
  }
  get isNotConneModeType() {
    return this.formData.groupCepPatternConditionList[this.scopeIndex].isNotConneModeType;
  }
  get circularRuleTimes() {
    return this.formData.groupCepPatternConditionList[this.scopeIndex].circularRuleTimes;
  }
  get disabled() {
    return this.isNotConneModeType || this.circularRuleTimes === 'ONE_OR_MORE';
  }
  get showRightInterval() {
    if (this.isNotConneModeType) return false;
    return !this.circularRuleTimes || this.circularRuleTimes === 'TIMES';
  }
}
</script>
<style lang="scss" scoped>
.cycles {
  &__container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &-item {
    width: 100px;
  }
  &__span {
    display: inline-block;
    margin: 0 10px 22px;
  }
}
</style>
