<template>
  <div class="udf page-content">
    <transition name="el-zoom-in-center">
      <div v-loading="udfDetailLoading" class="udf-content">
        <div class="first_content" style="overflow: auto">
          <!-- 表信息 -->
          <div class="detail_content">
            <div class="no-bg-color-content">
              <div class="tab-title">
                <div class="title-text">
                  <span>基本信息</span>
                </div>
              </div>
              <div class="tab-content" style="height: 100%">
                <el-row>
                  <el-col style="diplay: flex">
                    <!-- 表信息详情 -->
                    <div class="chartInfoDetail">
                      <div class="item">UDF名：{{ chartInfo.udf }}</div>
                      <div class="item">中文名：{{ chartInfo.udfCn }}</div>
                      <div class="item">类型：{{ chartInfo.type }}</div>
                      <div class="item">功能说明：{{ chartInfo.businessExplain }}</div>
                      <div class="item">UDF主类全域名：{{ chartInfo.domain }}</div>
                      <div class="item">创建人：{{ detailSource.createdBy }}</div>
                      <div class="item">创建电话：{{ detailSource.createdByMobile }}</div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { URL_UDF_FINDBYID } from '@/apis/commonApi';
import { get } from '@/apis/utils/net';
@Component
export default class UDFDetail extends Vue {
  // UDF id
  @Prop({ type: String, default: '' }) id!: string;
  // udf详情页面加载loading
  private udfDetailLoading = false;
  private chartInfo = {
    domain: '', // 域名
    udf: '',
    udfCn: '',
    businessExplain: '',
    type: ''
  };
  private detailSource: any = {};
  created() {
    this.getUdfDetailData();
  }
  async getUdfDetailData() {
    const api = URL_UDF_FINDBYID + '?id=' + this.id;
    try {
      const { data, success, msg } = await get(api);
      if (success) {
        this.detailSource = data;
        this.chartInfo.udf = data.udfName;
        this.chartInfo.udfCn = data.udfNameCn;
        this.chartInfo.domain = data.udfNameMain;
        this.chartInfo.type = data.udfType;
        this.chartInfo.businessExplain = data.udfExplain;
        return;
      }
      this.$message.error(msg);
    } catch (error) {
      return;
    }
  }
}
</script>

<style lang="scss" scoped>
.page-content {
  height: 100%;
}
.tab-content {
  border-top: 1px solid #e2e2e2;
}
.udf-content {
  .detail_content {
    text-align: left;
  }
  .chartInfoDetail {
    margin-top: 10px;
    .item {
      margin: 5px 0 18px 18px;
    }
  }
}
</style>
