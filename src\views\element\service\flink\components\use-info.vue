<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">集群资源使用情况</div>
    </div>
    <!--per job-->
    <div v-if="showMemory" class="tab-content">
      <el-row style="padding: 0px 10px">
        <el-col :span="10" style="">
          <el-form label-width="115px">
            <div class="my-wrap-row">
              <div style="width: 47%">
                <el-form-item label="集群总内存:">
                  <div class="my-wrap-tag">{{ useInfo.clusterMemory }}MB</div>
                </el-form-item>
              </div>
              <div style="width: 47%">
                <el-form-item label="机构可用内存:">
                  <div class="my-wrap-tag">{{ useInfo.orgMemory }}MB</div>
                </el-form-item>
              </div>
            </div>
            <div class="my-wrap-row">
              <div style="width: 47%">
                <el-form-item label="集群已使用内存:">
                  <div class="my-wrap-tag">{{ useInfo.clusterUsedMemory }}MB</div>
                </el-form-item>
              </div>
              <div style="width: 47%">
                <el-form-item label="机构已使用内存:">
                  <div class="my-wrap-tag">{{ useInfo.orgUsedMemory }}MB</div>
                </el-form-item>
              </div>
            </div>
            <div class="my-wrap-row">
              <div style="width: 47%">
                <el-form-item label="集群剩余内存:">
                  <div class="my-wrap-tag">{{ useInfo.clusterResidualMemory }}MB</div>
                </el-form-item>
              </div>
              <div style="width: 47%">
                <el-form-item label="机构剩余内存:">
                  <div class="my-wrap-tag">{{ useInfo.orgResidualMemory }}MB</div>
                </el-form-item>
              </div>
            </div>
            <div class="my-wrap-row">
              <div style="width: 47%">
                <el-form-item label="集群总CPU:">
                  <div class="my-wrap-tag">
                    {{ useInfo.clusterCpu }}
                  </div>
                </el-form-item>
              </div>
              <div style="width: 47%">
                <el-form-item label="机构已分配内存:">
                  <div class="my-wrap-tag">{{ useInfo.orgChildrenMemory }}MB</div>
                </el-form-item>
              </div>
            </div>
            <div class="my-wrap-row">
              <div style="width: 47%">
                <el-form-item label="集群已使用CPU:">
                  <div class="my-wrap-tag">
                    {{ useInfo.clusterUsedCpu }}
                  </div>
                </el-form-item>
              </div>
              <div style="width: 47%">
                <el-form-item label="机构可用CPU:">
                  <div class="my-wrap-tag">
                    {{ useInfo.orgCpu }}
                  </div>
                </el-form-item>
              </div>
            </div>
            <div class="my-wrap-row">
              <div style="width: 47%">
                <el-form-item label="集群剩余CPU:">
                  <div class="my-wrap-tag">
                    {{ useInfo.clusterResidualCpu }}
                  </div>
                </el-form-item>
              </div>
              <div style="width: 47%">
                <el-form-item label="机构已使用CPU:">
                  <div class="my-wrap-tag">
                    {{ useInfo.orgUsedCpu }}
                  </div>
                </el-form-item>
              </div>
            </div>
            <div class="my-wrap-row">
              <div style="width: 47%"></div>
              <div style="width: 47%">
                <el-form-item label="机构剩余CPU:">
                  <div class="my-wrap-tag">
                    {{ useInfo.orgResidualCpu }}
                  </div>
                </el-form-item>
              </div>
            </div>
            <div class="my-wrap-row">
              <div style="width: 47%"></div>
              <div style="width: 47%">
                <el-form-item label="机构已分配CPU:">
                  <div class="my-wrap-tag">
                    {{ useInfo.orgChildrenCpu }}
                  </div>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </el-col>
        <el-col :span="7" style="">
          <div ref="memory1" style="width: 100%; height: 400px">暂无数据</div>
        </el-col>
        <el-col :span="7" style="">
          <div ref="cpu1" style="width: 100%; height: 400px">暂无数据</div>
        </el-col>
      </el-row>
    </div>
    <!--其他-->
    <div v-if="showSlots" class="tab-content">
      <el-row style="padding: 0px 10px">
        <el-col :span="12" style="">
          <el-form label-width="150px">
            <div class="my-wrap-row">
              <div style="width: 47%">
                <el-form-item label="集群总slots:">
                  <div class="my-wrap-tag">
                    {{ useInfo.clusterSlots }}
                  </div>
                </el-form-item>
              </div>
              <div style="width: 47%">
                <el-form-item label="机构可用slots:">
                  <div class="my-wrap-tag">
                    {{ useInfo.orgSlots }}
                  </div>
                </el-form-item>
              </div>
            </div>
            <div class="my-wrap-row">
              <div style="width: 47%">
                <el-form-item label="集群已使用slots:">
                  <div class="my-wrap-tag">
                    {{ useInfo.clusterUsedSlots }}
                  </div>
                </el-form-item>
              </div>
              <div style="width: 47%">
                <el-form-item label="机构已使用slots:">
                  <div class="my-wrap-tag">
                    {{ useInfo.orgUsedSlots }}
                  </div>
                </el-form-item>
              </div>
            </div>
            <div class="my-wrap-row">
              <div style="width: 47%">
                <el-form-item label="集群剩余slots:">
                  <div class="my-wrap-tag">
                    {{ useInfo.clusterResidualSlots }}
                  </div>
                </el-form-item>
              </div>
              <div style="width: 47%">
                <el-form-item label="机构剩余slots:">
                  <div class="my-wrap-tag">
                    {{ useInfo.orgResidualSlots }}
                  </div>
                </el-form-item>
              </div>
            </div>
            <div class="my-wrap-row">
              <div style="width: 47%">
                <el-form-item label="task managers:">
                  <div class="my-wrap-tag">
                    {{ useInfo.taskManage }}
                  </div>
                </el-form-item>
              </div>
              <div style="width: 47%">
                <el-form-item label="机构已分配slots:">
                  <div class="my-wrap-tag">
                    {{ useInfo.orgChildrenSlots }}
                  </div>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </el-col>
        <el-col :span="12" style="">
          <div ref="slots1" style="width: 100%; height: 400px">暂无数据</div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject } from 'vue-property-decorator';
import { URL_FLINK_CLUSTERMSG } from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
import echarts from 'echarts';
@Component({
  components: {}
})
export default class FlinkUseInfo extends PaBase {
  height = '300px';
  resRecord: any = {};
  clusterType = 'YARN_PER_JOB';
  useInfo: any = {};
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;

  get showMemory() {
    return ['YARN_PER_JOB', 'YARN_APPLICATION'].includes(this.clusterType);
  }

  get showSlots() {
    return ['STANDALONE', 'YARN_SESSION'].includes(this.clusterType);
  }

  created() {
    this.loadData(this.comDetailRecord.val || {}, this.comParams.FlinkUseInfo || {});
  }
  getUseInfo() {
    if (!this.$route.query.id) {
      return;
    }
    this.doPost(URL_FLINK_CLUSTERMSG + '?clusterId=' + this.$route.query.id).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.useInfo = resp.data;
        if (this.clusterType === 'STANDALONE' || this.clusterType === 'YARN_SESSION') {
          this.drawPie3(
            'slots1',
            '机构Slots',
            'Slots',
            ['机构已使用', '机构已分配', '机构剩余'],
            [
              {
                name: '机构已使用',
                value: this.useInfo.orgUsedSlots
              },
              {
                name: '机构已分配',
                value: this.useInfo.orgChildrenSlots
              },
              {
                name: '机构剩余',
                value: this.useInfo.orgResidualSlots
              }
            ]
          );
        }
        if (this.clusterType === 'YARN_PER_JOB' || this.clusterType === 'YARN_APPLICATION') {
          this.drawPie3(
            'memory1',
            '内存',
            '内存',
            ['机构已分配', '机构已使用', '机构剩余'],
            [
              {
                name: '机构已分配',
                value: this.useInfo.orgChildrenMemory
              },
              {
                name: '机构已使用',
                value: this.useInfo.orgUsedMemory
              },
              {
                name: '机构剩余',
                value: this.useInfo.orgResidualMemory
              }
            ]
          );
          this.drawPie3(
            'cpu1',
            '机构CPU',
            'CPU',
            ['机构已分配', '机构已使用', '机构剩余'],
            [
              {
                name: '机构已分配',
                value: this.useInfo.orgChildrenCpu
              },
              {
                name: '机构已使用',
                value: this.useInfo.orgUsedCpu
              },
              {
                name: '机构剩余',
                value: this.useInfo.orgResidualCpu
              }
            ]
          );
        }
      });
    });
  }
  drawPie3(el: string, title: string, seriesName: string, legendData: any, seriesData: any) {
    const charts = echarts.init(this.$refs[el] as HTMLCanvasElement);
    charts.setOption({
      title: {
        text: title,
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)'
      },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        right: 0,
        top: 20,
        bottom: 20,
        data: legendData
      },
      series: [
        {
          name: seriesName,
          type: 'pie',
          radius: '53%',
          center: ['40%', '50%'],
          data: seriesData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    });
  }

  async loadData(data: any, params: any) {
    this.resRecord = data;
    this.height = params.height;
    const resProperty = JSON.parse(this.resRecord.resProperty);
    this.clusterType = resProperty.clusterType;
    // this.clusterType = 'YARN_PER_JOB';
    this.getUseInfo();
  }
}
</script>
<style scoped>
.tab-content ::v-deep .el-form-item {
  margin-bottom: 0;
}
.my-wrap-row {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
}
.my-wrap-col {
  border: 1px solid #000;
}
.my-wrap-tag {
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
  color: #333;
  padding: 0 10px;
  height: 32px;
}
</style>
