import { IPreviewData } from '../interface';
import i18n from '@/i18n';
export const getOverviewTips = (data: IPreviewData) => {
  const { count, statisticsDetails } = data;
  const tips: string[] = [];
  const assetsTypes = statisticsDetails.filter((el) => el.count);
  assetsTypes.forEach((el) => {
    tips.push(`${el.label}${el.count}${i18n.t('pa.flow.tiao')}`);
  });
  return count
    ? `${i18n.t('pa.flow.total')}${count}${i18n.t('pa.flow.tiao')}：${tips.join('，')}。`
    : `${i18n.t('pa.flow.total')}${count}${i18n.t('pa.flow.tiao')}。`;
};

export const ASSETS_TPYE_ENUMS = {
  PRE_FIX: i18n.t('pa.resource.importExport.tablePrefix'),
  SUF_FIX: i18n.t('pa.resource.importExport.tableSuffix'),
  TABLE: i18n.t('pa.flow.table'),
  SQL_FRAGMENT_CODE: i18n.t('pa.menu.sqlClip'),
  JOB: i18n.t('pa.flowName'),
  UDF: 'UDF',
  PROJECT: i18n.t('pa.resource.importExport.project'),
  JOB_PRE: i18n.t('pa.data.text29'),
  JOB_SUF: i18n.t('pa.data.text30'),
  PROJECT_DIR: i18n.t('pa.data.text31'),
  ROUTE_TEMPLATE: i18n.t('pa.menu.routeTemplate'),
  FILTER_TEMPLATE: i18n.t('pa.menu.filterTemplate'),
  CATALOG: 'Catalog'
};
