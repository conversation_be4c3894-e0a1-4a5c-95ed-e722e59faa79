<template>
  <div
    v-loading="loading"
    :element-loading-text="loadingText"
    class="artboard__container"
    @contextmenu.stop.prevent
  >
    <!-- 画布 -->
    <div v-show="hasData" id="artboard" ref="artboardRef" class="artboard"></div>
    <!-- 无数据 -->
    <div v-show="!hasData" class="artboard artboard--nodata">
      <div>
        <div style="font-weight: bold">暂无数据</div>
        可以通过查询节点按钮开始血缘关系建立
      </div>
    </div>
    <!-- 工具栏 -->
    <tool-bar :graph="$graph" />
    <!-- 右键菜单 -->
    <right-menu
      v-model="showRightMenu"
      :graph="$graph"
      :config="rightMenuConfig"
      @click="handleRightClick"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, InjectReactive, Ref, Vue } from 'vue-property-decorator';
import ElementResizeDetector from 'element-resize-detector';
import cyDomNode from 'cytoscape-dom-node';
import debounce from 'lodash/debounce';
import html2canvas from 'html2canvas';
import cola from 'cytoscape-cola';
import cytoscape from 'cytoscape';
import { get } from '@/apis/utils/net';
import { GET_HEAD_MSG, GET_NEXT_LEVEL_RESOURCES, GET_HOMOLOGY_NODES } from '@/apis/blood-relation';
import ToolBar from './tool-bar/index.vue';
import RightMenu from './right-menu/index.vue';
import GraphConfig, { layout } from './config';
import {
  isValidArray,
  RuleMapping,
  generateParentNode,
  generateNode,
  generateNodeInfo,
  generateEdge,
  generateEdge1,
  generateEdgeInfo,
  generateNodeContent,
  generateFileName,
  downloadFile,
  filterAll,
  isDynamicNode,
  generateParams,
  validArray,
  dataURLtoBlob,
  uuid
} from './util';

cytoscape.use(cyDomNode);
cytoscape.use(cola);
@Component({ components: { ToolBar, RightMenu } })
export default class Artboard extends Vue {
  @Prop({ default: false }) isFullScreen!: boolean;
  @Prop({ default: () => ({}) }) displayConfig!: any;
  @InjectReactive() canUseSql!: boolean;
  @Ref('artboardRef') readonly artboard!: HTMLDivElement;

  private loading = false;
  private loadingText = '数据加载中';
  private $graph: any = null;
  private graphData: any[] = [];

  private showRightMenu = false;
  private rightMenuConfig = {
    x: 0,
    y: 0,
    zoom: 1,
    id: '',
    tags: [],
    type: '',
    trigger: '',
    showHomology: false
  };

  private mapping = new Map();
  private hasData = false;

  mounted() {
    this.changeFlag();
    this.initGraph();
    const resizer = debounce(() => {
      this.$graph.resize();
      this.$graph.fit();
      this.$graph.center();
    });
    ElementResizeDetector().listenTo(this.artboard, () => resizer());
    // const timer = setTimeout(() => {
    //   this.getGraphData({
    //     resType: 'JOB',
    //     serviceId: '65654a828e5f40cfbe8de9d69644bf7e',
    //     resourceList: ''
    //   });
    //   clearTimeout(timer);
    // }, 1000);
  }
  changeFlag() {
    this.hasData = this.mapping.size > 0;
  }

  initGraph() {
    this.$graph = cytoscape({ container: this.artboard, ...GraphConfig });
    this.$graph.domNode();
    this.addTapEventListener();
    this.initRightMenu(); // TODO:不太好关闭
  }

  addTapEventListener() {
    this.$graph.batch(() => {
      this.$graph.on('tap', 'node', ({ target }: any) => {
        if (target.isParent()) return;
        this.$emit('show-detail', ...['node', this.mapping.get(target.id())]);
      });
      this.$graph.on('tap', 'edge', ({ target }: any) => {
        if (target.classes().includes('homology')) return;
        const data = this.mapping.get(target.id());
        data.source.resType = this.mapping.get(data.source.id).resType;
        data.target.resType = this.mapping.get(data.target.id).resType;
        this.$emit('show-detail', ...['edge', data]);
      });
    });
  }

  initRightMenu() {
    const resetRightMenu = () => {
      this.showRightMenu = false;
      this.rightMenuConfig = {
        x: 0,
        y: 0,
        zoom: 1,
        id: '',
        tags: [],
        type: '',
        trigger: '',
        showHomology: false
      };
    };
    this.$graph.batch(() => {
      this.$graph.on('zoom', resetRightMenu);
      this.$graph.on('cxttap', ({ target, renderedPosition }) => {
        const flag =
          !target ||
          target.data('attribute.isHomology') ||
          target.classes().includes('parent') ||
          !target.isNode ||
          !target.isNode();
        if (flag) {
          return resetRightMenu();
        }
        this.rightMenuConfig = {
          trigger: 'node',
          id: target.id(),
          x: renderedPosition.x + 2,
          y: renderedPosition.y,
          zoom: this.$graph.zoom(),
          type: target.data('type'),
          tags: target.data('tags'),
          showHomology: target.data('showHomology')
        };
        this.showRightMenu = true;
      });
    });
  }
  deleteSingleNode(id: string) {
    this.$graph.batch(() => {
      this.breakLine(id);
      const node = this.$graph.getElementById(id);
      const children = node.successors('node');
      const flags = children.reduce(
        (pre: string[], next: any) => {
          pre.push(next.data('id'));
          return pre;
        },
        [id]
      );
      children.forEach((el) => this.breakLine(el.data('id'), flags));
      const components = node.component();
      components.forEach((el) => {
        el.parent().forEach((item) => this.deleteNodeData(item));
        this.deleteNodeData(el);
      });
      this.deleteNodeData(node);
    });
    this.$graph.removeScratch();
  }
  breakLine(id: string, flags: any[] = []) {
    this.$graph.elements(`edge[target="${id}"][type="normal"]`).forEach((el) => {
      if (!flags.includes(el.data('source'))) this.deleteNodeData(el);
    });
  }
  deleteNodeData(node: any) {
    const id = node.data('id');
    const dom: any = document.getElementById(id);
    dom && (dom.innerHTML = '');
    dom?.parentNode?.removeChild(dom);
    this.mapping.delete(id);
    this.changeFlag();
    node.remove();
  }
  deleteNode(id: string) {
    this.$graph.batch(() => {
      this.$graph
        .elements(`edge[target="${id}"][type="normal"]`)
        .forEach((el) => this.deleteNodeData(el));
      this.$graph
        .elements(`edge[source="${id}"][type="normal"]`)
        .forEach((el) => this.deleteNodeData(el));
      const node = this.$graph.getElementById(id);
      this.deleteNodeData(node);
    });
    this.$graph.removeScratch();
  }
  async handleRightClick({ value, id }: any) {
    /* 删除 */
    if (value === 'delete') {
      const label = this.$graph.getElementById(id).data('label');
      await this.$confirm(`您确定要删除节点【${label}】吗?`, '提示', {
        type: 'warning',
        el: this.isFullScreen ? '#bloodRelation' : ''
      });
      return this.deleteNode(id);
    }
    /* 向上扩展 */
    if (value.includes('up')) return this.getUpData(value, id);
    /* 向下扩展 */
    if (value.includes('down')) return this.getDownData(value, id);
    /* 隐藏/显示同源 */
    if (value === 'hideHomology') {
      return this.$graph.batch(() => {
        const node = this.$graph.getElementById(id);
        const result = node.data('showHomology');
        const parent = node.data('parent');
        const [Pkey, Hkey] = result ? ['addClass', 'removeClass'] : ['removeClass', 'addClass'];
        this.$graph.getElementById(parent)[Pkey]('showParent');
        this.$graph.elements(`.${parent}`).forEach((el) => {
          el[Hkey]('hidden');
          const dom: any = document.getElementById(el.data('id'));
          if (dom) {
            dom.style.visibility = !result ? 'hidden' : 'visible';
          }
        });
        node.data('showHomology', !result);
      });
    }
    /* 获取同源 */
    if (value === 'getHomology') {
      if (!this.displayConfig.showHomologyNodes) {
        return this.$message.warning({
          message: '显示同源信息配置为false，无法完成操作',
          el: this.isFullScreen ? '#bloodRelation' : ''
        });
      }
      return this.getHomologyData(value, id);
    }
  }
  async getRelationData(params: any = {}) {
    this.loading = true;
    const { success, data, error } = await get(GET_NEXT_LEVEL_RESOURCES, params);
    if (success) return validArray(data);
    this.$message.error({
      message: error,
      el: this.isFullScreen ? '#bloodRelation' : ''
    });
    return Promise.reject(error);
  }
  async getHeadData(params: any = {}) {
    const { success, data, error } = await get(GET_HEAD_MSG, generateParams('head', params));
    if (success) return validArray([data]);
    this.$message.error({
      message: error,
      el: this.isFullScreen ? '#bloodRelation' : ''
    });
    return Promise.reject(error);
  }
  validityCheck(data: any = []) {
    if (isValidArray(data)) return true;
    this.loading = false;
    this.$message.success({
      message: '没有可扩散的节点',
      el: this.isFullScreen ? '#bloodRelation' : ''
    });
    return null;
  }
  async getGraphData(params: any = {}) {
    params.resourceList = encodeURIComponent(params.resourceList);
    const main = await this.getHeadData(params);
    const other = await this.getRelationData(generateParams('all', params));
    const data = [...main, ...other];
    if (!this.validityCheck(data)) return;
    for (const key of this.mapping.keys()) {
      const dom: any = document.getElementById(key);
      dom && (dom.innerHTML = '');
      dom?.parentNode?.removeChild(dom);
    }
    this.mapping = new Map();
    this.changeFlag();
    this.$graph.batch(() => {
      this.$graph.remove('*');
      this.$graph.removeScratch();
    });
    this.handleGraphData(data);
    const node = this.$graph.getElementById(main[0].id);
    node && node.select && node.select();
  }
  updateNodeInfo(arr: any[] = []) {
    arr.forEach((el) => {
      /* 1, 操作数据更新 */
      this.mapping.set(el.id, generateNodeInfo(el));
      this.changeFlag();
      /* 2, 显示数据更新 */
      this.$graph.batch(() => {
        const label = el.resType === 'JOB' ? `${el.projectName}-${el.jobName}` : el.serviceName;
        const tags = [el.isDynamic ? '动' : ''];
        const node = this.$graph.getElementById(el.id);
        /* label, isDynamic, dom, tags */
        node.data('label', label);
        node.data('tags', tags);
        node.data('attribute.isDynamic', el.isDynamic);
        const dom: any = document.getElementById(el.id);
        dom && (dom.innerHTML = '');
        generateNodeContent(dom, node.data('tags'), node.data('name'));
      });
    });
  }
  handleData(type: string, id: string, arr: any[] = []) {
    const node = this.$graph.getElementById(id);
    const [lineType, nodeType] = type.includes('up') ? ['target', 'source'] : ['source', 'target'];
    const lineData = node
      .neighborhood(`edge[${lineType}="${id}"][type="normal"]`)
      .map((el) => el.data(nodeType));
    const nodeData = node.neighborhood(RuleMapping[type]).map((el) => el.data('id'));
    const validData = lineData.filter((el) => nodeData.includes(el));
    const [deleteData, noDelData] = filterAll(validData, (el) => !arr.some(({ id }) => id === el));
    const [updateData, addData] = filterAll(arr, (el) => noDelData.includes(el.id));
    return [deleteData, updateData, addData];
  }
  async getUpData(type: string, id: string) {
    try {
      const data = await this.getRelationData(generateParams(type, this.mapping.get(id)));
      if (!this.validityCheck(data)) return;
      const [deleteData, updateData, addData] = this.handleData(type, id, data);
      this.deleteNodes(deleteData);
      this.updateNodeInfo(updateData);
      this.handleGraphData(addData);
    } catch (e) {
      this.loading = false;
      console.log(e);
    }
  }

  async getDownData(type: string, id: string) {
    try {
      const data = await this.getRelationData(generateParams(type, this.mapping.get(id)));
      if (!this.validityCheck(data)) return;
      const [deleteData, updateData, addData] = this.handleData(type, id, data);
      this.deleteNodes(deleteData, 'target');
      this.updateNodeInfo(updateData);
      this.handleGraphData(addData);
    } catch (e) {
      this.loading = false;
      console.log(e);
    }
  }
  async getHomologyData(type: string, id: string) {
    try {
      this.loading = true;
      const { success, data, error } = await get(
        GET_HOMOLOGY_NODES,
        generateParams(type, this.mapping.get(id))
      );
      if (success) {
        if (!this.validityCheck(data)) return;
        await this.deleteHomologyNodes(id);
        const node = this.$graph.getElementById(id);
        return this.handleGraphData(data, node.data('parent'), id);
      }
      this.$message.error({
        message: error,
        el: this.isFullScreen ? '#bloodRelation' : ''
      });
      this.loading = false;
    } catch (e) {
      this.loading = false;
      console.log(e);
    }
  }
  deleteHomologyNodes(id: string) {
    this.$graph.batch(() => {
      const node = this.$graph.getElementById(id);
      /* 删除同源标签 */
      const tags = node.data('tags').filter((el) => el !== '同');
      node.data('tags', tags);
      const dom: any = document.getElementById(id);
      dom && (dom.innerHTML = '');
      generateNodeContent(dom, node.data('tags'), node.data('name'));
      /* 删除同源节点 */
      const lineData = this.$graph.elements(`edge[source="${id}"][type="homology"]`);
      const nodeData = lineData.map((el) => el.target());
      [...lineData, ...nodeData].forEach((el) => {
        this.deleteData(el.data('id'));
        el.remove();
      });
      /* 隐藏parent */
      this.$graph.getElementById(parent).removeClass('showParent');
    });
  }

  handleGraphData(rawData: any[] = [], parent = null, id = '') {
    const [data, nodeNum, edgeNum] = this.dataConversion(rawData, parent, id);
    this.graphData.push(...data);
    this.$graph.add(data);
    if (parent) {
      this.$graph.getElementById(parent).addClass('showParent');
      /* 同源标签更新 */
      const node = this.$graph.getElementById(id);
      const tags = node.data('tags');
      if (!tags.includes('同')) {
        this.$graph.batch(() => {
          tags.unshift('同');
          node.data('tags', tags);
          const dom: any = document.getElementById(id);
          dom && (dom.innerHTML = '');
          generateNodeContent(dom, node.data('tags'), node.data('name'));
        });
      }
    }
    this.$graph.layout(layout).run();
    this.$graph.center();
    this.$graph.fit();
    this.loading = false;
    if (nodeNum > 0 || edgeNum > 0) {
      this.$message.success({
        message: `新增了${nodeNum}个节点，${edgeNum}条边`,
        el: this.isFullScreen ? '#bloodRelation' : ''
      });
    } else {
      this.$message.success({
        message: '没有可扩散的节点',
        el: this.isFullScreen ? '#bloodRelation' : ''
      });
    }
  }
  dataConversion(rawData: any[] = [], parentId = null, id) {
    if (!this.canUseSql) {
      rawData = rawData.filter(({ resType }) => !['TABLE', 'VIEW'].includes(resType));
    }
    return rawData.reduce(
      (pre: any[], next: any) => {
        if (!next.id || next.id === id) return pre;
        let parent: any = null;
        if (isDynamicNode(next.resType) && !parentId) {
          parent = generateParentNode();
          pre[0].push(parent);
        }
        if (Boolean(parentId)) next.id = uuid();
        const node = generateNode(
          next,
          this.displayConfig.showNodeLabel,
          parent ? parent.data.id : parentId,
          Boolean(parentId)
        );
        if (!this.mapping.has(node.data.id)) {
          this.mapping.set(node.data.id, generateNodeInfo(next));
          this.changeFlag();
          pre[1] += 1;
        }
        pre[0].push(node);
        if (next.sourceId && next.targetId) {
          const edge = generateEdge(next, this.displayConfig.showLineLabel);
          if (!this.mapping.has(edge.data.id)) {
            this.mapping.set(edge.data.id, generateEdgeInfo(next));
            this.changeFlag();
            pre[2] += 1;
          }
          pre[0].push(edge);
        } else {
          if (parentId) {
            const edge = generateEdge1(next, id, parentId);
            pre[0].push(edge);
            pre[2] += 1;
          }
        }
        return pre;
      },
      [[], 0, 0]
    );
  }
  changeDisplay() {
    /* 节点信息 */
    if (!this.displayConfig.showNodeLabel) {
      this.$graph.batch(() => {
        this.$graph.nodes().forEach((el) => el.removeClass('label'));
      });
    } else {
      this.$graph.batch(() => {
        this.$graph.nodes().forEach((el) => el.addClass('label'));
      });
    }
    /* 边信息 */
    if (!this.displayConfig.showLineLabel) {
      this.$graph.batch(() => {
        this.$graph.elements('edge[type = "normal"]').forEach((el) => el.removeClass('label'));
      });
    } else {
      this.$graph.batch(() => {
        this.$graph.elements('edge[type = "normal"]').forEach((el) => el.addClass('label'));
      });
    }
    /* 同源信息 */
    if (!this.displayConfig.showHomologyNodes) {
      const parents = this.$graph.elements('node.parent[]');
      const homologys = this.$graph.elements('.homology');
      homologys.forEach((el) => {
        el.addClass('hidden');
        const dom: any = document.getElementById(el.data('id'));
        dom && (dom.style.visibility = 'hidden');
      });
      parents.forEach((el) => el.removeClass('showParent'));
    } else {
      const parents = this.$graph.elements('node.parent');
      const homologys = this.$graph.elements('.homology');
      homologys.forEach((el) => {
        el.removeClass('hidden');
        const dom: any = document.getElementById(el.data('id'));
        dom && (dom.style.visibility = 'visible');
      });
      parents.forEach((el) => el.addClass(el.children().length > 1 ? 'showParent' : ''));
    }
  }
  deleteNodes(id: string | string[], active = 'source') {
    const arr = Array.isArray(id) ? id : [id];
    this.$graph.batch(() => {
      arr.forEach((unid) => {
        const res = this.$graph.elements(`edge[${active}="${unid}"][type="normal"]`);
        for (const i of res) {
          this.deleteData(i.data('id'));
          i.remove();
        }
        const node = this.$graph.getElementById(unid);
        if (node.connectedEdges().length > 0) return;
        const components = node.component();
        components.forEach((el) => {
          el.parent().forEach((item) => {
            this.deleteData(item.data('id'));
            item.remove();
          });
          this.deleteData(el.data('id'));
          el.remove();
          /* 删除对于同源 */
        });
        this.deleteData(unid);
      });
      this.$graph.removeScratch();
    });
  }
  deleteData(id) {
    const dom: any = document.getElementById(id);
    dom && (dom.innerHTML = '');
    dom?.parentNode?.removeChild(dom);
    this.mapping.delete(id);
    this.changeFlag();
  }
  async exportImage() {
    try {
      this.loading = true;
      this.loadingText = '图片导出中...';
      const canvas = await html2canvas(this.artboard);
      downloadFile(dataURLtoBlob(canvas.toDataURL('image/png', 1.0)), generateFileName('png'));
      this.loading = false;
      this.loadingText = '数据加载中...';
    } catch (e) {
      this.loading = false;
      this.loadingText = '数据加载中...';
      console.log(e);
    }
  }
}
</script>

<style lang="scss" scoped>
.artboard {
  &__container {
    position: relative;
    width: 100%;
    height: calc(100% - 64px);
    overflow: hidden;
    background: #fafcff;
    .artboard {
      width: 100%;
      height: 100%;
      overflow: hidden;
      &--nodata {
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        line-height: 40px;
      }
    }
  }
}
</style>
