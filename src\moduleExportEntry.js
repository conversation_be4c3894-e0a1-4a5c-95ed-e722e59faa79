import { routes } from './router/index';
import axios from 'axios';
import vueAxios from 'vue-axios';
import BSUI from 'bs-ui-pro';
import 'bs-ui-pro/lib/theme-chalk/default-index.css';
import './assets/iconfonts/iconfont.css';
import './style/style.scss';
import zhCnLocale from 'bsview/src/locale/lang/zh-CN';
import BSCnLocale from 'bs-ui-pro/lib/locale/lang/zh-CN';

import userInfo from './store/modules/user-info';
import job from './store/modules/job';
import others from './store/modules/others';
import asset from './store/modules/asset';

// import { AxiosInterceptor } from './apis/utils/http';
import { autoLogin } from './utils';
import { installFilterAndDirective } from './filters-directives';

const RouterInfo = routes.filter(({ name }) => !['home'].includes(name));
function install(Vue, store) {
  const httpInterceptor = () => {
    (Vue.axios.defaults.baseURL = process.env.NODE_ENV === 'development' ? '/api' : '.'),
      Vue.axios.interceptors.response.use(
        (response) => {
          if (response.headers && response.headers['content-disposition']) {
            // closeLoading();
            return Promise.resolve({
              blob: response.data,
              fileName: response.headers['content-disposition'].split(';')[1].split('=')[1]
            });
          } else {
            // closeLoading();
            return Promise.resolve(response);
          }
        },
        ({ response }) => {
          if (response.status) {
            if (response.status === 400) {
              Vue.prototype.$message({
                type: 'warning',
                message: response.data.message
              });
            } else if (response.status === 401) {
              invalidSessionHandler();
            } else {
              Vue.prototype.$message({
                type: 'error',
                message: '服务端异常！'
              });
            }
            // Message.error(response.data.msg);
            return Promise.reject(response.data);
          }
        }
      );
  };
  Vue.use(vueAxios, axios);
  Vue.use(axios);
  Vue.use(BSUI);
  autoLogin();
  installFilterAndDirective(); // 注册全局过滤器和自定义指令
  httpInterceptor();
}
const lang_zh = Object.assign(zhCnLocale, BSCnLocale);
const modules = { userInfo, job, asset, others };
export { modules, RouterInfo, lang_zh, install as default };
