<template>
  <bs-dialog :title="$t('pa.flow.fieldUpdateTip')" :visible="visible" size="large" @close="closeDialog" @confirm="submit">
    <div class="node-info">
      <p class="node-info__tip">{{ $t('pa.flow.msg51') }}</p>
      <bs-table
        :data="tableData"
        :column-data="columnData"
        :page-data="pageData"
        :column-settings="false"
        @page-change="pageChange"
      >
        <span slot="header-sourceChangedFields" slot-scope="{ row }">
          {{ row.label }}
          <el-tooltip
            class="item"
            effect="light"
            :content="tooltipContent1"
            placement="top"
            popper-class="node-info_table-tooltip"
          >
            <i class="el-tooltip iconfont icon-wenhao"></i>
          </el-tooltip>
        </span>
        <span slot="header-targetChangedFields" slot-scope="{ row }">
          {{ row.label }}
          <el-tooltip
            class="item"
            effect="light"
            :content="tooltipContent2"
            placement="top"
            popper-class="node-info_table-tooltip"
          >
            <i class="el-tooltip iconfont icon-wenhao"></i>
          </el-tooltip>
        </span>
        <div slot="sourceChangedFields" slot-scope="{ row }">
          <SourceField type="add" :data="row.sourceChangedFields.addFields" />
          <SourceField type="del" :data="row.sourceChangedFields.delFields" />
        </div>
        <div slot="targetChangedFields" slot-scope="{ row }">
          <TargetField
            v-for="(item, idx) in getTargetChangedFields(row)"
            :key="item.model + row.nodeId + idx"
            :data="item"
          />
        </div>
      </bs-table>
    </div>
  </bs-dialog>
</template>
<script lang="tsx">
import { Component, Prop, Vue } from 'vue-property-decorator';
import SourceField from './source-field.vue';
import TargetField from './target-field.vue';
import { ChangedNodeInfo } from '../../interface';
@Component({
  components: {
    SourceField: SourceField,
    TargetField: TargetField
  }
})
export default class NodeAutoUpdateDialog extends Vue {
  @Prop() visible!: boolean;
  @Prop() data!: ChangedNodeInfo[];
  tableData: ChangedNodeInfo[] = [];
  columnData = [
    {
      label: this.$t('pa.flow.componentName'),
      value: 'nodeName',
      width: 120
    },
    {
      label: this.$t('pa.flow.componentType'),
      value: 'operateType',
      width: 120
    },
    {
      label: this.$t('pa.flow.outputChange'),
      value: 'sourceChangedFields',
      showOverflowTooltip: false
    },
    {
      label: this.$t('pa.flow.autoUpdateField'),
      value: 'targetChangedFields',
      showOverflowTooltip: false
    }
  ];
  pageData = {
    pageSize: 10,
    currentPage: 1,
    total: 0
  };
  tooltipContent1 = `${this.$t('pa.flow.msg52')} \n ${this.$t('pa.flow.msg53')}`;
  tooltipContent2 = `${this.$t('pa.flow.msg56')} \n ${this.$t('pa.flow.msg54')} \n ${this.$t('pa.flow.msg55')}`;
  created() {
    this.pageData.total = this.data.length;
    this.tableData = this.data.slice(0, 10);
  }
  pageChange(currentPage, pageSize) {
    this.tableData = this.data.splice((currentPage - 1) * pageSize, currentPage * pageSize);
  }
  closeDialog() {
    this.$emit('cancel', this.data);
    this.$emit('update:visible', false);
  }
  submit() {
    this.$emit('submit', this.data);
    this.$emit('update:visible', false);
  }
  getTargetChangedFields({ operateType, targetChangedFields }) {
    if (operateType === this.$t('pa.flow.outputComponent')) {
      targetChangedFields = targetChangedFields.filter((item) => !item.isOutput);
    }
    return targetChangedFields;
  }
}
</script>
<style lang="scss" scoped>
.node-info {
  min-height: 200px;
  margin: -18px -35px;
  &__tip {
    width: 100%;
    height: 32px;
    line-height: 32px;
    text-align: center;
    color: #ff9e2b;
    background: #fff5ea;
  }
}
</style>
<style>
body > .node-info_table-tooltip {
  white-space: pre-line;
}
</style>
