<template>
  <bs-dialog
    title="字段自动更新提示"
    :visible="visible"
    size="large"
    @close="closeDialog"
    @confirm="submit"
  >
    <div class="node-info">
      <p class="node-info__tip">因上游字段的增删导致以下组件字段出现变更，请确定是否进行自动更新</p>
      <bs-table
        :data="tableData"
        :column-data="columnData"
        :page-data="pageData"
        :column-settings="false"
        @page-change="pageChange"
      >
        <span slot="header-sourceChangedFields" slot-scope="{ row }">
          {{ row.label }}
          <el-tooltip
            class="item"
            effect="light"
            :content="tooltipContent1"
            placement="top"
            popper-class="node-info_table-tooltip"
          >
            <i class="el-tooltip iconfont icon-wenhao"></i>
          </el-tooltip>
        </span>
        <span slot="header-targetChangedFields" slot-scope="{ row }">
          {{ row.label }}
          <el-tooltip
            class="item"
            effect="light"
            :content="tooltipContent2"
            placement="top"
            popper-class="node-info_table-tooltip"
          >
            <i class="el-tooltip iconfont icon-wenhao"></i>
          </el-tooltip>
        </span>
        <div slot="sourceChangedFields" slot-scope="{ row }">
          <SourceField type="add" :data="row.sourceChangedFields.addFields" />
          <SourceField type="del" :data="row.sourceChangedFields.delFields" />
        </div>
        <div slot="targetChangedFields" slot-scope="{ row }">
          <TargetField
            v-for="(item, idx) in getTargetChangedFields(row)"
            :key="item.model + row.nodeId + idx"
            :data="item"
          />
        </div>
      </bs-table>
    </div>
  </bs-dialog>
</template>
<script lang="tsx">
import { Component, Prop, Vue } from 'vue-property-decorator';
import SourceField from './source-field.vue';
import TargetField from './target-field.vue';
import { ChangedNodeInfo } from '../../interface';
@Component({
  components: {
    SourceField: SourceField,
    TargetField: TargetField
  }
})
export default class NodeAutoUpdateDialog extends Vue {
  @Prop() visible!: boolean;
  @Prop() data!: ChangedNodeInfo[];
  tableData: ChangedNodeInfo[] = [];
  columnData = [
    {
      label: '组件名称',
      value: 'nodeName',
      width: 120
    },
    {
      label: '组件类型',
      value: 'operateType',
      width: 120
    },
    {
      label: '上游输出变更自动',
      value: 'sourceChangedFields',
      showOverflowTooltip: false
    },
    {
      label: '可自动更新字段',
      value: 'targetChangedFields',
      showOverflowTooltip: false
    }
  ];
  pageData = {
    pageSize: 10,
    currentPage: 1,
    total: 0
  };
  tooltipContent1 = `红色字段：被删除的输出字段 \n 绿色字段：增加的输出字段`;
  tooltipContent2 = `当前组件使用到的上游输出字段，不包括可选字段 \n红色字段：被删除的字段 \n 绿色字段：增加的字段`;
  created() {
    this.pageData.total = this.data.length;
    this.tableData = this.data.slice(0, 10);
  }
  pageChange(currentPage, pageSize) {
    this.tableData = this.data.splice((currentPage - 1) * pageSize, currentPage * pageSize);
  }
  closeDialog() {
    this.$emit('cancel', this.data);
    this.$emit('update:visible', false);
  }
  submit() {
    this.$emit('submit', this.data);
    this.$emit('update:visible', false);
  }
  getTargetChangedFields({ operateType, targetChangedFields }) {
    if (operateType === '输出组件') {
      targetChangedFields = targetChangedFields.filter((item) => !item.isOutput);
    }
    return targetChangedFields;
  }
}
</script>
<style lang="scss" scoped>
.node-info {
  min-height: 200px;
  margin: -18px -35px;
  &__tip {
    width: 100%;
    height: 32px;
    line-height: 32px;
    text-align: center;
    color: #ff9e2b;
    background: #fff5ea;
  }
}
</style>
<style>
body > .node-info_table-tooltip {
  white-space: pre-line;
}
</style>
