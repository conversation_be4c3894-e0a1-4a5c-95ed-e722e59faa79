<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">缓存信息</div>
      <div class="bs-page__header-operation">
        <el-button type="primary" @click="getCacheInfoData">刷新</el-button>
      </div>
    </div>
    <div class="tab-content" :style="{ height: height, padding: '0 20px' }">
      <el-tabs v-loading="loading">
        <el-tab-pane v-for="(value, key) in data" :key="key" :label="key">
          <div style="height: 416px; overflow: auto; flex-wrap: wrap; padding-left: 20px">
            <pre v-for="(valueKey, subKey) in value" :key="subKey">
            <pre style="font-weight:bold">{{ subKey }}:</pre>{{ valueKey }}</pre>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject } from 'vue-property-decorator';
import { URL_RES_DETAIL_REDIS_CACHEINFO } from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {}
})
export default class CacheInfoTable extends PaBase {
  height = '300px';
  data: any = [];
  private loading = false;
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  created() {
    this.loadData(this.comDetailRecord.val || {}, this.comParams.CacheInfo || {});
  }
  getCacheInfoData() {
    this.loading = true;
    this.doGet(URL_RES_DETAIL_REDIS_CACHEINFO, {
      params: {
        id: this.$route.query.id
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.data = resp.data;
      });
      this.loading = false;
    });
  }
  async loadData(data: any, params: any) {
    this.height = params.height;
    this.getCacheInfoData();
  }
}
</script>
<style scoped>
.my-card_body ::v-deep .el-card__body {
  padding: 0px 0px 0px 0px;
}
.my-wrap-row {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  font-family: 'Microsoft YaHei';
}
.my-wrap-col {
  border: 1px solid #000;
}
.my-wrap-tag {
  width: 350px;
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
pre {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}
</style>
