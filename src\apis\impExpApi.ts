import { post, get } from '@bs/axios';
export const IMP_PREVIEW = '/rs/pa/expImp/importDataPreview';
// 获取不可导入数据&重复性数据
export const getCannotAndRepeatData = (id) => {
  return get('/rs/pa/expImp/importVerify', { impId: id });
};
// 不可导入数据下载
export const unImportDataDownload = (data) => {
  return post('/rs/pa/expImp/export/unImport', data, {
    responseType: 'blob'
  });
};
// 重复性数据下载
export const repeatDataDownload = (data) => {
  return post('/rs/pa/expImp/export/repeat', data, {
    responseType: 'blob'
  });
};
// 导入数据
export const importSubmit = (data) => {
  return post('/rs/pa/expImp/importSubmit', data);
};

// 获取可以导出的资源
export const getExportTree = () => {
  return get('/rs/pa/expImp/getResList');
};
// 导出预览
export const previewData = (data) => {
  return post('/rs/pa/expImp/preview', data);
};
// 导出
export const exportData = (data) => {
  return post('/rs/pa/expImp/downloadRecords', data, {
    responseType: 'blob'
  });
};
