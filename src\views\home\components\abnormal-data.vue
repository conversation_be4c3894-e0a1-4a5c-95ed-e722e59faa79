<template>
  <div class="abnormal-data">
    <bs-empty image-size="150">
      <p v-if="isTimeout" slot="description">
        {{ $t('pa.home.timeout') }}
        <span class="abnormal-data__refresh" @click="refreshData">{{ $t('pa.action.refresh') }} </span>
        {{ $t('pa.home.tryAgain') }}
      </p>
    </bs-empty>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component
export default class EmptyData extends Vue {
  @Prop({ default: 'empty' }) status!: string;
  get isTimeout() {
    return this.status === 'timeout';
  }
  refreshData() {
    this.$emit('refresh', true);
  }
}
</script>

<style lang="scss" scoped>
.abnormal-data {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  .bs-empty {
    height: 100%;
  }
  &__refresh {
    cursor: pointer;
    color: #ccc;
  }
}
</style>
