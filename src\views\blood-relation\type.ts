import i18n from '@/i18n';

/**
 * 节点tag类型
 */
export const NODE_TAG = {
  Dynamic: [i18n.t('pa.blood.dynamic'), 'red'],
  Dep: [i18n.t('pa.blood.dep'), 'orange']
};

/**
 * 查找方向
 *
 * @export
 * @typedef {SerachType}
 */
export type SerachType = 'SOURCE' | 'SINK' | 'SOURCE,SINK';
/**
 * 原数据模型
 *
 * @export
 * @interface SourceData
 * @typedef {SourceData}
 */
export interface SourceData {
  jobId?: string; // 流程id
  jobName?: string; // 流程名称
  projectName?: string; // 流程所在项目名称
  nodeId?: string; // 流程节点id
  connectorType?: string; // 流程类型 DS还是SQL
  resType: 'JOB' | 'SERVICE' | 'TABLE' | 'CATALOG';
  // 对应资源的ID。服务/表/视图/流程/catalog的id
  serviceId?: string;
  // 资源的名称 服务/表/流程/catalog的名称
  serviceName: string;
  // 服务的类型，kafka、JDBC等
  serviceType?: string;
  resourceList?: string; // 具体的资源
  // 该节点可查询上游还是下游的数据
  componentType: 'SOURCE' | 'SINK';
  isDynamic?: boolean; // 是否为动态资源
  depUpstream?: boolean; // 是否依赖上游资源
  noResource?: boolean; // 是否为无资源
  relatedInformation?: string; // 后端需要的一些数据
  sourceId?: string; // 查询当前节点的上游节点id
  targetId?: string; // 查询当前节点的下游节点id
  hasAccess: boolean; // 是否有权限
  id: string; // 节点唯一表示id
  sinkCount?: number; // 节点下游数量
  sourceCount?: number; // 节点上游数量
  iconByte?: string; // 服务类型的icon
}

export interface Node {
  // 节点唯一id
  id: string;
  // 节点类型
  type: string;
  // 是否为当前查询节点
  isRoot?: boolean;
  // 节点类型
  resType: 'JOB' | 'SERVICE' | 'TABLE' | 'CATALOG';
  // 节点名称
  name: string;
  // 节点二级名称
  subName?: string;
  // 节点图标
  img?: string;
  // 左上角tag
  tag?: 'dynamic' | 'dep';
  // 节点下游数量，表示可以向下游扩展查询
  sinkCount: number;
  // 节点上游数量，表示可以向上游扩展查询
  sourceCount: number;
  // 是否已经向下游进行查询（存在连线）
  sinkExpand: boolean;
  // 是否已经向上游进行查询（存在连线）
  sourceExpand: boolean;
  // 通过上游还是下游查询出来的节点
  componentType: 'SOURCE' | 'SINK';
}

export interface Edge {
  targetAnchor?: number;
  sourceAnchor?: number;
  type?: string;
  source: string;
  target: string;
  style?: any;
}

export interface SearchParams {
  relatedInformation: string;
  serviceType: string;
  serviceId: string;
  resourceList: string;
  componentType: SerachType;
  isDynamic: boolean;
}
