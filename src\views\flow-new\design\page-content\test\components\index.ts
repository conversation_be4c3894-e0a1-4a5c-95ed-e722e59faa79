import i18n from '@/i18n';

export const logicList = [
  {
    value: 'RANDOM',
    label: i18n.t('pa.flow.random')
  },
  {
    value: 'ENUM',
    label: i18n.t('pa.flow.randomEnum')
  }
];

export const rules = {
  type: [{ required: true, message: i18n.t('pa.flow.msg226'), trigger: 'blur' }],
  accuracy: [{ required: true, message: i18n.t('pa.flow.msg227'), trigger: 'change' }],
  timeStamp: [{ required: true, message: i18n.t('pa.flow.msg228'), trigger: 'change' }],
  rule: [{ required: true, message: i18n.t('pa.flow.msg229'), trigger: 'change' }],
  enumVal: [{ required: true, message: i18n.t('pa.flow.msg230'), trigger: 'change' }],
  check: [{ required: true, message: i18n.t('pa.flow.msg231'), trigger: 'change' }]
};

export const pickerOptions = {
  shortcuts: [
    {
      text: i18n.t('pa.flow.mm1'),
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        const first = new Date(start.getFullYear(), start.getMonth(), 1);
        picker.$emit('pick', [first, end]);
      }
    },
    {
      text: i18n.t('pa.flow.3mm'),
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setMonth(start.getMonth() - 3);
        picker.$emit('pick', [start, end]);
      }
    }
  ]
};

export const editInfo = (data, info) => {
  const { type, other } = data;
  if (['tinyint', 'smallint', 'mediumint', 'int', 'bigint'].includes(type)) {
    if (other.type === 'RANDOM') {
      info.random = other.random && +other.random.split(',')[0];
      info.random1 = other.random && +other.random.split(',')[1];
    } else {
      info.enumVal = other.enumVal;
    }
  } else if (['date', 'timestamp(3)', 'timestamp'].includes(type)) {
    info.timeStamp = [new Date(+other.timeStamp.split(',')[0]), new Date(+other.timeStamp.split(',')[1])];
  } else if (['bool', 'boolean'].includes(type)) {
    info.check = other.rule.split(',');
  } else if (['float', 'double'].includes(type)) {
    if (other.type === 'RANDOM') {
      info.random = +other.random.split(',')[0];
      info.random1 = +other.random.split(',')[1];
      info.accuracy = other.accuracy;
    } else {
      info = { ...info, ...other };
    }
  } else {
    info = { ...info, ...other };
  }
  return { info };
};
