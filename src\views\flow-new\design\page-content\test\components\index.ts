export const logicList = [
  {
    value: 'RANDOM',
    label: '随机生成'
  },
  {
    value: 'ENUM',
    label: '随机生成-枚举值'
  }
];

export const rules = {
  type: [{ required: true, message: '请选择用例', trigger: 'blur' }],
  accuracy: [{ required: true, message: '请输入小数位数', trigger: 'change' }],
  timeStamp: [{ required: true, message: '请输入时间范围', trigger: 'change' }],
  rule: [{ required: true, message: '请选择字符类型', trigger: 'change' }],
  enumVal: [{ required: true, message: '请输入枚举值', trigger: 'change' }],
  check: [{ required: true, message: '请选择枚举值', trigger: 'change' }]
};

export const pickerOptions = {
  shortcuts: [
    {
      text: '本月',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        const first = new Date(start.getFullYear(), start.getMonth(), 1);
        picker.$emit('pick', [first, end]);
      }
    },
    {
      text: '最近三个月',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setMonth(start.getMonth() - 3);
        picker.$emit('pick', [start, end]);
      }
    }
  ]
};

export const editInfo = (data, info) => {
  const { type, other } = data;
  if (['tinyint', 'smallint', 'mediumint', 'int', 'bigint'].includes(type)) {
    if (other.type === 'RANDOM') {
      info.random = other.random && +other.random.split(',')[0];
      info.random1 = other.random && +other.random.split(',')[1];
    } else {
      info.enumVal = other.enumVal;
    }
  } else if (['date', 'timestamp(3)', 'timestamp'].includes(type)) {
    info.timeStamp = [
      new Date(+other.timeStamp.split(',')[0]),
      new Date(+other.timeStamp.split(',')[1])
    ];
  } else if (['bool', 'boolean'].includes(type)) {
    info.check = other.rule.split(',');
  } else if (['float', 'double'].includes(type)) {
    if (other.type === 'RANDOM') {
      info.random = +other.random.split(',')[0];
      info.random1 = +other.random.split(',')[1];
      info.accuracy = other.accuracy;
    } else {
      info = { ...info, ...other };
    }
  } else {
    info = { ...info, ...other };
  }
  return { info };
};
