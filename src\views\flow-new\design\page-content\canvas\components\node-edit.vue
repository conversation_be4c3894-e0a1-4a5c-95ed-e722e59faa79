<template>
  <bs-dialog
    :title="$t('pa.flow.edit')"
    :visible="visible"
    :before-close="closeDialog"
    @close="closeDialog"
    @confirm="submit"
  >
    <el-form ref="form" :model="formData" :rules="rules" label-width="80px">
      <el-form-item :label="$t('pa.flow.name')" prop="nodeName">
        <el-input
          v-model="formData.nodeName"
          maxlength="30"
          type="text"
          show-word-limit
          :placeholder="$t('pa.flow.placeholder5')"
        />
      </el-form-item>
      <el-form-item :label="$t('pa.flow.bingxingdu')" prop="parallelism">
        <el-input-number
          v-model="formData.parallelism"
          style="width: 100%"
          :min="1"
          :max="32768"
          number
          :disabled="parallelismDisabled"
          :placeholder="$t('pa.flow.placeholder11')"
        />
      </el-form-item>
    </el-form>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

const strs = ['~', '!', '@', '#', '$', '&', '*', '?', '/', '|', '\\', '`', '。', '，', '.', ','];
const checkName = (val) => {
  const target = {
    res: false,
    str: ''
  };
  val.split('').some((i) => {
    if (strs.includes(i)) {
      target.str = i;
      target.res = true;
      return true;
    } else {
      return false;
    }
  });
  return target;
};
@Component
export default class NodeEdit extends Vue {
  @Prop() visible!: boolean;
  @Prop() data!: any;
  formData = { nodeName: '', parallelism: '' };
  rules: any = {
    nodeName: [{ validator: this.checkNodeName, trigger: 'blur' }],
    parallelism: [
      {
        required: true,
        pattern: /(^[1-9]\d*$)/,
        message: this.$t('pa.flow.msg57'),
        trigger: 'blur'
      }
    ]
  };
  // 并行度是否可编辑
  get parallelismDisabled() {
    return ['SOURCE_SQL', 'PROCESS_SQL'].includes(this.data.type);
  }
  created() {
    this.formData.nodeName = this.data.nodeName;
    this.formData.parallelism = this.data.parallelism;
  }
  checkNodeName(rule, value, callback) {
    const checkRes = checkName(value);
    if (value === '') {
      callback(new Error(this.$t('pa.flow.placeholder7')));
    } else if (checkRes.res) {
      callback(new Error(this.$t('pa.flow.msg58') + checkRes.str));
    } else {
      if ((this.data.otherNodeNames || []).includes(value)) {
        callback(new Error(this.$t('pa.flow.msg59')));
      } else {
        callback();
      }
    }
  }

  closeDialog() {
    this.$emit('update:visible', false);
  }

  submit() {
    const form: any = this.$refs.form;
    form.validate((valid: any) => {
      if (valid) {
        this.$emit('node-edit-confirm', this.formData);
        this.$emit('update:visible', false);
      } else {
        return false;
      }
    });
  }
}
</script>
<style lang="scss" scoped></style>
