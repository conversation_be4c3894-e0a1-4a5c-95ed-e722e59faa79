<template>
  <bs-dialog
    title="编辑"
    :visible="visible"
    :before-close="closeDialog"
    @close="closeDialog"
    @confirm="submit"
  >
    <el-form ref="form" :model="formData" :rules="rules" label-width="80px">
      <el-form-item label="名称" prop="nodeName">
        <el-input
          v-model="formData.nodeName"
          maxlength="30"
          type="text"
          show-word-limit
          placeholder="请输入名称，长度不超过30字符"
        />
      </el-form-item>
      <el-form-item label="并行度" prop="parallelism">
        <el-input-number
          v-model="formData.parallelism"
          style="width: 100%"
          :min="1"
          :max="2147483647"
          number
          :disabled="parallelismDisabled"
          placeholder="请输入并行度"
        />
      </el-form-item>
    </el-form>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

const strs = ['~', '!', '@', '#', '$', '&', '*', '?', '/', '|', '\\', '`', '。', '，', '.', ','];
const checkName = (val) => {
  const target = {
    res: false,
    str: ''
  };
  val.split('').some((i) => {
    if (strs.includes(i)) {
      target.str = i;
      target.res = true;
      return true;
    } else {
      return false;
    }
  });
  return target;
};
@Component
export default class NodeEdit extends Vue {
  @Prop() visible!: boolean;
  @Prop() data!: any;
  formData = { nodeName: '', parallelism: '' };
  rules: any = {
    nodeName: [{ validator: this.checkNodeName, trigger: 'blur' }],
    parallelism: [
      {
        required: true,
        pattern: /(^[1-9]\d*$)/,
        message: '请输入大于0小于100的整数',
        trigger: 'blur'
      }
    ]
  };
  // 并行度是否可编辑
  get parallelismDisabled() {
    return ['SOURCE_SQL', 'PROCESS_SQL'].includes(this.data.type);
  }
  created() {
    this.formData.nodeName = this.data.nodeName;
    this.formData.parallelism = this.data.parallelism;
  }
  checkNodeName(rule, value, callback) {
    const checkRes = checkName(value);
    if (value === '') {
      callback(new Error('请输入名称'));
    } else if (checkRes.res) {
      callback(new Error('名称中存在特殊字符' + checkRes.str));
    } else {
      if ((this.data.otherNodeNames || []).includes(value)) {
        callback(new Error('名称已存在'));
      } else {
        callback();
      }
    }
  }

  closeDialog() {
    this.$emit('update:visible', false);
  }

  submit() {
    const form: any = this.$refs.form;
    form.validate((valid: any) => {
      if (valid) {
        this.$emit('node-edit-confirm', this.formData);
        this.$emit('update:visible', false);
      } else {
        return false;
      }
    });
  }
}
</script>
<style lang="scss" scoped></style>
