const router = [
  {
    path: '/source',
    name: 'source',
    meta: {
      title: '资源管理',
      access: 'PA.RES',
      icon: 'iconfont icon-quanjuziyuan'
    },
    component: () => import('../views/source/index.vue'),
    children: [
      {
        path: '/source/importExport',
        name: 'importExport',
        meta: {
          access: 'PA.RES.PRIVATE.TRANSPORT.MENU', // 权限信息
          title: '导入导出'
        },
        component: () => import('../views/source/export-import/index.vue')
      }
    ]
  }
];

export { router };
