<template>
  <bs-dialog
    top="30px"
    :title="title"
    :visible.sync="visible"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    class="my-dialog_body"
  >
    <div id="terminal" :style="{ height: height + 'px' }"></div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import 'xterm/css/xterm.css';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import * as _ from 'lodash';
import { Component, Emit } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
@Component
export default class Term extends PaBase {
  visible = false;
  term!: any;
  uid!: string;
  stompClient!: any;
  title = '';
  height = 0;

  open(hostId: string, ip: string, dir: string) {
    const $ = require('jquery');
    const Stomp = require('stompjs');
    const SockJS = require('sockjs-client');
    this.title = '终端[' + ip + ']';
    this.uid = 'test' + _.now();
    $('#terminal').empty();
    const socket = new SockJS(this.getEndpointOyzc());
    this.stompClient = Stomp.over(socket);
    this.$nextTick(function () {
      /* eslint-disable-next-line */
      const that = this;
      this.stompClient.connect({}, function () {
        that.stompClient.subscribe(
          '/user/' + that.uid + '/queue/getResponse',
          function (response: any) {
            that.term.write(response.body);
          }
        );
        that.term = new Terminal({
          scrollback: 800,
          cursorBlink: true
        });
        const fitAddon = new FitAddon();
        that.term.loadAddon(fitAddon);
        that.term.open(document.getElementById('terminal'));
        that.term.focus();
        fitAddon.fit();
        let message = '\n';
        if (dir) {
          message = 'cd ' + dir + message;
        }
        that.stompClient.send(
          '/sendMessage',
          {},
          JSON.stringify({
            uid: that.uid,
            hostId,
            wp: that.term.cols,
            message: message
          })
        );
        that.term.write('登录中...');
        that.term.onData(function (data: any) {
          that.stompClient.send(
            '/sendMessage',
            {},
            JSON.stringify({
              uid: that.uid,
              hostId,
              wp: that.term.cols,
              message: data
            })
          );
        });
      });
    });
  }

  disconnect() {
    try {
      this.stompClient.disconnect();
    } catch (e) {
      // do nothing
    }
    try {
      this.term.dispose();
    } catch (e) {
      // do nothing
    }
  }

  beforeDestroy() {
    this.disconnect();
  }

  mounted() {
    this.height = window.innerHeight - 200;
  }
  /**
   * 关闭对话框并反馈给父组件
   */
  @Emit('close')
  private closeDialog() {
    this.disconnect();
    this.visible = false;
  }
}
</script>

<style scoped>
.my-dialog_body ::v-deep .el-dialog__body {
  padding: 0px;
}
</style>
