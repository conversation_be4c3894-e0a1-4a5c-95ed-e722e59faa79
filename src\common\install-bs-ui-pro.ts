import Vue from 'vue';
import BSUI, {
  BsProPage,
  BsProGrid,
  BsCode,
  BsProEditTable,
  BsProTable,
  BsDag,
  BsDrag,
  BsDragNode,
  BsProForm
} from 'bs-ui-pro';
import 'bs-ui-pro/lib/theme-chalk/default-index.css';
export default (): void => {
  Vue.use(BSUI);
  Vue.use(BsProPage);
  Vue.use(BsProGrid);
  Vue.use(BsCode);
  Vue.use(BsProEditTable);
  Vue.use(BsProTable, {
    height: '100vh - 180px',
    cellEmptyText: '-',
    actionLimit: 4
  });
  Vue.use(BsDag);
  Vue.use(BsDrag);
  Vue.use(BsDragNode);
  Vue.use(BsProForm);
};
