<template>
  <div v-loading="loading" class="flow">
    <div v-show="!fromMonitorFlow" class="flow-left">
      <header class="flow-left-header">
        <bs-select
          v-model="projectName"
          v-access="'PA.FLOW.FLOW_MGR.FILTER'"
          :options="projectList"
          filterable
          default-first-option
          :class="[isOnfocus ? 'hasBorder' : 'noBorder']"
          @visible-change="isVisible"
          @change="getCurProject($event)"
        />

        <span class="flow-left-header__oper">
          <el-tooltip v-access="'PA.FLOW.FLOW_MGR.IMPORT'" effect="light" content="导入流程">
            <i class="iconfont icon-daoru marR15" @click="showImportFlowDialog"></i>
          </el-tooltip>
          <el-tooltip v-access="'PA.FLOW.FLOW_MGR.EXPORT'" effect="light" content="导出流程">
            <i class="iconfont icon-daochu" @click="exportFlows"></i>
          </el-tooltip>
        </span>
      </header>
      <el-tabs v-model="activeName">
        <el-tab-pane label="流程管理" name="flowMgr">
          <flow-list
            ref="flowListRef"
            :project-id="projectId"
            @flowClick="handleFlowClick"
            @update="updateFlow"
            @config="handleConfig"
          />
        </el-tab-pane>
        <el-tab-pane label="流程组件" name="eleMgr" :disabled="!flowId">
          <element-list :editor="editor" :job-type="flowType" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="flow-right">
      <!-- ccc -->
      <div v-if="flowId" style="display: flex; height: 100%">
        <div style="width: calc(100% - 50px)">
          <header class="flow-right-header">
            <!-- <el-button @click="useSaveConfirm">使用this.$saveConfirm</el-button> -->
            <header-info
              :from-monitor-flow="fromMonitorFlow"
              :data="baseInfo"
              :online-success="onlineSuccess"
            />
            <header-action-bar
              v-if="!fromMonitorFlow"
              :data="baseInfo"
              :current-tab.sync="curTab"
              @click="handleClick"
            />
          </header>
          <!--流程画布-->
          <flow-canvas
            v-show="curTab === 'CANVAS' && flowId"
            ref="flowCanvasRef"
            v-loading="canvasLoading"
            :element-loading-text="canvasLoadingText"
            :class="{ showFlowTest: showFlowTest, showNodeInfoDrawer: showNodeInfoDrawer }"
            :flow-status="flowStatus"
            :flow-id="flowId"
            :flow-type="flowType"
            :content="jobContent"
            @node-detail="handleNodeDetail"
            @node-config="handleNodeConfig"
            @canvas-ready="handleCanvasReady"
            @content-change="handleContentChange"
            @canvas-active-change="(e) => canvasChange(e)"
          />
          <!-- 测试 -->
          <!-- @content-change="(e) => (isChange = Boolean(e))" -->
          <flow-test
            v-if="showFlowTest"
            :show.sync="showFlowTest"
            :data="rawData"
            :is-change="isChange"
          />
          <!--流程预警规则-->
          <flow-warning v-if="curTab === 'WARNING'" :flow-id="flowId" />
          <!--流程历史版本-->
          <flow-history v-if="curTab === 'HISTORY'" :flow-id="flowId" />
          <!--流程版本-->
          <flow-version
            v-if="curTab === 'VERSION'"
            :flow-id="flowId"
            :flow-type="flowType"
            @back="versionBack"
          />
          <!--流程日志-->
          <flow-log v-if="curTab === 'LOG'" :flow-id="flowId" />
          <!--流程源码-->
          <flow-code v-if="curTab === 'CODE'" :flow-data="rawData" />
        </div>
        <right-bar
          :flow-id="flowId"
          :flow-status="flowStatus"
          :current-tab.sync="curTab"
          :show-reso-config.sync="showResourceConfig"
          :show-comp-config.sync="showCompInfoConfig"
          :show-test.sync="showFlowTest"
          :show-compile="showFlowCompile"
          :from-monitor-flow="fromMonitorFlow"
          @config="handleConfig"
        />
      </div>
    </div>
    <import-flow-dialog
      v-if="importFlowDialogVisible"
      :project-id="projectId"
      :show.sync="importFlowDialogVisible"
      @close="refreshFlowList"
    />
    <!-- 流程监控运行信息 -->
    <node-info
      v-if="showNodeInfoDrawer"
      :visible.sync="showNodeInfoDrawer"
      :flow-id="flowId"
      :node-id="currentNodeId"
    />
    <!-- 流程资源配置 -->
    <resource-config
      v-if="showResourceConfig"
      :show.sync="showResourceConfig"
      :flow-id="flowId"
      :project-id="projectId"
      :is-full-screen="isFullScreen"
      @update="handleResourceUpdate"
    />
    <!-- 组件信息配置 -->
    <component-info-config
      :show.sync="showCompInfoConfig"
      :disabled="disabled"
      :data="componentInfo"
      :is-full-screen="isFullScreen"
      @submit="handleCompInfoSubmit"
    />
    <!-- 组件详情配置 -->
    <component-detail-config
      v-if="showCompDetailConfig"
      :show.sync="showCompDetailConfig"
      :data.sync="currentNodeData"
      :flow-data="rawData"
      @update="handleNodeConfigUpdate"
    />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Provide, Ref, Watch } from 'vue-property-decorator';
import { ContentType, CheckType } from './interface';
import { getFlowById, getProjectList, validateComponent, getFlowList } from '@/apis/flowNewApi';
import {
  URL_JOB_UPDATE,
  URL_JOB_COMPILE,
  URL_JOB_PRECOMPILE,
  URL_JOB_PREPUBLISH,
  URL_JOB_PUBLISH,
  URL_JOB_CANCEL_PUBLISH,
  URL_SQL_BATCH_JOB,
  URL_JOB_PREONLINE,
  URL_JOB_ONLINE,
  URL_JOB_OFFLINE
} from '@/apis/commonApi';
import { isFlinkSql } from '@/utils';
import { post, put } from '@/apis/utils/net';
import FlowList from './flow-list/index.vue';
import FlowCanvas from './page-content/canvas/canvas.vue';
import FlowStore from './store/flow-store';
import { cloneDeep } from 'lodash';
import { componentConfigPreTreated } from './utils';
import { MessageBox } from 'bs-ui-pro';

const validStr =
  'save' ||
  'handleCompile' ||
  'handleTest' ||
  'publish' ||
  'cancelPublish' ||
  'online' ||
  'offline' ||
  'blood';
@Component({
  name: 'Flow',
  components: {
    'flow-list': FlowList,
    'element-list': () => import('./element-list.vue'),
    'header-info': () => import('./header-info.vue'),
    'header-action-bar': () => import('./header-action-bar/index.vue'),
    'flow-canvas': () => import('./page-content/canvas/canvas.vue'),
    'import-flow-dialog': () => import('../components/import-flow-dialog.vue'),
    'node-info': () => import('./node-info.vue'),
    FlowTest: () => import('./page-content/test/index.vue'),
    FlowWarning: () => import('./page-content/warning.vue'),
    FlowHistory: () => import('./page-content/history.vue'),
    FlowVersion: () => import('./page-content/version.vue'),
    FlowLog: () => import('./page-content/log/index.vue'),
    flowCode: () => import('./page-content/code.vue'),
    RightBar: () => import('./page-content/right-bar.vue'),
    ResourceConfig: () => import('./page-content/config-popup/resource/resource-single-drawer.vue'),
    ComponentInfoConfig: () => import('./page-content/config-popup/component/info/index.vue'),
    ComponentDetailConfig: () => import('./page-content/config-popup/component/detail/index.vue')
  }
})
export default class Flow extends Vue {
  @Ref('flowListRef') readonly flowListRef!: FlowList;
  @Ref('flowCanvasRef') readonly flowCanvas!: FlowCanvas;
  activeName = 'flowMgr'; // 左侧tab切换：流程管理or流程组件
  curTab: ContentType = ContentType.CANVAS; // 当前内容显示区域
  flowStore: any = null;
  componentStore: any = null;
  editor = null;
  jobContent: any = {};
  originalJobContent = null;
  baseInfo: any = {};
  flowStatus = 'DEV';
  flowType = 'PROCESSFLOW';
  projectList: any = [];

  private isChange = false;
  private staticData: any = null;

  private loading = false;
  private isFullScreen = false;
  private importFlowDialogVisible = false;
  private showFlowTest = false;
  private showFlowCompile = false;
  private onlineSuccess = true;
  private showCompInfoConfig = false;
  private componentInfo: any = null;
  private showResourceConfig = false;
  private showCompDetailConfig = false;
  private currentNodeData: any = null;
  private fromMonitorFlow = false;
  private showNodeInfoDrawer = false;
  private currentNodeId = 0;
  rawData: any = null;
  //下拉框是否获得焦点
  isOnfocus = false;
  // 画布loading
  canvasLoading = false;
  canvasLoadingText = '拼命加载中...';
  projectName = '';
  isRender = false;
  isClose = false;
  needGetFlowData = true;

  get flowId() {
    return this.baseInfo.id;
  }

  get projectId() {
    return this.$route.query.id;
  }

  get disabled() {
    return this.flowStatus !== 'DEV';
  }

  @Watch('curTab')
  handlerCurTabChange(val: string) {
    val === 'CODE' && (this.rawData = this.getNewestFlow());
    if (val === 'CANVAS') {
      this.isRender = false;
      this.renderCanvas();
    } else this.jobContent = this.flowCanvas.getContent();
  }

  @Watch('$route.query', { immediate: true })
  handleQueryChange(query) {
    this.projectName = query.name || query.id || '';
    this.canvasLoading = false;
  }

  activated() {
    this.watchWindowOnload();
    this.curTab = ContentType.CANVAS;
    this.isClose = false;
    this.$route.query?.flowId && this.getCurFlow({ id: this.$route.query?.flowId });
    const { id, name, title } = this.$route.query;
    const data: any = localStorage.getItem('flow');
    const flowObj = JSON.parse(data);
    if (flowObj && flowObj.flowId) {
      this.$router.replace({
        name: 'refresh',
        query: { id, name, title, state: flowObj.state, flowId: flowObj.flowId }
      });
      localStorage.removeItem('flow');
    }
    this.handleTabsNavClose();
  }

  created() {
    this.fetchProjectList();
    if (this.$route.name === 'flowMonitorDetail') {
      this.fromMonitorFlow = true;
    }
    this.$watch('showFlowTest', () => this.flowCanvas.renderCanvas());
    this.$watch('showNodeInfoDrawer', () => this.flowCanvas.renderCanvas());
  }

  beforeRouteUpdate(to, from, next) {
    this.isRender = false;
    this.flowStatus === 'DEV' ? this.checkLeave(next) : next();
  }

  beforeRouteLeave(to, from, next) {
    this.isRender = false;
    if (to.name === 'refresh') return next();
    this.flowStatus === 'DEV' ? this.checkLeave(next) : next();
  }

  handleTabsNavClose() {
    (this as any).$tabsNav.beforeClose = async (pane) => {
      // 关闭页签为当前流程设计tab页时触发
      if (this.$route.fullPath === pane.name && pane.name.includes('/flow?id')) {
        const result = this.flowCanvas?.validteFlow('save');
        if (
          (this.isChange && this.$route.name === 'flowNew') ||
          (!result && this.$route.name === 'flowNew' && this.$route.query.flowId)
        ) {
          try {
            if (!result && this.baseInfo.id) {
              const res = await MessageBox.confirm(
                '流程中有节点字段可能发生变更,现在离开将会丢失修改的数据。确定离开吗？',
                '提示'
              );
              res === 'confirm' && (this.isClose = true);
            } else {
              const res = await this.$confirm(
                `流程${this.baseInfo.jobName}还没保存，确定离开吗？`,
                '提示'
              );
              res === 'confirm' && (this.isClose = true);
            }
            return true;
          } catch {
            throw new Error('error');
          }
        }
      }
      return;
    };
  }

  //select下拉框是否显示
  isVisible(val) {
    this.isOnfocus = val;
  }

  async nodesUpdateConfirm() {
    try {
      const msg = '流程中有节点字段可能发生变更,现在离开将会丢失修改的数据。确定离开吗？';
      await MessageBox.confirm(msg, '提示');
      return true;
    } catch {
      return false;
    }
  }

  // 监听页面离开事件
  async checkLeave(next: (flag?: boolean) => {}) {
    const result = this.flowCanvas?.validteFlow('save');
    if (!result && this.baseInfo.id && !this.isClose) {
      if (await this.nodesUpdateConfirm()) return next();
      return next(false);
    }
    if (!this.isChange || this.isClose) return next();
    try {
      await MessageBox.confirm(`流程${this.baseInfo.jobName}还没保存，确定离开吗？`, '提示');
      next();
    } catch (e) {
      next(false);
    }
  }

  exportFlows() {
    this.flowListRef.export(true);
  }

  // 获取所有的项目（下拉框数据，切换项目）
  async fetchProjectList() {
    const { success, msg, data } = await getProjectList({ name: '', sortByName: false });
    if (success) {
      this.projectList = data.map((item) => {
        return { label: item.projectName, value: item.projectId };
      });
      return;
    }
    this.$message.error({ message: msg, duration: 5000 });
  }

  // 监听浏览器页面刷新事件
  watchWindowOnload() {
    window.onbeforeunload = (e) => {
      if (this.isChange) {
        const info = window.event || e;
        info.returnValue = '确定离开当前页面吗？';
      }
    };
  }

  async handleProjectChange(value: any) {
    this.baseInfo.id = '';
    const name = this.projectList.filter((el) => el.value === value)[0].label;
    // 切换项目时，默认选中第一条流程数据
    const { success, data } = await getFlowList({
      id: value,
      name: '',
      jobStatus: 'ALL',
      jobType: 'ALL',
      jobRunTimeStatus: 'ALL',
      mode: 'ALL'
    });
    if (success) {
      const flowId = data.children[0]?.id || '';
      const title = data.children[0]?.jobName || name;
      if (
        (this as any).$tabsNav
          .getAllTabs()
          .find((item) => item.title === title && item.value.split('flowId=')[1] === flowId)
      ) {
        const value = (this as any).$tabsNav
          .getAllTabs()
          .find((item) => item.title === title).value;
        localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId }));
        this.$router.push({
          path: value
        });
      } else {
        this.$router.replace({
          name: 'refresh',
          query: { id: value, name, title, state: 'ALL', flowId }
        });
      }
    }
  }

  async getCurProject(value: string) {
    if (this.isChange) {
      const result = this.flowCanvas?.validteFlow('save');
      if (!result && this.baseInfo.id) {
        if (await this.nodesUpdateConfirm()) {
          this.handleProjectChange(value);
        } else {
          this.projectName = this.$route.query.id as string;
          return;
        }
      }
      if (!this.isChange) this.handleProjectChange(value);
      try {
        await MessageBox.confirm(`流程${this.baseInfo.jobName}还没保存，确定离开吗？`, '提示');
        this.isChange = false;
        this.handleProjectChange(value);
      } catch (e) {
        this.projectName = this.$route.query.id as string;
        return;
      }
    } else {
      this.handleProjectChange(value);
    }
  }

  showImportFlowDialog() {
    this.importFlowDialogVisible = true;
  }

  refreshFlowList() {
    (this.$refs as any).flowListRef.fetchList();
  }
  // 画布单击点击事件
  canvasChange(e) {
    this.componentInfo = e;
    // e为null 画布空白处点击直接返回
    if (!e) return;
    // 流程监控进来 || 已发布的流程，可以查看节点监控信息
    if (this.fromMonitorFlow || this.baseInfo.jobStatus === 'PROD') {
      this.currentNodeId = e.nodeId;
      const { type = '' } = this.jobContent.nodes.find((item) => item.nodeId === e.nodeId);
      this.showNodeInfoDrawer = !isFlinkSql(type);
    }
  }
  /* right start */

  /* 节点信息查看事件 */
  handleNodeDetail(data) {
    this.componentInfo = data;
    this.showCompInfoConfig = true;
  }
  /* 节点信息保存事件 */
  handleCompInfoSubmit(id: string, data: any) {
    this.flowCanvas?.updateNodeDetail(id, data);
  }
  /* 组件详情配置事件 */
  handleNodeConfig(data: any) {
    this.rawData = this.getNewestFlow(false);
    this.currentNodeData = data;
    this.showCompDetailConfig = true;
  }
  /* 组件详情配置更新事件 */
  handleNodeConfigUpdate(jobNode) {
    jobNode.jobId = this.flowId;
    this.flowCanvas?.updateNodeData(jobNode.nodeId, jobNode);
    this.flowCanvas?.updateNodeStatus(jobNode.nodeId, 1); // 状态更新
  }

  /* 配置按钮点击事件 */
  handleConfig() {
    if (Boolean(this.componentInfo)) {
      this.showCompInfoConfig = true;
      this.showResourceConfig = false;
      return;
    }
    if (this.isChange) return this.$tip.warning('请先保存再配置资源！');
    this.showCompInfoConfig = false;
    this.showResourceConfig = true;
  }
  /* 编译按钮点击事件 */
  async handleCompile() {
    try {
      this.loading = true;
      this.showFlowCompile = true;
      const result = await this.checkFlow('complier'); // 流程检查
      if (!result || this.disabled) {
        this.loading = false;
        this.showFlowCompile = false;
        return false;
      }
      this.jobContent = await this.preCompile();
      await this.realCompile();
      this.loading = false;
      this.showFlowCompile = false;
    } catch {
      this.loading = false;
      this.showFlowCompile = false;
    }
  }
  /* 预编译 */
  async preCompile() {
    const params = this.getNewestFlow();
    delete params.properties;
    const { success, data, error } = await post(URL_JOB_PRECOMPILE, params);
    if (success) return JSON.parse(data.content);
    this.$tip.error({ message: error, duration: 5000 });
    throw new Error(error);
  }
  /* 编译 */
  async realCompile() {
    const { success, error, msg } = await post(URL_JOB_COMPILE, this.getNewestFlow());
    if (success) return this.$tip.success(msg);
    this.$tip.error({ message: error, duration: 5000 });
    throw new Error(error);
  }
  /* 测试按钮点击事件 */
  async handleTest() {
    if (this.isChange) {
      this.$tip.warning('请先保存再测试！');
      return;
    }
    this.loading = true;
    const result = await this.checkFlow('test'); // 流程检查
    this.loading = false;
    if (!result) return;
    this.rawData = this.getNewestFlow(false);
    this.showFlowTest = true;
  }
  async handleResourceUpdate(data: any) {
    this.isRender = false;
    await this.getCurFlow(data);
    (this as any).$tabsNav.updateTabTitle(this.$route.fullPath, this.baseInfo.jobName);
    this.flowListRef.fetchList();
  }
  handleContentChange(e) {
    this.isChange = Boolean(e);
    if (this.showFlowTest) {
      setTimeout(() => (this.rawData = this.getNewestFlow(false)));
    }
  }
  versionBack() {
    this.getCurFlow({ id: this.flowId });
  }
  /* right end */

  /* 流程检查 */
  checkFlow(type: CheckType) {
    this.componentConfigPreTreated(type);
    // 1. 前端错误检查
    const result = this.flowCanvas?.validteFlow(type);
    if (type === 'save') {
      return result;
    } else {
      // 2. 后端错误检查
      return result ? this.validateFlowByBack() : result;
    }
  }
  /* 发布、编译前数据预处理 */
  componentConfigPreTreated(type: CheckType) {
    if (['save', 'complier', 'publish'].includes(type)) {
      this.jobContent = componentConfigPreTreated(
        this.$store.state.job.componentList,
        this.flowCanvas.getContent()
      );
    }
  }
  /* header start */

  /* 保存 */
  // 操作栏：header-action-bar的触发事件
  handleClick(name = validStr, value) {
    this[name]({ state: value });
  }
  async save() {
    const isEqual = this.originalJobContent === JSON.stringify(this.flowCanvas.content);
    if (this.isChange || !isEqual) {
      const result = await this.checkFlow('save');
      if (!result || this.disabled) return;
      const saveInfo = await this.$saveConfirm.show({
        isFlow: true,
        title: `${this.baseInfo.jobName}保存提示`
      });
      this.handleFlowSave(saveInfo, this.flowId);
      return;
    }
    this.$tip.warning('流程并未改变，不需要保存');
  }
  /* 处理更新时间 */
  handleContent(content: any) {
    const find = (arr, key, value) => arr.find((el: any) => el[key] === value);
    if (Array.isArray(content.nodes)) {
      content.nodes.forEach((o: any, index: number) => {
        const item = find(this.$store.state.job.componentList, 'operateType', o.operateType);
        if (item && item.paJobComponentList) {
          const target = find(item.paJobComponentList, 'className', o.className);
          content.nodes[index].updateTime = target && target.updateTime ? target.updateTime : null;
        }
      });
    }
    return JSON.stringify(content);
  }
  // 发布: 预发布成功后才能发布，需要判断流程是否保存(批量发布时不需要进行前后端校验)
  @Provide() async publish({ flowIds, isBatch }) {
    if (!flowIds && this.isChange && !isBatch) return this.$tip.warning('请先保存再发布！');
    let result = true;
    const loading = this.$loading({ lock: true, text: '发布中，请稍等...' });
    if (!isBatch) {
      result = await this.checkFlow('publish'); // 流程检查
    }
    if (!flowIds && (this.disabled || !result)) return loading.close();
    const ids = flowIds || [this.flowId];
    if (ids.length < 1) return this.$tip.warning('没有需要发布的流程');
    try {
      const { success, error } = await post(URL_JOB_PREPUBLISH, {
        relationBatchTag: false,
        jobs: ids
      });
      this.flowStatus = 'INPUB';
      if (success) return await this.realPublishProcess(ids, loading);
      this.$tip.error({ message: error, duration: 5000 });
      loading.close();
    } catch {
      loading.close();
    }
  }
  async realPublishProcess(ids: any[], loading: any) {
    const { success, msg, error } = await post(URL_JOB_PUBLISH, ids);
    if (success) {
      this.flowListRef.fetchList();
      if (ids.includes(this.flowId)) {
        this.flowStatus = 'PUB';
        this.getCurFlow({ id: this.flowId });
      }
      loading.close();
      this.flowListRef.selectedFlowIds = [];
      this.flowListRef.isCheckedAll = false;
      return this.$tip.success(msg);
    }
    this.flowStatus = 'DEV';
    this.$tip.error(error);
    loading.close();
  }

  /* 取消发布 */
  @Provide() async cancelPublish({ flowIds }) {
    if (!flowIds && this.flowStatus !== 'PUB') return;
    const ids = flowIds || [this.flowId];
    if (ids.length < 1) return this.$tip.warning('没有需要取消发布的流程');
    const loading = this.$loading({ lock: true, text: '取消发布中，请稍等...' });
    try {
      const { success, msg, error } = await post(URL_JOB_CANCEL_PUBLISH, ids);
      if (success) {
        this.flowListRef.fetchList();
        if (ids.includes(this.flowId)) this.getCurFlow({ id: this.flowId });
        loading.close();
        this.flowListRef.selectedFlowIds = [];
        this.flowListRef.isCheckedAll = false;
        return this.$tip.success(msg);
      }
      this.$tip.error({ message: error, duration: 5000 });
      loading.close();
    } catch {
      loading.close();
    }
  }
  isBatchFlow() {
    try {
      return JSON.parse(this.getNewestFlow()?.properties)?.mode === 'batch';
    } catch {
      return;
    }
  }

  /* 启动 */
  @Provide() async online({ state: fromLastCheckpoint, flowIds }) {
    if (!fromLastCheckpoint) {
      await this.$confirm('确定无状态启动吗？', '提示', { type: 'warning' });
    }
    if (!flowIds && this.flowStatus !== 'PUB') return;
    const params = flowIds || [{ id: this.flowId, fromLastCheckpoint }];
    if (params.length < 1) return this.$tip.warning('没有需要上线的流程');
    const loading = this.$loading({ lock: true, text: '启动中，请稍等...' });
    try {
      const preParams = params.map(({ id }) => id);
      if (!flowIds && this.isBatchFlow()) {
        return this.realOnline(URL_SQL_BATCH_JOB, params, preParams, loading);
      }
      const { success, data, error } = await post(URL_JOB_PREONLINE, {
        relationBatchTag: false,
        jobs: preParams
      });
      if (success) return this.realOnline(URL_JOB_ONLINE, params, preParams, loading);
      this.$tip.errorPro(error, data);
      loading.close();
    } catch {
      loading.close();
    }
  }
  async realOnline(url, params, preParams, loading: any) {
    this.flowStatus = 'INPROD';
    const { success, msg, data, error } = await post(url, params);
    this.onlineSuccess = success;
    if (success) {
      this.flowListRef.fetchList();
      if (preParams.includes(this.flowId)) {
        this.flowStatus = 'PROD';
        this.getCurFlow({ id: this.flowId });
      }
      this.$tip.success(msg);
      return loading.close();
    }
    this.flowStatus = 'PUB';
    this.$tip.errorPro(error, data);
    loading.close();
  }

  /* 下线：停止、停止并保留状态 */
  @Provide() async offline({ state: savepoint, flowIds }) {
    if (!savepoint) {
      await this.$confirm('确定不保留状态停止吗？', '提示', { type: 'warning' });
    }
    if (!flowIds && this.flowStatus !== 'PROD') return;
    const params = flowIds || [{ jobId: this.flowId, savepoint }];
    if (params.length < 1) return this.$tip.warning('没有需要停止的流程');
    const loading = this.$loading({ lock: true, text: '停止中，请稍等...' });
    try {
      const { success, msg, error } = await post(URL_JOB_OFFLINE, params);
      if (success) {
        this.flowListRef.fetchList();
        if (params.map(({ jobId }) => jobId).includes(this.flowId))
          this.getCurFlow({ id: this.flowId });
        this.$tip.success(msg);
        this.flowListRef.selectedFlowIds = [];
        this.flowListRef.isCheckedAll = false;
        return loading.close();
      }
      this.$tip.error({ message: error, duration: 5000 });
      loading.close();
    } catch {
      loading.close();
    }
  }
  /* 血缘关系 */
  blood() {
    if (!this.flowId) return;
    this.$router.push({
      name: 'bloodRelation',
      params: { resType: 'JOB', serviceId: this.flowId }
    });
  }

  async validateFlowByBack() {
    const { success, data, msg } = await validateComponent(this.getNewestFlow(false));
    if (!success) {
      // 节点错误信息
      if (data) {
        Object.keys(data).forEach((key) => {
          this.flowCanvas?.updateNodeStatus(key, 2, data[key].exceptionInfo);
        });
      } else {
        this.$tip.error({ message: msg, duration: 5000 });
      }
      this.loading = false;
      this.showFlowCompile = false;
    }
    return success;
  }
  /* header end */

  getNewestFlow(isString = true) {
    const content = this.flowCanvas.getContent();
    return {
      ...cloneDeep(this.staticData),
      content: isString ? JSON.stringify(content) : cloneDeep(content)
    };
  }

  async handleFlowSave(saveInfo: object | null, flowId: string, needRefresh = true) {
    const loading = this.$loading({ lock: true, text: '保存中，请稍等...' });
    try {
      const flowData = { ...this.getNewestFlow(false), ...saveInfo };
      flowData.content = this.handleContent(flowData.content);
      delete flowData.showOverlay;
      delete flowData.properties;
      const { success, msg, error } = await put(URL_JOB_UPDATE, flowData);
      if (success) {
        this.$tip.success(msg);
        this.isChange = false;
        needRefresh && this.getCurFlow({ id: flowId });
        return loading.close();
      }
      this.$tip.error(error);
      loading.close();
    } catch {
      loading.close();
    }
  }

  async handleFlowClick(data: any) {
    if (this.isChange) {
      const result = await this.checkFlow('save');
      if (!result || this.disabled) return;
      let saveInfo = null;
      try {
        saveInfo = await this.$saveConfirm.show({
          isFlow: true,
          title: `${this.baseInfo.jobName}保存提示`
        });
      } catch {
        this.isChange = false;
        const title = data.jobName;
        if (
          (this as any).$tabsNav
            .getAllTabs()
            .find((item) => item.title === title && item.value.split('flowId=')[1] === data.id)
        ) {
          const value = (this as any).$tabsNav
            .getAllTabs()
            .find(
              (item) => item.title === title && item.value.split('flowId=')[1] === data.id
            ).value;
          localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId: data.id }));
          this.$router.push({
            path: value
          });
        } else {
          this.$router.replace({
            name: 'refresh',
            query: {
              ...this.$route.query,
              title,
              state: data.state,
              flowId: data.id
            }
          });
        }
        return;
      }
      this.handleFlowSave(saveInfo, data.id, false);
    }
    // 更改路由的fullPath
    const title = data.jobName;
    if (
      (this as any).$tabsNav
        .getAllTabs()
        .find((item) => item.title === title && item.value.split('flowId=')[1] === data.id)
    ) {
      const value = (this as any).$tabsNav
        .getAllTabs()
        .find((item) => item.title === title && item.value.split('flowId=')[1] === data.id).value;
      localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId: data.id }));
      this.$router.push({
        path: value
      });
    } else {
      this.$router.replace({
        name: 'refresh',
        query: {
          ...this.$route.query,
          title,
          state: data.state,
          flowId: data.id
        }
      });
    }
  }

  updateFlow() {
    this.isRender = false;
    this.flowId && this.getCurFlow({ id: this.flowId });
  }
  async beforeRouteEnter(to, from, next) {
    if (!to.query.flowId) return next();
    to.query.state = to.query.state || 'ALL';
    if (!to.query.id) {
      const { data } = await getFlowById({ id: to.query.flowId, isMonitor: false });
      to.query.id = data.projectId;
      to.query.name = data.projectName;
      to.meta.title = data.projectName;
      return next((vm: any) => vm.initFlow(data));
    }
    next();
  }

  async initFlow(data: any) {
    if (!data) return;
    this.needGetFlowData = false;
    this.onlineSuccess = true;
    this.flowStore = new FlowStore(data);
    this.flowStatus = this.flowStore.baseInfo.jobStatus;
    this.flowType = this.flowStore.baseInfo.jobType;
    this.baseInfo = this.flowStore.baseInfo;
    this.jobContent = this.flowStore.content;
    this.staticData = this.flowStore.staticData;
    this.handleCanvasLoading(data);
    await this.$nextTick();
    this.renderCanvas();
    this.needGetFlowData = true;
  }

  // 获取当前流程信息
  async getCurFlow({ id }) {
    if (!id || !this.needGetFlowData) return; // 处理id为空的情况：项目下无流程
    const { data } = await getFlowById({ id: id, isMonitor: false });
    this.originalJobContent = data.content;
    this.initFlow(data);
  }

  // 处理画布loading逻辑
  handleCanvasLoading(data) {
    const { content, jobStatus } = data;
    const isEmpty = JSON.stringify(content) === '{"nodes":[],"edges":[]}';
    if (!isEmpty && ['INPROD', 'INPUB'].includes(jobStatus)) {
      this.canvasLoading = true;
      this.canvasLoadingText = '拼命加载中...';
    }
  }

  handleCanvasReady(editor) {
    this.editor = editor;
    this.renderCanvas();
  }

  renderCanvas() {
    if (!this.isRender && this.flowCanvas) {
      this.flowCanvas.renderCanvas();
      this.handleTabsNavClose();
      this.isRender = true;
    }
  }

  // 处理请求后的回调
  handleResponse(success, msg) {
    if (success) {
      this.$message.success(msg);
      this.flowListRef.fetchList();
      if (this.flowId) {
        this.getCurFlow({ id: this.flowId });
      }
      return;
    }
    this.$message.error({ message: msg, duration: 5000 });
  }

  // 查看流程数据是否有变更，进行保存提示
  // dataChange(change: boolean) {
  //   this.flowStore.dataChange = change;
  // }
}
</script>
<style lang="scss" scoped src="./index.scss"></style>
