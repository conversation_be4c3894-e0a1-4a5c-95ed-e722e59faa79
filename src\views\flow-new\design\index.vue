<template>
  <div style="position: relative">
    <bs-split v-loading="loading" class="flow" :size="splitSize" :min="300" :max="0.5" :on-drag-end="splitDragEnd">
      <!-- 选择目录时将flowId置空 隐藏对应tab -->
      <FlowLeft
        v-show="!fromMonitorFlow"
        slot="left"
        :flow-id="showDirEmpty ? '' : flowId"
        :project-id="projectId"
        :job-type="flowType"
        :project-list="projectList"
        @project-change="handleProjectChange"
      >
        <flow-list
          slot="flowList"
          ref="flowListRef"
          :project-id="projectId"
          @flow-click="handleFlowClick"
          @update="updateFlow"
          @config="handleConfig"
          @publish="publish"
          @cancelPublish="cancelPublish"
          @offline="offline"
          @setCodeError="(data) => flowSqlCode.setCodeError(data)"
          @setCanvasError="(data) => flowCanvas.updateNodeStatus(data)"
          @toggle-empty="handleToggleEmpty"
          @update-flow="handleFlowUpdate"
        />
        <element-list slot="eleList" :editor="editor" :flow-type="flowType" />
        <data-set slot="dataSet" :flow-status="flowStatus" @click="handleDataSetClick" />
      </FlowLeft>
      <div
        slot="right"
        v-loading="rightContentLoading"
        style="overflow: hidden"
        :element-loading-text="rightContentLoadingText"
        class="flow-right"
      >
        <!-- ccc -->
        <div v-if="flowId" style="display: flex; height: 100%">
          <div style="width: calc(100% - 50px)">
            <header class="flow-right-header">
              <!-- <el-button @click="useSaveConfirm">使用this.$saveConfirm</el-button> -->
              <header-info :from-monitor-flow="fromMonitorFlow" :data="baseInfo" :online-success="onlineSuccess" />
              <header-action-bar
                v-if="!fromMonitorFlow"
                :flow-type="flowType"
                :job-status="flowStatus"
                :job-runtime-id="baseInfo.jobRuntimeId"
                :current-tab.sync="curTab"
                @click="handleClick"
              />
            </header>
            <!--jobType为PROCESSFLOW-流程画布-->
            <flow-canvas
              v-if="isDS"
              v-show="showCanvas"
              ref="flowCanvasRef"
              :class="{ showFlowTest: showFlowTest }"
              :flow-status="flowStatus"
              :flow-id="flowId"
              :flow-type="flowType"
              :content="jobContent"
              @node-detail="handleNodeDetail"
              @node-config="handleNodeConfig"
              @canvas-ready="handleCanvasReady"
              @content-change="handleContentChange"
              @canvas-active-change="(e) => canvasChange(e)"
              @removeErrorInfo="(flowId) => removeError(flowId)"
            />
            <!-- SQL流程代码编辑页 -->
            <flow-sql-code
              v-if="showSqlCanvas"
              ref="flowSqlCodeRef"
              :class="{ showFlowTest: showFlowTest }"
              :content="jobContent"
              :flow-id="flowId"
              :flow-status="flowStatus"
              :is-running="isRunning"
              @content-change="handleContentChange"
              @real-test="handleRealTest"
              @auto-save="handleAutoSave"
              @canvas-click="handleOpenRunInfoDrawer"
              @removeErrorInfo="(flowId) => removeError(flowId)"
            />
            <!-- JAR类型流程编辑页  -->
            <flow-jar
              v-if="showJarContent"
              ref="flowJarRef"
              :disabled="disabled"
              :from-monitor-flow="fromMonitorFlow"
              :data="Object.assign(baseInfo, jobContent)"
              @content-change="handleContentChange"
            />
            <!-- 会话启动的时候展示该图标 -->
            <div v-if="isRunning" class="test-reslt--oper" @click="showTestResultDrawer = true">
              <el-tooltip effect="light" :content="$t('pa.realTestResult')" placement="bottom">
                <i class="iconfont icon-ceshijieguo"></i>
              </el-tooltip>
            </div>
            <!-- 模拟输入测试 -->
            <flow-test
              v-if="showFlowTest"
              :show.sync="showFlowTest"
              :data="rawData"
              :sql-table="sqlTable"
              :is-change="isChange"
              @set-code-error="setCodeError"
            />
            <!-- 真实输入测试抽屉 -->
            <sql-test-result-drawer
              v-if="showTestResultDrawer"
              :visible.sync="showTestResultDrawer"
              :is-running="isRunning"
              :test-log="testLog"
              :active-name="testResultActiveTab"
              :column-data="testResultColumns"
              :table-data="testResultTableData"
              @stopExcute="handleStopExcute"
            />
            <!--流程预警规则-->
            <flow-warning v-if="curTab === 'WARNING'" :flow-id="flowId" />
            <!--流程历史版本-->
            <flow-history v-if="curTab === 'HISTORY'" :flow-id="flowId" />
            <!--流程版本-->
            <flow-version v-if="curTab === 'VERSION'" :flow-id="flowId" :flow-type="flowType" @back="versionBack" />
            <!--流程日志-->
            <flow-log v-if="curTab === 'LOG'" :flow-id="flowId" />
            <!--流程源码-->
            <flow-code v-if="showFlowCode" :flow-data="rawData" />
          </div>
          <right-bar
            :flow-id="flowId"
            :flow-type="flowType"
            :flow-status="flowStatus"
            :current-tab.sync="curTab"
            :show-reso-config.sync="showResourceConfig"
            :show-comp-config.sync="showCompInfoConfig"
            :show-test.sync="showFlowTest"
            :show-session="showSessionDrawer"
            :show-snippet-library="showSnippetLibraryDrawer"
            :from-monitor-flow="fromMonitorFlow"
            @config="handleConfig"
            @session="handleSession"
            @snippet-library="showSnippetLibrary"
          />
        </div>
        <p v-if="showDirEmpty" class="flow-right__empty">{{ $t('pa.flow.msg266') }}</p>
      </div>
    </bs-split>
    <!-- 流程监控节点运行信息 -->
    <node-info v-if="showNodeInfoDrawer" :visible.sync="showNodeInfoDrawer" :flow-id="flowId" :node-id="currentNodeId" />
    <!-- 流程监控运行信息 -->
    <run-info-drawer v-if="showRunInfoDrawer" :show.sync="showRunInfoDrawer" :flow-id="flowId" />
    <!-- 流程资源配置 -->
    <resource-config
      v-if="showResourceConfig"
      :show.sync="showResourceConfig"
      :flow-id="flowId"
      :project-id="projectId"
      :is-full-screen="isFullScreen"
      @update="handleResourceUpdate"
    />
    <!-- 组件信息配置 -->
    <component-info-config
      :show.sync="showCompInfoConfig"
      :disabled="disabled"
      :data="componentInfo"
      :is-full-screen="isFullScreen"
      @submit="handleCompInfoSubmit"
    />
    <!-- 组件详情配置 -->
    <component-detail-config
      v-if="showCompDetailConfig"
      :show.sync="showCompDetailConfig"
      :data.sync="currentNodeData"
      :flow-data="rawData"
      @update="handleNodeConfigUpdate"
    />
    <session-drawer
      v-if="showSessionDrawer"
      :show.sync="showSessionDrawer"
      :readonly="disabled"
      @sessionConn="handleSessionConn"
      @sessionStopConn="handleSessionStopConn"
    />
    <code-snippet-drawer
      v-if="showSnippetLibraryDrawer"
      :show.sync="showSnippetLibraryDrawer"
      @update="handleCodeDrawerUpdate"
    />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Ref, Watch } from 'vue-property-decorator';
import { ContentType, CheckType, JobStatusMap, JobStatus, MsgType, IColumn } from './interface';
import { getFlowById, getProjectList, validateComponent, getFlowList, updateFlow } from '@/apis/flowNewApi';
import { isFlinkSql } from '@/utils';
import FlowLeft from './components/flow-left.vue';
import FlowList from './flow-list/index.vue';
import FlowCanvas from './page-content/canvas/canvas.vue';
import FlowStore from './store/flow-store';
import cloneDeep from 'lodash/cloneDeep';
import { componentConfigPreTreated, compileFlowStore, transformLineErrorInfo } from './utils';
import { MessageBox } from 'bs-ui-pro';
import BeforeLeave from './mixins/before-leave';
import Operator from './mixins/operator';
import SqlRealTest from './mixins/sql-real-test';
import { SET_FLOW_SEARCH_OBJ } from '@/store/event-name';
import SessionDrawer from './page-content/session-drawer.vue';
import CodeSnippetDrawer from './page-content/code-snippet-drawer/index.vue';
import { getSqlFields } from '@/apis/flowTestApi';
import FlowJar from './page-content/flow-jar.vue';
import './flow-list/style/flow-list.scss';
import openNodeConfigDialog from './page-content/config-popup/component/detail/modals/node-config-new';

const validStr =
  'save' || 'handleCompile' || 'handleTest' || 'publish' || 'cancelPublish' || 'online' || 'offline' || 'blood';
@Component({
  name: 'Flow',
  components: {
    FlowLeft: FlowLeft,
    SessionDrawer,
    SqlTestResultDrawer: () => import('./page-content/sql-test-result-drawer.vue'),
    CodeSnippetDrawer,
    'flow-list': FlowList,
    'element-list': () => import('./element-list.vue'),
    'data-set': () => import('./data-set/index.vue'),
    'header-info': () => import('./header-info.vue'),
    'header-action-bar': () => import('./header-action-bar/index.vue'),
    'flow-canvas': () => import('./page-content/canvas/canvas.vue'),
    'flow-sql-code': () => import('./page-content/sql-code/index.vue'),
    'flow-jar': () => import('./page-content/flow-jar.vue'),
    'node-info': () => import('./node-info.vue'),
    FlowTest: () => import('./page-content/test/index.vue'),
    FlowWarning: () => import('./page-content/warning.vue'),
    FlowHistory: () => import('./page-content/history.vue'),
    FlowVersion: () => import('./page-content/version.vue'),
    FlowLog: () => import('./page-content/log/index.vue'),
    flowCode: () => import('./page-content/code.vue'),
    RightBar: () => import('./page-content/right-bar.vue'),
    ResourceConfig: () => import('./page-content/config-popup/single-resource-config-drawer.vue'),
    ComponentInfoConfig: () => import('./page-content/config-popup/component/info/index.vue'),
    ComponentDetailConfig: () => import('./page-content/config-popup/component/detail/index.vue'),
    RunInfoDrawer: () => import('./page-content/components/run-info/run-info-drawer.vue')
  },
  mixins: [BeforeLeave, Operator, SqlRealTest]
})
export default class Flow extends Vue {
  @Ref('flowListRef') readonly flowListRef!: FlowList;
  @Ref('flowCanvasRef') readonly flowCanvas!: FlowCanvas;
  @Ref('flowSqlCodeRef') readonly flowSqlCode!: any;
  @Ref('flowJarRef') readonly flowJarRef!: FlowJar;
  curTab: ContentType = ContentType.SQL_CODE; // 当前内容显示区域
  flowStore: any = null;
  componentStore: any = null;
  editor = null;
  jobContent: any = {};
  originalJobContent = null;
  baseInfo: any = {};
  flowStatus: keyof typeof JobStatus = JobStatus.DEV;
  flowType = 'PROCESSFLOW';
  projectList: any = [];

  isChange = false;
  // 用于自动保存处理是否变更
  isChangeForAutoSaving = false;
  private staticData: any = null;

  private loading = false;
  private isFullScreen = false;
  // 流程测试相关
  showFlowTest = false;
  sqlTestResult: any = {};
  // sql类型的流程测试表和字段相关信息
  sqlTable: any = {};

  private onlineSuccess = true;
  private showCompInfoConfig = false;
  private componentInfo: any = null;
  private showResourceConfig = false;
  private showCompDetailConfig = false;
  private currentNodeData: any = null;
  private showNodeInfoDrawer = false;
  private currentNodeId = 0;
  rawData: any = null;
  isRender = false;
  needGetFlowData = true;
  rightContentLoading: any = null;
  rightContentLoadingText = this.$t('pa.loading');
  showSnippetLibraryDrawer = false; // 控制代码片段库抽屉显隐
  showSessionDrawer = false; /* SQL流程测试相关 start */
  showTestResultDrawer = false;
  testResultActiveTab = 'RESULT';
  testResultColumns: IColumn[] = [{ label: '', value: '' }];
  testResultTableData: any = [];
  testLog: any = [];
  isRunning = false; /* SQL流程测试相关 end */

  showRunInfoDrawer = false;

  // 展示目录对应的右侧空白内容区域
  showDirEmpty = false;
  splitSize = 300;
  get flowId() {
    return this.baseInfo.id;
  }

  get projectId() {
    return this.$route.query.id;
  }

  get disabled() {
    return this.flowStatus !== JobStatus.DEV;
  }
  // 是否从流程监控跳转
  get fromMonitorFlow() {
    return this.$route.name === 'flowMonitorDetail';
  }

  get isJar() {
    return this.flowType === 'UDJ';
  }

  get isDS() {
    return this.flowType === 'PROCESSFLOW';
  }
  get showCanvas() {
    return this.curTab === 'CANVAS' && this.flowId && this.isDS;
  }

  get showSqlCanvas() {
    return this.curTab === 'CANVAS' && this.flowId && isFlinkSql(this.flowType);
  }

  get showJarContent() {
    return this.curTab === 'CANVAS' && this.flowId && this.isJar;
  }

  get showFlowCode() {
    return this.curTab === 'CODE' && !isFlinkSql(this.flowType);
  }
  @Watch('curTab')
  handlerCurTabChange(val: string, old: string) {
    this.showNodeInfoDrawer = false;
    this.showRunInfoDrawer = false;
    // 从画布或者代码编辑框离开时 优先获取最新内容
    if (old === 'CANVAS') {
      this.jobContent = isFlinkSql(this.flowType) ? this.flowSqlCode.getContent() : this.flowCanvas.getContent();
    }
    val === 'CODE' && (this.rawData = this.getNewestFlow());
    val !== 'CANVAS' && (this.showSessionDrawer = this.showTestResultDrawer = this.showSnippetLibraryDrawer = false);
    if (val === 'CANVAS' && !isFlinkSql(this.flowType)) {
      this.isRender = false;
      this.renderCanvas();
    }
  }

  @Watch('$route.query', { immediate: true })
  handleQueryChange() {
    this.closeLoading();
  }

  activated() {
    this.curTab = ContentType.CANVAS;
    const { id, name, title, flowId } = this.$route.query || {};
    // 获取当前流程信息（若路由带了流程ID）
    this.getCurFlow({ id: flowId });
    const data: any = localStorage.getItem('flow');
    const flowObj = JSON.parse(data || '{}');
    if (flowObj && flowObj.flowId) {
      this.$router.replace({
        name: 'refresh',
        query: { id, name, title, state: flowObj.state, flowId: flowObj.flowId }
      });
      localStorage.removeItem('flow');
    }
    this['handleTabsNavClose']();
    // 处理流程监控详情画布无法拖动的问题  传入0时 split计算有问题
    this.splitSize = this.fromMonitorFlow ? 1 : Math.max(Number(sessionStorage.getItem(this.projectId as string)), 300);
    /* test: 画布未保存，切换 tab 后切回，偶尔出现画布未刷新情况 */
    this.$nextTick(() => this.renderCanvas());
  }

  created() {
    // 处理查看测试结果得画布高亮
    this.showCanvas &&
      this.$on('highlight', (val) => {
        (this.flowCanvas as any).highlightNodes(val || []);
      });
    this.getProjectList();
  }

  // 显示右侧区域得loading
  showLoading(text = this.$t('pa.loading')) {
    this.rightContentLoading = true;
    this.rightContentLoadingText = text;
  }
  // 关闭loading
  closeLoading() {
    this.rightContentLoading = false;
  }

  // 获取所有的项目（下拉框数据，切换项目）
  async getProjectList() {
    const { success, msg, data } = await getProjectList({ name: '', sortByName: false });
    if (!success) return this.$message.error({ message: msg, duration: 5000 });
    this.projectList = data.map((item) => ({ label: item.projectName, value: item.projectId }));
  }

  // 项目变更时更新流程列表
  async getFlowList(value: any) {
    this.$store.commit(SET_FLOW_SEARCH_OBJ, null); // 清空store里的搜索条件
    this.baseInfo.id = '';
    const name = this.projectList.filter((el) => el.value === value)[0].label;
    // 切换项目时，默认选中第一条流程数据
    const { success, data } = await getFlowList({
      id: value,
      name: '',
      jobStatus: 'ALL',
      jobType: 'ALL',
      jobRunTimeStatus: 'ALL',
      mode: 'ALL',
      clusterType: 'ALL'
    });
    if (success) {
      const { nodeId, nodeName, nodeType } = (data || {}).children[0] || {};
      const existTab = (this as any).$tabNav
        .getAllTabs()
        .find((item) => item.title === nodeName && item.value.split('flowId=')[1] === nodeId);
      if (existTab) {
        const value = existTab.value;
        localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId: nodeId }));
        this.$router.push({
          path: value
        });
      } else {
        this.$router.replace({
          name: 'refresh',
          query: { id: value, name, title: nodeName || name, state: 'ALL', flowId: nodeType === 'JOB' ? nodeId : '' }
        });
      }
    }
  }
  // 处理当前项目change回调
  async handleProjectChange(value: string) {
    if (!this.isChange) return this.getFlowList(value);
    try {
      await MessageBox.confirm(this.$t('pa.flow.msg21', [this.baseInfo.jobName]), this.$t('pa.prompt'), {
        type: 'warning',
        customClass: 'flow-list__confirm'
      });
      this.isChange = false;
      this.getFlowList(value);
    } catch (e) {
      // do nothing
    }
  }
  refreshFlowList() {
    this.flowListRef.fetchList();
  }
  // 画布单击点击事件
  canvasChange(e) {
    this.componentInfo = e;
    // e为null 画布空白处点击直接返回
    if (!e) return this.handleOpenRunInfoDrawer();
    // 流程监控进来 || 已发布的流程，可以查看节点监控信息
    if (this.fromMonitorFlow || this.baseInfo.jobStatus === JobStatus.PROD) {
      this.currentNodeId = e.nodeId;
      const { type = '' } = this.jobContent.nodes.find((item) => item.nodeId === e.nodeId);
      this.showNodeInfoDrawer = !isFlinkSql(type);
      this.showRunInfoDrawer = false;
    }
  }
  handleOpenRunInfoDrawer() {
    if (this.fromMonitorFlow || this.baseInfo.jobStatus === JobStatus.PROD) {
      this.showNodeInfoDrawer = false;
      this.showRunInfoDrawer = true;
    }
  }
  /* right start */

  /* 节点信息查看事件 */
  handleNodeDetail(data) {
    this.componentInfo = data;
    this.showCompInfoConfig = true;
  }
  /* 节点信息保存事件 */
  handleCompInfoSubmit(id: string, data: any) {
    this.flowCanvas?.updateNodeDetail(id, data);
  }
  /* 组件详情配置事件 */
  async handleNodeConfig(data: any) {
    this.rawData = this.getNewestFlow(false);
    this.currentNodeData = data;
    // 新配置逻辑
    const properties = this.$store.getters.componentListMap.get(data.className) || {};
    if (properties.newComponent) {
      const res = await openNodeConfigDialog({ data, jobData: this.rawData, disabled: this.flowStatus !== 'DEV' });
      res.success && this.handleNodeConfigUpdate(res.data);
    } else {
      this.showCompDetailConfig = true;
    }
  }
  /* 组件详情配置更新事件 */
  handleNodeConfigUpdate(jobNode) {
    jobNode.jobId = this.flowId;
    this.flowCanvas?.updateNodeData(jobNode.nodeId, jobNode);
    this.flowCanvas?.updateNodeStatus({ id: jobNode.nodeId, status: 1 }); // 状态更新
  }

  /* 配置按钮点击事件 */
  handleConfig() {
    this.showSessionDrawer = this.showTestResultDrawer = this.showSnippetLibraryDrawer = false; // 关闭其他抽屉
    if (this.isChange) return this.$tip.warning(this.$t('pa.flow.msg22'));
    this.showCompInfoConfig = false;
    this.showResourceConfig = true;
  }

  handleSession() {
    if (this.isChange) {
      this.$tip.warning(this.$t('pa.flow.msg1'));
      return;
    }
    this.showCompInfoConfig = this.showResourceConfig = this.showSnippetLibraryDrawer = false; // 关闭其他抽屉
    this.showSessionDrawer = !this.showSessionDrawer;
  }

  /* 设置代码报错 */
  setCodeError(error) {
    const lineErrors = JSON.parse(error || '[]');
    (this as any).flowSqlCode.setCodeError(transformLineErrorInfo(lineErrors));
    this.$tip.error(this.$t('pa.flow.msg24'));
  }

  /* 测试按钮点击事件 */
  async handleTest() {
    if (this.isChange) {
      this.$tip.warning(this.$t('pa.flow.msg4'));
      return;
    }
    this.loading = true;
    let result = true;
    if (isFlinkSql(this.flowType)) {
      // // 通过获取sql流程测试表和字段的接口来判断编译是否正确
      const { success, error, data, msgType } = await getSqlFields(this.flowId);
      if (!success) {
        if (msgType === MsgType.LINE_MESSAGE) {
          this.setCodeError(error);
        } else {
          this.$tip.error(error);
        }
      } else {
        // 此处信息用外部传入主要是因为节约代码解析时间
        this.sqlTable = data;
      }
      result = success;
    } else {
      result = await this.checkFlow('test'); // 流程检查
    }
    this.loading = false;
    if (!result) return;
    this.rawData = this.getNewestFlow(false);
    this.showFlowTest = true;
  }
  async handleResourceUpdate(data: any) {
    this.isRender = false;
    await this.getCurFlow(data);
    (this as any).$tabNav.updateTabTitle(this.$route.fullPath, this.baseInfo.jobName);
    this.flowListRef.fetchList();
  }
  handleContentChange(e) {
    this.isChange = Boolean(e);
    this.isChangeForAutoSaving = Boolean(e);
    // this.showFlowTest && (this.rawData = this.getNewestFlow(false));
    if (this.showFlowTest) {
      setTimeout(() => (this.rawData = this.getNewestFlow(false)));
    }
  }
  versionBack() {
    this.getCurFlow({ id: this.flowId });
    /* 更新流程列表 */
    this.flowListRef.fetchList();
  }
  showSnippetLibrary() {
    this.showCompInfoConfig = this.showResourceConfig = this.showSessionDrawer = this.showTestResultDrawer = false; // 关闭其他抽屉
    this.showSnippetLibraryDrawer = !this.showSnippetLibraryDrawer;
  }

  /* right end */

  /* 流程检查 */
  checkFlow(type: CheckType) {
    this.componentConfigPreTreated(type);
    // 1. 前端错误检查
    const result = this.flowCanvas?.validteFlow(type);
    if (type === 'save') {
      return result;
    } else {
      // 2. 后端错误检查
      return result ? this.validateFlowByBack() : result;
    }
  }
  /* 发布、编译前数据预处理 */
  componentConfigPreTreated(type: CheckType) {
    if (['save', 'complier', 'publish'].includes(type)) {
      this.jobContent = componentConfigPreTreated(this.$store.getters.componentList, this.getCanvasContent());
    }
  }
  /* header start */

  /* 保存 */
  // 操作栏：header-action-bar的触发事件
  handleClick(name = validStr, value) {
    this[name]({ state: value });
  }

  getCanvasContent() {
    return this.showCanvas ? this.flowCanvas.getContent() : this.showSqlCanvas ? this.flowSqlCode.getContent() : '';
  }
  async save() {
    const isEqual = this.isDS ? this.originalJobContent === JSON.stringify(this.flowCanvas.content) : true;
    if (this.isChange || !isEqual || this.isJar) {
      this.isJar && (await this.flowJarRef.handleFormValidate());
      // fink sql不调用校验接口
      const result = this.isDS ? await this.checkFlow('save') : true;
      if (!result || this.disabled) return;
      const saveInfo = await this.$saveConfirm.show({
        isFlow: true,
        title: this.$t('pa.flow.msg26', [this.baseInfo.jobName])
      });
      this.handleFlowSave(saveInfo, this.flowId);
      return;
    }
    this.$tip.warning(this.$t('pa.flow.msg2'));
  }
  // fink sql自动保存
  async handleAutoSave() {
    // 未更改时不进行自动保存
    if (!this.isChangeForAutoSaving) return;
    this.flowSqlCode.isAutoSaving = true;
    const flowData = this.getNewestFlow(false);
    flowData.content = this.$store.getters.encrypt(flowData.content);
    const { success } = (await updateFlow(flowData, true)) || {};
    this.isChangeForAutoSaving = false;
    this.flowSqlCode.updateSaveTime(success);
  }
  /* 处理更新时间 */
  handleContent(content: any) {
    const find = (arr, key, value) => arr.find((el: any) => el[key] === value);
    if (Array.isArray(content.nodes)) {
      content.nodes.forEach((o: any, index: number) => {
        const item = find(this.$store.getters.componentList, 'operateType', o.operateType);
        if (item && item.paJobComponentList) {
          const target = find(item.paJobComponentList, 'className', o.className);
          content.nodes[index].updateTime = target && target.updateTime ? target.updateTime : null;
        }
      });
    }
    return JSON.stringify(content);
  }

  /* 血缘关系 */
  blood() {
    if (!this.flowId) return;
    this.$router.push({
      path: '/blood-relation',
      query: { resType: 'JOB', serviceId: this.flowId }
    });
  }

  async validateFlowByBack() {
    const params = this.getNewestFlow(false);
    if (isFlinkSql(params.jobType)) {
      params.content = this.$store.getters.encrypt(params.content);
    }
    const { success, data, msg } = await validateComponent(params);
    if (!success) {
      // 节点错误信息
      if (data) {
        const updateInfos = Object.keys(data).map((key) => ({
          id: key,
          status: 2,
          msg: data[key]
        }));
        this.flowCanvas?.updateNodeStatus(updateInfos);
      } else {
        this.$tip.error({ message: msg, duration: 5000 });
      }
      this.loading = false;
    }
    return success;
  }
  /* header end */

  getNewestFlow(isString = true) {
    const content = this.getCanvasContent() || this.jobContent;
    return {
      ...cloneDeep(this.staticData),
      content: isFlinkSql(this.flowType) ? content : isString ? JSON.stringify(content) : cloneDeep(content),
      ...(this.isJar && {
        content: JSON.stringify({
          entryClass: this.flowJarRef.jarData.entryClass,
          ...(this.flowJarRef.jarData.flinkJarFile && { flinkJarFile: this.flowJarRef.jarData.flinkJarFile }),
          ...(this.flowJarRef.jarData.flinkJarFileUpdateTime && {
            flinkJarFileUpdateTime: this.flowJarRef.jarData.flinkJarFileUpdateTime
          })
        })
      })
    };
  }

  async handleFlowSave(saveInfo: object | null, flowId: string, needRefresh = true) {
    const loading = this.$loading({ lock: true, text: this.$t('pa.flow.msg3') });
    try {
      const flowData = { ...this.getNewestFlow(false), ...saveInfo };
      if (!this.isJar)
        flowData.content = isFlinkSql(this.flowType)
          ? this.$store.getters.encrypt(this.flowSqlCode.getContent())
          : this.handleContent(flowData.content);
      delete flowData.showOverlay;
      delete flowData.properties;
      const params = new FormData();
      params.append(
        'delta',
        new Blob([JSON.stringify(flowData)], {
          type: 'application/json'
        })
      );
      const { success, msg, error } = await updateFlow(this.isJar ? this.getSaveJarParams(flowData) : params);
      if (success) {
        this.$tip.success(msg);
        this.isChange = false;
        needRefresh && this.getCurFlow({ id: flowId });
        return loading.close();
      }
      this.$tip.error(error);
      loading.close();
    } catch (err) {
      loading.close();
    }
  }

  // 保存jar类型流程所需参数
  getSaveJarParams(flowData) {
    const params = new FormData();
    const jarData = cloneDeep(this.flowJarRef.jarData);
    delete jarData.flinkJarFile;
    delete jarData.entryClass;
    jarData.jobType = jarData.originalJobType;
    params.append(
      'delta',
      new Blob([JSON.stringify(Object.assign(flowData, jarData))], {
        type: 'application/json'
      })
    );
    params.append('jobJarFile', this.flowJarRef.flinkJarFile ? this.flowJarRef.flinkJarFile : '');
    return params;
  }

  async handleFlowClick(data: any) {
    if (this.isChange) {
      const result = this.isDS ? await this.checkFlow('save') : true;
      if (!result || this.disabled) return;
      let saveInfo = null;
      try {
        saveInfo = await this.$saveConfirm.show({
          isFlow: true,
          title: this.$t('pa.flow.msg26', [this.baseInfo.jobName])
        });
      } catch {
        this.isChange = false;
        const title = data.nodeName;
        if (
          (this as any).$tabNav
            .getAllTabs()
            .find((item) => item.title === title && item.value.split('flowId=')[1] === data.nodeId)
        ) {
          const value = (this as any).$tabNav
            .getAllTabs()
            .find((item) => item.title === title && item.value.split('flowId=')[1] === data.nodeId).value;
          localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId: data.nodeId }));
          this.$router.push({
            path: value
          });
        } else {
          this.$router.replace({
            name: 'refresh',
            query: {
              ...this.$route.query,
              title,
              state: data.state,
              flowId: data.nodeId
            }
          });
        }
        return;
      }
      this.isJar && (await this.flowJarRef.handleFormValidate());
      this.handleFlowSave(saveInfo, data.nodeId, false);
    }
    // 更改路由的fullPath
    const title = data.nodeName;
    if (
      (this as any).$tabNav
        .getAllTabs()
        .find((item) => item.title === title && item.value.split('flowId=')[1] === data.nodeId)
    ) {
      const value = (this as any).$tabNav
        .getAllTabs()
        .find((item) => item.title === title && item.value.split('flowId=')[1] === data.nodeId).value;
      localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId: data.nodeId }));
      this.$router.push({
        path: value
      });
    } else {
      this.$router.replace({
        name: 'refresh',
        query: {
          ...this.$route.query,
          title,
          state: data.state,
          flowId: data.nodeId
        }
      });
    }
  }

  updateFlow() {
    this.isRender = false;
    this.flowId && this.getCurFlow({ id: this.flowId });
  }
  async beforeRouteEnter(to, from, next) {
    if (!to.query.flowId) return next();
    to.query.state = to.query.state || 'ALL';
    if (!to.query.id) {
      const { data } = await getFlowById({ id: to.query.flowId, isMonitor: false });
      to.query.id = data.projectId;
      to.query.name = data.projectName;
      to.meta.title = data.projectName;
      return next((vm: any) => {
        if (isFlinkSql(data.jobType)) {
          data.content = vm.$store.getters.decrypt(data.content || '');
        }
        vm.initFlow(data);
      });
    }
    next();
  }
  async initFlow(data: any) {
    if (!data) return;
    this.needGetFlowData = false;
    this.onlineSuccess = true;
    this.flowStore = new FlowStore(data);
    this.flowStatus = this.flowStore.baseInfo.jobStatus;
    this.flowType = this.flowStore.baseInfo.jobType;
    this.baseInfo = this.flowStore.baseInfo;
    this.jobContent = this.flowStore.content;
    this.staticData = this.flowStore.staticData;
    this.$forceUpdate();
    await this.$nextTick();
    this.renderCanvas();
    this.needGetFlowData = true;
  }

  // 获取当前流程信息
  async getCurFlow({ id }) {
    if (!id || !this.needGetFlowData) return; // 处理id为空的情况：项目下无流程
    this.showLoading();
    const { data } = await getFlowById({ id: id, isMonitor: false });
    if (data.id !== id) return;
    if (isFlinkSql(data.jobType)) {
      data.content = this.$store.getters.decrypt(data.content || '');
    }
    this.originalJobContent = data.content;
    this.initFlow(data);
    this.handleLoadingAfterGet(data);
  }
  // 处理获取当前流程信息后得loading加载
  handleLoadingAfterGet({ jobStatus, id }) {
    const isInStatus = [JobStatus.INPROD, JobStatus.INPUB, JobStatus.INOFF, JobStatus.UNPUB].includes(jobStatus);
    if (isInStatus) {
      this.rightContentLoadingText = this.$t('pa.flow.msg27', [JobStatusMap[jobStatus]]);
    } else if (compileFlowStore.iscompiling(id)) {
      this.rightContentLoadingText = this.$t('pa.flow.msg30');
    } else {
      this.closeLoading();
    }
  }
  handleCanvasReady(editor) {
    console.log('handleCanvasReady');
    this.editor = editor;
    this.renderCanvas();
  }
  // 画布渲染
  renderCanvas() {
    if (!this.isRender && this.flowCanvas) {
      this.flowCanvas.renderCanvas();
      (this as any).verifyDdStatus();
      this['handleTabsNavClose']();
      this.isRender = true;
    }
  }
  handleDataSetClick(data) {
    this.flowSqlCode.insertCode(data);
  }
  // 代码片段库新建/编辑成功时的回调函数
  handleCodeDrawerUpdate() {
    this.flowSqlCode?.getAllSqlCode();
  }
  // 更新流程名字
  handleFlowUpdate({ nodeName, prefix, suffix, originalJobName, projectId, jobStatus }) {
    if (projectId !== this.staticData.projectId) {
      this.isChange = true;
    }
    Object.assign(this.staticData, {
      prefix,
      suffix,
      originalJobName,
      projectId,
      jobStatus
    });
    Object.assign(this.baseInfo, {
      jobName: nodeName,
      jobStatus
    });
    this.flowStatus = jobStatus;
  }
  // 拖拉面板结束后记录位置
  splitDragEnd(e) {
    sessionStorage.setItem(this.projectId as string, String(e.clientX - this.$el.getBoundingClientRect().x));
    this.isDS && this.flowCanvas.renderCanvas();
  }
  // 切换目录和流程
  handleToggleEmpty(bol) {
    if (this.fromMonitorFlow) return;
    this.showDirEmpty = bol;
    if (!bol) {
      // 切换成流程 清除存储的错误
      (this as any).removeError(this.flowId);
    }
  }
}
</script>
<style lang="scss" scoped src="./index.scss"></style>
