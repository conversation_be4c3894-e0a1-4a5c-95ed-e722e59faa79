export default {};

// export interface FormConfig:FormConfigItem[]
export interface FormConfig {
  /* 通用配置 */
  label: string;
  name: string;
  type?: string;
  placeholder?: string;
  tooltip?: string;
  disabled?: boolean;
  hidden?: boolean;
  /* select */
  options?: any[];
  /* number */
  min?: number;
  max?: number;
}
export interface ConditionItem {
  funcArgs: any[];
  funcName: string;
  funcId?: string;
  funcType: string;
  name: string;
}
export interface Condition {
  logicType: string;
  expr: string;
  resultExpression: string;
  conditions: ConditionItem[];
}
export interface FormData {
  delayTime: number;
  keyByModel: string;
  paTimeCharacteristic: string;
  timeModel: string;
  orderlessTime: number;
  conditionA: Condition;
  conditionB: Condition;
}
