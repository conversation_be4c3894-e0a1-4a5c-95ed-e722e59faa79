<template>
  <flow-drawer
    :size="isEn ? 960 : 820"
    :title="title"
    :show.sync="display"
    :before-close="close"
    :is-full-screen="isFullScreen"
    @submit="handleSubmit"
  >
    <config-form v-if="display" ref="configRef" :data="formData" />
  </flow-drawer>
</template>

<script lang="ts">
import { Component, PropSync, Prop, Ref, Vue } from 'vue-property-decorator';
import { getFlowById, updateResourceConfig } from '@/apis/flowNewApi';
import { FLOW_DEFAULT_CONFIG } from '@/components/resource-config/config';

@Component({
  components: {
    FlowDrawer: () => import('../components/flow-drawer.vue'),
    ConfigForm: () => import('@/components/resource-config/components/index.vue')
  }
})
export default class ResourceConfig extends Vue {
  @PropSync('show', { default: false }) display!: boolean;
  @Prop({ required: true, default: '' }) flowId!: string;
  @Prop({ required: true, default: '' }) projectId!: string;
  @Prop({ default: false }) isFullScreen!: boolean;
  @Ref('configRef') readonly configRef!: any;

  private loading = false;
  private formData: any = FLOW_DEFAULT_CONFIG();

  get title() {
    return this.$t('pa.flow.title1', [this.formData.jobName]);
  }

  created() {
    this.getFlowDetail();
  }

  async getFlowDetail() {
    this.loading = true;
    const { success, data, msg } = await getFlowById({ id: this.flowId });
    this.loading = false;
    if (success) {
      this.formData = {
        ...this.formData,
        ...JSON.parse(data?.properties || '{}'),
        jobName: data.jobName,
        jobType: data.jobType,
        orgId: data.orgId,
        memo: data.memo,
        jobStatus: data.jobStatus,
        prefix: data.prefix,
        originalJobName: data.originalJobName,
        suffix: data.suffix,
        projectId: data.projectId
      };
      return;
    }
    this.$tip.error(msg);
  }

  async handleSubmit() {
    const data = await this.configRef.getFormData();
    this.loading = true;
    data.pajob = new Blob([JSON.stringify(Object.assign(data.pajob, { id: this.flowId, projectId: this.projectId }))], {
      type: 'application/json'
    });
    const { success, msg, error } = await updateResourceConfig(data);
    this.loading = false;
    this.close();
    if (success) {
      this.$tip.success(msg);
      this.$emit('update', { id: this.flowId });
      return;
    }
    this.$tip.error(error);
  }

  close() {
    this.display = false;
  }
}
</script>
<style lang="scss" scoped>
.resource {
  &__container {
    padding: 20px 8px 100px 0;
  }
}
</style>
