import i18n from '@/i18n';
import { JobStatusMap } from '.';
export type PreTaskResult =
  | void
  | null
  | undefined
  | {
      [k: string]: any;
      success?: boolean;
      msg?: string;
    };
export type Task<T> = (data?: T | undefined, result?: PreTaskResult) => any | void | Promise<PreTaskResult | any | void>;
export interface PublishParams {
  flowIds: string[];
  flows?: { id: string; jobType: string }[];
  isBatch: boolean;
}
export interface CancelPublishParams {
  flowIds: string[];
  isBatch: boolean;
}
export interface OnlineParams {
  flowIds: { id: string; fromLastCheckpoint: boolean }[];
  state: boolean;
}
export interface OfflineParams {
  flowIds: { jobId: string; savepoint: boolean }[];
  state: 'stop' | 'retain' | 'force';
}
export const LOADING_TEXT_MAP = {
  complier: i18n.t('pa.flow.complier'),
  cancelPublish: i18n.t('pa.flow.canclePublish'),
  publish: JobStatusMap.INPUB,
  online: JobStatusMap.INPROD,
  offline: JobStatusMap.INOFF
} as const;
export type OperatorType = keyof typeof LOADING_TEXT_MAP;
export type ErrorType = 'error' | 'warning' | 'errorPro';
export type ErrorInfo = {
  message: string;
  duration?: number;
};
export type ErrorProInfo = {
  msgType: string;
  msg: string;
  data: any;
};
