<template>
  <div class="bs-detail-block service">
    <div class="bs-detail__header">
      <div class="bs-detail__header-title">{{ $t('pa.home.regist') }}</div>
    </div>
    <data-handler :request="request" @get-data="getData">
      <template v-slot:content>
        <div class="bs-detail__content service__content">
          <pro-grid :gutter="21">
            <pro-grid v-for="{ count, title } in serviceRegistrationsList" :key="title" :col-span="2">
              <div class="service__content--item" @click="handleListItemClick(title)">
                <span class="title">{{ title }}</span>
                <div class="number">{{ count }}</div>
              </div>
            </pro-grid>
          </pro-grid>
          <bs-empty v-if="serviceRegistrationsList.length === 0" />
        </div>
      </template>
    </data-handler>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch, Inject } from 'vue-property-decorator';
import { getServiceData } from '@/apis/homeApi';
import DataHandler from './data-handler.vue';
import { hasPermission } from '@/utils';
import { getTypeList } from '@/apis/serviceApi';
import '../index.scss';
@Component({
  components: {
    DataHandler
  }
})
export default class Service extends Vue {
  @Prop({ default: '' }) orgId!: string;
  @Inject('enableSql') enableSql;
  // 服务注册列表
  private serviceRegistrationsList: object[] = [];
  private request: PromiseConstructor[] = [];
  private serviceTypeList: any = null;
  @Watch('orgId', { immediate: true })
  handleOrgIdChange(val) {
    this.request = [getServiceData(val)];
  }

  created() {
    this.getTypeList();
  }

  // 获取服务类型键值对
  async getTypeList() {
    const { data, success, error } = await getTypeList();
    if (!success) return this.$message.error(error);
    this.serviceTypeList = Object.fromEntries(data.map((el) => [el.label, el.type]));
  }

  // 获取服务注册列表
  async getData({ data }) {
    if (data && Array.isArray(data)) {
      this.serviceRegistrationsList = [];
      data.forEach((el: any) => {
        if (el.listConf && hasPermission(el.listConf.menuAuthCode)) {
          this.serviceRegistrationsList.push({
            title: el.resName,
            count: el.registrationCount
          });
        }
      });
      // 服务注册量无SQL模块能力时过滤daemon、dts
      !this.enableSql &&
        (this.serviceRegistrationsList = this.serviceRegistrationsList.filter(
          (el: any) => !['daemon', 'dts'].includes(el.title)
        ));
    }
  }

  handleListItemClick(title) {
    if (!this.serviceTypeList[title]) return this.$message.error(this.$t('pa.home.noPage'));
    this.$router.push({
      name: 'elementService',
      params: {
        type: this.serviceTypeList[title]
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.service {
  height: 100% !important;
  &__content {
    width: 100%;
    height: 100%;
    min-height: 300px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 21px;
    ::v-deep &--item {
      min-width: 150px;
      height: 72px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      background: #f9f9f9;
      border-radius: 6px;
      border-left: 3px solid #377cff;
      cursor: pointer;
      .title {
        font-weight: 400;
        font-size: 1rem;
        color: #777777;
        line-height: 20px;
        margin-left: 21px;
      }
      .number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #444444;
        line-height: 28px;
        margin-right: 32px;
      }
    }
  }
}
</style>
