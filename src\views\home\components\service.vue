<template>
  <div class="bs-detail-block service">
    <div class="bs-detail__header">
      <div class="bs-detail__header-title">服务注册量</div>
    </div>
    <data-handler :request="request" @get-data="getData">
      <template v-slot:content>
        <div class="bs-detail__content service__content">
          <pro-grid :gutter="21">
            <pro-grid
              v-for="{ count, title } in serviceRegistrationsList"
              :key="title"
              :col-span="2"
            >
              <div class="service__content--item" @click="handleListItemClick(title)">
                <span class="title">{{ title }}</span>
                <div class="number">{{ count }}</div>
              </div>
            </pro-grid>
          </pro-grid>
          <bs-empty v-if="serviceRegistrationsList.length === 0" />
        </div>
      </template>
    </data-handler>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch, Inject } from 'vue-property-decorator';
import { getServiceData } from '@/apis/homeApi';
import DataHandler from './data-handler.vue';
import { hasPermission } from '@/utils';
@Component({
  components: {
    DataHandler
  }
})
export default class Service extends Vue {
  @Prop({ default: '' }) orgId!: string;
  @Inject('enableSql') enableSql;
  // 服务注册列表
  private serviceRegistrationsList: object[] = [];
  private request: PromiseConstructor[] = [];
  @Watch('orgId')
  handleOrgIdChange(val) {
    this.request = [getServiceData(val)];
  }
  // 获取服务注册列表
  async getData({ data }) {
    if (data && Array.isArray(data)) {
      this.serviceRegistrationsList = [];
      data.forEach((el: any) => {
        if (el.listConf && hasPermission(el.listConf.menuAuthCode)) {
          this.serviceRegistrationsList.push({
            title: el.resName,
            count: el.registrationCount
          });
        }
      });
      // 服务注册量无SQL模块能力时过滤daemon、dts
      !this.enableSql &&
        (this.serviceRegistrationsList = this.serviceRegistrationsList.filter(
          (el: any) => !['daemon', 'dts'].includes(el.title)
        ));
    }
  }
  handleListItemClick(title) {
    const routeMap = {
      主机: 'host',
      aerospike: 'aerospike',
      zookeeper: 'zookeeper',
      kafka: 'kafka',
      计算引擎: 'streamcube',
      加工引擎: 'flink',
      redis: 'redis',
      rocketMQ: 'rocketmq',
      hbase: 'hbase',
      hdfs: 'hdfs',
      dts: 'dts',
      外部http接口: 'http',
      数据库: 'jdbc',
      hive: 'hive',
      ftp: 'ftp',
      socket: 'socket',
      elasticsearch: 'elasticsearch',
      daemon: 'daemon',
      pulsar: 'pulsar',
      rabbitMq: 'RABBITMQ'
    };
    if (routeMap[title]) {
      this.$router.push(
        `/element/clusters/${routeMap[title]}?title=${title}&type=${routeMap[title].toUpperCase()}`
      );
      return;
    }
    this.$message.error('无指定的服务列表页面');
  }
}
</script>

<style lang="scss" scoped>
.service {
  height: 100% !important;
  &__content {
    width: 100%;
    height: 100%;
    min-height: 300px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 21px;
    ::v-deep &--item {
      min-width: 150px;
      height: 72px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      background: #f9f9f9;
      border-radius: 6px;
      border-left: 3px solid #377cff;
      cursor: pointer;
      .title {
        font-weight: 400;
        font-size: 1rem;
        color: #777777;
        line-height: 20px;
        margin-left: 21px;
      }
      .number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #444444;
        line-height: 28px;
        margin-right: 32px;
      }
    }
  }
}
</style>
