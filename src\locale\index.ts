import Vue from 'vue';
import VueI18n from 'vue-i18n';
import zhCnLocale from 'bsview-3.0/dist/locale/zh-CN';
import BSCnLocale from 'bs-ui-pro/lib/locale/lang/zh-CN';
import local from 'bs-ui-pro/lib/locale';
Vue.use(VueI18n);

const i18n = new VueI18n({
  locale: 'zh-CN',
  messages: {
    'zh-CN': Object.assign(zhCnLocale, BSCnLocale)
  },
  silentTranslationWarn: true
});
local.i18n((key, value) => i18n.t(key, value));
export default i18n;
