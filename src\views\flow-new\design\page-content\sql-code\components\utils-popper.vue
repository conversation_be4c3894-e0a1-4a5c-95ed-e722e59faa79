<template>
  <el-popover
    v-model="innerVisible"
    placement="bottom-start"
    :width="isEn ? 820 : 640"
    trigger="manual"
    :visible-arrow="false"
    popper-class="flow-code__utils-popper"
  >
    <div class="utils" :class="{ 'utils-en': isEn }">
      <div class="utils-header">{{ $t('pa.flow.util') }}</div>
      <div class="utils-content">
        <div class="utils-left" :class="{ 'utils-left__en': isEn }">
          <div class="utils-item">
            <div class="left-img"></div>
            <div class="left-text">
              <p>
                {{ $t('pa.flow.genSql') }}
                <el-tooltip effect="light" placement="bottom-start">
                  <div slot="content" style="width: 520px">
                    <p>{{ $t('pa.flow.msg284') }}</p>
                    <p>{{ $t('pa.flow.table') }}user：id,name,sex,number,area_id</p>
                    <p>{{ $t('pa.flow.table') }}area：id,province,city,district</p>
                    <p>{{ $t('pa.flow.msg285') }}</p>
                    <p>&nbsp;&nbsp;u.`id` as `id`,</p>
                    <p>&nbsp;&nbsp;u.`name` as `name`,</p>
                    <p>&nbsp;&nbsp;u.`sex` as `sex`,</p>
                    <p>&nbsp;&nbsp;u.`number` as `number`,</p>
                    <p>&nbsp;&nbsp;u.`area_id` as `area_id`,</p>
                    <p>&nbsp;&nbsp;a.`id` as `id`,</p>
                    <p>&nbsp;&nbsp;a.`province` as `province`,</p>
                    <p>&nbsp;&nbsp;a.`city` as `city`,</p>
                    <p>&nbsp;&nbsp;a.`district` as `district`</p>
                    <p>{{ $t('pa.flow.msg286') }}</p>
                  </div>
                  <i class="iconfont icon-weizhi" style="cursor: pointer"></i>
                </el-tooltip>
              </p>
              <p>{{ $t('pa.flow.msg287') }}</p>
            </div>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
        <!-- 表字段生成sql -->
        <slot name="gen-field"></slot>
      </div>
    </div>
    <div slot="reference" class="reference-box" :style="rStyles"></div>
  </el-popover>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
@Component
export default class UtilsPopper extends Vue {
  @Prop({ default: () => [0, 0] }) position!: number[];
  @Prop() visible!: boolean;
  innerVisible = false;
  get rStyles() {
    const [x, y] = this.position;
    return {
      left: (x || 0) + 'px',
      top: (y || 0) + 'px'
    };
  }
  @Watch('visible')
  handleVisibleChange(val) {
    this.innerVisible = val;
  }
  close() {
    this.innerVisible = false;
    this.$emit('close');
  }
  mounted() {
    this.innerVisible = true;
    document.addEventListener('click', this.close);
  }
  beforeDestroy() {
    document.removeEventListener('click', this.close);
  }
}
</script>
<style lang="scss" scoped>
.utils {
  width: 640px;
  border: 1px solid rgba(229, 229, 229, 1);
  box-shadow: 0px 2px 20px 0px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  background: #ffffff;
  &-en {
    width: 820px;
  }
  * {
    box-sizing: border-box;
  }
  &-header {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    padding: 0 16px;
    line-height: 40px;
    border-bottom: 1px solid rgba(241, 241, 241, 1);
    font-family: PingFangSC-Medium;
    font-weight: 500;
  }
  &-content {
    display: flex;
    box-sizing: border-box;
  }
  &-left {
    box-sizing: border-box;
    width: 300px;
    padding: 10px;
    border-right: 1px solid rgba(241, 241, 241, 1);
    cursor: pointer;
    &__en {
      width: 405px;
    }
  }
  &-item {
    display: flex;
    align-items: center;
    width: 100%;
    height: 60px;
    padding: 0 7px;
    background: $--bs-color-background-base;
    border-radius: 4px;
    & .left-img {
      flex-shrink: 0;
      width: 36px;
      height: 36px;
      background: #ffffff;
      border: 1px solid rgba(241, 241, 241, 1);
      border-radius: 2px;
      margin-right: 10px;
      background-image: url('../../../../../../assets/createfield.png');
      background-repeat: no-repeat;
      background-size: 24px;
      background-position: center;
    }
    & .left-text {
      flex: 1;
      font-size: 12px;
      & p:first-child {
        font-family: PingFangSC-Semibold;
        font-weight: 600;
      }
      & p:last-child {
        color: $--bs-color-text-secondary;
      }
      .icon-weizhi {
        font-size: 14px;
        font-weight: normal;
        color: rgba(0, 0, 0, 0.25);
      }
    }
    & .el-icon-arrow-right {
      color: #aaa;
    }
  }
}
.reference-box {
  position: absolute;
  width: 8px;
  height: 18px;
  background: transparent;
  top: 100px;
}
</style>
<style>
.el-popper.flow-code__utils-popper {
  padding: 0;
}
</style>
