<template>
  <info-block :title="$t('pa.baseInfo')" :list="renderList" />
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { dayjs, safeArray } from '@/utils';
import { REDIS_TYPES } from '../utils';

@Component({
  components: { InfoBlock: () => import('./components/info-block.vue') }
})
export default class BaseInfo extends Vue {
  @Prop({ default: () => ({}) }) config!: any;
  @Prop({ default: () => ({}) }) data!: any;

  get count() {
    return Math.ceil(100 / this.config.width);
  }
  get renderList() {
    const base = safeArray(this.config?.rowData).reduce((pre, it, index) => {
      let curIndex = Math.ceil((index + 1) / this.count) - 1;
      if (curIndex < 0) curIndex = 0;
      if (!pre[curIndex]) pre[curIndex] = [];
      pre[curIndex].push(this.getItem(it));
      return pre;
    }, []);
    const others = safeArray(this.config?.textAreaData).map((it) => [this.getItem(it)]);
    return [...base, ...others];
  }

  getItem({ label, method, key, subKey }: any) {
    let value = this.data[key] || '';
    if (method) value = this[method](value);
    if (subKey) value = value[subKey] || '';
    if (key === 'resProperty' && this.$route.query.resType === 'REDIS') {
      value = REDIS_TYPES[value];
    }
    return { label: label.replace(':', ''), value: String(value) };
  }
  timeFormatter(data: string) {
    return !data ? '' : dayjs(data).format('YYYY-MM-DD HH:mm:ss');
  }
  parsePro(text: string) {
    try {
      return JSON.parse(text);
    } catch {
      return {};
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-descriptions {
  margin: 0 20px;
  &-item {
    padding-bottom: 0 !important;
    height: 47px;
  }
}
::v-deep .base-info {
  &__label {
    display: inline-block;
    width: fit-content;
    font-size: 14px;
  }
  &__content {
    display: inline-block;
    width: 100%;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
