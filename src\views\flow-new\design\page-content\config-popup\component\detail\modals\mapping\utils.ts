export const VALID_TYPE = ['String', 'Boolean', 'Date', 'Double', 'Long', 'Float', 'Integer'];
export const vaildArray = (target: any) => Array.isArray(target) && target.length > 0;

export const getOptions = (arr: any[] = [], modifier = '') => {
  return arr
    .filter(({ name }) => name)
    .map((el) => {
      return {
        ...el,
        disabled: false,
        label: `${modifier}${el.name}${modifier}`,
        value: `${modifier}${el.name}${modifier}`
      };
    });
};

export const includesPro = (a, b) => {
  return String(a).toLowerCase().includes(String(b).toLowerCase().trim());
};

export const getSuggestions = (queryString: string, source: any[] = []) => {
  return !queryString ? source : source.filter(({ value }) => includesPro(value, queryString));
};

export const handleArray = (target: any) => (Array.isArray(target) ? target : []);

export const uuid = () => {
  const url = URL.createObjectURL(new Blob([]));
  URL.revokeObjectURL(url);
  return url.substring(url.lastIndexOf('/') + 1);
};
