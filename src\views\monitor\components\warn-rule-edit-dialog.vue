<template>
  <bs-dialog :title="$t('pa.action.edit')" :visible.sync="display" size="medium">
    <pro-form
      ref="proForm"
      :value="formData"
      :options="options"
      :request="getRecordInfo"
      :form-items="formItems"
      @change="handleChange"
    >
      <span slot="stateType">{{ stateTypeMap[formData.stateType] }}</span>
      <span slot="sendEnable">
        <el-switch v-model="formData.sendEnable" active-value="Y" inactive-value="N" />
        <el-tooltip placement="top" effect="light">
          <div slot="content">{{ $t('pa.monitor.text1') }}<br />{{ $t('pa.monitor.text2') }}</div>
          <i class="bs-pro-form-item__icon bs-icon-wenti"></i>
        </el-tooltip>
      </span>
      <span slot="noticeMsg">
        <el-input v-model="formData.noticeMsg" type="textarea" autosize :placeholder="$t('pa.placeholder.search')" />
        <el-tooltip placement="top" effect="light">
          <div slot="content">
            {{ $t('pa.monitor.text3') }}
            <ul class="warn-rule-ul">
              <li>{{ $t('pa.monitor.text4') }}</li>
              <li>{{ $t('pa.monitor.text5') }}</li>
              <li>{{ $t('pa.monitor.text6') }}</li>
              <li>{{ $t('pa.monitor.text7') }}</li>
            </ul>
            {{ $t('pa.monitor.text8') }}
          </div>
          <i class="bs-pro-form-item__icon bs-icon-wenti"></i>
        </el-tooltip>
      </span>
      <bs-select slot="noticeUser" v-model="formData.noticeUser" :options="userList" virtual-loading clearable multiple />
    </pro-form>
    <!-- footer -->
    <div slot="footer">
      <!-- 关闭 -->
      <el-button @click="display = false">{{ $t('pa.action.close') }}</el-button>
      <!-- 保存 -->
      <el-button type="primary" :loading="loading" @click="handleSubmit(false)">{{ $t('pa.action.save') }}</el-button>
      <!-- 保存并重启 -->
      <el-button v-access="'PA.MONITOR.WARN.ON'" type="primary" :loading="loading" @click="handleSubmit(true)">
        {{ $t('pa.monitor.warningRule.edit.saveAndOnline') }}
      </el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, PropSync, Prop, Vue } from 'vue-property-decorator';
import { getWarnRule, getNoticeTemplate, getUserList, updateWarnRule, updateWarnRuleState } from '@/apis/warnRuleApi';
import { safeArray } from '@/utils';
@Component
export default class WarnRuleEditDialog extends Vue {
  @PropSync('show', { required: true, default: false }) display!: boolean;
  @Prop() id!: string;
  @Prop() orgId!: string;

  formData: any = {
    id: '',
    title: '',
    ruleType: '',
    stateType: '',
    cron: '',
    effectiveTime: [],
    sendEnable: '',
    noticeMsg: '',
    noticeUser: [],
    silent: 1,
    silentUnit: 1000
  };
  options: any = { labelWidth: this.isEn ? 180 : 120 };
  stateTypeMap: any = { ON: this.$t('pa.action.enable'), OFF: this.$t('pa.action.close') };
  formItems: any = [
    {
      type: 'input',
      prop: 'title',
      label: this.$t('pa.warnRule'),
      componentProps: {
        maxlength: 30,
        placeholder: this.$t('pa.monitor.text9'),
        disabled: true,
        showWordLimit: true
      }
    },
    {
      type: 'text',
      prop: 'ruleType',
      label: this.$t('pa.monitor.warningRule.edit.ruleType')
    },
    {
      type: 'custom',
      prop: 'stateType',
      label: this.$t('pa.monitor.text13')
    },
    {
      type: 'input',
      prop: 'cron',
      label: this.$t('pa.monitor.warningRule.edit.cron'),
      tooltip: this.$t('pa.monitor.text10'),
      componentProps: {
        maxlength: 30,
        placeholder: this.$t('pa.monitor.warningRule.edit.cronPlaceholder'),
        showWordLimit: true
      },
      rules: [{ required: true, message: this.$t('pa.monitor.warningRule.edit.cronPlaceholder'), trigger: 'blur' }]
    },
    {
      type: 'time',
      prop: 'effectiveTime',
      tooltip: this.$t('pa.monitor.text11'),
      componentProps: {
        isRange: true,
        startPlaceholder: this.$t('pa.monitor.warningRule.edit.startTime'),
        endPlaceholder: this.$t('pa.monitor.warningRule.edit.endTime'),
        valueFormat: 'HH:mm:ss'
      },
      label: this.$t('pa.monitor.warningRule.edit.effectiveTime'),
      rules: [{ required: true, message: this.$t('pa.monitor.text14'), trigger: 'blur' }]
    },
    {
      type: 'custom',
      prop: 'sendEnable',
      label: this.$t('pa.monitor.warningRule.edit.sendEnable')
    },
    {
      type: 'custom',
      prop: 'noticeMsg',
      label: this.$t('pa.monitor.text12'),
      deps: ['sendEnable'],
      visible: ({ sendEnable }) => sendEnable === 'Y',
      rules: [{ required: true, message: this.$t('pa.monitor.text15'), trigger: 'blur' }]
    },
    {
      type: 'custom',
      prop: 'noticeUser',
      label: this.$t('pa.monitor.warningRule.edit.noticeUser'),
      deps: ['sendEnable'],
      visible: ({ sendEnable }) => sendEnable === 'Y',
      componentProps: {
        clearable: true,
        multiple: true
      },
      request: this.getUserList,
      rules: [{ required: true, message: this.$t('pa.monitor.warningRule.edit.noticeUserPlaceholder'), trigger: 'blur' }]
    },
    {
      type: 'input',
      label: this.$t('pa.monitor.warningRule.edit.silent'),
      gutter: 10,
      deps: ['sendEnable'],
      visible: ({ sendEnable }) => sendEnable === 'Y',
      required: true,
      children: [
        {
          type: 'input-number',
          prop: 'silent',
          colSpan: 12,
          componentProps: {
            stepStrictly: true
          },
          rules: [{ required: true, message: this.$t('pa.monitor.warningRule.edit.silentPlaceholder'), trigger: 'blur' }]
        },
        {
          type: 'select',
          prop: 'silentUnit',
          colSpan: 12,
          componentProps: {
            options: [
              { value: 1000, label: this.$t('pa.unit.second') },
              { value: 60000, label: this.$t('pa.unit.minute') },
              { value: 3600000, label: this.$t('pa.unit.hours') },
              { value: 86400000, label: this.$t('pa.unit.day') }
            ]
          },
          rules: [{ required: true, message: this.$t('pa.monitor.warningRule.edit.silentPlaceholder'), trigger: 'blur' }]
        }
      ]
    }
  ];

  loading = false;
  userList: any[] = [];
  async created() {
    await this.getRecordInfo();
    await this.getNoticeTemplate();
    await this.getUserList();
  }
  async getRecordInfo() {
    try {
      this.loading = true;
      const { success, data, error } = await getWarnRule(this.id);
      if (!success) return this.$message.error(error);
      return { ...data, effectiveTime: [data.effectiveStartTime, data.effectiveEndTime] };
    } finally {
      this.loading = false;
    }
  }
  async getNoticeTemplate() {
    const { success, data, error } = await getNoticeTemplate();
    if (!success) return this.$message.error(error);
    if (!this.formData.noticeMsg) this.formData.noticeMsg = data;
  }

  async getUserList() {
    const { success, data, error } = await getUserList(this.orgId);
    if (!success) return this.$message.error(error);
    this.userList = safeArray(data).map((it) => ({ label: `${it.username}(${it.realname})`, value: it.username }));
  }

  handleChange(data: any) {
    this.formData = data;
  }
  async handleSubmit(isRestart = false) {
    try {
      await (this.$refs.proForm as any).validate();
      const { success, msg, error } = await updateWarnRule({
        ...this.formData,
        effectiveStartTime: this.formData.effectiveTime[0],
        effectiveEndTime: this.formData.effectiveTime[1]
      });
      if (!success) return this.$message.error(error);
      if (isRestart) {
        const { success, msg, error } = await updateWarnRuleState([{ id: this.formData.id, stateType: 'ON' }]);
        if (!success) return this.$message.error(error);
        this.$message.success(msg);
      } else {
        this.$message.success(msg);
      }
      this.display = false;
      this.$emit('close');
    } catch {
      return this.$message.error(this.$t('pa.tip.checkMessage'));
    }
  }
}
</script>

<style lang="scss" scoped>
.warn-rule-ul {
  padding-left: 10px;
  li:before {
    content: '';
    width: 5px;
    height: 5px;
    display: inline-block;
    border-radius: 50%;
    vertical-align: middle;
    margin-right: 14px;
    background: $--bs-color-text-primary;
  }
}
::v-deep .el-date-editor .el-range-separator {
  width: 28px;
}
</style>
