import { JobComponent } from '../interface';

class ComponentStore {
  componentMap: Map<string, JobComponent>;
  constructor(componentList: any[]) {
    this.componentMap = new Map();
    componentList.forEach((com: Base) => {
      this.componentMap.set(com.className as string, this.componentAdapter(com));
    });
  }
  componentAdapter(com: Base): JobComponent {
    console.log(com);
    return { className: '', componentName: '' };
  }
  getComponentConfig(className) {
    this.componentMap.get(className);
  }
}
export default ComponentStore;
