<template>
  <el-collapse v-model="activeList">
    <el-collapse-item name="info">
      <div slot="title" class="resource-title">流程信息</div>
      <template v-for="el in renderList">
        <el-form-item :key="el.name" :prop="el.name" :label="el.label" :rules="rules[el.name]">
          <div :class="['resource-item', el.tip ? 'resource-item--tip' : '']">
            <!-- input -->
            <el-input
              v-model="form[el.name]"
              autocomplete="off"
              :type="el.type"
              :maxlength="el.maxlength"
              :disabled="el.disabled"
              show-word-limit
              clearable
              :placeholder="el.placeholder"
            />
          </div>
        </el-form-item>
      </template>
    </el-collapse-item>
  </el-collapse>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';

@Component
export default class FlowInfo extends Vue {
  @Prop({ type: Object, default: () => ({}) }) data!: any;
  @PropSync('data', { type: Object, default: () => ({}) }) form!: any;

  private activeList = ['info'];
  private renderList: any[] = [
    {
      name: 'jobName',
      label: '名称',
      type: 'text',
      maxlength: 200,
      placeholder: '请输入流程名称，长度不超过200字符'
    },
    {
      name: 'jobType',
      label: '类型',
      type: 'text',
      placeholder: '请选择流程类型',
      disabled: true
    },
    {
      name: 'memo',
      label: '备注',
      type: 'textarea',
      maxlength: 255,
      placeholder: '请输入流程备注'
    }
  ]; // 渲染列表
  private rules: any = {
    jobName: [{ required: true, message: '请输入流程名称', trigger: 'blur' }]
  };
}
</script>
