<template>
  <table-block
    :title="$t('pa.udfInfo')"
    :height="height"
    :loading="loading"
    :page-data="pageData"
    :table-data="tableData"
    :column-data="columnData"
    @page-change="pageChange"
  >
    <el-button slot="operation" type="primary" @click="getListData">{{ $t('pa.action.refresh') }}</el-button>
  </table-block>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { detailScUdf } from '@/apis/serviceApi';
@Component({
  components: { TableBlock: () => import('../components/table-block.vue') }
})
export default class UdfTable extends Vue {
  @Prop({ default: () => ({}) }) params!: any;

  loading = false;
  pageData: any = {
    pageSize: this.$store.getters.pageSize || 25,
    currentPage: 1,
    total: 0
  };
  columnData: any[] = [];
  tableData: any[] = [];

  get height() {
    return this.params?.height || 'auto';
  }

  created() {
    this.getListData();
  }
  async getListData() {
    this.loading = true;
    const { data, success, msg } = await detailScUdf(this.$route.query.id);
    if (success) {
      if (!data.tableData) data.tableData = [];
      data.columnData.forEach((el) => (el.value = el.prop));
      this.columnData = data.columnData;
      this.tableData = data.tableData;
      this.pageData.total = data.tableData.length;
    } else {
      this.$message.error(msg);
    }
    this.loading = false;
  }

  // 监听分页变换
  pageChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
  }
}
</script>
