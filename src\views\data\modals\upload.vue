<template>
  <div class="dialog">
    <bs-dialog
      :title="title"
      :visible.sync="visible"
      width="490px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="closeDialog"
    >
      <div>
        <div class="tips">
          <i class="iconfont icon-tishi" style="font-size: 12px; color: inherit"></i>
          {{ $t('pa.data.udf.detail.tips.importTip') }}
        </div>
        <div class="conter">
          <div class="first_title">
            {{ $t('pa.data.udf.detail.tips.downloadSteps1') }}
            <el-tooltip effect="light" placement="bottom" :content="templateTooltip">
              <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer"></i>
            </el-tooltip>
          </div>
          <div>
            <el-button class="btn" icon="iconfont icon-xiazai" @click="download">
              {{ $t('pa.data.udf.detail.downloadFile') }}
            </el-button>
          </div>
          <div class="first_title">
            {{ `第二步：上传${uploadTitle}文件` }}
            <el-tooltip effect="light" placement="bottom" :content="uploadJarContent">
              <i class="iconfont icon-wenhao" style="margin-left: 10px; cursor: pointer"></i>
            </el-tooltip>
          </div>
          <el-form
            ref="ruleForm"
            v-loading="formLoading"
            :model="formData"
            :rules="rules"
            class="demo-ruleForm"
            :element-loading-text="$t('pa.tip.loading')"
          >
            <el-form-item prop="pkg">
              <el-tooltip effect="light" :content="fileName" :disabled="isDisabled" placement="bottom-start">
                <el-upload
                  ref="upload"
                  action
                  :http-request="handleFile"
                  :multiple="false"
                  :limit="1"
                  :on-exceed="handleExceed"
                  :before-upload="beforeUpload"
                  :file-list="formData.fileList"
                  :on-remove="handleRemove"
                >
                  <el-button class="btn" icon="iconfont icon-shangchuan">
                    {{ $t('pa.data.udf.detail.selectFile') }}
                  </el-button>
                </el-upload>
              </el-tooltip>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog(false)">{{ $t('pa.action.close') }}</el-button>
        <el-button type="primary" :loading="loading" @click="submit('ruleForm')">{{ $t('pa.flow.confirm') }}</el-button>
      </div>
    </bs-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Emit } from 'vue-property-decorator';
import { download, get, post } from '@/apis/utils/net';
import { URL_TABLE_IMPORT, URL_TABLE_DOWNLOAD, URL_UDF_TEMPLATE } from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
import i18n from '@/i18n';
@Component
export default class AddEdit extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: i18n.t('pa.home.service') }) type!: string;
  @Prop({ default: i18n.t('pa.action.add') }) title!: string;
  @Prop({ default: '' }) resType!: string;
  @Prop({ default: false }) formLoading!: boolean;
  @Prop({ default: '' }) downloadType!: string;

  formData: any = {};
  loading = false;
  rules: any = {
    pkg: [{ required: true, validator: this.validateFile, trigger: 'blur' }]
  };
  fileName = '';

  get isUdfUploadDialog() {
    return this.type === 'UDF' ? true : false;
  }

  get isDisabled() {
    return this.fileName === '' || this.fileName.length < 50;
  }

  get templateTooltip() {
    return this.$t('pa.data.table.detail.tips.templateTooltip', [this.type, this.type]);
  }

  get uploadJarContent() {
    return this.type === 'UDF' ? this.$t('pa.data.text8') : this.$t('pa.data.table.detail.tips.type2');
  }

  get uploadTitle() {
    return this.type === 'UDF' ? this.$t('pa.data.table.detail.zipPackage') : this.$t('pa.data.table.detail.template');
  }

  validateFile(rule: any, value: any, callback: any) {
    if (this.formData.fileList === undefined || this.formData.fileList.length === 0) {
      callback(new Error(this.$t('pa.tip.addFile')));
    } else {
      callback();
    }
  }

  @Emit('close')
  closeDialog(needFresh: boolean, data: false) {
    (this.$refs.upload as any).clearFiles();
    this.formData = {};

    this.loading = false;
    this.formLoading = false;
    return data;
  }

  beforeUpload(file) {
    const name = file.name;
    const fileType = this.type === 'UDF' ? 'zip' : 'EXCEL';
    const isJar = this.type === 'UDF' ? name.indexOf('zip') >= 0 : name.indexOf('xlsx') >= 0 || name.indexOf('xls') >= 0;
    const isLt60M = file.size / 1024 / 1024 < 10;
    if (!isJar) {
      this.$message.error(this.$t('pa.tip.typeJudge', [fileType]));
      return isJar;
    }
    if (!isLt60M) {
      this.$message.error(this.$t('pa.data.text4'));
      return isLt60M;
    }
    return isJar && isLt60M;
  }

  /**
   * 表单提交
   */
  submit(formName: string) {
    if (this.formData.fileList === undefined || this.formData.fileList.length === 0) {
      this.$message.warning(this.$t('pa.tip.selectFile'));
      return;
    }
    if (this.isUdfUploadDialog) {
      this.closeDialog(true, this.formData.fileList[0]);
      return;
    }
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        this.$confirm(this.$t('pa.data.udf.detail.tips.importCoverTip'), this.$t('pa.prompt'), {
          confirmButtonText: this.$t('pa.flow.confirm'),
          cancelButtonText: this.$t('pa.action.cancel'),
          type: 'warning'
        }).then(() => {
          const config = {
            timeout: 1500000,
            headers: { ContentType: 'multipart/form-data' }
          };
          const submitData = new FormData();
          if (this.formData.fileList !== undefined) {
            submitData.append('file', this.formData.fileList[0]);
          }
          this.loading = true;
          const api = `${URL_TABLE_IMPORT}/${this.resType}`;
          post(api, submitData, config).then((resp: any) => {
            // this.$message.success(`${resp.resp}`);
            this.parseResp(resp);
          });
        });
      } else {
        this.$message.error(this.$t('pa.tip.checkMessage'));
        return false;
      }
    });
  }
  handleRemove() {
    this.fileName = '';
  }

  /**
   * 解析返回结果
   */
  parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.closeDialog(true, resp.data);
    });
  }
  /**
   * 处理文件上传
   */
  handleFile(file: any) {
    this.formData.fileList = [];
    this.formData.fileList.push(file.file);
    this.fileName = this.formData.fileList[0].name;
  }
  /**
   * 判断上传文件个数
   */
  handleExceed(files: any) {
    this.$message.warning(this.$t('pa.tip.fileCount', [files.length]));
  }
  // 下载表模板
  download() {
    if (this.isUdfUploadDialog) {
      if (this.downloadType === '') {
        this.$message.warning(this.$t('pa.data.udf.placeholder.udfPlaceholder'));
        return;
      }
      this.downloadUDFTemplate();
      return;
    }
    download(URL_TABLE_DOWNLOAD + '/' + this.resType);
  }

  async downloadUDFTemplate() {
    const res = await get(`${URL_UDF_TEMPLATE}/${this.downloadType}`, null, {
      responseType: 'blob'
    });
    if (res.blob) {
      const { blob, fileName } = res;
      const url = window.URL.createObjectURL(new Blob([blob]));
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      return;
    }
  }
}
</script>

<style scoped>
.dialog ::v-deep .el-dialog__body {
  padding: 12px 20px;
  text-align: left;
}
.dialog ::v-deep .el-upload {
  display: block;
}
.dialog ::v-deep .iconfont {
  color: inherit;
}
.btn {
  width: 100%;
}
.tips {
  font-size: 12px;
  background: #fff5ea;
  color: #ff9e2b;
  border-radius: 16px;
  line-height: 36px;
  padding: 0 20px;
}
.conter {
  padding: 0 20px;
}
.first_title {
  margin: 8px 0;
  line-height: 30px;
  font-size: 14px;
  color: black;
  font-weight: bold;
  display: flex;
  align-items: center;
}
</style>
