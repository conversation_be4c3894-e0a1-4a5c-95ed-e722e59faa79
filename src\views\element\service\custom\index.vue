<template>
  <div class="bs-page bs-layout-flex-column">
    <div v-if="isSinglePage" class="bs-page__header marB20">
      <div class="bs-page__header-title">{{ title }}</div>
      <div class="bs-page__header-operation">
        <bs-search
          v-model="searchObj.search"
          placeholder="请输入名称或服务地址"
          style="width: 210px; margin-right: 10px"
          @input="fetchList"
        />
        <el-button v-if="hasReg && !monitor" type="primary" class="default-btn" @click="reg">
          注册
        </el-button>
        <!-- 去掉新建功能 -->
        <el-button v-if="hasAdd" type="primary" class="default-btn" @click="add"> 新建 </el-button>
      </div>
    </div>
    <div class="content">
      <bs-table
        v-loading="tableLoading"
        :height="isSinglePage ? 'calc(100vh - 310px)' : 'calc(100vh - 355px)'"
        :data="tableData.tableData"
        :column-data="tableData.columnData"
        :page-data="tableData.pageData"
        @page-change="handleCurrentChange"
        @handleSortChange="handleSortChange"
        @cell-click="handleCellClick"
        @row-dblclick="handleRowDblclick"
        @refresh="getListData"
      >
        <div slot="status" slot-scope="{ row }">
          <el-tag
            size="mini"
            :type="tag.enumColor[`${row.status}`]"
            style="cursor: pointer"
            @click="statusClick(row)"
          >
            {{ tag.enumData[`${row.status}`] }}
          </el-tag>
        </div>
        <template slot="belongType" slot-scope="{ row }">
          <span>{{ belongEnumData[row.belongType] }}</span>
        </template>
        <div slot="title" slot-scope="{ row }" class="title">
          <span class="data-title" :title="row.title">
            {{ row.title }}
          </span>
          <el-tag v-if="row.shareFlag" size="mini">分享</el-tag>
        </div>
        <template slot="operator" slot-scope="{ row }">
          <el-tooltip content="编辑" effect="light">
            <i
              v-if="hasAuthority(hasFeatureAuthority(getEditAuthCode()), row) && !monitor"
              class="iconfont icon-bianji"
              @click="edit(row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="删除" effect="light">
            <i
              v-if="hasAuthority(hasFeatureAuthority(getDeleteAuthCode()), row) && !monitor"
              class="iconfont icon-shanchu"
              @click="delRow(row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="详情" effect="light">
            <i
              v-if="hasFeatureAuthorityWithParent(getMenuAuthCode())"
              class="iconfont icon-chakan"
              @click="detail(row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="分享" effect="light">
            <i
              v-if="hasAuthority(hasFeatureAuthority(getShareAuthCode()), row) && !monitor"
              class="iconfont icon-fenxiang"
              @click="deptRole(row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="终端" effect="light">
            <i
              v-if="hasAuthority(hasFeatureAuthority(getTermAuthCode()), row) && !monitor"
              class="iconfont icon-zhongduan"
              @click="term(row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="上传" effect="light">
            <i
              v-if="hasAuthority(hasFeatureAuthority(getScpAuthCode()), row) && !monitor"
              class="iconfont icon-shangchuan"
              @click="scp(row)"
            ></i>
          </el-tooltip>
        </template>
      </bs-table>
    </div>
    <term ref="Term" />
    <scp ref="Scp" />
    <dept-role
      v-if="showDeptRoleDialog"
      ref="DeptRole"
      :visible.sync="showDeptRoleDialog"
      :data="deptData"
    />
    <addEdit
      v-if="showAddEditDialog"
      :visible="showAddEditDialog"
      :form-data="addEditFormData"
      :form-config="addEditFormConfig"
      :operation="addEditOperation"
      @close="closeDialog"
    />
    <contact-info ref="ContactInfo" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
import {
  URL_HOST_LIST,
  URL_HOST_DELETE,
  URL_HOST_FIND,
  URL_RES_LIST,
  URL_RES_DELETE,
  URL_RES_FINDBYID,
  URL_RESCONF_GETRESTYPELIST,
  URL_RESCONF_GETFORMCONF,
  URL_RES_TESTCONNECT
} from '@/apis/commonApi';
import term from '@/components/term.vue';
import scp from '@/components/scp.vue';
import deptRole from '@/components/dept-role-1.vue';
import addEdit from './modals/add-edit.vue';
import contactInfo from '@/components/contact-info.vue';
import moment from 'moment';
interface Service extends Base {
  listConf?: Base;
}

import { cloneDeep } from 'lodash';
@Component({
  name: 'ServiceCustom',
  components: {
    term,
    scp,
    deptRole,
    addEdit,
    contactInfo
  }
})
export default class ServiceCustom extends PaBase {
  @Prop() currentService!: Service;
  @Prop({ default: false, type: Boolean }) monitor!: boolean;
  pageLoading = false;
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: {
      updateTime: 'DESC'
    }
  };
  tableData: ITableData = {
    columnData: [],
    tableData: []
  };
  fetchList: any = _.debounce(this.getListData, 500);
  // 分享弹窗相关
  showDeptRoleDialog = false;
  deptData: any = {};
  addFile: any;
  regFile: any;
  clusterType: any;
  title = '';
  type = '';
  tag: any = {
    enumColor: {},
    enumData: {}
  };
  belongEnumData = {};

  // 是否显示服务注册新建弹窗
  showAddEditDialog = false;
  addEditOperation: 'ADD' | 'EDIT' = 'ADD';
  addEditFormData = {}; // 表单数据
  addEditFormConfig = {}; // 表单配置
  // 是作为单独的页面组件 还是 作为部分嵌入服务列表页面中
  get isSinglePage() {
    return this.$route.path.includes('clusters');
  }

  get hasReg() {
    const resType = this.type;
    return (
      this.getResLabel(resType, 'hasReg') &&
      this.hasFeatureAuthority(this.getResLabel(resType, 'listConf')['regAuthCode'])
    );
  }
  get hasAdd() {
    const resType = this.type;
    return (
      resType === 'HOST' &&
      this.getResLabel(resType, 'hasAdd') &&
      this.hasFeatureAuthority(this.getResLabel(resType, 'listConf')['addAuthCode'])
    );
  }
  handleRowDblclick(row) {
    this.detail(row);
  }
  handleCellClick(row, cell) {
    const ContactInfo: any = this.$refs.ContactInfo;
    if (cell.property === 'createdBy') {
      ContactInfo.open([row.createdBy]);
    }
    if (cell.property === 'updatedBy') {
      ContactInfo.open([row.updatedBy]);
    }
  }
  isHost() {
    return (this.$route.meta!.resType || this.type) === 'HOST';
  }
  hasAuthority(hasRole: any, row: any) {
    return !_.isEmpty(_.toString(row.dataLevelType)) && row.dataLevelType !== 'PARENT' && hasRole;
  }

  async getRecordById(id: string) {
    if (!id) {
      return;
    }
    const params = { id: id };
    return await Vue.axios.get(this.makeDetailUrl(), { params: params });
  }
  showEdit(row: any) {
    this.pageLoading = true;
    this.doGet(URL_RESCONF_GETFORMCONF, {
      params: {
        file: this.getAddFile()
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.getRecordById(row.id).then((result: any) => {
          if (result.success) {
            this.showAddEditDialog = true;
            this.addEditOperation = 'EDIT';
            this.addEditFormData = {
              ...(result.data || {}),
              resType: this.$route.query.type || this.currentService.type,
              belongType: 'SELF'
            };
            this.addEditFormConfig = JSON.parse(resp.data);
          } else {
            this.$message.warning(result.msg);
          }
        });
      });
      this.pageLoading = false;
    });
  }
  showReg(row: any) {
    this.pageLoading = true;
    this.doGet(URL_RESCONF_GETFORMCONF, {
      params: {
        file: this.getRegFile()
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.getRecordById(row.id).then((result: any) => {
          if (result.success) {
            this.showAddEditDialog = true;
            this.addEditOperation = 'EDIT';
            this.addEditFormData = {
              ...result.data,
              resType: this.$route.query.type || this.currentService.type,
              belongType: 'REG'
            };
            this.addEditFormConfig = JSON.parse(resp.data);
          } else {
            this.$message.warning(result.msg);
          }
        });
      });
      this.pageLoading = false;
    });
  }
  edit(row) {
    if (row.belongType !== undefined) {
      row.belongType === 'SELF' && this.showEdit(row);
      row.belongType === 'REG' && this.showReg(row);
    } else {
      this.showEdit(row);
    }
  }
  detail(row) {
    if (row.resProperty) {
      this.clusterType = JSON.parse(row.resProperty).clusterType;
      if (this.clusterType === undefined) {
        this.clusterType = '';
      }
    } else {
      this.clusterType = '';
    }
    const path =
      this.$route.path.includes('clusters') && !this.monitor
        ? this.$route.path
        : '/element/clusters' + `/${row.resType}`;
    this.$router.push({
      path:
        path +
        '/detail?id=' +
        row.id +
        '&resType=' +
        row.resType +
        '&clusterType=' +
        this.clusterType +
        '&title=' +
        row.title
    });
  }
  deptRole(row) {
    this.deptData = row;
    this.showDeptRoleDialog = true;
  }
  term(row) {
    const termEl: any = this.$refs.Term;
    termEl.open(row.id, row.ip);
    termEl.visible = true;
  }
  scp(row) {
    const scpEl: any = this.$refs.Scp;
    scpEl.recordId = row.id;
    scpEl.visible = true;
  }
  delRow(row: any) {
    this.$confirm(`您确定要删除选中的${row.title}吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        this.tableLoading = true;
        const ids: any[] = [];
        ids.push(row.id);
        this.doDelete(this.makeDeleteUrl(), { data: ids }).then((resp: any) => {
          this.parseResponse(resp, () => {
            this.getListData();
          });
          this.tableLoading = false;
        });
      })
      .catch(() => {
        return true;
      });
  }
  handleCurrentChange(currentPage, pageSize) {
    this.searchObj.pageData.currentPage = currentPage;
    this.searchObj.pageData.pageSize = pageSize;
    this.fetchList();
  }

  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.fetchList();
  }
  closeDialog(needFresh: boolean) {
    this.showAddEditDialog = false;
    if (needFresh) {
      this.getListData();
    }
  }
  reg() {
    this.doGet(URL_RESCONF_GETFORMCONF, {
      params: {
        file: this.getRegFile()
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.showAddEditDialog = true;
        this.addEditOperation = 'ADD';
        this.addEditFormData = {
          resType: this.$route.query.type || this.currentService.type,
          belongType: 'REG'
        };
        this.addEditFormConfig = JSON.parse(resp.data);
      });
    });
  }
  add() {
    this.doGet(URL_RESCONF_GETFORMCONF, {
      params: {
        file: this.getAddFile()
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.addEditOperation = 'ADD';
        this.addEditFormData = {
          resType: this.$route.query.type || this.currentService.type,
          belongType: 'SELF'
        };
        this.showAddEditDialog = true;
        this.addEditFormConfig = JSON.parse(resp.data);
      });
    });
  }
  getListData() {
    this.tableLoading = true;
    const searchObj = cloneDeep(this.searchObj);
    searchObj.search = searchObj.search.trim();
    this.doPost(this.makeQueryUrl(), searchObj).then((resp: any) => {
      this.parseResponse(resp, () => {
        const { columnData, tableData, pageData } = resp.data;
        if ((this.$route.meta!.resType || this.type) === 'FLINK') {
          const target = columnData.find((item) => item.prop === 'authorized');
          if (target) {
            target.dataType = 'Enum';
            target.enumColor = { false: 'red', true: 'green' };
            target.enumData = { false: '否', true: '是' };
          }
          tableData.forEach((item) => {
            item.authorized = item.resProperty && JSON.parse(item.resProperty).authorized;
          });
        }
        columnData.forEach((el) => {
          if (el.prop === 'belongType') this.belongEnumData = el.enumData;
          if (el.prop === 'checkResult') el.width = '50';
          if (el.prop === 'title') el.width = '150';
          if (el.prop === 'status') {
            this.tag = el;
          }
          // el.width = el.label.length * 50;
          el.value = el.prop;
        });
        const operationWidth = this.monitor ? 100 : 200;
        columnData.push({
          label: '操作',
          value: 'operator',
          width: operationWidth,
          fixed: 'right'
        });
        tableData.forEach((el) => {
          el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
        });
        tableData.forEach((item) => {
          item.zkRes = item.resProperty && JSON.parse(item.resProperty).zkRes;
        });
        this.tableData = {
          columnData,
          tableData,
          pageData
        };
      });
      this.tableLoading = false;
    });
  }
  makeQueryUrl() {
    const typeInfo = this.$route.query.type || this.type;
    if (this.isHost()) {
      return URL_HOST_LIST;
    } else {
      return URL_RES_LIST + '/' + typeInfo;
    }
  }
  makeDeleteUrl() {
    if (this.isHost()) {
      return URL_HOST_DELETE;
    } else {
      return URL_RES_DELETE;
    }
  }
  makeDetailUrl() {
    if (this.isHost()) {
      return URL_HOST_FIND;
    } else {
      return URL_RES_FINDBYID;
    }
  }
  init() {
    this.getListData();
    Vue.axios.get(URL_RESCONF_GETRESTYPELIST).then((resp: any) => {
      if (resp.success) {
        this.resEnums = resp.data;
      }
    });
  }
  wsCallback(res: any) {
    if (res.id) {
      const rec = _.find(this.tableData.tableData, { id: res.id });
      if (rec) {
        this.$set(rec, 'checkResult', res.info);
        this.$set(rec, 'status', res.status);
      }
    }
  }
  getListConf() {
    return this.getResLabel(this.$route.meta!.resType || this.type, 'listConf');
  }
  getEditAuthCode() {
    const name = 'editAuthCode';
    return this.getListConf()[name];
  }
  getDeleteAuthCode() {
    const name = 'deleteAuthCode';
    return this.getListConf()[name];
  }
  getMenuAuthCode() {
    const name = 'menuAuthCode';
    return this.getListConf()[name];
  }
  getShareAuthCode() {
    const name = 'shareAuthCode';
    return this.getListConf()[name];
  }
  getTermAuthCode() {
    const name = 'termAuthCode';
    return this.getListConf()[name];
  }
  getScpAuthCode() {
    const name = 'scpAuthCode';
    return this.getListConf()[name];
  }
  getAddFile() {
    const name = 'file';
    this.addFile = this.getResLabel(this.type, 'addConf')[name];
    if ((this.$route.query.clusterType as string) === 'YARN_PER_JOB') {
      this.addFile = this.addFile.replace(/detail.json/, 'detail-per-job.json');
    } else if ((this.$route.query.clusterType as string) === 'YARN_SESSION') {
      this.addFile = this.addFile.replace(/detail.json/, 'detail-yarn-session.json');
    } else if ((this.$route.query.clusterType as string) === 'YARN_APPLICATION') {
      this.addFile = this.addFile.replace(/detail.json/, 'detail-yarn-application.json');
    }
    return this.addFile;
  }
  getRegFile() {
    const name = 'file';
    this.regFile = this.getResLabel(this.type, 'regConf')[name];
    if ((this.$route.query.clusterType as string) === 'YARN_PER_JOB') {
      this.regFile = this.regFile.replace(/detail.json/, 'detail-per-job.json');
    } else if ((this.$route.query.clusterType as string) === 'YARN_SESSION') {
      this.addFile = this.addFile.replace(/detail.json/, 'detail-yarn-session.json');
    } else if ((this.$route.query.clusterType as string) === 'YARN_APPLICATION') {
      this.addFile = this.addFile.replace(/detail.json/, 'detail-yarn-application.json');
    }
    return this.regFile;
  }
  statusClick(row) {
    this.$set(row, 'status', 2);
    this.doGet(URL_RES_TESTCONNECT, {
      params: {
        id: row.id
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        const all = resp.data.fail + resp.data.success;
        if (resp.data.success === 0) {
          this.$set(row, 'status', 0);
          this.$set(row, 'checkResult', '0/' + all);
        } else {
          this.$set(row, 'status', 1);
          this.$set(row, 'checkResult', resp.data.success + '/' + all);
        }
      });
    });
  }
  created() {
    // 获取服务集群的类型和名称
    this.title = (this.$route.query.title as string) || '';
    this.type = (this.currentService ? this.currentService.type : this.$route.query.type) as string;
    this.init();
  }
}
</script>

<style lang="scss" scoped>
.content {
  background: #fff;
  height: calc(100vh - 180px);
  overflow-x: auto;
  padding: 0;
  .title {
    display: flex;
    .data-title {
      display: inline-block;
      margin-right: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
.iconfont {
  cursor: pointer;
}
.iconfont + .iconfont {
  margin-left: 10px;
}
</style>
