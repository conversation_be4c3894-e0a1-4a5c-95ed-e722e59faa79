import { decode } from '@/store/utils';

export const SMALL_SIZE_LIST = Object.freeze([
  'AEROS<PERSON>KE',
  'ELASTICSEARCH',
  'FLINK',
  'HBASE',
  'HDFS',
  'HOST',
  'HTTP',
  'J<PERSON><PERSON>',
  'KAF<PERSON>',
  'REDIS',
  'STREAMCUBE'
]);
export const getIconByType = (icon: string, type: string, width: number) => {
  const size = SMALL_SIZE_LIST.includes(type) ? 40 : 1024;
  return decode(icon).replace('<svg', `<svg width="${width}px" height="${width}px" viewBox="0 0 ${size} ${size}"`);
};

export const EN_COLUMN_WIDTH_MAP = {
  checkResult: 170,
  url: 150,
  orgName: 200,
  updateTime: 200
};
