// 前端所需的数据模型

enum ContentType {
  'CANVAS' = 'CANVAS',
  'WARNING' = 'WARNING',
  'HISTORY' = 'HISTORY',
  'VERSION' = 'VERSION',
  'LOG' = 'LOG',
  'CODE' = 'CODE'
}

// 发布状态枚举
enum JobStatus {
  'DEV' = '开发',
  'INPUB' = '发布中',
  'PUB' = '发布',
  'INPROD' = '启动中',
  'PROD' = '运行'
}

// 流程基本信息
interface JobBaseInfo {
  id: string;
  jobName: string;
  jobStatus: JobStatus;
  memo: string;
  createdBy: string;
  createTime: string;
  orgName: string;
  urls: string[];
}

// 资源配置信息
interface JobResourceInfo {
  custom: boolean;
}

// 流程画布信息
interface JobContent {
  nodeNum: number;
  edges: Edge[];
  nodes: Node[];
}
interface Node {
  className: string;
}
interface Edge {
  endNode: string;
}

// 组件列表
interface JobComponent {
  className: string;
  componentName: string;
}
enum JobType {
  'PROCESSFLOW' = 'PROCESSFLOW',
  'FLINK_SQL' = 'FLINK_SQL'
}

export interface StaticData {
  cloudJob?: boolean;
  clusterId?: string;
  comments?: string;
  createTime?: number;
  createdBy?: string;
  dataLevelType?: string;
  id?: string;
  jobName?: string;
  jobRunTimeStatus?: string;
  jobStatus?: string;
  jobTag?: string;
  jobType?: string;
  jobVersion?: number;
  memo?: string;
  orgId?: string;
  orgName?: string;
  orgPath?: string;
  projectId?: string;
  projectName?: string;
  properties?: string;
  updateTime?: string;
  updatedBy?: string;
}
export interface CheckFlowType {
  save: string;
  complier: string;
  publish: string;
  test: string;
  code: string;
}
export type CheckType = keyof CheckFlowType;
export { ContentType, JobBaseInfo, JobResourceInfo, JobContent, JobComponent, JobStatus, JobType };
