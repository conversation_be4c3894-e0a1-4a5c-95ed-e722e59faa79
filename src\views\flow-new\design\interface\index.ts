// 前端所需的数据模型

import i18n from '@/i18n';
enum ContentType {
  'CANVAS' = 'CANVAS',
  'SQL_CODE' = 'SQL_CODE',
  'SESSION' = 'SESSION',
  'WARNING' = 'WARNING',
  'HISTORY' = 'HISTORY',
  'SNIPPET' = 'SNIPPET',
  'VERSION' = 'VERSION',
  'LOG' = 'LOG',
  'CODE' = 'CODE'
}

// 发布状态枚举
enum JobStatus {
  'DEV' = 'DEV',
  'INPUB' = 'INPUB',
  'PUB' = 'PUB',
  'INPROD' = 'INPROD',
  'PROD' = 'PROD',
  'INOFF' = 'INOFF',
  'UNPUB' = 'UNPUB'
}
const JobStatusMap = {
  DEV: i18n.t('pa.flow.dev'),
  INPUB: i18n.t('pa.flow.publishing'),
  PUB: i18n.t('pa.flow.pub'),
  INPROD: i18n.t('pa.flow.onlining'),
  PROD: i18n.t('pa.flow.prod'),
  INOFF: i18n.t('pa.flow.stopping'),
  UNPUB: i18n.t('pa.flow.canclePublish')
} as const;

const JobErrorMap = {
  PROD: i18n.t('pa.flow.msg35'),
  PUB: i18n.t('pa.flow.msg36'),
  DEV: i18n.t('pa.flow.msg37')
} as const;
// 流程基本信息
interface JobBaseInfo {
  id: string;
  jobName: string;
  jobStatus: keyof typeof JobStatus;
  memo: string;
  createdBy: string;
  createTime: string;
  orgName: string;
  urls: string[];
  projectId: string;
}

// 资源配置信息
interface JobResourceInfo {
  custom: boolean;
}

// 流程画布信息
interface JobContent {
  nodeNum: number;
  edges: Edge[];
  nodes: Node[];
}
interface Node {
  className: string;
}
interface Edge {
  endNode: string;
}

// 组件列表
interface JobComponent {
  className: string;
  componentName: string;
}
enum JobType {
  'PROCESSFLOW' = 'PROCESSFLOW',
  'FLINK_SQL' = 'FLINK_SQL',
  'UDJ' = 'UDJ'
}

// 报错信息类型
enum MsgType {
  SIMPLE = 'SIMPLE',
  DETAIL = 'DETAIL',
  LINE_MESSAGE = 'LINE_MESSAGE',
  NODE_MESSAGE = 'NODE_MESSAGE'
}

export interface StaticData {
  cloudJob?: boolean;
  clusterId?: string;
  comments?: string;
  createTime?: number;
  createdBy?: string;
  dataLevelType?: string;
  id?: string;
  jobName?: string;
  jobRunTimeStatus?: string;
  jobStatus?: string;
  jobTag?: string;
  jobType?: string;
  jobVersion?: number;
  memo?: string;
  orgId?: string;
  orgName?: string;
  orgPath?: string;
  projectId?: string;
  projectName?: string;
  properties?: string;
  updateTime?: string;
  updatedBy?: string;
}
export interface CheckFlowType {
  save: string;
  complier: string;
  publish: string;
  test: string;
  code: string;
}
export type CheckType = keyof CheckFlowType;

interface IColumn {
  label: string;
  value: string;
}
export {
  ContentType,
  JobBaseInfo,
  JobResourceInfo,
  JobContent,
  JobComponent,
  JobStatus,
  JobStatusMap,
  JobType,
  MsgType,
  IColumn,
  JobErrorMap
};
