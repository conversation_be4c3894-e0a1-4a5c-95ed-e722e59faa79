<template>
  <div v-if="!fromMonitorFlow" class="info__container">
    <!-- 信息 -->
    <el-popover trigger="hover" placement="bottom-start">
      <!-- 信息窗 -->
      <el-form label-position="left" :label-width="isEn ? '155px' : '85px'" class="info-form">
        <template v-for="el in formConfig">
          <div v-if="el.name === 'urls'" :key="el.name">
            <el-form-item v-for="url in data.urls" :key="url" :label="el.label">
              <span v-if="url === '-'">-</span>
              <a v-else :href="`${url}`" target="_blank">{{ url }}</a>
            </el-form-item>
          </div>
          <el-form-item v-else :key="el.name" :label="el.label">
            {{ data[el.name] }}
          </el-form-item>
        </template>
      </el-form>
      <!-- 显示内容 -->
      <div slot="reference" class="info-title">{{ data.jobName }}</div>
    </el-popover>
    <!-- 状态 -->
    <span v-if="jobStatusText" :class="`info-tag info-tag--${data.jobStatus}`">
      <i
        v-if="data.jobStatus === 'INPUB' || data.jobStatus === 'INPROD' || !onlineSuccess"
        class="icon-run iconfont icon-shangxianzhong"
      ></i>
      {{ !onlineSuccess ? '上线中' : jobStatusText }}
    </span>
    <!--流程运行状态-->
    <bs-tag
      v-if="jobRunStatusText && this.data.jobStatus !== 'DEV'"
      :color="jobRunStatusColor"
      :border="false"
      style="margin-left: 10px"
    >
      {{ jobRunStatusText }}
    </bs-tag>
    <el-tooltip v-if="showTimeIcon" class="info-count-down" effect="light" placement="top" :content="onlineInfo">
      <i class="iconfont icon-daojishi"></i>
    </el-tooltip>
    <el-tooltip
      v-if="!onlineSuccess"
      :content="$t('pa.flowOnlineException')"
      effect="light"
      :open-delay="500"
      placement="bottom"
    >
      <i class="info-icon-memo iconfont icon-wenhao"></i>
    </el-tooltip>
  </div>
  <div v-else class="info__container is-monitor">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item>{{ $t('pa.flow.monitorMgr') }}</el-breadcrumb-item>
      <el-breadcrumb-item to="/monitor/flow">{{ $t('pa.flow.monitor') }}</el-breadcrumb-item>
      <el-breadcrumb-item :title="data.jobName">{{ data.jobName }}</el-breadcrumb-item>
      <span v-if="jobStatusText" :class="`info-tag info-tag--${data.jobStatus}`">
        <i
          v-if="data.jobStatus === 'INPUB' || data.jobStatus === 'INPROD' || !onlineSuccess"
          class="icon-run iconfont icon-shangxianzhong"
        ></i>
        {{ !onlineSuccess ? '上线中' : jobStatusText }}
      </span>
      <!--流程运行状态-->
      <bs-tag
        v-if="jobRunStatusText && this.data.jobStatus !== 'DEV'"
        :color="jobRunStatusColor"
        :border="false"
        style="margin-left: 10px"
      >
        {{ jobRunStatusText }}
      </bs-tag>
    </el-breadcrumb>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import dayjs from 'dayjs';
import { JobStatusMap } from './interface';
import { runStatusColor } from '@/config';
@Component
export default class HeaderInfo extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: false }) fromMonitorFlow!: boolean;
  @Prop({ default: false }) onlineSuccess!: true;
  get showTimeIcon() {
    return this.data.properties?.includes('batch');
  }
  get onlineInfo() {
    try {
      if (!this.data.properties.includes('batch')) return;
      const config = JSON.parse(this.data.properties);
      const $t = this.$t;
      const handler = {
        once: ({ startTime }) => this.$t('pa.flow.onlineTip', [dayjs(startTime).format(this.$t('pa.flow.format'))]),
        intervalPeriod({ startTime, intervalHour, intervalMinute }) {
          return $t('pa.flow.onlineMode', [dayjs(startTime).format($t('pa.flow.format')), intervalHour, intervalMinute]);
        },
        timePeriod({ startDate, repeatTime }) {
          return $t('pa.flow.onlineMode1', [dayjs(startDate).format($t('pa.flow.format1')), repeatTime]);
        },
        prescribed: ({ cron }) => $t('pa.flow.customCron', [cron])
      };
      return handler[config.jobRunningRule] ? handler[config.jobRunningRule](config) : '';
    } catch (e) {
      return;
    }
  }
  private formConfig: any[] = [
    {
      label: this.$t('pa.flow.name') + '：',
      name: 'jobName'
    },
    {
      label: this.$t('pa.notes') + '：',
      name: 'memo'
    },
    {
      label: this.$t('pa.flow.creater') + '：',
      name: 'createdBy'
    },
    {
      label: this.$t('pa.flow.createTime') + '：',
      name: 'createTime'
    },
    {
      label: this.$t('pa.flow.createOrg') + '：',
      name: 'orgName'
    },
    {
      label: this.$t('pa.flow.address') + '：',
      name: 'urls'
    }
  ];

  get jobStatusText() {
    return JobStatusMap[this.data.jobStatus];
  }
  get jobRunStatusText() {
    return {
      FAILED: this.$t('pa.home.text13'),
      RUNNING: this.$t('pa.home.running'),
      CANCELED: this.$t('pa.home.text14'),
      RESTARTING: this.$t('pa.home.restart'),
      UNKNOWN: this.$t('pa.status.unKnow'),
      NONE: this.$t('pa.status.none'),
      FINISHED: this.$t('pa.home.text12')
    }[this.data.jobRunTimeStatus];
  }
  get jobRunStatusColor() {
    return runStatusColor[this.data.jobRunTimeStatus];
  }
}
</script>
<style lang="scss" scoped>
.info {
  &-container {
    font-size: 15px;
  }
  &__container {
    display: flex;
    align-items: center;
  }
  &__container.is-monitor {
    width: 100%;
    .el-breadcrumb {
      width: 100%;
    }
    .el-breadcrumb .el-breadcrumb__item {
      line-height: 18px;
    }
    .el-breadcrumb .el-breadcrumb__item:nth-child(3) {
      max-width: calc(100% - 290px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      ::v-deep .el-breadcrumb__separator {
        display: none;
      }
    }
  }
  &-tag {
    display: inline-block;
    text-align: center;
    box-sizing: border-box;
    width: max-content;
    min-width: 50px;
    padding: 0 7px;
    font-size: 12px;
    border-radius: 4px;
    white-space: nowrap;
    margin-left: 8px;
    .icon-run {
      font-size: 12px;
      margin-right: 4px;
    }

    &--DEV,
    &--INPUB {
      background-color: #f4f0fb;
      border-color: #e8e1f7;
      color: #8c6bd6;
      .icon-run {
        color: inherit;
      }
    }

    &--PROD {
      background-color: #eefaee;
      border-color: #ddf4de;
      color: #54c958;
    }

    &--PUB,
    &--INPROD {
      background-color: #ebf2ff;
      color: #377cff;
      border: 1px solid #d7e5ff;
      .icon-run {
        color: inherit;
      }
    }
  }

  &-count-down {
    margin: 0 12px;
    color: #ff5353;
    font-size: 12px;
    i {
      color: inherit;
      margin-right: 3px;
    }
  }
  &-icon-memo {
    cursor: pointer;
    margin-left: 10px;
  }
  &-title {
    display: inline-block;
    max-width: 200px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    cursor: pointer;
    vertical-align: middle;
  }

  &-form {
    max-width: 45vw;
    ::v-deep .el-form-item {
      margin-bottom: 0;
      a {
        display: inline-block;
        line-height: initial;
        vertical-align: text-top;
      }
    }
  }
}
</style>
