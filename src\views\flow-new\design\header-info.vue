<template>
  <div v-if="!fromMonitorFlow" class="info__container">
    <!-- 信息 -->
    <el-popover trigger="hover" placement="bottom-start">
      <!-- 信息窗 -->
      <el-form label-position="left" label-width="85px" class="info-form">
        <template v-for="el in formConfig">
          <div v-if="el.name === 'urls'" :key="el.name">
            <el-form-item v-for="url in data.urls" :key="url" :label="el.label">
              <span v-if="url === '-'">-</span>
              <a v-else :href="url" target="_blank">{{ url }}</a>
            </el-form-item>
          </div>
          <el-form-item v-else :key="el.name" :label="el.label">
            {{ data[el.name] }}
          </el-form-item>
        </template>
      </el-form>
      <!-- 显示内容 -->
      <div slot="reference" class="info-title">{{ data.jobName }}</div>
    </el-popover>
    <!-- 状态 -->
    <span v-if="jobStatusText" :class="`info-tag info-tag--${data.jobStatus}`">
      <i
        v-if="data.jobStatus === 'INPUB' || data.jobStatus === 'INPROD' || !onlineSuccess"
        class="icon-run iconfont icon-shangxianzhong"
      ></i>
      {{ !onlineSuccess ? '上线中' : jobStatusText }}
    </span>
    <!--流程运行状态-->
    <span
      v-if="jobRunStatusText && this.data.jobStatus !== 'DEV'"
      :class="`info-tag info-tag--${data.jobRunTimeStatus}`"
    >
      {{ jobRunStatusText }}
    </span>
    <el-tooltip
      v-if="showTimeIcon"
      class="info-count-down"
      effect="light"
      placement="top"
      :content="onlineInfo"
    >
      <i class="iconfont icon-daojishi"></i>
    </el-tooltip>
    <el-tooltip
      v-if="!onlineSuccess"
      content="流程上线异常，您可以在流程监控模块执行强制停止操作"
      effect="light"
      :open-delay="500"
      placement="bottom"
    >
      <i class="info-icon-memo iconfont icon-wenhao"></i>
    </el-tooltip>
  </div>
  <div v-else class="info__container">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item>监控管理</el-breadcrumb-item>
      <el-breadcrumb-item to="/monitor/flow">流程监控</el-breadcrumb-item>
      <el-breadcrumb-item>{{ data.jobName }}--{{ jobStatusText }}</el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import moment from 'moment';
@Component
export default class HeaderInfo extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: false }) fromMonitorFlow!: boolean;
  @Prop({ default: false }) onlineSuccess!: true;
  get showTimeIcon() {
    return this.data.properties?.includes('batch');
  }
  get onlineInfo() {
    try {
      if (!this.data.properties.includes('batch')) return;
      const config = JSON.parse(this.data.properties);
      const handler = {
        once: ({ startTime }) =>
          `一次性上线：${moment(startTime).format('YYYY年MM月DD日 HH:mm')}上线`,
        intervalPeriod({ startTime, intervalHour, intervalMinute }) {
          return `间隔周期模式：${moment(startTime).format(
            'YYYY年MM月DD日 HH:mm'
          )}上线，每隔${intervalHour}小时${intervalMinute}分执行`;
        },
        timePeriod({ startDate, repeatTime }) {
          return `时间周期模式：${moment(startDate).format(
            'YYYY年MM月DD日'
          )}上线，每天${repeatTime}运行`;
        },
        prescribed: ({ cron }) => `自定义：${cron}`
      };
      return handler[config.jobRunningRule] ? handler[config.jobRunningRule](config) : '';
    } catch (e) {
      return;
    }
  }
  private formConfig: any[] = [
    {
      label: '名称：',
      name: 'jobName'
    },
    {
      label: '描述：',
      name: 'memo'
    },
    {
      label: '创建人：',
      name: 'createdBy'
    },
    {
      label: '创建时间：',
      name: 'createTime'
    },
    {
      label: '创建机构：',
      name: 'orgName'
    },
    {
      label: '地址：',
      name: 'urls'
    }
  ];

  get jobStatusText() {
    return {
      DEV: '开发',
      INPUB: '发布中',
      PUB: '已发布',
      INPROD: '上线中',
      PROD: '已上线'
    }[this.data.jobStatus];
  }

  get jobRunStatusText() {
    return {
      FAILED: '失败',
      RUNNING: '运行',
      NONE: '未运行',
      FINISHED: '完成',
      UNKNOWN: '未知',
      OTHER: '其他'
    }[this.data.jobRunTimeStatus];
  }
}
</script>
<style lang="scss" scoped>
.info {
  &-container {
    font-size: 15px;
  }
  &__container {
    display: flex;
    align-items: center;
  }
  &-tag {
    display: inline-block;
    text-align: center;
    box-sizing: border-box;
    width: max-content;
    min-width: 50px;
    padding: 0 7px;
    font-size: 12px;
    border-radius: 4px;
    white-space: nowrap;
    margin-left: 8px;
    .icon-run {
      font-size: 12px;
      margin-right: 4px;
    }

    &--DEV,
    &--INPUB {
      background-color: #f4f0fb;
      border-color: #e8e1f7;
      color: #8c6bd6;
      .icon-run {
        color: inherit;
      }
    }

    &--PROD {
      background-color: #eefaee;
      border-color: #ddf4de;
      color: #54c958;
    }

    &--PUB,
    &--INPROD {
      background-color: #ebf2ff;
      color: #377cff;
      border: 1px solid #d7e5ff;
      .icon-run {
        color: inherit;
      }
    }
    &--FINISHED {
      background: rgba(84, 201, 88, 0.3);
      color: #54c958;
    }
    &--RUNNING {
      background: rgba(55, 124, 255, 0.3);
      color: #377cff;
    }
    &--NONE {
      background: rgba(255, 83, 83, 0.3);
      color: #ff5353;
    }
    &--UNKNOWN,
    &--OTHER,
    &--FAILED {
      background: #f1f1f1;
      color: #908f94;
    }
  }

  &-count-down {
    margin: 0 12px;
    color: #ff5353;
    font-size: 12px;
    i {
      color: inherit;
      margin-right: 3px;
    }
  }
  &-icon-memo {
    cursor: pointer;
    margin-left: 10px;
  }
  &-title {
    display: inline-block;
    max-width: 200px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    cursor: pointer;
    vertical-align: middle;
  }

  &-form {
    max-width: 45vw;
    ::v-deep .el-form-item {
      margin-bottom: 0;
      a {
        display: inline-block;
        line-height: initial;
        vertical-align: text-top;
      }
    }
  }
}
</style>
