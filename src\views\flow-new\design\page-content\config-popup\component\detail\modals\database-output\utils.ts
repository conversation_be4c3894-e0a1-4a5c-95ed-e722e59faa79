import type { Option } from './type';

export const getValue = (key: string, list: Option[] = [], map: any = {}) => {
  if (map[key]) return map[key];
  const target = list.find(({ value }) => value === key);
  return target && target.value ? target.value : '';
};
export const hide = {
  inserted({ clientWidth, scrollWidth }, bind, vNode) {
    if (clientWidth !== scrollWidth) return;
    (vNode as any).componentInstance.$destroy();
  }
};

export const includesPro = (data: string, query: string) => {
  return String(data).toLowerCase().includes(String(query).toLowerCase().trim());
};
