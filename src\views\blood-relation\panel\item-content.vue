<template>
  <div class="content__container">
    <!-- label -->
    <span v-if="label" class="content-lable"> {{ label }}{{ label ? ':' : '' }} </span>
    <!-- value 有权限、链接模式 -->
    <a v-if="linkPower && viewPower" class="content-value--underline" @click="$emit('click')"> {{ value }}</a>
    <!-- value 无权限、查看模式 -->
    <el-tooltip v-else-if="viewPower" effect="light" placement="right" :content="$t('pa.blood.tip11')">
      <span class="content-value content-value--underline" :title="value"> {{ value }}</span>
    </el-tooltip>
    <!-- value 查看模式 -->
    <p v-else class="content-value" :title="value">
      <span class="content-value__text">{{ value }}</span>
      <bs-tag v-if="tagInfo" :color="tagInfo[1]" :border="false">{{ tagInfo[0] }}</bs-tag>
    </p>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { TAG_MAPS } from './utils';

@Component
export default class ItemContent extends Vue {
  @Prop({ default: false }) linkPower!: boolean | string;
  @Prop({ default: false }) viewPower!: boolean | string;
  @Prop({ default: '' }) label!: string;
  @Prop({ default: '' }) value!: string;
  @Prop({ default: '' }) tag!: string;
  get tagInfo() {
    return TAG_MAPS[this.tag];
  }
}
</script>

<style lang="scss" scoped>
.content {
  &__container {
    display: flex;
    width: 100%;
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    overflow: hidden;
    a {
      color: $--color-primary;
    }
  }
  &-lable {
    flex-shrink: 0;
    margin-right: 10px;
    min-width: 60px;
    color: #777777;
  }
  &-value {
    display: flex;
    color: #444444;
    overflow: hidden;
    text-overflow: ellipsis;
    &--underline {
      cursor: pointer;
      text-decoration: underline;
      word-break: break-all;
    }
    &__text {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .bs-tag {
      margin-left: 8px;
    }
  }
}
</style>
