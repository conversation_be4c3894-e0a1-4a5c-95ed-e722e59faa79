version="2.7.13-9-guangda"
currentBranch=`git symbolic-ref HEAD 2>/dev/null | cut -d"/" -f 3`
isClean="false"
# Start
if ${isClean} == "true" ;then
git push origin :refs/tags/v${version}
git tag -d v${version}
git branch -D ${version}
git push origin --delete ${version}
else
echo "当前分支是 ${currentBranch} !!!"
echo "要生成的tag版本是 ${version} !!!"
echo "要生成的tag版本是 ${version} !!!"
echo "要生成的tag版本是 ${version} !!!"

if git rev-parse --verify v${version};then
   echo "目标tag存在"
else
 echo "目标tag不存在"
 if git rev-parse --verify ${version};then
    echo "目标branch存在"
    git branch -D ${version}
  else
    echo "目标branch不存在"
  fi
  git checkout -b ${version}
  # sed -i "" "10d" pom.xml
  # sed -i "" "9a\\"$'\n'"    <version>${version}</version>"$'\n'"" pom.xml
  sed -i "" "15d" pom.xml
  sed -i "" "14a\\"$'\n'"    <version>${version}</version>"$'\n'"" pom.xml
  git add pom.xml
  git commit -m "feat(pom.xml)：版本号升级至${version}"
  git push origin ${version}
  git tag -a v${version} -m "${version}封版"
  git push origin v${version}
  git push origin --delete ${version}
  git checkout ${currentBranch}
  git branch -D ${version}
  git remote prune origin
fi
fi

