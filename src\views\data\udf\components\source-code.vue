<template>
  <div ref="page" v-loading="codeLoading" :class="{ 'is-udf': !showButton, 'is-func': showButton }">
    <div class="source-code">
      <div v-if="!showButton" class="source-code__header">
        <span class="source-code__header--title" @click="format">源码</span>
      </div>
      <bs-code
        :key="key"
        ref="code"
        :value="value"
        title="源码"
        language="java"
        :extra-style="codeStyle"
        :extra-operations="extraOperations"
        :formatter="false"
        :operatable="showButton"
        :read-only="readOnly"
        @change="handleChange"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { URL_FUNC_VALIDATE, URL_FUNC_FORMAT } from '@/apis/commonApi';
import { post } from '@/apis/utils/net';
@Component
export default class SourceCode extends Vue {
  @Prop({ default: false }) readOnly!: boolean;
  @Prop({ default: false }) status!: string;
  @Prop({ default: false }) showButton!: boolean;
  @Prop({
    default: () => {
      return {};
    }
  })
  data!: any;
  @Prop({
    default: ''
  })
  code!: string;
  @Prop({
    default: ''
  })
  name!: string;
  private sourceCode = '';
  private value = '';
  private codeLoading = false;
  private key = 0;
  private codeStyle = {
    height: this.showButton ? '210px' : '760px'
  };
  private extraOperations: any = null;
  @Watch('status', { immediate: true })
  handleStatusChange(val) {
    this.extraOperations =
      val === '1'
        ? []
        : [
            { text: '校验', handle: this.validate },
            { text: '格式化', handle: this.format }
          ];
    this.key = Date.now();
  }
  @Watch('code', { immediate: true })
  handleCodeChange(val) {
    this.value = val;
    this.sourceCode = val;
    this.key = Date.now();
  }
  handleChange(val) {
    this.sourceCode = val;
  }
  // 校验
  async validate() {
    this.codeLoading = true;
    try {
      const { data, success, msg } = await post(URL_FUNC_VALIDATE, {
        body: (this.$refs.code as any).getValue(),
        name: this.name,
        id: this.data.id,
        nameCn: this.data.nameCn,
        orgId: this.data.orgId,
        tempSave: this.data.tempSave,
        description: this.data.description
      });
      if (success) {
        this.$emit('getName', data.name);
        this.$set(this, 'value', data.code);
        this.$set(this, 'sourceCode', data.code);
        this.key = Date.now();
        this.$message.success(msg);
      } else {
        this.$message.error(msg);
      }
    } catch {
      this.codeLoading = false;
    }
    this.codeLoading = false;
  }
  async format() {
    this.codeLoading = true;
    try {
      const { data, success, msg } = await post(URL_FUNC_FORMAT, {
        body: (this.$refs.code as any).getValue()
      });
      if (success) {
        this.$set(this, 'value', data);
        this.$set(this, 'sourceCode', data);
        this.key = Date.now();
      } else {
        this.$message.error(msg);
      }
      this.codeLoading = false;
    } catch {
      this.codeLoading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .bs-code {
  border: none;
}
.is-udf {
  height: 840px;
}
.is-func {
  height: 290px;
}
.source-code {
  width: calc(100% - 40px);
  display: flex;
  border: 1px solid #e5e5e5;
  flex-direction: column;
  justify-content: center;
  margin-left: 20px;
  &__header {
    height: 50px;
    background-color: #fafafa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 20px;
    &--title {
      font-size: 14px;
      color: #444444;
    }
    &--buttons {
      margin-right: 10px;
    }
  }
}
</style>
