<template>
  <el-descriptions :column="1">
    <el-descriptions-item :label="$t('pa.flow.name')">{{ data.projectName }}</el-descriptions-item>
    <el-descriptions-item :label="$t('pa.flow.fDir')">{{ data.parentProject }}</el-descriptions-item>
    <el-descriptions-item :label="$t('pa.flow.createOrg')">{{ data.orgName }}</el-descriptions-item>
    <el-descriptions-item :label="$t('pa.flow.creater')">{{ data.createdBy }}</el-descriptions-item>
    <el-descriptions-item :label="$t('pa.flow.createTime')">{{ data.createTime }}</el-descriptions-item>
    <el-descriptions-item :label="$t('pa.flow.updater')">{{ data.updatedBy }}</el-descriptions-item>
    <el-descriptions-item :label="$t('pa.flow.updateTime')">{{ data.updateTime }}</el-descriptions-item>
  </el-descriptions>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getDirById } from '@/apis/flowNewApi';
import dayjs from 'dayjs';

@Component
export default class AddDir extends Vue {
  @Prop() id!: string;
  data: Record<string, any> = {};
  async created() {
    const { data } = await getDirById({ id: this.id });
    data.createTime = dayjs(data.createTime).format('YYYY-MM-DD HH:mm:ss');
    data.updateTime = dayjs(data.updateTime).format('YYYY-MM-DD HH:mm:ss');
    this.data = data;
  }
}
</script>

<style lang="scss" scoped></style>
