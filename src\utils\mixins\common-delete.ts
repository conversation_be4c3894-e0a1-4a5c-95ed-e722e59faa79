import { Component, Vue } from 'vue-property-decorator';
const SINGEL_TEXT = {
  '0': '您确定要删除选中的${name}吗？',
  '1': '${name}已经被关联，无法删除！'
};
const BATCH_TEXT = {
  '0': '您确定要删除选中的 [${name}] ${num}条记录吗？',
  '1': '您选中的 [${name}] ${num}条记录被关联，无法删除！[${name}] ${num}条记录没有关联，您确定要删除吗？',
  '2': '您选中的 [${name}] ${num}条记录被关联，无法删除！'
};
@Component
export default class CommonDelete extends Vue {
  replaceStr(str, datas) {
    datas.forEach((data) => {
      str = str.replace(/\${name}/, data.name);
      str = str.replace(/\${num}/, data.num);
    });
    return str;
  }
  getMsg(isBatch, { referenced, notReferenced }) {
    const jointName = (arr) => {
      return (
        arr
          .slice(0, 3)
          .map((e) => e.name)
          .join('、') + (arr.length > 3 ? '...' : '')
      );
    };
    if (!isBatch) {
      const target = (referenced.length ? referenced : notReferenced)[0];
      return this.replaceStr(SINGEL_TEXT[referenced.length ? '1' : '0'], [{ name: target.name }]);
    } else if (referenced.length === 0) {
      return this.replaceStr(BATCH_TEXT['0'], [
        { name: jointName(notReferenced), num: notReferenced.length }
      ]);
    } else if (notReferenced.length === 0) {
      return this.replaceStr(BATCH_TEXT['2'], [
        { name: jointName(referenced), num: referenced.length }
      ]);
    } else {
      return this.replaceStr(BATCH_TEXT['1'], [
        { name: jointName(referenced), num: referenced.length },
        { name: jointName(notReferenced), num: notReferenced.length }
      ]);
    }
  }
  async commonDel(datas, delFn) {
    const isBatch = Array.isArray(datas);
    if (isBatch && datas.length === 0) {
      this.$message.warning('请选择需要删除的记录');
      return;
    }
    // 删除前进行校验
    const referenced = (isBatch ? datas : [datas]).filter((item) => item.reference > 0);
    const notReferenced = (isBatch ? datas : [datas]).filter((item) => item.reference === 0);
    const canDelete = !!notReferenced.length;
    const msg = this.getMsg(isBatch, { referenced, notReferenced });
    if (canDelete) {
      this.$confirm(msg, '提示', {
        type: 'warning'
      }).then(() => {
        delFn(notReferenced.map((e) => e.id));
      });
    } else {
      this.$message.error(msg);
    }
  }
}
