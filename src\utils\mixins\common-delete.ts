import { Component, Vue } from 'vue-property-decorator';
import i18n from '@/i18n';
const SINGLE_TEXT = {
  '0': i18n.t('pa.singleText0'),
  '1': i18n.t('pa.singleText1')
};
const BATCH_TEXT = {
  '0': i18n.t('pa.batchText0'),
  '1': i18n.t('pa.batchText1'),
  '2': i18n.t('pa.batchText2')
};
@Component
export default class CommonDelete extends Vue {
  replaceStr(str, datas) {
    datas.forEach((data) => {
      str = str.replace(/\${name}/, data.name);
      str = str.replace(/\${num}/, data.num);
    });
    return str;
  }
  getMsg(isBatch, { referenced, notReferenced }) {
    const jointName = (arr) => {
      return (
        arr
          .slice(0, 3)
          .map((e) => e.name)
          .join('、') + (arr.length > 3 ? '...' : '')
      );
    };
    if (!isBatch) {
      const target = (referenced.length ? referenced : notReferenced)[0];
      return this.replaceStr(SINGLE_TEXT[referenced.length ? '1' : '0'], [{ name: target.name }]);
    } else if (referenced.length === 0) {
      return this.replaceStr(BATCH_TEXT['0'], [{ name: jointName(notReferenced), num: notReferenced.length }]);
    } else if (notReferenced.length === 0) {
      return this.replaceStr(BATCH_TEXT['2'], [{ name: jointName(referenced), num: referenced.length }]);
    } else {
      return this.replaceStr(BATCH_TEXT['1'], [
        { name: jointName(referenced), num: referenced.length },
        { name: jointName(notReferenced), num: notReferenced.length }
      ]);
    }
  }
  async commonDel(datas, delFn) {
    const isBatch = Array.isArray(datas);
    if (isBatch && datas.length === 0) return this.$message.warning(this.$t('pa.delData'));
    await this.$confirm(this.$t('pa.singleText0'), i18n.t('pa.prompt') as string);
    delFn((isBatch ? datas : [datas]).map((el) => el.id));
  }
}
