<template>
  <bs-dialog
    title="导出日志"
    width="40%"
    :visible.sync="display"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @confirm="handler"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="80px"
      @submit.native.prevent
    >
      <el-form-item label="字段名称" prop="headTitles">
        <el-input
          v-model="formData.headTitles"
          autocomplete="off"
          placeholder="请输入导出字段，以逗号分隔"
        />
      </el-form-item>
    </el-form>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import { post } from '@/apis/utils/net';
import { URL_LOG_DOWNLOAD, URL_IQ_DATA_DOWNLOAD } from '@/apis/commonApi';
import { download } from '@/utils';
import ElForm from 'bs-ui-pro/packages/form';
import dayjs from 'dayjs';
@Component
export default class DownloadModal extends Vue {
  @PropSync('show', { default: false }) display!: boolean;
  @Prop({ default: '' }) flowId!: string;
  @Prop({ default: () => ({}) }) data!: any;
  @Ref('formRef') readonly form!: ElForm;

  private formData: any = { headTitles: '' };
  private rules: any = {
    headTitles: {
      required: true,
      message: '请输入导出字段，以逗号分隔',
      trigger: 'blur'
    }
  };

  async handler() {
    try {
      await this.form.validate();
      const res = await post(this.getUrl(), this.getParams());
      this.display = false;
      download(res);
    } catch {}
  }
  getUrl() {
    return this.data.level === 'warn_data' ? URL_LOG_DOWNLOAD : URL_IQ_DATA_DOWNLOAD;
  }
  getParams() {
    return {
      begTime: dayjs(this.data.dateTime[0]).format('YYYY-MM-DD HH:mm'),
      endTime: dayjs(this.data.dateTime[1]).format('YYYY-MM-DD HH:mm'),
      jobId: this.flowId,
      keyWord: this.data.keyWord,
      headTitles: this.formData.headTitles
    };
  }
}
</script>
