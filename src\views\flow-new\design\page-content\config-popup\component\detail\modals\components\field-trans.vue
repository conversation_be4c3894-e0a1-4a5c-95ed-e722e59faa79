<template>
  <bs-dialog
    :title="$t('pa.flow.fieldTrans')"
    size="medium"
    append-to-body
    :visible.sync="display"
    :before-close="closeDialog"
  >
    <div v-if="!onlyCsv" class="head">
      <div>
        {{ $t('pa.flow.fieldTransMethod') }}
        <bs-select v-model="type" :options="options" style="width: 150px; margin: 0 30px 0 10px" @change="handleChange" />
      </div>
      <div v-if="type === 'CSV'">
        {{ $t('pa.flow.splitStr') }}
        <el-input v-model="separator" :placeholder="$t('pa.flow.placeholder18')" style="width: 220px" />
      </div>
    </div>
    <div class="codeInfo">
      <el-form ref="form" :model="formData" :rules="rules">
        <el-form-item v-if="type === 'JSON'" prop="codeData">
          <bs-code
            ref="code"
            :value="formData.codeData"
            :operatable="false"
            :read-only="disabled"
            :extra-style="{ height: '290px' }"
          />
        </el-form-item>
        <el-form-item v-if="type === 'CSV'" prop="csvData">
          <el-input
            v-model="formData.csvData"
            type="textarea"
            :autosize="{ minRows: 14 }"
            :placeholder="placeholder"
            :disabled="disabled"
            style="width: 100%; height: 100%"
          />
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">{{ $t('pa.flow.close') }}</el-button>
      <el-button v-if="!onlyCsv" type="primary" @click="codeFormat">{{ $t('pa.flow.trans') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('pa.flow.confirm') }}</el-button>
    </span>
  </bs-dialog>
</template>
<script lang="ts">
import { Vue, Component, PropSync, Prop, Watch } from 'vue-property-decorator';
import _ from 'lodash';
import i18n from '@/i18n';
// 校验是都为json可解析的对象
const validateJsonVaild = (rule, value, callback) => {
  const errorMsg = i18n.t('pa.flow.msg87') as string;
  try {
    const str = JSON.parse(value);
    if (typeof str !== 'object') {
      callback(new Error(errorMsg));
    } else {
      callback();
    }
  } catch (err) {
    callback(new Error(errorMsg));
  }
};
@Component
export default class FieldTrans extends Vue {
  @PropSync('show', { type: Boolean, default: false }) display!: boolean;
  @Prop() info!: any;
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: false }) onlyCsv!: boolean;
  @Prop() inputs!: any;
  visible = false; // 是否展示弹窗
  // 字段转换方式
  type = 'JSON';
  options = [
    {
      label: this.$t('pa.flow.jsonTtrans'),
      value: 'JSON'
    },
    {
      label: this.$t('pa.flow.csvTrans'),
      value: 'CSV'
    }
  ];
  separator = ''; // 分隔符
  formData: { codeData?: string; csvData: string } = {
    codeData: '{"key1":"value","key2":[1,2],"key3":{"key4":"xx"}}',
    csvData: ''
  };
  rules: { codeData?: Base[]; csvData: Base[] } = {
    codeData: [
      { required: true, message: this.$t('pa.flow.msg88') },
      { validator: validateJsonVaild, trigger: 'blur' }
    ],
    csvData: [
      { required: true, message: this.$t('pa.flow.msg89') },
      { validator: this.validateRepeat, trigger: 'blur' }
    ]
  };
  get isJSON() {
    return this.type === 'JSON';
  }
  get placeholder() {
    return this.onlyCsv ? this.$t('pa.flow.msg90') : this.$t('pa.flow.placeholder0');
  }
  @Watch('info', { immediate: true })
  dataChange(val) {
    if (!val) return;
    const obj = {};
    val.forEach((item) => {
      obj[item] = '';
    });
    this.formData.codeData = JSON.stringify(obj);
    this.formData.csvData = val.join(',');
  }
  created() {
    if (this.onlyCsv) {
      delete this.formData.codeData;
      delete this.rules.codeData;
      this.type = 'CSV';
    }
  }
  // 转换样式
  codeFormat() {
    (this.$refs.code as any).format();
  }
  // 表单校验字段重复
  validateRepeat(rule, value, callback) {
    const keys = value.split(this.separator || ',') || [];
    const newFields: string[] = [];
    if (this.onlyCsv) {
      // 获取原始字段
      const originalFields = (Array.isArray(this.inputs) ? this.inputs : []).map((i) => i.name);
      keys.forEach((k: string) => {
        if (!originalFields.includes(k)) {
          newFields.push(k);
        }
      });
    }
    if (newFields.length) {
      const msg = [...new Set([...newFields])].map((r) => `{${r}}`).join(',');
      callback(new Error(this.$t('pa.flow.msg91', [msg])));
    } else {
      const result = _.uniq(_.filter(keys, (v, i, a) => a.indexOf(v) !== i));
      if (result.length) {
        const msg = [...new Set(result)].map((r) => `{${r}}`).join(',');
        callback(new Error(this.$t('pa.flow.msg92', [msg])));
      } else {
        callback();
      }
    }
  }
  // 切换方式时清除原先的校验结果
  handleChange() {
    (this.$refs.form as any).clearValidate();
  }
  // 确认
  submit() {
    (this.$refs.form as any).validate((valid) => {
      if (valid) {
        const { codeData, csvData } = this.formData;
        const saveData = this.isJSON
          ? Object.keys(JSON.parse(codeData || '')) || []
          : csvData.split(this.separator || ',') || [];
        this.$emit('trans', saveData);
        this.closeDialog();
      }
    });
  }
  //关闭弹窗
  closeDialog() {
    this.display = false;
    this.type = 'JSON';
  }
}
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  margin-bottom: 14px;
}
.codeInfo {
  height: 320px;
}
</style>
