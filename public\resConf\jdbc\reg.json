{"forms": [{"type": "input", "prop": "title", "label": "名称", "componentProps": {"maxlength": 30, "placeholder": "请输入名称", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入名称", "trigger": "blur"}, {"min": 2, "max": 30, "message": "长度在 2 到 30 个字符", "trigger": "blur"}]}, {"type": "select", "prop": "jdbcType", "label": "数据库类型", "componentProps": {"options": [{"label": "MySQL", "value": "MySQL"}, {"label": "Oracle", "value": "Oracle"}, {"label": "OceanBase", "value": "OceanBase"}, {"label": "PostgreSQL", "value": "PostgreSQL"}, {"label": "OpenGauss", "value": "OpenGauss"}, {"label": "DM", "value": "DM"}, {"label": "GBase", "value": "GBase"}, {"label": "GaussDB", "value": "GaussDB"}], "placeholder": "请选择数据库类型"}, "rules": [{"message": "请选择数据库类型", "required": true, "trigger": "blur"}], "defaultVal": "MySQL"}, {"type": "textarea", "prop": "url", "label": "JDBC连接串", "componentProps": {"rows": 3, "maxlength": 1024, "placeholder": "请输入JDBC连接串，jdbc:mysql://${ip}:${port}/${schema}或jdbc:oracle:thin:@${ip}:${port}:${schema}"}, "rules": [{"required": true, "message": "请输入JDBC连接串", "trigger": "blur"}, {"pattern": "/^\\S*$/", "message": "JDBC连接串中不能有空格或换行", "trigger": "blur"}]}, {"type": "input", "prop": "schema-name", "label": "schema", "deps": ["jdbcType"], "visible": "(scope) => ['PostgreSQL', 'OpenGauss',  'GaussDB'].includes(scope.jdbcType)", "componentProps": {"maxlength": 255, "placeholder": "schema-name, schema-name','分隔, 支持正则表达式, 请勿输入空格"}, "rules": [{"required": true, "message": "请输入schema", "trigger": "blur"}]}, {"type": "input", "prop": "username", "label": "用户名", "componentProps": {"maxlength": 60, "placeholder": "请输入用户名", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入用户名", "trigger": "blur"}]}, {"type": "password", "prop": "password", "label": "密码", "componentProps": {"maxlength": 100, "placeholder": "请输入密码"}, "rules": [{"required": true, "message": "请输入密码", "trigger": "blur"}]}, {"type": "input", "prop": "OceanBase-tenant", "label": "租户", "deps": ["jdbcType"], "visible": "(scope) => scope.jdbcType === 'OceanBase'", "componentProps": {"maxlength": 30, "placeholder": "请输入租户名", "showWordLimit": true}, "rules": [{"required": true, "message": "请输入租户名", "trigger": "blur"}]}, {"type": "select", "prop": "OceanBase-tenantMode", "label": "租户模式", "deps": ["jdbcType"], "visible": "(scope) => scope.jdbcType === 'OceanBase'", "componentProps": {"options": [{"label": "MySQL", "value": "MySQL"}, {"label": "Oracle", "value": "Oracle"}], "placeholder": "请选择租户模式"}, "rules": [{"message": "请选择数据库类型", "required": true, "trigger": "blur"}], "defaultVal": "MySQL"}, {"type": "input", "prop": "OceanBase-rsList", "label": "rsList", "deps": ["jdbcType"], "visible": "(scope) => scope.jdbcType === 'OceanBase'", "componentProps": {"maxlength": 30, "placeholder": "127.0.0.1:2882:2881", "showWordLimit": true}, "tooltip": "ip1:rpcPort1:sqlPort1,ip2:rpcPort2:sqlPort2 如：127.0.0.1:2882:2881 -供cdc使用"}, {"type": "input", "prop": "OceanBase-logProxyHost", "label": "logProxyHost", "deps": ["jdbcType"], "visible": "(scope) => scope.jdbcType === 'OceanBase'", "componentProps": {"maxlength": 30, "showWordLimit": true}, "tooltip": "logProxy的ip地址 -供cdc使用"}, {"type": "input-number", "prop": "OceanBase-logProxyPort", "label": "logProxyPort", "deps": ["jdbcType"], "visible": "(scope) => scope.jdbcType === 'OceanBase'", "componentProps": {"min": 1, "max": 65535, "maxlength": 30}, "defaultVal": "2983", "tooltip": "logProxy所使用的端口-供cdc使用"}, {"type": "textarea", "prop": "memo", "label": "备注", "componentProps": {"maxlength": 255, "placeholder": "请输入备注", "rows": 5}}]}