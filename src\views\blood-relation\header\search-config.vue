<template>
  <el-popover
    v-model="dispaly"
    width="350"
    popper-class="search-popover"
    trigger="click"
    placement="bottom"
    :append-to-body="false"
    @hide="hide"
  >
    <!-- 表单 -->
    <el-form
      v-if="haschange"
      ref="form"
      label-position="top"
      :model="formData"
      :rules="rules"
      class="form"
    >
      <el-form-item label="节点类型" prop="resType">
        <el-select
          v-model="formData.resType"
          :popper-append-to-body="!isFullScreen"
          @change="resTypeChange"
          @visible-change="visibleChange"
        >
          <el-option
            v-for="item in nodeType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="formData.resType === 'SERVICE' || formData.resType === 'TABLE'"
        label="服务资源类型"
        prop="serviceType"
      >
        <el-select
          v-model="formData.serviceType"
          clearable
          filterable
          :popper-append-to-body="!isFullScreen"
          @change="serviceChange"
          @visible-change="visibleChange"
        >
          <el-option
            v-for="item in serviceList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="formData.resType === 'JOB'" label="项目名称" prop="serviceName">
        <el-select
          v-model="formData.serviceName"
          clearable
          filterable
          :popper-append-to-body="!isFullScreen"
          @change="nameChange"
          @visible-change="visibleChange"
        >
          <el-option
            v-for="item in projectList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="formData.resType"
        :label="nodeLable"
        prop="serviceId"
        :rules="{
          required: true,
          message: `请选择${nodeLable}`,
          trigger: 'blur'
        }"
      >
        <el-select
          v-model="formData.serviceId"
          clearable
          filterable
          :popper-append-to-body="!isFullScreen"
          @change="pointChange"
          @visible-change="visibleChange"
        >
          <el-option
            v-for="item in pointList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <div v-if="formData.resType === 'SERVICE' || formData.resType === 'TABLE'">
        <el-form-item
          v-for="item in enumsList"
          :key="item.label"
          :prop="item.prop"
          :rules="getRule(item.label)"
        >
          <template slot="label">
            <span>{{ item.label }}</span>
            <el-tooltip v-if="item.memo" :content="item.memo" effect="light" placement="top">
              <span class="iconfont icon-wenhao" style="marginleft: 5px"></span>
            </el-tooltip>
          </template>
          <el-input v-model="formData[item.prop]" />
        </el-form-item>
      </div>

      <!-- 按钮 -->
      <div style="text-align: right; padding: 0 14px">
        <el-button size="small" @click="rest">重置</el-button>
        <el-button type="primary" size="small" @click="submit">确定</el-button>
      </div>
    </el-form>
    <el-button slot="reference" icon="bs-icon-pingjiguanli" type="primary">查询节点</el-button>
  </el-popover>
</template>

<script lang="ts">
import { Component, Prop, InjectReactive, Watch, Vue } from 'vue-property-decorator';
import { flowType, rules, data, labelMapping } from './index';
import { getViewList, getResList, getChartList, sourceInfo } from '@/apis/dataApi';
import { getTypeList, getServiceList } from '@/apis/serviceApi';
import { getFlowList } from '@/apis/flowTestApi';
@Component
export default class DisplayConfig extends Vue {
  @Prop({ default: false }) isFullScreen!: boolean;
  @InjectReactive() canUseSql!: boolean;
  dispaly = false;
  haschange = true;
  formData = {
    resType: '',
    serviceType: '',
    serviceName: '',
    flowType: '',
    serviceId: '',
    resourceList: ''
  };
  flowType = flowType;
  pointList = []; //节点列表
  serviceList: any[] = []; // 服务资源列表
  projectList = []; // 流程列表

  enumsList = []; // 资源项列表
  visible = false;
  rules = rules;

  get nodeType() {
    return [
      { value: 'JOB', label: '流程' },
      { value: 'SERVICE', label: '服务资源' },
      this.canUseSql && { value: 'TABLE', label: '表' },
      this.canUseSql && { value: 'VIEW', label: '视图' }
    ].filter(Boolean);
  }

  get nodeLable() {
    return labelMapping[this.formData.resType] || '节点名称';
  }

  //监控是否是全屏
  @Watch('isFullScreen')
  screenChnage() {
    this.haschange = false;
    this.$nextTick(() => {
      this.haschange = true;
    });
  }
  hide() {
    this.visible && (this.dispaly = true);
  }
  visibleChange(val: boolean) {
    this.visible = val;
  }
  getRule(label: string) {
    return label === '目录表达式'
      ? {}
      : { required: true, message: `请输入${label}`, trigger: 'blur' };
  }
  submit() {
    const form: any = this.$refs['form'];
    form.validate((valid: any) => {
      if (valid) {
        const { serviceId, resType, resourceList } = this.formData;
        this.dispaly = false;
        let list = '';
        this.enumsList &&
          this.enumsList.forEach((it: any) => {
            if (this.formData[it.prop]) {
              list += this.formData[it.prop].replace(' ', '') + ' ';
            }
          });
        this.$emit('submit', {
          serviceId,
          resType,
          resourceList: resourceList || list.trim()
        });
        form.resetFields();
      }
    });
  }
  rest() {
    const form: any = this.$refs['form'];
    form.resetFields();
  }
  resTypeChange(val) {
    this.formData = { ...this.formData, ...data };
    this.pointList = []; //节点列表
    this.serviceList = []; // 服务资源列表
    this.projectList = [];
    this.enumsList = [];
    const form: any = this.$refs['form'];
    form.clearValidate();
    if (val === 'VIEW') {
      getViewList({ search: {} }).then((res) => {
        if (res.success) {
          this.pointList = res.data.tableData.map((item) => {
            return {
              label: item.viewName,
              value: item.id
            };
          });
        }
      });
    }
    if (val === 'TABLE') {
      getResList().then((res) => {
        if (res.success) {
          this.serviceList = res.data.map((item) => {
            return {
              label: item,
              value: item
            };
          });
        } else {
          this.$message.success({
            message: res.msg,
            el: this.isFullScreen ? '#bloodRelation' : ''
          });
        }
      });
    }
    if (val === 'SERVICE') {
      getTypeList().then((res) => {
        if (res.success) {
          this.serviceList = res.data.reduce((pre: any[], { label, type: value }: any) => {
            if (!['ZOOKEEPER', 'FLINK'].includes(value)) {
              pre.push({ label, value });
            }
            return pre;
          }, []);
        } else {
          this.$message.success({
            message: res.msg,
            el: this.isFullScreen ? '#bloodRelation' : ''
          });
        }
      });
    }
    if (val === 'JOB') {
      getFlowList().then((res) => {
        if (res.success) {
          this.projectList = res.data.map((item) => {
            return {
              children: item.children,
              label: item.projectName,
              value: item.projectId
            };
          });
        } else {
          this.$message.success({
            message: res.msg,
            el: this.isFullScreen ? '#bloodRelation' : ''
          });
        }
      });
    }
  }
  async getInfo(val) {
    if (this.formData.resType === 'SERVICE') {
      const { data, success, msg } = await sourceInfo(val);
      if (success) {
        if (!['DAEMON', 'SOCKET', 'REDIS', 'STREAMCUBE'].includes(this.formData.serviceType)) {
          this.enumsList = data;
        }
      } else {
        this.$message.success({
          message: msg,
          el: this.isFullScreen ? '#bloodRelation' : ''
        });
      }
    }
  }
  serviceChange(val) {
    this.getInfo(val);
    this.formData = { ...this.formData, serviceId: '', resourceList: '' };
    this.pointList = [];
    this.enumsList = [];
    const form: any = this.$refs['form'];
    form.clearValidate();
    if (this.formData.resType === 'TABLE') {
      getChartList({ search: { resType: val } }).then((res) => {
        if (res.success) {
          this.pointList = res.data.tableData.map((item) => {
            return {
              source: item.level1,
              label: item.tableName,
              value: item.id
            };
          });
        } else {
          this.$message.success({
            message: res.msg,
            el: this.isFullScreen ? '#bloodRelation' : ''
          });
        }
      });
    } else {
      getServiceList({ type: val, params: { search: '' } }).then((res) => {
        if (res.success) {
          this.pointList = res.data.tableData.map((item) => {
            return {
              label: item.title,
              value: item.id
            };
          });
        } else {
          this.$message.success({
            message: res.msg,
            el: this.isFullScreen ? '#bloodRelation' : ''
          });
        }
      });
    }
  }
  nameChange(val) {
    this.formData = { ...this.formData, flowType: '', serviceId: '' };
    this.pointList = [];
    const obj: any = this.projectList.find((item: any) => item.value === val);
    this.pointList =
      obj &&
      obj.children.map((item: any) => {
        return {
          label: item.jobName,
          value: item.id
        };
      });
  }
  pointChange(val) {
    if (this.formData.resType === 'TABLE') {
      const info: any = this.pointList.find((item: any) => item.value === val);
      this.formData.resourceList = (info && info.source) || '';
    }
  }
}
</script>
<style lang="scss">
.search {
  &-popover {
    padding: 16px 16px !important;
    width: 350px !important;
    background: #ffffff;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    box-sizing: border-box;
    z-index: 2001 !important;
  }
}
</style>
<style lang="scss" scoped>
::v-deep .el-form-item__label {
  padding: 0px;
}
::v-deep .el-select {
  width: 100%;
}
.form {
  max-height: 60vh;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
