<template>
  <div class="search-config">
    <!-- 表单 -->
    <el-form ref="formRef" v-loading="loading" class="form" :rules="rules" :model="formData">
      <!-- 节点类型 -->
      <el-form-item :label="$t('pa.blood.nodeType')" prop="resType">
        <el-select
          v-model="formData.resType"
          filterable
          :popper-append-to-body="false"
          @change="handleResTypeChange"
          @visible-change="handleVisibleChange"
        >
          <el-option v-for="el in nodeType" :key="el.value" :label="el.label" :value="el.value" />
        </el-select>
      </el-form-item>
      <!-- 服务/表资源类型 -->
      <el-form-item v-if="showServiceType" :label="$t('pa.blood.serviceType')" prop="serviceType">
        <el-select
          v-model="formData.serviceType"
          clearable
          filterable
          :popper-append-to-body="false"
          @change="handleServiceChange"
          @visible-change="handleVisibleChange"
        >
          <el-option v-for="el in serviceList" :key="el.value" :label="el.label" :value="el.value" />
        </el-select>
      </el-form-item>
      <!-- 流程 -->
      <el-form-item v-if="formData.resType === 'JOB'" :label="$t('pa.flowName')" :rules="serviceIdRules" prop="serviceId">
        <bs-cascader
          v-model="formData.serviceId"
          :options="projectList"
          filterable
          :append-to-body="false"
          :props="{ emitPath: false }"
          popper-class="dir-cascader"
          style="width: 100%"
        />
      </el-form-item>
      <!-- serviceId -->
      <el-form-item
        v-if="formData.resType && formData.resType !== 'JOB'"
        prop="serviceId"
        :label="serviceIdLable"
        :rules="serviceIdRules"
      >
        <el-select
          v-model="formData.serviceId"
          clearable
          filterable
          :popper-append-to-body="false"
          @change="handleServiceIdChange"
          @visible-change="handleVisibleChange"
        >
          <el-option v-for="el in pointList" :key="el.value" :label="el.label" :value="el.value" />
        </el-select>
      </el-form-item>
      <!-- 自定义数据区域 -->
      <template v-if="showEnumsArea">
        <el-form-item v-for="el in enumsList" :key="el.label" :prop="el.prop" :rules="getItemRules(el)">
          <template slot="label">
            <span>{{ el.label }}</span>
            <el-tooltip v-if="el.memo" :content="el.memo" effect="light" placement="top">
              <i class="bs-pro-form-item__icon bs-icon-wenti" style="cursor: pointer"></i>
            </el-tooltip>
          </template>
          <el-input v-model="formData[el.prop]" :placeholder="$t('pa.placeholder.input')" />
        </el-form-item>
      </template>
    </el-form>
    <el-button type="primary" @click="submit">{{ $t('pa.action.search') }}</el-button>
  </div>
</template>

<script lang="ts">
import { Component, Prop, InjectReactive, Ref, Vue } from 'vue-property-decorator';
import { getResTypeList as getTableList, getChartList, sourceInfo, getCatalogList } from '@/apis/dataApi';
import { getTypeList as getResTypeList, getServiceList } from '@/apis/serviceApi';
import { FLOW_TYPE_OPTION, FORM_RULES, GET_FORM_DEFAULT_VALUE, LABLE_MAPPING } from './utils';
import Elform from 'bs-ui-pro/lib/form';
import { getAllJob } from '@/apis/blood-relation';
@Component
export default class DisplayConfig extends Vue {
  @Prop({ default: false }) isFullScreen!: boolean;
  @InjectReactive() canUseSql!: boolean;
  @InjectReactive('isCloud') isCloud!: boolean;
  @Ref('formRef') readonly form!: Elform;

  visible = false;
  flowType = FLOW_TYPE_OPTION;
  pointList: any[] = []; //节点列表
  serviceList: any[] = []; // 服务资源列表
  projectList: any[] = []; // 流程列表
  enumsList: any[] = []; // 资源项列表
  formData = {
    resType: '',
    serviceType: '',
    serviceName: '',
    flowType: '',
    serviceId: '',
    resourceList: ''
  };
  rules = FORM_RULES;
  loading = false;
  get nodeType() {
    return [
      { value: 'JOB', label: this.$t('pa.flowName') },
      { value: 'SERVICE', label: this.$t('pa.service') },
      this.canUseSql && { value: 'TABLE', label: this.$t('pa.table') },
      this.canUseSql && { value: 'CATALOG', label: 'Catalog' }
      // !this.isCloud && this.canUseSql && { value: 'VIEW', label: '视图' }
    ].filter(Boolean);
  }

  get serviceIdLable() {
    return LABLE_MAPPING[this.formData.resType] || this.$t('pa.blood.nodeName');
  }
  get serviceIdRules() {
    return {
      required: true,
      message: `${this.$t('pa.placeholder.select')}${this.serviceIdLable}`,
      trigger: 'blur'
    };
  }
  get showServiceType() {
    return this.formData.resType === 'SERVICE' || this.formData.resType === 'TABLE';
  }
  get showEnumsArea() {
    return this.formData.resType === 'SERVICE' || this.formData.resType === 'TABLE';
  }
  // 单个资源，多个维度间的分隔符。 如as 的namespace和set
  get lineageDimensionSplit() {
    return this.$store.getters.lineageDimensionSplit;
  }
  async created() {
    if (this.$route.query.serviceId) {
      Object.assign(this.formData, this.$route.query);
      // 触发搜索
      this.$emit('search', {
        serviceId: this.formData.serviceId,
        resType: this.formData.resType,
        resourceList: this.formData.resourceList
      });
      // 根据formData的值请求列表
      await this.handleResTypeChange(this.formData.resType, false);
      ['SERVICE', 'TABLE'].includes(this.formData.resType) &&
        this.formData.serviceType &&
        (await this.handleServiceChange(this.formData.serviceType, false));
      if (Array.isArray(this.enumsList) && this.formData.resourceList) {
        const list = this.formData.resourceList.split(this.lineageDimensionSplit);
        this.enumsList.forEach(({ prop }, idx) => {
          this.$set(this.formData, prop, list[idx] || '');
        });
      }
    }
  }

  /* 处理【节点类型】变化事件 */
  handleResTypeChange(val, needRefesh = true) {
    needRefesh && (this.formData = { ...this.formData, ...GET_FORM_DEFAULT_VALUE() });
    this.pointList = []; //节点列表
    this.serviceList = []; // 服务资源列表
    this.projectList = [];
    this.enumsList = [];
    this.form?.clearValidate();
    switch (val) {
      case 'JOB':
        this.getJobOptions();
        break;
      case 'SERVICE':
        this.getServiceOptions();
        break;
      case 'TABLE':
        this.getTableOptions();
        break;
      case 'CATALOG':
        this.getCatalogOptions();
        break;
      default:
        break;
    }
  }
  /* 获取流程下拉选项 */
  async getJobOptions() {
    this.loading = true;
    const { success, msg, data } = await getAllJob();
    this.loading = false;
    if (!success) return this.notify(msg);
    const transform = (data: any[]) => {
      return (Array.isArray(data) ? data : []).map(({ nodeId, nodeName, children }) => ({
        value: nodeId,
        label: nodeName,
        children: Array.isArray(children) && children.length ? transform(children) : undefined
      }));
    };
    this.projectList = transform(data);
  }
  /* 获取服务下拉选项 */
  async getServiceOptions() {
    const { success, msg, data } = await getResTypeList();
    if (!success) return this.notify(msg);
    const newData = Array.isArray(data) ? data : [];
    this.serviceList = newData.reduce((pre: any[], { label, type: value }: any) => {
      if (!['ZOOKEEPER', 'FLINK'].includes(value)) {
        pre.push({ label, value });
      }
      return pre;
    }, []);
  }
  /* 获取表下拉选项 */
  async getTableOptions() {
    const { success, msg, data } = await getTableList();
    if (!success) return this.notify(msg);
    const newData = Array.isArray(data) ? data : [];
    this.serviceList = newData.map((label) => ({ label, value: label }));
  }
  /* 获取Catalog下拉选项 */
  async getCatalogOptions() {
    const {
      success,
      msg,
      data: { tableData = [] }
    } = await getCatalogList({ pageData: null, search: '' });
    if (!success) return this.notify(msg);
    this.pointList = tableData.map((el) => {
      return {
        label: el.catalogName,
        value: el.id
      };
    });
  }
  /* 处理【服务资源类型】变化处理 */
  async handleServiceChange(val, needRefesh = true) {
    if (this.formData.resType === 'SERVICE') {
      await this.getSourceInfo(val);
    }
    needRefesh && (this.formData = { ...this.formData, serviceId: '', resourceList: '' });
    this.pointList = [];
    this.form?.clearValidate();
    if (this.formData.resType === 'TABLE') {
      val && (await this.getTableNameOptios(val));
    } else {
      val && (await this.getServerNameOptios(val));
    }
  }
  /* 获取配置字段列表 */
  async getSourceInfo(val) {
    if (!val) return this.$set(this, 'enumsList', []);
    const { success, data, msg } = await sourceInfo(val);
    if (!success) return this.notify(msg);
    this.$set(this, 'enumsList', Array.isArray(data) ? data : []);
  }
  /* 获取表名称下拉选项 */
  async getTableNameOptios(resType) {
    const { success, msg, data } = await getChartList({ search: { resType } });
    if (!success) return this.notify(msg);
    const newData = Array.isArray(data?.tableData) ? data.tableData : [];
    this.pointList = newData.map((el) => {
      return {
        source: el.level1,
        label: el.tableName,
        value: el.id
      };
    });
  }
  /* 获取服务名称下拉选项 */
  async getServerNameOptios(type) {
    const { success, msg, data } = await getServiceList({ type, params: { search: '' } });
    if (!success) return this.notify(msg);
    const newData = Array.isArray(data?.tableData) ? data.tableData : [];
    this.pointList = newData.map((el) => {
      return {
        label: el.title,
        value: el.id
      };
    });
  }
  /* 处理【项目名称】变化 */
  handleServiceNameChange(val) {
    this.formData = { ...this.formData, flowType: '', serviceId: '' };
    this.pointList = [];
    const target = this.projectList.find(({ value }: any) => value === val);
    if (target && Array.isArray(target?.children)) {
      this.pointList = target.children.map((el: any) => ({ label: el.jobName, value: el.id }));
    }
  }
  /* 处理【ServiceId】变化 */
  handleServiceIdChange(val) {
    if (this.formData.resType !== 'TABLE') return;
    const target = this.pointList.find(({ value }: any) => value === val);
    this.formData.resourceList = target?.source || '';
  }
  /* 获取item校验规则 */
  getItemRules({ prop, label }) {
    return prop === 'catalogExpression'
      ? {}
      : { required: true, message: `${this.$t('pa.placeholder.input')}${label}`, trigger: 'blur' };
  }
  /* 统一消息提示 */
  notify(message, type = 'error') {
    this.$message[type]({ message, el: this.isFullScreen ? '#bloodRelation' : '' });
  }

  handleVisibleChange(val: boolean) {
    this.visible = val;
  }
  /* 重置表单 */
  restForm() {
    this.form?.resetFields();
  }
  /* 提交表单 */
  async submit() {
    try {
      await this.form?.validate();
      let list = '';
      if (Array.isArray(this.enumsList)) {
        this.enumsList.forEach(({ prop }) => {
          if (this.formData[prop]) {
            list += this.formData[prop].replace(' ', '') + this.lineageDimensionSplit;
          }
        });
      }
      this.$emit('search', {
        serviceId: this.formData.serviceId,
        resType: this.formData.resType,
        resourceList: this.formData.resourceList || list.trim()
      });
    } catch (err) {
      this.$message.error(this.$t('pa.blood.tip9'));
    }
  }
}
</script>

<style lang="scss" scoped>
.search-config {
  padding: 20px;
  > .form {
    flex: 1;
  }
  > .el-button {
    width: 100%;
    margin-top: 20px;
  }
}
::v-deep .el-select,
::v-deep .el-input {
  width: 100%;
}
::v-deep .el-form-item.is-required {
  margin-bottom: 10px;
}
.el-form--inline {
  ::v-deep .el-form-item__label {
    width: 110px;
    float: none;
  }
}

::v-deep .el-form-item__error {
  display: none;
}
</style>
