<template>
  <bs-dialog :title="$t('pa.flow.title4')" width="30%" :visible.sync="display">
    <el-form ref="formRef" :model="formData" :rules="rules" :label-width="isEn ? '120px' : '80px'">
      <el-form-item :label="$t('pa.flow.label46')" prop="isSink">
        <el-select v-model="formData.isSink" :placeholder="$t('pa.flow.placeholder40')" style="width: 100%">
          <!-- <el-option label="真实输出" :value="true" />
          <el-option label="模拟输出" :value="false" /> -->
          <el-option v-for="el in outputOptions" :key="el.value" :label="el.label" :value="el.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="display = false">{{ $t('pa.flow.cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('pa.flow.confirm') }}</el-button>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Vue, Component, Prop, PropSync, Ref } from 'vue-property-decorator';
import ElFrom from 'bs-ui-pro/packages/form';
import cloneDeep from 'lodash/cloneDeep';
import { post } from '@/apis/utils/net';
import { isFlinkSql } from '@/common/utils';

@Component
export default class PreExecuteDialog extends Vue {
  @PropSync('show', { default: false }) display!: boolean;
  @Prop() jobData!: any;
  @Ref('formRef') readonly form!: ElFrom;

  private formData: any = { isSink: null };
  private rules: any = {
    isSink: { required: true, message: this.$t('pa.flow.placeholder40'), trigger: 'change' }
  };
  outputList: any[] = [];

  get outputOptions() {
    return [
      this.outputList.includes('MOCK_SINK') && { label: this.$t('pa.flow.label47'), value: false },
      this.outputList.includes('REAL_SINK') && { label: this.$t('pa.flow.label48'), value: true }
    ].filter(Boolean);
  }

  created() {
    this.getOutputOptions();
  }

  async getOutputOptions() {
    const params = cloneDeep(this.jobData);
    params.content = JSON.stringify(params.content);
    if (isFlinkSql(params.jobType)) {
      params.content = this.$store.getters.encrypt(params.content);
    }
    const { success, data, msg } = await post('/rs/pa/job/test/type', params);
    if (!success) return this.$tip.error(msg);
    this.outputList = Array.isArray(data) ? data : [];
  }
  async submit() {
    await this.form.validate();
    this.$emit('submit', this.formData);
    this.display = false;
  }
}
</script>
