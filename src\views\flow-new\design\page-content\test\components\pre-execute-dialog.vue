<template>
  <bs-dialog title="执行提示信息" width="30%" :visible.sync="display">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px">
      <el-form-item label="输出方式" prop="isSink">
        <el-select v-model="formData.isSink" placeholder="请选择数据输出方式" style="width: 100%">
          <el-option
            v-for="el in outputOptions"
            :key="el.value"
            :label="el.label"
            :value="el.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="display = false">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Vue, Component, PropSync, Prop, Ref } from 'vue-property-decorator';
import ElFrom from 'bs-ui-pro/packages/form';
import { post } from '@/apis/utils/net';
import { cloneDeep } from 'lodash';

@Component
export default class PreExecuteDialog extends Vue {
  @PropSync('show', { default: false }) display!: boolean;
  @Prop() jobData!: any;
  @Ref('formRef') readonly form!: ElFrom;

  private formData: any = { isSink: null };
  private rules: any = {
    isSink: { required: true, message: '请选择数据输出方式', trigger: 'change' }
  };
  outputList: any[] = [];

  get outputOptions() {
    return [
      this.outputList.includes('MOCK_SINK') && { label: '模拟输出', value: false },
      this.outputList.includes('REAL_SINK') && { label: '真实输出', value: true }
    ].filter(Boolean);
  }
  created() {
    this.getOutputOptions();
  }

  async getOutputOptions() {
    const params = cloneDeep(this.jobData);
    params.content = JSON.stringify(params.content);
    const { success, data, msg } = await post('/rs/pa/job/test/type', params);
    if (!success) return this.$tip.error(msg);
    this.outputList = Array.isArray(data) ? data : [];
  }

  async submit() {
    await this.form.validate();
    this.$emit('submit', this.formData);
    this.display = false;
  }
}
</script>
