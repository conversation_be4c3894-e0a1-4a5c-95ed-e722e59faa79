<!--
 * @Description: 流立方批查批推组件配置
 -->
<template>
  <bs-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    width="680px"
    append-to-body
    @close="closeDialog(false)"
    @confirm="handleConfirm"
  >
    <div v-loading="loading" :element-loading-text="$t('pa.flow.comLoading')" class="process-container">
      <!-- 表单 -->
      <el-collapse>
        <main-form
          ref="formRef"
          :disabled="disabled"
          :org-id="orgId"
          :form-data="formData"
          :field-list="fieldList"
          :indicator="indicator"
          @config="showConfig"
          @empty="emptyIndicator"
        />
        <el-collapse-item :title="$t('pa.flow.fieldParam')">
          <node-field-list ref="NodeFieldList" />
        </el-collapse-item>
      </el-collapse>
      <!-- 配置明细 -->
      <logic-config
        v-if="showConfigDialog"
        :title="$t('pa.flow.logicConfig', [title])"
        :show.sync="showConfigDialog"
        :form-data="formData"
        :field-list="fieldList"
        @config="configChange"
      />
    </div>
    <print-log slot="footer-left" v-model="data.printLog" :disabled="disabled" />
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Vue, Prop, Emit, Ref } from 'vue-property-decorator';
import * as _ from 'lodash';
import MainForm from './main-form.vue';
import LogicConfig from './logic-config.vue';
import { getArray, generateFormData } from './utils';
import type { FormData } from './type';
import nodeFieldList from '../components/node-field-list.vue';
import PrintLog from '../components/print-log.vue';
@Component({
  components: { MainForm, LogicConfig, nodeFieldList, PrintLog }
})
export default class BatchProcess extends Vue {
  @Prop({ default: false }) visible!: boolean; // 控制弹窗显隐
  @Prop({ default: () => ({}) }) data!: any;
  @Prop() jobData!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: false }) disabled!: boolean;
  @Ref('formRef') readonly form!: MainForm;
  private loading = false;
  private formData: FormData = generateFormData();
  private fieldList: any[] = []; //维度值
  private showConfigDialog = false; //指标配置弹窗的显隐
  private indicator = ''; //指标配置展示
  get title() {
    const { nodeName = '', componentName = '' } = this.data;
    return this.$t('pa.flow.msg288', [nodeName, componentName]);
  }
  mounted() {
    this.$nextTick(() => {
      const ref: any = this.$refs.NodeFieldList;
      if (ref) {
        ref.loadData(this.jobData, this.data, false, false, false);
      }
    });
  }
  created() {
    this.getFieldList(getArray(this.data.inputFields));
    this.formData = { ...generateFormData(), ...this.data.properties };
    this.configChange(this.formData['indicatorConfiguration']);
  }
  //弹出指标配置窗口
  showConfig() {
    //需要计算引擎、命名空间、维度
    if (this.formData['serviceAddress'] === '') {
      this.$message.error(this.$t('pa.flow.msg103'));
    } else if (this.formData['namespace'] === '') {
      this.$message.error(this.$t('pa.flow.msg104'));
    } else if (this.formData['dimensionality'] === '') {
      this.$message.error(this.$t('pa.flow.msg105'));
    } else {
      this.showConfigDialog = true;
    }
  }

  //指标配置置空
  emptyIndicator() {
    this.indicator = '';
    this.formData['indicatorConfiguration'] = [];
  }

  //指标配置修改
  configChange(data: any = []) {
    this.formData['indicatorConfiguration'] = data;
    this.indicator = data.map(({ indicatorId }: any) => `{${indicatorId}}`).join('、');
  }

  getFieldList(data: any = []) {
    this.fieldList = data.map(({ name, type }) => ({ label: name, value: name, type }));
  }

  async handleConfirm() {
    try {
      await this.form.validate();
      const ref: any = this.$refs.NodeFieldList;
      const result = ref.getJobNode();
      if (result === null) {
        return;
      }
      const nodeDto = _.cloneDeep(this.data);
      const resultOutputFields = _.filter(result.outputFields, {
        disabled: true
      });
      const outputField = {
        name: this.formData['resultFields'],
        type: 'String',
        outputable: true,
        targetable: true
      };
      nodeDto.inputFields = this.data.inputFields;
      nodeDto.outputFields = _.concat(resultOutputFields, outputField);
      nodeDto.ioProperties = `{"emitTime":0,"f0":["inputField"],"f1":["resultFields"]}`;
      nodeDto.properties = this.formData;
      this.closeDialog(true, nodeDto);
    } catch (e) {
      console.log(e);
    }
  }
  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    return { needUpdate, jobNode };
  }
}
</script>
<style lang="scss" scoped>
.process {
  &-dialog {
    ::v-deep .el-dialog {
      &__body {
        padding: 0 10px;
      }
    }
  }
}
</style>
