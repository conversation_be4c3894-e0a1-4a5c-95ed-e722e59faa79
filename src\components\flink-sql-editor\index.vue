<template>
  <codemirror
    ref="editor"
    :value="code"
    :options="options"
    :height="height"
    class="code-mirror"
    @changes="valueChange"
  />
</template>

<script>
import { codemirror } from 'vue-codemirror';
import 'codemirror/lib/codemirror.css';
import CodeMirror from 'codemirror/lib/codemirror';
/*代码高亮支持的语言*/
import 'codemirror/mode/sql/sql.js';
import 'codemirror/addon/edit/matchbrackets.js';
/*支持代码折叠*/
import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/fold/foldcode.js';
import 'codemirror/addon/fold/foldgutter.js';
import 'codemirror/addon/fold/brace-fold.js';
import 'codemirror/addon/fold/comment-fold.js';
/*自动补全*/
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/show-hint.js';
import 'codemirror/addon/hint/anyword-hint.js';
import 'codemirror/addon/hint/sql-hint.js';
/*行注释*/
import 'codemirror/addon/comment/comment.js';
/*格式化*/
import './format.js';
/*代码检查错误*/
import 'codemirror/addon/lint/lint.css';
import 'codemirror/addon/lint/lint.js';
import './flink-sql.js';

export default {
  name: 'FlinkSqlEditor',
  components: {
    codemirror
  },
  props: {
    options: Object,
    height: String,
    code: String,
    originCode: String
  },
  data() {
    return {
      codeValue: '',
      firstCome: false
    };
  },
  computed: {
    codemirror() {
      if (!this.$refs.editor.codemirror.options.readOnly && !this.firstCome) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.firstCome = true;
        this.codemirror.setOption('extraKeys', {
          a: this.completeAfter,
          b: this.completeAfter,
          c: this.completeAfter,
          d: this.completeAfter,
          e: this.completeAfter,
          f: this.completeAfter,
          g: this.completeAfter,
          h: this.completeAfter,
          i: this.completeAfter,
          j: this.completeAfter,
          k: this.completeAfter,
          l: this.completeAfter,
          m: this.completeAfter,
          n: this.completeAfter,
          o: this.completeAfter,
          p: this.completeAfter,
          q: this.completeAfter,
          r: this.completeAfter,
          s: this.completeAfter,
          t: this.completeAfter,
          u: this.completeAfter,
          v: this.completeAfter,
          w: this.completeAfter,
          x: this.completeAfter,
          y: this.completeAfter,
          z: this.completeAfter,
          '.': this.completeAfter,
          ':': this.completeAfter,
          A: this.completeAfter,
          B: this.completeAfter,
          C: this.completeAfter,
          D: this.completeAfter,
          E: this.completeAfter,
          F: this.completeAfter,
          G: this.completeAfter,
          H: this.completeAfter,
          I: this.completeAfter,
          J: this.completeAfter,
          K: this.completeAfter,
          L: this.completeAfter,
          M: this.completeAfter,
          N: this.completeAfter,
          O: this.completeAfter,
          P: this.completeAfter,
          Q: this.completeAfter,
          R: this.completeAfter,
          S: this.completeAfter,
          T: this.completeAfter,
          U: this.completeAfter,
          V: this.completeAfter,
          W: this.completeAfter,
          X: this.completeAfter,
          Y: this.completeAfter,
          Z: this.completeAfter,
          // '=': this.completeIfInTag,
          'Ctrl-Q': 'autocomplete',
          Tab: function (cm) {
            const spaces = Array(cm.getOption('indentUnit') + 1).join(' ');
            cm.replaceSelection(spaces);
          }
        });
      }
      return this.$refs.editor.codemirror;
    }
  },
  mounted() {
    this.firstCome = false;
  },
  methods: {
    valueChange(mirrorObj, data) {
      const value = this.codemirror.getValue();
      if (data[0].origin === '+delete') {
        this.codeValue = value;
      } else {
        this.codeValue = value ? value : this.originCode;
      }

      if (!value) {
        this.codemirror.setValue(this.codeValue);
      }
      this.$emit('saveCode', this.codeValue);
    },
    // 代码格式化
    autoFormat() {
      const totalLines = this.codemirror.lineCount();
      this.codemirror.autoFormatRange({ line: 0, ch: 0 }, { line: totalLines });
      this.codemirror.setSelection({ line: 0, ch: 0 }, { line: 0, ch: 0 });
    },
    completeAfter(cm) {
      cm.showHint({
        completeSingle: false
      });
      return CodeMirror.Pass;
    },
    periodCompleteAfter(cm) {
      const cur = cm.getCursor();
      const curLine = cm.getLine(cur.line);
      const end = cur.ch;
      const start = end;
      const data = {
        nowSymbol: '.',
        preSourceCode: curLine
      };
      return prompt(data).then(
        (resp) => {
          if (resp.data.success) {
            const hintList = resp.data.data.map((re) => '.' + re);
            cm.showHint({
              completeSingle: false,
              hint: () => {
                return {
                  list: hintList,
                  from: CodeMirror.Pos(cur.line, start),
                  to: CodeMirror.Pos(cur.line, end)
                };
              }
            });
          }
        },
        () => {
          this.$Message.error(this.$t('cube.scripts.recycleList.alertTip5'));
        }
      );
    },

    getValueBeforeCursor(curLine) {
      let value = '';
      for (let i = 0; i < curLine; i++) {
        value += this.codemirror.getLine(i);
      }
      return value;
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .CodeMirror {
  &-line {
    font-family: 'Microsoft YaHei' !important;
    text-align: left;
  }

  &-scroll {
    height: 300px !important;
  }
}
</style>
