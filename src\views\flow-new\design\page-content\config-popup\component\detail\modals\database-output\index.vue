<template>
  <bs-dialog
    size="large"
    :title="title"
    :visible.sync="display"
    :before-close="closeDialog"
    append-to-body
    :confirm-button="{ disabled }"
    @close="closeDialog(false)"
    @confirm="closeDialog(true)"
  >
    <!-- 表单 -->
    <el-form
      ref="formRef"
      v-loading="loading"
      :model="formData"
      :disabled="disabled"
      :label-width="labelWidth"
      class="database__container"
      :element-loading-text="$t('pa.flow.comLoading')"
    >
      <template v-for="el in renderList">
        <el-form-item
          v-if="el.show"
          :key="el.fieid"
          :label="el.label"
          :prop="el.fieid"
          :rules="el.show && el.rules ? el.rules : null"
        >
          <div class="database-item">
            <!-- select -->
            <el-select
              v-if="el.type === 'select'"
              v-model="formData[el.fieid]"
              filterable
              remote
              :remote-method="el.remotemethod"
              :clearable="el.clearable"
              :placeholder="el.placeholder || `${$t('pa.flow.placeholder21')}${el.label}`"
              :disabled="el.disabled"
              @change="handleChange($event, el.fieid)"
            >
              <el-option v-for="item in el.options" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
            <!-- bs-select -->
            <bs-select
              v-if="el.type === 'bsSelect'"
              v-model="formData[el.fieid]"
              class="database-item__select"
              :show-all="el.showAll"
              :multiple="el.multiple"
              clearable
              filterable
              collapse-tags
              :virtual-loading="el.virtualLoading"
              :options="el.options"
              :placeholder="el.placeholder || `${$t('pa.flow.placeholder21')}${el.label}`"
              @change="handleChange($event, el.fieid)"
            >
              <div slot-scope="{ item }" class="database-option">
                <!-- lable -->
                <div>
                  <span class="database-option__label">{{ item.label }}</span>
                  <bs-tag v-if="item.isPrimarykey">{{ $t('pa.flow.mainKey') }}</bs-tag>
                </div>
                <el-tooltip v-if="item.disabled" effect="light" :content="$t('pa.flow.msg309')" placement="right">
                  <i class="iconfont icon-wenhao database-icon"></i>
                </el-tooltip>
              </div>
            </bs-select>
            <!-- textarea -->
            <el-input
              v-if="el.type === 'textarea'"
              v-model="formData[el.fieid]"
              type="textarea"
              :autosize="el.autosize"
              :readonly="el.readonly"
              :placeholder="el.placeholder"
            />
            <!-- input-number -->
            <el-input-number
              v-if="el.type === 'inputNumber'"
              v-model="formData[el.fieid]"
              :min="el.min"
              :max="el.max"
              :placeholder="el.placeholder"
            />
            <!-- input -->
            <div v-if="el.type === 'input'" class="database-item__input" :data-tip="hasSpace(formData[el.fieid])">
              <el-input v-model="formData[el.fieid]" :min="el.min" :max="el.max" :placeholder="el.placeholder" />
            </div>
          </div>
          <span v-if="el.appendText" class="database-item__append">{{ el.appendText }}</span>
          <!-- tooltip -->
          <el-tooltip v-if="el.tooltip" effect="light" placement="top" :content="el.tooltip">
            <i class="database-icon el-icon-warning-outline"></i>
          </el-tooltip>
          <!-- button -->
          <el-button
            v-if="el.config"
            type="text"
            class="database-config"
            :disabled="false"
            @click="showFieldMappingDialog = true"
          >
            {{ disabled ? $t('pa.flow.view') : $t('pa.flow.config1') }}
          </el-button>
        </el-form-item>
      </template>
    </el-form>
    <field-mapping-dialog
      v-if="showFieldMappingDialog"
      type="JDBC"
      :show.sync="showFieldMappingDialog"
      :disabled="disabled"
      :res-id="formData.resId"
      :input-fields="inputFields"
      :table-mapping-fields="tableMappingFields"
      :table-name="isSingle ? formData.subName : fullDynamicTable"
      :output-fields="formData.outputFields"
      :output-columns="formData.outputColumns"
      :pre-fields="preTableFields"
      @change="handleConfigChange"
      @pre-data-change="handlePreDataChange"
    />
    <print-log slot="footer-left" v-model="printLog" :disabled="disabled" />
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Watch, Vue } from 'vue-property-decorator';
import cloneDeep from 'lodash/cloneDeep';
import Form from 'bs-ui-pro/packages/form';
import { get } from '@/apis/utils/net';
import { URL_TABLE_LIST } from '@/apis/commonApi';
import type { FormData, RenderListItem } from '../type';
import { getSqlColumn } from '@/apis/serviceApi';
import { getJdbcRes } from '@/apis/flowNewApi';
import { getClickhouseTableFields, getHudiTableFields } from '@/apis/flowNewApi';

const DYNAMIC_TABLE_APPEND = '{yyyyMMdd}';
@Component({
  components: {
    FieldMappingDialog: () => import('../components/field-mapping-dialog.vue'),
    PrintLog: () => import('../components/print-log.vue')
  }
})
export default class DatabaseOutput extends Vue {
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @PropSync('show', { type: Boolean, default: false }) display!: boolean;

  @Ref('formRef') readonly form!: Form;

  private loading = false;
  private labelWidth = this.isEn ? '200px' : '120px';
  private printLog = false; // 输出日志
  private formData: FormData = {
    jdbcType: '',
    resTitle: '', // 服务名称
    resId: '', // 服务Id
    address: '', //服务地址
    tableType: 'single', // 表匹配方式
    subName: '', // 表
    dynamicTable: '', // 动态表名
    outputFields: [], //输出字段
    outputColumns: '', // 数据库列名
    isUpsert: 'no', // 是否UPSERT
    batchSize: 100, // 批量大小
    timeout: 10, // 超时时间
    upsertFieldSet: [], // 更新字段
    upsertPrimaryKeySet: [], // 更新主键
    localPartition: 'no',
    partitionField: ''
  };

  private renderList: RenderListItem[] = [
    {
      show: true,
      label: this.$t('pa.flow.jdbcType'),
      fieid: 'jdbcType',
      rules: { required: true, message: this.$t('pa.flow.msg127'), trigger: 'change' },
      type: 'select',
      options: [],
      placeholder: this.$t('pa.flow.msg127')
    },
    {
      show: true,
      label: this.$t('pa.flow.serveName'),
      fieid: 'resId',
      rules: { required: true, message: this.$t('pa.flow.msg128'), trigger: 'change' },
      type: 'select',
      clearable: true,
      options: [],
      placeholder: this.$t('pa.flow.msg128')
    },
    {
      show: true,
      label: this.$t('pa.flow.serveAddress'),
      fieid: 'address',
      type: 'textarea',
      rules: [
        { required: true, message: this.$t('pa.flow.msg115'), trigger: 'blur' },
        { required: true, message: this.$t('pa.flow.msg115'), trigger: 'change' }
      ],
      readonly: true,
      placeholder: this.$t('pa.flow.msg115'),
      autosize: { minRows: 2 }
    },
    {
      show: true,
      label: this.$t('pa.flow.key17'),
      fieid: 'tableType',
      rules: { required: true, message: this.$t('pa.flow.msg148'), trigger: 'change' },
      type: 'select',
      options: [
        { label: this.$t('pa.flow.key18'), value: 'single' },
        { label: this.$t('pa.flow.key19'), value: 'dynamic' }
      ],
      placeholder: this.$t('pa.flow.placeholder26')
    },
    {
      show: true,
      label: this.$t('pa.flow.table'),
      fieid: 'subName',
      rules: {
        message: this.$t('pa.flow.msg149'),
        required: true,
        trigger: 'blur'
      },
      type: 'bsSelect',
      options: [],
      clearable: true,
      placeholder: this.$t('pa.flow.msg149'),
      config: true,
      virtualLoading: true,
      remotemethod: (query) => this.getTableList(query)
    },
    {
      show: false,
      label: this.$t('pa.flow.key20'),
      fieid: 'dynamicTable',
      rules: [
        {
          message: this.$t('pa.flow.msg150'),
          required: true,
          trigger: 'blur'
        },
        {
          message: this.$t('pa.flow.msg150'),
          required: true,
          trigger: 'change'
        }
      ],
      type: 'input',
      placeholder: this.$t('pa.flow.msg150'),
      tooltip: this.$t('pa.flow.msg151'),
      appendText: DYNAMIC_TABLE_APPEND,
      config: true
    },
    {
      show: true,
      label: this.$t('pa.flow.key21'),
      fieid: 'isUpsert',
      rules: {
        message: this.$t('pa.flow.msg152'),
        required: true,
        trigger: 'blur'
      },
      type: 'select',
      options: [
        {
          label: this.$t('pa.flow.yes'),
          value: 'yes'
        },
        {
          label: this.$t('pa.flow.no'),
          value: 'no'
        }
      ],
      disabled: false,
      tooltip: this.$t('pa.flow.msg153')
    },
    {
      show: false,
      label: this.$t('pa.flow.label77'),
      fieid: 'enableNullSkip',
      type: 'select',
      disabled: false,
      rules: {
        message: this.$t('pa.flow.label79'),
        required: true,
        trigger: 'blur'
      },
      options: [
        {
          label: this.$t('pa.flow.use'),
          value: 'true'
        },
        {
          label: this.$t('pa.flow.label76'),
          value: 'false'
        }
      ],
      tooltip: this.$t('pa.flow.label80')
    },
    {
      show: false,
      label: this.$t('pa.flow.label69'),
      fieid: 'upsertPrimaryKeySet',
      type: 'bsSelect',
      showAll: true,
      multiple: true,
      options: [],
      tooltip: this.$t('pa.flow.msg154'),
      rules: [{ validator: this.validatePrimaryKey, trigger: 'blur' }]
    },
    {
      show: false,
      label: this.$t('pa.flow.key22'),
      fieid: 'upsertFieldSet',
      type: 'bsSelect',
      showAll: true,
      multiple: true,
      options: [],
      tooltip: this.$t('pa.flow.msg304')
    },
    {
      show: true,
      label: this.$t('pa.flow.key23'),
      fieid: 'batchSize',
      type: 'inputNumber',
      min: 1,
      max: 2147483647,
      placeholder: this.$t('pa.flow.msg155'),
      tooltip: this.$t('pa.flow.msg156')
    },
    {
      show: true,
      label: this.$t('pa.flow.timeout'),
      fieid: 'timeout',
      type: 'inputNumber',
      min: 1,
      max: 2147483647,
      placeholder: this.$t('pa.flow.msg157'),
      tooltip: this.$t('pa.flow.msg157')
    },
    {
      show: true,
      label: this.$t('pa.flow.label68'),
      fieid: 'localPartition',
      type: 'select',
      options: [
        {
          label: this.$t('pa.flow.yes'),
          value: 'yes'
        },
        {
          label: this.$t('pa.flow.no'),
          value: 'no'
        }
      ],
      tooltip: this.$t('pa.flow.msg305')
    },
    {
      show: false,
      label: this.$t('pa.flow.key5'),
      fieid: 'partitionField',
      type: 'select',
      options: [],
      tooltip: this.$t('pa.flow.msg306'),
      rules: {
        message: this.$t('pa.flow.msg307'),
        required: true,
        trigger: 'blur'
      }
    }
  ];
  // 添加字段映射相关数据
  private fieldMappingTableData: any[] = [];
  private fieldMappingTableFields: any[] = [];
  private inputFields: any[] = [];
  private tableFields: any[] = [];
  private showFieldMappingDialog = false;
  private preTableFields: any = [];
  private tableMappingFields: any = [];
  isInit = true;
  // 服务类型及信息原始数据
  originalData: Record<string, any> = {};
  // 表字段是否存在主键
  hasPrimaryKey = false;

  get title() {
    const { nodeName = '', componentName = '' } = this.data || {};
    return this.$t('pa.flow.msg288', [nodeName, componentName]);
  }
  get isSingle() {
    return this.formData.tableType === 'single';
  }
  get fullDynamicTable() {
    return this.formData.dynamicTable + DYNAMIC_TABLE_APPEND;
  }

  @Watch('isSingle', { immediate: true })
  async handleTableTypeChange(isSingle: boolean) {
    this.changeRenderList('subName', 'show', isSingle);
    this.changeRenderList('dynamicTable', 'show', !isSingle);
    this.changeRenderList('outputFields', 'show', !isSingle);
    this.changeRenderList('outputColumns', 'show', !isSingle);
  }

  @Watch('formData.localPartition')
  handleLocalPartition(newVal) {
    if (newVal === 'yes') {
      this.changeRenderList('partitionField', 'show', true);
    } else {
      this.formData.partitionField = '';
      this.changeRenderList('partitionField', 'show', false);
    }
  }

  @Watch('formData.isUpsert')
  handleIsUpsert(newVal) {
    if (newVal === 'yes') {
      this.changeRenderList('upsertPrimaryKeySet', 'show', true);
      this.changeRenderList('upsertFieldSet', 'show', true);
      this.changeRenderList('enableNullSkip', 'show', true);
    } else {
      this.formData.upsertFieldSet = [];
      this.formData.upsertPrimaryKeySet = [];
      this.changeRenderList('upsertPrimaryKeySet', 'show', false);
      this.changeRenderList('upsertFieldSet', 'show', false);
      this.changeRenderList('enableNullSkip', 'show', false);
    }
  }

  @Watch('formData.subName')
  handlesubNameChange() {
    this.getTableField(this.formData.subName);
  }
  @Watch('formData.dynamicTable')
  handleSynamicTableChange() {
    this.getTableField(this.fullDynamicTable);
  }
  async created() {
    try {
      this.loading = true;
      /* 是否开启日志 */
      this.printLog = Boolean(this.data.printLog);
      /* 获取服务列表 */
      await this.getServiceTypeList();
      /* 处理配置信息 */
      const properties = cloneDeep(this.data.properties || {});
      this.formData = { ...this.formData, ...properties };
      // 动态表名存在进行切割
      if (!this.isSingle) {
        this.formData.dynamicTable = this.formData.dynamicTable.replace(DYNAMIC_TABLE_APPEND, '');
      }
      // 不存在服务类型 默认填充全部
      if (!this.formData.jdbcType) {
        this.formData.jdbcType = 'all';
      }
      // 获取服务列表根据服务类型
      this.getServiceList();
      /* 处理输入字段 */
      const data = Array.isArray(this.data?.inputFields) ? this.data.inputFields : [];
      this.inputFields = data.map(({ name, type }) => ({ label: name, value: name, type }));
      /*。更新输入字段options */
      this.changeRenderList('outputFields', 'options', this.inputFields);
      this.changeRenderList('partitionField', 'options', this.inputFields);
      /* 更新服务相关信息 */
      await this.handleResIdChange(this.formData.resId);
      // 预先获取字段映射所需数据，而不是等组件打开后再获取
      await this.preloadFieldMappingData();
      this.loading = false;
      this.preTableFields = {
        checkedData: cloneDeep(this.fieldMappingTableData).filter((el) => el.value),
        tableData: cloneDeep(this.fieldMappingTableData)
      };
    } catch {
      this.loading = false;
    }
  }
  // 预加载字段映射数据的方法
  async preloadFieldMappingData() {
    try {
      const tableName = this.isSingle ? this.formData.subName : this.fullDynamicTable;
      if (!tableName) return;
      await this.getFieldMappingTableFields();
      this.constructFieldMappingData();
    } catch (error) {
      console.error('Failed to preload field mapping data:', error);
    }
  }
  // 获取表格字段（与 FieldMappingDialog 中的 getFiledList 方法类似）
  async getFieldMappingTableFields() {
    const remoteApis = {
      JDBC: () =>
        getSqlColumn(this.formData.resId, this.isSingle ? this.formData.subName : this.fullDynamicTable, {
          search: '',
          pageData: null
        }),
      CLICKHOUSE: () =>
        getClickhouseTableFields(this.formData.resId, this.isSingle ? this.formData.subName : this.fullDynamicTable),
      HUDI: () => getHudiTableFields(this.formData.resId, this.isSingle ? this.formData.subName : this.fullDynamicTable)
    };
    const responseMethods = {
      JDBC: ({ tableData }) =>
        tableData.map(({ columnName, primaryKey }) => ({
          id: columnName,
          isPrimarykey: primaryKey
        })),
      CLICKHOUSE: (data) => data.map(({ value, primaryKey }) => ({ id: value, isPrimarykey: primaryKey })),
      HUDI: ({ fields = [] }) =>
        fields.map(({ fieldName, primaryKey, partition }) => ({
          id: fieldName,
          isPrimarykey: !!Number(primaryKey),
          isPartition: !!Number(partition)
        }))
    };
    const type = 'JDBC';
    const request = remoteApis[type];
    if (!request) return;
    const { data = [] } = (await request()) || {};
    this.fieldMappingTableFields = responseMethods[type](data);
    this.tableMappingFields = cloneDeep(this.fieldMappingTableFields);
  }

  // 构造字段映射数据
  constructFieldMappingData() {
    this.fieldMappingTableData = this.fieldMappingTableFields.map(({ id, isPrimarykey, isPartition }) => {
      const value = this.getValue(id, this.inputFields, this.formData.outputColumns, this.formData.outputFields);
      return {
        id,
        isPrimarykey,
        isPartition,
        value
      };
    });
  }

  getValue(id: string, inputFields: any[], outputColumns: string | string[], outputFields: string[]) {
    if (!outputColumns || !outputFields) return '';
    const temp = Array.isArray(outputColumns) ? outputColumns : outputColumns.split(',').filter(Boolean);
    const valueMap = temp.reduce((pre: any, next: string, index: number) => {
      pre[next] = outputFields[index];
      return pre;
    }, {});
    const matchedInput = inputFields.find((el: any) => el.value === valueMap[id]);
    return matchedInput ? matchedInput.value : '';
  }

  handlePreDataChange(data) {
    this.preTableFields = cloneDeep(data);
  }

  /* 获取服务类型及服务列表 */
  async getServiceTypeList() {
    const { success, data, error } = await getJdbcRes();
    if (!success) return this.$tip.error(error);
    this.originalData = cloneDeep(data);
    const options = Object.keys(data).map((key) => {
      return { value: key, label: key === 'all' ? this.$t('pa.flow.all') : key };
    });
    this.changeRenderList('jdbcType', 'options', options);
  }
  /* 获取服务列表 */
  getServiceList() {
    const { jdbcType } = this.formData;
    const options = (this.originalData[jdbcType] || []).map((item) => ({
      ...item,
      value: item.id,
      label: item.title
    }));
    this.changeRenderList('resId', 'options', options);
  }
  /* 处理服务类型变化事件 */
  handleTypeChange() {
    this.formData.resId = '';
    this.formData.address = '';
    this.getServiceList();
  }
  /* 处理服务名称变化事件 */
  async handleResIdChange(id: string) {
    if (!id) return (this.formData.address = '');
    const target = this.getRenderListItem('resId', 'options', []).find(({ value }) => value === id);
    if (!target) return;
    this.formData.resTitle = target.title;
    this.formData.address = target.address;
    await this.getTableList();
  }
  /* 获取表列表 */
  async getTableList(subName: any = null) {
    if (!this.isSingle) return;
    const { success, data, error } = await get(URL_TABLE_LIST, {
      resTitle: this.formData.resTitle,
      resId: this.formData.resId,
      orgId: this.orgId,
      subName
    });
    if (success) {
      const newData = Array.isArray(data) ? data : [];
      const options = newData.map(({ subName }) => ({ label: subName, value: subName }));
      return this.changeRenderList('subName', 'options', options);
    }
    this.$tip.error(error);
  }
  /* 获取表字段 */
  async getTableField(val) {
    if (!val) return;
    const { resId } = this.formData;
    const {
      success,
      data = { tableData: [] },
      error
    } = (await getSqlColumn(resId, val, { search: '', pageData: null })) || {};
    if (success) {
      this.hasPrimaryKey = false;
      const outputColumns = this.formData.outputColumns.split(',');
      this.tableMappingFields = cloneDeep(data.tableData).map((el) => ({
        id: el.columnName,
        isPrimarykey: el.primaryKey
      }));
      const tableFields = data.tableData.map((el) => {
        el.primaryKey && (this.hasPrimaryKey = true);
        return {
          label: el.columnName,
          value: el.columnName,
          isPrimarykey: el.primaryKey,
          disabled: !outputColumns.includes(el.columnName)
        };
      });

      this.tableFields = tableFields;
      // 初始化不清空值
      if (!this.isInit) {
        this.formData.upsertFieldSet = [];
        this.formData.upsertPrimaryKeySet = [];
      }
      this.isInit = false;
      this.changeRenderList('upsertFieldSet', 'options', tableFields);
      this.changeRenderList('upsertPrimaryKeySet', 'options', tableFields);
      return;
    }
    this.$tip.error(error);
    this.changeRenderList('upsertFieldSet', 'options', []);
    this.changeRenderList('upsertPrimaryKeySet', 'options', []);
  }
  /* 处理select修改事件 */
  handleChange(val: string, fieid: string) {
    const resetData = () => {
      this.formData.subName = '';
      this.formData.dynamicTable = '';
      this.formData.outputFields = [];
      this.formData.outputColumns = '';
    };
    if (fieid === 'jdbcType') {
      resetData();
      this.handleTypeChange();
    }
    if (fieid === 'resId') {
      resetData();
      this.handleResIdChange(val);
    }
    if (fieid === 'subName') {
      this.formData.outputFields = [];
      this.formData.outputColumns = '';
    }
    if (fieid === 'tableType') {
      resetData();
      this.getTableList();
    }
  }
  /* 修改RenderList数据 */
  changeRenderList(fieid: string, key: string, value: any) {
    const index = this.renderList.findIndex((el) => el.fieid === fieid);
    if (index > -1) {
      this.$set(this.renderList[index], key, value);
    }
  }
  /* 获取RenderList数据 */
  getRenderListItem(fieid: string, key: string, defaultValue: any = null) {
    const target = this.renderList.find((el) => el.fieid === fieid);
    return target && target[key] ? target[key] : defaultValue;
  }
  /* 字段配置完成时间 */
  handleConfigChange(fields: string[], columns: string[]) {
    this.formData.outputFields = fields;
    this.formData.outputColumns = columns.join(',');
    this.tableFields = this.tableFields.map((el) => {
      el.disabled = !columns.includes(el.value);
      return el;
    });
    this.changeRenderList('upsertFieldSet', 'options', this.tableFields);
    this.changeRenderList('upsertPrimaryKeySet', 'options', this.tableFields);
    this.formData.upsertFieldSet = this.formData.upsertFieldSet.filter((it) => columns.includes(it));
  }
  /* 是否有空格校验 */
  hasSpace(val) {
    if (typeof val === 'string' && val && val.trim().includes(' ')) {
      return this.$t('pa.flow.msg158');
    }
  }
  validateFeild() {
    return new Promise((resolve: any, reject: any) => {
      const { tableType, outputColumns, outputFields } = this.formData;
      if (tableType !== 'single') return resolve(true);
      if (outputColumns && outputFields.length > 0) return resolve(true);
      this.$tip.error(this.$t('pa.flow.msg159'));
      return reject(new Error(this.$t('pa.flow.msg159')));
    });
  }
  async closeDialog(needUpdate = false) {
    if (needUpdate === true) {
      await this.validateFeild();
      await this.form.validate();
      const jobNode = cloneDeep(this.data);
      jobNode.outputFields = cloneDeep(this.data.iutputFields);
      jobNode.properties = { ...cloneDeep(this.formData) };
      !this.isSingle && (jobNode.properties.dynamicTable = this.fullDynamicTable);
      jobNode.printLog = this.printLog;
      return this.reset({ needUpdate, jobNode });
    }
    this.reset();
  }
  reset(data: any = {}) {
    this.display = false;
    this.form.resetFields();
    this.$emit('close', data);
  }
  validatePrimaryKey(rule, value, callback) {
    if (this.formData.isUpsert === 'yes' && !this.hasPrimaryKey && this.formData.upsertPrimaryKeySet.length === 0) {
      callback(this.$t('pa.flow.msg308'));
    } else {
      callback();
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
  display: flex;
  align-items: center;
}
.database {
  &-item {
    display: inline-block;
    flex: 1;
    vertical-align: middle;

    ::v-deep .el-select,
    .el-input-number {
      width: 100%;
    }
    &__select {
      width: 100%;
    }
    &__input {
      position: relative;
      &::after {
        content: attr(data-tip);
        display: inline-block;
        position: absolute;
        top: 100%;
        left: 0;
        padding-top: 6px;
        color: #ff9e2b;
        font-size: 12px;
        line-height: 1;
      }
    }
    &__append {
      margin-left: 8px;
      color: $--bs-color-text-secondary;
    }
  }
  &-config {
    padding-left: 4px;
    box-sizing: border-box;
  }
  &-icon {
    display: inline-block;
    margin-left: 8px;
    font-size: 16px;
    color: #577690;
    cursor: pointer;
    vertical-align: middle;
  }
  &-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    &__label {
      padding: 0 10px;
    }
  }
}
</style>
