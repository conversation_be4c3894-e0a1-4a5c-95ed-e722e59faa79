<template>
  <bs-dialog
    :width="width"
    :title="title"
    :visible.sync="display"
    :before-close="closeDialog"
    append-to-body
  >
    <!-- 表单 -->
    <el-form
      ref="formRef"
      v-loading="loading"
      :model="formData"
      :disabled="disabled"
      :label-width="labelWidth"
      class="database__container"
      element-loading-text="配置加载中..."
    >
      <template v-for="el in renderList">
        <el-form-item
          v-if="el.show"
          :key="el.fieid"
          :label="el.label"
          :prop="el.fieid"
          :rules="el.show && el.rules ? el.rules : null"
        >
          <div class="database-item">
            <!-- select -->
            <el-select
              v-if="el.type === 'select'"
              v-model="formData[el.fieid]"
              filterable
              remote
              :remote-method="el.remotemethod"
              :clearable="el.clearable"
              :placeholder="el.placeholder || `请选择${el.label}`"
              @change="handleChange($event, el.fieid)"
            >
              <el-option
                v-for="item in el.options"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
            <!-- bs-select -->
            <bs-select
              v-if="el.type === 'bsSelect'"
              v-model="formData[el.fieid]"
              class="database-item__select"
              show-all
              multiple
              clearable
              filterable
              collapse-tags
              :options="el.options"
              :placeholder="el.placeholder || `请选择${el.label}`"
              @change="handleChange($event, el.fieid)"
            />
            <!-- textarea -->
            <el-input
              v-if="el.type === 'textarea'"
              v-model="formData[el.fieid]"
              type="textarea"
              :autosize="el.autosize"
              :readonly="el.readonly"
              :placeholder="el.placeholder"
            />
            <!-- input-number -->
            <el-input-number
              v-if="el.type === 'inputNumber'"
              v-model="formData[el.fieid]"
              :min="el.min"
              :max="el.max"
              :placeholder="el.placeholder"
            />
            <!-- input -->
            <div
              v-if="el.type === 'input'"
              class="database-item__input"
              :data-tip="hasSpace(formData[el.fieid])"
            >
              <el-input
                v-model="formData[el.fieid]"
                :min="el.min"
                :max="el.max"
                :placeholder="el.placeholder"
              />
            </div>
          </div>
          <!-- tooltip -->
          <el-tooltip v-if="el.tooltip" effect="light" placement="top" :content="el.tooltip">
            <i class="database-icon el-icon-warning-outline"></i>
          </el-tooltip>
          <!-- button -->
          <el-button
            v-if="el.config"
            type="text"
            class="database-config"
            :disabled="false"
            @click="showFieldMappingDialog = true"
          >
            {{ disabled ? '查看' : '配置' }}
          </el-button>
        </el-form-item>
      </template>
    </el-form>
    <field-mapping-dialog
      v-if="showFieldMappingDialog"
      :show.sync="showFieldMappingDialog"
      :disabled="disabled"
      :input-fields="inputFields"
      :table-fields="tableFields"
      :table-name="formData.subName"
      :output-fields="formData.outputFields"
      :output-columns="formData.outputColumns"
      @change="handleConfigChange"
    />
    <div slot="footer" class="database-footer">
      <el-switch
        v-model="printLog"
        active-color="#13ce66"
        inactive-color="#ff4949"
        active-text="开启日志"
        inactive-text="关闭日志"
        :disabled="disabled"
      />
      <div>
        <el-button @click="closeDialog(false)">取消</el-button>
        <el-button type="primary" @click="closeDialog(true)">确定</el-button>
      </div>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Watch, Vue } from 'vue-property-decorator';
import cloneDeep from 'lodash/cloneDeep';
import Form from 'bs-ui-pro/packages/form';
import { get, post } from '@/apis/utils/net';
import { URL_SERVICE_LIST, URL_TABLE_LIST, URL_TABLE_FIEID } from '@/apis/commonApi';
import type { FormData, RenderListItem } from './type';

@Component({
  components: {
    FieldMappingDialog: () => import('./field-mapping-dialog.vue')
  }
})
export default class DatabaseOutput extends Vue {
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @PropSync('show', { type: Boolean, default: false }) display!: boolean;

  @Ref('formRef') readonly form!: Form;

  private loading = false;
  private labelWidth = '110px';
  private width = '650px';
  private printLog = false; // 输出日志
  private formData: FormData = {
    resTitle: '', // 服务名称
    resId: '', // 服务Id
    address: '', //服务地址
    tableType: 'single', // 表匹配方式
    subName: '', // 表
    dynamicTable: '', // 动态表名
    outputFields: [], //输出字段
    outputColumns: '', // 数据库列名
    isUpsert: 'no', // 是否UPSERT
    batchSize: 100, // 批量大小
    timeout: 10 // 超时时间
  };

  private renderList: RenderListItem[] = [
    {
      show: true,
      label: '服务名称',
      fieid: 'resId',
      rules: { required: true, message: '请选择服务名称', trigger: 'change' },
      type: 'select',
      clearable: true,
      options: [],
      placeholder: '请选择服务名称'
    },
    {
      show: true,
      label: '服务地址',
      fieid: 'address',
      type: 'textarea',
      rules: [
        { required: true, message: '请输入服务地址', trigger: 'blur' },
        { required: true, message: '请输入服务地址', trigger: 'change' }
      ],
      readonly: true,
      placeholder: '请输入服务地址',
      autosize: { minRows: 2 }
    },
    {
      show: true,
      label: '表匹配方式',
      fieid: 'tableType',
      rules: { required: true, message: '请选择表匹配方式', trigger: 'change' },
      type: 'select',
      options: [
        { label: '指定唯一表', value: 'single' },
        { label: '动态表', value: 'dynamic' }
      ],
      placeholder: '指定table匹配方式，可指定唯一table或者通过时间表达式配置动态table'
    },
    {
      show: true,
      label: '表',
      fieid: 'subName',
      rules: {
        message: '请选择表，并配置映射字段信息',
        required: true,
        trigger: 'blur'
      },
      type: 'select',
      options: [],
      clearable: true,
      placeholder: '请选择表，并配置映射字段信息',
      config: true,
      remotemethod: (query) => this.getTableList(query)
    },
    {
      show: false,
      label: '动态表名',
      fieid: 'dynamicTable',
      rules: [
        {
          message: '请输入动态表名',
          required: true,
          trigger: 'blur'
        },
        {
          message: '请输入动态表名',
          required: true,
          trigger: 'change'
        }
      ],
      type: 'input',
      placeholder: '请输入动态表名',
      tooltip:
        '表名支持支持时间宏，如使用时间宏则表名表达式必须用table_{yyyyMMdd}形式，匹配到的表的字段应完全一致'
    },
    {
      show: false,
      label: '输入字段',
      fieid: 'outputFields',
      rules: {
        message: '请选择输入字段',
        required: true,
        trigger: 'blur'
      },
      type: 'bsSelect',
      options: [],
      placeholder: '输入字段，多个字段用逗号(,)分隔',
      tooltip: '即需要输入到数据库表中的对应列的字段'
    },
    {
      show: false,
      label: '数据库列名',
      fieid: 'outputColumns',
      type: 'textarea',
      placeholder:
        '数据库列名，多个列名用英文逗号(,)分隔，与输入字段一一对应，若是为空则默认置为输入字段，无需再次填写',
      tooltip: '即数据库表中的列名，与输入字段一一对应，若是为空则默认置为输入字段，无需再次填写'
    },
    {
      show: true,
      label: '是否UPSERT',
      fieid: 'isUpsert',
      rules: {
        message: '请选择是否UPSERT',
        required: true,
        trigger: 'blur'
      },
      type: 'select',
      options: [
        {
          label: '是',
          value: 'yes'
        },
        {
          label: '否',
          value: 'no'
        }
      ],
      tooltip:
        '若选择`否`，则为`insert`语句; 若选择`是`，则会根据主键进行`insert`或`update`，主键是自动获取的'
    },
    {
      show: true,
      label: '批量大小',
      fieid: 'batchSize',
      type: 'inputNumber',
      min: 1,
      max: 2147483647,
      placeholder: '每次写入数据库时的记录条数，默认100条',
      tooltip: '适当的增加批量大小可以增加数据的写入速度'
    },
    {
      show: true,
      label: '超时时间',
      fieid: 'timeout',
      type: 'inputNumber',
      min: 1,
      max: 2147483647,
      placeholder: '超过该时间，若是数据量还未积攒到指定的批量大小，则会输出，单位为`秒`',
      tooltip: '超过该时间，若是数据量还未积攒到指定的批量大小，则会输出，单位为`秒`'
    }
  ];

  private inputFields: any[] = [];
  private tableFields: any[] = [];
  private showFieldMappingDialog = false;

  get title() {
    return `${this.data?.nodeName || ''}组件配置`;
  }
  get isSingle() {
    return this.formData.tableType === 'single';
  }

  @Watch('isSingle', { immediate: true })
  async handleTableTypeChange(isSingle: boolean) {
    this.changeRenderList('subName', 'show', isSingle);
    this.changeRenderList('dynamicTable', 'show', !isSingle);
    this.changeRenderList('outputFields', 'show', !isSingle);
    this.changeRenderList('outputColumns', 'show', !isSingle);
  }

  async created() {
    try {
      this.loading = true;
      /* 是否开启日志 */
      this.printLog = Boolean(this.data.printLog);
      /* 获取服务列表 */
      await this.getServiceList();
      /* 处理配置信息 */
      const properties = cloneDeep(this.data.properties || {});
      this.formData = { ...this.formData, ...properties };
      /* 处理输入字段 */
      const data = Array.isArray(this.data?.inputFields) ? this.data.inputFields : [];
      this.inputFields = data.map(({ name, type }) => ({ label: name, value: name, type }));
      /*。更新输入字段options */
      this.changeRenderList('outputFields', 'options', this.inputFields);
      /* 更新服务相关信息 */
      await this.handleResIdChange(this.formData.resId);
      /* 获取表字段 */
      await this.getTableField();
      this.loading = false;
    } catch {
      this.loading = false;
    }
  }
  /* 获取服务列表 */
  async getServiceList() {
    const { success, data, error } = await get(URL_SERVICE_LIST, {
      orgId: this.orgId
    });
    if (success) {
      const newData = Array.isArray(data) ? data : [];
      const options = newData.map(({ id, title, address }) => {
        return {
          label: title,
          value: id,
          info: { resId: id, resTitle: title, address }
        };
      });
      return this.changeRenderList('resId', 'options', options);
    }
    this.$tip.error(error);
  }
  /* 处理服务名称变化事件 */
  async handleResIdChange(id: string) {
    if (!id) return (this.formData.address = '');
    const target = this.getRenderListItem('resId', 'options', []).find(({ value }) => value === id);
    if (target && target.info) {
      this.formData = { ...this.formData, ...target.info };
    }
    await this.getTableList();
  }
  /* 获取表列表 */
  async getTableList(subName: any = null) {
    if (!this.isSingle) return;
    const { success, data, error } = await get(URL_TABLE_LIST, {
      resTitle: this.formData.resTitle,
      resId: this.formData.resId,
      orgId: this.orgId,
      subName
    });
    if (success) {
      const newData = Array.isArray(data) ? data : [];
      const options = newData.map(({ subName }) => ({ label: subName, value: subName }));
      return this.changeRenderList('subName', 'options', options);
    }
    this.$tip.error(error);
  }
  /* 获取表字段 */
  async getTableField() {
    if (!this.isSingle || !this.formData.subName) return;
    const URL = `${URL_TABLE_FIEID}?id=${this.formData.resId}&tableName=${this.formData.subName}`;
    const { success, data, error } = await post(URL, { search: '', pageData: null });
    if (success) {
      const newData = Array.isArray(data?.tableData) ? data.tableData : [];
      this.tableFields = newData.map((el) => {
        return { id: el.columnName, isPrimarykey: el.primaryKey };
      });
      return;
    }
    this.$tip.error(error);
    this.tableFields = [];
  }
  /* 处理select修改事件 */
  handleChange(val: string, fieid: string) {
    fieid === 'resId' && this.handleResIdChange(val);
    if (fieid === 'subName') {
      this.formData.outputFields = [];
      this.formData.outputColumns = '';
      this.getTableField();
    }
    if (fieid === 'tableType') {
      this.formData.subName = '';
      this.formData.dynamicTable = '';
      this.formData.outputFields = [];
      this.formData.outputColumns = '';
      this.getTableList();
      this.formData.subName && this.getTableField();
    }
  }
  /* 修改RenderList数据 */
  changeRenderList(fieid: string, key: string, value: any) {
    const index = this.renderList.findIndex((el) => el.fieid === fieid);
    if (index > -1) {
      this.$set(this.renderList[index], key, value);
    }
  }
  /* 获取RenderList数据 */
  getRenderListItem(fieid: string, key: string, defaultValue: any = null) {
    const target = this.renderList.find((el) => el.fieid === fieid);
    return target && target[key] ? target[key] : defaultValue;
  }
  /* 字段配置完成时间 */
  handleConfigChange(fields: string[], columns: string) {
    this.formData.outputFields = fields;
    this.formData.outputColumns = columns;
  }
  /* 是否有空格校验 */
  hasSpace(val) {
    if (typeof val === 'string' && val && val.trim().includes(' ')) {
      return '文本内容有空格，请确认输入是否有误';
    }
  }
  validateFeild() {
    return new Promise((resolve: any, reject: any) => {
      const { tableType, outputColumns, outputFields } = this.formData;
      if (tableType !== 'single') return resolve(true);
      if (outputColumns && outputFields.length > 0) return resolve(true);
      this.$tip.error('请配置表对应的映射字段');
      return reject(new Error('请配置表对应的映射字段'));
    });
  }
  async closeDialog(needUpdate = false) {
    if (needUpdate === true) {
      await this.validateFeild();
      await this.form.validate();
      const jobNode = cloneDeep(this.data);
      jobNode.outputFields = cloneDeep(this.data.iutputFields);
      jobNode.properties = { ...cloneDeep(this.formData) };
      jobNode.printLog = this.printLog;
      return this.reset({ needUpdate, jobNode });
    }
    this.reset();
  }
  reset(data: any = {}) {
    this.display = false;
    this.form.resetFields();
    this.$emit('close', data);
  }
}
</script>
<style lang="scss" scoped>
.database {
  &-item {
    display: inline-block;
    width: calc(100% - 35px);
    vertical-align: middle;

    ::v-deep .el-select,
    .el-input-number {
      width: 100%;
    }
    &__select {
      width: 100%;
    }
    &__input {
      position: relative;
      &::after {
        content: attr(data-tip);
        display: inline-block;
        position: absolute;
        top: 100%;
        left: 0;
        padding-top: 6px;
        color: #ff9e2b;
        font-size: 12px;
        line-height: 1;
      }
    }
  }
  &-config {
    padding-left: 4px;
    box-sizing: border-box;
  }
  &-icon {
    display: inline-block;
    margin-left: 12px;
    font-size: 16px;
    color: #577690;
    cursor: pointer;
    vertical-align: middle;
  }
  &-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
