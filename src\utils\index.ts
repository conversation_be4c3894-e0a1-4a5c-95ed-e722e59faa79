import { login } from '@/apis/userApi';
import store from '@/store';
import sha256 from 'sha256';
import dayjs from 'dayjs';
const duration = require('dayjs/plugin/duration');
import cookie from 'js-cookie';
import Vue from 'vue';
import { BsDialog, Message } from 'bs-ui-pro';
import i18n from '@/i18n';
dayjs.extend(duration);
const beautify = require('js-beautify');

export { dayjs, sha256, beautify, cookie };

// 是否有权限
export const hasPermission = (access) => {
  return !access ? true : store.getters.authorities.includes(access);
};

export function download(data: any, fileName?: string, type?: string) {
  const blob = type ? new Blob([data.blob], { type: type }) : new Blob([data.blob]);
  const objectUrl = URL.createObjectURL(blob);
  //  创建a标签模拟下载
  let a: any = document.createElement('a');
  a.style.display = 'none';
  // 处理文件名
  a.download = fileName ? fileName : decodeURIComponent(data.fileName);
  a.href = objectUrl;
  a.click();
  a = null;
}

export const isFlinkSql = (str: string) => {
  return typeof str === 'string' ? str.includes('_SQL') : false;
};

export const isFlinkSqlFlow = ({ content }: any) => {
  if (content) {
    const { nodes } = content;
    if (Array.isArray(nodes)) {
      const [first] = nodes;
      if (first && first.type) {
        return isFlinkSql(first.type);
      }
    }
  }
  return;
};

export const hasDdMap = (content: any = {}) => {
  return safeArray(content.nodes).some(({ type }) => type === 'DD-MAP');
};

// 开发环境自动登录
export const autoLogin = async () => {
  if (process.env.NODE_ENV === 'development') {
    const password = sha256('bangsun');
    const formData = new FormData();
    formData.append('username', 'admin');
    formData.append('password', password);
    const { token } = await login(formData);
    document.cookie = `access_token=${token};`;
  }
};

// uuid function
function S4() {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
}

// id不能以纯数字开头，不能含·有$符
export const guid = () => {
  const uuid = 'n' + S4() + S4() + S4() + S4() + S4() + S4() + S4() + S4();
  return uuid.slice(0, uuid.length - 1);
};

export const hasFeatureAuthority = (code: any, dataLevel?: string) => {
  if (code === undefined) {
    return false;
  }
  // 不能修改上级数据
  let isNotParent = false;
  if (dataLevel !== undefined) {
    isNotParent = dataLevel !== 'PARENT';
  } else {
    isNotParent = true;
  }
  return isNotParent && store.getters.authorities.includes(code);
};

export const includesPro = (data = '', queryStr = '') => {
  return data.toLowerCase().includes(queryStr.toLowerCase());
};
export const safeArray = (target: any, defaultValue: any[] = []) => (Array.isArray(target) ? target : defaultValue);

export const portalAppMenu: any[] = [
  {
    icon: 'iconfont icon-yonghuquanxian2',
    index: '/portal/sysconf',
    title: () => i18n.t('pa.portal.title1'),
    accessRelation: 'or',
    access: ['SYSCONF.ORGMGR', 'SYSCONF.USERMGR.USERTAB'],
    children: [
      {
        index: '/portal/sysconf/orgMgr',
        title: i18n.t('pa.portal.title2'),
        access: 'SYSCONF.ORGMGR'
      },
      {
        index: '/portal/sysconf/userMgr',
        title: i18n.t('pa.portal.title3'),
        access: 'SYSCONF.USERMGR.USERTAB'
      },
      {
        index: '/portal/sysconf/roleMgr',
        title: i18n.t('pa.portal.title4'),
        access: 'SYSCONF.USERMGR.ROLETAB'
      }
    ]
  },
  {
    icon: 'iconfont icon-xitongpeizhi3',
    index: '/portal/sysconf/config',
    title: i18n.t('pa.portal.title5'),
    accessRelation: 'or',
    access: ['SYSCONF.GENFUNC.GENFUNC', 'SYSCONF.GENFUNC.LOGOINFO', 'SDM.SYSTEM.LOG'],
    children: [
      {
        index: '/portal/sysconf/config/genfunc',
        title: i18n.t('pa.portal.title6'),
        access: 'SYSCONF.GENFUNC.GENFUNC'
      },
      {
        index: '/portal/sysconf/config/viewConfig',
        title: i18n.t('pa.portal.title7'),
        access: 'SYSCONF.GENFUNC.LOGOINFO'
      }
    ]
  },
  {
    icon: 'iconfont icon-caozuorizhi',
    index: '/portal/sysconf/operlog',
    title: i18n.t('pa.portal.title8'),
    access: ['SDM.SYSTEM.LOG']
  }
];
// assets菜单相关信息
export const assetsAppMenu: any[] = [
  {
    icon: 'iconfont icon-data-source',
    index: '/assets',
    title: i18n.t('pa.assets.title1'),
    access: ['ASSETS.DD', 'ASSETS.JAR', 'ASSETS.FUNC', 'ASSETS.SETTING'],
    children: [
      {
        index: '/assets/dd',
        title: i18n.t('pa.assets.title2'),
        access: 'ASSETS.DD'
      },
      {
        index: '/assets/jar',
        title: i18n.t('pa.assets.title3'),
        access: 'ASSETS.JAR'
      },
      {
        index: '/assets/function',
        title: i18n.t('pa.assets.title4'),
        access: 'ASSETS.FUNC'
      },
      {
        index: '/assets/setting',
        title: i18n.t('pa.assets.title5'),
        access: 'ASSETS.SETTING'
      }
    ]
  }
];
export const microAppCfg: any[] = [
  {
    name: 'portal',
    url: '/portal/main.html',
    menu: [
      ...portalAppMenu,
      {
        index: '/portal/sysconf/userProfile',
        title: i18n.t('pa.portal.title9')
      }
    ]
  },
  {
    name: 'assets',
    url: '/assets/assets.html',
    menu: [...assetsAppMenu]
  }
];
export const getToken = () => cookie.get('access_token');

export const safeParse = (data: string, defaultValue = {}) => {
  try {
    return JSON.parse(data);
  } catch {
    return defaultValue;
  }
};
export const timeFormat = (date: any, format = 'YYYY-MM-DD HH:mm:ss') => (date ? dayjs(date).format(format) : date);
export { default as cloneDeep } from 'lodash/cloneDeep';
export { default as uniqueId } from 'lodash/uniqueId';
export { default as debounce } from 'lodash/debounce';

export const createDialog = (options, children) => {
  const props = Object.assign({ visible: true, size: 'medium' }, options);
  const attrs = {}; // 兼容bs-dialog

  return new Promise<any>((res: any, rej) => {
    const dialogC = Vue.extend({
      render(h) {
        return h(BsDialog, { props, attrs, on: { close: () => close(), confirm: (done) => confirm(done) } }, children(h));
      }
    });
    // 生成弹窗实例
    const dialogInstance = new dialogC({ store, i18n }).$mount();
    document.body.appendChild(dialogInstance.$el);

    const close = (v = false, data?) => {
      dialogInstance.$el.remove();
      dialogInstance.$destroy();
      res(data ? { success: v, data } : v);
    };
    const confirm = (done) => {
      const _done = (bol, data?) => {
        done && done();
        bol && close(true, data);
      };
      // TODO: 此处需要优化
      if (
        dialogInstance.$children[0] &&
        dialogInstance.$children[0].$slots.default &&
        (dialogInstance.$children[0].$slots.default[0] as any).componentInstance.confirm
      ) {
        (dialogInstance.$children[0].$slots.default[0] as any).componentInstance.confirm(_done);
      } else {
        close();
        res(true);
      }
    };
  });
};

export const hasServiceDetailAccess = () => {
  if (hasPermission('PA.ELE.SERVICE.VIEW_DETAIL')) return true;
  Message.warning(i18n.t('pa.notPermission') as string);
  return false;
};
