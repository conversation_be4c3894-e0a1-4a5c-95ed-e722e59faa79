import { login } from '@/apis/userApi';
import store from '@/store';
import sha256 from 'sha256';
import { menuRoutes } from '@/router';

// 是否有权限
export const hasPermission = (access) => {
  if (!access || !(store && store.state)) return true;
  const { state } = store;
  const authorities = state.userInfo.authorities || [];
  return authorities.includes(access);
};
export const CBBE_COMPUTE_MENU = {
  icon: 'iconfont icon-ccguanlipingtai',
  index: '/cc',
  title: 'CC管理平台',
  accessRelation: 'or',
  access: [],
  children: [
    {
      index: '/remoteCarStorage',
      title: '远程CAR仓库',
      access: []
    },
    {
      name: 'localCar',
      index: '/localCarStorage',
      title: '本地CAR仓库',
      access: []
    },
    {
      name: 'queue',
      index: '/queue',
      title: '队列管理',
      access: []
    },
    {
      name: 'taskManagement',
      index: '/taskManagement',
      title: '任务管理',
      access: []
    },
    {
      name: 'taskResultQuery',
      index: '/taskResultQuery',
      title: '任务结果查询',
      access: []
    }
  ]
};
// 根据路由配置信息生成左侧菜单栏
export const generateMenudata: () => MenuItem[] = () => {
  const menuData: MenuItem[] = [];
  // 获取menu跳转路径 拼接
  const joinPath = (path, basePath) => {
    if (/^\/[a-z]*/.test(path)) {
      return path;
    } else {
      return `${basePath}/${path}`;
    }
  };
  const transformRoutesToMenuData = (routes, target = [], basePath = '/') => {
    routes.forEach((item) => {
      if (item.meta && item.meta.noMenu) return;
      // 是否为左侧菜单项 && 是否用拥有对应权限
      if (
        item.meta &&
        item.meta.title &&
        hasPermission(item.meta.access) &&
        hasCustomPermission(item.meta.customAccess)
      ) {
        const {
          path,
          meta: { title, icon }
        } = item;
        const menuItem: MenuItem = {
          icon,
          index: joinPath(path, basePath),
          title
        };
        if (Array.isArray(item.children) && item.children.length) {
          menuItem.children = [];
          transformRoutesToMenuData(item.children, menuItem.children as never, menuItem.index);
        }
        target.push(menuItem as never);
      }
    });
    return target;
  };
  const result: any[] = transformRoutesToMenuData(menuRoutes, menuData as never);
  result.push(CBBE_COMPUTE_MENU);
  return result;
};

export function download(data: any, fileName?: string, type?: string) {
  const blob = type ? new Blob([data.blob], { type: type }) : new Blob([data.blob]);
  const objectUrl = URL.createObjectURL(blob);
  //  创建a标签模拟下载
  let a: any = document.createElement('a');
  a.style.display = 'none';
  // 处理文件名
  a.download = fileName ? fileName : data.fileName;
  a.href = objectUrl;
  a.click();
  a = null;
}

export const isFlinkSql = (str: string) => {
  return typeof str === 'string' ? str.includes('_SQL') : false;
};

export const isFlinkSqlFlow = ({ content }: any) => {
  if (content) {
    const { nodes } = content;
    if (Array.isArray(nodes)) {
      const [first] = nodes;
      if (first && first.type) {
        return isFlinkSql(first.type);
      }
    }
  }
  return;
};

export const flowDefaultConfig = () => {
  return {
    jobName: '',
    jobType: '',
    memo: '',
    clusterId: '', // 集群id
    clusterType: '', // 集群类型
    jobManagerMemory: 1024,
    taskManagerMemory: 1024,
    taskManagerSlotNumber: 1,
    taskManagerRequestCpu: 0.5, // task manager Cpu
    taskManagerLimitCpu: 1, // task manager Cpu爆发倍数
    taskManagerLimitMemory: 1, // task manager 内存爆发倍数
    jobManagerRequestCpu: 0.5, // job manager Cpu
    jobManagerLimitCpu: 1, // job manager Cpu爆发倍数
    jobManagerLimitMemory: 1, // job manager 内存爆发倍数
    parallelism: 1, // 并行度
    isApplyParallelism: false, // 同步到每个组件
    queue: '', // 队列
    disableOperatorChain: false, // 是否打断算子链
    enableCheckPoint: true, // 是否开启checkpoint
    delay: 10, // 固定重启延迟时间
    failureRateDelay: 10, // 失败重启延迟时间
    checkpointInterval: 60000,
    cp_timeout: 60000,
    cp_min_pause: 500,
    taskmanager_managed_frac: 0.1,
    cp_failed: 0,
    cp_unaligned: false,
    logOutputKafka: true, // 输出日志到kafka
    stateBackend: 'filesystem', // 状态后端
    restartStrategy: '', // 重启策略
    attempts: 5, // 尝试次数
    failuresPerInterval: 1, // 重启次数
    failureRateInterval: 1, // 时间间隔
    mode: 'stream', // 模式选择
    jobRunningRule: '', // 任务运行规则 一次性、间隔周期、时间周期、自定义
    startTime: '', // 上线时间
    intervalHour: 0, // 间隔小时
    intervalMinute: 0, // 间隔分钟
    repeatTime: '', // 重复时间
    cron: '', // cron表达式
    initialBackoff: 1, // 初始间隔时间
    maxBackoff: 300, // 最大间隔时间
    backoffMultiplier: 2.0, // 增长乘数
    resetBackoffThreshold: 60, // 最小稳定运行时间,
    jitterFactor: 0.1 // 振动因子
  };
};

// 开发环境自动登录
export const autoLogin = async () => {
  if (process.env.NODE_ENV === 'development') {
    const password = sha256('bangsun');
    const formData = new FormData();
    formData.append('username', 'admin');
    formData.append('password', password);
    const { token } = await login(formData);
    sessionStorage.setItem('token', token);
    document.cookie = `access_token=${token};`;
  }
};

// uuid function
function S4() {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
}

// id不能以纯数字开头，不能含·有$符
export const guid = () => {
  const uuid = 'n' + S4() + S4() + S4() + S4() + S4() + S4() + S4() + S4();
  return uuid.slice(0, uuid.length - 1);
};

export const hasCustomPermission = (access = '') => {
  const { others = {} } = store.state || {};
  if (!access || !(access in others)) return true;
  return access === 'isFuseMode' ? !others[access] : others[access];
};
export const getToken = (key = 'token') => {
  const { searchParams } = new URL(window.location.href);
  return searchParams.get(key) || '';
};
