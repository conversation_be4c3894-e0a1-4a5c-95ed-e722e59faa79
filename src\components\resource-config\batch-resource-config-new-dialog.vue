<!-- 流程设计和流程监控下批处理流程共用一套资源配置方案 -->
<template>
  <bs-dialog
    size="large"
    :title="$t('pa.resourceConfig')"
    append-to-body
    class="batch-resource-config-dialog"
    :visible.sync="display"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @confirm="handleConfirm"
  >
    <config-form v-if="componentKey" :key="componentKey" ref="configRef" :data="form" :show-info="false" />
  </bs-dialog>
</template>

<script lang="ts">
import { Vue, Component, Prop, Ref, Watch } from 'vue-property-decorator';
import { FLOW_DEFAULT_CONFIG } from './config';
@Component({
  components: {
    ConfigForm: () => import('./components/index.vue')
  }
})
export default class BatchResourceConfig extends Vue {
  @Prop({ type: Boolean, default: true }) show!: boolean;
  @Ref('configRef') readonly configRef!: any;

  private formData: any = {};
  private componentKey = Date.now();

  get form() {
    return this.formData;
  }

  get display() {
    return this.show;
  }

  set display(val: boolean) {
    this.$emit('update:show', val);
  }

  // 监听 show 属性变化，重置组件
  @Watch('show')
  onShowChange(newVal: boolean, oldVal: boolean) {
    if (newVal && !oldVal) {
      // 对话框从关闭变为打开时，重置组件
      this.resetComponent();
    }
  }

  created() {
    this.initFormData();
  }

  // 重置组件
  resetComponent() {
    this.componentKey = Date.now();
    this.initFormData();
  }

  // 初始化表单数据
  initFormData() {
    this.formData = {
      ...FLOW_DEFAULT_CONFIG(),
      orgId: this.$store.getters.orgId
    };
  }

  // 确认配置
  async handleConfirm() {
    const formData = await this.configRef.getFormData();
    this.closeDialog(formData);
  }

  closeDialog(data?: any) {
    this.$emit('close', data);
    this.$emit('update:show', false);
  }
}
</script>
