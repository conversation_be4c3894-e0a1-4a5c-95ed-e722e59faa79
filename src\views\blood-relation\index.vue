<template>
  <pro-page :title="$t('pa.menu.bloodRelation')" :fixed-header="false" :loading="pageLoading" class="relation">
    <!-- 页面头部操作按钮 -->
    <span slot="operation" class="relation__operation">
      <!-- 下载 -->
      <el-tooltip v-access="'PA.BLOOD_RELATION.DOWNLOAD'" :content="$t('pa.action.download')" effect="light" placement="top">
        <i class="iconfont icon-xiazai1" @click="download"></i>
      </el-tooltip>
      <!-- 全屏 -->
      <el-tooltip v-if="!isFullScreen" :content="$t('pa.blood.fullScreen')" effect="light" placement="top">
        <i class="iconfont icon-quanping" @click="fullScreen"></i>
      </el-tooltip>
      <!-- 退出全屏 -->
      <el-tooltip v-else :content="$t('pa.blood.exitFullScreen')" effect="light" placement="top">
        <i class="iconfont icon-tuichuquanping" @click="exitFullScreen"></i>
      </el-tooltip>
    </span>
    <!-- 左侧查询 -->
    <left-search :is-full-screen="isFullScreen" @search="rootSearch" @expand-change="handleResize" />
    <!-- 画布 -->
    <div ref="containerRef" class="relation__canvas">
      <!-- 画布展示区域 -->
      <div v-if="!isCanvasEmpty" id="g6-box"></div>
      <!-- 画布缩放操作按钮区域 -->
      <canvas-handler v-if="!isCanvasEmpty" :zoom="graphZoom" @canvas-handler="canvasHandler" />
      <!-- 画布为空时的提示文案 -->
      <p v-if="isCanvasEmpty" class="relation__canvas-text">{{ canvasEmptyTip }}</p>
    </div>
    <!-- 操作栏 -->
    <panel
      :show="panelShow"
      class="drawer-info"
      :params="panelData"
      :style="{ top: isFullScreen ? '64px' : '160px', right: isFullScreen ? 0 : '20px' }"
      :is-full-screen="isFullScreen"
      @close="panelShow = false"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Ref, ProvideReactive, Vue } from 'vue-property-decorator';
import LeftSearch from './header/index.vue';
import Panel from './panel/index.vue';
import CanvasHandler from './canvas-handler.vue';
import { fullScreen, exitFullScreen, transformToNodeData, getReqParams, tooltip } from './utils';
import G6 from '@antv/g6';
import { getNextLevelResource, getNextLevelResourceList, getSearchNode } from '@/apis/blood-relation';
import { SerachType, SourceData } from './type';
import debounce from 'lodash/debounce';

@Component({ components: { LeftSearch, Panel, CanvasHandler } })
export default class BloodRelation extends Vue {
  @Ref('containerRef') readonly container!: HTMLDivElement;
  // 页面loading
  pageLoading = false;
  isFullScreen = false;
  type = '';
  panelData: any = {};
  panelShow = false;
  graph: any = null;
  rootNodeId = '';
  // 画布是否为空
  isCanvasEmpty = true;
  canvasEmptyTip = this.$t('pa.blood.tip1');
  // 节点源数据映射关系
  nodeMaps = new Map<string, SourceData>();
  // 节点的上游节点Id合集
  nodeParentMaps = new Map<string, string[]>();
  // 节点点击
  timer: any = null;
  // 画布缩放度
  graphZoom = 1;
  // 当前选中的节点id
  selectedId = null;
  // 环上的联线（基础布局之上需要额外添加的）
  circleEdges: any[] = [];
  @ProvideReactive()
  get canUseSql() {
    return Boolean(this.$store.getters.enableSql);
  }
  created() {
    // 带有查询条件进入血缘关系页面时 显示loading
    if (this.$route.query.serviceId) {
      this.pageLoading = true;
    }
  }
  beforeDestroy() {
    this.graph && this.graph.destroy();
    window.removeEventListener('resize', this.handleResize);
  }
  // 初始化graph实例
  initGraph() {
    this.graph = new G6.Graph({
      container: 'g6-box',
      fitCenter: true,
      fitViewPadding: 20,
      renderer: 'canvas',
      // renderer: 'svg',
      plugins: [tooltip()],
      defaultNode: {
        type: 'blood-relation-node',
        // 锚点校准
        anchorPoints: [
          [0, 0.5],
          [1, 0.5]
        ]
      },
      defaultEdge: {
        type: 'cubic-horizontal',
        sourceAnchor: 1,
        targetAnchor: 0,
        style: {
          endArrow: {
            path: 'M 0,0 L 8,4 L 8,-4 Z',
            fill: '#d4dbe9'
          }
        }
      },
      layout: {
        // 使用 dagre 布局算法
        type: 'dagre',
        // 图的布局方向为从左到右(LR = Left to Right)
        rankdir: 'LR',
        // 节点之间的水平间距为 30 像素
        nodesep: 30,
        // 层与层之间的垂直间距为 130 像素
        ranksep: 130
      },
      // 交互相关
      modes: {
        // 支持的 behavior
        default: ['drag-canvas', 'scroll-canvas']
      },
      edgeStateStyles: {
        active: {
          stroke: '#377CFF',
          endArrow: { path: 'M 0,0 L 8,4 L 8,-4 Z', fill: '#377CFF' }
        }
      }
    });

    window.addEventListener('resize', this.handleResize);
    this.graph.on('canvas:click', () => {
      // 画布空白区域点击
    });
    // 监听滚轮事件
    this.graph.on(
      'wheel',
      debounce((e) => {
        this.graphZoom = this.graph.getZoom();
      }, 100)
    );
    this.graph.on('node:click', (ev: any) => {
      // 避免双击时触发单击事件
      if (!this.timer) {
        this.timer = setTimeout(() => {
          const node = ev.item.getModel(); // 被点击的节点元素
          this.panelData = this.nodeMaps.get(node.id);
          this.panelShow = true;
          // 设置当前节点为选中状态
          this.setNodeSelected(node.id);
          this.timer = clearTimeout(this.timer);
        }, 200);
      }
    });
    this.graph.on('node:dblclick', (ev: any) => {
      if (this.timer) this.timer = clearTimeout(this.timer);
      // 被点击的节点元素
      const node = ev.item.getModel();
      // 当前查询节点不跳转
      if (node.isRoot) return;
      // 动态服务不进行提奥转
      if (node.tag === 'dynamic') return;
      // 无权限不进行跳转
      if (!this.nodeMaps.get(node.id)?.hasAccess) return this.$message.warning(this.$t('pa.blood.tip2'));
      const { resType, serviceType, serviceId, serviceName, resourceList } = this.nodeMaps.get(node.id)!;
      this.$router.push({
        path: '/blood-relation',
        query: {
          resType,
          serviceType,
          serviceId,
          serviceName,
          resourceList
        }
      });
    });
    // 查找上下游节点
    const runSearch = (e, type) => {
      // 阻止冒泡
      e.propagationStopped = true;
      this.search(e.item, type);
    };
    this.graph.on('right-number:click', (e) => {
      runSearch(e, 'SINK');
    });
    this.graph.on('right-number-wapper:click', (e) => {
      runSearch(e, 'SINK');
    });
    this.graph.on('left-number:click', (e) => {
      runSearch(e, 'SOURCE');
    });
    this.graph.on('left-number-wapper:click', (e) => {
      runSearch(e, 'SOURCE');
    });
    // 收起上下游节点
    const runContract = (e, type: 'SINK' | 'SOURCE') => {
      // 阻止冒泡
      e.propagationStopped = true;
      this.contract(e.item, type);
    };
    this.graph.on('left-circle:click', (e) => {
      runContract(e, 'SOURCE');
    });
    this.graph.on('left-circle-text:click', (e) => {
      runContract(e, 'SOURCE');
    });
    this.graph.on('right-circle:click', (e) => {
      runContract(e, 'SINK');
    });
    this.graph.on('right-circle-text:click', (e) => {
      runContract(e, 'SINK');
    });
    this.graph.on('node:mouseenter', (e: any) => {
      const hasSelected = e.item.hasState('selected');
      !hasSelected && this.graph.setItemState(e.item, 'active', true);
    });
    this.graph.on('node:mouseleave', (e: any) => {
      const hasSelected = e.item.hasState('selected');
      !hasSelected && this.graph.setItemState(e.item, 'active', false);
    });
  }
  // 全屏
  async fullScreen() {
    await fullScreen(this.$el);
    this.isFullScreen = true;
  }
  // 退出全屏
  async exitFullScreen() {
    await exitFullScreen();
    this.isFullScreen = false;
  }
  // 画布导出
  download() {
    this.graph.downloadFullImage(this.$t('pa.menu.bloodRelation'), 'image/png', { backgroundColor: '#fff', padding: 20 });
  }
  // 重置画布信息
  resetGraph() {
    this.graph && this.graph.clear();
    this.nodeMaps = new Map();
    this.nodeParentMaps = new Map();
    this.circleEdges = [];
    this.graphZoom = 1;
  }
  // 根节点查询
  async rootSearch({ serviceId, resType, resourceList }) {
    this.pageLoading = true;
    this.isCanvasEmpty = false;
    this.resetGraph();
    const { data = {}, success, msg } = await getSearchNode({ serviceId, resType, resourceList });
    if (!success) {
      this.pageLoading = false;
      return this.$message.error(msg);
    }
    !this.graph && this.initGraph();
    // 查询节点 源数据存储
    this.nodeMaps.set(data.id, data);
    this.rootNodeId = data.id;
    // 初始化节点信息
    this.graph.data({ nodes: [Object.assign(transformToNodeData(data, true)[0], { isRoot: true })] });
    this.graph.render();
    const node = this.graph.findById(this.rootNodeId);
    await this.search(node, 'SOURCE', true);
    await this.search(node, 'SINK', true);
    // 设置当前查询节点选中
    this.setNodeSelected(data.id);
    // 关闭loading
    this.pageLoading = false;
  }
  /**
   * 查询展开上下游节点
   *
   * @param {node} any 当前查询的节点
   * @param {type} SINKSOURCE 节点类型
   */
  async search(node, type, isInit?) {
    this.pageLoading = true;
    const model = node.getModel();
    const searchNodeId = model.id;
    const sourceModel = this.nodeMaps.get(searchNodeId);
    const { data, success, msg } = await getNextLevelResource(getReqParams(sourceModel!, type));
    if (!success) {
      this.pageLoading = false;
      return this.$message.error(msg);
    }
    // 将当前查询节点的状态设置为展开状态
    node.update(type === 'SINK' ? { sinkExpand: true } : { sourceExpand: true });
    // 查询节点进行查询更新count
    model.isRoot && node.update(type === 'SINK' ? { sinkCount: data.length } : { sourceCount: data.length });
    // 获取当前已经存在的
    const nodes = this.graph.getNodes().map((item) => item.getModel());
    const edges = this.graph
      .getEdges()
      .map((item) => item.getModel())
      .filter(({ type }) => type === 'cubic-horizontal');
    // 获取环上的节点
    const getInCirclenNodes = (startNode, endNode) => {
      const nodeIds: string[] = [startNode];
      // 查询节点的上一级节点id
      let preId = startNode;
      let runCount = 0;
      let hasRoot = preId === this.rootNodeId;
      // 当上一级节点id 等于 结束节点 停止循环
      while (preId !== endNode && runCount < 50) {
        runCount++;
        this.graph
          .findById(preId)
          .getInEdges()
          .find((item) => {
            const id = item.getModel().source;
            if ((this.nodeParentMaps.get(id) || []).includes(endNode)) {
              preId = id;
              nodeIds.push(id);
              this.rootNodeId === id && (hasRoot = true);
            }
          });
      }
      return {
        hasRoot,
        nodeIds
      };
    };
    // 记录成环信息
    const circleInfos: { [key: string]: string[] } = {};
    // 记录环上包含查询节点 需要进行上下游查找的节点
    const searchInfos: { [key: string]: SerachType } = {};
    // 是否已经存在连线
    const isExistEdge = ({ source, target }) => {
      return [...this.circleEdges, ...edges].find((item) => source === item.source && target === item.target);
    };
    data.forEach((item) => {
      // 已经存在的节点不再次添加
      const isExist = this.nodeMaps.has(item.id);
      // 获取已经存在节点的model
      const existModel = isExist && this.graph.findById(item.id) ? this.graph.findById(item.id).getModel() : null;
      if (!isExist) {
        this.nodeMaps.set(item.id, item);
        nodes.push(transformToNodeData(item)[0]);
      }
      // 往下游查找节点
      if (type === 'SINK') {
        this.setParents(item.id, searchNodeId);
        const hasCircle = this.hasCircle(item.id);
        const edgeData = { source: searchNodeId, target: item.id };
        if (hasCircle) {
          this.circleEdges.push(edgeData);
          const { hasRoot, nodeIds } = getInCirclenNodes(searchNodeId, item.id);
          hasRoot && (circleInfos[item.id] = nodeIds);
        } else if (isExist && existModel && (existModel.x - model.x > 600 || existModel.x <= model.x)) {
          // 已存在的节点且该节点的层级不相邻 为避免改动节点所在的层级 所以额外增加连线
          this.circleEdges.push(edgeData);
        } else {
          edges.push(edgeData);
        }
      } else {
        // 往上游查找节点
        this.setParents(searchNodeId, item.id);
        const hasCircle = this.hasCircle(item.id);
        const edgeData = { source: item.id, target: searchNodeId };
        if (hasCircle) {
          this.circleEdges.push(edgeData);
          const { hasRoot, nodeIds } = getInCirclenNodes(item.id, searchNodeId);
          hasRoot && (circleInfos[item.id] = nodeIds);
        } else if (isExist && existModel && (model.x - existModel.x > 600 || model.x <= existModel.x)) {
          // 已存在的节点且该节点的层级不相邻 为避免改动节点所在的层级 所以额外增加连线
          this.circleEdges.push(edgeData);
        } else {
          edges.push(edgeData);
        }
      }
    });
    // 处理环上节点的状态 全部展开且置灰
    Object.values(circleInfos).forEach((nodeIds) => {
      nodes.forEach((item) => {
        // 标记节点存在环中
        if (nodeIds.includes(item.id)) {
          item.inCircle = true;
          // 未展开判断 现有连线数 === count 若小于需要发起请求
          if (!item.sinkExpand && item.sinkCount) {
            const outEdges = [...edges, ...this.circleEdges].filter(({ source }) => source === item.id).length;
            if (outEdges < item.sinkCount) {
              searchInfos[item.id] = 'SINK';
            }
          }
          if (!item.sourceExpand && item.sourceCount) {
            const inEdges = [...edges, ...this.circleEdges].filter(({ target }) => target === item.id).length;
            if (inEdges < item.sourceCount) {
              searchInfos[item.id] = searchInfos[item.id] === 'SINK' ? 'SOURCE,SINK' : 'SOURCE';
            }
          }
          item.sinkExpand = true;
          item.sourceExpand = true;
        }
      });
    });

    // 存在需要查询的节点 进行上下游查询
    if (Object.keys(searchInfos).length) {
      const { data: nextDatas = [] } =
        (await getNextLevelResourceList(
          Object.keys(searchInfos).map((id) => getReqParams(this.nodeMaps.get(id)!, searchInfos[id]))
        )) || {};
      nextDatas
        .sort((a, b) => Number(a.componentType === 'SINK') - Number(b.componentType === 'SINK'))
        .forEach((item: SourceData) => {
          // 上下游节点 源数据存储
          const { id, targetId, sourceId, componentType } = item;
          if (!this.nodeMaps.has(item.id)) {
            this.nodeMaps.set(item.id, item);
            nodes.push(transformToNodeData(item)[0]);
          }
          if (componentType === 'SOURCE') {
            this.setParents(targetId, id);
            const edgeData = { source: id, target: targetId };
            if (isExistEdge(edgeData)) return;
            this.hasCircle(item.id) ? this.circleEdges.push(edgeData) : edges.push(edgeData);
          }
          if (componentType === 'SINK') {
            this.setParents(id, sourceId);
            const edgeData = { source: sourceId, target: id };
            if (isExistEdge(edgeData)) return;
            this.hasCircle(item.id) ? this.circleEdges.push(edgeData) : edges.push(edgeData);
          }
        });
    }
    // edges.forEach(({ id }) => this.graph.removeItem(id));
    // 更新布局
    this.graph.changeData({ nodes, edges });
    // 更新环上的连线
    await this.addCircleEdges();
    // 设置当前查询节点高亮
    this.setNodeSelected(searchNodeId);
    !isInit && (this.pageLoading = false);
    // 存在包含查询节点的成环情况 进行提示
    if (Object.values(circleInfos).length) {
      this.$message.warning(this.$t('pa.blood.tip3'));
    }
  }
  /**
   * 渲染环上的连线
   *
   */
  addCircleEdges() {
    return new Promise<void>((res, rej) => {
      setTimeout(() => {
        this.circleEdges.forEach((edge) => {
          const { source, target } = edge;
          const sourceNode = this.graph.findById(source);
          const targetNode = this.graph.findById(target);
          const sourceModel = sourceNode.getModel();
          const targetModel = targetNode.getModel();
          // 起始锚点的坐标
          const sourceAnchor = sourceNode.getAnchorPoints()[1];
          const targetAnchor = targetNode.getAnchorPoints()[0];
          // 锚点出去多少位置开始转弯
          const offsetX = 30;
          // 连线方向
          const directionY = sourceModel.y < targetModel.y ? 'down' : 'up';
          const directionX = sourceModel.x < targetModel.x ? 'right' : 'left';
          const controlPoints: any[] = [];
          if (directionX === 'left') {
            const xRang = [sourceModel.x, targetModel.x].sort((x1, x2) => x1 - x2);
            // 连线控制拐点方向
            controlPoints.push({ x: sourceAnchor.x + offsetX, y: sourceAnchor.y });
            let offsetY = sourceAnchor.y;
            // 在起始结束和目标节点 x轴范围内的节点 避开
            this.graph.getNodes().forEach((node) => {
              const { x, y } = node.getModel();
              if (x >= xRang[0] && x <= xRang[1]) {
                if (directionY === 'down') {
                  offsetY = Math.max(y, offsetY);
                } else {
                  offsetY = Math.min(y, offsetY);
                }
              }
            });
            // 70 节点本身的高度
            offsetY = offsetY + (directionY === 'down' ? 40 + 70 : -40);
            controlPoints.push(
              ...[
                { x: sourceAnchor.x + offsetX, y: offsetY },
                { x: targetAnchor.x - offsetX, y: offsetY },
                { x: targetAnchor.x - offsetX, y: targetAnchor.y }
              ]
            );
          } else {
            controlPoints.push(
              ...[
                { x: targetAnchor.x - offsetX, y: sourceAnchor.y },
                { x: targetAnchor.x - offsetX, y: targetAnchor.y }
              ]
            );
          }
          this.graph.addItem('edge', Object.assign(edge, { type: 'polyline', style: { radius: 10 }, controlPoints }));
        });
        res();
      }, 10);
    });
  }
  /**
   * 收起上下游节点
   *
   * @param {node} any 当前节点
   * @param {type} string 节点类型
   */
  contract(node, type: 'SINK' | 'SOURCE') {
    const model = node.getModel();
    // 在环内不可以收起
    if (model.inCircle) return;
    const delEdgeIds: string[] = [];
    const delNodeIds: string[] = [];
    // 递归查询上下游节点
    const contractFn = (curNode) => {
      if (type === 'SINK') {
        // 查找下游的节点时 过滤已经进去删除节点数组中的节点（不包含查询节点的成环情况）
        const outEdges = curNode.getOutEdges().filter((edge) => !delNodeIds.includes(edge.getModel().target));
        outEdges.forEach((edge) => {
          const { id, target } = edge.getModel();
          const targetNode = this.graph.findById(target);
          const targetModel = targetNode.getModel();
          !delEdgeIds.includes(id) && delEdgeIds.push(id);
          // 目标节点包含其他的连线不删除
          if (
            targetNode.getInEdges().filter((edge) => {
              const { source, target } = edge.getModel();
              return !this.nodeParentMaps.get(source)?.includes(target);
            }).length > 1
          )
            return;
          // 作为下游节点才可以删除，避免成环情况
          if (targetModel.x > model.x) {
            !delNodeIds.includes(id) && delNodeIds.push(target);
            contractFn(targetNode);
          }
        });
      } else {
        // 查找上游的节点时 过滤已经进去删除节点数组中的节点（不包含查询节点的成环情况）
        const inEdges = curNode.getInEdges().filter((edge) => !delNodeIds.includes(edge.getModel().source));
        inEdges.forEach((edge) => {
          const { id, source } = edge.getModel();
          const sourceNode = this.graph.findById(source);
          const sourceModel = sourceNode.getModel();
          !delEdgeIds.includes(id) && delEdgeIds.push(id);
          // 目标节点包含其他的连线不删除
          // 过滤当前节点的输出连线成环，也就是输出节点也是需要删除的上游节点
          if (
            sourceNode.getOutEdges().filter((edge) => {
              const { source, target } = edge.getModel();
              return !this.nodeParentMaps.get(source)?.includes(target);
            }).length > 1
          )
            return;
          // 作为下游节点才可以删除，避免成环情况
          if (sourceModel.x < model.x) {
            !delNodeIds.includes(id) && delNodeIds.push(source);
            contractFn(sourceNode);
          }
        });
      }
    };
    contractFn(node);
    // 连线删除
    delEdgeIds.forEach((id) => {
      const item = this.graph.findById(id);
      const model = item.getModel();
      const idx = this.circleEdges.findIndex(({ source, target }) => model.source === source && model.target === target);
      if (idx > -1) this.circleEdges.splice(idx);
      this.graph.removeItem(id);
    });
    // 节点删除
    delNodeIds.forEach((id) => {
      // 清除相关联的数据
      this.nodeMaps.delete(id);
      this.nodeParentMaps.delete(id);
      Array.from(this.nodeParentMaps.entries()).forEach(([key, ids]) => {
        this.nodeParentMaps.set(
          key,
          ids.filter((pId) => pId !== id)
        );
      });
      this.graph.removeItem(id);
    });
    if (type === 'SINK') {
      node.update({ sinkExpand: false });
    } else {
      node.update({ sourceExpand: false });
    }
  }
  // 设置节点的选中状态
  setNodeSelected(id) {
    if (this.selectedId && this.graph.findById(this.selectedId)) {
      this.graph.clearItemStates(this.selectedId, 'selected');
    }
    this.selectedId = id;
    this.graph.setItemState(id, 'selected', true);
    // 设置查询节点相关连线高亮
    this.graph.getEdges().forEach((edge) => {
      const { source, target } = edge.getModel();
      this.graph.setItemState(edge, 'active', [source, target].includes(id));
    });
    // 使查询节点居中
    this.graph.focusItem(id);
    this.graph.translate(-150, -30);
  }
  // 画布缩放
  canvasHandler(type) {
    const zoomStep = 0.1;
    const currentZoom = this.graph.getZoom();
    switch (type) {
      case 'in':
        this.graph.zoomTo(currentZoom + zoomStep);
        break;
      case 'out':
        this.graph.zoomTo(currentZoom - zoomStep);
        break;
      case 'fit':
        this.graph.fitView(20);
        break;
      case 'center':
        this.graph.fitCenter();
        break;
      default:
        if (typeof type === 'number') {
          this.graph.zoomTo(type / 100);
          this.graph.fitCenter();
        }
        break;
    }
    // 重新获取缩放后的值
    this.graphZoom = this.graph.getZoom();
  }
  // 处理resize事件回调
  handleResize() {
    this.$nextTick(() => {
      const box = document.getElementById('g6-box');
      if (!box) return;
      const { width, height } = document.getElementById('g6-box')!.getBoundingClientRect() || {};
      width && height && this.graph.changeSize(width, height);
    });
  }
  // 记录节点的上游节点合集
  setParents(id, pId) {
    const unique = (arr: any[]) => [...new Set(arr)];
    // 父节点的父级id合集
    const PPIds = this.nodeParentMaps.get(pId) || [];
    this.nodeParentMaps.set(id, unique([...(this.nodeParentMaps.get(id) || []), pId, ...PPIds]));
    // 当子节点作为其他节点的父级节点时 更新其他节点的父级节点
    Array.from(this.nodeParentMaps.entries()).forEach(([key, val]) => {
      if (val.includes(id)) {
        this.nodeParentMaps.set(
          key,
          unique([...(this.nodeParentMaps.get(key) || []), ...(this.nodeParentMaps.get(id) || [])])
        );
      }
    });
  }
  // 判断该新增节点是否会成环
  hasCircle(id) {
    return (this.nodeParentMaps.get(id) || []).includes(id);
  }
}
</script>

<style lang="scss" scoped>
.relation {
  position: relative;
  width: 100%;
  height: calc(100vh - 110px);
  overflow: hidden;
  background: #fafcff;
  &__operation .iconfont {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin-left: 16px;
    width: 32px;
    height: 32px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 4px;
    border: 1px solid #dbd9d9;
  }
  .drawer-info {
    right: 20px;
  }
  ::v-deep .bs-pro-page__content {
    display: flex;
    overflow-y: auto;
    .relation__canvas {
      position: relative;
      overflow: hidden;
      flex: 1;
      > #g6-box {
        width: 100%;
        height: 100%;
      }
    }
    .relation__canvas-text {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #aaaaaa;
    }
  }
}
</style>
