<template>
  <div id="bloodRelation" ref="containerRef" class="relation__container">
    <!-- 头部 -->
    <top-header
      :is-full-screen="isFullScreen"
      :display-config.sync="displayConfig"
      @click="handleClick"
      @submit="handleSubmit"
      @search="handleSearch"
    />
    <!-- 画布 -->
    <artboard
      ref="artboardRef"
      :is-full-screen="isFullScreen"
      :display-config="displayConfig"
      @show-detail="showDetail"
    />
    <!-- 操作栏 -->
    <panel
      :show="panelShow"
      class="drawer-info"
      :type="type"
      :params="detail"
      :style="{ top: isFullScreen ? '52px' : '145px' }"
      :is-full-screen="isFullScreen"
      @close="panelShow = false"
    />
  </div>
</template>

<script lang="ts">
import { Component, Ref, ProvideReactive, Vue } from 'vue-property-decorator';
import TopHeader from './header/index.vue';
import Artboard from './artboard/index.vue';
import Panel from './panel/index.vue';
import { fullScreen, exitFullScreen } from './utils';

@Component({ components: { TopHeader, Artboard, Panel } })
export default class BloodRelation extends Vue {
  @Ref('artboardRef') readonly artboard!: Artboard;
  @Ref('containerRef') readonly container!: HTMLDivElement;

  private isFullScreen = false;
  private displayConfig: any = {
    showNodeLabel: true,
    showLineLabel: true,
    showHomologyNodes: true
  };
  private detail: any = {};
  private type = '';
  private panelShow = false;

  @ProvideReactive()
  get canUseSql() {
    return Boolean(this.$store.state?.others?.enableSql);
  }

  activated() {
    if (Object.keys(this.$route.params).length > 1) {
      this.handleSearch(this.$route.params);
    }
  }
  async handleClick(type: string) {
    if (type === 'download') return this.artboard.exportImage();
    if (type === 'fullScreen') {
      await fullScreen(this.container);
      this.isFullScreen = true;
      return;
    }
    if (type === 'exitFullScreen') {
      await exitFullScreen();
      this.isFullScreen = false;
      return;
    }
  }
  handleSubmit() {
    this.artboard.changeDisplay();
  }
  showDetail(type, info) {
    this.detail = info;
    this.type = type;
    this.panelShow = true;
  }
  handleSearch(data: any) {
    this.artboard.getGraphData(data);
  }
}
</script>

<style lang="scss" scoped>
.relation {
  &__container {
    position: relative;
    width: 100%;
    height: calc(100vh - 110px);
    overflow: hidden;
    background: #fafcff;
    .drawer-info {
      right: 20px;
    }
  }
}
</style>
