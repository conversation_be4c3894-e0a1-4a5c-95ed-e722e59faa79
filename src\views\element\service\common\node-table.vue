<template>
  <div class="bs-detail-block" style="padding-bottom: 1px">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">节点</div>
      <div class="bs-detail__header-operation">
        <el-button
          v-show="hasAuthority()"
          v-if="this.hasFeatureAuthority(this.listConf.nodeDeployAuthCode)"
          @click="handleDeploy"
        >
          部署
        </el-button>
        <el-button v-show="hasAuthority()" style="marginleft: 10px" @click="getNodeListData">
          刷新
        </el-button>
      </div>
    </div>
    <div
      class="tab-content"
      :style="{ height: nodeTableData.tableData.length ? height : '180px', marginBottom: '20px' }"
    >
      <base-table
        v-loading="nodeTableLoading"
        :table-data="nodeTableData"
        :table-config="nodeTableConfig"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>
    <term ref="Term" :visible="showTermDialog" @close="closeTermDialog" />
    <scp ref="Scp" :visible="showScpDialog" :record-id="recordId" @close="closeScpDialog" />
    <deploy ref="Deploy" :visible="showDeployDialog" @close="closeDeployDialog" />
  </div>
</template>
<script lang="ts">
import { Component, Inject, Watch } from 'vue-property-decorator';
import {
  URL_HOST_FIND_BY_TITLE,
  URL_RES_FINDBYID,
  URL_RES_NODE_LIST,
  URL_RES_NODE_DELETE,
  URL_RES_NODE_START,
  URL_RES_NODE_STOP
} from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {
    'base-table': () => import('@/components/base-table.vue'),
    term: () => import('@/components/term.vue'),
    scp: () => import('@/components/scp.vue'),
    deploy: () => import('../common/deploy.vue')
  }
})
export default class NodeTable extends PaBase {
  height = '300px';
  detailRecord: any = {};
  listConf: any = {};
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  @Inject('comListConf') comListConf;
  created() {
    this.loadData(
      this.comDetailRecord.val || {},
      this.comParams.NodeTable || {},
      this.comListConf.val || {}
    );
  }
  private nodeTableData: ITableData = {
    columnData: [],
    tableData: [],
    pageData: {
      pageSize: 10,
      currentPage: 1,
      total: 0
    }
  };
  allTableData: any = [];
  private nodeTableConfig: ITableConfig = {
    width: 200,
    columnsExtend: {
      edit: []
    }
  };
  private nodeTableLoading = false;

  private showTermDialog = false;
  private showScpDialog = false;
  private recordId = '';
  private showDeptRoleDialog = false;
  private showDeployDialog = false;

  async loadData(data: any, params: any, listConf: any) {
    this.detailRecord = data;
    this.listConf = listConf;
    this.height = '500px';
    this.handleDetail(this.$route.query.id);
  }

  @Watch('isLoad')
  watchIsLoad(val) {
    if (val) {
      this.handleDetail(this.$route.query.id);
    }
  }
  handleDetail(id: any) {
    if (!id) {
      return;
    }
    this.nodeTableConfig.columnsExtend = {
      edit: []
    };
    this.doGet(URL_RES_FINDBYID, {
      params: {
        id
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        this.detailRecord = resp.data;
        this.getNodeListData();
        if (this.detailRecord.belongType === 'SELF') {
          (this.nodeTableConfig.columnsExtend as any) = {
            edit: [
              {
                tipMessage: '启动',
                handler: this.start.bind(this),
                iconfont: 'icon-qidong',
                hasAuthority:
                  this.hasAuthority() && this.hasFeatureAuthority(this.listConf.nodeStartAuthCode)
              },
              {
                tipMessage: '停止',
                handler: this.stop.bind(this),
                iconfont: 'icon-guanbi',
                hasAuthority:
                  this.hasAuthority() && this.hasFeatureAuthority(this.listConf.nodeStopAuthCode)
              },
              {
                tipMessage: '编辑',
                handler: this.edit.bind(this),
                iconfont: 'icon-bianji',
                hasAuthority:
                  this.hasAuthority() && this.hasFeatureAuthority(this.listConf.nodeDeployAuthCode)
              },
              {
                tipMessage: '终端',
                handler: this.term.bind(this),
                iconfont: 'icon-zhongduan',
                hasAuthority:
                  this.hasAuthority() && this.hasFeatureAuthority('PA.ELE.SERVICE.HOST.TERM')
              },
              {
                tipMessage: '上传',
                handler: this.scp.bind(this),
                iconfont: 'icon-shangchuan',
                hasAuthority:
                  this.hasAuthority() && this.hasFeatureAuthority('PA.ELE.SERVICE.HOST.SCP')
              },
              {
                tipMessage: '删除',
                handler: this.delete.bind(this),
                iconfont: 'icon-shanchu',
                hasAuthority:
                  this.hasAuthority() && this.hasFeatureAuthority(this.listConf.nodeDeleteAuthCode)
              }
            ]
          };
        }
      });
    });
  }

  getNodeListData() {
    if (!this.$route.query.id) {
      return;
    }
    this.nodeTableLoading = true;
    this.doGet(URL_RES_NODE_LIST, {
      params: {
        resId: this.$route.query.id
      }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        if (!resp.data.tableData) {
          resp.data.tableData = [];
        }
        resp.data.tableData.forEach((n) => {
          n.url = n.ip + ':' + n.port;
          const rowData = _.cloneDeep(n);
          rowData.resProperty = '';
          // this.ws.next(rowData);
        });
        this.allTableData = resp.data.tableData;
        this.nodeTableData = {
          columnData: resp.data.columnData,
          tableData: resp.data.tableData.slice(0, 10),
          pageData: {
            currentPage: 1,
            pageSize: 10,
            total: resp.data.tableData.length
          }
        };
      });
      this.nodeTableLoading = false;
    });
  }
  handleDeploy() {
    (this.$refs.Deploy as any).loadData({
      resId: this.$route.query.id,
      resType: this.$route.query.resType,
      orgId: this.detailRecord.orgId
    });
    this.showDeployDialog = true;
  }
  start(row: any) {
    if (row.status !== 0) {
      this.$message.error('实例不可启动，请稍后再试');
      return;
    }
    this.$message.info('准备启动实例');
    this.nodeTableLoading = true;
    this.doPost(URL_RES_NODE_START, row).then((resp: any) => {
      this.parseResponse(resp, () => {
        setTimeout(() => {
          this.getNodeListData();
        }, 10000);
      });
      this.nodeTableLoading = false;
    });
  }
  stop(row: any) {
    if (row.status !== 1) {
      this.$message.error('实例不可停止，请稍后再试');
      return;
    }
    this.$message.info('准备停止实例');
    this.nodeTableLoading = true;
    this.doPost(URL_RES_NODE_STOP, row).then((resp: any) => {
      this.parseResponse(resp, () => {
        setTimeout(() => {
          this.nodeTableLoading = false;
          this.getNodeListData();
        }, 10000);
      });
    });
  }
  hasAuthority() {
    return this.detailRecord.dataLevelType !== 'PARENT' && this.detailRecord.belongType === 'SELF';
  }
  delete(row: any) {
    this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        this.nodeTableLoading = true;
        this.doDelete(URL_RES_NODE_DELETE, { data: row.id }).then((resp: any) => {
          this.parseResponse(resp, () => {
            this.getNodeListData();
          });
          this.nodeTableLoading = false;
        });
      })
      .catch(() => {
        return true;
      });
  }
  edit(row: any) {
    (this.$refs.Deploy as any).loadData(row);
    this.showDeployDialog = true;
  }

  term(row: any) {
    this.doGet(URL_HOST_FIND_BY_TITLE, {
      params: { title: row.hostTitle }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        const termEl: any = this.$refs.Term;
        termEl.open(resp.data.id, resp.data.ip, row.rootDir);
        termEl.visible = true;
      });
    });
  }
  scp(row: any) {
    this.doGet(URL_HOST_FIND_BY_TITLE, {
      params: { title: row.hostTitle }
    }).then((resp: any) => {
      this.parseResponse(resp, () => {
        const scpEl: any = this.$refs.Scp;
        scpEl.recordId = resp.data.id;
        scpEl.visible = true;
      });
    });
  }
  closeTermDialog() {
    this.showTermDialog = false;
  }
  closeScpDialog() {
    this.showScpDialog = false;
  }
  closeDeployDialog(needFresh: any) {
    if (needFresh === true) {
      this.getNodeListData();
    }
    this.showDeployDialog = false;
  }

  get nodeDataLen() {
    let success = 0;
    this.nodeTableData.tableData.forEach((n: any) => {
      if (n.status === 1) {
        success++;
      }
    });
    return success + '/' + this.nodeTableData.tableData.length;
  }
  handleCurrentChange(val) {
    if (this.nodeTableData.pageData) {
      this.nodeTableData.pageData.currentPage = val;
    }
    this.nodeTableData.tableData = this.allTableData.slice(10 * (val - 1), 10 * val);
  }
}
</script>
<style scoped></style>
