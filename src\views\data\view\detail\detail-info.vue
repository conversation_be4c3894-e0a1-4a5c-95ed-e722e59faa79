<template>
  <pro-grid direction="column" :gutter="20">
    <pro-grid type="info" title="基本信息">
      <!-- 表信息详情 -->
      <el-descriptions style="padding: 10px 32px">
        <el-descriptions-item label="视图名" :span="3" label-class-name="view-info-label">
          {{ chartInfo.tableName }}
        </el-descriptions-item>
        <el-descriptions-item label="中文名" :span="3" label-class-name="view-info-label">
          {{ chartInfo.tableNameCn }}
        </el-descriptions-item>
        <el-descriptions-item label="业务口径" :span="3" label-class-name="view-info-label">
          {{ chartInfo.businessExplain }}
        </el-descriptions-item>
        <el-descriptions-item label="创建人" label-class-name="view-info-label">
          {{ detailSource.createdBy }}
        </el-descriptions-item>
        <el-descriptions-item label="创建人电话">
          {{ detailSource.createdByMobile }}
        </el-descriptions-item>
      </el-descriptions>
    </pro-grid>
    <pro-grid type="info">
      <div slot="title">
        <span style="margin-right: 10px">选择表</span>
        <span>{{ detailSource.dependTableNames }}</span>
      </div>
      <div class="tab-content">
        <div class="little-title">
          <p class="tab-content__title">基础字段</p>
          <el-input
            v-model="searchValue"
            placeholder="输入字段名模糊搜索"
            style="margin-left: 10px; width: 300px"
            @input="searchField"
          >
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>
        <bs-table
          class="field-table"
          :data="showFieldList"
          :column-data="fieldColumnData"
          :column-settings="false"
          :page-data="page"
        >
          <template slot="primaryKey" slot-scope="{ row }">
            <el-switch
              v-model="row.primaryKey"
              active-value="1"
              inactive-value="0"
              :disabled="true"
            />
          </template>
          <template slot="partition" slot-scope="{ row }">
            <el-switch
              v-model="row.partition"
              active-value="1"
              inactive-value="0"
              :disabled="true"
            />
          </template>
        </bs-table>
        <p v-if="advancedFieldList.length" class="tab-content__title" style="margin-top: 20px">
          高级字段
        </p>
        <el-table
          v-if="advancedFieldList.length"
          class="advanced-field-table"
          :data="advancedFieldList"
        >
          <el-table-column prop="value" label="表字段" />
        </el-table>
      </div>
    </pro-grid>
    <pro-grid type="info" title="字段条件" tooltip="条件逻辑：字段相同是or，字段不同是and。">
      <div class="tab-content">
        <bs-table
          class="rule-field-table"
          :data="conditions"
          :column-data="ruleColumnData"
          :column-settings="false"
        >
          <div slot="condition" slot-scope="scope">条件{{ scope.$index + 1 }}</div>
        </bs-table>
      </div>
    </pro-grid>
  </pro-grid>
</template>

<script lang="ts">
import { getViewDetail } from '@/apis/dataApi';
import { Component, Vue } from 'vue-property-decorator';
import { cloneDeep } from 'lodash';
@Component({
  name: 'ViewDetailInfo',
  components: {}
})
export default class ViewAddEdit extends Vue {
  id = '';
  loading = false;
  // 视图信息
  chartInfo = {
    tableName: '',
    tableNameCn: '',
    businessExplain: ''
  };
  detailSource = {};
  // 字段条件
  conditions = [];
  // 选择的数据表
  selectedTables = [];
  // 基础字段列表
  fieldList: any[] = [];
  sourceFieldList: any[] = [];
  // 高级表字段列表
  advancedFieldList: any = [];
  // 字段分页信息
  page = {
    currentPage: 1,
    pageSize: 10,
    total: 0
  };
  // 字段表头列表
  ruleColumnData = [
    {
      label: '条件',
      value: 'condition'
    },
    {
      label: '判断字段',
      value: 'fieldName'
    },
    {
      label: '字段类型',
      value: 'fieldType'
    },
    {
      label: '操作符',
      value: 'operator'
    },
    {
      label: '值',
      value: 'fieldValue'
    }
  ];
  // 搜索字段
  searchValue = '';
  allTableData: any = [];
  //搜索
  searchField(val) {
    if (!this.allTableData.length) {
      this.allTableData = cloneDeep(this.fieldList);
    }
    this.searchValue = val;
    if (val) {
      this.fieldList = this.allTableData.filter((item) => {
        return item.fieldName && item.fieldName.includes(val);
      });
    } else {
      this.fieldList = cloneDeep(this.allTableData);
    }
    this.page.currentPage = 1;
    this.page.total = this.fieldList.length;
  }
  created() {
    // 初始信息获取
    this.id = (this.$route.query.id as string) || '';
    // 存在id 获取详情
    if (this.id) this.getDetail();
  }
  // 基础字段表头
  get fieldColumnData() {
    const baseColumnData = [
      {
        label: '字段名',
        value: 'fieldName'
      },
      {
        label: '中文名',
        value: 'fieldNameCn'
      },
      {
        label: '字段类型',
        value: 'fieldType'
      },
      {
        label: '主键',
        value: 'primaryKey'
      },
      {
        label: '分区',
        value: 'partition'
      },
      {
        label: '业务口径',
        value: 'businessExplain'
      }
    ];
    return (this.resType === 'HBASE' ? [{ label: '列簇', value: 'columnFamily' }] : []).concat(
      baseColumnData
    );
  }
  // 服务类型
  get resType() {
    return (this.selectedTables[0] ? this.selectedTables[0][0] : '').toUpperCase();
  }
  // 用于显示前端分页展示的数据
  get showFieldList() {
    const { currentPage, pageSize } = this.page;
    const start = (currentPage - 1) * pageSize;
    const end = currentPage * pageSize;
    return this.fieldList.slice(start, end);
  }
  async getDetail() {
    this.loading = true;
    const { data = {} } = await getViewDetail({ id: this.id });
    this.detailSource = Object.freeze(data);
    this.setChartInfo(data);
    // 获取选中的数据表
    this.selectedTables = JSON.parse(data.dependTableInfo || '[]');
    // 获取字段列表
    const { baseFieldInfo = '', advanceFieldInfo = '' } = data || {};
    const fields: any = JSON.parse(baseFieldInfo || '[]');
    this.fieldList = fields;
    this.sourceFieldList = [...fields];
    this.advancedFieldList = JSON.parse(advanceFieldInfo || '[]');
    this.page.total = fields.length;
    // 获取字段条件
    this.conditions = JSON.parse(data.viewCondition || '[]');
    // 回传sql代码
    this.$emit('get-sql', data.sqlContent);
    this.loading = false;
  }
  // 视图基本信息
  setChartInfo(data) {
    this.chartInfo.tableName = data.viewName;
    this.chartInfo.tableNameCn = data.viewNameCn;
    this.chartInfo.businessExplain = data.businessExplain;
  }
}
</script>

<style scoped lang="scss">
.tab-content {
  width: 100%;
  border-top: 1px solid #f1f1f1;
  padding: 20px 25px;
}
.field-table {
  border: 1px solid $--bs-color-border-lighter;
}
.advanced-field-table {
  border: 1px solid $--bs-color-border-lighter;
  border-bottom: none;
  width: 100%;
  max-height: 400px;
  overflow: auto;
}
.rule-field-table {
  border: 1px solid #f1f1f1;
  border-bottom: none;
}
::v-deep .view-info-label {
  width: 70px;
  justify-content: flex-end;
}
.little-title {
  display: flex;
  align-items: center;
  word-break: break-all;
  margin-bottom: 10px;
}
</style>
