<template>
  <div style="position: relative">
    <bs-dag
      ref="dag"
      class="dag-style"
      :disabled="canvasDisabled"
      :nodes="dagNodes"
      :edges="dagEdges"
      :node-options="nodeOptions"
      :edge-options="edgeOptions"
      :port-options="portOptions"
      :context-menu="contextMenu"
      :connect-validate="connectValidateMethod"
      @context-menu-click="handleContextMenuClick"
      @graph-ready="handleGraphReady"
      @blank-click="handleBlankClick"
      @node-add="handleNodeAdd"
      @node-remove="handleNodeRemove"
      @node-copy="handleNodeCopy"
      @node-move="handleNodeMove"
      @edge-add="handleEdgeAdd"
      @edge-remove="handleEdgeRemove"
    >
      <div
        slot-scope="{ node }"
        :class="['dag-node', node.selected ? 'is-active' : '', node.status === 2 ? 'is-error' : '']"
        @click="handleNodeClick(node)"
        @dblclick="handleNodeDblClick(node)"
      >
        <el-tooltip placement="bottom" effect="light" :open-delay="1000" popper-class="dag-node__left">
          <div class="dag-node__left">
            <div class="dag-node__icon">
              <img
                v-if="componentIconMap[node.className]"
                :src="getComImg(componentIconMap[node.className])"
                width="20"
                height="20"
              />
            </div>
            <div class="dag-node__con">
              <span class="dag-node__name">{{ node.nodeName }}</span>
              <span class="dag-node__num">{{ node.parallelism }}</span>
            </div>
          </div>
          <el-descriptions slot="content" :column="1" style="width: 300px">
            <el-descriptions-item :label="$t('pa.flow.component1')">{{ node.componentName }}</el-descriptions-item>
            <el-descriptions-item :label="$t('pa.blood.nodeName')">{{ node.nodeName }}</el-descriptions-item>
            <el-descriptions-item :label="$t('pa.flow.bingxingdu')">{{ node.parallelism }}</el-descriptions-item>
          </el-descriptions>
        </el-tooltip>
        <el-tooltip
          :content="node.msg || getNodeStatusText(node.status, node.msg)"
          placement="top"
          effect="light"
          popper-class="dag-node__tooltip"
        >
          <svg-icon class="dag-node__status" :name="getNodeStatus(node.status)" style="width: 16px; height: 16px" />
        </el-tooltip>
      </div>
    </bs-dag>
    <el-popover v-if="inputOutputLogSetVisible" class="canvas-log-set" trigger="hover" width="350" placement="bottom-start">
      <span class="canvas-log-set__title">{{ $t('pa.flow.msg277') }}</span>
      <p class="canvas-log-set__content">
        {{ $t('pa.flow.msg278') }}
      </p>
      <span class="bs-table_td--operator" @click="handleOpenOrClose(true)">{{ $t('pa.flow.allOpen') }}</span>
      <span class="bs-table_td--operator" @click="handleOpenOrClose(false)">{{ $t('pa.flow.allClose') }}</span>
      <el-button-group slot="reference" class="canvas-log-set__button">
        <el-button icon="iconfont icon-shurushuchuzujiankaiguan" />
      </el-button-group>
    </el-popover>
    <div v-if="isEmpty" class="empty-canvas">{{ $t('pa.flow.msg60') }}</div>
    <!-- 错误节点查找 -->
    <div v-if="errorNodesPosition.length" class="canvas-error-info">
      <i class="el-icon-warning"></i>
      <span class="canvas-error-info__title">{{ $t('pa.flow.msg61') }}</span>
      <span class="canvas-error-info__count"> {{ currentErrorNodeIdx + 1 }}/{{ errorNodesPosition.length }} </span>
      <i :class="arrowUpClass" @click="handleArrowUpClick"></i>
      <i :class="arrowBottomClass" @click="handleArrowBottomClick"></i>
    </div>
    <!-- 节点引用关系 -->
    <NodeRelations v-if="visibleNodeRelations" :visible.sync="visibleNodeRelations" :data="dataForNodeRelations" />
    <!-- 节点自动更新数据弹窗 -->
    <NodeAutoUpdateDialog
      v-if="visibleNodeUpdate"
      :visible.sync="visibleNodeUpdate"
      :data="nodeUpdateDatasForDialog"
      @submit="handleUpdateSubmit"
      @cancel="handleUpdateCancel"
    />
  </div>
</template>

<script lang="ts">
import { getComponentDataGlobalRef } from '@/apis/flowNewApi';
import { Component, Vue, Prop, Ref } from 'vue-property-decorator';
import { ChangedNodeInfo, DagEdge, DagNode, extraOriginPoint, OriginNode, OriginChangedFields } from './interface';
import CanvasStore, {
  checkFormItemRequired,
  checkFormItemVisable,
  getComponentProperties,
  getComponentType,
  getMaxNodeCount,
  isEmptyArray,
  isNewComponent
} from './service';
import NodeRelations from './components/node-relations.vue';
import NodeAutoUpdateDialog from './components/node-auto-update-info/index.vue';
import { cloneDeep } from 'lodash';
import { CheckType } from '../../interface/index';
import nodeValidate from './node-validate-service';
import NodeUpdateInfo, {
  filterOutputFields,
  generatePoints,
  isFieldschanged,
  setChangeInfoMsg,
  setTargetChangedFields,
  updateInputAndOutpyFields,
  updateUseInputFields,
  isFieldsEqual,
  fieldCheckForConnect,
  returnFieldChangeInfo,
  mergeSourceChangedFields,
  updateNodeData,
  appendToNodeUpdateData,
  isFieldsSortingChanged,
  FullInputAndOutputComponents
} from './field-validate-service';
import { getErrorInfos } from '../../flow-list/service';
const tansferSvgColor = (img) => {
  // png格式直接返回
  if (img.includes('data:image/png;base64')) return img;
  img = img.replace('data:image/svg+xml;base64,', '');
  img = decodeURIComponent(escape(window.atob(img)));
  const svgImg = img.replace(/fill="#[0-9a-zA-Z]{6}"/, 'fill="#ffffff"');
  return 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgImg)));
};
@Component({
  name: 'FlowCanvas',
  components: { NodeRelations, NodeAutoUpdateDialog }
})
export default class FlowCanvas extends Vue {
  @Prop() content!: any;
  @Prop() flowStatus!: string;
  @Prop() flowId!: string;
  @Prop() flowType!: string;
  @Ref('dag') dag: any;
  // 传入dag组件的数据
  dagNodes: DagNode[] = [];
  dagEdges: DagEdge[] = [];
  canvasStore: any = {};
  // 节点配置
  nodeOptions = { width: 220, height: 42 };
  // 连线样式配置
  edgeOptions = { color: '#54C958' };
  // 连接点配置
  portOptions = { width: 10, borderColor: '#C2D0F0', shape: 'circle' };
  // 节点引用引用关系弹窗
  visibleNodeRelations = false;
  dataForNodeRelations: Base = {};

  // 当前激活的节点 点击画布清空激活节点
  activeNode = null;

  // 节点自动更新信息汇总弹窗
  visibleNodeUpdate = false;
  // 当前操作一次需要更新的数据 主要用于弹窗展示
  nodeUpdateDatasForDialog: ChangedNodeInfo[] = [];
  // 记录多次校验的更新数据合集
  nodeUpdateDatas: ChangedNodeInfo[] = [];
  // 当前错误节点id的索引
  currentErrorNodeIdx = 0;
  // 记录错误节点id及位置
  errorNodesPosition: { id: string; position: number[] }[] = [];

  clickCount = 0;
  clickTimer: any = null;
  get isFlinkSql() {
    return this.flowType === 'FLINK_SQL';
  }
  get isEmpty() {
    return this.canvasStore && this.canvasStore.nodes && this.canvasStore.nodes.length === 0;
  }
  // 节点右击菜单
  get contextMenu() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _this: any = this;
    return [
      {
        label: this.$t('pa.flow.nodeDetail'),
        value: 'Detail'
      },
      {
        label: this.$t('pa.flow.del'),
        value: 'DelNode'
      },
      {
        label: this.$t('pa.flow.copy'),
        value: 'CopyNode',
        dataHandler(data, nodes) {
          const max = getMaxNodeCount(nodes);
          data.nodeName = data.nodeName.replace(/\d+$/g, max + 1);
          _this.$emit('content-change', true);
        }
      },
      {
        label: this.$t('pa.flow.relation'),
        value: 'Relationship',
        show(node) {
          const data = _this.getNodeData(node.id);
          if ((data.type && ['FILTER', 'MAP'].includes(data.type)) || _this.isFlinkSql) {
            return false;
          } else {
            return true;
          }
        }
      }
    ];
  }
  // 画布是否可操作
  get canvasDisabled() {
    return this.flowStatus !== 'DEV';
  }
  // 组件配置
  get componentPropertiesMaps() {
    return getComponentProperties(this.$store.getters.componentList);
  }
  // 查看上一个错误箭头图标
  get arrowUpClass() {
    return ['el-icon-caret-top', this.currentErrorNodeIdx === 0 ? 'is-disabled' : ''];
  }
  // 看下一个错误箭头图标
  get arrowBottomClass() {
    return ['el-icon-caret-bottom', this.currentErrorNodeIdx === this.errorNodesPosition.length - 1 ? 'is-disabled' : ''];
  }
  /* 是否为流程监控 */
  get isMonitor() {
    return this.$route.name === 'flowMonitorDetail';
  }

  // 是否展示批量设置组件输入输出日志开关（流程为开发状态且画布节点数量>=1时展示）
  get inputOutputLogSetVisible() {
    return (
      this.$route.name === 'flowNew' &&
      this.flowStatus === 'DEV' &&
      this.canvasStore.nodes &&
      this.canvasStore.nodes.length >= 1
    );
  }

  get componentIconMap() {
    return this.$store.getters.componentIconMap;
  }
  // 获取组件对应图标
  getComImg(img) {
    return tansferSvgColor(img);
  }
  // 开启/关闭日志
  handleOpenOrClose(isOpen: boolean) {
    const data = this.getContent();
    data.nodes.forEach((el) => {
      el.printLog = isOpen;
      this.updateNodeData(el.nodeId, el);
    });
    this.$message.success(
      `${this.$t('pa.flow.all')}${isOpen ? this.$t('pa.flow.open') : this.$t('pa.flow.close')}${this.$t('pa.home.success')}`
    );
  }

  // 画布渲染 手动进行渲染
  public renderCanvas() {
    this.dag && this.dag.removeNodeAndEdge();
    this.initCanvasStore();
    this.$emit('content-change', false);
    this.$nextTick(() => {
      this.dag.initNodeAndEdge();
      this.dag.resetGraphZoom();
      // 去除画布渲染结束 节点默认激活的功能
      this.dag.removeActiveNode();
      if (getErrorInfos()[this.flowId]) {
        const { errorInfo } = JSON.parse(getErrorInfos()[this.flowId]) || [];
        errorInfo && this.updateNodeStatus(JSON.parse(errorInfo || '[]'));
        this.$emit('removeErrorInfo', this.flowId);
      }
      this.getErrorNodesPosition();
    });
  }
  initCanvasStore() {
    this.canvasStore = new CanvasStore(cloneDeep(this.content), this.canvasDisabled);
    const { dagNodes, dagEdges } = this.canvasStore.convertToDag();
    this.dagNodes = dagNodes;
    this.dagEdges = dagEdges;
    // 画布生成完后更新一下连线id
    setTimeout(() => {
      this.canvasStore.updateEdgeData(this.dag.getEdges());
    }, 10);
  }
  // 画布加载完成后通知组件拖拽目标
  handleGraphReady(editor, graph) {
    this.$emit('canvas-ready', editor, graph);
  }
  handleContextMenuClick(type: 'Relationship' | 'Detail', node: DagNode) {
    const data = this.getNodeData(node.id);
    console.log(data);
    type === 'Relationship' ? this.viewRelationship(data) : this.openNodeDetail(data);
  }
  // 画布空白处点击
  handleBlankClick() {
    this.activeNode = null;
    this.$emit('canvas-active-change', null);
  }

  // 获取节点状态
  getNodeStatus(status = 0) {
    if (this.isMonitor) return 'chakanjiankongshuju';
    return {
      0: 'weipeizhi',
      1: 'yipeizhi',
      2: 'peizhicuowu',
      3: 'ddError'
    }[status];
  }
  // 获取节点状态对应文本
  getNodeStatusText(status = 0, msg = '') {
    if (this.isMonitor) return this.$t('pa.flow.view3');
    return {
      0: this.$t('pa.flow.notConfig'),
      1: this.$t('pa.flow.configured'),
      2: this.$t('pa.flow.configErr'),
      3: msg
    }[status];
  }
  getNodeData(id) {
    return this.canvasStore.getNodeData(id);
  }
  // 查看节点的引用关系
  async viewRelationship(data) {
    const {
      success,
      error,
      data: resData
    } = (await getComponentDataGlobalRef({
      componentId: data.componentId,
      nodeId: data.nodeId,
      jobId: this.flowId
    })) || {};
    if (success) {
      this.dataForNodeRelations = resData;
      this.visibleNodeRelations = true;
    } else {
      this.$tip.warning(error);
    }
  }
  // 打开节点详情
  openNodeDetail(data) {
    // 用于判断编辑后的节点名称是否重名
    const otherNodeNames = this.dag
      .getNodes()
      .filter((item) => item.id !== data.nodeId)
      .map((item) => item.nodeName);
    this.$emit('node-detail', { nodeName: data.nodeName, otherNodeNames, ...data });
  }
  // 处理节点点击
  handleNodeClick(node) {
    this.clickCount++;
    if (this.clickCount === 1) {
      this.clickTimer = setTimeout(() => {
        this.activeNode = this.getNodeData(node.id);
        this.$emit('canvas-active-change', this.activeNode);
        this.clickCount = 0;
      }, 500);
    }
  }
  // 处理节点双击事件
  handleNodeDblClick(node) {
    clearTimeout(this.clickTimer);
    this.clickCount = 0;
    // 是否存在自动更新的数据
    const updateInfos = this.nodeUpdateDatas.filter((item) => item.nodeId === node.id);
    const nodeData = cloneDeep(this.getNodeData(node.id));
    if (Array.isArray(updateInfos) && updateInfos.length) {
      updateInfos.forEach((data) => {
        const { targetChangedFields } = data;
        updateNodeData(nodeData, targetChangedFields);
      });
    }
    this.$emit('node-config', nodeData);
  }
  // 处理节点拖拽新增
  handleNodeAdd(node) {
    console.log('handleNodeAdd');
    // 默认给节点赋值未配置状态
    this.updateNodeStatus({ id: node.id, status: 0 });
    this.canvasStore.addNodeData(cloneDeep(node), this.flowId);
    this.$emit('content-change', true);
  }
  // 处理节点复制后新增
  handleNodeCopy(node, copyNode) {
    this.updateNodeStatus({ id: node.id, status: 0 });
    this.canvasStore.addNodeData(cloneDeep(Object.assign(node, { options: this.getNodeData(copyNode.id) })), this.flowId);
    this.$emit('content-change', true);
  }
  // 处理节点删除
  handleNodeRemove(nodeId) {
    const delNode = this.getNodeData(nodeId);
    if (JSON.stringify(delNode) === '{}') return;
    (delNode.pointOut || []).forEach(({ uuid }) => {
      const connectedEdges = this.canvasStore.getEdgesByPortId(uuid, 'OUT');
      connectedEdges.forEach((edge) => {
        this.clearTargetInputFields(edge.endNode, edge.endNodePoint);
      });
    });
    this.canvasStore.updateEdgeData(this.dag.getEdges());
    this.canvasStore.removeNode(nodeId);
    // 若存在节点更新信息 则删除
    this.nodeUpdateDatas = this.nodeUpdateDatas.filter((i) => i.nodeId !== nodeId);
    this.$emit('content-change', true);
  }
  // 处理节点连线删除
  handleEdgeRemove(edge) {
    // 删除下游输入端点的params
    if (edge.target) {
      this.clearTargetInputFields(edge.target.cell, edge.target.port);
      // 删除连线数据
      this.canvasStore.delEdgeData(edge.source.cell, edge.target.cell);
    }
    this.$emit('content-change', true);
  }
  // 处理新增连线
  handleEdgeAdd({ id, source = { cell: '', port: '' }, target = { cell: '', port: '' } }) {
    // 更新canvasStore中edge的数据
    this.canvasStore.addEdge({ id, source, target });
    const sourceNode = this.getNodeData(source.cell);
    const targetNode = this.getNodeData(target.cell);
    // 更新端点上的数据并且过滤outputable不为true的字段
    const data = sourceNode.outputFields.filter((item) => item.outputable);
    this.canvasStore.updatePortData(source.cell, source.port, 'OUT', data);
    const connectPoints = this.canvasStore.getEdgesByPortId(targetNode.pointIn[0].uuid, 'IN');
    // 更新下游节点的输入字段
    // 当同个链接桩有其他连线 不需要重新赋值
    // 因为前置条件相同输出数据的端点 或者节点为空 可链接
    // 未避免sourcePoint的输出数据为空将targetPoint进行覆盖 此处进行判断
    if (connectPoints.length === 1) {
      this.canvasStore.updatePortData(target.cell, target.port, 'IN', data);
      targetNode.pointIn.length === 1 && (targetNode.inputFields = data);
    }
    this.$emit('content-change', true);
  }
  // 处理节点移动
  handleNodeMove(node, position) {
    const _node = this.errorNodesPosition.find((i) => i.id === node.id);
    if (_node) {
      _node.position = position;
    }
    this.$emit('content-change', true);
  }
  // 连线删除或者节点删除 清除下游节点的输入数据
  clearTargetInputFields(targetId, targetPointId) {
    if (!targetId || !targetPointId) return;
    const node = this.getNodeData(targetId);
    const portIndex = node.pointIn.findIndex((item) => item.uuid === targetPointId);
    if (this.canvasStore.getEdgesByPortId(targetPointId, 'IN').length === 1) {
      // 当前输入端点只有一条连线时才进行清除数据
      node.pointIn[portIndex].data = null;
      // 当前节点只有一个输入端点时 清除节点的输入字段
      node.pointIn.length === 1 && (node.inputFields = []);
    }
    // TODO:join组件后续需要在数据层面进行兼容
    if (node.type === 'JOIN') {
      node[portIndex === 0 ? 'leftStartNodeId' : 'rightStartNodeId'] = null;
    }
  }
  // 字段自动更新信息弹窗确认回调  更新节点数据
  handleUpdateSubmit(data: ChangedNodeInfo[]) {
    const ids: string[] = [];
    data.forEach(({ nodeId, portId, targetChangedFields }) => {
      ids.push(nodeId + portId);
      const node = this.getNodeData(nodeId);
      updateNodeData(node, targetChangedFields);
      this.canvasStore.updateOutputFieldsAndTargetInputFields(node);
      this.updateNodeStatus({ id: node.nodeId, status: 1 });
    });
    this.nodeUpdateDatas = this.nodeUpdateDatas.filter((i) => !ids.includes(i.nodeId + i.portId));
  }
  // 字段自动更新信息弹窗取消回调 设置节点错误信息
  handleUpdateCancel() {
    this.setNodeStatusByUpdateInfo();
  }
  // 是否可进行连线的校验方法
  connectValidateMethod({ source, target }) {
    const sourceNode = this.getNodeData(source.cell);
    const targetNode = this.getNodeData(target.cell);
    const sourcePort = sourceNode.pointOut.find((item) => item.uuid === source.port) || {};
    const targetPort = targetNode.pointIn.find((item) => item.uuid === target.port) || {};
    // 输入端或者输出端数据为空 可进行连线
    if (targetPort.limit < Infinity) {
      // 输入端口判断数量
      const len = (this.canvasStore.getEdgesByPortId(targetPort.uuid, 'IN') || []).length;
      const overLimit = len + 1 > targetPort.limit;
      overLimit && this.$tip.warning(this.$t('pa.flow.msg62', [targetNode.nodeName, targetPort.limit]));
      return !overLimit;
    } else if (
      !sourcePort.data ||
      !targetPort.data ||
      isEmptyArray(sourcePort.data) ||
      isEmptyArray(targetPort.data) ||
      this.isFlinkSql // FlinkSql类型可以直接连接
    ) {
      return true;
    } else {
      const equalResult = isFieldsEqual(
        sourcePort.data.filter((i) => i.outputable !== false),
        targetPort.data.filter((i) => i.outputable !== false)
      );
      if (!equalResult) {
        const { overInputs, overOutputs } = fieldCheckForConnect(sourcePort.data, targetPort.data);
        this.$tip.warning(
          this.$t('pa.flow.msg63', [
            sourceNode.nodeName,
            targetNode.nodeName,
            overOutputs.length
              ? this.$t('pa.flow.outputFields') + overOutputs.join('、') + this.$t('pa.flow.notInInputs')
              : '',
            overInputs.length ? this.$t('pa.flow.inputField') + overInputs.join('、') + this.$t('pa.flow.notInOutputs') : ''
          ])
        );
      }
      return equalResult;
    }
  }
  // 获取校验失败的提示文案
  getErrorMsg(emptyComponentName: string, emptyField?: string) {
    const errMsg = this.$t('pa.flow.msg64', [
      emptyComponentName,
      !emptyField
        ? this.$t('pa.flow.configMsg1')
        : this.$t('pa.flow.configMsg2') + emptyField + this.$t('pa.flow.configMsg3')
    ]);
    return errMsg;
  }
  // 校验组件配置是否为空
  validateComponentConfig() {
    // 组件配置映射数据
    const propertiesMaps = getComponentProperties(this.$store.getters.componentList);
    const nodes = this.canvasStore.getNodeData();
    const errorNodes: { [key: number]: string }[] = [];
    const isEmpty = (val) => {
      return val === '' || val === null || val === undefined || (Array.isArray(val) && val.length === 0);
    };
    nodes.forEach((node) => {
      // 处理node type异常丢失的情况
      if (!node.type) {
        node.type = getComponentType(node.className);
      }
      let aliasType = '';
      if (node.type === 'JDBC') {
        aliasType = getComponentType(node.className);
      }
      // 特殊组件调用nodeValidate中对应的方法
      if (
        [
          'MAP',
          'DD-MAP',
          'JOIN',
          'FILTER',
          'CUBE_BATCH_PROCESS',
          'DYNAMIC_FILTER',
          'JSON-EXTRACT',
          'ROUTETEMPLATE',
          'Dynamic-MAP',
          'DELAY_PROCESSING',
          'DATA_QUALITY_MONITOR'
        ].includes(node.type) ||
        this.isFlinkSql ||
        aliasType
      ) {
        const { isError, errorMsg } = nodeValidate[aliasType || node.type](node);
        isError && errorNodes.push({ [node.nodeId]: errorMsg });
      } else if (node.properties) {
        if (!propertiesMaps[node.className] || !Array.isArray(propertiesMaps[node.className])) {
          errorNodes.push({ [node.nodeId]: '' });
          return false;
        }
        // 新配置组件校验适配
        if (isNewComponent(node.className)) {
          return propertiesMaps[node.className].some((formItem) => {
            const prop = formItem.prop || (formItem.type === 'serve' ? 'resId' : '');
            const data = node.properties[prop];
            // 确认是否是必填项
            const isRequired = checkFormItemRequired(formItem);
            const isDataEmpty = isEmpty(data);
            const isVisable = checkFormItemVisable(formItem.visible, node.properties);
            if (isRequired && isVisable && isDataEmpty) {
              errorNodes.push({
                [node.nodeId]: this.getErrorMsg(node.nodeName || node.componentName, formItem.label)
              });
              console.log('为空字段(新):', formItem.label);
              return true;
            }
            return false;
          });
        }
        const maps = {};
        // 记录被控制隐藏显示的表单字段
        let hideEls: any = [];
        const hideElObj = {};
        propertiesMaps[node.className].forEach((formItem) => {
          if (formItem.componentCfg && formItem.componentCfg.hideEl) {
            Object.entries(formItem.componentCfg.hideEl).forEach((val) => {
              if (val && Array.isArray(val[1])) {
                val[1].forEach((key) => {
                  if (hideElObj[key] === undefined) {
                    hideElObj[key] = new Set([formItem.model]);
                  } else {
                    hideElObj[key].add(formItem.model);
                  }
                });
                hideEls = hideEls.concat(val[1]);
              }
            });
          }
          maps[formItem.model] = formItem;
        });
        hideEls = [...new Set(hideEls)];
        const v = propertiesMaps[node.className].some((formItem) => {
          // 确认是否是必填项
          const isRequired = checkFormItemRequired(formItem);
          // 如果该项表单有依赖项 判断是否显示 不显示则不需要进行校验非空
          let isShow = true;
          // 必填且值为空 返回 true 结束循环
          if (isRequired && isEmpty(node.properties[formItem.model || formItem.prop])) {
            // 显示隐藏被控制
            if (hideEls.includes(formItem.model)) {
              isShow = [...hideElObj[formItem.model]].every((dep) => {
                if (maps[dep].componentCfg && maps[dep].componentCfg.hideEl) {
                  const val = maps[dep].componentCfg.hideEl[node.properties[dep]];
                  return val && Array.isArray(val) ? !val.includes(formItem.model) : false;
                } else {
                  return true;
                }
              });
              if (isShow) {
                errorNodes.push({
                  [node.nodeId]: this.getErrorMsg(node.nodeName || node.componentName, formItem.label)
                });
                console.log('为空字段:', formItem.label);
              }
              return isShow;
            } else {
              errorNodes.push({
                [node.nodeId]: this.getErrorMsg(node.nodeName || node.componentName, formItem.label)
              });
              console.log('为空字段:', formItem.label);
              // 没有依赖 且必填  且值为空 返回 true
              return true;
            }
          } else {
            return false;
          }
        });
        return v;
      } else if (!node.properties) {
        errorNodes.push({ [node.nodeId]: this.getErrorMsg(node.nodeName || node.componentName) });
      }
      // 节点校验正确 且 原先为报错状态  进行状态重置
      if (!errorNodes.find((i) => i[node.nodeId]) && node.status === 2) {
        this.updateNodeStatus({ id: node.nodeId, status: 1 });
      }
    });
    return errorNodes;
  }
  // 校验组件上下游数据是否一致（字段顺序允许不同）
  validateEdgeData() {
    // 非开发环境直接返回true
    if (this.flowStatus !== 'DEV') return [];
    const errorEdges: any[] = [];
    const edges = this.canvasStore.edges || [];
    edges.forEach((eItem) => {
      const outputFields = this.canvasStore.getPortData(eItem.startNodePoint);
      const inputFields = this.canvasStore.getPortData(eItem.endNodePoint);
      if (!isFieldsEqual(filterOutputFields(outputFields), filterOutputFields(inputFields))) {
        //  TODO: Object.assign(eItem, {
        //   errorMsg: '输入输出字段不匹配, 请断开连线重新连线或者打开组件配置弹窗进行调整保存'
        // })
        errorEdges.push(eItem.id);
      }
    });
    return errorEdges;
  }
  // 遍历节点及以下节点，检查是否有缺失字段
  validateNodeFields(root, rootChangedFields) {
    // 队列
    let queue: OriginNode[] = [cloneDeep(root)];
    const pointUuidAndOutputMaps = new Map();
    // 返回的节点更新信息
    const autoUpdateNodes: ChangedNodeInfo[] = [];
    // 若存在更新后节点数据字段数据缺失 则报错
    let vaild = true;
    // 层序遍历
    while (queue.length) {
      const tempQueue: OriginNode[] = [];
      // 获取该层级所有的输出端点合集
      const points: extraOriginPoint[] = generatePoints(queue);
      points.forEach(({ nodeId, uuid }) => {
        // 当前节点更新输出端点的数据
        const sourceNode = cloneDeep(this.getNodeData(nodeId));
        const sourcePoint = this.canvasStore.getPortData(uuid);
        root.nodeId === nodeId && pointUuidAndOutputMaps.set(uuid, sourcePoint);
        // 获取上有输出字段的删除和新增的字段
        const sourceChangedFields =
          root.nodeId === nodeId
            ? rootChangedFields
            : returnFieldChangeInfo(
                filterOutputFields(sourcePoint),
                pointUuidAndOutputMaps.get(uuid) || this.canvasStore.getPortData(uuid)
              );

        // 获取当前输出端点所有相关的连线
        const connectedEdges = this.canvasStore.getEdgesByPortId(uuid, 'OUT');
        connectedEdges.forEach((edge) => {
          const targetNode = cloneDeep(this.getNodeData(edge.endNode));
          const targetPortId = edge.endNodePoint;
          // 若该节点之前存在上游字段变更 合并变更
          const mergedSourceChangedFields = mergeSourceChangedFields(
            sourceChangedFields,
            targetNode.nodeId,
            targetPortId,
            this.nodeUpdateDatas
          );
          const nodeUpdateInfo: ChangedNodeInfo = new NodeUpdateInfo(targetNode, targetPortId, mergedSourceChangedFields);
          // 判断是否有其他父节点
          if (this.canvasStore.getEdgesByPortId(edge.endNodePoint, 'In').length > 1) {
            this.dag.delEdge({
              id: edge.id,
              target: { cell: edge.endNode },
              source: { cell: edge.startNode }
            });
            this.canvasStore.delEdgeData(edge.startNode, edge.endNode);
            return;
          } else if (root.nodeId === nodeId) {
            this.canvasStore.updateOutputFieldsAndTargetInputFields(root);
          }
          if (targetNode.status === 0) return;
          const setNodeUpdateInfo = (targetPoint: any = null) => {
            // 若节点的输出没有变更直接返回
            if (!isFieldschanged(mergedSourceChangedFields)) return;
            // 更新下游数据的输出和相关字段
            const { outputFields, changeInfo } = updateInputAndOutpyFields(
              pointUuidAndOutputMaps.get(uuid),
              targetPoint ? this.canvasStore.getPortData(targetPoint.uuid) : targetNode.outputFields,
              mergedSourceChangedFields,
              targetNode,
              edge.endNodePoint
            );
            // 输出组件的输出不进行提示
            targetNode.operateType !== 'SINK' && setChangeInfoMsg(nodeUpdateInfo, changeInfo, sourceNode.nodeName);
            setTargetChangedFields(nodeUpdateInfo, changeInfo);
            // 更新节点输出的映射数据 用于下一次循环计算输出数据的变更
            targetPoint && pointUuidAndOutputMaps.set(targetPoint.uuid, outputFields);
            changeInfo && changeInfo.status === 2 && vaild && (vaild = false);
            // 获取下游以输入字段做数据源字段的变更
            const changeInfos = updateUseInputFields(
              targetNode,
              edge.endNodePoint,
              mergedSourceChangedFields,
              this.$store.getters.componentListMap.get(targetNode.className)
            );
            changeInfos.forEach((cItem) => {
              setChangeInfoMsg(nodeUpdateInfo, cItem, sourceNode.nodeName);
              setTargetChangedFields(nodeUpdateInfo, cItem);
              cItem && cItem.status === 2 && vaild && (vaild = false);
            });
          };
          if (targetNode.operateType === 'SINK') {
            setNodeUpdateInfo();
          } else {
            targetNode.pointOut.forEach((targetPoint) => {
              setNodeUpdateInfo(targetPoint);
            });
          }
          // 节点状态为未配置状态，不进入下一步循环
          targetNode.status !== 0 && targetNode.operateType !== 'SINK' && tempQueue.push(targetNode);
          if (nodeUpdateInfo.msg.length) {
            autoUpdateNodes.push(nodeUpdateInfo);
          } else if (targetNode.status === 2) {
            // 原本校验错误的节点重置为已配置状态
            this.updateNodeStatus({ id: targetNode.nodeId, status: 1 });
            this.nodeUpdateDatas = this.nodeUpdateDatas.filter((i) => i.nodeId !== targetNode.nodeId);
          }
        });
      });
      queue = tempQueue;
    }
    // return 所有需要自动更新的节点
    return { vaild, autoUpdateNodes };
  }
  // 根据更新信息 设置节点状态及提示信息
  setNodeStatusByUpdateInfo() {
    // 将节点相同的数据合并
    const map = new Map();
    this.nodeUpdateDatas.forEach(({ nodeId, msg }) => {
      if (!map.has(nodeId)) {
        map.set(nodeId, [...msg]);
      } else {
        map.get(nodeId).push(...msg);
      }
    });
    for (const nodeId of map.keys()) {
      this.updateNodeStatus({ id: nodeId, status: 2, msg: map.get(nodeId).join('\n') });
    }
  }
  // 获取错误节点position信息
  getErrorNodesPosition() {
    this.errorNodesPosition = (this.dag.getNodes() || [])
      .map((node) =>
        node.status === 2
          ? {
              id: node.id,
              position: node.position
            }
          : null
      )
      .filter(Boolean);

    this.currentErrorNodeIdx = 0;
    this.errorNodesPosition.length && this.setCenterNode(this.errorNodesPosition[0].position);
  }
  // 查找上一个错误节点
  handleArrowUpClick() {
    if (this.currentErrorNodeIdx === 0) return;
    this.currentErrorNodeIdx--;
    this.setCenterNode(this.errorNodesPosition[this.currentErrorNodeIdx].position);
  }
  // 查找下一个错误节点
  handleArrowBottomClick() {
    if (this.currentErrorNodeIdx === this.errorNodesPosition.length - 1) return;
    this.currentErrorNodeIdx++;
    this.setCenterNode(this.errorNodesPosition[this.currentErrorNodeIdx].position);
  }
  // 设置节点至中心点
  setCenterNode(position) {
    let [x, y] = position || [0, 0];
    const { width, height } = this.nodeOptions;
    x = x + width / 2;
    y = y + height / 2;
    this.dag.setCenterNode([x, y]);
  }
  // 当输出字段的顺序变更后 遍历节点及下游节点进行更新
  updateNodeFieldsWhenSorted(root) {
    // 队列
    let queue: OriginNode[] = [cloneDeep(root)];
    // 层序遍历
    while (queue.length) {
      const tempQueue: OriginNode[] = [];
      // 获取该层级所有的输出端点合集
      const points: extraOriginPoint[] = generatePoints(queue);
      points.forEach(({ nodeId, uuid }) => {
        // 当前节点更新输出端点的数据
        const sourceNode = this.getNodeData(nodeId);
        // 更新输出端点的数据
        this.canvasStore.updatePortData(sourceNode.nodeId, uuid, 'OUT', sourceNode.outputFields);
        // 获取当前输出端点所有相关的连线
        const connectedEdges = this.canvasStore.getEdgesByPortId(uuid, 'OUT');
        connectedEdges.forEach((edge) => {
          const targetNode = this.getNodeData(edge.endNode);
          const targetPortId = edge.endNodePoint;
          // 更新输入字段
          targetNode.inputFields = sourceNode.outputFields.filter((i) => i.outputable);
          this.canvasStore.updatePortData(targetNode.nodeId, targetPortId, 'IN', targetNode.inputFields);
          // 全量输入输出组件直接进行更新
          if (
            FullInputAndOutputComponents.includes(targetNode.type) ||
            this.$store.getters.componentListMap.get(targetNode.className).outputs === 'all' // 新组件配置适配
          ) {
            targetNode.outputFields = cloneDeep(targetNode.inputFields);
            // 节点状态为未配置状态，不进入下一步循环
            targetNode.status !== 0 && targetNode.operateType !== 'SINK' && tempQueue.push(targetNode);
          }
        });
      });
      queue = tempQueue;
    }
  }
  // 更新节点状态
  public updateNodeStatus(data) {
    if (!Array.isArray(data)) data = [data];
    let isChange = false;
    data.forEach(({ id, status, msg = '' }) => {
      // 优化: 当节点状态或者文案真正更改时去调用方法
      const node = (this.dag.getNodes() || []).find((item) => item.id == id);
      if (node.status === 2 && status === 3) return;
      if (node.status !== status || node.msg !== msg) {
        isChange = status !== 3 ? true : false;
        this.dag.updateNode(id, 'status', status);
        this.dag.updateNode(id, 'msg', msg);
        this.getNodeData(id).status = status;
      }
    });
    isChange && this.$emit('content-change', true);
    // 获取报错节点的汇总信息
    this.getErrorNodesPosition();
  }
  // 更新节点配置信息
  public updateNodeData(id, data) {
    // 原始数据备份
    const rawData = cloneDeep(this.getNodeData(id));
    // updatedData: 更新后的节点数据   isChange：节点数据是否有更新
    const { updatedData, isChange } = this.canvasStore.updateNodeData(id, data);
    // 部分组件需要更新画布中的节点名称
    this.dag.updateNode(id, 'nodeName', data.nodeName);
    isChange && this.$emit('content-change', true);
    // 若存在节点更新信息 则删除
    this.nodeUpdateDatas = this.nodeUpdateDatas.filter((i) => i.nodeId !== updatedData.nodeId);
    // 输出字段是否已更新
    const changedFields: OriginChangedFields = returnFieldChangeInfo(
      filterOutputFields(rawData.outputFields),
      filterOutputFields(updatedData.outputFields)
    );
    // 输入输出字段是否更新
    if (isFieldschanged(changedFields)) {
      const { vaild, autoUpdateNodes } = this.validateNodeFields(updatedData, changedFields);
      // 校验数据 模拟节点更新后是否有节点报错，缺失必要字段时进行报错
      // 若不报错 打开字段自动更新弹窗
      if (autoUpdateNodes.length === 0) return;
      this.nodeUpdateDatas = appendToNodeUpdateData(this.nodeUpdateDatas, autoUpdateNodes);
      if (vaild) {
        this.visibleNodeUpdate = true;
        this.nodeUpdateDatasForDialog = autoUpdateNodes;
      } else {
        // 重置节点状态
        this.setNodeStatusByUpdateInfo();
      }
    } else if (isFieldsSortingChanged(rawData.outputFields || [], updatedData.outputFields || [])) {
      // 输出字段未增删 但排序变更 往下游进行更新
      this.updateNodeFieldsWhenSorted(updatedData);
    }
  }
  // 更新节点详情
  public updateNodeDetail(id, data) {
    this.dag.updateNode(id, 'nodeName', data.nodeName);
    this.dag.updateNode(id, 'parallelism', data.parallelism);
    const nodeData = this.getNodeData(id);
    nodeData.nodeName = data.nodeName;
    nodeData.parallelism = data.parallelism;
    this.$emit('content-change', true);
  }
  // 返回处理完成的原始数据
  public getContent() {
    // 获取每个节点所有的父节点
    const dagnNodes = (this.dag.getNodes() || []).map((item) => {
      item.parentNodes = this.dag.getParentNodeIds(item.id);
      return item;
    });
    return (this.canvasStore as any).convertToOrigin(dagnNodes, this.dag.getEdges());
  }
  public validteFlow(type: CheckType) {
    if (type === 'save') {
      // FlinkSql类型保存时不校验连线信息
      if (this.isFlinkSql) return true;
      // 报错操作校验上下游数据是否一致’
      const errorEdges = this.validateEdgeData();
      // TODO:标红连线 显示错误信息
      errorEdges.forEach((id) => {
        this.dag.updateEdge(id, '#FF5353');
      });
      return !errorEdges.length;
    } else {
      // 其他校验数据组件配置数据是否不完整
      const errorNodes = this.validateComponentConfig();
      errorNodes.forEach((item) => {
        const [[key, value]] = Object.entries(item);
        this.updateNodeStatus({ id: key, status: 2, msg: value });
      });
      return !errorNodes.length;
    }
  }
  // 设置部分节点高亮
  public highlightNodes(nodes) {
    const { removeActiveNode, setActiveNode } = this.dag;
    const edges = this.canvasStore.edges;
    // 移除所有高亮节点
    removeActiveNode();
    nodes.forEach((id) => setActiveNode(id));
    edges.forEach(({ id, startNode, endNode }) => {
      // 起始节点都在目标节点中，保持现有颜色，否则置灰
      const needHightlight = nodes.length === 0 || (nodes.includes(startNode) && nodes.includes(endNode));
      this.dag.updateEdge(id, needHightlight ? '#54C958' : '#AAAAAA');
    });
  }
}
</script>

<style lang="scss" scoped>
@import './canvas.scss';
</style>
<style>
body > .dag-node__tooltip {
  white-space: pre-line;
}
body > .dag-node__left .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 0px;
}
</style>
