import { createDialog } from '@/utils';
import NodeConfig from './node-config.vue';
import PrintLog from '../components/print-log.vue';
import FieldsMapping from './components/fields-mapping.vue';
import i18n from '@/i18n';
const openNodeConfigDialog = ({ data, jobData, disabled }) => {
  return createDialog({ title: i18n.t('pa.flow.msg288', [data.nodeName, data.componentName]), size: 'large' }, (h) => [
    h(NodeConfig, { props: { data, jobData, disabled }, slot: 'default' }),
    h(PrintLog, {
      props: { value: data.printLog, disabled },
      slot: 'footer-left',
      on: { input: (val) => (data.printLog = val) }
    })
  ]);
};
export default openNodeConfigDialog;

// 打开表字段配置弹窗
export const openFieldsMappingDialog = ({ value, tableName, formData, disabled, inputOptions, config }) => {
  return createDialog({ title: i18n.t('pa.flow.fieldMapTitle') }, (h) => [
    h(FieldsMapping, { props: { value, tableName, formData, disabled, inputOptions, config }, slot: 'default' })
  ]);
};
