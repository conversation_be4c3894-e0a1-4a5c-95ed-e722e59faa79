<template>
  <div class="config-container">
    <el-form
      v-for="(el, index) in list"
      :key="el.name"
      ref="form"
      :disabled="disabled"
      :model="el"
      class="config-item"
      :class="{ 'config-item--height': judgeShowRule(el) }"
    >
      <!-- 信息栏 -->
      <div class="config-item-bar">
        <!-- 左侧 方法 -->
        <el-form-item
          :rules="methodRules"
          class="config-item-funcName"
          label-width="40px"
          prop="funcName"
        >
          <div slot="label" class="config-item-funcName__name">{{ el.name }}</div>
          <el-select
            v-model="el.funcName"
            filterable
            placeholder="请选择方法"
            @change="$emit('change', index)"
          >
            <el-option-group v-for="group in methodList" :key="group.label" :label="group.label">
              <el-option
                v-for="method in group.children"
                :key="method.value"
                :label="method.label"
                :value="method.value"
              >
                <div :key="methodCode[method.value]" class="config-option">
                  <span>{{ method.label }}</span>
                  <el-popover
                    v-if="method.type !== 'DEFAULT'"
                    placement="right"
                    trigger="hover"
                    width="360"
                    @show="handleShow(method)"
                  >
                    <div v-loading="!Boolean(methodCode[method.value])" style="min-height: 35px">
                      <div v-highlight>
                        <code class="java">{{ methodCode[method.value] }}</code>
                      </div>
                    </div>
                    <span slot="reference" class="config-option__code">源码</span>
                  </el-popover>
                </div>
              </el-option>
            </el-option-group>
          </el-select>
          <!-- 表达式 -->
          <div
            v-if="activeNames[el.name].length < 1 || judgeShowRule(el)"
            class="config-expression"
          >
            <span>表达式：</span>
            <el-tooltip v-hide="expressionMapping[el.name]" effect="light" placement="top">
              <div slot="content" v-html="expressionMapping[el.name]"></div>
              <div class="config-expression-item" v-html="expressionMapping[el.name]"></div>
            </el-tooltip>
          </div>
        </el-form-item>
        <!-- 右侧 -->
        <el-button-group>
          <el-button
            class="config-item-bar__btn"
            :disabled="disabled"
            type="text"
            @click.native="emitEvent('copy', index)"
          >
            复制
          </el-button>
          <el-button :disabled="disabled" type="text" @click.native="emitEvent('delete', index)">
            删除
          </el-button>
        </el-button-group>
      </div>
      <!-- 配置详情 -->
      <el-collapse
        v-if="judgeShowRule(el, true)"
        v-model="activeNames[el.name]"
        class="config-collapse"
      >
        <el-collapse-item :name="el.name">
          <!-- 标题 -->
          <div slot="title" class="config-collapse__title">
            {{ el.funcType === 'DEFAULT' ? '条件配置' : '参数映射' }}
          </div>
          <!-- 表格 -->
          <el-table :data="el.tableData" border>
            <el-table-column
              v-for="item in el.tableHead"
              :key="item.prop"
              :label="item.label"
              :min-width="item.minWidth"
              :prop="item.prop"
              :width="item.width"
            >
              <template v-slot="scope">
                <div v-if="item.type === 'select'">
                  <el-form-item
                    v-if="el.funcType === 'DEFAULT'"
                    :rules="upstreamFieldRules"
                    prop="funcArgs[0].key"
                  >
                    <el-autocomplete
                      v-model="el.funcArgs[0].key"
                      clearable
                      size="small"
                      :fetch-suggestions="querySearch"
                    />
                    <el-select
                      v-if="false"
                      v-model="el.funcArgs[0].key"
                      clearable
                      filterable
                      size="small"
                    >
                      <el-option
                        v-for="field in upstreamField"
                        :key="field.name"
                        :label="field.name"
                        :value="field.name"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    v-else
                    :prop="`funcArgs[${scope.$index}].key`"
                    :rules="upstreamFieldRules"
                  >
                    <el-autocomplete
                      v-model="el.funcArgs[scope.$index].key"
                      clearable
                      size="small"
                      :fetch-suggestions="querySearch"
                    />
                    <el-select
                      v-if="false"
                      v-model="el.funcArgs[scope.$index].key"
                      clearable
                      filterable
                      size="small"
                    >
                      <el-option
                        v-for="field in upstreamField"
                        :key="field.name"
                        :label="field.name"
                        :value="field.name"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <el-form-item
                  v-else-if="item.type === 'input'"
                  :rules="scope.row.disabled ? {} : thresholdRules"
                  prop="funcArgs[0].value"
                >
                  <el-input
                    v-model="el.funcArgs[0].value"
                    :type="scope.row.type"
                    :disabled="scope.row.disabled"
                    clearable
                    size="small"
                  />
                </el-form-item>
                <div v-else>{{ scope.row[item.prop] }}</div>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </el-form>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Watch, Vue } from 'vue-property-decorator';
import { debounce } from 'lodash';
import { highlight, hide } from './utils';

@Component({
  directives: { highlight, hide }
})
export default class LogicConfig extends Vue {
  @PropSync('data', { default: () => [] }) list!: any[];
  @Prop({ type: Array, default: () => [] }) upstreamField!: any[];
  @Prop({ type: Array, default: () => [] }) methodList!: any[];
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => ({}) }) expressionMapping!: any;
  @Prop({ default: () => ({}) }) methodCode!: any;

  private methodRules: any[] = [
    {
      required: true,
      message: '请选择方法',
      trigger: 'change'
    }
  ];
  private upstreamFieldRules: any = {
    required: true,
    message: '请选择上游组件字段',
    trigger: 'change'
  };
  private thresholdRules: any[] = [
    {
      required: true,
      message: '请输入阈值',
      trigger: 'blur'
    },
    {
      min: 1,
      max: 50,
      message: '长度在 1 到 50 个字符',
      trigger: 'blur'
    }
  ];
  private activeNames: any = {};
  private emitEvent: any = debounce(this.handleEvent, 1000);

  // private methodCode: any = {};
  @Watch('list', { immediate: true })
  handleListChange(value) {
    this.activeNames = value.reduce((pre, { name }) => {
      pre[name] = Array.isArray(this.activeNames[name]) ? this.activeNames[name] : [name];
      return pre;
    }, {});
  }

  judgeShowRule({ funcType, funcArgs }, defaultVal = false) {
    if (['PRIVATE', 'SHARE'].includes(funcType)) {
      return defaultVal ? funcArgs.length : !funcArgs.length;
    }
    return defaultVal;
  }

  validate() {
    return Promise.all((this.$refs.form as any).map((el) => el.validate()));
  }

  handleEvent(name, index) {
    this.$emit(name, index);
  }

  handleShow({ value: name, type: funcType }) {
    if (this.methodCode[name]) return;
    this.$emit('getCode', { name, funcType });
  }

  querySearch(queryStr, cb) {
    const fieldList = !queryStr
      ? this.upstreamField
      : this.upstreamField.filter(({ name }) => name.includes(queryStr.trim()));
    cb(fieldList.map(({ name }: any) => ({ value: `#${name}#` })));
  }
}
</script>
<style lang="scss" scoped>
$borderColor: #f1f1f1;

.config-option {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &__code {
    display: none;
    color: #377cff;
    font-weight: normal !important;
  }

  &:hover {
    .config-option__code {
      display: inline-block;
    }
  }
}

.config {
  &-container {
    background: #ffffff;
  }

  &-item {
    padding: 16px 20px;
    margin: 0 auto 16px;
    width: calc(100% - 40px);
    min-height: 100px;
    background: #fafbfc;
    border-radius: 4px;
    border: 1px solid #f1f1f1 !important;
    box-sizing: border-box;

    ::v-deep .el-autocomplete {
      width: 100%;
    }

    &--height {
      padding-bottom: 0;
      min-height: unset;
    }

    ::v-deep .el-form {
      &-item {
        margin: 0;

        &.is-error {
          margin-bottom: 20px;
        }
      }
    }

    &-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      &__btn {
        margin-right: 20px !important;
      }
    }

    &-funcName {
      width: calc(100% - 108px);

      ::v-deep .el-form-item {
        &__label {
          &::before {
            display: none;
          }
        }

        &__content {
          display: flex;
          align-items: center;
        }
      }

      ::v-deep .el-select {
        width: 312px;
      }

      &__name {
        width: 32px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        color: #fff;
        background: #377cff;
      }
    }
  }

  &-collapse {
    border-bottom: 0;

    &__title {
      font-weight: bold;
      color: #444444;
    }

    ::v-deep .el-collapse {
      &-item {
        &__content {
          padding: 0;
        }

        &__header {
          background: transparent;
          border: 0;
        }
      }
    }

    ::v-deep .el-table {
      border-top: 1px solid #f1f1f1;

      .cell {
        height: 49px !important;
      }

      &::before {
        height: 0;
      }

      &--border,
      &__cell {
        border-right: 0;

        &:first-child .cell {
          padding-left: 18px;
        }
      }

      &__row:last-child {
        .el-table__cell {
          border-bottom: 0;
        }
      }

      ::v-deep .el-input {
        width: 220px;

        &__suffix {
          top: 1px;
        }
      }

      ::v-deep .el-select {
        .el-input__suffix {
          top: -1px;
        }
      }
    }
  }

  &-expression {
    display: flex;
    align-items: center;
    margin-left: 20px;
    width: 430px;

    &-item {
      width: calc(100% - 58px);
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
</style>
