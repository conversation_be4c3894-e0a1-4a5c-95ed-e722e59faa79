<template>
  <div>
    <el-form-item
      v-for="el in renderList"
      v-show="!el.hidden"
      :key="el.name"
      :prop="el.name"
      :label="el.label"
      :rules="rules[el.name] || []"
    >
      <div :class="['resource-item', el.tip || el.unit ? 'resource-item--tip' : '']">
        <!-- select -->
        <el-select v-if="el.type === 'select'" v-model="form[el.name]" filterable clearable :placeholder="el.placeholder">
          <el-option v-for="item in el.options" :key="item" :label="item" :value="item" />
        </el-select>
        <!-- number -->
        <el-input-number
          v-if="el.type === 'number'"
          v-model="form[el.name]"
          number
          :min="el.min"
          :max="el.max"
          :step="el.step ? el.step : 1"
          :precision="el.precision ? el.precision : 0"
          :placeholder="el.placeholder"
        />
        <!-- 复选框 -->
        <el-checkbox v-if="el.type === 'checkbox'" v-model="form[el.name]" />
      </div>
      <!-- 单位 -->
      <span v-if="el.unit" class="advanced-config-unit">{{ el.unit }}</span>
      <!-- 提示 -->
      <el-tooltip v-if="el.tip" effect="light" :content="el.tip" placement="bottom">
        <div v-if="el.wrap" slot="content" v-html="el.tip"></div>
        <i class="iconfont icon-wenhao advanced-config-icon"></i>
      </el-tooltip>
    </el-form-item>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, PropSync, Watch } from 'vue-property-decorator';
import { ADVANCE_RENDER_LIST, FLOW_DEFAULT_CONFIG } from '../config';

@Component
export default class AdvancedConfig extends Vue {
  @Prop({ default: () => ({}) }) property!: any;
  @PropSync('data', { default: FLOW_DEFAULT_CONFIG() }) form!: any;
  @Prop({ type: String }) type!: string;
  private renderList: any[] = ADVANCE_RENDER_LIST(); // 渲染列表
  private rules: any = {
    taskManagerLimitCpu: [{ required: false, message: this.$t('pa.tip.taskManagerLimitCpu'), trigger: 'blur' }],
    queue: [{ required: false, message: this.$t('pa.placeholder.queue') }],
    namespace: [{ required: false, message: this.$t('pa.flow.msg104') }],
    taskManagerLimitMemory: [{ required: false, message: this.$t('pa.tip.taskManagerLimitMemory'), trigger: 'blur' }],
    jobManagerRequestCpu: [{ required: false, message: this.$t('pa.tip.jobManagerRequestCpu'), trigger: 'blur' }],
    deployTimeout: [{ required: true, message: this.$t('pa.tip.deployTimeout'), trigger: 'blur' }],
    jobManagerMemory: [{ required: true, message: this.$t('pa.tip.jobManagerMemory'), trigger: 'blur' }],
    jobManagerLimitCpu: [{ required: false, message: this.$t('pa.tip.jobManagerLimitCpu'), trigger: 'blur' }],
    jobManagerLimitMemory: [{ required: false, message: this.$t('pa.tip.jobManagerLimitMemory'), trigger: 'blur' }]
  };
  get isJar() {
    return this.type === 'jar';
  }
  @Watch('property', { immediate: true, deep: true })
  handleProperty({ custom = '', clusterType = '' }: any) {
    this.handleIsCloud(clusterType === 'CLOUD');
    const key = clusterType === 'CLOUD' ? 'namespace' : 'queue';
    const info = custom.split(',');
    // 1.当选中Standalone、yarn session模式，隐藏托管内存因子,job manager内存;
    if (info.length > 0) {
      const isHiddenClusterType = ['STANDALONE', 'YARN_SESSION'].includes(clusterType);
      this.setRenderListValue('taskmanager_managed_frac', isHiddenClusterType ? true : false);
      this.setRenderListValue('jobManagerMemory', isHiddenClusterType ? true : false);
    }
    if (info.length > 0 && ['YARN_APPLICATION', 'YARN_PER_JOB', 'CLOUD'].includes(clusterType)) {
      this.setRenderListValue(key, false);
      this.setRenderListValue(key, info, 'options');
      this.setRulesValue(key, true);
      this.$set(this.form, key, this.form[key] || info[0] || '');
      return;
    }
    this.$set(this.form, key, '');
    this.setRenderListValue(key, true);
    this.setRenderListValue(key, [], 'options');
    this.setRulesValue(key, false);
  }
  @Watch('form.clusterType', { immediate: true })
  handleClusterTypeChange(val) {
    if (!this.isJar) return;
    // 重置renderList
    this.renderList = ADVANCE_RENDER_LIST();
    if (val === 'YARN_PER_JOB') {
      this.setRenderListValue('logOutputKafka', true, 'hidden');
    }
    if (val === 'STANDALONE') {
      this.renderList.forEach((i, index) => {
        this.$set(this.renderList[index], 'hidden', i.name !== 'deployTimeout');
      });
    }
  }

  @Watch('form.enableCheckPoint', { immediate: true })
  handleCheckPointChange(val: string) {
    this.setRenderListValue('checkpointInterval', !val, 'hidden');
    this.setRenderListValue('cp_timeout', !val, 'hidden');
    this.setRenderListValue('cp_min_pause', !val, 'hidden');
    this.setRenderListValue('cp_failed', !val, 'hidden');
    this.setRenderListValue('cp_unaligned', !val, 'hidden');
    this.setRenderListValue('stateBackend', !val, 'hidden');
  }

  @Watch('form.restartStrategy', { immediate: true })
  handleIsRestartStrategy(val: string, oldVal: string) {
    const arr = [
      { name: 'failuresPerInterval', showRule: 'failure-rate' },
      { name: 'failureRateInterval', showRule: 'failure-rate' },
      { name: 'failureRateDelay', showRule: 'failure-rate' },
      { name: 'attempts', showRule: 'fixed-delay' },
      { name: 'delay', showRule: 'fixed-delay' },
      { name: 'initialBackoff', showRule: 'exponential-delay' },
      { name: 'maxBackoff', showRule: 'exponential-delay' },
      { name: 'backoffMultiplier', showRule: 'exponential-delay' },
      { name: 'resetBackoffThreshold', showRule: 'exponential-delay' },
      { name: 'jitterFactor', showRule: 'exponential-delay' }
    ];
    arr.forEach(({ name: key, showRule }) => {
      this.setRenderListValue(key, showRule !== val);
    });
    if (oldVal) {
      this.form.attempts = 5; // 尝试次数
      this.form.delay = 10; // 固定重启延迟时间
    }
  }

  handleIsCloud(val: boolean) {
    [
      'taskManagerLimitCpu',
      'taskManagerLimitMemory',
      'taskManagerNumber',
      'jobManagerRequestCpu',
      'jobManagerLimitCpu',
      'jobManagerLimitMemory'
    ].forEach((el) => {
      this.setRenderListValue(el, !val);
      this.setRulesValue(el, val);
    });
    this.setRenderListValue('queue', val);
    this.setRenderListValue('namespace', !val);
    this.setRulesValue('queue', !val);
    this.setRulesValue('namespace', val);
  }

  setRenderListValue(name: string, value: any, key = 'hidden') {
    const index = this.renderList.findIndex((el) => el.name === name);
    if (index > -1) {
      this.$set(this.renderList[index], key, value);
    }
  }

  setRulesValue(name: string, value: any, key = 'required') {
    if (name in this.rules) {
      this.$set(this.rules[name][0], key, value);
    }
  }
}
</script>

<style scoped lang="scss">
.advanced-config {
  &-icon {
    margin-left: 10px;
  }
  &-unit {
    padding-left: 10px;
  }
}
</style>
