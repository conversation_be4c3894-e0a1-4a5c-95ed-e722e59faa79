{"name": "pipeace-mgr-ui-v2", "version": "0.0.2", "main": "dist/src/moduleExportEntry.ts", "files": ["dist"], "scripts": {"vite": "vite", "preview": "vite preview", "build:vite": "vuedx-typecheck . && vite build", "serve": "nodemon --watch vue.config.js --exec \"vue-cli-service  serve\"", "build": "node --max_old_space_size=12288 node_modules/@vue/cli-service/bin/vue-cli-service.js build", "format": "ucs format --write src", "lint": "ucs lint --ext .ts,.js,.jsx,.vue src", "fix": "ucs lint --fix --ext .ts,.js,.jsx,.vue src", "iserve": "npm install & vue-cli-service  serve", "ivite": "npm install & vite", "release": "bs subBuild && npm publish", "report": "vue-cli-service build --report", "prepare": "husky install"}, "dependencies": {"@antv/g6": "^4.8.24", "@antv/util": "^3.2.2", "@bs/axios": "1.1.0", "@bs/wujie": "^1.2.8", "@svgdotjs/svg.js": "^3.1.2", "acorn": "^8.7.0", "axios": "^1.6.8", "bs-ui-pro": "^1.3.23", "core-js": "^3.4.3", "cron-parser": "4.9.0", "crypto-js": "4.1.1", "cytoscape": "^3.20.0", "cytoscape-cola": "^2.5.1", "cytoscape-dom-node": "1.1.0", "dayjs": "^1.11.5", "echarts": "4.6.0", "element-resize-detector": "^1.2.4", "generate-source-map": "^0.0.5", "highlight.js": "^11.5.0", "html2canvas": "^1.4.1", "js-beautify": "^1.10.3", "js-cookie": "^3.0.5", "json-stringify-pretty-compact": "^4.0.0", "lodash": "4.17.15", "net": "^1.0.2", "npx": "^10.2.2", "rxjs": "^6.5.4", "sha256": "^0.2.0", "sm-crypto": "0.3.12", "sockjs-client": "^1.4.0", "stompjs": "^2.3.3", "tslib": "^2.3.1", "vue": "2.6.14", "vue-class-component": "^7.0.2", "vue-clipboard2": "^0.3.3", "vue-i18n": "^8.16.0", "vue-property-decorator": "^9.1.2", "vue-router": "^3.1.3", "vuex": "^3.1.2", "vuex-class": "^0.3.2", "xml-formatter": "^3.6.3", "xterm": "5.3.0", "xterm-addon-fit": "0.8.0"}, "devDependencies": {"@bs/root": "0.0.1", "@originjs/vite-plugin-commonjs": "^1.0.3", "@types/echarts": "4.4.2", "@types/js-beautify": "^1.8.2", "@types/lodash": "4.17.5", "@types/stompjs": "^2.3.4", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "^5.0.8", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-plugin-component": "^1.1.1", "compression-webpack-plugin": "^6.1.0", "copy-webpack-plugin": "^5.1.2", "husky": "^8.0.3", "lint-staged": "^9.5.0", "monaco-editor-webpack-plugin": "^2.1.0", "nodemon": "^3.1.7", "rollup-plugin-require-context": "^1.0.1", "sass": "~1.33.0", "sass-loader": "~8.0.0", "speed-measure-webpack-plugin": "^1.5.0", "svg-sprite-loader": "^6.0.11", "svg-transform-loader": "^2.0.13", "svgo-loader": "^3.0.0", "typescript": "4.2.4", "util": "^0.12.5", "vite": "4.3.3", "vite-plugin-require-transform": "^1.0.3", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue2": "^2.0.1", "vue-template-compiler": "2.6.14"}, "overrides": {"webpack": "5.54.0"}, "lint-staged": {"*.{js,vue,ts}": ["ucs format --write", "ucs lint --ext .ts,.js,.vue", "git add"]}, "browserslist": ["> 1%", "last 2 versions", "not dead", "chrome >= 70"], "babel": {"presets": ["@vue/cli-plugin-babel/preset"]}, "eslintConfig": {"extends": ["@bs/eslint-config"]}, "eslintIgnore": ["/*.js", "/src/assets/*", "/src/config/*", "/src/local/*", "/src/router/*", "/src/store/*", "/src/common/*", "/src/shims-vue.d.ts", "/src/shims-tsx.d.ts"], "prettier": "@bs/prettier-config", "commitlint": {"extends": ["@bs/commitlint-config"]}}