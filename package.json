{"name": "pipeace-mgr-ui-v2", "version": "0.0.2", "main": "dist/src/moduleExportEntry.ts", "files": ["dist"], "scripts": {"vite": "vite", "preview": "vite preview", "build:vite": "vuedx-typecheck . && vite build", "serve": "vue-cli-service  serve", "build": "vue-cli-service build", "commit": "git cz", "add-commit": "git add . && git cz", "git": "git add . && git cz && git pull && git push", "format": "prettier --write .", "lint": "eslint  --ext .ts,.js,.jsx,.vue src", "fix": "eslint --fix --ext .ts,.js,.jsx,.vue src", "iserve": "npm install & vue-cli-service  serve", "ivite": "npm install & vite", "release": "bs subBuild && npm publish", "report": "vue-cli-service build --report"}, "dependencies": {"@antv/g6": "^4.6.0", "@antv/util": "^3.2.2", "@bs-ui-pro/pro-form": "^1.0.0", "@svgdotjs/svg.js": "^3.1.2", "@types/echarts": "^4.4.2", "@types/js-base64": "^2.3.1", "@types/js-beautify": "^1.8.2", "@types/lodash": "4.17.5", "@types/stompjs": "^2.3.4", "acorn": "^8.7.0", "assets-fix": "0.0.22-2", "assets-ofa-for-pa": "^13.0.9", "axios": "^0.19.0", "bs-graph-sdk-1": "^0.1.50", "bs-ui-pro": "1.3.12", "bsview-3.0": "~3.1.1", "clipboard": "^2.0.10", "core-js": "^3.4.3", "cron-parser": "^2.5.0", "crypto-js": "^4.0.0", "cube-compute-mgr-ui": "2.5.3", "cytoscape": "^3.20.0", "cytoscape-cola": "^2.5.1", "cytoscape-dom-node": "1.1.0", "dagre-d3": "^0.6.4", "dayjs": "^1.11.5", "diff": "^5.0.0", "diff2html": "^3.4.11", "echarts": "^4.6.0", "generate-source-map": "^0.0.5", "highlight.js": "^11.5.0", "html2canvas": "^1.4.1", "insert-css": "^2.0.0", "jquery": "^3.4.1", "jquery-ui-dist": "^1.13.1", "js-base64": "^3.7.5", "js-beautify": "^1.10.3", "JSONPath": "^0.11.2", "jsplumb": "^2.12.9", "lodash": "4.17.15", "moment": "^2.24.0", "npx": "^10.2.2", "rxjs": "^6.5.4", "sha256": "^0.2.0", "sockjs-client": "^1.4.0", "stompjs": "^2.3.3", "stream-cube-script-ui_3.4.0": "^1.0.3", "tippy.js": "^5.2.1", "tslib": "^2.3.1", "vue": "2.6.14", "vue-axios": "^2.1.5", "vue-class-component": "^7.0.2", "vue-clipboard2": "^0.3.3", "vue-codemirror": "^4.0.6", "vue-property-decorator": "^9.1.2", "vue-router": "^3.1.3", "vuex": "^3.1.2", "vuex-class": "^0.3.2", "xterm": "^4.3.0", "xterm-addon-fit": "^0.3.0"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@commitlint/config-conventional": "^17.0.3", "@originjs/vite-plugin-commonjs": "^1.0.3", "@types/jquery": "^3.3.31", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "babel-plugin-component": "^1.1.1", "commitizen": "^4.2.4", "commitlint": "^17.0.3", "commitlint-config-cz": "^0.13.3", "copy-webpack-plugin": "^5.1.2", "cz-customizable": "^6.9.0", "eslint": "^6.7.2", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "lint-staged": "^9.5.0", "monaco-editor-webpack-plugin": "^2.1.0", "prettier": "2.3.2", "rollup-plugin-require-context": "^1.0.1", "sass": "~1.33.0", "sass-loader": "~8.0.0", "svg-sprite-loader": "^6.0.11", "svg-transform-loader": "^2.0.13", "svgo-loader": "^3.0.0", "typescript": "4.2.4", "vite": "^3.1.3", "vite-plugin-require-transform": "^1.0.3", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue2": "^2.0.1", "vue-eslint-parser": "^7.10.0", "vue-template-compiler": "2.6.14"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,vue,ts}": ["prettier --write", "eslint --ext .ts,.js,.vue", "git add"]}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "commitlint": {"extends": ["@commitlint/config-conventional", "cz"], "rules": {"type-enum": [2, "always", ["feat", "fix", "docs", "style", "refactor", "perf", "test", "chore", "revert", "build"]], "subject-case": [0]}}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}}, "eslintConfig": {"root": true, "env": {"browser": true, "node": true, "es6": true}, "parser": "vue-eslint-parser", "extends": ["plugin:vue/essential", "plugin:vue/recommended", "plugin:prettier/recommended", "prettier/@typescript-eslint", "plugin:@typescript-eslint/recommended"], "plugins": ["@typescript-eslint"], "parserOptions": {"parser": "@typescript-eslint/parser"}, "rules": {"prettier/prettier": "error", "no-console": 0, "no-debugger": 1, "array-bracket-spacing": 2, "no-unused-vars": 2, "no-var": 2, "no-eval": 2, "arrow-spacing": 2, "block-spacing": 2, "key-spacing": 2, "brace-style": 2, "vue/camelcase": 2, "vue/require-component-is": 0, "vue/require-default-prop": 0, "vue/this-in-template": 0, "vue/attributes-order": 2, "comma-dangle": ["off", "always-multiline"], "vue/eqeqeq": [2, "always", {"null": "ignore"}], "object-curly-spacing": [2, "always"], "vue/singleline-html-element-content-newline": 0, "vue/html-closing-bracket-newline": [2, {"singleline": "never", "multiline": "always"}], "vue/no-v-html": 0, "vue/max-attributes-per-line": 0, "vue/html-self-closing": [2, {"html": {"void": "always", "normal": "never", "component": "always"}, "svg": "always", "math": "always"}], "@typescript-eslint/explicit-module-boundary-types": 0, "@typescript-eslint/no-explicit-any": 0, "@typescript-eslint/camelcase": 0, "@typescript-eslint/no-non-null-assertion": 0, "@typescript-eslint/member-delimiter-style": [0, {"multiline": {"delimiter": "none", "requireLast": true}, "singleline": {"delimiter": "semi", "requireLast": false}}], "@typescript-eslint/no-unused-vars": [2, {"args": "none"}], "@typescript-eslint/interface-name-prefix": 0, "@typescript-eslint/explicit-function-return-type": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-var-requires": 0, "@typescript-eslint/no-use-before-define": 0}}, "eslintIgnore": ["/*.js", "/src/assets/*", "/src/config/*", "/src/local/*", "/src/router/*", "/src/store/*", "/src/views/element/service/custom/modals/*", "/src/views/dataAccess/*", "/src/views/expimp/*", "/src/views/org-ast/*", "/src/common/*", "/src/shims-vue.d.ts", "/src/shims-tsx.d.ts", "/src/pro-form1.js"], "prettier": {"printWidth": 100, "tabWidth": 2, "singleQuote": true, "semi": true, "trailingComma": "none", "bracketSpacing": true, "jsxBracketSameLine": false, "arrowParens": "always", "endOfLine": "lf"}}