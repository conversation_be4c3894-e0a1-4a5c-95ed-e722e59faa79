<template>
  <bs-dialog
    size="large"
    :title="title"
    class="monitor-dialog"
    :visible.sync="display"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    @close="closeDialog(false)"
    @confirm="saveConfigData"
  >
    <div v-loading="loading" :element-loading-text="$t('pa.flow.comLoading')" class="monitor__container">
      <!-- 操作按钮 -->
      <div v-if="!disabled" class="monitor__btns">
        <el-button @click="addNewData">{{ $t('pa.flow.createRow') }}</el-button>
      </div>
      <!-- 表单 -->
      <config-table ref="tableRef" :disabled="disabled" :data.sync="tableData" @click="handleClick" />
      <!-- 配置明细 -->
      <logic-config
        v-if="showConfigDialog"
        :show.sync="showConfigDialog"
        :cache.sync="cache"
        :data="configData"
        :unid="configUuid"
        :title="configTitle"
        :disabled="false"
        :field-list="fieldList"
        @confirm="handleConfigChange"
      />
      <!-- 测试 -->
      <test-dialog v-if="showTestDialog" :show.sync="showTestDialog" :data="testData" />
    </div>
    <print-log slot="footer-left" v-model="printLog" :disabled="disabled" />
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import cloneDeep from 'lodash/cloneDeep';
import TestDialog from './test-dialog.vue';
import ConfigTable from './config-table.vue';
import LogicConfig from '@/components/logic-expression-config/index.vue';
import PrintLog from '../components/print-log.vue';
const uuid = () => {
  const url = URL.createObjectURL(new Blob([]));
  URL.revokeObjectURL(url);
  return url.substring(url.lastIndexOf('/') + 1);
};

@Component({ components: { ConfigTable, LogicConfig, TestDialog, PrintLog } })
export default class MonitorDelay extends Vue {
  @Prop() jobData!: any;
  @Prop({ default: '' }) orgId!: string;
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: false }) disabled!: boolean;
  @PropSync('show', { default: false }) display!: boolean;
  @Ref('tableRef') readonly table!: ConfigTable;

  private loading = false;
  private fieldList: any[] = [];
  private outputFields: any[] = [];
  /* about config dialog start*/
  private showConfigDialog = false;
  private configTitle = '';
  private configUuid: number | string = '';
  private configData: any = {};
  private cache = {};
  /* about config dialog end*/
  private showTestDialog = false;
  private testData: any = {};
  private printLog = false; //  是否开启日志
  /* 配置数据 */
  private tableData: any[] = [];

  get title() {
    const { nodeName = '', componentName = '' } = this.data;
    return this.$t('pa.flow.msg288', [nodeName, componentName]);
  }
  async created() {
    try {
      this.loading = true;
      /* 是否开启日志 */
      this.printLog = Boolean(this.data.printLog);
      /* 上游字段 */
      this.handleUpstreamField(this.data.inputFields);
      /* 配置数据处理 */
      this.handleConfigData(this.data.properties);
      this.loading = false;
    } catch (e) {
      this.loading = false;
      console.log(e);
    }
  }

  handleUpstreamField(data: any[] = []) {
    if (!Array.isArray(data) || data.length < 1) return;
    const [fieldList = [], outputFields = []] = data.reduce((pre: any, { name, type }: any) => {
      if (name && type) {
        if (!pre[0]) pre[0] = [];
        if (!pre[1]) pre[1] = [];
        pre[0].push({ label: name, value: name, type });
        pre[1].push({ name, type, outputable: true });
      }
      return pre;
    }, []);
    this.fieldList = fieldList;
    this.outputFields = outputFields;
  }
  handleConfigData({ rules = [], newRules = [] }: any = {}) {
    const result = [...rules, ...newRules];
    result.forEach((el) => !el.id && (el.id = uuid()));
    this.tableData = result;
    if (this.tableData.length < 1) this.addNewData();
  }
  addNewData() {
    this.tableData.push({
      name: '',
      used: false,
      id: uuid(),
      mvel: {
        expr: '',
        logicType: '&&',
        resultExpression: '',
        conditions: []
      }
    });
    this?.table?.goLastPage();
  }
  handleClick(type: string, row: any) {
    const fn = this[`${type}EventHandler`];
    return typeof fn === 'function' ? fn(row) : null;
  }
  editEventHandler(row: any) {
    this.configTitle = row.name;
    this.configUuid = row.id;
    this.configData =
      typeof row.mvel === 'string'
        ? {
            expr: '',
            logicType: '&&',
            resultExpression: '',
            conditions: []
          }
        : { ...row.mvel };
    this.showConfigDialog = true;
  }
  testEventHandler(row: any) {
    this.testData = { ...row };
    this.showTestDialog = true;
  }
  async deleteEventHandler(row: any) {
    await this.$confirm(this.$t('pa.flow.delMsg1'), this.$t('pa.flow.tip'));
    const index = this.tableData.findIndex(({ id }) => id == row.id);
    if (index > -1) {
      this.tableData.splice(index, 1);
      this.table.handleCurrentPage();
    }
  }
  handleConfigChange(data: any, uuid: number | string) {
    if (data && String(uuid)) {
      const index = this.tableData.findIndex(({ id }) => id == uuid);
      index > -1 && this.$set(this.tableData[index], 'mvel', data);
    }
  }
  saveConfigData() {
    const nameList: any[] = [];
    for (const i in this.tableData) {
      if (!this.isRight(this.tableData[i])) {
        return this.$tip.error(this.$t('pa.flow.msg125', [Number(i) + 1]));
      }
      if (nameList.includes(this.tableData[i].name)) {
        return this.$tip.error(this.$t('pa.flow.msg126', [Number(i) + 1]));
      }
      nameList.push(this.tableData[i].name);
    }
    this.closeDialog(true);
  }
  isRight({ name, mvel }: any) {
    return name && typeof mvel === 'string' ? mvel : mvel.expr && mvel.logicType && mvel.resultExpression && mvel.conditions;
  }
  closeDialog(needUpdate = false) {
    if (!needUpdate) return this.$emit('close', { needUpdate, jobNode: null });
    const jobNode = cloneDeep(this.data);
    jobNode.outputFields = cloneDeep(this.outputFields);
    const [rules = [], newRules = []] = this.tableData.reduce((pre: any, next: any) => {
      const index = typeof next.mvel === 'string' ? 0 : 1;
      if (!pre[index]) pre[index] = [];
      pre[index].push({ ...next });
      return pre;
    }, []);
    jobNode.printLog = this.printLog;
    jobNode.properties = { rules, newRules };
    this.$emit('close', { needUpdate, jobNode });
  }
}
</script>
<style lang="scss" scoped>
.monitor {
  &-dialog {
    ::v-deep .el-dialog__body {
      padding: 0 10px;
      min-height: 490px !important;
    }
  }
  &-container {
    margin-top: 20px;
    padding: 0 20px;
  }
  &__btns {
    padding: 7px 0;
    text-align: right;
  }
}
</style>
