<template>
  <el-descriptions
    :column="isEn ? 3 : column"
    class="info-desc__container"
    label-class-name="info-desc__label"
    content-class-name="info-desc__content"
  >
    <el-descriptions-item v-for="it in renderList" :key="it.value" :label="it.label">
      {{ getValue(it) }}
    </el-descriptions-item>
  </el-descriptions>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

interface RenderItem {
  label: string;
  value: string;
  unit?: string;
}

@Component
export default class InfoDesc extends Vue {
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: 4 }) column!: number;

  renderList: RenderItem[] = [
    {
      label: this.$t('pa.flow.label56'),
      value: 'backPressure'
    },
    {
      label: this.$t('pa.flow.label57'),
      value: 'checkpointFails'
    },
    {
      label: this.$t('pa.flow.label58'),
      value: 'checkpointDurationMax'
    },
    {
      label: this.$t('pa.flow.label59'),
      value: 'checkpointDurationAvg'
    },
    {
      label: this.$t('pa.flow.label60'),
      value: 'taskmanagerMemoryMax'
    },
    {
      label: this.$t('pa.flow.label61'),
      value: 'jobmanagerMemoryMax'
    },
    {
      label: this.$t('pa.flow.label62'),
      value: 'gCCount'
    },
    {
      label: this.$t('pa.flow.label63'),
      value: 'duration'
    },
    {
      label: this.$t('pa.flow.label64'),
      value: 'inputBytes'
    },
    {
      label: this.$t('pa.flow.label65'),
      value: 'outputBytes'
    }
  ];
  getValue({ value }: RenderItem) {
    return String(this.data[value]) ? this.data[value] : '-';
  }
}
</script>
<style lang="scss" scoped>
.info-desc {
  &__container {
    margin-top: 20px;
    ::v-deep .el-descriptions-item__container {
      margin-bottom: 25px;
      .info-desc {
        &__label,
        &__content {
          font-size: 12px;
          color: $--bs-color-text-primary;
        }
      }
    }
  }
}
</style>
