<template>
  <pro-page :title="title" :loading="loading">
    <!-- operation -->
    <el-button v-if="showRefreshSource" slot="operation" type="primary" @click="handleRefreshSource">
      {{ $t('pa.refreshSource') }}
    </el-button>
    <!-- body -->
    <div direction="column" class="service_detail_main">
      <!-- 基本信息 -->
      <base-info :config="config" :data="data" />
      <!--其他组件-->
      <template v-for="it in componentList">
        <component
          :is="it.component"
          :key="`${commonKey}_${it.name}`"
          :data="data"
          :params="it.params"
          :share-flag="shareFlag"
          @refresh="handleRefreshSource(false)"
        />
      </template>
    </div>
  </pro-page>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { getServerData, getServerConfig, refreshCluster } from '@/apis/serviceApi';
import { safeParse, safeArray } from '@/utils';
import { getComponentName } from './utils';

@Component({
  components: {
    BaseInfo: () => import('./components/base-info.vue'),
    DependRelation: () => import('./components/depend-relation.vue'),
    NodeInfo: () => import('./components/node-info.vue'),
    WarnRules: () => import('./components/warn-rules.vue'),
    TableView: () => import('./components/table-view.vue'),
    FlinkJobManagerInfo: () => import('./components/flink/jobmanager-info.vue'),
    QueueResourceInfo: () => import('./components/flink/per-job-res-used.vue'),
    FlinkResAllocate: () => import('./components/flink/res-allocate.vue'),
    FlinkTaskManagerInfo: () => import('./components/flink/taskmanager-info.vue'),
    FlinkUseInfo: () => import('./components/flink/use-info.vue'),
    CacheInfo: () => import('./components/aerospike/cache-info.vue'),
    CacheData: () => import('./components/aerospike/get-cache-data.vue'),
    TopicManager: () => import('./components/kafka/topic-manager.vue'),
    ConsumerManager: () => import('./components/kafka/consumer-manager.vue'),
    RedisCacheInfo: () => import('./components/redis/cache-info.vue'),
    RedisCacheData: () => import('./components/redis/get-cache-data.vue'),
    ScriptTable: () => import('./components/stream-cube/script-table.vue'),
    UdfTable: () => import('./components/stream-cube/udf-table.vue'),
    UdjTable: () => import('./components/stream-cube/udj-table.vue')
  }
})
export default class ServiceDetail extends Vue {
  loading = true;
  title = this.$t('pa.serviceDetail');
  commonKey: number = Date.now();
  id = '';
  resType = '';
  data: any = {};
  config: any = {};
  componentList: any[] = [];
  clusterType = '';
  shareFlag = false;

  get showRefreshSource() {
    return this.$route.params.type === 'FLINK';
  }
  get confFileName() {
    if (this.$route.query.resType === 'FLINK') {
      return (
        {
          standalone: 'detail',
          yarn_application: 'detail-yarn-application'
        }[(this.$route.query.clusterType as string).toLocaleLowerCase()] || 'detail'
      );
    }
    return 'detail';
  }

  async created() {
    try {
      this.loading = true;
      this.title = this.$route.query.title as string;
      this.id = this.$route.query.id as string;
      this.resType = this.$route.query.resType as string;
      this.clusterType = this.$route.query.clusterType as string;
      await this.getDetailData();
      await this.getDetailConf();
    } finally {
      this.loading = false;
    }
  }
  /* 获取服务数据详情 */
  async getDetailData() {
    const { success, data, error } = await getServerData(this.id, this.resType === 'HOST');
    if (!success) return this.$message.error(error);
    this.data = data;
    this.shareFlag = !!data.shareFlag;
  }
  /* 获取服务详情配置 */
  async getDetailConf() {
    const { success, data, error } = await getServerConfig(this.resType, this.confFileName);
    if (!success) return this.$message.error(error);
    const newData = safeParse(data);
    this.config = newData?.forms || {};
    const list = safeArray(newData?.components)
      .map((it) => safeArray(it?.children))
      .flat(2);
    const result: any[] = [];
    for (const it of list) {
      it.component = getComponentName(it.file);
      result.push(it);
    }
    this.componentList = result;
  }
  /* 刷新资源使用情况后数据刷新 */
  async handleRefreshSource(tip = true) {
    const res: any = await refreshCluster(this.id);
    tip && this.$message.success(res.msg);
    this.commonKey = Date.now();
  }
}
</script>
<style scoped lang="scss">
::v-deep .bs-pro-grid {
  margin: 10px 0;
  width: 100%;
}
::v-deep .bs-table {
  width: 100%;
}
.service_detail {
  &_main {
    margin-top: 20px;
    width: 100%;
    overflow: hidden;
  }
}
</style>
