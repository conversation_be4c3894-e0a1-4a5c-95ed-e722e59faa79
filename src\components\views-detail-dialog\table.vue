<template>
  <div v-loading="loading" class="table-detail">
    <!-- 服务信息 -->
    <!-- 标题 -->
    <pro-grid type="info" title="服务信息">
      <!-- 内容 -->
      <div class="table-content">
        <!-- 信息 -->
        <div class="infoDetail">
          <div>服务类型：{{ serviceInfo.type }}</div>
          <div>服务：{{ serviceInfo.name }}</div>
          <div v-if="serviceInfo.key1">{{ serviceInfo.key1 }}：{{ serviceInfo.value1 }}</div>
          <div v-if="serviceInfo.key2">{{ serviceInfo.key2 }}：{{ serviceInfo.value2 }}</div>
          <div v-if="serviceInfo.orgCodeFiled">组织机构字段：{{ serviceInfo.orgCodeFiled }}</div>
        </div>
      </div>
    </pro-grid>
    <!-- 表信息 -->
    <pro-grid type="info" title="表信息">
      <!-- 信息 -->
      <div class="infoDetail table-info">
        <div v-for="el in tableInfoRenderList" :key="el.name" class="table-info__content__inline">
          {{ el.label }}: {{ tableInfo[el.name] }}
        </div>
      </div>
    </pro-grid>
    <!-- 字段信息 -->
    <pro-grid type="info" title="字段信息">
      <!-- 信息 -->
      <div style="width: 100%">
        <div class="table-info__content__advanced-title">基础字段</div>
        <el-table size="mini" :data="fieldInfo" class="table-info__table">
          <!-- 字段名 -->
          <el-table-column prop="fieldName" min-width="130" align="center">
            <template slot="header">
              <span class="table-info__table__required">字段名</span>
            </template>
            <template slot-scope="{ row }">
              {{ row.fieldName }}
            </template>
          </el-table-column>
          <!-- 中文名 -->
          <el-table-column prop="fieldNameCn" min-width="130" align="center">
            <template slot="header">
              <span class="table-info__table__required">中文名</span>
            </template>
            <template slot-scope="{ row }">
              {{ row.fieldNameCn }}
            </template>
          </el-table-column>
          <!-- 字段类型 -->
          <el-table-column prop="fieldType" label="字段类型" min-width="130" align="center">
            <template slot-scope="{ row }">
              {{ row.fieldType }}
            </template>
          </el-table-column>
          <!-- 列蔟 -->
          <el-table-column
            v-if="type === 'HBASE'"
            prop="columnFamily"
            label="列簇"
            min-width="130"
            align="center"
          >
            <template slot-scope="{ row }">
              {{ row.columnFamily }}
            </template>
          </el-table-column>
          <el-table-column prop="primaryKey" label="主键" min-width="130" align="center">
            <template slot-scope="{ row }">
              <el-switch :value="row.primaryKey" active-value="1" inactive-value="0" />
            </template>
          </el-table-column>
          <el-table-column
            v-if="type === 'HIVE'"
            prop="partition"
            label="分区"
            min-width="130"
            align="center"
          >
            <template slot-scope="{ row }">
              <el-switch :value="row.partition" active-value="1" inactive-value="0" />
            </template>
          </el-table-column>
          <el-table-column prop="businessExplain" min-width="130" align="center">
            <template slot="header">
              <span>业务口径</span>
            </template>
            <template slot-scope="{ row }">
              {{ row.businessExplain }}
            </template>
          </el-table-column>
        </el-table>
        <div class="table-info__content__advanced">
          <div class="table-info__content__advanced-title">高级字段</div>
          <el-table size="mini" :data="advanceTableField" class="table-info__table">
            <el-table-column prop="type" label="字段分类" min-width="130">
              <template slot-scope="{ row }">
                {{ getType(row.advanceFieldType, typeList) }}
              </template>
            </el-table-column>
            <el-table-column prop="type" label="字段名" min-width="130">
              <template slot-scope="{ row }">
                <div v-if="row.advanceFieldType === 'WATERMARK'">
                  <span style="margin-right: 100px">{{ row.field }}</span>
                  {{ row.column1 }} {{ getType(row.column2, timeType) }}
                </div>
                <div v-else>
                  {{ row.field }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </pro-grid>
    <!-- 连接器 -->
    <pro-grid type="info" :title="`连接器 ${this.connectorName}`">
      <div style="width: 100%">
        <div class="table-info__content__advanced-title">基础连接器</div>
        <!-- 信息 -->
        <el-table
          v-if="!flagMap.includes('connector')"
          size="mini"
          :data="connectList"
          class="table-info__table"
        >
          <el-table-column prop="name" label="属 性" min-width="130" align="center">
            <template slot-scope="{ row }">
              <span class="table-info__table__attr">{{ row.name }}</span>
              <el-tooltip class="item" effect="light" :content="row.keyExplain" placement="top">
                <i class="iconfont icon-wenhao data-card-icon"></i>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="defaultValue" label="值" min-width="130" align="center">
            <template slot-scope="{ row }">
              <el-input v-model="row.defaultValue" disabled />
            </template>
          </el-table-column>
        </el-table>
        <div class="table-info__content__advanced">
          <div class="table-info__content__advanced-title">高级连接器</div>
          <el-table size="mini" :data="advanceConnectorInfo" class="table-info__table">
            <el-table-column prop="key" label="连接器属性" min-width="130" align="center">
              <template slot-scope="{ row }">
                <el-input v-model="row.key" disabled />
              </template>
            </el-table-column>
            <el-table-column prop="value" label="值" min-width="130" align="center">
              <template slot-scope="{ row }">
                <el-input v-model="row.value" disabled />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </pro-grid>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import {
  URL_CONNECTORINFO,
  URL_FINDBYCODE,
  URL_GETSUBBYPARENT,
  URL_TABLE_FIND_BY_ID,
  URL_TABLE_RESCONF
} from '@/apis/commonApi';
import { get } from '@/apis/utils/net';

@Component
export default class TableDetail extends PaBase {
  @Prop({ type: String, default: '' }) id!: string;
  @Prop({ type: Boolean, default: true }) showAdvancedField!: boolean;
  loading = false;
  flagMap: any[] = [];
  serviceInfo: any = {
    type: '', // 服务类型
    name: '', // 服务
    orgCodeFiled: '', // 组织机构
    type1: null, // level1 类型
    key1: null, // level1 label
    value1: null, //  level1 model 对应数据
    key2: null, // level2label
    value2: null //  level2 model 对应数据
  }; // 服务信息
  tableInfo = {
    tableName: '',
    tableNameCn: '',
    createdBy: '',
    createdByMobile: '',
    businessExplain: '',
    label: ''
  };
  tableInfoRenderList: any[] = [
    {
      label: '表名',
      name: 'tableName'
    },
    {
      label: '中文名',
      name: 'tableNameCn'
    },
    {
      label: '创建人',
      name: 'createdBy'
    },
    {
      label: '创建人电话',
      name: 'createdByMobile'
    },
    {
      label: '业务口径',
      name: 'businessExplain'
    },
    {
      label: '标签',
      name: 'label'
    }
  ];
  fieldInfo = []; // 字段信息
  typeEnums: any = [];
  connectList: any[] = []; // 连接器字段
  connectorId = ''; // 选中连接器id
  connectorName = ''; //连接器名字
  connector: any[] = []; // 连接器列表
  connectorMapping = {}; // 连接器id映射
  advanceTableField: any = [];
  advanceConnectorInfo: any = [];
  tableAttributes: any = []; // 表属性
  type = ''; //
  typeList: any = [
    {
      label: '水位线',
      value: 'WATERMARK'
    },
    {
      label: '处理时间',
      value: 'PROCTIME'
    },
    {
      label: '自定义字段',
      value: 'OTHER'
    }
  ];
  timeType: any = [
    {
      label: '时',
      value: 'HOUR'
    },
    {
      label: '分',
      value: 'MINUTE'
    },
    {
      label: '秒',
      value: 'SECOND'
    }
  ];
  getType(type, typeList) {
    const value = typeList.find((item) => item.value === type);
    return value && value['label'];
  }
  created() {
    this.getFieldType();
    this.getTableDetail();
  }

  /* 获取字段类型 */
  async getFieldType() {
    try {
      const { success, data, error } = await get(URL_FINDBYCODE);
      if (success) {
        const info = JSON.parse(data.value1);
        const arr = Array.isArray(info) ? info : [];
        this.typeEnums = arr.map(({ value, label }: any) => ({ value, label }));
        return;
      }
      this.$message.error(error);
    } catch (e) {
      this.typeEnums = [];
    }
  }

  /* 获取表详情 */
  async getTableDetail() {
    this.loading = true;
    const { id } = this;
    if (!id) {
      this.$message.error('id不能为空');
      return;
    }
    const { success, data, error } = await get(URL_TABLE_FIND_BY_ID, { id });
    if (success) {
      const {
        tableNamePrefix: prefix,
        tableName: name,
        tableNameCn,
        createdBy,
        createdByMobile,
        businessExplain,
        label,
        resType,
        advanceFieldInfo,
        advanceConnectorInfo,
        advanceProperty,
        connectorId,
        connectorInfo,
        baseFieldInfo
      } = data;
      /* 表信息 */
      const tableName = prefix + (name.includes(prefix) ? name.replace(prefix, '') : name);
      this.tableInfo = {
        ...this.tableInfo,
        tableName,
        tableNameCn,
        createdBy,
        createdByMobile,
        businessExplain,
        label
      };
      this.connectorId = connectorId;
      this.type = resType;
      /* 服务信息 */
      this.getServiceInfo(data);
      /* 字段信息 */
      this.handleFieldInfo(baseFieldInfo);
      /* 连接器 */
      this.getConnector(resType, connectorInfo);
      /* 高级 表字段 */
      this.handleAdvanceTableField(advanceFieldInfo);
      /* 高级 连接器 */
      this.handleAdvanceConnectorInfo(advanceConnectorInfo);
      /* 表属性 */
      this.handleTableAttributes(advanceProperty);
      return;
    }
    this.loading = false;
    this.$message.error(error);
  }

  async getServiceInfo(rootData: any) {
    const { resType, resName, orgCodeFiled, isDataPermission } = rootData;
    this.serviceInfo.type = resType;
    this.serviceInfo.name = resName;
    const { success, data, error } = await get(
      `${URL_TABLE_RESCONF}${this.type.toLowerCase()}/sql.json`
    );
    if (success) {
      const { level1 = {}, openDataAuth = {} } = data;
      const { label, type, model, componentCfg = {} } = level1;
      this.serviceInfo.type1 = type; // 一级type
      this.serviceInfo.key1 = label ? label : null; // 一级key
      this.serviceInfo.value1 = model && rootData[model] ? rootData[model] : null; // 一级value
      if (type === 'cascader') {
        const { level2 = {} } = componentCfg;
        const { label: label2, model: model2 } = level2;
        this.serviceInfo.key2 = label2 ? label2 : null; // 二级key
        this.serviceInfo.value2 = model2 && rootData[model2] ? rootData[model2] : null; // 二级value
      }
      if (isDataPermission && openDataAuth.show) {
        this.serviceInfo.orgCodeFiled = orgCodeFiled;
      }
      return;
    }
    this.$message.error(error);
  }

  handleFieldInfo(infoStr = '') {
    try {
      const info = JSON.parse(infoStr);
      this.fieldInfo = info.map(
        ({
          fieldName,
          fieldNameCn,
          fieldType,
          primaryKey,
          partition,
          businessExplain,
          columnFamily
        }: any) => {
          const temp: any = {
            fieldName,
            fieldNameCn,
            fieldType,
            primaryKey,
            partition,
            businessExplain
          };
          if (this.type === 'HBASE') {
            temp['columnFamily'] = columnFamily;
          }
          return temp;
        }
      );
    } catch (e) {
      this.fieldInfo = [];
    }
  }

  handleFlag(name: string) {
    const index = this.flagMap.findIndex((el) => el === name);
    if (index > -1) {
      this.flagMap.splice(index, 1);
    } else {
      this.flagMap.push(name);
    }
  }

  generateClass(name: string) {
    const style = ['businessExplain', 'label'].includes(name) ? 'block' : 'inline';
    return `table-info__content__${style}`;
  }

  handleConnector(infoStr = '', connectorInfo) {
    try {
      const info = JSON.parse(infoStr).filter(({ needHide }: any) => !needHide);
      return info.map((el: any) => {
        const { name, defaultValue } = el;
        if (name === 'database') {
          el.defaultValue = (connectorInfo && connectorInfo[name]) || defaultValue || 0;
        } else {
          el.defaultValue = (connectorInfo && connectorInfo[name]) || defaultValue || '';
        }
        return el;
      });
    } catch (e) {
      return [];
    }
  }

  async getConnector(type = '', connectorInfoStr = '') {
    if (!type) {
      return;
    }
    try {
      const { success, data, error } = await get(`${URL_CONNECTORINFO}${type}`);
      const connectorInfo = JSON.parse(connectorInfoStr);
      if (success) {
        const arr = Array.isArray(data) ? data : [];
        this.connector = arr.map(({ componentName, id }: any) => ({
          componentName,
          id
        }));
        this.connectorName = this.connector.find(
          (item) => item.id === this.connectorId
        ).componentName;
        this.connectorMapping = arr.reduce((pre: any, { id, properties }: any) => {
          pre[id] = this.handleConnector(properties, connectorInfo);
          return pre;
        }, {});
        const list = this.connectorMapping[this.connectorId];
        this.connectList =
          this.fieldInfo.length !== 3 ? list.filter((item) => item.name !== 'field-column') : list;
        this.loading = false;
        return;
      }
      this.connector = [];
      this.connectList = [];
      this.$message.error(error);
    } catch (e) {
      this.connector = [];
      this.connectList = [];
    }
  }

  handleAdvanceTableField(infoStr = '') {
    try {
      const info = JSON.parse(infoStr);
      this.advanceTableField = info;
    } catch (e) {
      this.advanceTableField = [];
    }
  }

  handleAdvanceConnectorInfo(infoStr = '') {
    try {
      const info = JSON.parse(infoStr);
      this.advanceConnectorInfo = info.map((el: any) => {
        const [key] = Object.keys(el);
        return {
          key,
          value: el[key]
        };
      });
    } catch (e) {
      this.advanceConnectorInfo = [];
    }
  }

  async handleTableAttributes(infoStr = '') {
    try {
      const { success, data, error } = await get(URL_GETSUBBYPARENT);
      const info = JSON.parse(infoStr);
      if (success) {
        const arr = Array.isArray(data) ? [...data] : [];
        this.tableAttributes = arr.map(({ title, value1, code: key }: any) => {
          const temp = { title, value1, key, value: '' };
          const el = info.find((item) => Object.keys(item).includes(key));
          temp.value = el && el[key] ? el[key] : '';
          return temp;
        });
        return;
      }
      this.$message.error(error);
    } catch (e) {
      this.tableAttributes = [];
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .bs-pro-grid--info {
  width: 97.5%;
  margin-bottom: 10px;
}
::v-deep .bs-pro-grid__header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 0 16px;
  height: 50px;
  background: #fff;
  ::v-deep &-title {
    flex: inherit;
  }
}
::v-deep .bs-pro-grid__content {
  margin: 0 16px;
  padding: 10px 25px;
  font-size: 12px;
  background: #fff;
}
.table {
  &-detail {
    padding-bottom: 30px;
    text-align: left;
  }
  &-info {
    margin: 0 0 10px;
    /* 标题 */
    &__title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 16px;
      height: 50px;
      background: #fff;
      border: 1px solid #e2e2e2;

      &__text {
        margin: 0 0 0 16px;
        padding: 0 0 0 6px;
        color: #666;
        font-size: 14px;
        font-weight: bolder;
        border-left: 3px solid #2196f3;
      }

      &__button {
        margin: 0 10px 0 0;
      }

      &__icon {
        margin: 0 15px 0 0;
      }

      &__flag {
        display: flex;
        align-items: center;
        cursor: pointer;

        i {
          margin-left: 15px;
          font-size: 18px;
        }
      }
    }

    /* 内容 */
    &__content {
      margin: 0 16px;
      padding: 20px 25px;
      font-size: 12px;
      background: #fff;

      > div {
        margin-bottom: 30px;
      }

      &__block {
        display: block;
        margin-top: 20px;
      }

      &__inline {
        display: inline-block;
        margin-top: 20px;
        width: 25%;
      }

      &__advanced {
        margin: 20px 0;

        &-title {
          margin: 0 0 10px 0;
          font-size: 12px;
        }
      }
    }

    &__table {
      width: 100%;
      max-height: 400px;
      overflow-y: auto;

      &__required {
        position: relative;
        padding: 0 0 0 10px;

        &::before {
          content: '*';
          position: absolute;
          top: 0;
          left: 0;
          color: red;
        }
      }

      &__attr {
        display: inline-block;
        margin: 0 10px 0 0;
      }
    }
  }
}

.chartInfoDetail {
  .item {
    margin-bottom: 30px;
  }

  .top {
    div {
      width: 240px;
    }
  }
}
.service-row {
  display: flex;
}
.table-list {
  padding: 1px;
  min-height: 300px;
  max-height: 500px;
  // overflow-y: auto;
  background: #fff;
  .even:nth-child(even) {
    background: #fafdff;
  }
  .advanced-item {
    border: 1px solid #e2e2e2;
    margin: 10px;
    padding: 15px;
    .item-title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
  }

  .el-row {
    height: 50px;
    line-height: 50px;

    .grid-content {
      display: flex;
      justify-content: center;
      align-items: center;
      min-width: 210px;
      .data-card-icon {
        font-size: 19px;
      }
    }
  }

  .el-row .grid-content {
    text-align: center;
  }
}
.connectList {
  min-height: 100px;
  overflow-y: hidden;
  overflow-x: hidden;
}
.table-info {
  margin-top: -20px;
}
.infoDetail {
  display: flex;
  flex-wrap: wrap;
  div {
    width: 280px;
  }
}
.advanced {
  width: 100%;
}
.connector {
  float: left;
}
</style>
