<template>
  <div :key="key" :class="['table-info__container', isEn ? 'table-info__container-en' : '']">
    <!-- ​基础信息 -->
    <div class="table-info-title">{{ $t('pa.flow.baseInfo') }}</div>
    <!-- ​基础信息 内容 -->
    <ul class="table-info__content">
      <div class="table-info__flex">
        <!-- 表名 -->
        <div class="table-info-item">
          <div class="table-info-item__label">{{ $t('pa.flow.tableName') }}：</div>
          <el-tooltip v-hide effect="light" placement="top" :content="data.tableName">
            <div class="table-info-item__value">{{ data.tableName }}</div>
          </el-tooltip>
        </div>
        <!-- 中文名 -->
        <div class="table-info-item">
          <div class="table-info-item__label">{{ $t('pa.flow.chineseName') }}：</div>
          <el-tooltip v-hide effect="light" placement="top" :content="data.tableNameCn">
            <div class="table-info-item__value">{{ data.tableNameCn }}</div>
          </el-tooltip>
        </div>
      </div>
      <!-- 业务口径 -->
      <div class="table-info-item">
        <div class="table-info-item__label">{{ $t('pa.flow.business') }}：</div>
        <el-tooltip v-hide effect="light" placement="top" :content="data.businessExplain">
          <div class="table-info-item__value">{{ data.businessExplain }}</div>
        </el-tooltip>
      </div>
    </ul>
    <!-- ​服务信息 -->
    <div class="table-info-title">{{ $t('pa.flow.serveInfo') }}</div>
    <!-- ​服务信息 内容 -->
    <ul class="table-info__content">
      <div class="table-info__flex">
        <!-- 服务类型 -->
        <div class="table-info-item">
          <div class="table-info-item__label">{{ $t('pa.flow.serveType') }}：</div>
          <el-tooltip v-hide effect="light" placement="top" :content="data.serviceType">
            <div class="table-info-item__value">{{ data.serviceType }}</div>
          </el-tooltip>
        </div>
        <!-- 服务 -->
        <div class="table-info-item">
          <div class="table-info-item__label">{{ $t('pa.flow.serve') }}：</div>
          <el-tooltip v-hide effect="light" placement="top" :content="data.serviceName">
            <div class="table-info-item__value">{{ data.serviceName }}</div>
          </el-tooltip>
        </div>
      </div>
      <!-- serviceLabel -->
      <div v-if="data.serviceLabel" class="table-info-item">
        <div class="table-info-item__label" :title="isEn && data.serviceLabel">{{ data.serviceLabel }}：</div>
        <el-tooltip v-hide effect="light" placement="top" :content="data.serviceValue">
          <div class="table-info-item__value">{{ data.serviceValue }}</div>
        </el-tooltip>
      </div>
      <!-- 服务地址 -->
      <div class="table-info-item">
        <div class="table-info-item__label">{{ $t('pa.flow.serveAddress') }}：</div>
        <el-tooltip v-hide effect="light" placement="top" :content="data.serviceAddress">
          <div class="table-info-item__value">{{ data.serviceAddress }}</div>
        </el-tooltip>
      </div>
    </ul>
    <!-- 连接器 -->
    <div class="table-info-title">{{ data.connectorName }}{{ $t('pa.flow.connector') }}</div>
    <div class="table-info-subtitle">{{ $t('pa.flow.baseConnector') }}</div>
    <!-- 基础连接器 -->
    <bs-table :column-settings="false" :data="data.baseConnector" :column-data="columnData" />
    <div class="table-info-subtitle">{{ $t('pa.flow.hignConnector') }}</div>
    <!-- 高级连接器 -->
    <bs-table :column-settings="false" :data="data.advancedConnector" :column-data="columnData" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { CONNECTOR_COLUMN_DATA, SHA256 } from './utils';

@Component
export default class TableInfo extends Vue {
  @Prop({ default: () => ({}) }) data!: string;

  get key() {
    return SHA256(JSON.stringify(this.data));
  }

  columnData = CONNECTOR_COLUMN_DATA;
}
</script>

<style lang="scss" scoped>
$labelWidth: 80px;
.table-info {
  &__container {
    margin: 4px 0 20px;
    color: #444444;
  }
  &-title {
    padding: 0 8px;
    border-bottom: 1px solid #f1f1f1;
    font-size: 14px;
    font-weight: 500;
    color: #444444;
    line-height: 32px;
    &:before {
      content: ' ';
      display: inline-block;
      position: relative;
      left: 0;
      top: -1px;
      margin-right: 12px;
      width: 8px;
      height: 8px;
      background: #ff9c00;
    }
  }
  &-subtitle {
    padding-left: 12px;
    border-bottom: 1px solid #f1f1f1;
    line-height: 40px;
    box-sizing: border-box;
  }
  &__content {
    padding: 4px 20px;
    font-weight: 400;
    font-size: 14px;
    color: #444;
    border-bottom: 1px solid #ebebeb;
    box-sizing: border-box;
  }
  &__flex {
    display: flex;
    align-items: center;
    .table-info-item {
      flex: 1;
    }
  }
  &-item {
    display: flex;
    align-items: center;
    line-height: 32px;
    overflow: hidden;

    &__label {
      max-width: $labelWidth;
    }
    &__value {
      width: calc(100% - $labelWidth);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #777777;
    }
  }
  &__container-en &-item__label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
    flex-shrink: 0;
  }
}
</style>
