<template>
  <pro-page :title="$t('pa.params.template.historicalFilterTemplate')" :fixed-header="false" class="filter-history">
    <pro-table v-loading="tableLoading" :columns="columnData" :request="request" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { post } from '@/apis/utils/net';
@Component({
  name: 'ElementFilterTemplateHistory'
})
export default class ElementFilterTemplateHistory extends Vue {
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 }
  };

  columnData = [];
  // 路由拼接id
  get id() {
    return this.$route.query.id as string;
  }
  async request() {
    this.tableLoading = true;
    try {
      // 获取模板列表
      const { data, msg, success } = await post(`/rs/pa/filter/versionList?id=${this.id}`, this.searchObj);
      if (success) {
        data.columnData.forEach((el) => {
          if (el.prop) {
            el.value = el.prop;
          }
          if (el.value === 'updateTime') el.valueProps = { type: 'datetime' };
        });
        this.columnData = data.columnData;
        this.tableLoading = false;
        return { data: data.tableData, total: data.pageData.total };
      } else {
        this.$message.error(msg);
      }
      this.tableLoading = false;
    } catch {
      this.tableLoading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-history {
  height: calc(100vh - 114px);
  background: #fff;
}
</style>
