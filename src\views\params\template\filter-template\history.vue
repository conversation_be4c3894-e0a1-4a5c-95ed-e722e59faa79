<template>
  <pro-page title="历史过滤模板" :fixed-header="false" class="filter-history">
    <pro-table v-loading="tableLoading" :columns="columnData" :request="request" />
  </pro-page>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { post } from '@/apis/utils/net';
@Component({
  name: 'ElementFilterTemplateHistory'
})
export default class ElementFilterTemplateHistory extends PaBase {
  // 路由拼接id
  id = '';
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 }
  };
  tableData: ITableData = {
    columnData: [],
    tableData: []
  };
  columnData = [];
  created() {
    this.id = this.$route.query.id as string;
  }
  async request() {
    this.tableLoading = true;
    try {
      // 获取模板列表
      const { data, msg, success } = await post(
        `/rs/pa/filter/versionList?id=${this.id}`,
        this.searchObj
      );
      if (success) {
        data.columnData.forEach((el) => {
          if (el.prop) {
            el.value = el.prop;
          }
          if (el.value === 'updateTime') el.valueProps = { type: 'datetime' };
        });
        this.columnData = data.columnData;
        this.tableLoading = false;
        return { data: data.tableData, total: data.pageData.total };
      } else {
        this.$message.error(msg);
      }
      this.tableLoading = false;
    } catch {
      this.tableLoading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-history {
  height: calc(100vh - 114px);
  background: #fff;
}
</style>
