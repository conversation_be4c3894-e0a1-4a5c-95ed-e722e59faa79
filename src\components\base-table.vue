<!--
 * @Description: 
 * @Author: ranran
 * @Date: 2020-01-17 14:50:36
 * @LastEditTime: 2020-08-05 19:03:00
 * @LastEditors: magicyang
 -->
<template>
  <div class="base-table-content">
    <el-table
      tooltip-effect="light"
      :height="height"
      :data="tableData.tableData"
      :size="size"
      @cell-click="cellClick"
      @cell-dblclick="cellDblclick"
      @row-click="rowClick"
      @row-dblclick="rowDblclick"
      @sort-change="handleSortChange"
      @select="select"
      @select-all="selectAll"
      @selection-change="selectionChange"
    >
      <el-table-column
        v-if="tableConfig && tableConfig.type && tableConfig.type === 'expand'"
        :type="tableConfig.type"
      >
        <template slot-scope="props">
          <el-form label-position="left">
            <el-form-item
              v-for="expandProp in tableConfig.expandProps"
              :key="expandProp"
              :label="getExpandLabel(expandProp) + ':'"
            >
              <div style="font-size: 15px">{{ props.row[expandProp] }}</div>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tableConfig && tableConfig.type && tableConfig.type !== 'expand'"
        :type="tableConfig.type"
      />
      <el-table-column
        v-for="column in filterCol"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        align="center"
        :sortable="column.sortable === true ? 'custom' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <slot
            v-if="column.dataType === 'Property'"
            :name="column.prop"
            :row="scope.row"
            :$index="scope.$index"
          >
            {{ parseJson(scope.row[column.propertyField], column.prop) }}
          </slot>
          <slot
            v-if="column.dataType === 'Date'"
            :name="column.prop"
            :row="scope.row"
            :$index="scope.$index"
          >
            {{ scope.row[column.prop] | dateFormat }}
          </slot>
          <slot
            v-if="column.dataType === 'Enum' && column.enumData"
            :name="column.prop"
            :row="scope.row"
            :$index="scope.$index"
          >
            <span v-if="column.enumColor">
              <span :style="{ color: column.enumColor[scope.row[column.prop]] }">{{
                column.enumData[scope.row[column.prop]]
              }}</span>
            </span>
            <span v-else>{{ column.enumData[scope.row[column.prop]] }}</span>
          </slot>

          <slot
            v-if="column.dataType === 'Tag'"
            :name="column.prop"
            :row="scope.row"
            :$index="scope.$index"
          >
            <el-tag
              size="mini"
              style="cursor: pointer"
              :type="column.enumColor[scope.row[column.prop]]"
              @click="clickHandler(scope.row, column.prop)"
            >
              {{ column.enumData[scope.row[column.prop]] }}
            </el-tag>
          </slot>

          <slot v-if="!column.dataType" :name="column.prop" :row="scope.row" :$index="scope.$index">
            {{ scope.row[column.prop] }}
          </slot>
        </template>
      </el-table-column>
      <el-table-column
        v-if="showTableConfig"
        label="操作"
        :width="tableConfig.width"
        :fixed="tableConfig.fixed || false"
        align="center"
      >
        <template slot-scope="scope">
          <slot name="operateConfig" :row="scope.row" :$index="scope.$index">
            <el-tooltip
              v-for="item in tableConfig.columnsExtend.edit"
              :key="item.tipMessage"
              effect="light"
              placement="bottom"
              :content="item.tipMessage"
            >
              <i
                v-show="show(item.hasAuthority, scope.row)"
                class="iconfont"
                :class="item.iconfont"
                style="margin-left: 10px; cursor: pointer"
                @click="item.handler(scope.row, scope.$index)"
              ></i>
            </el-tooltip>
          </slot>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="tableData && tableData.pageData"
      class="pagination"
      background
      :current-page.sync="tableData.pageData.currentPage"
      :page-size="tableData.pageData.pageSize"
      :total="tableData.pageData.total"
      :pager-count="5"
      :layout="
        paginationConfig && paginationConfig.layout
          ? paginationConfig.layout
          : 'total, prev, pager, next, jumper'
      "
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import { find, filter } from 'lodash';
import JSONPath from 'JSONPath';
@Component
export default class Host extends Vue {
  @Prop() tableData!: ITableData;
  @Prop() tableConfig!: ITableConfig;
  @Prop() paginationConfig!: IPaginationConfig;
  @Prop({ default: 'calc(100% - 41px)' }) height!: string;
  @Prop({ default: 'medium' }) size!: string;
  get showTableConfig() {
    return (
      this.tableConfig &&
      this.tableConfig.columnsExtend &&
      this.tableConfig.columnsExtend.edit &&
      this.tableConfig.columnsExtend.edit.length > 0 &&
      this.tableData.tableData.length > 0 &&
      this.showColumnsExtend()
    );
  }

  @Emit('cellClick')
  cellClick(row, column, cell, event) {
    return { row, column, cell, event };
  }
  @Emit('cell-dblclick')
  cellDblclick(row, column, cell, event) {
    return { row, column, cell, event };
  }

  @Emit('rowClick')
  rowClick(row, column, event) {
    return { row, column, event };
  }

  @Emit('rowDblclick')
  rowDblclick(row, column, event) {
    return { row, column, event };
  }

  @Emit('handleCurrentChange')
  handleCurrentChange(val) {
    return val;
  }

  @Emit('handleSortChange')
  handleSortChange(val) {
    if (!!val.order) {
      return { [val.prop]: val.order === 'ascending' ? 'ASC' : 'DESC' };
    } else {
      return {};
    }
  }

  @Emit('handleSelect')
  select(selection, row) {
    return { selection, row };
  }

  @Emit('handleSelectAll')
  selectAll(selection) {
    return selection;
  }

  @Emit('handleSelectionChange')
  selectionChange(selection) {
    return selection;
  }

  /**
   * 处理点击事件，执行外部的一个方法
   */
  @Emit('clickHandler')
  clickHandler(row, col) {
    return { row, col };
  }

  show(hasAuthority: any, row: any) {
    if (hasAuthority === undefined) {
      return true;
    }
    if (typeof hasAuthority === 'boolean') {
      return hasAuthority;
    }
    if (typeof hasAuthority === 'function') {
      return hasAuthority(row);
    }
  }

  showColumnsExtend() {
    let needOper = false;
    if (this.tableData.tableData) {
      for (const key of Object.keys(this.tableData.tableData)) {
        if (this.operDisplay(this.tableData.tableData[key])) {
          needOper = true;
          break;
        }
      }
    }
    return needOper;
  }
  operDisplay(row: any) {
    const columnsExtend = this.tableConfig.columnsExtend;
    if (columnsExtend !== undefined) {
      let flag = false;
      const edit: any = columnsExtend.edit;
      for (const key of Object.keys(edit)) {
        if (this.show(edit[key].hasAuthority, row)) {
          flag = true;
          break;
        }
      }
      return flag;
    }
    return true;
  }
  getExpandLabel(prop: string) {
    const rec = find(this.tableData.columnData, { prop: prop });
    if (rec) {
      return rec.label;
    }
    return '';
  }
  parseJson(prop: string, key: string) {
    try {
      const idArr = JSONPath({
        json: JSON.parse(prop),
        path: '$.' + key
      });
      if (idArr.length > 0) {
        return idArr[0];
      }
    } catch (e) {
      // do nothing
    }
    return '';
  }
  get filterCol() {
    return filter(this.tableData.columnData, (column) => {
      return column.show || column.show === undefined;
    });
  }
}
</script>
<style lang="scss" scoped>
.base-table-content {
  height: 100%;
  z-index: 200;
  .col-max-hidden {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  ::v-deep .el-table--medium td.el-table__cell,
  ::v-deep .el-table--medium th.el-table__cell {
    padding: 13px 0;
  }
}
</style>
