<template>
  <bs-dialog
    v-loading="loading"
    :title="$t('pa.flow.flowRunInfo')"
    size="large"
    :visible.sync="display"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :footer-visible="false"
  >
    <!-- title -->
    <div slot="title" class="run-info-title">
      <div class="bs-dialog-title">{{ $t('pa.flow.flowRunInfo') }}</div>
    </div>
    <!-- main -->
    <info-desc :data="data" :column="3" />
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { getJobRunInfo } from '@/apis/flowNewApi';

@Component({ components: { InfoDesc: () => import('./info-desc.vue') } })
export default class RunInfoDialog extends Vue {
  @PropSync('show', { required: true, default: false }) display!: boolean;
  @Prop({ required: true, default: '' }) historyId!: string;

  loading = false;
  data: any = {};

  async created() {
    try {
      this.loading = true;
      const { success, data, msg } = await getJobRunInfo(null, this.historyId);
      if (!success) return this.$message.error(msg);
      this.data = { ...data };
    } finally {
      this.loading = false;
    }
  }
}
</script>
<style lang="scss" scoped>
.run-info {
  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 32px;
  }
}
</style>
