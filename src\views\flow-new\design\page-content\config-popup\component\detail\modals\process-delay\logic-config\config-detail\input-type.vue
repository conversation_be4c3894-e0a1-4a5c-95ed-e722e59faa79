<template>
  <el-form-item :rules="rules" prop="funcArgs[0].value">
    <el-input
      v-model="config.funcArgs[0].value"
      clearable
      size="small"
      :type="type"
      :disabled="disabled"
    />
  </el-form-item>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';

@Component
export default class InputType extends Vue {
  @Prop({ default: () => ({}) }) rowData!: any;
  @PropSync('data', { default: () => ({}) }) config!: any;

  get type() {
    return this.rowData.type;
  }
  get disabled() {
    return this.rowData.disabled;
  }

  get rules() {
    if (this.disabled) return {};
    return [
      {
        required: true,
        message: '请输入阈值',
        trigger: 'blur'
      },
      {
        min: 1,
        max: 50,
        message: '长度在 1 到 50 个字符',
        trigger: 'blur'
      }
    ];
  }
}
</script>
<style lang="scss" scoped></style>
