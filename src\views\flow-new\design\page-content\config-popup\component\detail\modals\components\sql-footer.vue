<template>
  <div :class="classList">
    <div v-if="changeName" class="sqlFooter-common">
      <el-checkbox v-model="formData.autoChangeName">自动赋值组件名称</el-checkbox>
      <el-tooltip
        placement="top"
        effect="light"
        content="选中后组件名称默认读取选中输出表/视图的名称"
      >
        <i class="iconfont icon-wenhao sqlFooter-icon"></i>
      </el-tooltip>
    </div>
    <div class="sqlFooter-common">
      <el-button @click="$emit('cancel', false)">取消</el-button>
      <el-button type="primary" @click="$emit('submit', true)">确定</el-button>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';

@Component
export default class SqlFooter extends Vue {
  @Prop({ type: Boolean, default: false }) changeName!: boolean;
  @PropSync('data', { type: Object, default: () => ({}) }) formData!: boolean;

  get classList() {
    return {
      sqlFooter__container: true,
      'sqlFooter__container--changeName': this.changeName
    };
  }
}
</script>
<style lang="scss" scoped>
.sqlFooter {
  &__container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    &--changeName {
      justify-content: space-between;
    }
  }
  &-common {
    display: flex;
    align-items: center;
  }
  &-icon {
    margin-left: 10px;
  }
}
</style>
