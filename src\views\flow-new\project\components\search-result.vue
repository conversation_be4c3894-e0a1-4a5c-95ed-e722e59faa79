<template>
  <div v-loading="searchResultLoading">
    <span class="search-result__span">
      {{ $t('pa.flow.total') }}
      <span class="search-result__num">{{ totalLength }}</span>
      {{ $t('pa.flow.searchResult') }}
    </span>
    <div class="search-result" @scroll="debounceScroll($event)">
      <div
        v-for="(item, index) in searchResultList"
        v-show="searchResultList.length"
        :key="item.id + index"
        class="search-result__item"
        :class="{ 'search-result__item--last': index === searchResultList.length - 1 }"
      >
        <!-- 头部信息 -->
        <div class="search-result__item--header">
          <!-- 头部信息-左侧（类别、项目名） -->
          <div class="search-result__item--left">
            <div class="search-result__item--type">
              {{ item.type === 'JOB' ? $t('pa.flow.steam') : $t('pa.flow.xiang') }}
            </div>
            <div
              class="search-result__item--name"
              @click="
                handleNameClick({
                  type: item.type,
                  id: item.id,
                  name: item.name,
                  ...(item.jobProjectId && { projectId: item.jobProjectId }),
                  ...(item.jobProjectName && { projectName: item.jobProjectName })
                })
              "
              v-html="item.searchLabel"
            ></div>
          </div>
          <!-- 头部信息-右侧（状态） -->
          <div v-if="item.type === 'JOB'" class="search-result__item--status">
            <el-tag v-if="item.jobStatus === 'PROD'" type="success">{{ $t('pa.flow.prod') }}</el-tag>
            <el-tag v-else-if="item.jobStatus === 'PUB'" color="#EBF2FF">{{ $t('pa.flow.pub') }}</el-tag>
            <el-tag v-else type="info">{{ $t('pa.flow.other') }}</el-tag>
          </div>
        </div>
        <!-- 底部内容（项目归属、创建人、创建时间） -->
        <div class="search-result__item--content">
          <div v-if="item.type === 'JOB'" class="search-result__item--from">
            {{ $t('pa.flow.from') }}：{{ item.jobProjectName }} {{ $t('pa.flow.project') }}
          </div>
          <div class="search-result__item--info">
            <span class="search-result__item--user" :class="{ 'is-project': item.type === 'PROJECT' }">
              {{ item.createdBy }}
            </span>
            <span class="search-result__item--date" :class="{ 'is-project': item.type === 'PROJECT' }">
              {{ item.formatCreatedTime }}
            </span>
          </div>
        </div>
      </div>
      <el-divider v-if="showNoDataText">
        <span class="search-result__item--text">{{ $t('pa.flow.noData') }}</span>
      </el-divider>
      <bs-empty v-show="showEmptyContent" class="search-result__item--empty" image-size="200" />
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { dateFormat } from '@/utils/format';
import { debounce } from 'lodash';
import { getFlowList, getFullSearchList } from '@/apis/flowNewApi';
@Component
export default class SearchResult extends Vue {
  @Prop() params!: any;
  @Prop() visible!: boolean;
  private searchResultList: object[] = [];
  private debounceSearch = debounce(this.getSearchResultList, 800);
  private debounceScroll = debounce(this.handleScroll, 400);
  private searchResultLoading = false;
  private currentResultLength = 0;
  private showEmptyContent = true;
  private totalLength = 0;
  private pageData = {
    currentPage: 1,
    pageSize: 20,
    total: 0
  };
  private isNewSearch = true;
  // 下拉加载更多无数据返回时，显示暂无更多数据字样
  get showNoDataText() {
    return !this.currentResultLength && this.pageData.currentPage > 1 && !this.showEmptyContent;
  }

  @Watch('params', { deep: true })
  handleSearch() {
    this.handleSearchParamsChange();
  }

  @Watch('visible')
  handleVisibleChange(isVisible) {
    isVisible && this.handleSearchParamsChange();
  }

  created() {
    this.getSearchResultList();
  }

  // 点击项目名、流程名跳转
  handleNameClick({ type, id, name, projectId, projectName }) {
    if (type === 'JOB') {
      this.toFlowCanvas(name, id, projectId, projectName);
    } else {
      this.toProject(id, name);
    }
  }
  toFlowCanvas(name, id, projectId, projectName) {
    if ((this as any).$tabNav.getAllTabs().find((item) => item.title === name && item.value.split('flowId=')[1] === id)) {
      const value = (this as any).$tabNav
        .getAllTabs()
        .find((item) => item.title === name && item.value.split('flowId=')[1] === id).value;
      localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId: id }));
      this.$router.push({
        path: value
      });
    } else {
      this.$router.push({
        path: 'flow',
        query: {
          id: projectId,
          name: projectName,
          title: name,
          state: 'ALL',
          flowId: id
        }
      });
    }
  }
  async toProject(id, name) {
    // 从卡片列表进流程设计，默认选中第一个流程
    const { success, data } = await getFlowList({
      id,
      name: '',
      jobStatus: 'ALL',
      jobType: 'ALL',
      mode: 'ALL',
      clusterType: 'ALL',
      jobRunTimeStatus: 'ALL'
    });
    if (success) {
      const flowId = data.children[0]?.id || '';
      const title = data.children[0]?.jobName || name;
      if (
        (this as any).$tabNav.getAllTabs().find((item) => item.title === title && item.value.split('flowId=')[1] === flowId)
      ) {
        const value = (this as any).$tabNav
          .getAllTabs()
          .find((item) => item.title === title && item.value.split('flowId=')[1] === flowId).value;
        localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId }));
        this.$router.push({
          path: value
        });
      } else {
        this.$router.push({
          path: '/flow',
          query: {
            id,
            name,
            title,
            state: 'ALL',
            flowId
          }
        });
      }
    }
  }

  // 根据搜索关键字请求接口
  async getSearchResultList(resetSearchList = false) {
    this.searchResultLoading = true;
    this.showEmptyContent = false;
    // 搜索类型及搜索词变化时，重置列表及分页数据
    if (resetSearchList) {
      this.searchResultList = [];
      this.pageData.currentPage = 1;
      this.totalLength = 0;
    }
    try {
      const { data, success, msg, error } = await getFullSearchList(
        {
          pageData: this.pageData,
          search: this.params.keyword
        },
        this.params?.queryType.match(/[^\s]+/)[0]
      );
      if (success) {
        this.isNewSearch = false;
        this.currentResultLength = data.tableData.length;
        if (data.tableData.length) {
          this.searchResultList = [...this.searchResultList, ...data.tableData];
          this.totalLength = data.pageData.total;
          this.searchResultList.forEach((el: any) => {
            el.formatCreatedTime = dateFormat(el.createdTime);
            el.searchLabel = this.setActiveWord(this.params.keyword, el.name);
          });
        }
        !this.searchResultList.length && (this.showEmptyContent = true);
      } else {
        this.$message.error(error || msg);
      }
      this.searchResultLoading = false;
    } catch {
      this.searchResultLoading = false;
    }
  }

  // 高亮匹配关键字
  setActiveWord(keyword, label: string) {
    if (label.includes(keyword)) {
      // 消除包含font标签的相关字符
      const clearTagString = label.replace(new RegExp('</?font.*?>', 'gi'), '');
      return clearTagString.replace(keyword, `<font style='color: #377cff'>${keyword}</font>`);
    }
    return label;
  }

  // 滚动加载更多
  handleScroll({ target: { scrollTop, clientHeight, scrollHeight } }) {
    // （clientHeight+1原因：小屏有1px高度误差）
    if (scrollTop + clientHeight + 1 >= scrollHeight && !this.isNewSearch && scrollTop !== 0) {
      this.pageData.currentPage++;
      this.currentResultLength && this.getSearchResultList();
    }
  }

  handleSearchParamsChange() {
    this.isNewSearch = true;
    this.debounceSearch(true);
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-divider--horizontal {
  width: 90%;
  transform: translateX(6%);
}
.is-project {
  padding-top: 12px;
  padding-bottom: 2px;
}
.el-tag {
  border-color: #d7e5ff;
  box-sizing: border-box;
  height: 18px;
  width: max-content;
  min-width: 50px;
  padding: 0 7px;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  white-space: nowrap;
}
.search-result {
  height: calc(56vh - 95px);
  flex-grow: 1;
  overflow-y: scroll;
  margin: 0 12px 0 20px;
  border-top: 1px solid #f2f4f7;
  @media screen and (min-width: 1300px) {
    height: calc(73vh - 95px);
  }
  &__span {
    display: inline-block;
    font-size: 12px;
    font-weight: 400;
    color: #777777;
    line-height: 17px;
    padding: 0 0 17px 25px;
  }
  &__num {
    color: #377cff;
  }
  &__item {
    padding: 12px 0;
    border-bottom: 1px solid #f2f4f7;
    &--from {
      font-size: 12px;
      color: #777770;
      font-weight: 400;
      line-height: 17px;
      padding: 12px 0;
    }
    &--last {
      border-bottom: 0px;
    }
    &--text {
      color: #d7d7d7;
      padding: 0 12px;
    }
    &--empty {
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: 35vh;
    }
    &--type {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 18px;
      height: 18px;
      border-radius: 4px;
      border: 1px solid #057eff;
      font-size: 12px;
    }
    &--status,
    &--date {
      margin-right: 5px;
    }
    &--name {
      font-size: 14px;
      margin-left: 10px;
      color: #444;
      cursor: pointer;
    }
    &--user,
    &--date {
      font-size: 12px;
      font-weight: 400;
      color: #aaaaaa;
      line-height: 17px;
    }
    &--header {
      width: 100%;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #4681fa;
    }
    &--left {
      width: 100%;
      display: flex;
      align-items: center;
    }
    &--content {
      display: flex;
      flex-direction: column;
      color: #aaa;
    }
    &--info {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
