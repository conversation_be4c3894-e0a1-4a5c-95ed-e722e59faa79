<template>
  <div class="config-text">
    <bs-code ref="bsCode" :value="codeValue" :read-only="readOnly" :formatter="formatCode" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import stringify from 'json-stringify-pretty-compact';
import { uuid } from './utils';
import * as monaco from 'monaco-editor';
@Component
export default class ConfigText extends Vue {
  @Prop({ default: () => [] }) data!: any[];
  @Prop({ default: () => [] }) inputData!: any[];
  @Prop({ default: () => new Map() }) methods!: any;
  @Prop({ default: false }) readOnly!: boolean;
  private bsCode: any = null;
  private codeValue = '';

  created() {
    this.codeValue = this.initCodeValue();
  }
  mounted() {
    this.bsCode = this.$refs.bsCode;
    // 将上游字段区域置为灰色
    this.setDisabledArea();
  }
  /**
   * 初始化代码值
   */
  initCodeValue() {
    if (!Array.isArray(this.data)) return '';
    return stringify(
      this.data.map(({ outputable, name, type, method, params }) => ({ outputable, name, type, method, params })),
      { maxLength: 200 }
    );
  }

  /**
   * 验证内容
   */
  validate() {
    try {
      const value = this.bsCode ? this.bsCode.getValue() : '';
      const data = JSON.parse(value || '[]'); // 默认为空数组
      if (!Array.isArray(data)) {
        this.$message.error(this.$t('pa.flow.msg25'));
        return false;
      }
      let index = this.inputData.findIndex(({ name, type }, i) => data[i].name !== name || data[i].type !== type);
      if (index > -1) {
        this.$message.error(this.$t('pa.flow.msg23') + `【${this.inputData[index].name}】` + this.$t('pa.flow.msg318'));
        return false;
      }
      return true;
    } catch (err) {
      this.$message.error(this.$t('pa.flow.msg25'));
      return false;
    }
  }
  /**
   * 格式化代码
   */
  formatCode(val) {
    return stringify(JSON.parse(val), { maxLength: 180 });
  }

  /**
   * 同步至tableData
   */
  syncTableData() {
    const value = this.bsCode ? this.bsCode.getValue() : '';
    const data = JSON.parse(value || '[]'); // 默认为空数组
    // 字段名称及类型映射
    const nameTypeMap = {};
    return data.map(({ outputable, name, type, method, params }) => {
      const item = this.inputData.find((i) => i.name === name);
      const methodItem = this.methods.get(method);
      nameTypeMap[`#${name}#`] = type;
      // 若method作为方法存在，则返回该方法的返回值返回值类型覆盖
      // 若引用其他字段，则返回该字段的类型
      const getType = () => {
        if (methodItem) return methodItem.retType;
        if (method && nameTypeMap[method]) return nameTypeMap[method];
        return type || 'String';
      };
      return {
        id: uuid(),
        hide: false,
        outputable: outputable,
        name: name,
        type: getType(),
        method: method,
        params: params || [],
        disabled: !!item,
        targetable: item ? item.targetable : undefined,
        isInput: item ? true : undefined
      };
    });
  }
  /**
   * 设置上游字段区域为灰色
   */
  setDisabledArea() {
    const editor = (this.bsCode || {}).monacoInstance;
    if (!editor) return;
    const isOneLine = (this.bsCode.getValue() || '').split('\n') <= 1;
    // 定义只读区域
    let index = this.data.findIndex((i) => !i.isInput);
    if (index < 0) index = this.data.length;
    const readOnlyRanges = [isOneLine ? 1 : 2, index + 1];
    // 应用装饰以视觉上标记只读区域
    editor.deltaDecorations(
      [],
      [
        {
          range: new monaco.Range(readOnlyRanges[0], 1, readOnlyRanges[1], 1),
          options: {
            isWholeLine: true,
            className: 'readonly-line',
            // 确保装饰随着编辑操作保持在正确位置
            stickiness: monaco.editor.TrackedRangeStickiness.AlwaysGrowsWhenTypingAtEdges
          }
        }
      ]
    );
  }
}
</script>

<style lang="scss" scoped>
.bs-code {
  margin-bottom: 20px;

  ::v-deep .readonly-line {
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 0.8;
  }
}
</style>
