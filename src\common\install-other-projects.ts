// import streamCube from 'stream-cube-script-ui_3.4.0';
// import assets from 'assets-fix';
import assets from 'assets-ofa-for-pa';
import 'bsview-3.0/dist/styles/bsview.css';
import bsview from 'bsview-3.0';
import store from '@/store';
import router from '@/router';
import System from '@/utils/system';
import moment from 'moment';
import CubeCompute from 'cube-compute-mgr-ui';

export default (Vue, i18n): void => {
  // assets里有用到该字段，部分接口会undefined
  window['moment'] = moment;
  if (System.isProduction()) {
    const pathName = document.location.pathname;
    const index = pathName.substr(1).lastIndexOf('/');
    window['prefixURL'] = pathName.substr(0, index + 1);
  } else {
    window['prefixURL'] = '';
  }
  window['assetsPrefix'] = '.'; // assets相关接口必须配置
  Vue.use(bsview, {
    i18n: (key, value) => i18n.t(key, value)
  });
  if (!process.env.IS_VITE) {
    // Vue.use(streamCube, { router: routes, store, i18n });
  }
  Vue.use(assets, { router, store, i18n });
  Vue.use(CubeCompute, { router, store, i18n });
};
