import { get, post, put } from './utils/net';

// 获取测试用例参数
export const getJobFields = (data) => {
  return get('/rs/pa/job/findFieldMsg', data);
};

// 获取随机造数枚举值
export const getJobEnums = () => {
  return get('/rs/pa/job/test/getRandomEnum');
};

// 测试用例新增
export const addTestData = (param) => {
  return post('/rs/pa/job/test/data/add', param);
};

//更新测试用例

export const updateTestData = (param) => {
  return put('/rs/pa/job/test/data/update', param);
};

// 查询测试用例数据
export const testDataDetail = (param) => {
  return get('/rs/pa/job/test/data/view', param);
};

// 获取流程列表
export const getFlowList = () => {
  return get('/rs/pa/project/list');
};
