<template>
  <bs-dialog :title="title" :visible.sync="visible" width="40%" :before-close="closeDialog">
    <el-form ref="ruleForm" :model="formData" :rules="rules" @submit.native.prevent>
      <el-form-item label="名称" prop="projectName" :label-width="formLabelWidth">
        <el-input
          v-model="formData.projectName"
          autocomplete="off"
          maxlength="30"
          show-word-limit
          placeholder="请输入名称，长度不超过30字符"
        />
      </el-form-item>
      <el-form-item label="备注" prop="memo" :label-width="formLabelWidth">
        <el-input
          v-model="formData.memo"
          type="textarea"
          rows="5"
          placeholder="请输入备注"
          autocomplete="off"
          maxlength="255"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取消</el-button>
      <el-button :loading="confirmButtonLoading" type="primary" @click="submit">确定</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { addProject, getProjectById, updateProject } from '@/apis/flowApi';

@Component
export default class AddAndEditProject extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() projectId!: string;
  @Prop() operType!: string;

  title = '新建项目';
  confirmButtonLoading = false;
  formData: any = { projectName: '', memo: '' };
  rules: any = {
    projectName: [{ required: true, message: '请输入名称', trigger: 'blur' }]
  };
  formLabelWidth = '50px';

  closeDialog() {
    this.$emit('update:visible', false);
  }

  submit() {
    const form: any = this.$refs.ruleForm;
    form.validate((valid: any) => {
      if (valid) {
        this.confirmButtonLoading = true;
        if (this.projectId) {
          this.update();
          return;
        }
        this.add();
      } else {
        return false;
      }
    });
  }

  // 新建项目
  async add() {
    try {
      const { success, msg } = await addProject(this.formData);
      if (success) {
        this.$message.success(msg);
        this.closeDialog();
        this.$emit('close'); // 更新项目列表
      } else {
        this.$message.error(msg);
      }
      this.confirmButtonLoading = false;
    } catch {
      this.confirmButtonLoading = false;
    }
  }

  // 更新项目
  async update() {
    try {
      const { success, msg } = await updateProject(this.formData);
      if (success) {
        this.$message.success(msg);
        this.closeDialog();
        this.$emit('close'); // 更新项目列表
      } else {
        this.$message.error(msg);
      }
      this.confirmButtonLoading = false;
    } catch {
      this.confirmButtonLoading = false;
    }
  }

  async created() {
    // 编辑：获取项目信息
    if (this.operType === 'editProject') {
      this.title = '编辑项目';
      const { success, msg, data } = await getProjectById({ id: this.projectId });
      if (success) {
        this.formData = data;
        return;
      }
      this.$message.error(msg);
    }
  }
}
</script>
<style lang="scss" scoped></style>
