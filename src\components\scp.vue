<template>
  <bs-dialog
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    title="上传"
    :visible.sync="visible"
    width="40%"
    :before-close="closeDialog"
  >
    <el-alert title="提示:文件默认上传到用户根目录" type="success" :closable="false" />
    <el-form
      ref="ruleForm"
      :model="formData"
      :rules="rules"
      label-width="70px"
      class="demo-ruleForm"
    >
      <el-form-item label="文件" prop="pkg" style="text-align: left">
        <el-upload
          ref="upload"
          action
          :http-request="handleFile"
          :multiple="false"
          :limit="1"
          :on-exceed="handleExceed"
          :file-list="formData.fileList"
        >
          <el-button size="small" type="primary">选择文件</el-button>
        </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
      <el-button type="primary" :loading="loading" @click="submit('ruleForm')">上 传</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Vue, Emit } from 'vue-property-decorator';
import { URL_HOST_SCP } from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
@Component
export default class Scp extends PaBase {
  recordId!: string;
  visible = false;
  formData: any = {};
  loading = false;
  rules: any = {
    pkg: [{ required: true, validator: this.validateFile, trigger: 'blur' }]
  };
  validateFile(rule: any, value: any, callback: any) {
    if (this.formData.fileList === undefined || this.formData.fileList.length === 0) {
      if (this.formData.id) {
        callback();
      } else {
        callback(new Error('请添加文件'));
      }
    } else {
      callback();
    }
  }
  /**
   * 初始化，用于初始化数据和页面属性
   */
  init() {
    this.recordId = '';
    this.formData = {};
    this.loading = false;
  }

  /**
   * 表单提交
   */
  submit(formName: string) {
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        Vue.axios.defaults.timeout = 1500000;
        Vue.axios.defaults.headers.post['Content-Type'] = 'multipart/form-data';

        const submitData = new FormData();
        submitData.append('id', this.recordId);
        if (this.formData.fileList !== undefined) {
          submitData.append('file', this.formData.fileList[0]);
        }
        this.loading = true;
        this.doPost(URL_HOST_SCP, submitData).then((resp: any) => {
          this.parseResp(resp);
        });
      } else {
        this.$message.error('请检查输入内容');
        return false;
      }
    });
  }
  /**
   * 解析返回结果
   */
  private parseResp(resp: any) {
    this.loading = false;
    this.parseResponse(resp, () => {
      this.formData.fileList = [];
    });
  }
  /**
   * 处理文件上传
   */
  private handleFile(file: any) {
    this.formData.fileList = [];
    this.formData.fileList.push(file.file);
  }
  /**
   * 判断上传文件个数
   */
  private handleExceed(files: any) {
    this.$message.warning(`最多上传 ${files.length} 个文件`);
  }
  /**
   * 关闭对话框并反馈给父组件
   */
  @Emit('close')
  private closeDialog() {
    (this.$refs.upload as any).clearFiles();
    this.init();
    this.visible = false;
  }
}
</script>

<style scoped>
.demo-ruleForm {
  margin-top: 20px;
}
</style>
