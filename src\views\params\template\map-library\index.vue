<template>
  <pro-page title="映射字段库" :fixed-header="false" class="map">
    <div slot="operation">
      <bs-search
        v-model="searchObj.search"
        placeholder="系统编号 | 系统名称 | 映射字段"
        style="width: 230px; margin-right: 25px"
        size="small"
        @input="debounceSearch"
      />
      <el-button
        v-access="'PA.SETTING.MODEL.MAPPING_FIELD.IMPOET'"
        type="primary"
        size="small"
        @click="importData"
      >
        导入
      </el-button>
      <el-button
        v-access="'PA.SETTING.MODEL.MAPPING_FIELD.ADD'"
        type="primary"
        size="small"
        @click="add"
      >
        新建
      </el-button>
    </div>
    <pro-table
      ref="proTable"
      v-loading="tableLoading"
      :columns="columnData"
      :request="request"
      :actions="actions"
      :options="options"
      @action-click="handleActionClick"
    />
    <EditModal v-if="showEditModal" :id="editId" :visible="showEditModal" @close="closeEditModal" />
    <ImportModal v-if="showImportModal" :visible="showImportModal" @close="closeImportModal" />
  </pro-page>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { post, del } from '@/apis/utils/net';
import EditModal from './components/edit-modal.vue';
import ImportModal from './components/import-modal.vue';
import { cloneDeep, debounce } from 'lodash';

@Component({
  name: 'ElementMapLibrary',
  components: {
    EditModal,
    ImportModal
  }
})
export default class ElementMapLibrary extends PaBase {
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 }
  };
  columnData = [];
  options = {
    actionHandle: ({ actions }) => {
      return actions.filter(({ access }) =>
        this.$store.state.userInfo.authorities.includes(access)
      );
    }
  };
  actions = [
    {
      label: '编辑',
      icon: 'iconfont icon-bianji',
      value: 'edit',
      access: 'PA.SETTING.MODEL.MAPPING_FIELD.EDIT'
    },
    {
      label: '删除',
      icon: 'iconfont icon-shanchu',
      value: 'del',
      access: 'PA.SETTING.MODEL.MAPPING_FIELD.DELETE'
    }
  ];
  // 显示新建编辑弹窗
  showEditModal = false;
  // 显示导入字段库
  showImportModal = false;
  // 编辑字段数据
  editId: any = {};
  debounceSearch = debounce(this.search, 500);
  handleActionClick(event, { row }) {
    this[event](row);
  }
  // 新建
  add() {
    this.editId = null;
    this.showEditModal = true;
  }
  // 编辑
  edit(row) {
    this.editId = row.id;
    this.showEditModal = true;
  }
  importData() {
    this.showImportModal = true;
  }
  // 删除
  del(row) {
    this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const { error, msg, success } = await del('/rs/pa/mappingWarehouse/deleteById', {
        id: row.id
      });
      if (success) {
        this.$message({ message: '删除字段成功', type: 'success' });
        this.$refs.proTable!['loadData']();
      } else {
        this.$message({ message: msg || error, type: 'error' });
      }
    });
  }
  // 搜索
  search() {
    this.$refs.proTable!['loadDataAndReset']();
  }
  async request(page) {
    this.tableLoading = true;
    // 获取模板列表
    const searchObj = cloneDeep(this.searchObj);
    searchObj.search = searchObj.search.trim();
    try {
      const { data, success, msg } = await post('/rs/pa/mappingWarehouse/list', {
        search: searchObj.search,
        pageData: page
      });
      if (success) {
        data.columnData.forEach((el) => {
          if (el.prop) {
            el.value = el.prop;
            if (el.value === 'updateTime') el.valueProps = { type: 'datetime' };
          }
        });
        this.columnData = data.columnData;
        this.tableLoading = false;
        return { data: data.tableData, total: data.pageData.total };
      } else {
        this.$message.error(msg);
      }
      this.tableLoading = false;
    } catch {
      this.tableLoading = false;
    }
  }
  // 关闭新建/编辑弹窗
  closeEditModal(needFresh) {
    if (needFresh) {
      this.searchObj.pageData.currentPage = 1;
      this.$refs.proTable!['loadData']();
    }
    this.showEditModal = false;
  }
  // 关闭导入字段弹窗
  closeImportModal(needFresh) {
    if (needFresh) {
      this.searchObj.pageData.currentPage = 1;
      this.$refs.proTable!['loadData']();
    }
    this.showImportModal = false;
  }
}
</script>

<style lang="scss" scoped>
::v-deep .bs-pro-page__header .bs-pro-page__header-operation .el-input {
  width: 242px;
}
.map {
  height: calc(100vh - 107px);
  &-search {
    width: 210px;
    margin-right: 20px;
  }
  &-tooltip {
    cursor: pointer;
  }
  &-icon {
    margin: 0 5px;
  }
  &-header {
    height: 50px;
    background: #ffffff;
    border-left: none;
    padding: 0 20px;
    font-size: 16px;
    display: flex;
    align-items: center;
  }
  &-header-operate {
    flex: 1;
    text-align: right;
  }
  &-content {
    height: calc(100% - 58px);
    padding-bottom: 10px;
    overflow: hidden;
    background: #fff;
  }
}
</style>
