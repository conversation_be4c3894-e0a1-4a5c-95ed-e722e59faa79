<template>
  <div>
    <header class="header">
      <div class="header-info" :title="repeatImpOveriewMsg">{{ repeatImpOveriewMsg }}</div>
      <div class="header-oper">
        <el-button v-if="hideOper" type="primary" size="small" @click="download">{{ $t('pa.action.download') }}</el-button>
      </div>
    </header>
    <bs-table
      size="small"
      height="calc(70vh - 250px)"
      :column-settings="false"
      :data="repeatImportData"
      :column-data="columnData"
      :page-data="pageData"
      paging-front
      @page-change="handlePageChange"
    >
      <template slot="header-operator" slot-scope="{ $index }">
        {{ $t('pa.action.action') }}
        <el-popover trigger="click" :title="$t('pa.flow.batch')">
          <el-radio-group v-model="operType" @change="checkedChange">
            <el-radio :label="1">{{ $t('pa.action.cover') }}</el-radio>
            <el-radio :label="0">{{ $t('pa.action.skip') }}</el-radio>
          </el-radio-group>
          <i slot="reference" class="el-icon-arrow-down el-icon--right"></i>
        </el-popover>
      </template>
      <template slot="type" slot-scope="{ row }">
        {{ enumData[row.type] }}
      </template>
      <template slot="operator" slot-scope="{ row }">
        <el-radio-group v-model="row.operType" size="mini">
          <el-radio-button :label="1">{{ $t('pa.action.cover') }}</el-radio-button>
          <el-radio-button :label="0">{{ $t('pa.action.skip') }}</el-radio-button>
        </el-radio-group>
      </template>
    </bs-table>
  </div>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { repeatDataDownload } from '@/apis/impExpApi';
import { ASSETS_TPYE_ENUMS } from './utils';
import { download } from '@/utils';
@Component
export default class ImportRepetitiveData extends Vue {
  @Prop() repeatImportData!: any;
  @Prop() repeatImpOveriewMsg!: string;
  @PropSync('loading') downloadLoading!: boolean;
  columnData = [
    { label: this.$t('pa.resource.importExport.assetId'), value: 'id' },
    { label: this.$t('pa.resource.importExport.assetName'), value: 'name' },
    { label: this.$t('pa.resource.importExport.assetType'), value: 'type', width: 100 },
    { label: this.$t('pa.addOrg'), value: 'orgName', width: 100 },
    { label: this.$t('pa.resource.importExport.diffContent'), value: 'mesg' },
    {
      width: 150,
      label: this.$t('pa.action.action'),
      value: 'operator',
      fixed: 'right'
    }
  ];
  pageData = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 };
  operType = 1;
  enumData = ASSETS_TPYE_ENUMS;

  get hideOper() {
    return !!this.repeatImportData.length; // 有数据的时候显示操作栏
  }

  created() {
    this.repeatImportData.forEach((el) => {
      this.$set(el, 'operType', 1);
    });
    this.pageData.total = this.repeatImportData.length;
    this.pageData.currentPage = 1;
  }

  // 批量修改是否覆盖，默认覆盖
  checkedChange(value: number) {
    this.repeatImportData.forEach((item) => {
      this.$set(item, 'operType', value);
    });
  }
  // 表格页码修改回调
  handlePageChange(page: number, size: number) {
    this.pageData.currentPage = page;
    this.pageData.pageSize = size;
  }
  // 下载重复性数据
  async download() {
    try {
      this.downloadLoading = true;
      const data = await repeatDataDownload(this.repeatImportData);
      if (data.type === 'application/json') {
        data.text().then((text) => {
          const { success, msg, error } = JSON.parse(text);
          if (!success) this.$message.error(msg || error);
        });
      } else {
        download(data);
      }
      this.downloadLoading = false;
    } catch {
      this.downloadLoading = false;
    }
  }
}
</script>
<style scoped lang="scss">
.header {
  display: flex;
  height: 38px;
  justify-content: space-between;
  align-items: center;
  padding: 0 34px 10px;

  &-info {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &-oper {
    margin-left: 10px;
  }
}
</style>
