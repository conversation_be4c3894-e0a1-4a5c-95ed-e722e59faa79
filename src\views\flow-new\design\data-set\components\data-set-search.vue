<template>
  <div class="data-set-search">
    <bs-search
      v-if="isTableMgr"
      v-model="inTableMgrSearch"
      :placeholder="$t('pa.flow.placeholder4')"
      @search="$emit('search')"
    />
    <bs-search
      v-if="!isTableMgr"
      v-model="inOriginTableSearch"
      :select-value="inSelectVal"
      :options="searchOptions"
      :placeholder="$t('pa.flow.placeholder4')"
      :disabled="originTableSearchDisabled"
      @change="handleSelectValChange"
      @search="$emit('search')"
    />
    <el-tooltip :content="$t('pa.flow.refresh')" effect="light" :open-delay="500">
      <i class="iconfont icon-shuaxin" @click="$emit('refresh')"></i>
    </el-tooltip>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, PropSync } from 'vue-property-decorator';
@Component({
  name: 'Test'
})
export default class Test extends Vue {
  @Prop() isTableMgr!: boolean;
  @Prop() isExpandOriginRes!: boolean;
  @PropSync('selectVal') inSelectVal;
  @PropSync('tableMgrSearch') inTableMgrSearch;
  @PropSync('originTableSearch') inOriginTableSearch;
  @PropSync('isSelectValChange') inIsSelectValChange;

  searchOptions: any = [
    { label: this.$t('pa.flow.serve'), value: 'SERVICE' },
    { label: this.$t('pa.flow.table'), value: 'TABLE' }
  ];

  get originTableSearchDisabled() {
    return this.inSelectVal === 'TABLE' && !this.isExpandOriginRes;
  }

  // 原始表切换搜索类型后更新selectVal，用于处理原始表前端搜索
  handleSelectValChange(val) {
    if (Object.keys(val)[0] !== this.inSelectVal) {
      this.inSelectVal = (Object.keys(val) as any)[0];
      this.inIsSelectValChange = true;
    }
  }
}
</script>

<style lang="scss" scoped>
.data-set-search {
  height: 50px;
  display: flex;
  align-items: center;
  .iconfont {
    font-size: 15px;
    cursor: pointer;
  }
}
::v-deep .bs-search {
  width: 100% !important;
  margin-right: 15px;
  .el-input-group__prepend {
    width: 63px;
  }
}
</style>
