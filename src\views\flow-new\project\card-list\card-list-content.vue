<template>
  <div v-loading="loading" class="card-list__content">
    <div v-access="'PA.FLOW.PROJECT_MGR.VIEW'" class="sort">
      <el-tooltip content="按照更新时间倒叙排序" effect="light">
        <span
          class="time"
          :class="sortBy === 'time' ? 'used' : 'noUsed'"
          @click="sortByChange('time')"
        >
          更新时间
        </span>
      </el-tooltip>
      <el-tooltip content="按照项目名称首字母排序" effect="light">
        <span :class="sortBy === 'name' ? 'used' : 'noUsed'" @click="sortByChange('name')">
          项目名称
        </span>
      </el-tooltip>
    </div>
    <div class="center-info">
      <el-row :gutter="18">
        <el-col v-for="item in projects" :key="item.projectId" class="card" :span="6">
          <div
            class="projects-item"
            @click.self="toProject(item.projectId, item.projectName, 'ALL')"
          >
            <svg-icon :name="'xiangmu'" class="projects-item__img" />
            <div
              class="projects-item__info"
              @click="toProject(item.projectId, item.projectName, 'ALL')"
            >
              <el-tooltip effect="light">
                <div slot="content">
                  <p v-for="el in tooltipContent" :key="el.value" class="projects-item__tooltip">
                    {{ el.label }}：{{ item[el.value] ? item[el.value] : '-' }}
                  </p>
                </div>
                <span class="name">{{ item.projectName }}</span>
              </el-tooltip>
            </div>
            <div v-if="menuList.length" class="projects-item__more" @click="clickProject(item)">
              <bs-popover-menu :list="menuList" @click="menuItemClick" />
            </div>
          </div>
          <div class="warn-info">
            <div
              v-for="info in warnInfo"
              :key="info.value"
              class="info"
              @click="toProject(item.projectId, item.projectName, info.value)"
            >
              <el-badge is-dot class="item" :type="info.type" />
              <span class="label">{{ info.label }} </span>
              <span class="value">{{ item[info.count] || 0 }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
      <bs-empty v-if="showEmpty" />
    </div>
    <!-- 流程导入 -->
    <import-flow-dialog
      v-if="importFlowDialogVisible"
      :project-id="currentNode.projectId"
      :show.sync="importFlowDialogVisible"
      @close="getCardProjectList"
    />
    <!-- 编辑项目 -->
    <add-edit-project
      v-if="addEditProjectVisible"
      :project-id="currentNode.projectId"
      :oper-type="'editProject'"
      :visible.sync="addEditProjectVisible"
      @close="getCardProjectList"
    />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Watch, Vue } from 'vue-property-decorator';
import { getProjectList, deleteProject, getFlowList } from '@/apis/flowNewApi';
import moment from 'moment';
import { hasPermission } from '@/utils';
@Component({
  components: {
    'import-flow-dialog': () => import('../../components/import-flow-dialog.vue'),
    'add-edit-project': () => import('../../design/flow-list/modals/add-edit-project.vue')
  }
})
export default class CardListContent extends Vue {
  @Prop() keywords!: string;
  addEditProjectVisible = false; //控制新建或编辑弹窗显隐
  importFlowDialogVisible = false; //控制导入项目弹窗显隐
  showEmpty = false;
  loading = false;
  sortBy = 'time';
  //项目列表
  projects: any = [];
  tooltipContent = [
    {
      label: '备注',
      value: 'projectMemo'
    },
    {
      label: '创建机构',
      value: 'orgId'
    },
    {
      label: '创建人',
      value: 'createBy'
    },
    {
      label: '创建时间',
      value: 'createTime'
    },
    {
      label: '更新人',
      value: 'updatedBy'
    },
    {
      label: '更新时间',
      value: 'updateTime'
    }
  ];
  warnInfo = [
    {
      label: '已上线',
      value: 'PROD',
      type: 'success',
      count: 'onlineCount'
    },
    {
      label: '已发布',
      value: 'PUB',
      type: 'primary',
      count: 'publishCount'
    },
    {
      label: '开发',
      value: 'DEV',
      type: 'info',
      count: 'otherCount'
    }
  ];
  currentNode: any = {};
  get menuList() {
    // 权限过滤
    return [
      { label: '编辑项目', value: 'editProject', access: 'PA.FLOW.PROJECT_MGR.EDIT' },
      { label: '删除项目', value: 'deleteProject', access: 'PA.FLOW.PROJECT_MGR.DELETE' },
      { label: '导入流程', value: 'importFlow', access: 'PA.FLOW.PROJECT_MGR.IMPORT' }
    ].filter((item) => hasPermission(item.access));
  }
  @Watch('keywords')
  handleKeyWordsChange() {
    this.getCardProjectList();
  }
  created() {
    this.getCardProjectList();
  }
  async getCardProjectList(refresh = false) {
    try {
      this.showEmpty = false;
      this.loading = true;
      const { data, success, msg, error } = await getProjectList({
        name: this.keywords,
        sortByName: this.sortBy === 'name'
      });
      if (success) {
        data.forEach((el) => {
          el.createTime = moment(el.createTime).format('YYYY-MM-DD HH:mm:ss');
          el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
        });
        this.projects = data;
        this.showEmpty = !this.projects.length;
        refresh && this.$message.success('刷新成功');
        this.loading = false;
      } else {
        this.loading = false;
        this.$message.error(error || msg);
      }
    } catch (e) {
      this.loading = false;
    }
  }
  async toProject(id, name, state) {
    try {
      // 从卡片列表进流程设计，默认选中第一个流程
      const { success, data } = await getFlowList({
        id,
        name: '',
        jobStatus: state,
        jobType: 'ALL',
        mode: 'ALL'
      });
      if (success) {
        this.loading = false;
        const flowId = data.children[0]?.id || '';
        const title = data.children[0]?.jobName || name;
        if (
          (this as any).$tabsNav
            .getAllTabs()
            .find((item) => item.title === title && item.value.split('flowId=')[1] === flowId)
        ) {
          const value = (this as any).$tabsNav
            .getAllTabs()
            .find(
              (item) => item.title === title && item.value.split('flowId=')[1] === flowId
            ).value;
          localStorage.setItem('flow', JSON.stringify({ state, flowId }));
          this.$router.push({
            path: value
          });
        } else {
          this.$router.push({
            path: '/flow',
            query: {
              id,
              name,
              title,
              state,
              flowId
            }
          });
        }
      }
    } catch (e) {
      this.loading = false;
    }
  }
  clickProject(item) {
    this.currentNode = item;
  }
  sortByChange(sortBy) {
    this.sortBy = sortBy;
    this.getCardProjectList();
  }
  menuItemClick(value) {
    switch (value) {
      case 'editProject':
        this.addEditProjectVisible = true;
        break;
      case 'deleteProject':
        this.deleteProject();
        break;
      case 'importFlow':
        this.importFlowDialogVisible = true;
    }
  }
  deleteProject() {
    this.$confirm('确认删除该项目吗?', '删除项目', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(async () => {
      const { success, msg, error } = await deleteProject({ id: this.currentNode.projectId });
      if (success) {
        this.$tip.success(msg);
        this.getCardProjectList();
      } else {
        this.$tip.error(error || msg);
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.projects-item {
  &__tooltip {
    margin: 5px 0;
  }
}
::v-deep .el-loading-mask {
  background-color: transparent;
}
.card-list__content {
  margin-top: 1px;
  width: 100%;
  height: calc(100% - 92px);
  min-height: 60vh;
  overflow-x: hidden;
  .sort {
    height: 57px;
    padding: 21px 0 16px 20px;
    font-size: 14px;
    line-height: 20px;
    .time {
      width: 72px;
      padding-right: 16px;
      margin-right: 16px;
      border-right: 1px solid #e5e5e5;
    }
    .used {
      color: #377cff;
      cursor: pointer;
    }
    .noUsed {
      color: #777777;
      cursor: pointer;
    }
  }
  .center-info {
    height: 100%;
    width: 100%;
    .card {
      height: 160px;
      min-width: 250px;
      padding-right: 10px;
      border: 1px solid #f5f5f5;
      border-radius: 4px;
      margin-bottom: 20px;
      .projects-item {
        display: flex;
        align-items: center;
        height: 120px;
        cursor: pointer;
        background: #fff;
        border-bottom: 1px solid #f1f1f1;
        &__img {
          margin: 34px 16px 32px 32px;
          width: 44px;
          height: 44px;
        }
        &__info {
          padding-right: 23px;
          width: calc(100% - 120px);
          .name {
            font-size: 14px;
            line-height: 24px;
            word-wrap: break-word;
            font-weight: 500;
          }
        }
        &__more {
          padding-bottom: 90px;
          margin-right: 10px;
        }
      }

      .warn-info {
        display: flex;
        background: #fcfcfe;
        justify-content: space-between;
        line-height: 14px;
        padding: 13px;
        font-size: 12px;
        height: 40px;
        .info {
          cursor: pointer;
          .label {
            color: #777;
            margin-right: 6px;
            @media screen and (max-width: 1400px) {
              margin-right: 0;
            }
          }
          .value {
            color: #444;
            margin-right: 6px;
          }
          .item {
            width: 6px;
            height: 6px;
            margin-right: 6px;
          }
        }
      }
    }
  }
}
</style>
