<template>
  <div v-loading="loading" class="card-list__content">
    <div v-access="'PA.FLOW.PROJECT_MGR.VIEW'" class="sort">
      <el-tooltip :content="$t('pa.flow.sortByUpdateTime')" effect="light">
        <span class="time" :class="sortBy === 'time' ? 'used' : 'noUsed'" @click="sortByChange('time')">
          {{ $t('pa.flow.updateTime') }}
        </span>
      </el-tooltip>
      <el-tooltip :content="$t('pa.flow.sortByProjectName')" effect="light">
        <span :class="sortBy === 'name' ? 'used' : 'noUsed'" @click="sortByChange('name')">
          {{ $t('pa.flow.projectName') }}
        </span>
      </el-tooltip>
    </div>
    <pro-grid :gutter="18">
      <pro-grid v-for="item in projects" :key="item.projectId" type="card" :col-span="3" con-class-name="card">
        <div class="projects-item" @click.self="toProject(item.projectId, item.projectName, 'ALL')">
          <svg-icon name="xiangmu" class="projects-item__img" />
          <div class="projects-item__info" @click="toProject(item.projectId, item.projectName, 'ALL')">
            <div class="projects-item__info--name">
              <el-tooltip effect="light">
                <div slot="content">
                  <p v-for="el in tooltipContent" :key="el.value" class="projects-item__tooltip">
                    {{ el.label }}：{{ item[el.value] ? item[el.value] : '-' }}
                  </p>
                </div>
                <span class="name">{{ item.projectName }}</span>
              </el-tooltip>
            </div>
            <span v-show="!!item.dsCount" :title="item.dsCount" class="tag"> DS </span>
            <span v-show="enableSql && !!item.sqlCount" :title="item.sqlCount" class="tag"> SQL </span>
            <span v-show="enableJar && !!item.udjCount" :title="item.udjCount" class="tag"> JAR </span>
          </div>
          <div v-if="menuList.length" class="projects-item__more" @click="clickProject(item)">
            <bs-popover-menu :list="menuList" @click="menuItemClick" />
          </div>
        </div>
        <div class="warn-info" :class="{ 'warn-info--us': isEn }">
          <div
            v-for="info in warnInfo"
            :key="info.value"
            class="info"
            @click="toProject(item.projectId, item.projectName, info.value)"
          >
            <el-badge is-dot class="item" :type="info.type" />
            <span class="label">{{ info.label }} </span>
            <span class="value">{{ item[info.count] || 0 }}</span>
          </div>
        </div>
      </pro-grid>
    </pro-grid>
    <bs-empty v-if="showEmpty" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Watch, Vue, Inject } from 'vue-property-decorator';
import { getProjectList, deleteProject, getFlowList } from '@/apis/flowNewApi';
import dayjs from 'dayjs';
import { hasPermission } from '@/utils';
import { openProjectEditingDialog } from '../../design/flow-list/modals';
@Component
export default class CardListContent extends Vue {
  @Prop() keywords!: string;
  @Inject('enableSql') enableSql;
  @Inject('enableJar') enableJar;
  showEmpty = false;
  loading = false;
  sortBy = 'time';
  //项目列表
  projects: any = [];
  tooltipContent = [
    {
      label: this.$t('pa.flow.projectName'),
      value: 'projectName'
    },
    {
      label: this.$t('pa.flow.remark'),
      value: 'projectMemo'
    },
    {
      label: this.$t('pa.flow.createOrg'),
      value: 'orgId'
    },
    {
      label: this.$t('pa.flow.creater'),
      value: 'createBy'
    },
    {
      label: this.$t('pa.flow.createTime'),
      value: 'createTime'
    },
    {
      label: this.$t('pa.flow.updater'),
      value: 'updatedBy'
    },
    {
      label: this.$t('pa.flow.updateTime'),
      value: 'updateTime'
    }
  ];
  warnInfo = [
    {
      label: this.$t('pa.flow.prod'),
      value: 'PROD',
      type: 'success',
      count: 'onlineCount'
    },
    {
      label: this.$t('pa.flow.pub'),
      value: 'PUB',
      type: 'primary',
      count: 'publishCount'
    },
    {
      label: this.$t('pa.flow.dev'),
      value: 'DEV',
      type: 'info',
      count: 'otherCount'
    }
  ];
  currentNode: any = {};
  get menuList() {
    // 权限过滤
    return [
      { label: this.$t('pa.flow.editProject'), value: 'editProject', access: 'PA.FLOW.PROJECT_MGR.EDIT' },
      { label: this.$t('pa.flow.delProject'), value: 'deleteProject', access: 'PA.FLOW.PROJECT_MGR.DELETE' }
    ].filter((item) => hasPermission(item.access));
  }
  @Watch('keywords')
  handleKeyWordsChange() {
    this.getCardProjectList();
  }
  created() {
    this.getCardProjectList();
  }
  async getCardProjectList(refresh = false) {
    try {
      this.showEmpty = false;
      this.loading = true;
      const { data, success, msg, error } = await getProjectList({
        name: this.keywords,
        sortByName: this.sortBy === 'name'
      });
      if (success) {
        data.forEach((el) => {
          el.createTime = dayjs(el.createTime).format('YYYY-MM-DD HH:mm:ss');
          el.updateTime = dayjs(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
          el.dsCount = Number(el.dsCount);
          el.sqlCount = Number(el.sqlCount);
          el.udjCount = Number(el.udjCount);
        });
        this.projects = data;
        this.showEmpty = !this.projects.length;
        refresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
        this.loading = false;
      } else {
        this.loading = false;
        this.$message.error(error || msg);
      }
    } catch (e) {
      this.loading = false;
    }
  }
  async toProject(id, name, state) {
    try {
      // 从卡片列表进流程设计，默认选中第一个流程
      const { success, data } = await getFlowList({
        id,
        clusterType: 'ALL',
        name: '',
        jobStatus: state,
        jobType: 'ALL',
        mode: 'ALL',
        jobRunTimeStatus: 'ALL'
      });
      if (success) {
        this.loading = false;
        const { nodeId, nodeName, nodeType } = (data || {}).children[0] || {};
        // 第一个流程已经存在对应的tab页面，直接跳转至对应页面
        const existTab = (this as any).$tabNav
          .getAllTabs()
          .find((item) => item.title === nodeName && item.value.split('flowId=')[1] === nodeId);
        if (existTab) {
          const value = existTab.value;
          localStorage.setItem('flow', JSON.stringify({ state, flowId: nodeId }));
          this.$router.push({
            path: value
          });
        } else {
          this.$router.push({
            path: '/flow',
            query: {
              id,
              name,
              title: nodeName || name,
              state,
              flowId: nodeType === 'JOB' ? nodeId : '' // 若第一个节点不是流程 flowId传空
            }
          });
        }
      }
    } catch (e) {
      this.loading = false;
    }
  }
  clickProject(item) {
    this.currentNode = item;
  }
  sortByChange(sortBy) {
    this.sortBy = sortBy;
    this.getCardProjectList();
  }
  async menuItemClick(value) {
    switch (value) {
      case 'editProject':
        const needRefresh = await openProjectEditingDialog(this.currentNode.projectId);
        needRefresh && this.getCardProjectList();
        break;
      case 'deleteProject':
        this.deleteProject();
        break;
    }
  }
  deleteProject() {
    this.$confirm(
      `<p>${this.$t('pa.flow.msg7')}?<p>
       <p style="color: #777777;line-height: 20px;margin-top: 6px;">${this.$t('pa.flow.msg248')}</p>`,
      this.$t('pa.flow.delProject'),
      {
        confirmButtonText: this.$t('pa.flow.confirm'),
        cancelButtonText: this.$t('pa.flow.cancel'),
        dangerouslyUseHTMLString: true
      }
    ).then(async () => {
      const { success, msg, error } = await deleteProject({ id: this.currentNode.projectId });
      if (success) {
        this.$tip.success(msg);
        this.getCardProjectList();
      } else {
        this.$tip.error(error || msg);
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.projects-item__tooltip {
  margin: 6px 0;
  max-width: 376px;
  font-size: 14px;
}
::v-deep .el-loading-mask {
  background-color: transparent;
}
.card-list__content {
  margin-top: 1px;
  width: 100%;
  height: calc(100% - 92px);
  min-height: 60vh;
  overflow-x: hidden;
  .sort {
    height: 57px;
    padding: 21px 0 16px 20px;
    font-size: 14px;
    line-height: 20px;
    .time {
      width: 72px;
      padding-right: 16px;
      margin-right: 16px;
      border-right: 1px solid #e5e5e5;
    }
    .used {
      color: #377cff;
      cursor: pointer;
    }
    .noUsed {
      color: #777777;
      cursor: pointer;
    }
  }
  .center-info {
    height: 100%;
    width: 100%;
  }
  ::v-deep .card {
    height: 170px;
    border-radius: 4px;
    // margin-bottom: 18px;
    .projects-item {
      position: relative;
      display: flex;
      align-items: center;
      height: 130px;
      cursor: pointer;
      background: #fff;
      border-bottom: 1px solid #f1f1f1;
      &__img {
        margin: 34px 16px 52px 32px;
        width: 44px;
        height: 44px;
      }
      &__info {
        width: calc(100% - 75px);
        height: 100%;
        &--name {
          height: 48px;
          margin-top: 32px;
          line-height: 24px;
          font-weight: 500;
          display: flex;
          align-items: center;
          .name {
            display: -webkit-box;
            width: 145px;
            overflow: hidden;
            white-space: normal !important;
            text-overflow: ellipsis;
            word-wrap: break-word;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }

        .tag {
          display: inline-block;
          width: 50px;
          height: 18px;
          margin: 7px 8px 22px 0;
          text-align: center;
          box-sizing: border-box;
          font-size: 12px;
          line-height: 17px;
          border-radius: 4px;
          background-color: #ebf2ff;
          color: #377cff;
          border: 1px solid #c3d8ff;
        }
      }
      &__more {
        position: absolute;
        margin: 0 12px 0 0;
        bottom: 100px;
        right: 0px;
      }
    }

    .warn-info {
      border-radius: 0px 0px 4px 4px;
      background: #fcfcfe;
      line-height: 14px;
      padding: 13px 32px;
      font-size: 12px;
      height: 40px;
      display: flex;
      justify-content: space-between;
      &--us {
        padding: 13px 5px;
      }
      .info {
        display: inline-block;
        cursor: pointer;
        .label {
          color: #777;
          margin-right: 6px;
          @media screen and (max-width: 1400px) {
            margin-right: 0;
          }
        }
        .value {
          color: #444;
        }
        .item {
          width: 6px;
          height: 6px;
          margin-right: 6px;
          margin-bottom: 4px;
        }
      }
    }
  }
}
@media screen and (max-width: 1400px) {
  .bs-pro-grid-col.pro-col-3 {
    width: calc(100% * 4 / 12);
  }
}
@media screen and (max-width: 1000px) {
  .bs-pro-grid-col.pro-col-3 {
    width: 50%;
  }
}
</style>
