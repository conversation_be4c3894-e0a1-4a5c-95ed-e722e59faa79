<template>
  <pro-page :title="$t('pa.menu.routeTemplate')" :fixed-header="false" class="rule">
    <div slot="operation">
      <bs-search
        v-model="searchObj.search"
        class="rule-search"
        :placeholder="$t('pa.params.template.name')"
        size="small"
        @search="debounceSearch"
      />
      <el-button v-access="'PA.SETTING.MODEL.ROUTE.ADD'" type="primary" @click="add"> {{ $t('pa.action.add') }} </el-button>
    </div>
    <pro-table
      ref="proTable"
      :columns="columnData"
      :request="request"
      :actions="actions"
      :options="options"
      @refresh="refresh"
      @action-click="handleActionClick"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { post, del } from '@/apis/utils/net';
import { cloneDeep, debounce } from 'lodash';

@Component({
  name: 'ElementRuleTemplate'
})
export default class ElementRuleTemplate extends Vue {
  refreshStatus = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 }
  };

  debounceSearch = debounce(this.search, 500);
  columnData: any = [];

  options = {
    actionHandle: ({ actions }) => {
      return actions.filter(({ access }) => this.$store.getters.authorities.includes(access));
    },
    actionFixed: false,
    pageOptions: {
      pageSize: this.$store.getters.pageSize,
      currentPage: 1,
      total: 1
    }
  };

  actions = [
    {
      label: this.$t('pa.action.edit'),
      value: 'edit',
      icon: 'iconfont icon-bianji',
      access: 'PA.SETTING.MODEL.ROUTE.EDIT'
    },
    {
      label: this.$t('pa.action.del'),
      value: 'del',
      icon: 'iconfont icon-shanchu',
      access: 'PA.SETTING.MODEL.ROUTE.DELETE'
    },
    {
      label: this.$t('pa.action.viewVersion'),
      value: 'viewHistory',
      icon: 'iconfont icon-lishi',
      access: 'PA.SETTING.MODEL.ROUTE.MENU'
    }
  ];
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.name === 'elementRuletTemplateDetail') {
        vm.search();
      }
    });
  }

  handleActionClick(event, { row }) {
    this[event](row);
  }

  // 新建
  add() {
    this.$router.push({
      path: 'routeTemplateDetail',
      query: { id: '', title: this.$t('pa.params.template.addTemplate') as string }
    });
  }

  // 编辑
  edit(row) {
    this.$router.push({
      path: 'routeTemplateDetail',
      query: {
        id: row.id,
        title: `${this.$t('pa.menu.routeTemplate')}：${row.routeTemplateName || this.$t('pa.params.template.editTemplate')}`
      }
    });
  }

  // 删除
  del(row) {
    this.$confirm(this.$t('pa.params.delConfirm') as string, this.$t('pa.prompt') as string).then(async () => {
      const { error, msg, success } = await del('/rs/pa/route/deleteById', {
        id: row.id
      });
      if (success) {
        this.$message.success(this.$t('pa.params.template.delSuccess'));
        this.$refs.proTable!['loadData']();
      } else {
        this.$message({ message: msg || error, type: 'error' });
      }
    });
  }

  // 搜索
  search() {
    this.$refs.proTable!['loadDataAndReset']();
  }

  //点击刷新图标刷新
  refresh() {
    if (this.refreshStatus) this.$message.success(this.$t('pa.tip.refreshSuccess'));
  }

  // 查看历史版本
  viewHistory(row) {
    this.$router.push({
      path: 'routeTemplateHistory',
      query: {
        id: row.id,
        title: `${this.$t('pa.params.template.historicalRouteTemplate')}：${
          row.routeTemplateName || this.$t('pa.params.template.historicalTemplate')
        }}`
      }
    });
  }

  async request(page) {
    const searchObj = cloneDeep(this.searchObj);
    searchObj.search = searchObj.search.trim();
    const { data, success, msg } = await post('/rs/pa/route/list', {
      search: searchObj.search,
      pageData: page.page
    });
    if (success) {
      data.columnData.forEach((el) => {
        if (el.prop) {
          el.value = el.prop;
          if (el.value === 'updateTime') el.valueProps = { type: 'datetime' };
          if (this.isEn) {
            el.value === 'routeTemplateName' && (el.width = 160);
            el.value === 'resType' && (el.width = 140);
            el.value === 'serviceName' && (el.width = 140);
            el.value === 'serviceAddress' && (el.width = 140);
            el.value === 'memo' && (el.width = 100);
            el.value === 'updatedBy' && (el.width = 100);
          }
        }
      });
      this.columnData = data.columnData;
      this.refreshStatus = true;
      return { data: data.tableData, total: data.pageData.total };
    } else {
      this.refreshStatus = false;
      this.$message.error(msg);
    }
  }
}
</script>

<style lang="scss" scoped>
.rule {
  &-search {
    width: 210px;
    margin-right: 10px;
  }
}
</style>
