<template>
  <pro-page title="路由模板" :fixed-header="false" class="rule">
    <div slot="operation">
      <bs-search
        v-model="searchObj.search"
        class="rule-search"
        placeholder="请输入模板名称"
        size="small"
        @search="debounceSearch"
      />
      <el-button v-access="'PA.SETTING.MODEL.ROUTE.ADD'" type="primary" @click="add">
        新建
      </el-button>
    </div>
    <pro-table
      ref="proTable"
      v-loading="tableLoading"
      :columns="columnData"
      :request="request"
      :actions="actions"
      :options="options"
      @action-click="handleActionClick"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { post, del } from '@/apis/utils/net';
import { cloneDeep, debounce } from 'lodash';
import moment from 'moment';
@Component({
  name: 'ElementRuleTemplate'
})
export default class ElementRuleTemplate extends PaBase {
  tableLoading = false;
  searchObj: ISearchObj = {
    search: '',
    pageData: { pageSize: 20, currentPage: 1, total: 1 }
  };
  tableData: ITableData = {
    columnData: [],
    tableData: []
  };
  debounceSearch = debounce(this.search, 500);
  columnData: any = [
    {
      label: '路由模板',
      value: 'routeTemplateName'
    },
    {
      label: '服务类型',
      value: 'resType'
    },
    {
      label: '集群名称',
      value: 'serviceName'
    },
    {
      label: '集群地址',
      value: 'serviceAddress'
    },
    {
      label: '版本',
      value: 'version'
    },
    {
      label: '备注',
      value: 'memo'
    },
    {
      label: '创建者',
      value: 'createdBy'
    },
    {
      label: '更新者',
      value: 'updatedBy'
    },
    {
      label: '修改时间',
      value: 'updateTime',
      valueProps: {
        type: 'datetime'
      }
    }
  ];

  options = {
    actionHandle: ({ actions }) => {
      return actions.filter(({ access }) =>
        this.$store.state.userInfo.authorities.includes(access)
      );
    }
  };

  actions = [
    {
      label: '编辑',
      value: 'edit',
      icon: 'iconfont icon-bianji',
      access: 'PA.SETTING.MODEL.ROUTE.EDIT'
    },
    {
      label: '删除',
      value: 'del',
      icon: 'iconfont icon-shanchu',
      access: 'PA.SETTING.MODEL.ROUTE.DELETE'
    },
    {
      label: '查看历史版本',
      value: 'viewHistory',
      icon: 'iconfont icon-lishi',
      access: 'PA.SETTING.MODEL.ROUTE.MENU'
    }
  ];

  handleActionClick(event, { row }) {
    this[event](row);
  }

  // 新建
  add() {
    this.$router.push({
      path: 'routeTemplateDetail',
      query: { id: '', title: '新建模板' }
    });
  }

  // 编辑
  edit(row) {
    this.$router.push({
      path: 'routeTemplateDetail',
      query: { id: row.id, title: `路由模板：${row.routeTemplateName || '编辑模板'}` }
    });
  }

  // 删除
  del(row) {
    this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const { error, msg, success } = await del('/rs/pa/route/deleteById', {
        id: row.id
      });
      if (success) {
        this.$message({ message: '删除模板成功', type: 'success' });
        this.$refs.proTable!['loadData']();
      } else {
        this.$message({ message: msg || error, type: 'error' });
      }
    });
  }

  // 搜索
  search() {
    this.$refs.proTable!['loadDataAndReset']();
  }

  // 刷新
  refresh() {
    this.getList();
  }

  // 查看历史版本
  viewHistory(row) {
    this.$router.push({
      path: 'routeTemplateHistory',
      query: {
        id: row.id,
        title: `历史路由模板：${row.routeTemplateName || '历史模板'}`
      }
    });
  }

  async request(page) {
    this.tableLoading = true;
    const searchObj = cloneDeep(this.searchObj);
    searchObj.search = searchObj.search.trim();
    try {
      const { data, success, msg } = await post('/rs/pa/route/list', {
        search: searchObj.search,
        pageData: page
      });
      if (success) {
        this.tableLoading = false;
        return { data: data.tableData, total: data.pageData.total };
      } else {
        this.tableLoading = false;
        this.$message.error(msg);
      }
    } catch {
      this.tableLoading = false;
    }
  }

  async getList() {
    // 获取模板列表
    const searchObj = cloneDeep(this.searchObj);
    searchObj.search = searchObj.search.trim();
    const { data } = await post('/rs/pa/route/list', searchObj);
    data.columnData.push({
      label: '操作',
      value: 'operator',
      fixed: 'right',
      minWidth: '110',
      showOverflowTooltip: false
    });
    data.columnData.forEach((el) => {
      if (el.prop) {
        el.value = el.prop;
        delete el.prop;
      }
    });
    data.tableData.forEach((el) => {
      el.updateTime = moment(el.updateTime).format('YYYY-MM-DD HH:mm:ss');
    });
    this.tableData = {
      ...data
    };
  }

  handleCurrentChange(val) {
    this.searchObj.pageData.currentPage = val;
    this.getList();
  }
}
</script>

<style lang="scss" scoped>
.rule {
  height: calc(100vh - 107px);
  &-search {
    width: 210px;
    margin-right: 10px;
  }
  &-tooltip {
    cursor: pointer;
  }
  &-icon {
    margin: 0 5px;
  }
  &-header {
    height: 50px;
    background: #ffffff;
    border-left: none;
    padding: 0 20px;
    font-size: 16px;
    display: flex;
    align-items: center;
  }
  &-header-operate {
    flex: 1;
    text-align: right;
  }
  &-content {
    height: calc(100% - 58px);
    padding-bottom: 10px;
    overflow: hidden;
    background: #fff;
  }
}
</style>
