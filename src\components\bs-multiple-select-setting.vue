<!--
 * @Description: 
 * @Autor: magicyang
 * @Date: 2020-07-15 14:14:52
 * @LastEditors: magicyang
 * @LastEditTime: 2020-07-15 15:59:58
-->
<template>
  <span style="width: 100%">
    <span
      style="marginleft: 5px; cursor: pointer"
      class="iconfont icon-shezhi1"
      title="配置"
      @click="handleSetting"
    ></span>
    <bs-dialog
      title="选择"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      width="60%"
    >
      <div :style="{ width: '100%', height: this.height }">
        <div style="display: flex; height: 100%">
          <div
            style="
              width: 50%;
              height: 100%;
              overflow-y: scroll;
              word-wrap: break-word;
              paddingleft: 5px;
            "
          >
            <span v-if="leftList.length === 0">数据待添加</span>
            <el-tag
              v-for="tag in leftList"
              :key="tag.label"
              closable
              :type="tag.value"
              size="small"
              style="marginright: 5px; margintop: 3px"
              :disable-transitions="false"
              @close="handleClose(tag)"
            >
              {{ tag.label }}
            </el-tag>
          </div>
          <div style="width: 50%; height: 100%; flex-flow: column">
            <div style="height: 30px">
              <el-input
                v-model="search"
                placeholder="输入关键字进行过滤"
                clearable
                @input="filterList"
              />
            </div>
            <div style="height: calc(100% - 30px)">
              <el-table
                ref="multipleTable"
                :data="tableData22"
                height="100%"
                style="cursor: pointer"
                @select="handleSelect"
                @select-all="handleSelectAll"
                @row-click="handleRowClick"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="name" label="名称" />
              </el-table>
            </div>
          </div>
        </div>
        <div style="display: flex; height: 30px">
          <div style="width: 50%">记录数:{{ leftList.length }}</div>
          <div style="width: 50%">记录数:{{ tableData22.length }}</div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="ok">确定</el-button>
      </div>
    </bs-dialog>
  </span>
</template>
<script lang="ts">
import { Component, Prop, Emit } from 'vue-property-decorator';
import { debounceTime } from 'rxjs/operators';
import { Observable, Observer } from 'rxjs';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {}
})
export default class BsMultipleSelectSetting extends PaBase {
  /**
   * 下拉框的选项
   */
  @Prop()
  private options!: any;
  @Prop({ default: '' })
  private nameProp!: string;
  @Prop({ default: '' })
  private valueProp!: string;
  @Prop({ default: '500px' })
  private height!: string;
  @Prop({ default: [] })
  private multipleSelect!: any;
  search = '';
  tableData22: any = [];
  leftList: any = [];
  filterList!: () => void;
  visible = false;

  created() {
    Observable.create((observer: Observer<string>) => {
      this.filterList = () => {
        observer.next(this.search);
      };
    })
      .pipe(debounceTime(200))
      .subscribe(() => {
        this.getTableList();
      });
    this.getTableList();
  }

  getTableList() {
    this.tableData22 = [];
    this.options.forEach((n) => {
      this.tableData22.push({
        value: n[this.valueProp],
        name: n[this.nameProp]
      });
    });
    if (this.search !== '') {
      this.tableData22 = _.filter(this.tableData22, (n) => {
        return n.name.indexOf(this.search) >= 0;
      });
    }
    // 和左侧列表比较，设置存在的记录为checked
    this.tableData22.forEach((n) => {
      const index = _.findIndex(this.leftList, (o) => {
        const obj: any = o;
        return obj[this.valueProp] === n.value;
      });
      if (index >= 0) {
        this.$nextTick(() => {
          (this.$refs.multipleTable as any).toggleRowSelection(n);
        });
      }
    });
  }

  handleSelect(selection: any) {
    this.leftList = [];
    selection.forEach((n) => {
      const rec = _.find(this.options, (op) => {
        return op[this.valueProp] === n.value;
      });
      this.leftList.push(rec);
    });
  }

  handleSelectAll(selection: any) {
    this.handleSelect(selection);
  }

  handleRowClick(row) {
    const index = _.findIndex(this.leftList, (o) => {
      const obj: any = o;
      return obj[this.valueProp] === row.value;
    });
    if (index < 0) {
      const rec = _.find(this.options, (op) => {
        return op[this.valueProp] === row.value;
      });
      this.leftList.push(rec);
    } else {
      this.removeLeft(index);
    }
    (this.$refs.multipleTable as any).toggleRowSelection(row);
  }

  removeLeft(index) {
    this.leftList.splice(index, 1);
  }

  handleClose(a) {
    this.leftList.splice(this.leftList.indexOf(a), 1);
    const rec = _.find(this.tableData22, { value: a[this.valueProp] });
    if (rec) {
      this.$nextTick(() => {
        (this.$refs.multipleTable as any).toggleRowSelection(rec);
      });
    }
  }

  handleSetting() {
    this.multipleSelect.forEach((n) => {
      const rec = _.find(this.options, (o) => {
        return o[this.valueProp] === n;
      });
      if (rec) {
        this.leftList.push(rec);
      }
    });
    this.getTableList();
    this.visible = true;
  }

  @Emit('setSelectItem')
  ok() {
    const values = this.leftList.map((m) => m[this.valueProp]);
    this.closeDialog();
    return { values };
  }

  closeDialog() {
    this.leftList = [];
    this.tableData22 = [];
    this.search = '';
    this.visible = false;
  }
}
</script>

<style lang="scss" scoped></style>
