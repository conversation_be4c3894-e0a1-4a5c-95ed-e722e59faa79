<template>
  <flow-drawer
    :title="$t('pa.flow.comInfo')"
    :show.sync="display"
    :disabled="disabled"
    :before-close="resetForm"
    :is-full-screen="isFullScreen"
    @submit="handleSubmit"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      :disabled="disabled"
      class="info__container"
    >
      <el-form-item :label="$t('pa.flow.component1')">{{ (data || {}).componentName }} </el-form-item>
      <el-form-item :label="$t('pa.blood.nodeName')" prop="nodeName">
        <el-input
          v-model="formData.nodeName"
          maxlength="40"
          type="text"
          show-word-limit
          :placeholder="$t('pa.flow.msg220')"
        />
      </el-form-item>
      <el-form-item :label="$t('pa.flow.bingxingdu')" prop="parallelism">
        <el-input-number
          v-model="formData.parallelism"
          style="width: 100%"
          :min="1"
          :max="32768"
          number
          :disabled="parallelismDisabled"
          :placeholder="$t('pa.flow.placeholder11')"
        />
      </el-form-item>
    </el-form>
  </flow-drawer>
</template>

<script lang="ts">
import { Component, PropSync, Prop, Ref, Watch, Vue } from 'vue-property-decorator';
import ElForm from 'bs-ui-pro/packages/form';
import cloneDeep from 'lodash/cloneDeep';

@Component({ components: { FlowDrawer: () => import('../../../components/flow-drawer.vue') } })
export default class ComponentInfoConfig extends Vue {
  @PropSync('show', { default: false }) display!: boolean;
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: false }) isFullScreen!: boolean;
  @Prop({ default: false }) disabled!: boolean;
  @Ref('formRef') readonly form!: ElForm;

  private formData: any = { nodeName: '', parallelism: '' };
  private formRules: any = {
    nodeName: { required: true, validator: this.validateNodeName, trigger: 'blur' },
    parallelism: { required: true, message: this.$t('pa.flow.msg57'), trigger: 'blur' }
  };

  get parallelismDisabled() {
    return ['SOURCE_SQL', 'PROCESS_SQL'].includes(this.data?.type);
  }
  @Watch('display')
  handler() {
    this.formData = {
      nodeName: this.display ? this.data?.nodeName : '',
      parallelism: this.display ? this.data?.parallelism : ''
    };
  }

  validateNodeName(rule, value, callback) {
    const result = this.validate(value);
    if (!value) return callback(new Error(this.$t('pa.flow.placeholder7')));
    if (result) return callback(new Error(this.$t('pa.flow.msg221', [result])));
    if ((this.data?.otherNodeNames || []).includes(value)) {
      return callback(new Error(this.$t('pa.flow.msg59')));
    }
    callback();
  }
  async handleSubmit() {
    await this.form.validate();
    this.$emit('submit', ...[this.data?.nodeId, cloneDeep(this.formData)]);
    this.display = false;
  }
  validate(str: string) {
    const invaildStr = '~!@#$&*?/|\\`。，.,';
    const [target, source] = invaildStr.length > str.length ? [str, invaildStr] : [invaildStr, str];
    for (const i of target) {
      if (source.includes(i)) {
        return i;
      }
    }
  }
  resetForm() {
    typeof this.form.resetFields === 'function' && this.form.resetFields();
  }
}
</script>
<style lang="scss" scoped>
.info {
  &__container {
    display: block;
    padding: 20px 0;
    width: 100%;
    height: 100%;
  }
}
</style>
