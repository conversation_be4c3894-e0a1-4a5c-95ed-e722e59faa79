<template>
  <bs-dialog
    :title="$t('pa.formDesign')"
    :visible.sync="display"
    width="80%"
    class="form-design-dialog"
    @close="handleClose"
  >
    <!-- code -->
    <bs-code
      ref="code"
      :value="tempFormConfig"
      language="json"
      :read-only="false"
      :extra-style="{ height: '100%' }"
      @change="handleChange"
    />
    <!-- form -->
    <div class="form-design-form">
      <node-form-new
        v-if="config.newComponent"
        ref="nodeForm"
        :data="param.data"
        :job-data="param.jobData"
        :org-id="$store.getters.orgId"
        :is-design="true"
        :design-code="formConfig"
      />
      <node-form v-else ref="nodeForm" :data="param.data" :job-data="param.jobData" :org-id="$store.getters.orgId" />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ $t('pa.action.close') }}</el-button>
      <el-button type="primary" @click="handleTransform">{{ $t('pa.action.transfer') }}</el-button>
      <el-button v-if="formConfig && config.forms" type="primary" @click="handleSubmit">
        {{ $t('pa.action.mockSubmit') }}
      </el-button>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, PropSync, Vue } from 'vue-property-decorator';
import { safeParse, beautify } from '@/utils';

@Component({
  components: {
    NodeForm: () => import('./node-form.vue'),
    NodeFormNew: () =>
      import('@/views/flow-new/design/page-content/config-popup/component/detail/modals/node-config-new/node-config.vue')
  }
})
export default class FormDesignDialog extends Vue {
  @PropSync('show', { type: Boolean }) display!: boolean;
  @PropSync('code', { type: String }) value!: string;

  tempFormConfig = '';
  formConfig = '';
  param: any = {
    data: { operateType: 'SOURCE', formConf: {} },
    jobData: {},
    versionComponentList: []
  };

  get config() {
    return safeParse(this.formConfig) || {};
  }

  mounted() {
    this.tempFormConfig = this.formConfig = this.value;
    this.formConfig && this.handleTransform();
  }

  handleChange() {
    this.formConfig = (this.$refs.code as any).getValue();
  }

  async handleTransform() {
    this.formConfig = beautify.js_beautify(this.formConfig);
    await this.$nextTick();
    (this.$refs.nodeForm as any) && (this.$refs.nodeForm as any).renderForm(this.config);
  }
  handleClose() {
    this.value = '';
    this.display = false;
  }
  // 模拟提交
  handleSubmit() {
    if (this.config.newComponent) {
      (this.$refs.nodeForm as any).confirm(() => {});
    } else {
      (this.$refs.nodeForm as any).handleSubmit('ruleForm');
    }
  }
}
</script>

<style scoped lang="scss">
$height: 496px;
.form-design {
  &-dialog {
    ::v-deep .el-dialog__body {
      display: flex;
      padding: 0;
      height: $height;
      max-height: $height;
      overflow: hidden;
    }
    .bs-code {
      width: 40%;
      height: $height;
      border-width: 0 1px 0 0;
    }
  }
  &-form {
    padding: 10px;
    width: 60%;
    height: $height;
    box-sizing: border-box;
    overflow-y: auto;
  }
}
</style>
