<template>
  <div class="case__container">
    <!-- 头部 -->
    <div class="case-header">
      <span>测试用例</span>
      <el-button-group>
        <el-button @click="addTestCase">添加测试用例</el-button>
        <el-button :disabled="disabled" @click="$emit('view-log')">日志</el-button>
      </el-button-group>
    </div>
    <!-- 内容 -->
    <div class="case-content">
      <bs-table
        height="240px"
        :data="caseData.tableData"
        :column-data="caseData.columnData"
        :column-settings="false"
      >
        <template slot="operator" slot-scope="{ row }">
          <div>
            <el-tooltip
              v-for="el in actionList"
              :key="el.icon"
              effect="light"
              placement="top"
              :content="el.label"
            >
              <i :class="['iconfont', 'case-content__icon', el.icon]" @click="el.handler(row)"></i>
            </el-tooltip>
          </div>
        </template>
      </bs-table>
    </div>
    <!-- 添加测试用例 -->
    <add-test-case
      v-if="showDialog"
      :data="flowData"
      :type="dialogType"
      :title="dialogTitle"
      :info="currentRowData"
      @close="closeDialog"
    />
    <!--  -->
    <pre-execute-dialog
      v-if="showPreExecuteDialog"
      :job-data="flowData"
      :show.sync="showPreExecuteDialog"
      @submit="handleSubmit"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { URL_TEST_DATA_DELETE, URL_TEST_DATA_LIST } from '@/apis/commonApi';
import { post, del } from '@/apis/utils/net';

@Component({
  components: {
    AddTestCase: () => import('./new/index.vue'),
    PreExecuteDialog: () => import('./components//pre-execute-dialog.vue')
  }
})
export default class TestContent extends Vue {
  @PropSync('loading', { default: false }) caseLoading!: boolean;
  @Prop({ default: () => ({}) }) flowData!: any;
  @Prop({ default: () => ({}) }) disabled!: boolean;

  private caseData: any = {
    columnData: [],
    tableData: []
  };
  private showDialog = false;
  private dialogType = '';
  private dialogTitle = '';
  private currentRowData: any = {};

  private showPreExecuteDialog = false;

  get id() {
    return this.flowData.id;
  }
  get length() {
    return this.flowData.content?.nodes?.length;
  }
  get actionList() {
    return [
      {
        icon: 'icon-chakan',
        label: '数据预览',
        handler: this.dataView
      },
      {
        icon: 'icon-bianji',
        label: '数据修改',
        handler: this.dataEdit
      },
      {
        icon: 'icon-zhihang',
        label: '执行',
        handler: this.dataExecute
      },
      {
        icon: 'icon-shanchu',
        label: '删除',
        handler: this.dataDelete
      }
    ];
  }

  created() {
    this.getCaseList();
  }
  async getCaseList() {
    try {
      this.caseLoading = true;
      const { success, data, error } = await post(`${URL_TEST_DATA_LIST}?jobId=${this.id}`);
      if (success) {
        data.columnData.push({
          label: '操作',
          show: true,
          value: 'operator',
          width: 200,
          showOverflowTooltip: false
        });
        this.caseData = { ...data };
        this.caseLoading = false;
        return;
      }
      this.$tip.error(error);
      this.caseLoading = false;
    } catch {
      this.caseLoading = false;
    }
  }
  addTestCase() {
    if (!this.length) return this.$tip.warning('请先设置流程');
    this.dialogType = 'add';
    this.dialogTitle = '添加测试用例';
    this.showDialog = true;
  }
  dataView(row: any) {
    this.currentRowData = row;
    this.dialogType = 'view';
    this.dialogTitle = `${row.testDataName}数据预览`;
    this.showDialog = true;
  }
  dataExecute(row: any) {
    this.currentRowData = row;
    this.showPreExecuteDialog = true;
  }
  dataEdit(row: any) {
    this.currentRowData = row;
    this.dialogType = 'edit';
    this.dialogTitle = `${row.testDataName}数据修改`;
    this.showDialog = true;
  }
  async dataDelete(row: any) {
    try {
      await this.$confirm(`确定删除【${row.testDataName}】该条数据吗?`, '提示', {
        type: 'warning'
      });
      const { success, msg, error } = await del(URL_TEST_DATA_DELETE, {
        jobId: this.id,
        dataId: row.id
      });
      if (success) {
        this.$tip.success(msg);
        this.$emit('delete', row);
        return this.getCaseList();
      }
      this.$tip.error(error);
    } catch {}
  }
  closeDialog(type) {
    if (type) this.getCaseList();
    this.showDialog = false;
  }
  handleSubmit(config: any) {
    this.$emit('execute', ...[this.currentRowData, config]);
  }
}
</script>

<style lang="scss" scoped>
.case {
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &-content {
    margin-top: 10px;
    height: calc(100% - 52px);
    border: 1px solid #d4dce2;
    background: #fff;
    &__icon {
      cursor: pointer;
      padding: 0 5px;
    }
  }
}
</style>
