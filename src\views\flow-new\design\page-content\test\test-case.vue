<template>
  <div class="case__container">
    <!-- 头部 -->
    <div class="case-header">
      <span>{{ $t('pa.flow.testEp') }}</span>
      <el-button-group>
        <el-button @click="addTestCase">{{ $t('pa.flow.addTest') }}</el-button>
        <el-button :disabled="disabled" @click="$emit('view-log')">{{ $t('pa.flow.log') }}</el-button>
      </el-button-group>
    </div>
    <!-- 内容 -->
    <div class="case-content">
      <bs-table height="240px" :data="caseData.tableData" :column-data="caseData.columnData" :column-settings="false">
        <template slot="operator" slot-scope="{ row }">
          <div>
            <el-tooltip v-for="el in actionList" :key="el.icon" effect="light" placement="top" :content="el.label">
              <i :class="['iconfont', 'case-content__icon', el.icon]" @click="el.handler(row)"></i>
            </el-tooltip>
          </div>
        </template>
      </bs-table>
    </div>
    <!-- 添加测试用例 -->
    <add-test-case
      v-if="showDialog"
      :data="flowData"
      :type="dialogType"
      :title="dialogTitle"
      :info="currentRowData"
      :sql-table="sqlTable"
      @close="closeDialog"
      @set-code-error="(error) => $emit('set-code-error', error)"
    />
    <pre-execute-dialog
      v-if="showPreExecuteDialog"
      :show.sync="showPreExecuteDialog"
      :job-data="flowData"
      @submit="handleSubmit"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import { URL_TEST_DATA_DELETE } from '@/apis/commonApi';
import { del } from '@/apis/utils/net';
import { getTestList } from '@/apis/flowTestApi';
import { MsgType } from '../../interface';

@Component({
  components: {
    AddTestCase: () => import('./new/index.vue'),
    PreExecuteDialog: () => import('./components//pre-execute-dialog.vue')
  }
})
export default class TestContent extends Vue {
  @PropSync('loading', { default: false }) caseLoading!: boolean;
  @Prop({ default: () => ({}) }) flowData!: any;
  @Prop({ default: () => ({}) }) disabled!: boolean;
  @Prop({ default: false }) sqlTable!: any;
  private caseData: any = {
    columnData: [],
    tableData: []
  };
  private showDialog = false;
  private dialogType = '';
  private dialogTitle = '';
  private currentRowData: any = {};

  private showPreExecuteDialog = false;

  get id() {
    return this.flowData.id;
  }
  get length() {
    return this.isFlinkSql ? this.flowData?.content.length : this.flowData.content?.nodes?.length;
  }
  get actionList() {
    return [
      {
        icon: 'icon-chakan',
        label: this.$t('pa.flow.label54'),
        handler: this.dataView
      },
      {
        icon: 'icon-bianji',
        label: this.$t('pa.flow.label55'),
        handler: this.dataEdit
      },
      {
        icon: 'icon-zhihang',
        label: this.$t('pa.flow.run1'),
        handler: this.dataExecute
      },
      {
        icon: 'icon-shanchu',
        label: this.$t('pa.flow.del'),
        handler: this.dataDelete
      }
    ];
  }

  get isFlinkSql() {
    return this.flowData?.jobType === 'FLINK_SQL';
  }

  created() {
    this.getCaseList();
  }
  async getCaseList() {
    try {
      this.caseLoading = true;
      const { success, data, error, msgType } = await getTestList(this.id);
      if (success) {
        data.columnData.push({
          label: this.$t('pa.action.action'),
          show: true,
          value: 'operator',
          width: 200,
          showOverflowTooltip: false
        });
        this.caseData = { ...data };
        this.caseLoading = false;
        return;
      } else {
        if (msgType === MsgType.LINE_MESSAGE) {
          this.$emit('set-code-error', error);
        } else {
          this.$tip.error(error);
        }
      }
      this.caseLoading = false;
    } catch {
      this.caseLoading = false;
    }
  }
  addTestCase() {
    if (!this.length) return this.$tip.warning(this.$t('pa.flow.msg246'));
    this.dialogType = 'add';
    this.dialogTitle = this.$t('pa.flow.addTest');
    this.showDialog = true;
  }
  dataView(row: any) {
    this.currentRowData = row;
    this.dialogType = 'view';
    this.dialogTitle = row.testDataName + this.$t('pa.flow.label54');
    this.showDialog = true;
  }
  dataExecute(row: any) {
    this.currentRowData = row;
    this.showPreExecuteDialog = true;
  }
  dataEdit(row: any) {
    this.currentRowData = row;
    this.dialogType = 'edit';
    this.dialogTitle = row.testDataName + this.$t('pa.flow.label55');
    this.showDialog = true;
  }
  async dataDelete(row: any) {
    try {
      await this.$confirm(this.$t('pa.flow.msg247', [row.testDataName]), this.$t('pa.flow.tip'), {
        type: 'warning'
      });
      const { success, msg, error } = await del(URL_TEST_DATA_DELETE, {
        jobId: this.id,
        dataId: row.id
      });
      if (success) {
        this.$tip.success(msg);
        this.$emit('delete', row);
        return this.getCaseList();
      }
      this.$tip.error(error);
    } catch {}
  }
  closeDialog(type) {
    if (type) this.getCaseList();
    this.showDialog = false;
  }
  handleSubmit(config: any) {
    this.$emit('execute', ...[this.currentRowData, config]);
  }
}
</script>

<style lang="scss" scoped>
.case {
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &-content {
    margin-top: 10px;
    height: calc(100% - 52px);
    border: 1px solid #d4dce2;
    background: #fff;
    &__icon {
      cursor: pointer;
      padding: 0 5px;
    }
  }
}
</style>
