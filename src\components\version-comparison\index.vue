<template>
  <bs-dialog
    v-loading="loading"
    :title="title"
    :visible.sync="visible"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :top="'5vh'"
    width="95%"
  >
    <div slot="title">
      <span class="title bs-dialog-title">{{ $t('pa.action.versionContrast') }}</span>
      <el-tooltip class="item" effect="light" :content="$t('pa.tip.color')" placement="top">
        <i class="iconfont icon-wenhao data-card-icon"></i>
      </el-tooltip>
    </div>
    <div class="diff">
      <div class="diff__title">
        <div class="diff__title__left">{{ $t('pa.selectVersion', [chooseVersion]) }}</div>
        <div class="diff__title__right">
          <span>{{ $t('pa.compareVersion', ['']) }}</span>
          <el-select
            v-model="selectVersion"
            :class="isEn ? 'diff__title--en' : ''"
            :placeholder="$t('pa.tip.compareVersion')"
            @change="selectChange"
          >
            <el-option v-for="item in List" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div>
      <div class="diff__content">
        <div class="diff__content__left">
          <diff-detail :data="leftData" />
        </div>
        <div v-show="rightData.length" class="diff__content__right">
          <diff-detail :data="rightData" />
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="closeDialog">{{ $t('pa.action.makeSure') }}</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { get } from '@/apis/utils/net';
import {
  URL_COMPARE_INIT,
  URL_COMPARE_VERSION,
  URL_TABLE_RESCONF,
  URL_LISTVERSION,
  URL_GETSUBBYPARENT
} from '@/apis/commonApi';
@Component({
  components: {
    'diff-detail': () => import('./diff-detail.vue')
  }
})
export default class SourceCode extends Vue {
  private formLabelWidth = '50px';
  private visible = false;
  @Prop({ default: '' }) id!: any;
  @Prop({ default: '' }) title;
  @Prop() chooseVersion: any;
  private loading = false;
  private formData: any = {};
  private List: any = [];
  selectVersion = '';
  leftData: any = [];
  rightData: any = [];
  leftLabel = '';
  advancedPropertyList: any = [];
  closeDialog() {
    this.visible = false;
  }
  leftId = '';
  async queryLebel() {
    // 根据服务类型查询服务信息配置
  }
  async selectChange() {
    this.loading = true;
    const { success, data, error } = await get(URL_COMPARE_VERSION, {
      leftId: this.leftId,
      rightId: this.selectVersion
    });
    if (success) {
      const resType = data.right[0].resType.value;
      //  根据服务类型查询服务信息配置查 label
      const jsonApi = URL_TABLE_RESCONF + resType.toLowerCase() + '/sql.json';
      const respones = await get(jsonApi);
      if (respones.success && respones.data.level1) {
        data.right.push({
          level1Lebel: {
            value: respones.data.level1.label,
            status: this.leftLabel === respones.data.level1.label ? 'NONE' : 'UPDATE'
          }
        });
      }
      // 设置 left数据
      data.left.forEach((element) => {
        if (element.advanceProperty) {
          this.queryBottomChart(element.advanceProperty, data.left, true);
        }
      });
      data.right.forEach((element) => {
        if (element.advanceProperty) {
          this.queryBottomChart(element.advanceProperty, data.right, false);
        }
      });
      this.leftData = data.left;
      this.rightData = data.right;
    } else {
      this.$message.error(error);
    }
    this.loading = false;
  }
  @Watch('visible')
  async visibleChange(e) {
    if (!e) {
      this.selectVersion = '';
      this.rightData = [];
      return;
    }
    if (this.id) {
      this.loading = true;
      this.leftId = this.id;
      const { success, data } = await get(URL_COMPARE_INIT, {
        id: this.id
      });
      if (success) {
        const resType = data.left[0].resType.value;

        //  根据服务类型查询服务信息配置查 label
        const jsonApi = URL_TABLE_RESCONF + resType.toLowerCase() + '/sql.json';
        const respones = await get(jsonApi);
        if (respones.success && respones.data.level1) {
          data.left.push({
            level1Lebel: {
              value: respones.data.level1.label,
              status: 'NONE'
            }
          });
          this.leftLabel = respones.data.level1.label;
        }

        this.getResTypeList(this.id);

        data.left.forEach((element) => {
          if (element.advanceProperty) {
            this.queryBottomChart(element.advanceProperty, data.left, true);
          }
        });
        this.leftData = data.left;
      }
      this.loading = false;
    }
  }
  // 查询高级表属性 设置左右表
  async queryBottomChart(data: any = [], dataList: any = [], left: any = true) {
    const api = URL_GETSUBBYPARENT;
    this.advancedPropertyList = [];
    const respones = await get(api);

    respones.data.forEach((n) => {
      this.advancedPropertyList.push({
        title: n.title,
        key: n.code,
        value: ''
      });
    });

    if (data.length) {
      this.advancedPropertyList.forEach((element) => {
        data.forEach((item) => {
          if (element.key === Object.keys(item.value)[0]) {
            item.value.title = element.title;
            item.value.value = item.value[Object.keys(item.value)[0]];
          }
        });
      });
    }
    if (left) {
      this.leftData = dataList;
    } else {
      this.rightData = dataList;
    }
  }
  // @Watch('id')
  // async idChange(e) {}
  async getResTypeList(id) {
    this.List = [];
    const { success, data } = await get(URL_LISTVERSION, { id: id });
    if (success) {
      data.forEach((n) => {
        this.List.push({
          value: n.id,
          label: n.tableVersion
        });
      });
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  &::before {
    content: ' ';
    position: relative;
    left: 0;
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 4px;
    border: 3px solid #ff9c00;
    background: #fff;
    border-radius: 50%;
    box-sizing: content-box;
  }
}
.data-card-icon {
  font-size: 14px;
}
.dialog-header {
  display: flex;
  width: 30%;
  font-size: 15px;
  align-items: center;
}
.diff {
  min-width: 1188px;
  &__title {
    display: flex;
    height: 40px;
    &--en {
      width: 290px;
    }
    &__left {
      width: 50%;
      text-align: left;
      padding-left: 20px;
    }
    &__right {
      width: 50%;
      text-align: left;
      padding-left: 20px;
    }
  }
  &__content {
    display: flex;
    justify-content: space-between;
    &__left {
      width: 50%;
    }
    &__right {
      width: 50%;
    }
    &__empty {
      height: 100%;
      border: 1px solid #e2e2e2;
    }
  }
}
</style>
