<template>
  <div class="bar__container">
    <!-- 逻辑关系  -->
    <el-form ref="formRef" :inline="true" :model="formData" :disabled="disabled" class="bar-form">
      <!-- 逻辑类型 -->
      <el-form-item prop="logicType" :label="$t('pa.flow.relation1')" :rules="logicTypeRule">
        <el-select
          v-model="formData.logicType"
          filterable
          class="bar-form__select"
          :class="{ 'bar-form__select--us': isEn }"
          :placeholder="$t('pa.placeholder.select')"
          @change="handleChange($event, 'select')"
        >
          <el-option v-for="el in typeList" :key="el.value" :label="el.label" :value="el.value" />
        </el-select>
      </el-form-item>
      <!-- 逻辑表达式 -->
      <el-tooltip effect="light" placement="top" :disabled="expressionDisabled" :content="formData.expr">
        <el-form-item prop="expr" :rules="exprRule">
          <el-input
            v-model="formData.expr"
            class="bar-form__input"
            :disabled="disabled || inputDisabled"
            @input="handleChange($event, 'input')"
          />
        </el-form-item>
      </el-tooltip>
    </el-form>
    <!-- 右侧 -->
    <el-button class="bar-btn" type="primary" @click="addNewCondition">{{ $t('pa.flow.addCondition') }}</el-button>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import { debounce } from 'lodash';
import ElForm from 'bs-ui-pro/packages/form/index.js';

@Component
export default class Bar extends Vue {
  @Prop({ default: false }) disabled!: boolean;
  @PropSync('data', { default: () => ({}) }) formData!: any;
  @Ref('formRef') readonly form!: ElForm;

  private logicTypeRule = {
    required: true,
    message: this.$t('pa.placeholder.select'),
    trigger: 'change'
  };
  private addNewCondition = debounce(() => this.$emit('increase'), 1200);
  private typeList: any[] = [
    {
      value: '&&',
      label: this.$t('pa.flow.label1')
    },
    {
      value: '||',
      label: this.$t('pa.flow.label2')
    },
    {
      value: 'CUSTOM',
      label: this.$t('pa.flow.label3')
    }
  ];

  get exprRule() {
    return {
      required: true,
      type: this.formData.logicType,
      conditions: this.formData.conditions,
      validator: (rule, value, callback) => {
        if (!value) return callback(new Error(this.$t('pa.placeholder.input')));
        const letter = rule.conditions.map(({ name }: any) => name).join('|');
        const rules = new RegExp(`(\\&\\&|\\|\\||\\(|\\)|\\s|${letter})+`, 'g');
        const str = value.replace(rules, '');
        if (str.length > 0) return callback(new Error(this.$t('pa.flow.msg181')));
        if (rule.type === 'CUSTOM') {
          const result = rule.conditions
            .map(({ name }: any) => (value.includes(name) ? null : this.$t('pa.flow.msg185', [name])))
            .filter(Boolean);
          if (result.length > 0) {
            return callback(new Error(this.$t('pa.flow.msg186', [result.join('、')])));
            return;
          }
        }
        callback();
      },
      trigger: 'blur'
    };
  }

  get inputDisabled() {
    return this.formData.logicType !== 'CUSTOM';
  }

  get expressionDisabled() {
    if (this.inputDisabled) {
      return this.formData.expr.length < (this.formData.logicType === '&&' ? 50 : 70);
      return false;
    }
    return !this.disabled;
  }

  async handleChange(type, name) {
    await this.$emit('change', ...[type, name]);
    if (name !== 'input') return;
    // this.checkExpression();
    //  const letter = this.conditions.map(({ name }: any) => name).join('|');
    // const rule = new RegExp(`(\\&\\&|\\|\\||\\(|\\)|\\s|${letter})+`, 'g');
    // const str = this.expression.replace(rule, '');
    // this.errorMsg = str.length > 0 ? '请输入合法的逻辑关系！' : '';
  }
  validate() {
    return this.form.validate();
  }
}
</script>
<style lang="scss" scoped>
.bar {
  &__container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 35px;
  }
  &-form {
    ::v-deep .el-form-item {
      margin-bottom: 20px;
    }
    &__select {
      width: 120px;
      &--us {
        width: 190px;
      }
    }
    &__input {
      width: 450px;
    }
  }
  &-btn {
    margin-bottom: 20px;
  }
  &__left {
    display: flex;
    align-items: center;
    width: calc(100% - 200px);
    background: pink;

    &__title {
      margin-right: 12px;
      font-size: 14px;
      font-weight: 400;
      color: #444444;
      line-height: 20px;
    }
  }

  &-result {
    width: calc(100% - 100px);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &-logic {
    &__form {
      display: flex;
      align-items: center;
      width: calc(100% - 76px);

      &__item {
        margin-bottom: 0px !important;
      }

      &__type {
        margin-right: 10px;
        width: 120px;
      }

      &__expression {
        width: 360px;
      }
    }
  }
}
</style>
