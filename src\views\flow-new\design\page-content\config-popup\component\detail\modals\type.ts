export interface TableItem {
  id: string;
  value: string;
  isPrimarykey: boolean;
  isPartition?: boolean;
}

export interface FormData {
  jdbcType: string;
  resTitle: string; // 服务名称
  resId: string; // 服务Id
  address: string; //服务地址
  tableType: string; // 表匹配方式
  subName: string; // 表
  dynamicTable: string; // 动态表名
  outputFields: string[]; //输出字段
  outputColumns: string; // 数据库列名
  isUpsert: string; // 是否UPSERT
  batchSize: number; // 批量大小
  timeout: number; // 超时时间
  upsertFieldSet: string[];
  upsertPrimaryKeySet: string[];
  localPartition: string;
  partitionField: string;
}

interface Info {
  resId: string;
  resTitle: string;
  address: string;
}
export interface Option {
  label: string;
  value: string;
  info?: Info;
  type?: string;
}

export interface RenderListItem {
  show: boolean;
  label: string;
  fieid: string;
  type: string;
  options?: Option[];
  min?: number;
  max?: number;
  placeholder?: string;
  tooltip?: string;
  rules?: any;
  clearable?: boolean;
  config?: boolean;
  readonly?: boolean;
  autosize?: any;
  remotemethod?: any;
  disabled?: boolean;
  appendText?: string;
  virtualLoading?: boolean;
  showAll?: boolean;
  multiple?: boolean;
}
