// 自定义指令
import Vue from 'vue';
import store from '@/store';
import hljs from '@/plugins/use-highlight';
const installDirectives = () => {
  Vue.directive('access', {
    bind: (el: HTMLElement, { value }: any) => {
      const { authorities = [] } = store.getters;
      let hasPermission = true;
      if (Array.isArray(value)) {
        hasPermission = value.every((item) => authorities.includes(item));
      } else {
        hasPermission = authorities.includes(value);
      }
      value && !hasPermission && (el.style.display = 'none');
    }
  });
  Vue.directive('loadmore', {
    bind(el: any, binding: any) {
      // 获取element-ui定义好的scroll盒子
      const SelectDom = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
      SelectDom!.addEventListener('scroll', () => {
        const CanTrigger = SelectDom.scrollHeight - SelectDom.scrollTop <= SelectDom.clientHeight;
        if (CanTrigger) {
          binding.value();
        }
      });
    }
  });

  Vue.directive('drawerDrag', {
    bind(el, { value }) {
      const minWidth = typeof value === 'number' ? value : 400;
      const dragDom = el.querySelector('.el-drawer') as Element;
      const resizeElL = document.createElement('div');
      resizeElL.classList.add('drag-drawer');
      dragDom.appendChild(resizeElL);
      resizeElL.style.position = 'absolute';
      resizeElL.style.top = '0px';
      resizeElL.style.left = '0px';

      resizeElL.onmousedown = (e) => {
        const elW = (dragDom as any).clientWidth;
        const EloffsetLeft = (dragDom as any).offsetLeft;
        const clientX = e.clientX;
        document.onmousemove = function (e) {
          e.preventDefault();
          // 左侧鼠标拖拽位置
          if (clientX > EloffsetLeft && clientX < EloffsetLeft + 10) {
            // 往左拖拽
            if (clientX > e.clientX) {
              if (e.clientX > 266) {
                (dragDom as any).style.width = elW + (clientX - e.clientX) + 'px';
              }
            }
            // 往右拖拽
            if (clientX < e.clientX) {
              if ((dragDom as any).clientWidth >= minWidth) {
                (dragDom as any).style.width = elW - (e.clientX - clientX) + 'px';
              }
            }
          }
        };
        // 拉伸结束
        document.onmouseup = function () {
          document.onmousemove = null;
          document.onmouseup = null;
        };
      };
    }
  });

  Vue.directive('dragging', {
    bind(el, { value }) {
      const dragDom = el;
      const minHeight = typeof value === 'number' ? value : '300';
      const resizeElL = document.createElement('span');
      resizeElL.classList.add('drag-row--resize');
      el.appendChild(resizeElL);

      resizeElL.onmousedown = (e) => {
        const clientY = e.clientY;
        const elH = (dragDom as any).clientHeight;
        document.onmousemove = function (e) {
          e.preventDefault();
          // 往上拖拽
          if (clientY > e.clientY) {
            if ((dragDom as any).offsetTop > 0) {
              (dragDom as any).style.height = elH + (clientY - e.clientY) + 'px';
            }
          }
          // 往下拖拽
          if (clientY < e.clientY) {
            if ((dragDom as any).clientHeight >= minHeight) {
              (dragDom as any).style.height = elH - (e.clientY - clientY) + 'px';
            }
          }
        };
        // 拉伸结束
        document.onmouseup = function () {
          document.onmousemove = null;
          document.onmouseup = null;
        };
      };
    }
  });
  Vue.directive('hide', {
    inserted(el, bind, vNode) {
      if (el.clientWidth === el.scrollWidth) {
        (vNode as any).componentInstance.$destroy();
      }
    }
  });
  const highlightBlock = (el) => {
    el.querySelectorAll('code').forEach((block: any) => hljs.highlightBlock(block));
  };
  Vue.directive('highlight', {
    bind(el) {
      highlightBlock(el);
    },
    update(el) {
      highlightBlock(el);
    }
  });
};
export default installDirectives;
