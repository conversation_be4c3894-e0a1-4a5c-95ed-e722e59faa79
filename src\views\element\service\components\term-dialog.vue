<template>
  <bs-dialog width="80%" :title="title" :visible.sync="display" :before-close="closeDialog" class="term-dialog">
    <div id="terminal" style="height: 100%"></div>
    <!-- footer -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">{{ $t('pa.action.close') }}</el-button>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, PropSync, Prop, Vue } from 'vue-property-decorator';
import { FitAddon } from 'xterm-addon-fit';
import { Terminal } from 'xterm';
import 'xterm/css/xterm.css';
import { baseUrl } from '@/config';
const SockJS = require('sockjs-client');
const Stomp = require('stompjs');

@Component
export default class TermDialog extends Vue {
  @PropSync('show', { required: true, default: false }) display!: boolean;
  @Prop({ default: () => ({}) }) data!: any;

  title = '';
  uid = '';
  height = 0;
  term: any = null;
  stompClient: any = null;

  mounted() {
    this.open(this.data.id, this.data.ip);
  }
  beforeDestroy() {
    this.disconnect();
  }

  async open(hostId: string, ip: string, dir = '') {
    this.title = this.$t('pa.tip.terminal', [ip]);
    this.uid = `test${Date.now()}`;
    const socket = new SockJS(this.getUrl());
    this.stompClient = Stomp.over(socket);
    await this.$nextTick();
    this.stompClient.connect({}, () => {
      this.stompClient.subscribe(`/user/${this.uid}/queue/getResponse`, (resp: any) => this.term.write(resp.body));
      this.term = new Terminal({ scrollback: 800, cursorBlink: true });
      const fitAddon = new FitAddon();
      this.term.loadAddon(fitAddon);
      this.term.open(document.getElementById('terminal'));
      this.term.focus();
      fitAddon.fit();
      let message = '\n';
      dir && (message = `cd ${dir}${message}`);
      this.stompClient.send(
        '/sendMessage',
        {},
        JSON.stringify({
          uid: this.uid,
          hostId,
          wp: this.term.cols,
          message: message
        })
      );
      this.term.write(this.$t('pa.tip.logging'));
      this.term.onData((data: any) => {
        this.stompClient.send(
          '/sendMessage',
          {},
          JSON.stringify({
            uid: this.uid,
            hostId,
            wp: this.term.cols,
            message: data
          })
        );
      });
    });
  }
  disconnect() {
    try {
      this.stompClient.disconnect();
    } catch {}
    try {
      this.term.dispose();
    } catch {}
  }
  getUrl() {
    if (process.env.NODE_ENV === 'development') {
      return 'http://' + window.location.hostname + ':' + window.location.port + baseUrl.prev + '/endpointOyzc';
    } else {
      return (
        'http://' +
        window.location.hostname +
        ':' +
        window.location.port +
        window.location.pathname.replace('/pipeace.html', '') +
        '/endpointOyzc'
      );
    }
  }
  closeDialog() {
    this.disconnect();
    this.display = false;
  }
}
</script>

<style lang="scss" scoped>
.term-dialog {
  ::v-deep .el-dialog__body {
    padding: 0px;
    height: calc(70vh - 108px);
  }
}
</style>
