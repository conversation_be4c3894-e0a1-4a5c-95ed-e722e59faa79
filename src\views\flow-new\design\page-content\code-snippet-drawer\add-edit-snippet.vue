<template>
  <el-drawer
    class="add-edit-snippet"
    :class="{ 'inner-full-screen': fullScreen, 'inner-full-screen__us': fullScreen && isEn, 'add-edit-snippet__us': isEn }"
    append-to-body
    :modal="false"
    :size="isEn ? 500 : 400"
    :modal-append-to-body="false"
    :visible.sync="addSnippetDrawerVisible"
    :before-close="handleDrawerClose"
  >
    <!-- 头部信息 -->
    <div slot="title" class="add-edit-snippet__header">
      <span class="add-edit-snippet__title">{{ title }}</span>
    </div>
    <!-- 内容区域 -->
    <div class="add-edit-snippet__content">
      <el-form ref="addEditForm" :model="addEditFormData" :rules="addEditFormRules" :label-width="isEn ? '170px' : '110px'">
        <el-form-item :label="$t('pa.flow.codeName')" prop="name">
          <el-input
            v-if="isAdd || isEdit"
            v-model="addEditFormData.name"
            :placeholder="$t('pa.flow.placeholder15')"
            maxlength="200"
            clearable
          />
          <el-tooltip v-if="isView && !isAdd" :key="keys" v-hide :content="addEditFormData.name" effect="light">
            <span class="add-edit-snippet__view">
              {{ addEditFormData.name }}
            </span>
          </el-tooltip>
        </el-form-item>
        <el-form-item :label="$t('pa.flow.codeKey')" prop="shortCode">
          <el-input
            v-if="isAdd || isEdit"
            v-model="addEditFormData.shortCode"
            :placeholder="$t('pa.flow.placeholder16')"
            clearable
            maxlength="30"
          />
          <el-tooltip
            v-if="isAdd || isEdit"
            class="code-snippet-library__tooltip"
            effect="light"
            :content="$t('pa.flow.msg74')"
          >
            <i class="iconfont icon-wenhao" style="cursor: pointer"></i>
          </el-tooltip>
          <el-tooltip v-if="isView && !isAdd" :key="keys" v-hide effect="light" :content="addEditFormData.shortCode">
            <span class="add-edit-snippet__view">{{ addEditFormData.shortCode }}</span>
          </el-tooltip>
        </el-form-item>
        <el-form-item :label="$t('pa.flow.remark')" prop="memo">
          <el-input
            v-if="isAdd || isEdit"
            v-model="addEditFormData.memo"
            :placeholder="$t('pa.flow.placeholder0')"
            clearable
            maxlength="255"
          />
          <el-tooltip v-if="isView && !isAdd" :key="keys" v-hide effect="light" :content="addEditFormData.memo">
            <span class="add-edit-snippet__view">{{ addEditFormData.memo || '-' }}</span>
          </el-tooltip>
        </el-form-item>
        <el-form-item class="add-edit-snippet__code" :label="$t('pa.flow.sqlCode')" prop="body">
          <div class="add-edit-snippet__content--sql">
            <bs-code
              ref="snippetCode"
              class="add-edit-snippet__content--code"
              :class="{ 'is-view': isView && !isAdd }"
              :value="addEditFormData.body"
              language="sql"
              :read-only="isView && !isAdd"
              :operatable="false"
              :extra-style="{ height: calcListHeight }"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>
    <!-- 底部按钮区域 -->
    <div class="add-edit-snippet__footer">
      <el-button
        v-for="item in buttonList"
        v-show="item.show"
        :key="item.label"
        class="add-edit-snippet__footer--button"
        :type="item.type"
        :loading="item.loading ? confirmLoading : false"
        @click="operateHandler(item.event)"
      >
        {{ item.label }}
      </el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
import { Vue, Component, Prop, PropSync, Watch, Ref } from 'vue-property-decorator';
import { addSql, updateSql, delSql } from '@/apis/sqlApi';
import ElForm from 'bs-ui-pro/lib/form';
import cloneDeep from 'lodash/cloneDeep';

interface AddEditForm {
  body: string;
  memo: string; // 备注
  name: string; // 片段名称
  shortCode: string; // 片段缩略词
  type: string;
  id?: string;
}
interface SnippetStatus {
  isEdit: boolean;
  [property: string]: any;
}
@Component({
  directives: {
    hide: {
      inserted(el, bind, vnode: any) {
        el.clientWidth === el.scrollWidth && vnode.componentInstance.$destroy();
      }
    }
  }
})
export default class AddEditSnippet extends Vue {
  @PropSync('show', { default: false }) addSnippetDrawerVisible!: boolean;
  @Prop({ default: null }) data!: null | SnippetStatus;
  @Prop({ default: false }) public!: boolean;
  @Prop({ type: Boolean, default: false }) fullScreen!: boolean;
  @Ref('addEditForm') readonly addEditForm!: ElForm;
  addEditFormData: AddEditForm = {
    body: '', // sql代码
    memo: '', // 备注
    name: '', // 片段名称
    shortCode: '', // 片段缩略词
    type: '0' // 类型（公用/个人）
  };
  keys = 0;
  // 代码框计算高度
  calcListHeight = '';
  // 表单校验规则
  addEditFormRules = {
    name: [{ required: true, validator: this.validateName, trigger: 'blur' }],
    shortCode: [{ required: true, validator: this.validateShortCode, trigger: 'blur' }],
    body: [{ required: true, message: this.$t('pa.placeholder.sqlPlaceholder'), trigger: 'blur' }]
  };
  confirmLoading = false;
  get title() {
    if (!this.data) return this.$t('pa.flow.newSql');
    return this.data.isEdit ? this.$t('pa.flow.editSql') : this.$t('pa.flow.sqlDetail');
  }

  // 新建
  get isAdd() {
    return !this.data;
  }

  // 编辑
  get isEdit() {
    return this.data?.isEdit;
  }

  // 查看
  get isView() {
    return !this.data?.isEdit;
  }

  get buttonList() {
    return [
      {
        show: this.isView && !this.isAdd && !this.public,
        event: 'handleEdit',
        label: this.$t('pa.flow.edit')
      },
      {
        show: this.isView && !this.isAdd,
        event: 'delSnippet',
        label: this.$t('pa.flow.del'),
        type: 'primary',
        loading: true
      },
      {
        show: this.isAdd || this.isEdit,
        event: 'handleDrawerClose',
        label: this.$t('pa.flow.cancel')
      },
      {
        show: this.isAdd || this.isEdit,
        event: 'addEditSqlSnippet',
        label: this.$t('pa.flow.confirm'),
        type: 'primary',
        loading: true
      }
    ];
  }

  @Watch('data', { immediate: true, deep: true })
  async handleDataChange(data) {
    this.addEditForm && this.addEditForm.clearValidate();
    if (!data)
      return (this.addEditFormData = {
        body: '', // sql代码
        memo: '', // 备注
        name: '', // 片段名称
        shortCode: '', // 片段缩略词
        type: '0' // 类型（公用/个人）
      });
    const {
      detail: { body, id, memo, name, shortCode, type }
    } = data;
    this.addEditFormData = { body, id, memo, name, shortCode, type };
    this.keys = Date.now();
  }

  mounted() {
    this.getHeight();
  }

  operateHandler(event: string) {
    event && this[event]();
  }

  // 空值/空格校验
  validateName(rule: any, value: string, callback: any) {
    if (!value) callback(new Error(this.$t('pa.flow.placeholder7')));
    if (new RegExp(/\s+/g).test(value)) callback(new Error(this.$t('pa.flow.msg75')));
    callback();
  }

  // 添加sql代码片段
  async addEditSqlSnippet() {
    try {
      await (this.addEditForm as ElForm).validate();
      this.confirmLoading = true;
      const params = cloneDeep(this.addEditFormData);
      params.body = this.$store.getters.encrypt(params.body);
      const { success, error, msg } = !this.data ? await addSql(params) : await updateSql(params);
      if (success) {
        this.$message.success(!this.data ? this.$t('pa.data.udf.detail.tips.addSuccess') : this.$t('pa.flow.editSuccess'));
        this.$emit('refresh');
      } else {
        this.$message.error(error || msg);
      }
      this.confirmLoading = false;
    } catch (error) {}
  }

  // 根据屏幕大小设定列表高度
  getHeight() {
    const calcListHeight = `${document.documentElement.clientHeight - (this.fullScreen ? 370 : 470)}px`;
    this.calcListHeight = calcListHeight;
    window.onresize = () => {
      this.calcListHeight = calcListHeight;
    };
  }

  // 删除代码片段
  async delSnippet() {
    await this.$confirm(this.$t('pa.flow.msg76'), this.$t('pa.flow.tip'));
    const { success, error, msg } = await delSql([this.addEditFormData.id]);
    if (success) {
      this.$message.success(this.$t('pa.tip.delSuccess'));
      this.$emit('refresh');
      this.handleDrawerClose();
    } else {
      this.$message.error(error || msg);
    }
  }

  handleDrawerClose() {
    this.addSnippetDrawerVisible = false;
  }

  handleEdit() {
    this.data && (this.data.isEdit = true);
  }

  validateShortCode(rule, value, callback) {
    if (!value) return callback(new Error(this.$t('pa.flow.placeholder16')));
    if (!new RegExp(/^[A-z0-9.]+$/g).test(value[0])) return callback(new Error(this.$t('pa.flow.msg77')));
    if (new RegExp(/\s+/g).test(value)) return callback(new Error(this.$t('pa.flow.msg75')));
    callback();
  }
}
</script>

<style lang="scss" scoped>
.add-edit-snippet__code {
  ::v-deep .el-form-item__error {
    margin-left: -65px;
  }
}
.add-edit-snippet {
  top: 95px;
  bottom: 11px;
  width: 435px;
  left: calc(100% - 900px);
  z-index: 1999 !important;
  border-right: 1px solid #f1f1f1;
  &__us {
    width: 535px;
    left: calc(100% - 1005px);
  }
  .is-view {
    margin-top: 37px !important;
  }
  .icon-wenhao {
    position: absolute;
    right: -20px;
  }
  ::v-deep .bs-code {
    min-width: 320px;
    margin-left: -65px;
  }
  &__view {
    display: inline-block;
    width: 100%;
    line-height: 22px;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
    overflow: hidden;
  }
  &__title {
    font-weight: 500;
    color: #377cff;
  }
  &__content {
    margin-top: 24px;
    margin-right: 35px;
    &--formatter {
      display: flex;
      justify-content: flex-end;
      color: #5d94fc;
      cursor: pointer;
    }
    &--code {
      margin-top: 35px;
    }
  }
  &__footer {
    width: 100%;
    text-align: center;
    &--button {
      width: 42%;
    }
  }
}
.inner-full-screen {
  top: 0;
  left: calc(100% - 880px);
  height: calc(100% - 10px);
  &__us {
    left: calc(100% - 985px);
  }
}
</style>
