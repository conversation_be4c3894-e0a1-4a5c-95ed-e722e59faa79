<template>
  <bs-table
    ref="tableRef"
    border
    stripe
    show-index
    size="mini"
    row-key="id"
    :height="380"
    index-width="50px"
    paging-front
    :data="validData"
    :page-data="pageData"
    :crossing="!disabled"
    :selection="!disabled"
    :column-settings="false"
    :column-data="columnData"
    :checked-rows="checkedData"
    :show-multiple-selection="false"
    @page-change="handlePageChange"
    @selection-change="selectionChange"
  >
    <!-- 选择全部 -->
    <template slot="footer-expand">
      <el-checkbox v-if="!disabled" v-model="checkAll" @change="handleCheckedAll">
        选择全部<span class="config-select__total">（已选择{{ checkedData.length }}项）</span>
      </el-checkbox>
    </template>
    <!-- 字段名称 -->
    <template slot="name" slot-scope="{ row }">
      <el-autocomplete
        v-model="row.name"
        size="small"
        :disabled="getDisabled(row)"
        :fetch-suggestions="handleNameSuggestions"
        @select="handleNameSelect($event, row)"
      />
    </template>
    <!-- 字段类型 -->
    <template slot="type" slot-scope="{ row }">
      <el-select v-model="row.type" size="small" :disabled="true">
        <el-option v-for="el in typeList" :key="el" :label="el" :value="el" />
      </el-select>
    </template>
    <!-- 处理规则 -->
    <template slot="method" slot-scope="{ row }">
      <bs-select
        v-model="row.method"
        filterable
        clearable
        size="small"
        virtual-loading
        placeholder="请选择"
        class="config-select"
        :options="methodOptions"
        :disabled="getDisabled(row)"
        @change="handleMethodChange($event, row)"
        @visible-change="handleVisibleChange($event, row)"
      />
    </template>
    <!-- 参数 -->
    <template slot="params" slot-scope="{ row }">
      <div v-if="vaildArray(row.params)">
        <div v-for="(el, index) in row.params" :key="index" class="config-parmas">
          <div class="config-parmas__type">
            <span class="config-parmas__label">参数类型：</span>
            <span> {{ el.type }}</span>
          </div>
          <div class="config-parmas__value">
            <span class="config-parmas__label">值：</span>
            <el-autocomplete
              v-model="row.params[index].value"
              size="small"
              :disabled="getDisabled(row)"
              :fetch-suggestions="handleParamsSuggestions"
            />
          </div>
        </div>
      </div>
      <span v-else></span>
    </template>
    <!-- 操作 -->
    <template slot="operator" slot-scope="{ row }">
      <div class="consfig-operator">
        <el-button v-if="disabled ? false : !row.disabled" type="text" @click="handleDetete(row)">
          删除
        </el-button>
      </div>
    </template>
  </bs-table>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue, Watch } from 'vue-property-decorator';
import isEqual from 'lodash/isEqual';
import cloneDeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';
import BsTable from 'bs-ui-pro/lib/bs-table';
import { VALID_TYPE, vaildArray, getSuggestions, getOptions } from './utils';

@Component
export default class ConfigTable extends Vue {
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => ({}) }) pageData!: any;
  @Prop({ default: () => [] }) inputFields!: any[];
  @Prop({ default: () => [] }) shareMethods!: any[];
  @Prop({ default: () => [] }) privateMethods!: any[];
  @Prop({ default: '' }) keyword!: string;
  @PropSync('data', { default: () => [] }) tableData!: any[];
  @Ref('tableRef') readonly table!: BsTable;

  private checkAll = false;
  private columnData = [
    {
      label: '字段名称',
      value: 'name',
      minWidth: 130,
      showOverflowTooltip: false
    },
    {
      label: '字段类型',
      value: 'type',
      minWidth: 130,
      showOverflowTooltip: false
    },
    {
      label: '处理规则',
      value: 'method',
      minWidth: 230,
      showOverflowTooltip: false
    },
    {
      label: '参数',
      value: 'params',
      minWidth: 320,
      showOverflowTooltip: false
    },
    {
      label: '操作',
      value: 'operator',
      width: 58,
      fixed: 'right',
      showOverflowTooltip: false
    }
  ];
  private typeList: string[] = VALID_TYPE;
  private methodOptions: any[] = []; // 处理规则下拉选项配置
  private vaildArray = vaildArray;
  private selectionChange = debounce(this.handleSelectionChange, 500);

  get validData() {
    return this.tableData.filter((el) => !el.hide);
  }

  get checkedData() {
    return this.tableData.filter(({ outputable }) => outputable);
  }

  get assetFuncs() {
    return cloneDeep(this.$store.state.asset.assetFuncs);
  }
  get assetFields() {
    return (this.$store.state.asset.assetFields || []).reduce((pre: any[], next: any) => {
      next.value = next.fName;
      pre.push(next);
      return pre;
    }, []);
  }
  @Watch('tableData', { deep: true })
  handler() {
    this.$nextTick(() => {
      this.table.$refs?.bsTable?.doLayout();
    });
  }

  @Watch('validData', { deep: true })
  handleValidDataChange(data) {
    // 表格内容发生变化时（用户搜索），同步修改"选择全部"按钮状态
    this.$nextTick(() => {
      this.checkAll = data.length ? data.every((el) => el.outputable) : false;
    });
  }

  /* 处理选择全部事件 */
  handleCheckedAll(checked: boolean) {
    // 当搜索关键词为空时，全量表格数据跟随"选择全部"进行联动
    if (!this.keyword) return this.tableData.forEach((el) => this.$set(el, 'outputable', checked));
    // 当有搜索关键词时，搜索项内容跟随"选择全部"进行联动
    this.tableData.forEach((el) => {
      !el.hide && this.$set(el, 'outputable', checked);
    });
  }

  /* 获取disabled */
  getDisabled({ disabled }: any = {}) {
    return this.disabled || disabled;
  }
  /* 处理name输入建议 */
  handleNameSuggestions(queryStr, cb) {
    cb(getSuggestions(queryStr, this.assetFields));
  }
  /* 处理name建议选择事件 */
  handleNameSelect({ fName, fType }: any, row: any) {
    this.$set(row, 'name', fName);
    this.$set(row, 'type', fType);
  }
  /* 获取处理规则聚焦事件 */
  handleMethodFocus(row): any[] {
    const index = this.getIndex(row);
    if (typeof index !== 'number') return [];
    const validData = this.tableData.slice(0, index);
    return [
      {
        label: '字段',
        value: 'field',
        options: getOptions(validData, '#')
      },
      {
        label: '共享方法',
        value: 'shareMethods',
        options: getOptions(this.shareMethods)
      }
    ];
  }
  handleVisibleChange(visible, row) {
    this.methodOptions = !visible ? [] : this.handleMethodFocus(row);
  }
  /* 处理处理规则选中事件 */
  handleMethodChange(method: string, row) {
    const isFieid = method.includes('#');
    const source = isFieid ? this.tableData : [...this.shareMethods, ...this.privateMethods];
    const target = source.find((el) => el.name === method.replaceAll('#', ''));
    if (target) {
      if (isFieid) {
        this.$set(row, 'type', target.type);
        this.$set(row, 'params', []);
        return;
      }
      this.typeList.includes(target.retType) && this.$set(row, 'type', target.retType);
      this.$set(row, 'funcType', target.funcType);
      this.$set(
        row,
        'params',
        target.paramTypes.map((type) => ({ type, value: '' }))
      );
      return;
    }
    this.$set(row, 'params', []);
  }

  /* 处理参数值建议 */
  handleParamsSuggestions(queryStr: string, cb: any) {
    const list = this.inputFields.map((el) => ({ value: `#${el.name}#` }));
    cb(getSuggestions(queryStr, list));
  }
  /* 处理删除事件 */
  async handleDetete(row: any) {
    await this.$confirm('您确定要删除该行数据吗?', '提示');
    const index = this.tableData.findIndex(({ id }) => id === row.id);
    index > -1 && this.tableData.splice(index, 1);
    this.$emit('detete', this.pageData.currentPage);
  }
  /* 获取下标 */
  getIndex(target: any) {
    const index = this.tableData.findIndex((el) => isEqual(el, target));
    return index > -1 ? index : null;
  }
  /* 处理页数变化 */
  handlePageChange(currentPage, pageSize) {
    this.$emit('page-change', ...[currentPage, pageSize]);
  }
  handleSelectionChange(data: any) {
    // 当表格中无搜索项内容时，选择全部为未勾选状态。搜索项内容跟随页面展示内容的选中状态
    this.checkAll = !this.validData.length ? false : this.validData.every((el) => el.outputable);
    const idList = data.map(({ id }) => id);
    this.tableData.forEach((el) => this.$set(el, 'outputable', idList.includes(el.id)));
  }
}
</script>
<style lang="scss" scoped>
%flex-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep .bs-table {
  &-footer-slot {
    padding-left: 0;
  }
  &__index {
    .cell {
      padding: 0;
      text-align: center;
    }
  }
}
::v-deep .config {
  &-parmas {
    margin-top: 8px;
    @extend %flex-box;

    &__type {
      margin-right: 16px;
      @extend %flex-box;
    }
    &__value {
      @extend %flex-box;
    }
    &__label {
      padding-right: 4px;
      font-weight: bold;
    }
  }
  &-select {
    width: 100%;
    .el-select {
      width: 100%;
    }
    &__total {
      font-weight: bolder;
      letter-spacing: 1px;
    }
  }
  &-operator {
    height: 32px;
  }
}
</style>
