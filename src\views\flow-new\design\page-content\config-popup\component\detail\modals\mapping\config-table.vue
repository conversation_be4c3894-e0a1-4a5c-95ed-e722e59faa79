<template>
  <bs-table
    ref="tableRef"
    border
    stripe
    show-index
    size="mini"
    row-key="id"
    :height="380"
    index-width="50px"
    paging-front
    :data="visibleTableData"
    :page-data="pageData"
    :crossing="true"
    :selection="true"
    :selectable="!disabled"
    :column-settings="false"
    :column-data="columnData"
    :checked-rows="checkedData"
    :show-multiple-selection="false"
    @page-change="handlePageChange"
    @selection-change="selectionChange"
  >
    <!-- 选择全部 -->
    <template slot="footer-expand">
      <el-checkbox :value="checkAll" :indeterminate="isIndeterminate" :disabled="disabled" @change="checkAll = $event">
        {{ $t('pa.flow.selectAll1') }}
        <span class="config-select__total">{{ $t('pa.flow.msg123', [checkedData.length]) }}</span>
      </el-checkbox>
    </template>
    <!-- 字段名称 -->
    <template slot="name" slot-scope="{ row }">
      <el-autocomplete
        v-model="row.name"
        size="small"
        :disabled="getDisabled(row)"
        :fetch-suggestions="handleNameSuggestions"
        @select="handleNameSelect($event, row)"
      />
    </template>
    <!-- 字段类型 -->
    <template slot="type" slot-scope="{ row }">
      <el-select v-model="row.type" size="small" :disabled="true">
        <el-option v-for="el in typeList" :key="el" :label="el" :value="el" />
      </el-select>
    </template>
    <!-- 处理规则 -->
    <template slot="method" slot-scope="{ row }">
      <!-- virtual-loading 有问题 -->
      <bs-select
        v-model="row.method"
        filterable
        clearable
        size="small"
        :placeholder="$t('pa.flow.placeholder21')"
        class="config-select"
        :options="methodOptions"
        :disabled="getDisabled(row)"
        @change="handleMethodChange($event, row)"
        @visible-change="handleVisibleChange($event, row)"
      />
    </template>
    <!-- 参数 -->
    <template slot="params" slot-scope="{ row }">
      <div v-if="vaildArray(row.params)">
        <div v-for="(el, index) in row.params" :key="index" class="config-parmas">
          <div class="config-parmas__type">
            <span class="config-parmas__label">{{ $t('pa.flow.paramType') }}：</span>
            <span> {{ el.type }}</span>
          </div>
          <div class="config-parmas__value">
            <span class="config-parmas__label">{{ $t('pa.flow.val') }}：</span>
            <el-autocomplete
              v-model="row.params[index].value"
              size="small"
              :disabled="getDisabled(row)"
              :fetch-suggestions="handleParamsSuggestions"
            />
          </div>
        </div>
      </div>
      <span v-else></span>
    </template>
    <!-- 操作 -->
    <template slot="operator" slot-scope="{ row }">
      <div class="consfig-operator">
        <template v-if="!row.disabled">
          <el-button type="text" @click="handleDetete(row)">
            {{ $t('pa.flow.del') }}
          </el-button>
          <el-button type="text" @click="handleAddNextRow(row)">
            {{ $t('pa.flow.addNextRow') }}
          </el-button>
        </template>
      </div>
    </template>
  </bs-table>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue, Watch } from 'vue-property-decorator';
import isEqual from 'lodash/isEqual';
import cloneDeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';
import BsTable from 'bs-ui-pro/lib/bs-table';
import { VALID_TYPE, vaildArray, getSuggestions, getOptions, uuid } from './utils';

@Component
export default class ConfigTable extends Vue {
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => ({}) }) pageData!: any;
  @Prop({ default: () => [] }) inputFields!: any[];
  @Prop({ default: () => [] }) shareMethods!: any[];
  @Prop({ default: () => [] }) privateMethods!: any[];
  @Prop({ default: '' }) keyword!: string;
  @PropSync('data', { default: () => [] }) tableData!: any[];
  @Ref('tableRef') readonly table!: BsTable;

  private columnData = [
    {
      label: this.$t('pa.flow.fieldName1'),
      value: 'name',
      minWidth: 130,
      showOverflowTooltip: false
    },
    {
      label: this.$t('pa.flow.fieldType'),
      value: 'type',
      minWidth: 130,
      showOverflowTooltip: false
    },
    {
      label: this.$t('pa.flow.rule'),
      value: 'method',
      minWidth: 230,
      showOverflowTooltip: false
    },
    {
      label: this.$t('pa.flow.params'),
      value: 'params',
      minWidth: this.isEn ? 480 : 400,
      showOverflowTooltip: false
    },
    {
      label: this.$t('pa.action.action'),
      value: 'operator',
      width: this.isEn ? 175 : 145,
      fixed: 'right',
      showOverflowTooltip: false
    }
  ];
  private typeList: string[] = VALID_TYPE;
  private methodOptions: any[] = []; // 处理规则下拉选项配置
  private vaildArray = vaildArray;
  private selectionChange = debounce(this.handleSelectionChange, 500);
  private currentMethodRowId: string | null = null;

  get checkedData() {
    return this.tableData.filter(({ outputable }) => outputable);
  }

  get assetFuncs() {
    return cloneDeep(this.$store.getters.assetFuncs);
  }
  get assetFields() {
    return (this.$store.getters.assetFields || []).reduce((pre: any[], next: any) => {
      next.value = next.fName;
      pre.push(next);
      return pre;
    }, []);
  }
  @Watch('tableData', { deep: true })
  handler() {
    this.$nextTick(() => {
      this.table.$refs?.bsTable?.doLayout();
    });
  }

  get checkAll() {
    return this.visibleTableData.length > 0 && this.visibleTableData.every((row) => row.outputable);
  }
  set checkAll(val: boolean) {
    this.visibleTableData.forEach((row) => {
      this.$set(row, 'outputable', val);
    });
  }
  get isIndeterminate() {
    const checkedCount = this.visibleTableData.filter((row) => row.outputable).length;
    return checkedCount > 0 && checkedCount < this.visibleTableData.length;
  }

  /* 获取disabled */
  getDisabled({ disabled }: any = {}) {
    return this.disabled || disabled;
  }
  /* 处理name输入建议 */
  handleNameSuggestions(queryStr, cb) {
    cb(getSuggestions(queryStr, this.assetFields));
  }
  /* 处理name建议选择事件 */
  handleNameSelect({ fName, fType }: any, row: any) {
    this.$set(row, 'name', fName);
    this.$set(row, 'type', fType);
  }
  /* 获取处理规则聚焦事件 */
  handleMethodFocus(row): any[] {
    const index = this.getIndex(row);
    if (typeof index !== 'number') return [];
    const validData = this.tableData.slice(0, index);
    return [
      {
        label: this.$t('pa.flow.field'),
        value: 'field',
        options: getOptions(validData, '#')
      },
      {
        label: this.$t('pa.flow.publicMethod'),
        value: 'shareMethods',
        options: getOptions(this.shareMethods)
      }
    ];
  }
  handleVisibleChange(visible, row) {
    if (visible) {
      this.currentMethodRowId = row.id;
      this.methodOptions = this.handleMethodFocus(row);
    } else if (this.currentMethodRowId === row.id) {
      this.methodOptions = [];
      this.currentMethodRowId = null;
    }
  }
  /* 处理处理规则选中事件 */
  handleMethodChange(method: string, row) {
    const isFieid = method.includes('#');
    const source = isFieid ? this.tableData : [...this.shareMethods, ...this.privateMethods];
    const target = source.find((el) => el.name === method.replaceAll('#', ''));
    if (target) {
      if (isFieid) {
        this.$set(row, 'type', target.type);
        this.$set(row, 'params', []);
        return;
      }
      this.typeList.includes(target.retType) && this.$set(row, 'type', target.retType);
      this.$set(row, 'funcType', target.funcType);
      this.$set(
        row,
        'params',
        target.paramTypes.map((type) => ({ type, value: '' }))
      );
      return;
    }
    this.$set(row, 'params', []);
  }

  /* 处理参数值建议 */
  handleParamsSuggestions(queryStr: string, cb: any) {
    const list = this.inputFields.map((el) => ({ value: `#${el.name}#` }));
    cb(getSuggestions(queryStr, list));
  }
  /* 处理删除事件 */
  async handleDetete(row: any) {
    await this.$confirm(this.$t('pa.flow.delMsg1'), this.$t('pa.flow.tip'));
    const index = this.tableData.findIndex((item) => item.id === row.id);
    if (index > -1) {
      this.tableData.splice(index, 1);
      this.$emit('detete', this.pageData.currentPage);
    }
  }
  /* 在指定行后添加新行 */
  handleAddNextRow(row: any) {
    const index = this.tableData.findIndex(({ id }) => id === row.id);
    if (index > -1) {
      const newRow = {
        id: uuid(),
        hide: false,
        outputable: false,
        name: '',
        type: 'String',
        method: '',
        params: [],
        disabled: false
      };
      this.tableData.splice(index + 1, 0, newRow);
    }
    this.pageData.total = this.tableData.filter((el) => !el.hide).length;
  }
  /* 获取下标 */
  getIndex(target: any) {
    const index = this.tableData.findIndex((el) => isEqual(el, target));
    return index > -1 ? index : null;
  }
  /* 处理页数变化 */
  handlePageChange(currentPage, pageSize) {
    this.$emit('page-change', ...[currentPage, pageSize]);
  }
  handleSelectionChange(data: any) {
    const idList = data.map(({ id }) => id);
    this.tableData.forEach((el) => this.$set(el, 'outputable', idList.includes(el.id)));
  }

  get visibleTableData() {
    const visible = this.tableData.filter((row) => !row.hide);
    return visible;
  }
}
</script>
<style lang="scss" scoped>
%flex-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep .bs-table {
  &-footer-slot {
    padding-left: 0;
  }
  &__index {
    .cell {
      padding: 0;
      text-align: center;
    }
  }
}
::v-deep .config {
  &-parmas {
    margin-top: 8px;
    @extend %flex-box;

    &__type {
      margin-right: 16px;
      @extend %flex-box;
    }
    &__value {
      @extend %flex-box;
    }
    &__label {
      padding-right: 4px;
      font-weight: bold;
    }
  }
  &-select {
    width: 100%;
    .el-select {
      width: 100%;
    }
    &__total {
      font-weight: bolder;
      letter-spacing: 1px;
    }
  }
  &-operator {
    height: 32px;
  }
}
</style>
