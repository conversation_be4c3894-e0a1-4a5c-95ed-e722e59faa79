<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">缓存查询</div>
    </div>
    <div class="tab-content">
      <el-row>
        <el-col :span="8">
          <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="200px">
            <el-form-item label="namespace:" prop="namespace">
              <el-input v-model="formData.namespace" />
            </el-form-item>
            <el-form-item label="set:" prop="set">
              <el-input v-model="formData.set" />
            </el-form-item>
            <el-form-item label="key:" prop="key">
              <el-input v-model="formData.key" />
            </el-form-item>
            <div style="text-align: right; margin-bottom: 10px">
              <el-button type="primary" :loading="loading" @click="submit('ruleForm')">
                查 询
              </el-button>
            </div>
          </el-form>
        </el-col>
        <el-col :span="16">
          <div style="padding: 10px 20px">
            <div>结果:</div>
            <div style="padding: 0px 30px">{{ result }}</div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject } from 'vue-property-decorator';
import { URL_RES_DETAIL_AEROSPIKE_GETCACHEDATA } from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {}
})
export default class GetCacheData extends PaBase {
  detailData: any = {};
  loading = false;
  result = '';
  formData: any = {};
  rules: any = {
    namespace: [{ required: true, message: '请输入namespace', trigger: 'blur' }],
    set: [{ required: true, message: '请输入set', trigger: 'blur' }],
    key: [{ required: true, message: '请输入key', trigger: 'blur' }]
  };
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  created() {
    this.detailData = this.comDetailRecord.val || {};
  }
  submit(formName: string) {
    this.result = '暂无数据';
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        this.loading = true;
        this.doGet(URL_RES_DETAIL_AEROSPIKE_GETCACHEDATA, {
          params: {
            id: this.detailData.id,
            key: this.formData.key,
            namespace: this.formData.namespace,
            set: this.formData.set
          }
        }).then((resp: any) => {
          this.parseResponse(resp, () => {
            this.result = resp.data || '暂无数据';
          });
          this.loading = false;
        });
      } else {
        this.$message.error('请检查输入内容');
        return false;
      }
    });
  }
}
</script>
<style scoped></style>
