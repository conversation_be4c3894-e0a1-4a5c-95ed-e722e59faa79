<template>
  <bs-dialog
    :title="`${title || ''} 条件配置`"
    width="1000px"
    append-to-body
    :visible.sync="display"
    class="config-dialog"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @confirm="handleConfirm"
  >
    <div v-loading="loading" element-loading-text="配置加载中" class="config__container">
      <!-- 逻辑关系 -->
      <logic-bar
        ref="bar"
        :disabled="disabled"
        :data.sync="formData"
        @change="generateExpression"
        @increase="generateConfigTemplate"
      />

      <!-- 结果表达式 -->
      <result-expression :expression="formData.resultExpression" />
      <!-- 配置明细 -->
      <logic-main
        ref="config"
        :catch.sync="store"
        :disabled="disabled"
        :field-list="fieldList"
        :data.sync="formData.conditions"
        @change="handleConfigChange"
        @copy="handleCopyConfig"
      />
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Watch, Vue } from 'vue-property-decorator';
import { get } from '@/apis/utils/net';
import { cloneDeep, isEmpty } from 'lodash';
import { URL_GET_FUNC_LIST } from '@/apis/commonApi';

import {
  handleFuncArgs,
  handleTableData,
  handleTableHead,
  isObject,
  isArray,
  isValidType,
  generateName,
  generateDefaultData
} from './utils';
import LogicBar from './logic-bar.vue';
import ResultExpression from './result-expression.vue';
import LogicMain from './config-detail/index.vue';

@Component({ components: { LogicBar, ResultExpression, LogicMain } })
export default class LoConfig extends Vue {
  @Prop({ type: String, default: '' }) title!: string;
  @Prop({ type: String, default: '' }) orgId!: string;
  @Prop({ type: Array, default: () => [] }) fieldList!: any[];
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: [String, Number], default: null }) unid!: string | number;
  @Prop({ type: Object, default: () => generateDefaultData() }) data!: any;
  @PropSync('catch', { type: Object, default: () => ({}) }) store!: any;
  @PropSync('show', { type: Boolean, default: false }) display!: boolean;

  @Ref('bar') readonly logicBar!: LogicBar;
  @Ref('config') readonly logicConfig!: LogicMain;

  private loading = false;
  private formData: any = {};
  private rawData: any = null; // 原始数据

  get methodList() {
    return this.store.methodList || [];
  }
  get funcMapping() {
    return this.store.funcMapping || {};
  }
  get sourceCode() {
    return this.store.sourceCode || {};
  }
  get expressionMapping() {
    return this.store.expressionMapping || [];
  }

  @Watch('formData.conditions', { immediate: true, deep: true })
  generateExpression(val: any[]) {
    /* 逻辑表达式 */
    if (!val) return;
    this.handleLogicalRelation();
    this.handleResultExpression();
  }

  async created() {
    try {
      this.loading = true;
      /* 1，数据赋值，与原数据脱离联系 */
      this.formData = cloneDeep(generateDefaultData(this.data));
      /* 2，前置数据检查 */
      if (this.methodList.length < 1) {
        await this.getMethodList();
      }
      const result = await this.parseConditions(isArray(this.formData.conditions));
      this.$set(this.formData, 'conditions', result);
      this.loading = false;
    } catch (e) {
      this.loading = false;
      console.log(e);
    }
  }

  async getMethodList() {
    try {
      const { success, data, error } = await get(URL_GET_FUNC_LIST, {
        orgId: this.orgId,
        name: this.$store.state.userInfo.username
      });
      if (success) {
        const [mapping, ...list]: any[] = this.handleMethodList(isArray(data), {
          SHARE: [1, '共享方法'],
          DEFAULT: [2, '默认方法']
        });
        this.$set(this.store, 'funcMapping', mapping);
        this.$set(this.store, 'methodList', list);
        return;
      }
      this.$message.error(error);
    } catch (e) {
      console.log(e);
    }
  }

  handleMethodList(list: any[], mapping: any = {}) {
    return list
      .reduce((pre: any, { funcType, funcName, funcArgs, paramsType }) => {
        if (funcType in mapping) {
          const [index, label] = mapping[funcType];
          if (!pre[index]) pre[index] = { label, children: [] };
          pre[index].children.push({
            label: funcName,
            value: funcName,
            type: funcType
          });
        }
        if (!pre[0]) pre[0] = {};
        pre[0][funcName] = {
          funcType,
          funcName,
          funcArgs,
          paramsType,
          isOneParams: funcArgs.length < 1
        };
        return pre;
      }, [])
      .filter(Boolean);
  }

  /* 处理逻辑表达式 */
  handleLogicalRelation() {
    if (this.formData.logicType === 'CUSTOM') return;
    const result = this.formData.conditions
      .map(({ name, funcName }) => (funcName ? name : null))
      .filter(Boolean)
      .join(` ${this.formData.logicType} `);
    this.$set(this.formData, 'expr', result);
  }
  /* 处理结果表达式 */
  handleResultExpression() {
    let str = this.formData.expr.replace(/([A-Z])/g, '@@_$1_@@');
    /* 模块对应表达式 */
    const expressionMapping = this.formData.conditions.reduce((pre, next) => {
      const { name, funcName, funcType, funcArgs } = next;
      const params = funcArgs
        .map(({ key, value }) => {
          if (funcType === 'DEFAULT' && !isObject(this.funcMapping[funcName]).isOneParams) {
            return `${key},${value}`;
          }
          return key;
        })
        .filter(Boolean);
      if (funcName) {
        pre[name] = `${funcName}(<span class="code-green">${params.join(',')}</span>)`;
        str = str.replace(new RegExp(`@@_${name}_@@`, 'g'), pre[name]);
      }
      return pre;
    }, {});
    this.$set(this.store, 'expressionMapping', expressionMapping);
    /* 结果表达式 */
    const result = str
      .replace(/(\&\&|\|\|)/g, '<span class="code-red"> $1 </span>')
      .replace(/[\@\_]/g, '');
    this.$set(this.formData, 'resultExpression', result);
  }

  /* 解析All条件 */
  parseConditions(list: any[] = []) {
    if (list.length < 1) {
      return [this.generateConfigTemplate(false)];
    }
    return list.map((el) => this.parseCondition(el));
  }
  /* 解析条件 */
  parseCondition(data: any, isChange = false) {
    const { funcId, funcType, funcArgs, paramsType } = isObject(this.funcMapping[data.funcName]);
    const result: any = {
      expand: '',
      name: data.name,
      funcName: data.funcName,
      funcId,
      funcType: isValidType(funcType) ? funcType : data.funcType
    };
    result.funcArgs = handleFuncArgs(funcType, funcArgs, data.funcArgs, isChange);
    result.showConfig = result.expand || result.funcArgs.length > 0;
    result.tableHead = handleTableHead(funcType, data.funcType, funcArgs, result.funcArgs);
    result.tableData = handleTableData(
      isValidType(funcType) ? funcType : data.funcType,
      funcArgs,
      result.funcArgs,
      paramsType
    );
    return result;
  }

  generateConfigTemplate(needPush = true, config: any = {}) {
    const { length } = this.formData.conditions;
    if (length > 25) {
      this.$message.warning('条件数量已经达到上限');
      return;
    }
    const name = generateName(length + 1);
    const base = isEmpty(config)
      ? {
          expand: '',
          showConfig: true,
          funcName: '',
          funcType: '',
          funcArgs: [],
          tableHead: [],
          tableData: []
        }
      : config;
    return needPush ? this.formData.conditions.push({ ...base, name }) : { ...base, name };
  }

  handleConfigChange(index) {
    this.$set(
      this.formData.conditions,
      index,
      this.parseCondition(this.formData.conditions[index], true)
    );
  }

  handleCopyConfig(index: number) {
    this.generateConfigTemplate(true, cloneDeep(this.formData.conditions[index]));
  }

  async handleConfirm() {
    try {
      const legalData = this.formData.conditions.filter((el) => {
        return el.funcArgs && el.funcName && el.funcType && el.name;
      });
      if (legalData.length < 1) {
        this.$message.error('请至少配置一个条件');
        return;
      }
      await this.logicBar.validate();
      await this.logicConfig.validate();
      this.closeDialog(false);
    } catch (e) {
      console.log(e);
    }
  }
  closeDialog(resetData = true) {
    if (!resetData) {
      const data = cloneDeep(this.formData);
      data.conditions = data.conditions.map((el: any) => ({
        funcArgs: el.funcArgs,
        funcName: el.funcName,
        funcId: el.funcId,
        funcType: el.funcType,
        name: el.name
      }));
      this.$emit('confirm', ...[data, this.unid]);
    }
    this.display = false;
  }
}
</script>
<style lang="scss" scoped>
$marginTop: 15px;
$padding: 20px;
$tabHeight: 48px;
$borderColor: #f1f1f1;

.config {
  &-dialog {
    ::v-deep .el-dialog {
      &__body {
        padding: 0;
      }
    }
  }

  &__container {
    margin-top: 20px;
  }
  &-resultExpression {
    display: flex;
    align-items: center;
    margin: 0 auto 20px;
    width: calc(100% - 70px);
    font-size: 14px;
    font-weight: 400;
    color: #444444;
    &__main {
      width: calc(100% - 90px);
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  &-content {
    margin-top: $marginTop;
    height: 400px;
    overflow-x: hidden;
    overflow-y: auto;
  }
}
</style>
