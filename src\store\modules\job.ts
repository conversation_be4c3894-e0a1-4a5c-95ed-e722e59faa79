import { webSocket } from 'rxjs/webSocket';
import {
  RELOAD_JOB,
  UPDATE_COMPONENT_LIST,
  SET_FLOW_SEARCH_OBJ,
  ADD_LOAING_PROJECT_IDS,
  DEL_LOAING_PROJECT_IDS,
  SET_SESSION_WS_MAP,
  DEL_SESSION_WS_MAP
} from '../event-name';
import { getSocketUrl } from '@/views/flow-new/design/utils';

const jobModule = {
  componentList: [],
  reloadJob: false,
  flowSearchObj: null,
  loadingProjectIds: [],
  sessionWsMap: new Map()
};
export default {
  state: () => jobModule,
  mutations: {
    [UPDATE_COMPONENT_LIST](state: IState, payload: any) {
      state.componentList = payload;
    },

    [RELOAD_JOB](state: IState, payload: any) {
      state.reloadJob = payload;
    },
    [SET_FLOW_SEARCH_OBJ](state: any, payload: any) {
      state.flowSearchObj = payload;
    },
    [ADD_LOAING_PROJECT_IDS](state: any, payload: any) {
      state.loadingProjectIds.push(payload);
    },
    [DEL_LOAING_PROJECT_IDS](state: any, payload: any) {
      const index = state.loadingProjectIds.findIndex((el) => el === payload);
      state.loadingProjectIds.splice(index, 1);
    },
    [SET_SESSION_WS_MAP](state: any, payload: any) {
      const ws = webSocket(`${getSocketUrl()}/sqlTest?websocketKey=${payload}`);
      state.sessionWsMap.set(payload, ws);
    },
    [DEL_SESSION_WS_MAP](state: any, payload: any) {
      state.sessionWsMap.delete(payload);
    }
  },
  getters: {
    componentList: ({ componentList }) => componentList,
    componentListMap: ({ componentList }) => {
      return (componentList || []).reduce((res, item) => {
        item.paJobComponentList.forEach((item) => {
          res.set(item.className, JSON.parse(item.properties || '{}') || {});
        });
        return res;
      }, new Map());
    },
    componentIconMap: ({ componentList }) => {
      return (componentList || []).reduce((res, item) => {
        item.paJobComponentList.forEach((item) => {
          res[item.className] = item.iconBase64;
        });
        return res;
      }, {});
    },
    flowSearchObj: ({ flowSearchObj }) => flowSearchObj,
    loadingProjectIds: ({ loadingProjectIds }) => (Array.isArray(loadingProjectIds) ? loadingProjectIds : [])
  }
};
