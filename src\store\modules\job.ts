import {
  RELOAD_JOB,
  UPDATE_COMPONENT_LIST,
  UPDATE_JOB_DATA,
  UPDATE_JOB_LIST,
  UPDATE_ORIGIN_JOB_CONTENT,
  UPDATE_ERROR_NODES,
  SET_FLOW_SEARCH_OBJ
} from '@/store/event-names/mutations';

const data: IJobData = {
  clusterId: '',
  content: {
    nodes: []
  },
  createTime: Date.now(),
  createdBy: '',
  dataLevelType: '',
  id: '',
  jobName: '',
  jobStatus: '',
  jobType: '',
  jobVersion: '',
  memo: '',
  orgId: '',
  orgPath: '',
  projectId: '',
  projectName: '',
  updateTime: Date.now(),
  updatedBy: ''
};
const originJobContent: string = '';
const componentList: any = [];
const reloadJob: boolean = false;
const updateJobList: boolean = false;
const errorNodes: any[] = [];
const flowSearchObj: any = null; // 保存流程设计列表（画布左侧）搜索信息状态
export default {
  state: () => ({
    data,
    originJobContent,
    componentList,
    reloadJob,
    updateJobList,
    errorNodes,
    flowSearchObj
  }),
  mutations: {
    [UPDATE_JOB_DATA](state: any, payload: any) {
      state.data = payload;
    },
    [UPDATE_ORIGIN_JOB_CONTENT](state: IState, payload: any) {
      state.originJobContent = payload;
    },

    [UPDATE_COMPONENT_LIST](state: IState, payload: any) {
      state.componentList = payload;
    },

    [RELOAD_JOB](state: IState, payload: any) {
      state.reloadJob = payload;
    },
    [UPDATE_JOB_LIST](state: any, payload: any) {
      state.updateJobList = payload;
    },
    [UPDATE_ERROR_NODES](state: any, payload: any) {
      state.errorNodes = payload;
    },
    [SET_FLOW_SEARCH_OBJ](state: any, payload: any) {
      state.flowSearchObj = payload;
    }
  }
};
