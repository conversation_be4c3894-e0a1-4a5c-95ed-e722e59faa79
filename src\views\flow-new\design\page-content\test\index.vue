<template>
  <div v-loading="loading" class="test__container">
    <!-- header -->
    <div class="tab-title">
      <div class="title-text">{{ $t('pa.flow.flowTest') }}</div>
      <span class="iconfont icon-close-small test-icon" @click="handleClose"></span>
    </div>
    <!-- 内容 -->
    <div class="test-main">
      <test-case
        :flow-data="data"
        :loading.sync="loading"
        :disabled="!logContent"
        :sql-table="sqlTable"
        @delete="handleDelete"
        @execute="handelExecute"
        @view-log="showLogcontent = true"
        @set-code-error="(error) => $emit('set-code-error', error)"
      />
      <test-result :data="resultData" :is-flink-sql="isFlinkSql" @view="resultView" />
    </div>
    <test-result-dialog
      v-if="showResutDialog"
      :visible.sync="showResutDialog"
      :is-sql="isFlinkSql"
      :output="output"
      :input="input"
      :no-input="noInput"
    />
    <!-- 测试日志弹窗 -->
    <test-log-dialog :visible.sync="showLogcontent" :case-name="currentCase.testDataName" :log-content="logContent" />
  </div>
</template>

<script lang="ts">
import { Component, PropSync, Prop, Vue } from 'vue-property-decorator';
import { isEqual, difference } from 'lodash';
import SockJS from 'sockjs-client';
import { get } from '@/apis/utils/net';
import { URL_TEST_DATA_RUN, URL_TEST_DATA_RUN_SQL } from '@/apis/commonApi';
import getWebConsole from '@/utils/get-web-console';
import { MsgType } from '../../interface';

@Component({
  components: {
    TestCase: () => import('./test-case.vue'),
    TestResult: () => import('./test-result.vue'),
    TestLogDialog: () => import('./components/test-log-dialog.vue'),
    TestResultDialog: () => import('./components/test-result-dialog.vue')
  }
})
export default class TestContent extends Vue {
  @PropSync('show', { default: false }) display!: boolean;
  @Prop({ default: () => ({}) }) data!: any;
  @Prop({ default: false }) isChange!: boolean;
  @Prop({ default: false }) sqlTable!: any;

  private loading = false;
  private resultData: any = {
    columnData: [
      { value: 'testResult', label: this.$t('pa.flow.result') },
      { width: 200, show: true, label: this.$t('pa.action.action'), value: 'operator', showOverflowTooltip: false }
    ],
    tableData: []
  };
  private socket: any = null;
  private output: any = null;
  private input: any = null;
  private noInput = false;
  private logContent = '';
  private showResutDialog = false;
  private currentCase: any = {};

  private showLogcontent = false;
  get isFlinkSql() {
    return this.data?.jobType === 'FLINK_SQL';
  }
  destory() {
    this.disconnect();
  }

  handleDelete(row: any) {
    if (!isEqual(this.currentCase, row)) return;
    this.$set(this.resultData, 'tableData', []);
  }
  async handelExecute(row: any, config: any) {
    this.logContent = ''; //清空上一次点击的测试日志
    this.noInput = false;
    this.currentCase = row;
    const userId = `key${Date.now()}`;
    this.initWebSocket(userId);
    if (this.isChange) return this.$tip.warning(this.$t('pa.flow.msg4'));
    const loading = this.$loading({ lock: true, text: this.$t('pa.flow.msg244') });
    try {
      const { success, data, error, msgType } = await get(`${this.getUrl()}?websocketKey=${userId}`, {
        jobId: row.jobId,
        isSink: config.isSink,
        dataId: row.id,
        userName: this.$store.getters.userName
      });
      if (success) {
        const tableData: any[] = [];
        if (this.isFlinkSql) {
          this.output = data.filter((el) => !el.isSourceNode);
          this.input = data.filter((el) => el.isSourceNode);
          tableData.push({ name: this.$t('pa.flow.msg245', [row.testDataName]) });
        } else {
          this.output = data;
          // todo 优化
          const dataResult = JSON.parse(data || '{}');
          for (const key of Object.keys(dataResult)) {
            dataResult[key].forEach((item) => {
              let flag = false;
              tableData.forEach((row) => {
                const diff = difference(row.graphPath, item.graphPath);
                if (diff.length === 0) {
                  row.result += item.result;
                  flag = true;
                  return true;
                }
              });
              if (!flag) {
                tableData.push({
                  testResult: key,
                  data: item.result,
                  graphPath: item.graphPath
                });
              }
            });
          }
        }
        this.$set(this.resultData.columnData[0], 'value', this.isFlinkSql ? 'name' : 'testResult');
        this.$set(this.resultData, 'tableData', tableData);
        return loading.close();
      } else {
        if (msgType === MsgType.LINE_MESSAGE) {
          this.$emit('set-code-error', error);
        } else {
          this.$tip.error(error);
        }
      }
      loading.close();
    } catch {
      loading.close();
    }
  }
  getUrl() {
    return this.isFlinkSql ? URL_TEST_DATA_RUN_SQL : URL_TEST_DATA_RUN;
  }
  initWebSocket(userId: string) {
    if (this.socket) this.disconnect();
    this.connection(userId);
  }

  connection(userId: string) {
    this.socket = new SockJS(getWebConsole(userId));
    this.socket.onmessage = this.onMessage;
  }
  onMessage({ data }) {
    this.logContent += JSON.parse(data).content;
  }

  disconnect() {
    typeof this.socket.close === 'function' && this.socket.close();
    this.socket = null;
  }

  handleViewLog() {}
  resultView(row: any) {
    if (!this.isFlinkSql) this.output = row;
    if (!this.isFlinkSql || (this.isFlinkSql && this.input.length < 1)) {
      this.noInput = true;
      this.input = this.currentCase;
    }
    this.showResutDialog = true;
  }
  handleClose() {
    this.display = false;
    this.$parent.$emit('highlight', []);
  }
}
</script>

<style lang="scss" scoped>
.test {
  &__container {
    height: 340px;
    background: #fff;
    overflow: hidden;
    border-top: 1px solid #f1f1f1;
  }
  &-icon {
    padding: 5px 8px;
    cursor: pointer;
  }
  &-main {
    display: flex;
    justify-content: space-between;
    padding: 0 16px;
    height: calc(100% - 50px);
    > div {
      width: calc(50% - 8px);
      height: 100%;
    }
  }
}
</style>
