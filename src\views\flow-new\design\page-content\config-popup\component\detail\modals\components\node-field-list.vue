<template>
  <div>
    <div v-if="isAdd">{{ $t('pa.flow.outputFields') }}</div>
    <el-divider v-if="!isAdd && !showAddRowBtn" content-position="left">{{ $t('pa.flow.fieldConfig') }}</el-divider>
    <div class="form-content">
      <span v-if="!isDqMonitor" class="tip">{{ type === 'map' ? $t('pa.flow.msg98') : '' }}</span>
      <el-input
        v-if="!isDqMonitor"
        v-model="search"
        class="search-field-btn"
        :placeholder="$t('pa.flow.placeholder20')"
        maxlength="30"
        @input="fetchList"
      />
      <el-button v-if="!disabled" v-show="showAddRowBtn" class="btn-content" @click="addRow">
        {{ $t('pa.flow.createRow') }}
      </el-button>
      <el-tooltip v-if="isDqMonitor" class="form-content__tooltip" effect="light" placement="top-start">
        <i class="iconfont icon-wenhao"></i>
        <pre slot="content">
          {{ $t('pa.tip.dataQualityMonitoring') }}
        </pre>
      </el-tooltip>
    </div>
    <el-table
      :key="key"
      class="node-table"
      :data="type === 'map' ? tableShowData : searchList"
      style="width: 100%"
      stripe
      size="mini"
      border
      :height="tableHeight"
    >
      <el-table-column type="index" :index="indexMethod" width="50" />
      <el-table-column :label="enable" width="70" header-align="center" align="center">
        <template slot="header">
          <span v-if="!!disabled">{{ enable }}</span>
          <el-dropdown v-else :key="Date.now()" placement="left" trigger="click" @command="checkedChange">
            <span class="el-dropdown-link"> {{ enable }}<i class="el-icon-arrow-down el-icon--right"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="allSelect">{{ $t('pa.flow.selectAll2') }}</el-dropdown-item>
              <el-dropdown-item command="allCancel">{{ $t('pa.flow.cancelAll') }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <template slot-scope="{ row, $index }">
          <el-checkbox v-model="row.outputable" :disabled="disabled" @change="handelCheck(row, $index)" />
        </template>
      </el-table-column>
      <!-- 非数据质量监控组件（映射组件） -->
      <template v-if="!isDqMonitor">
        <el-table-column prop="name" :label="$t('pa.flow.fieldName')" min-width="130">
          <template slot-scope="{ row, $index }">
            <el-autocomplete
              v-model="row.name"
              style="width: 100%; height: 29px"
              :disabled="disabled ? disabled : setDisabled(row)"
              :fetch-suggestions="queryFieldHandle"
              @select="selectFieldHandle($event, row, $index)"
              @input="fieldHandle(row, $index)"
              @blur="handlefieldBlur($index)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="type" :label="$t('pa.flow.fieldType')" min-width="130">
          <template slot-scope="{ row, $index }">
            <el-select
              v-model="row.type"
              style="width: 100%"
              :placeholder="$t('pa.flow.placeholder21')"
              :disabled="type === 'map' ? true : disabled ? disabled : setDisabled(row)"
              @change="handelType(row, $index)"
            >
              <el-option v-for="item in typeEnums" :key="item" :label="item" :value="item" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column v-if="showMethod" prop="type" :label="$t('pa.flow.rule')" min-width="250">
          <template slot-scope="{ row, $index }">
            <el-select
              v-model="row.method"
              v-loadmore="() => ruleLoadMore(row)"
              style="width: 100%"
              :placeholder="$t('pa.flow.placeholder21')"
              filterable
              clearable
              remote
              :loading="ruleloading"
              :disabled="disabled ? disabled : setDisabled(row)"
              :remote-method="(query) => ruleRemoteMethod(query, row)"
              @change="funcSelectHandle($event, row, $index)"
              @blur="methodBlurHandle($event, row, $index)"
              @clear="clearMethod"
              @visible-change="methodVisibleChangeHandle($event, row, $index)"
            >
              <el-option-group v-for="group in fieldsAndMethods" :key="group.label" :label="group.label">
                <el-option v-for="item in group.options" :key="item.name" :label="item.name" :value="item.name" />
              </el-option-group>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column v-if="showMethod" prop="type" :label="$t('pa.flow.params')" min-width="340">
          <template slot-scope="{ row, $index }">
            <div v-for="(param, index) in row.params" :key="index" style="marginbottom: 4px; display: flex">
              <div style="line-height: 30px">
                <span style="font-weight: bold">{{ $t('pa.flow.paramType') }}：</span>
                {{ param.type }}
              </div>
              <div style="flex: 1; text-align: right">
                <el-autocomplete
                  v-model="param.value"
                  style="height: 29px; float: right"
                  :disabled="disabled ? disabled : setDisabled(row)"
                  :fetch-suggestions="queryParamValueHandle"
                  @select="selectParamsHandle(row, $index, index)"
                  @input="paramsHandle(row, $index, index)"
                />
                <span style="font-weight: bold; float: right; line-height: 30px; padding-right: 10px">
                  {{ $t('pa.flow.val') }}:
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
      </template>
      <!-- 数据质量监控组件 -->
      <template v-else>
        <!-- 监控规则名称 -->
        <el-table-column :label="$t('pa.flow.monitorRule')" prop="name">
          <template slot-scope="{ row, $index }">
            <el-input
              v-model="row.name"
              maxlength="30"
              :disabled="disabled ? disabled : setDisabled(row)"
              @input="setMonitorRuleName(row, $index)"
            />
          </template>
        </el-table-column>
        <el-table-column v-if="showMethod" :label="$t('pa.flow.ruleStr')" min-width="250" prop="mvel">
          <!-- 规则表达式 -->
          <template slot-scope="{ row, $index }">
            <auto-complete
              ref="mvel"
              v-model="row.mvel"
              class="mvel-input"
              :disabled="disabled ? disabled : setDisabled(row)"
              :placeholder="$t('pa.flow.placeholder22')"
              :fetch-suggestions="querySearch"
              :trigger-on-focus="false"
              @input="setMonitorExpression(row, $index)"
            />
          </template>
        </el-table-column>
      </template>
      <el-table-column
        v-if="showOper"
        class="tableClass"
        fixed="right"
        :label="$t('pa.flow.operator')"
        header-align="center"
        align="center"
      >
        <template slot-scope="{ $index, row }">
          <el-button v-if="showTestButton(row)" type="text" @click="test(row, $index)"> {{ $t('pa.flow.test') }} </el-button>
          <el-button v-if="showDeleteButton(row)" type="text" @click="deleteHandle($index, row)">
            {{ $t('pa.flow.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="showAddRowBtn"
      class="dialog-page-content"
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="pageSize"
      :current-page="currentPage"
      @current-change="pageChange"
    />
  </div>
</template>
<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { URL_COMPONENT_FUNC_LIST, URL_ASSETS_PROMPT_LIST } from '@/apis/commonApi';
import * as _ from 'lodash';
import store from '@/store';
import { SET_ASSET_FUNCS } from '@/store/event-name';
import AutoComplete from './autocomplete/src/autocomplete.vue';
import { get } from '@/apis/utils/net';
@Component({
  components: {
    AutoComplete
  }
})
export default class NodeFieldList extends PaBase {
  @Prop() jobDataCopy!: any;
  @Prop() data!: any;
  @Prop() type!: string;
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: false }) isAdd!: boolean;
  @Prop({ default: '' }) config!: string;
  showMethod = false;
  jobData: any;
  showOper = false;
  showAddRowBtn = false;
  currentPage = 1;
  pageSize = 10;
  total = 1;
  search = '';
  tableData: any = [];
  searchList: any = [];
  tableShowData: any = [];
  assetFields: any[] = [];
  assetFuncs: any = []; // 方法列表：接口返回的元数据
  selectAssetFuncs: any = []; // 用于下拉选择的共享方法列表
  selectPrivateFuncs: any = []; // 用于下拉选择的独享方法列表
  fieldsAndMethods: any = [];
  input: any = [];
  code: any = [];
  typeEnums: string[] = ['String', 'Boolean', 'Date', 'Double', 'Long', 'Float', 'Integer'];
  jobNode: any;
  private tableHeight = 200;
  private isMap = false;
  // 上游节点输入的字段
  inputFieleOptions: any = [];
  currentRow: any;
  funStartIndex = 0;
  ruleloading = false;
  funcSelectQuery: any = '';
  timer: any;
  key = Date.now();
  selectPrivateFuncsCopy: any[] = [];
  get enable() {
    return this.config === 'dqMonitor' ? this.$t('pa.flow.use') : this.$t('pa.flow.output');
  }

  // 判断当前组件是否为数据质量监控组件
  get isDqMonitor() {
    return this.config === 'dqMonitor' ? true : false;
  }
  // 数据质量监控组件返回自定义代码提示
  get getAssetsFunc() {
    return this.config === 'dqMonitor' ? URL_ASSETS_PROMPT_LIST : URL_COMPONENT_FUNC_LIST;
  }

  excludeCurrRowField(row) {
    const field = this.fieldsAndMethods[0];
    const index = _.findIndex(field.options, { name: '#' + row.name + '#' });
    if (index === 0) {
      this.fieldsAndMethods.splice(0, 1);
    } else if (index > 0) {
      const len = field.options.length;
      field.options = _.dropRight(field.options, len - index);
    }
  }
  mounted() {
    if (this.type === 'map') {
      this.timer = setTimeout(() => {
        this.loadData(this.jobDataCopy, this.data, true, true, true);
        const map: any = document.getElementById('map-dialog');
        this.tableHeight = map.childNodes[0].offsetHeight - 175;
      }, 0);
    }
  }
  destroyed() {
    clearTimeout(this.timer);
  }
  indexMethod(val) {
    return (this.currentPage - 1) * this.pageSize + val + 1;
  }

  pageChange(val) {
    this.currentPage = val;
    this.tableShowData = this.searchList.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize);
  }

  fetchList = _.debounce(this.searchTable, 1000);

  setMonitorRuleName(row, index) {
    this.tableData[index].name = row.name;
  }

  setMonitorExpression(row, index) {
    this.tableData[index].mvel = row.mvel;
  }

  queryFilters(queryString) {
    return (code) => {
      return code.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
    };
  }

  showTestButton(row) {
    return this.isDqMonitor && !(this.disabled ? this.disabled : this.setDisabled(row));
  }

  showDeleteButton(row) {
    return (
      ((this.disabled ? false : !row.disabled) || this.isDqMonitor) &&
      !(this.disabled ? this.disabled : this.setDisabled(row))
    );
  }

  querySearch(queryString, cb) {
    let value = '';
    const word = queryString.charAt(queryString.length - 1);
    value = word === '&' ? '#' : word;
    // 提示词
    const code = _.cloneDeep(this.code);
    const results = queryString ? code.filter(this.queryFilters(value)) : code;
    cb(results);
  }

  searchTable(isSearch = true, isAdd = false) {
    if (!isSearch) {
      this.search = '';
      this.searchList = _.cloneDeep(this.tableData);
      if (isAdd) {
        this.currentPage = Math.ceil(this.searchList.length / this.pageSize);
      } else {
        if (this.tableShowData.length === 0) {
          this.currentPage = Math.ceil(this.searchList.length / this.pageSize);
        }
      }
    } else {
      this.searchList = this.tableData.filter((item) => item.name.includes(this.search));
      // 触发搜索后 页码重置为1
      this.currentPage = 1;
    }
    this.total = this.searchList.length;
    this.tableShowData = this.searchList.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize);
  }
  deleteHandle(index, row) {
    let realIndex = null;
    realIndex = row.realIndex ? row.realIndex : (this.currentPage - 1) * this.pageSize + index;
    this.tableData.splice(realIndex, 1);
    this.tableShowData.splice(index, 1);
    // 每次删除重新设置realindex
    this.tableData.forEach((item, i) => {
      item.realIndex = i;
    });
    this.searchTable(false);
  }
  methodBlurHandle(event, row, index) {
    if (_.toString(row.method) === '') {
      this.$set(row, 'params', []);
      const realIndex = (this.currentPage - 1) * this.pageSize + index;
      this.$set(this.tableData[realIndex], 'params', []);
    }
  }
  wrapField(name: string) {
    return '#' + name + '#';
  }
  funcSelectHandle(val, row, index) {
    const realIndex = (this.currentPage - 1) * this.pageSize + index;
    if (val.indexOf('#') >= 0) {
      const rec: any = _.find(this.field2Option(''), {
        name: val
      });
      if (rec !== undefined) {
        this.$set(row, 'type', rec.type);
        this.$set(this.tableData[realIndex], 'type', rec.type);
        this.$set(row, 'params', []);
        this.$set(this.tableData[realIndex], 'params', []);
        this.$set(this.tableData[realIndex], 'method', val);
      }
    } else {
      if (!val) return;
      const func: any = _.find(this.assetFuncs, { name: val }) || _.find(this.selectPrivateFuncsCopy, { name: val });
      const list: any = [];
      if (func !== undefined) {
        if (Array.isArray(func.paramTypes)) {
          func.paramTypes.forEach((n) => {
            list.push({
              type: n,
              value: ''
            });
          });
        }
      }
      this.$set(row, 'params', list);
      this.$set(this.tableData[realIndex], 'params', list);
      if (_.indexOf(this.typeEnums, func.retType) >= 0) {
        this.$set(row, 'type', func.retType);
        this.$set(this.tableData[realIndex], 'type', func.retType);
      }
      this.$set(this.tableData[realIndex], 'method', val);
      this.key = Date.now();
    }
  }
  queryFieldHandle(queryString, cb) {
    const list: any = [];
    this.assetFields.forEach((n) => {
      n.value = n.fName;
      list.push(n);
    });
    const results = queryString ? list.filter(this.createFilter(queryString)) : list;
    cb(results);
  }
  handelCheck(row, index) {
    const realIndex = row.realIndex ? row.realIndex : (this.currentPage - 1) * this.pageSize + index;
    this.$set(this.tableData[realIndex], 'outputable', row.outputable);
  }
  handelType(row, index) {
    const realIndex = row.realIndex ? row.realIndex : (this.currentPage - 1) * this.pageSize + index;
    this.$set(this.tableData[realIndex], 'type', row.type);
  }
  selectFieldHandle(val, row, index) {
    const realIndex = row.realIndex ? row.realIndex : (this.currentPage - 1) * this.pageSize + index;
    this.$set(this.tableData[realIndex], 'name', val.fName);
    this.$set(this.tableData[realIndex], 'type', val.fType);
  }

  fieldHandle(row, index) {
    const realIndex = row.realIndex ? row.realIndex : (this.currentPage - 1) * this.pageSize + index;
    this.$set(this.tableData[realIndex], 'name', row.name.trim());
    this.$set(this.tableData[realIndex], 'type', row.type);
  }

  selectParamsHandle(row, index) {
    const realIndex = row.realIndex ? row.realIndex : (this.currentPage - 1) * this.pageSize + index;
    this.$set(this.tableData[realIndex], 'params', row.params);
  }

  paramsHandle(row, index) {
    const realIndex = row.realIndex ? row.realIndex : (this.currentPage - 1) * this.pageSize + index;
    this.$set(this.tableData[realIndex], 'params', row.params);
  }

  queryParamValueHandle(queryString, cb) {
    const list: any = [];
    this.tableData.forEach((n) => {
      if (n.disabled) {
        list.push({
          value: this.wrapField(n.name)
        });
      }
    });
    const results = queryString ? list.filter(this.createFilter(queryString)) : list;
    cb(results);
  }
  createFilter(queryString) {
    return (obj) => {
      return _.toLower(obj.value).indexOf(_.toLower(queryString)) >= 0;
    };
  }
  addRow() {
    this.tableData.push(
      this.isDqMonitor
        ? { name: '', used: false, mvel: '', outputable: false }
        : { name: '', type: 'String', disabled: false, outputable: false }
    );
    this.searchTable(false, true);
  }
  showUp(index) {
    return !(index - this.input.length === 0);
  }
  showDown(index) {
    return !(index + 1 === this.tableData.length);
  }
  up(index, row) {
    const tmp = _.cloneDeep(this.tableData[index - 1]);
    this.$set(this.tableData, index, tmp);
    this.$set(this.tableData, index - 1, row);
  }
  down(index, row) {
    const tmp = _.cloneDeep(this.tableData[index + 1]);
    this.$set(this.tableData, index, tmp);
    this.$set(this.tableData, index + 1, row);
  }
  setDisabled(row) {
    return row.disabled;
  }
  checkedChange(val) {
    if (val === 'allSelect') {
      if (this.type !== 'map') {
        this.tableData.forEach((n) => {
          n.outputable = true;
        });
      } else {
        this.tableShowData.forEach((n) => {
          n.outputable = true;
        });
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = this.currentPage * this.pageSize - 1;
        this.tableData.forEach((n, i) => {
          if (i >= startIndex && i <= endIndex) {
            n.outputable = true;
          }
        });
      }
    }
    if (val === 'allCancel') {
      if (this.type !== 'map') {
        this.tableData.forEach((n) => {
          n.outputable = false;
        });
      } else {
        this.tableShowData.forEach((n) => {
          n.outputable = false;
        });
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = this.currentPage * this.pageSize - 1;
        this.tableData.forEach((n, i) => {
          if (i >= startIndex && i <= endIndex) {
            n.outputable = false;
          }
        });
      }
    }
  }
  test(row) {
    if (!row.mvel || !row.name) {
      this.$tip.error(this.$t('pa.flow.msg99'));
      return;
    }
    row.used = row.outputable === undefined ? false : row.outputable;
    delete row.outputable;
    this.$emit('test', row);
  }
  getInput() {
    return this.input;
  }
  getTableData() {
    return _.filter(this.tableData, { disabled: false });
  }
  getAllField() {
    return this.tableData;
  }
  getJobNode() {
    const fieldArr: any = [];
    let flag = true;
    for (const i of Object.keys(this.tableData)) {
      const rec = this.tableData[i];
      if (!rec.disabled) {
        const index1 = _.findIndex(this.tableData, { name: rec.name });
        if (index1 >= 0 && index1 !== _.toNumber(i)) {
          this.$tip.error(this.$t('pa.flow.msg100', [_.toNumber(i) + 1]));
          flag = false;
          break;
        }
        // 校验字段名和类型是否输入
        if (!this.isDqMonitor) {
          if (rec.name === '' || rec.type === '') {
            this.$tip.error(this.$t('pa.flow.msg101', [_.toNumber(i) + 1]));
            flag = false;
            break;
          }
          const arr = rec.params;
          if (arr !== undefined) {
            for (let j = 0; j < arr.length; j++) {
              if (_.toString(rec.params[j].value) === '') {
                this.$tip.error(this.$t('pa.flow.msg102', [_.toNumber(i) + 1]));
                flag = false;
                break;
              }
            }
          }
        } else {
          if (rec.name === '' || rec.mvel === '') {
            this.$tip.error(this.$t('pa.flow.msg101', [_.toNumber(i) + 1]));
            flag = false;
            break;
          }
        }
      }
      if (!this.isDqMonitor) {
        fieldArr.push({
          name: rec.name,
          type: rec.type,
          method: rec.method === undefined ? '' : rec.method,
          params: rec.params === undefined ? [] : rec.params,
          outputable: rec.outputable,
          targetable: rec.targetable,
          disabled: rec.disabled
        });
        // 处理规则选中方法类型
        const method =
          this.selectAssetFuncs.find((el) => el.name === rec.method) ||
          this.selectPrivateFuncs.find((el) => el.name === rec.method);
        if (method) {
          fieldArr[fieldArr.length - 1].funcType = method.funcType;
        }
      } else {
        fieldArr.push({
          name: rec.name,
          used: rec.outputable,
          mvel: rec.mvel
        });
      }
    }
    if (flag) {
      const nodeDto = _.cloneDeep(this.jobNode);
      nodeDto.inputFields = this.input;
      if (this.isDqMonitor) {
        nodeDto.outputFields = this.input;
        if (!nodeDto.properties) {
          nodeDto.properties = {};
        }
        nodeDto.properties['rules'] = fieldArr;
        nodeDto.outputFields.forEach((el) => {
          el.outputable = true;
        });
      } else {
        nodeDto.outputFields = fieldArr;
      }
      return nodeDto;
    }
    return null;
  }

  loadData(jobData: any, data: any, showMethod: boolean, showOper: boolean, showAddRowBtn: boolean) {
    this.tableData = [];
    this.showMethod = showMethod;
    this.showOper = showOper;
    this.showAddRowBtn = showAddRowBtn;
    this.jobData = _.cloneDeep(jobData);
    this.assetFields = _.cloneDeep(this.$store.getters.assetFields);
    this.assetFuncs = _.cloneDeep(this.$store.getters.assetFuncs);
    // 获取上一个节点的输出字段作为该节点的输入字段
    if (_.toString(this.jobData.content) !== '') {
      // 获取上一个节点的输出字段作为该节点的输入字段
      // 由于组件有多个上游组件进行连线时，字段需要一一对应，但是字段顺序可以不同。
      // 且连线时，后连的输入顺序会覆盖前一条连线的顺序，所以此处去获取最后一条连线信息
      const edges = this.jobData.content.edges.filter((e) => e.endNode === data.nodeId);
      let preOutputFields: any = [];
      if (edges && edges.length > 0) {
        const preNode: any = _.find(this.jobData.content.nodes, {
          nodeId: edges[edges.length - 1].startNode
        });
        preOutputFields = preNode.outputFields;
      }
      this.input = [];
      const filterResult: any = _.filter(preOutputFields, { outputable: true });
      filterResult.forEach((n) => {
        this.input.push({
          name: n.name,
          type: n.type,
          targetable: n.targetable
        });
        if (!this.isDqMonitor) {
          this.tableData.push({
            name: n.name,
            type: n.type,
            disabled: true,
            outputable: false,
            targetable: n.targetable
          });
        }
      });
      // 根据记录数量设置表格高度
      if (this.tableData.length > 5 && this.type !== 'map') {
        this.tableHeight = 400;
      }
      this.jobNode = _.cloneDeep(data);
      if (this.jobNode.outputFields !== undefined) {
        let outputFields = [];
        outputFields = this.isDqMonitor ? this.jobNode.properties.rules : this.jobNode.outputFields;
        outputFields.forEach((n: any) => {
          // tableData是preOutputFields的输出字段
          const rec: any = _.find(this.tableData, { name: n.name });
          if (rec !== undefined && (rec.disabled === undefined || rec.disabled)) {
            if (!this.isDqMonitor) {
              this.$set(rec, 'outputable', n.outputable);
            }
          } else if (!n.disabled) {
            if (n.targetable === undefined || !n.targetable) {
              // 设置不是目标字段到表格中
              this.tableData.push(
                this.isDqMonitor
                  ? { name: n.name, mvel: n.mvel, used: n.used, outputable: n.used }
                  : {
                      name: n.name,
                      type: n.type,
                      method: n.method,
                      params: n.params,
                      disabled: false,
                      targetable: false,
                      outputable: n.outputable
                    }
              );
            }
          }
        });
      }
      // bugfix：修复映射组件在搜索后，对于tableData数据修改时 index获取错误的问题
      // 此处对原来的index做一层备份
      this.tableData.forEach((item, index) => {
        item.realIndex = index;
      });
      this.searchList = _.clone(this.tableData);
      if (this.type === 'map') {
        this.tableShowData = this.tableData.slice(0, this.pageSize);
        this.total = this.tableData.length;
      }
      if (this.isDqMonitor) {
        this.input.forEach((el) => {
          this.code.push({ value: this.wrapField(el.name) });
        });
      }
      // 获取初始上游输入字段字段
      this.inputFieleOptions = this.input.map((n) => {
        return {
          name: this.wrapField(n.name),
          type: n.type
        };
      });
      // 获取初始化方法字段
      get(this.getAssetsFunc).then((resp: any) => {
        if (resp.success) {
          if (this.isDqMonitor) {
            resp.data.forEach((value) => {
              this.code.push({ value });
            });
          } else {
            // 将返回值根据funcType区分独享方法与共享方法。
            resp.data.forEach((el) => {
              this[el.funcType === 'SHARE' ? 'selectAssetFuncs' : 'selectPrivateFuncs'].push(el);
            });
            this.selectAssetFuncs = resp.data.filter((el) => el.funcType === 'SHARE');
            this.selectPrivateFuncs = resp.data.filter((el) => el.funcType === 'PRIVATE');
            store.commit(SET_ASSET_FUNCS, this.selectAssetFuncs);
            this.assetFuncs = _.cloneDeep(this.$store.getters.assetFuncs);
            this.selectPrivateFuncsCopy = _.cloneDeep(this.selectPrivateFuncs);
          }
        } else {
          this.$tip.error(resp.msg);
        }
      });
      // this.merge();
    }
  }

  merge(index, query = '') {
    this.fieldsAndMethods = [
      {
        label: this.$t('pa.flow.field'),
        options: this.field2Option(query)
      },
      {
        label: this.$t('pa.flow.publicMethod'),
        options: this.shareFuncToOption(index)
      }
    ];
  }

  field2Option(query) {
    const list: any = _.cloneDeep(this.inputFieleOptions);
    this.tableData
      .filter((item) => !item.disabled)
      .forEach((n) => {
        list.push({
          name: this.wrapField(n.name),
          type: n.type
        });
      });
    const obj = {};
    const info = list.filter((item) => item.name.toLowerCase().includes(query));
    const lastArray = info.reduce((total, item) => {
      obj[item.name] ? '' : (obj[item.name] = true && total.push(item));
      return total;
    }, []);
    return lastArray;
  }
  shareFuncToOption(index) {
    return this.selectAssetFuncs.slice(0, (index + 1) * 20);
  }
  methodVisibleChangeHandle(val, row, index) {
    if (val) {
      this.funStartIndex = 0;
      this.merge(0);
      this.excludeCurrRowField(row);
      this.currentRow = index;
    } else if (!val && index === this.currentRow) {
      this.fieldsAndMethods = [];
    }
  }
  ruleLoadMore(row) {
    if (this.funStartIndex * 20 >= this.assetFuncs.length) {
      return;
    }
    this.funStartIndex++;
    this.merge(this.funStartIndex, this.funcSelectQuery);
    this.excludeCurrRowField(row);
  }

  clearMethod() {
    this.selectAssetFuncs = this.assetFuncs;
    this.selectPrivateFuncs = this.selectPrivateFuncsCopy;
  }

  // 处理规则（字段、共享方法、独享方法）搜索
  ruleRemoteMethod(query, row) {
    this.funcSelectQuery = query;
    query = query.toLowerCase();
    this.ruleloading = true;
    this.selectAssetFuncs = this.assetFuncs.filter((item) => item.name && item.name.toLowerCase().includes(query));
    this.selectPrivateFuncs = this.selectPrivateFuncsCopy.filter(
      (item) => item.name && item.name.toLowerCase().includes(query)
    );
    this.funStartIndex = 0;
    this.merge(this.funStartIndex, query);
    this.excludeCurrRowField(row);
    this.fieldsAndMethods[0].options = this.fieldsAndMethods[0].options.filter(
      (item) => item.name && item.name.toLowerCase().includes(query)
    );
    this.ruleloading = false;
  }
  handlefieldBlur(index: number) {
    this.tableShowData[index].name = (this.tableShowData[index].name || '').trim();
  }
}
</script>
<style lang="scss" scoped>
.node-table {
  border-top: 1px solid #ebeef5;
  ::v-deep .el-table__fixed {
    height: 100% !important;
    &-right {
      height: 100% !important;
    }
  }
}
::v-deep .el-input__inner {
  height: 29px;
}
.form-content {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 40px;
  .tip {
    color: red;
    text-align: left;
    flex: 1;
  }
  &__tooltip {
    margin-left: 10px;
    line-height: 2;
  }
}
.btn-content {
  float: right;
  margin-top: 7px;
}
.search-field-btn {
  width: 200px;
  float: right;
  margin: 8px 10px 8px 0;
}
.el-icon-question {
  font-size: 20px;
  margin-top: 5px;
}
.dialog-page-content {
  margin-top: 5px;
}
.mvel-input {
  width: 100%;
}
</style>
