<template>
  <div>
    <div class="head-info">
      <div class="name" style="display: flex; align-items: center; word-break: keep-all">
        {{ $t('pa.data.baseFields') }}
        <bs-search
          v-model="searchValue"
          style="margin-left: 10px"
          :placeholder="$t('pa.placeholder.vaguePlaceholder')"
          @change="handleSearch"
        />
      </div>
      <div style="display: flex; white-space: pre">
        <el-button type="primary" size="small" style="margin-left: 10px" @click="exportFile">
          {{ $t('pa.action.export') }}
        </el-button>
      </div>
    </div>
    <bs-table
      paging-front
      size="mini"
      max-height="539px"
      :column-settings="false"
      :show-multiple-selection="false"
      :column-data="columnData"
      :data="tableData"
      :page-data="pageData"
      :selectable="(row) => !row.disabled"
      @page-change="pageChange"
    >
      <template slot="primaryKey" slot-scope="{ row }">
        <el-switch :value="row.primaryKey" active-value="1" :disabled="true" inactive-value="0" />
      </template>
    </bs-table>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator';
import { URL_TABLE_EXPORT } from '@/apis/commonApi';
import { PaBase } from '@/common/pipeace-base';
import { download } from '@/apis/utils/net';
@Component({
  name: 'Fields'
})
export default class Fields extends PaBase {
  @Prop({ default: [] }) list!: any[];
  @Prop({ type: String, default: '' }) resType!: string;
  pageData = { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 };
  tableData: any = [];
  // 字段筛选信息
  searchValue = '';
  get id() {
    return this.$route.query.id;
  }
  get columnData(): any {
    return [
      {
        label: this.$t('pa.data.table.detail.fieldName'),
        value: 'fieldName',
        minWidth: 130,
        show: true
      },
      {
        label: this.$t('pa.data.table.detail.chineseName'),
        value: 'fieldNameCn',
        minWidth: 130,
        show: true
      },
      {
        label: this.$t('pa.data.table.detail.fieldType'),
        value: 'fieldType',
        minWidth: 130,
        show: true
      },
      {
        label: this.$t('pa.data.table.detail.columnCluster'),
        value: 'columnFamily',
        minWidth: 130,
        show: this.resType === 'HBASE'
      },
      {
        label: this.$t('pa.data.table.detail.primaryKey'),
        value: 'primaryKey',
        minWidth: 130,
        show: this.resType !== 'HDFS'
      },
      {
        label: this.$t('pa.data.table.detail.partition'),
        value: 'partition',
        minWidth: 130,
        show: this.resType === 'HDFS'
      },
      {
        label: this.$t('pa.data.table.detail.businessCaliber'),
        value: 'businessExplain',
        minWidth: 130,
        show: true
      }
    ].filter((el: any) => el.show);
  }

  @Watch('list', { immediate: true })
  getList(val) {
    this.tableData = val;
    this.pageData.total = val.length;
  }

  exportFile() {
    download(`${URL_TABLE_EXPORT}/${this.resType}?id=${this.id}`);
  }
  pageChange(curPage, pageSize) {
    this.pageData.currentPage = curPage;
    this.pageData.pageSize = pageSize;
  }
  handleSearch(val: string) {
    this.tableData = this.list.filter((el: any) => el.fieldName.includes(val));
    this.pageData.total = this.tableData.length;
    this.pageData.currentPage = 1;
  }
}
</script>
<style scoped lang="scss">
@import './style.scss';
</style>
