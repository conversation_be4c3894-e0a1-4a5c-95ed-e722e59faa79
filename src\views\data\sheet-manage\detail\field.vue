<template>
  <div>
    <div class="head-info">
      <div class="name" style="display: flex; align-items: center; word-break: keep-all">
        基础字段
        <el-input
          v-model="searchValue"
          placeholder="输入字段名模糊搜索"
          style="margin-left: 10px"
          @input="searchField"
        >
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
      </div>
      <div style="display: flex; white-space: pre">
        <el-button type="primary" size="small" style="marginleft: 10px" @click="exportFile">
          导出
        </el-button>
      </div>
    </div>
    <el-table :data="tableData" class="field-style" size="mini">
      <el-table-column prop="fieldName" min-width="130">
        <template slot="header">
          <span>字段名</span>
        </template>
        <template slot-scope="{ row }">
          {{ row.fieldName }}
        </template>
      </el-table-column>
      <el-table-column prop="fieldNameCn" min-width="130">
        <template slot="header">
          <span>中文名</span>
        </template>
        <template slot-scope="{ row }">
          {{ row.fieldNameCn }}
        </template>
      </el-table-column>
      <el-table-column prop="fieldType" min-width="130">
        <template slot="header">
          <span>字段类型</span>
        </template>
        <template slot-scope="{ row }">
          {{ row.fieldType }}
        </template>
      </el-table-column>
      <!-- 列蔟 -->
      <el-table-column v-if="resType === 'HBASE'" prop="columnFamily" label="列簇" min-width="130">
        <template slot-scope="{ row }">
          {{ row.columnFamily }}
        </template>
      </el-table-column>
      <el-table-column v-if="resType !== 'HDFS'" prop="primaryKey" label="主键" min-width="130">
        <template slot-scope="{ row }">
          <el-switch :value="row.primaryKey" active-value="1" inactive-value="0" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="resType === 'HIVE' || resType === 'HDFS'"
        prop="partition"
        label="分区"
        min-width="130"
      >
        <template slot-scope="{ row }">
          <el-switch :value="row.partition" active-value="1" inactive-value="0" />
        </template>
      </el-table-column>
      <el-table-column prop="businessExplain" min-width="130">
        <template slot="header">
          <span>业务口径</span>
        </template>
        <template slot-scope="{ row }">
          {{ row.businessExplain }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination"
      background
      :current-page.sync="pageData.currentPage"
      :page-size="pageData.pageSize"
      :page-sizes="[10, 20, 40, 80, 100]"
      :total="total"
      :pager-count="5"
      layout="slot, total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { URL_TABLE_EXPORT } from '@/apis/commonApi';
import { cloneDeep } from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component({
  name: 'Fields'
})
export default class Fields extends PaBase {
  @Prop({
    default() {
      return [];
    }
  })
  list!: any[];
  @Prop({ type: String, default: '' }) resType!: string;
  // type = '';
  pageData = {
    currentPage: 1,
    pageSize: 10,
    total: 0
  };
  cloneTableData: any = [];
  tableData: any = [];
  // 字段筛选信息
  searchValue = '';
  allTableData = [];

  @Watch('list', { immediate: true })
  getList(arr) {
    this.cloneTableData = arr;
    this.handleCurrentChange(1);
  }
  // @Watch('resType')
  // getResType(val) {
  //   this.type = val;
  // }
  get total() {
    return this.cloneTableData.length;
  }

  handleCurrentChange(val) {
    const length = this.pageData.pageSize || 10;
    this.pageData.currentPage = val;
    this.tableData = this.cloneTableData.slice(length * (val - 1), length * val);
  }
  handleSizeChange(val) {
    this.pageData.pageSize = val;
    this.handleCurrentChange(1);
  }
  // 查询字段
  searchField(val) {
    if (!this.allTableData.length) {
      this.allTableData = cloneDeep(this.cloneTableData);
    }
    this.searchValue = val;
    if (val) {
      this.cloneTableData = this.allTableData.filter((item: any) => {
        return item.fieldName && item.fieldName.indexOf(val) !== -1;
      });
    } else {
      this.cloneTableData = cloneDeep(this.allTableData);
    }
    this.handleCurrentChange(1);
  }
  async exportFile() {
    const { id } = this.$route.query;
    window.location.href =
      Vue.axios.defaults.baseURL + URL_TABLE_EXPORT + '/' + this.resType + '?id=' + id;
  }
}
</script>
<style scoped lang="scss">
@import './style.scss';
</style>
