import { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { baseUrl } from '@/config';
import { Message, MessageBox } from 'bs-ui-pro';
import { Encrypt, Decrypt, getOrigin, canParse } from './utils';
import Base64 from './base64';
import store from '@/store';
import $ from 'jquery';

let unEncyptUrls = [];
const base64 = new Base64();
// 需要加密的url
const needEncrypts = (urlStr) => {
  getAesExcludeUrls();
  if (
    unEncyptUrls.some((s) => urlStr === s || urlStr.includes(s)) ||
    urlStr.includes('download') ||
    urlStr.includes('upload')
  ) {
    return false;
  } else {
    return urlStr.includes('rs/pa');
  }
};
// 获取不需要加密的url列表
function getAesExcludeUrls() {
  if (unEncyptUrls.length === 0) {
    $.ajax({
      url: baseUrl.prev + '/openApi/pa/getAesExcludeUrls',
      async: false,
      success: (data) => {
        unEncyptUrls = data;
      }
    });
  }
}

/**
 * @des: session过期处理函数
 * @author: ranran
 * @date: 2020-01-13 17:47:18
 */
function invalidRequestHandler(hasNoPermission = false) {
  MessageBox({
    title: '提示',
    message: '可能您的会话已过期，是否要跳转到登录页面？',
    showCancelButton: true,
    callback: (action) => {
      if (action === 'confirm') {
        location.href = store.state.app.loginUrl || baseUrl.loginUrl;
      }
      if (['cancel', 'close'].includes(action) && hasNoPermission) {
        location.reload();
      }
    }
  });
}

// 需要JSON解析的url
const needParseUrl = (urlStr) => {
  const urls = [
    '/rs/pa/resConf/getFormConf',
    '/rs/pa/job/jobStatusStatistics',
    '/rs/pa/job/test/run',
    '/rs/pa/job/sourceCode'
  ];
  if (urls.some((s) => urlStr === s || urlStr.includes(s))) {
    return false;
  } else {
    return true;
  }
};

const handleRequest = (config: AxiosRequestConfig, isEncypt: any) => {
  const url: any = config.url;
  const urlStrs: any = [];
  if (isEncypt && needEncrypts(url)) {
    // 处理url本身带有参数的情况
    if (config.params || url.indexOf('?') > -1) {
      const index = url.indexOf('?');
      if (index > 0) {
        const urlParams = url.slice(index + 1);
        const urlParamsArray = urlParams.split('&').map((s) => {
          const idx = s.indexOf('=');
          const left = s.slice(0, idx + 1);
          const right = s.slice(idx + 1);
          if (left === 'websocketKey=') {
            return left + (right || '');
          } else {
            return left + (right ? base64.encode(Encrypt(right)) : '');
          }
        });
        urlStrs.push(...urlParamsArray);
      }
      if (config.params) {
        for (const name in config.params) {
          if (config.params.hasOwnProperty(name)) {
            urlStrs.push(name + '=' + base64.encode(Encrypt(config.params[name])));
          }
        }
        config.params = {};
      }
    }
    // 增加websocketKey
    if (
      store.state.userInfo.userName !== undefined &&
      store.state.userInfo.userName !== '' &&
      !url.includes('websocketKey')
    ) {
      urlStrs.push('websocketKey=' + store.state.userInfo.userName);
    }
    config.url = getOrigin(config.url) + '?' + urlStrs.join('&');
    if (config.data) {
      const enc = Encrypt(JSON.stringify(config.data));
      const encryptDataBase64 = base64.encode(enc);
      // const dec = Decrypt(base64.decode(encryptDataBase64));
      config.data = { requestData: encryptDataBase64 };
    }
  } else {
    // 不需要加密 增加socketKey
    const hasWebsocketKey = url.indexOf('websocketKey=') >= 0;
    const hasWenHao = url.indexOf('?') >= 0;
    if (!hasWebsocketKey) {
      let symbol = '?';
      if (hasWenHao) {
        symbol = '&';
      }
      if (store.state.userInfo.userName !== undefined && store.state.userInfo.userName !== '') {
        config.url = config.url + symbol + 'websocketKey=' + store.state.userInfo.userName;
      }
    }
  }
};

const handleResponse = (response: AxiosResponse, isEncypt: any) => {
  const hasNoPermission = response.data.msg === '用户会话过期请重新登录';
  // session过期跳转登录页
  if (
    (response.headers['content-type'] === 'text/html' &&
      response.data.includes('<!DOCTYPE html>')) ||
    hasNoPermission
  ) {
    invalidRequestHandler(hasNoPermission);
    return Promise.reject('invalid session');
  }
  // 解密数据
  if (isEncypt && response.data && response.config && needEncrypts(response.config.url)) {
    if (response.data.data) {
      const decryptData = Decrypt(base64.decode(response.data.data));
      response.data.data =
        canParse(decryptData) && needParseUrl(response.config.url)
          ? JSON.parse(decryptData)
          : decryptData;
    }
  }
  if (response.status === 200) {
    const content: string = response.headers['content-disposition'];
    if (content) {
      const fileName: string = content.split(';').reduce((pre, next) => {
        if (next.includes('filename=')) {
          pre = next.split('=')[1].replace(/\"/g, '');
        }
        return pre;
      }, '');
      return Promise.resolve({
        blob: response.data,
        fileName: fileName || '未知文件'
      });
    } else {
      return Promise.resolve(response.data);
    }
    /**
     * 存在部分内容
     */
  } else if (response.status === 206) {
    Message({ message: response.data.msg, type: 'warning' });
    return Promise.resolve(response.data);
  } else if (response.data.msg) {
    Message({ message: response.data.msg, type: 'warning' });
    return Promise.reject(response);
  } else {
    return Promise.reject(response);
  }
};

const handleError = (error: AxiosError) => {
  if (error.message === '查询超时，请刷新重试') {
    Message({ message: '查询超时，请刷新重试', type: 'error' });
    return;
  }
  if (!error.response) {
    console.log(error);
    /**
     * 如果不能返回正常信息，默认跳转到登录页面
     */
    Message({
      message: '请求超时！',
      type: 'error'
    });
    return;
  }
  if (error.response.status) {
    switch (error.response.status) {
      /**
       * 401: 未登录未登录则跳转登录页面，
       * 并携带当前页面的路径在登录成功后返回当前页面，
       * 这一步需要在登录页操作。
       */
      case 401:
      /**
       * 403 session过期
       * 登录过期对用户进行提示
       * 跳转登录页面
       */
      case 403:
        Message({ message: '登录过期，请重新登录', type: 'error' });
        invalidRequestHandler();
        break;
      case 404:
        Message({
          message: `${error.response.data && error.response.data.path} 资源未被发现`,
          type: 'error'
        });
        break;
      case 405:
        invalidRequestHandler();
        break;
      default:
        Message({
          message:
            error.response.data && error.response.data.msg
              ? error.response.data.msg
              : `未知错误，错误代码：${error.response.status}`,
          type: 'error'
        });
    }
    return Promise.reject(error.response);
  } else {
    Message({
      message: `未知错误：${JSON.stringify(error.response)}`,
      type: 'error'
    });
  }
};

export { handleRequest, handleResponse, handleError };

export const handleConfigToken = (config, token: string) => {
  const loginToken = sessionStorage.getItem('token');
  const authorization = loginToken ? loginToken : token ? `Bearer ${token}` : '';
  authorization && (config.headers.Authorization = authorization);
};
export const handleResponseToken = ({ Authorization, authorization }: any = {}) => {
  if (!authorization && !Authorization) return;
  sessionStorage.setItem('token', Authorization || authorization);
};
