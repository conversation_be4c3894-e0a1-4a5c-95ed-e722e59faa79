import { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { baseUrl } from '@/config';
import { Message, MessageBox } from 'bs-ui-pro';
import { Encrypt, Decrypt, getOrigin, canParse } from './utils';
import Base64 from './base64';
import store from '@/store';
import axios from 'axios';
import { throttle } from 'lodash';
import { getToken, cookie } from '@/utils';
let unEncyptUrls = [];
const base64 = new Base64();
// 需要加密的url
const needEncrypts = async (urlStr) => {
  await getAesExcludeUrls();
  if (
    unEncyptUrls.some((s) => urlStr === s || urlStr.includes(s)) ||
    urlStr.includes('download') ||
    urlStr.includes('upload')
  ) {
    return false;
  } else {
    return urlStr.includes('rs/pa');
  }
};
// 获取不需要加密的url列表
async function getAesExcludeUrls() {
  if (unEncyptUrls.length === 0) {
    const { data = [] } =
      (await axios({
        url: '/openApi/pa/getAesExcludeUrls',
        baseURL: baseUrl.prev
      })) || [];
    unEncyptUrls = data;
  }
}
/**
 * @des: session过期处理函数
 * @author: ranran
 * @date: 2020-01-13 17:47:18
 */
import i18n from '@/i18n';
export function invalidRequestHandler(hasNoPermission = false) {
  MessageBox({
    title: i18n.t('pa.prompt') as string,
    message: i18n.t('pa.sessionExpired') as string,
    showCancelButton: true,
    callback: (action) => {
      if (action === 'confirm') {
        location.href = store.getters.loginUrl || baseUrl.loginUrl;
      }
      if (['cancel', 'close'].includes(action) && hasNoPermission) {
        location.reload();
      }
    }
  });
}

// 需要JSON解析的url
const needParseUrl = (urlStr) => {
  const urls = [
    '/rs/pa/resConf/getFormConf',
    '/rs/pa/job/jobStatusStatistics',
    '/rs/pa/job/test/run',
    '/rs/pa/job/sourceCode'
  ];
  if (urls.some((s) => urlStr === s || urlStr.includes(s))) {
    return false;
  } else {
    return true;
  }
};

/**
 * 遍历嵌套对象并调用回调函数处理每个属性
 * 如果属性值是对象（且不是数组或null），则递归遍历
 * 对于值为字符串且以pa.sys开头的key，进行国际化翻译
 *
 * @param obj 要遍历的对象
 */
export function traverseResponseData(obj: any, response: any = {}) {
  if (typeof obj === 'string') {
    response.data.data = obj.replace(/pa\.sys\.[a-z]+(?:\.[a-z0-9]+)*/gi, (key) => store.getters.i18nMap[key]);
    return;
  }
  const isObj = (v) => typeof v === 'object' && v !== null && !Array.isArray(v);
  const isNeedTrans = (v) => typeof v === 'string' && v.includes('pa.sys');
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      if (isObj(value)) {
        traverseResponseData(value);
      } else if (Array.isArray(value)) {
        value.forEach((item, index) => {
          if (isObj(item)) {
            traverseResponseData(item);
          } else if (isNeedTrans(item)) {
            item[index] = store.getters.i18nMap[item];
          }
        });
      } else if (isNeedTrans(value)) {
        obj[key] = obj[key].replace(/pa\.sys\.[a-z]+(?:\.[a-z0-9]+)*/gi, (key) => store.getters.i18nMap[key]);
      }
    }
  }
}

const handleRequest = async (config: AxiosRequestConfig, isEncypt: boolean) => {
  const url: any = config.url;
  const urlStrs: any = [];
  if (isEncypt && (await needEncrypts(url))) {
    // 处理url本身带有参数的情况
    if (config.params || url.indexOf('?') > -1) {
      const index = url.indexOf('?');
      if (index > 0) {
        const urlParams = url.slice(index + 1);
        const urlParamsArray = urlParams.split('&').map((s) => {
          const idx = s.indexOf('=');
          const left = s.slice(0, idx + 1);
          const right = s.slice(idx + 1);
          if (left === 'websocketKey=') {
            return left + (right || '');
          } else {
            return left + (right ? base64.encode(Encrypt(right)) : '');
          }
        });
        urlStrs.push(...urlParamsArray);
      }
      if (config.params) {
        for (const name in config.params) {
          if (config.params.hasOwnProperty(name)) {
            urlStrs.push(name + '=' + base64.encode(Encrypt(config.params[name])));
          }
        }
        config.params = {};
      }
    }
    // 增加websocketKey
    if (store.getters.userName !== undefined && store.getters.userName !== '' && !url.includes('websocketKey')) {
      urlStrs.push('websocketKey=' + store.getters.userName);
    }
    config.url = getOrigin(config.url) + '?' + urlStrs.join('&');
    if (config.data) {
      const enc = Encrypt(JSON.stringify(config.data));
      const encryptDataBase64 = base64.encode(enc);
      // const dec = Decrypt(base64.decode(encryptDataBase64));
      config.data = { requestData: encryptDataBase64 };
    }
  } else {
    // 不需要加密 增加socketKey
    const hasWebsocketKey = url.indexOf('websocketKey=') >= 0;
    const hasWenHao = url.indexOf('?') >= 0;
    if (!hasWebsocketKey) {
      let symbol = '?';
      if (hasWenHao) {
        symbol = '&';
      }
      if (store.getters.userName !== undefined && store.getters.userName !== '') {
        config.url = config.url + symbol + 'websocketKey=' + store.getters.userName;
      }
    }
  }
};

const handleResponse = async (response: AxiosResponse, isEncypt: boolean) => {
  const hasNoPermission = response.data.msg === (i18n.t('pa.logBack') as string);
  // session过期跳转登录页
  if ((response.headers['content-type'] === 'text/html' && response.data.includes('<!DOCTYPE html>')) || hasNoPermission) {
    invalidRequestHandler(hasNoPermission);
    return Promise.reject('invalid session');
  }
  // 解密数据
  if (isEncypt && response.data && response.config && (await needEncrypts(response.config.url))) {
    if (response.data.data) {
      const decryptData = Decrypt(base64.decode(response.data.data));
      response.data.data =
        canParse(decryptData) && needParseUrl(response.config.url) ? JSON.parse(decryptData) : decryptData;
    }
  }
  if (response.status === 200) {
    const content: string = response.headers['content-disposition'];
    if (content) {
      const fileName: string = content.split(';').reduce((pre, next) => {
        if (next.includes('filename=')) {
          pre = next.split('=')[1].replace(/\"/g, '');
        }
        return pre;
      }, '');
      return Promise.resolve({
        blob: response.data,
        fileName: fileName || i18n.t('pa.unknownFile')
      });
    } else {
      // 此处统一对后端返回的国际化key进行翻译
      try {
        if (response.request.response.includes('pa.sys') && typeof response.data === 'object') {
          traverseResponseData(response.data.data, response);
        }
      } catch (e) {
        console.log('翻译失败: ', response);
      }
      return Promise.resolve(response.data);
    }
    /**
     * 存在部分内容
     */
  } else if (response.status === 206) {
    Message({ message: response.data.msg, type: 'warning' });
    return Promise.resolve(response.data);
  } else if (response.data.msg) {
    Message({ message: response.data.msg, type: 'warning' });
    return Promise.reject(response);
  } else {
    return Promise.reject(response);
  }
};

// 对登录过期的错误提示做节流处理
const showNoAccessMsg = throttle(
  () => {
    Message({ message: i18n.t('pa.logExpired') as string, type: 'error' });
  },
  3000,
  { trailing: false }
);
const handleError = (error: AxiosError | any) => {
  if (error.message === i18n.t('pa.queryTimeout')) {
    Message({ message: i18n.t('pa.queryTimeout') as string, type: 'error' });
    return;
  }
  if (!error.response) {
    console.log(error);
    /**
     * 如果不能返回正常信息，默认跳转到登录页面
     */
    Message({
      message: i18n.t('pa.requestTimeout') as string,
      type: 'error'
    });
    return;
  }
  if (error.response.status) {
    switch (error.response.status) {
      /**
       * 401: 未登录未登录则跳转登录页面，
       * 并携带当前页面的路径在登录成功后返回当前页面，
       * 这一步需要在登录页操作。
       */
      case 401:
      /**
       * 403 session过期
       * 登录过期对用户进行提示
       * 跳转登录页面
       */
      case 403:
        showNoAccessMsg();
        invalidRequestHandler();
        break;
      case 404:
        Message({
          message: i18n.t('pa.resourceNotFound', [error.response.data && error.response.data.path]) as string,
          type: 'error'
        });
        break;
      case 405:
        invalidRequestHandler();
        break;
      default:
        Message({
          message:
            error.response.data && error.response.data.msg
              ? error.response.data.msg
              : (i18n.t('pa.unknownErrorCode', [error.response.status]) as string),
          type: 'error'
        });
    }
    return Promise.reject(error.response);
  } else {
    Message({
      message: i18n.t('pa.unknownError', [JSON.stringify(error.response)]) as string,
      type: 'error'
    });
  }
};

export { handleRequest, handleResponse, handleError };

export const handleConfigToken = (config) => {
  const token = getToken();
  token && (config.headers.Authorization = token);
};
export const handleResponseToken = ({ Authorization, authorization }: any = {}) => {
  if (!authorization && !Authorization) return;
  cookie.set('access_token', Authorization || authorization);
};
