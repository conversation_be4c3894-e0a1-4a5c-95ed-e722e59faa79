<template>
  <pro-page :active-tab="activeTab" :tabs="tabs" :loading="loading" @tab-click="handleTabClick">
    <div v-if="activeTab === '1'" slot="operation">
      <el-button type="primary" @click="toEdit"> 编辑 </el-button>
      <el-button style="marginleft: 10px" type="primary" @click="showDataPreviewDialog = true">
        数据预览
      </el-button>
    </div>
    <el-button v-if="activeTab === '3'" slot="operation" type="primary" @click="exportRelationList">
      下载引用关系
    </el-button>
    <!-- 基本信息 -->
    <view-detail-info v-if="activeTab === '1'" @get-sql="getSql" />
    <!-- 历史版本 -->
    <view-detail-history v-if="activeTab === '2'" />
    <!-- 引用关系 -->
    <reference-relation
      v-if="activeTab === '3'"
      :id="this.$route.query.id"
      type="JOB"
      relation="view"
    />
    <!-- 源码 -->
    <div v-if="activeTab === '4'" class="code-content">
      <bs-code
        ref="code"
        :value="sqlCode"
        language="sql"
        :read-only="false"
        :extra-style="extraCodeStyle"
        :extra-operations="extraOperations"
      />
    </div>
    <!-- 数据预览弹窗 -->
    <DataPreviewDialog
      v-if="showDataPreviewDialog"
      :id="id"
      :visible.sync="showDataPreviewDialog"
      :request="dataPreviewRequest"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import ViewDetailInfo from './detail-info.vue';
import ViewDetailHistory from './detail-history.vue';
import ReferenceRelation from '@/views/data/components/reference-relation.vue';
import { download } from '@/apis/utils/net';
import ClipboardJS from 'clipboard';
import DataPreviewDialog from '../../modals/data-preview-dialog.vue';
import { dataPreviewForView } from '@/apis/dataApi';
@Component({
  name: 'ViewDetail',
  components: { ViewDetailInfo, ReferenceRelation, ViewDetailHistory, DataPreviewDialog }
})
export default class ViewDetail extends Vue {
  loading = false;
  activeTab = '1';
  tabs = [
    {
      value: '1',
      label: '基本信息'
    },
    {
      value: '2',
      label: '历史版本'
    },
    {
      value: '3',
      label: '引用关系'
    },
    {
      value: '4',
      label: '源码'
    }
  ];
  sqlCode = '';
  extraCodeStyle = {
    height: 'calc(100vh - 264px)'
  };
  extraOperations = [{ icon: 'el-icon-document-copy', text: '复制' }];
  clipboard: any = null;
  // 数据预览字段
  showDataPreviewDialog = false;
  dataPreviewRequest = dataPreviewForView;
  get id() {
    return this.$route.query.id;
  }
  getSql(code: string) {
    this.sqlCode = code;
  }
  // 初始化剪切版
  initClipboard() {
    if (this.clipboard) return;
    this.$nextTick(() => {
      const target = document.querySelector('.el-icon-document-copy')!.parentNode as Element;
      this.clipboard = new ClipboardJS(target, {
        text: () => {
          return this.sqlCode;
        }
      });
      this.clipboard.on('success', (e) => {
        this.$message.success('复制成功！');
        e.clearSelection();
      });
      this.clipboard.on('error', (e) => {
        this.$message.error('复制不成功，请重新复制！');
        e.clearSelection();
      });
    });
  }
  handleTabClick(val) {
    this.activeTab = val;
    val === '4' && this.initClipboard();
  }
  // 导出引用关系
  exportRelationList() {
    download('/rs/pa/sql/view/exportRelation', '', { id: this.$route.query.id });
  }
  // 跳转至编辑页面
  toEdit() {
    const { id, title } = this.$route.query;
    this.$router.push({
      path: '/data/viewEdit',
      query: { id, status: '1', title }
    });
  }
}
</script>

<style scoped lang="scss">
.code-content {
  box-sizing: border-box;
  height: calc(100vh - 174px);
  padding: 20px;
  background: #fff;
}
</style>
