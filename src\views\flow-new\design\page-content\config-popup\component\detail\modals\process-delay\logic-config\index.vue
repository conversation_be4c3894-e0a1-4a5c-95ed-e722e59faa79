<template>
  <bs-dialog
    :title="`${title || ''} ${$t('pa.flow.config2')}`"
    width="1000px"
    append-to-body
    :visible.sync="display"
    class="config-dialog"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @confirm="handleConfirm"
  >
    <!--zhong -->
    <div v-loading="loading" :element-loading-text="$t('pa.flow.comLoading')" class="config__container">
      <!-- 逻辑关系 -->
      <logic-bar
        ref="bar"
        :data.sync="formData"
        :disabled="disabled"
        @change="generateExpression"
        @increase="generateConfigTemplate"
      />

      <!-- 结果表达式 -->
      <result-expression :expression="formData.resultExpression" />
      <!-- 配置明细 -->
      <logic-main
        ref="config"
        :disabled="disabled"
        :field-list="fieldList"
        :method-list="methodList"
        :data.sync="formData.conditions"
        :method-source-code.sync="methodCode"
        :expression-mapping="expressionMapping"
        @change="handleConfigChange"
        @copy="handleCopyConfig"
      />
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Watch, Vue } from 'vue-property-decorator';
import LogicBar from './logic-bar.vue';
import ResultExpression from './result-expression.vue';
import LogicMain from './config-detail/index.vue';
import { cloneDeep, isEmpty } from 'lodash';
import {
  handleFuncArgs,
  handleTableData,
  handleTableHead,
  isObject,
  isArray,
  // isString,
  isValidType,
  generateName
} from './utils';

@Component({ components: { LogicBar, ResultExpression, LogicMain } })
export default class LoConfig extends Vue {
  @Prop() name!: string;
  @Prop() title!: string;
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => [] }) fieldList!: any[];
  @Prop({ default: () => ({}) }) funcMapping!: any;
  @Prop({ default: () => ({}) }) methodList!: any;
  @PropSync('show', { default: false }) display!: boolean;
  @PropSync('data', { default: () => ({}) }) formData!: any;
  @PropSync('methodSourceCode', { default: () => ({}) }) methodCode!: any;

  @Ref('bar') readonly logicBar!: LogicBar;
  @Ref('config') readonly logicConfig!: LogicMain;

  private loading = false;
  private rawData: any = null; // 原始数据s
  private configData: any = []; // 条件配置详情
  private expressionMapping: any = {}; // 模块对应表达式映射

  @Watch('formData.conditions', { immediate: true, deep: true })
  generateExpression() {
    /* 逻辑表达式 */
    this.handleLogicalRelation();
    this.handleResultExpression();
  }

  async created() {
    try {
      this.loading = true;
      this.rawData = cloneDeep(this.formData);
      this.$set(this.formData, 'logicType', this.formData.logicType || '&&');
      const result = await this.parseConditions(isArray(this.formData.conditions));
      this.$set(this.formData, 'conditions', result);
      this.loading = false;
    } catch (e) {
      this.loading = false;
      console.log(e);
    }
  }
  /* 处理逻辑表达式 */
  handleLogicalRelation() {
    if (this.formData.logicType === 'CUSTOM') return;
    const result = this.formData.conditions
      .map(({ name, funcName }) => (funcName ? name : null))
      .filter(Boolean)
      .join(` ${this.formData.logicType} `);
    this.$set(this.formData, 'expr', result);
  }
  /* 处理结果表达式 */
  handleResultExpression() {
    let str = this.formData.expr.replace(/([A-Z])/g, '@@_$1_@@');
    /* 模块对应表达式 */
    const expressionMapping = this.formData.conditions.reduce((pre, next) => {
      const { name, funcName, funcType, funcArgs } = next;
      const params = funcArgs
        .map(({ key, value }) => {
          if (funcType === 'DEFAULT' && !isObject(this.funcMapping[funcName]).isOneParams) {
            return `${key},${value}`;
          }
          return key;
        })
        .filter(Boolean);
      if (funcName) {
        pre[name] = `${funcName}(<span class="code-green">${params.join(',')}</span>)`;
        str = str.replace(new RegExp(`@@_${name}_@@`, 'g'), pre[name]);
      }
      return pre;
    }, {});
    this.$set(this, 'expressionMapping', expressionMapping);
    /* 结果表达式 */
    const result = str.replace(/(\&\&|\|\|)/g, '<span class="code-red"> $1 </span>').replace(/[\@\_]/g, '');
    this.$set(this.formData, 'resultExpression', result);
  }

  /* 解析All条件 */
  parseConditions(list: any[] = []) {
    if (list.length < 1) {
      return [this.generateConfigTemplate(false)];
    }
    return list.map((el) => this.parseCondition(el));
  }
  /* 解析条件 */
  parseCondition(data: any, isChange = false) {
    const { funcId, funcType, funcArgs, paramsType } = isObject(this.funcMapping[data.funcName]);
    const result: any = {
      name: data.name,
      funcName: data.funcName,
      funcId,
      funcType: isValidType(funcType) ? funcType : data.funcType
    };
    result.funcArgs = handleFuncArgs(funcType, funcArgs, data.funcArgs, isChange);
    result.tableHead = handleTableHead(funcType, data.funcType, funcArgs, result.funcArgs);
    result.tableData = handleTableData(
      isValidType(funcType) ? funcType : data.funcType,
      funcArgs,
      result.funcArgs,
      paramsType
    );
    return result;
  }

  generateConfigTemplate(needPush = true, config: any = {}) {
    const { length } = this.formData.conditions;
    if (length > 25) {
      this.$message.warning(this.$t('pa.flow.msg182'));
      return;
    }
    const name = generateName(length + 1);
    const base = isEmpty(config)
      ? {
          funcName: '',
          funcType: '',
          funcArgs: [],
          tableHead: [],
          tableData: []
        }
      : config;
    return needPush ? this.formData.conditions.push({ ...base, name }) : { ...base, name };
  }

  handleConfigChange(index) {
    this.$set(this.formData.conditions, index, this.parseCondition(this.formData.conditions[index], true));
  }

  handleCopyConfig(index: number) {
    this.generateConfigTemplate(true, cloneDeep(this.formData.conditions[index]));
  }

  async handleConfirm() {
    try {
      const legalData = this.formData.conditions.filter((el) => {
        return el.funcArgs && el.funcName && el.funcType && el.name;
      });
      if (legalData.length < 1) {
        this.$message.error(this.$t('pa.tip.configureAtOne'));
        return;
      }
      await this.logicBar.validate();
      await this.logicConfig.validate();
      this.closeDialog(false);
    } catch (e) {
      console.log(e);
    }
  }
  closeDialog(resetData = true) {
    let data: any = this.rawData;
    if (!resetData) {
      data = cloneDeep(this.formData);
      data.conditions = data.conditions.map(({ funcArgs, funcName, funcId, funcType, name }: any) => ({
        funcArgs,
        funcName,
        funcId,
        funcType,
        name
      }));
    }
    this.$emit('change', ...[this.name, data]);
    this.display = false;
  }
}
</script>
<style lang="scss" scoped>
$marginTop: 15px;
$padding: 20px;
$tabHeight: 48px;
$borderColor: #f1f1f1;

.config {
  &-dialog {
    ::v-deep .el-dialog {
      &__body {
        padding: 0;
      }
    }
  }

  &__container {
    margin-top: 20px;
  }
  &-resultExpression {
    display: flex;
    align-items: center;
    margin: 0 auto 20px;
    width: calc(100% - 70px);
    font-size: 14px;
    font-weight: 400;
    color: #444444;
    &__main {
      width: calc(100% - 90px);
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  &-content {
    margin-top: $marginTop;
    height: 400px;
    overflow-x: hidden;
    overflow-y: auto;
  }
}
</style>
