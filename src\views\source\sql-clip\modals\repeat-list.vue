<template>
  <bs-dialog class="repeat-list" :title="$t('pa.resource.sql.detail.duplicateData')" :visible="visible" @close="closeDialog">
    <div class="repeat-list__point">{{ $t('pa.resource.sql.detail.alreadyExists') }}</div>
    <bs-table height="400px" :column-settings="false" :data="tableData" :column-data="columnData" />
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">{{ $t('pa.action.cancel') }}</el-button>
      <el-button type="primary" @click="submit('cover')"> {{ $t('pa.action.cover') }} </el-button>
      <el-button type="primary" @click="submit()"> {{ $t('pa.action.skip') }} </el-button>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { importSqlList } from '@/apis/sqlApi';
@Component
export default class ImportModal extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: [] }) repeatData!: any;
  @Prop({ default: [] }) noRepeatData!: any;
  @Watch('repeatData', { immediate: true })
  handData() {
    this.tableData = this.repeatData;
  }
  tableLoading = false;
  tableData: any = [];

  columnData = [
    {
      label: this.$t('pa.resource.sql.detail.partName'),
      value: 'name'
    },
    {
      label: this.$t('pa.resource.sql.detail.partAcronym'),
      value: 'shortCode'
    }
  ];

  closeDialog(needFresh = false) {
    this.$emit('close', needFresh);
  }
  async submit(val) {
    const param = val === 'cover' ? [...this.repeatData, ...this.noRepeatData] : this.noRepeatData;
    const { success, msg } = await importSqlList(param);
    if (success) {
      this.closeDialog();
    } else {
      this.$message.error(msg);
    }
  }
}
</script>

<style lang="scss" scoped>
.repeat-list {
  ::v-deep .bs-dialog .el-dialog__body {
    padding: 0;
  }
  &__point {
    padding: 5px 20px;
  }
}
</style>
