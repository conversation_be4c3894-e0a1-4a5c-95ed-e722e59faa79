<template>
  <div></div>
</template>
<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
@Component({ name: 'Refresh' })
export default class Refresh extends Vue {
  private beforeRouteEnter(to: any, from: any, next: any) {
    next((vm: Vue) => {
      from.meta.isReplaced = true;
      vm.$router
        .replace({
          name: from.name,
          query: { ...to.query },
          params: to.params
        })
        .then(() => {
          from.meta.isReplaced = false;
        });
    });
  }
}
</script>
