<!-- >保存提示弹窗<-->
<template>
  <bs-dialog
    :title="title"
    size="medium"
    class="verify-dd-dialog"
    :visible.sync="display"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @confirm="handleConfirm"
  >
    <div>
      <p style="margin-bottom: 16px">{{ flowDataSetChange }}</p>
      <span>
        {{ basisChange }}
      </span>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { validateDd } from '@/apis/flowNewApi';
import { safeArray } from '@/utils';
import i18n from '@/i18n';

@Component
export default class VerifyDd extends Vue {
  display = false;
  content = '';
  title = i18n.t('pa.prompt') as string;
  resolve: any = null;
  reject: any = null;
  basisChange = i18n.t('pa.basisChange') as string;

  get flowDataSetChange() {
    return i18n.t('pa.flowDataSetChange', [this.content]) as string;
  }

  async show(idList: string[] = []) {
    const { success, data, error } = await validateDd(idList);
    if (!success) return this.$tip.error(error);
    if (safeArray(data).length < 1) return true;
    return new Promise((resolve, reject) => {
      this.content = safeArray(data)
        .map((it) => `【${it}】`)
        .join('、');
      this.display = true;
      this.resolve = resolve;
      this.reject = reject;
    });
  }
  closeDialog(isCancel = true) {
    if (isCancel) {
      this.reject && this.reject.bind(Promise)(new Error(this.$t('pa.userCancel')));
    }
    this.display = false;
    this.content = '';
    this.resolve = null;
    this.reject = null;
  }
  handleConfirm() {
    this.resolve && this.resolve.bind(Promise)(true);
    this.closeDialog(false);
  }
}
</script>
<style lang="scss" scoped>
.verify-dd-dialog {
  ::v-deep .el-dialog__body {
    display: flex;
    align-items: center;
    min-height: unset;
  }
}
</style>
