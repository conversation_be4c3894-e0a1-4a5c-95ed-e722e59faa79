<template>
  <div :class="generateClass('arrowLine__container')">
    <template v-if="type === 'right'">
      <!-- 箭头 -->
      <div v-if="showArrow" :class="generateClass('arrowLine-arrow')"></div>
      <!-- 线 -->
      <div :class="generateClass('arrowLine-line')"></div>
    </template>
    <template v-else>
      <!-- 线 -->
      <div :class="generateClass('arrowLine-line')"></div>
      <!-- 箭头 -->
      <div v-if="showArrow" :class="generateClass('arrowLine-arrow')"></div>
    </template>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class ArrowLine extends Vue {
  @Prop({ default: 'down' }) type!: string;
  @Prop({ type: Boolean, default: false }) showArrow!: boolean;

  generateClass(prefix: string) {
    return [prefix, `${prefix}--${this.type}`, this.showArrow ? `${prefix}--arrow` : ''];
  }
}
</script>
<style lang="scss" scoped>
$borderWidth: 6px;
.arrowLine {
  &__container {
    display: flex;
    align-items: center;
    position: relative;
    background: pink;
    width: 35px;
    height: 1px;
    &--bottom {
      display: block;
      width: 1px;
      height: 35px;
    }
  }
  /* 线 */
  &-line {
    width: 35px;
    height: 1px;
    background: #7f8081;
    &--bottom {
      display: block;
      width: 1px;
      height: 35px;
    }
    &--arrow {
      // width: calc(100% - 10px);
    }
  }
  /* 箭头 */
  &-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border: $borderWidth solid;
    border-color: transparent;
    // background: yellow;
    &--left {
      right: 0;

      transform: rotate(-90deg);
    }
    &--right {
      left: 0;
      transform: rotate(90deg);
    }
    &--up {
      transform: rotate(-180deg);
    }
    &--bottom {
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    &::after {
      content: '';
      position: absolute;
      // top: 0;
      // // top: -55px;
      left: -$borderWidth;
      border: $borderWidth solid;
      border-color: #7f8081 transparent transparent transparent;
    }
  }
}
</style>
