<template>
  <pro-grid type="info" :title="$t('pa.data.table.detail.tableInfo')" class="table-Info__container">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRule"
      :label-width="isEn ? '130px' : '70px'"
      :label-position="'right'"
    >
      <!-- 表名 -->
      <el-form-item :label="$t('pa.data.table.detail.tableName')" prop="tableName">
        <!-- 前缀 -->
        <bs-select
          v-model="formData.tableNamePrefix"
          clearable
          filterable
          :disabled="disabled"
          :options="prefixList"
          :placeholder="$t('pa.data.table.detail.placeholder.tablePlaceholder1')"
        />
        <!-- 表名 -->
        <el-input
          v-model="formData.tableName"
          :maxlength="70"
          :disabled="disabled"
          :placeholder="$t('pa.data.table.detail.placeholder.tablePlaceholder2')"
          class="table-Info-input"
          @input="handleInput"
        />
        <!-- 后缀 -->
        <bs-select
          v-model="formData.tableNameSuffix"
          :options="suffixList"
          :placeholder="$t('pa.data.table.detail.placeholder.tablePlaceholder3')"
          :disabled="disabled"
          filterable
          clearable
        />
      </el-form-item>
      <!-- 中文名 -->
      <el-form-item :label="$t('pa.data.table.detail.chineseName')" prop="tableNameCn">
        <el-input v-model="formData.tableNameCn" maxlength="20" />
      </el-form-item>
      <!-- 业务口径 -->
      <el-form-item :label="$t('pa.data.table.detail.businessCaliber')" prop="businessExplain">
        <el-input
          v-model="formData.businessExplain"
          type="textarea"
          row="5"
          :placeholder="$t('pa.data.table.detail.placeholder.tablePlaceholder4')"
          maxlength="50"
        />
      </el-form-item>
    </el-form>
  </pro-grid>
</template>

<script lang="ts">
import { Component, Prop, Ref, Vue } from 'vue-property-decorator';
import { getPrefixList, getSuffixList } from '@/apis/dataApi';
import ElForm from 'bs-ui-pro/packages/form/index.js';
import { cloneDeep, safeArray } from '@/utils';

@Component
export default class TableInfo extends Vue {
  @Prop({ default: () => null }) data!: any;
  @Prop({ default: '' }) status!: string;
  @Ref('formRef') readonly form!: ElForm;

  formData: any = {
    tableNamePrefix: '',
    tableName: '',
    tableNameSuffix: '',
    tableNameCn: '',
    businessExplain: ''
  };
  formRule: any = {
    tableName: { required: true, message: this.$t('pa.data.table.detail.placeholder.tablePlaceholder2'), trigger: 'blur' }
  };
  prefixList: any[] = [];
  suffixList: any[] = [];

  get disabled() {
    return this.status === 'edit';
  }

  created() {
    this.getPrefixList();
    this.getSuffixList();
  }

  async getPrefixList() {
    const { success, data, error } = await getPrefixList();
    if (!success) return this.$message.error(error);
    this.prefixList = safeArray(data).map((value) => ({ value, label: value }));
  }
  async getSuffixList() {
    const { success, data, error } = await getSuffixList();
    if (!success) return this.$message.error(error);
    this.suffixList = safeArray(data).map((value) => ({ value, label: value }));
  }
  handleInput(tableName: string) {
    this.formData.tableName = tableName.replace(/[^\w\d]/g, '');
  }
  public init() {
    if (!this.data) return;
    this.formData.tableNamePrefix = this.data?.tableNamePrefix || '';
    this.formData.tableNameSuffix = this.data?.tableNameSuffix || '';
    this.formData.tableNameCn = this.data?.tableNameCn || '';
    this.formData.businessExplain = this.data?.businessExplain || '';
    let tableName = this.data?.tableName || '';
    if (this.formData.tableNamePrefix && tableName.includes(this.formData.tableNamePrefix)) {
      tableName = tableName.replace(this.formData.tableNamePrefix, '');
    }
    if (this.formData.tableNameSuffix && tableName.includes(this.formData.tableNameSuffix)) {
      const reg = new RegExp(`(.*)${this.formData.tableNameSuffix}`);
      tableName = tableName.replace(reg, '$1');
    }
    this.formData.tableName = tableName;
  }
  public async validate() {
    try {
      await this.form.validate();
      const result = cloneDeep(this.formData);
      result.tableName = `${result.tableNamePrefix}${result.tableName}${result.tableNameSuffix}`;
      return result;
    } catch (e) {
      throw e;
    }
  }
}
</script>

<style lang="scss" scoped>
.table-Info {
  &__container {
    margin-bottom: 20px;
    .el-form {
      padding: 20px 25px;
    }
  }
  &-input {
    margin: 0 10px;
    width: 193px;
  }
}
</style>
