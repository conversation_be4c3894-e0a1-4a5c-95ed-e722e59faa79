import { Component, Prop, Inject } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
@Component
export default class VirtualTreeItemContent extends PaBase {
  @Prop({}) node!: any;
  @Inject('sourceTreeMaps') sourceTreeMaps;
  sourceNode: any = {};
  created() {
    this.sourceNode = this.sourceTreeMaps.get(this.node.id);
  }
  render() {
    const parent: any = this.$parent;
    return parent.$scopedSlots.default
      ? parent.$scopedSlots.default({ id: this.node.id, data: this.sourceNode })
      : null;
  }
}
