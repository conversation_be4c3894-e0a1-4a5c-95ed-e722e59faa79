<template>
  <bs-table
    style="border-top: 1px solid #ebeef5"
    border
    size="mini"
    row-key="id"
    height="390"
    paging-front
    show-index
    index-width="50px"
    :data="tableData"
    :page-data="pageData"
    :selection="!disabled"
    :crossing="!disabled"
    :column-settings="false"
    :column-data="columnData"
    :checked-rows="checkedData"
    :show-multiple-selection="false"
    :cell-class-name="handleCellClassName"
    @page-change="pageChange"
    @selection-change="selectionChange"
  >
    <!-- 选择全部 -->
    <template slot="footer-expand">
      <el-checkbox v-if="!disabled" v-model="cheackAll" @change="handleCheckedAll">
        选择全部
      </el-checkbox>
      <span class="config-table__total">（已选择{{ checkedData.length }}项）</span>
    </template>
    <template slot="name" slot-scope="{ row }">
      <el-input
        v-model="row.name"
        minlength="1"
        maxlength="30"
        placeholder="请输入"
        :disabled="disabled"
      />
    </template>
    <template slot="mvel" slot-scope="{ row }">
      <div v-if="getShowData(row)" class="show-content" v-html="getShowData(row)"></div>
      <div v-else class="show-placeholder">请配置规则表达式</div>
    </template>
    <template slot="operate" slot-scope="{ $index, row }">
      <el-button
        v-for="el in operateList"
        :key="el.name"
        type="text"
        :disabled="disabled"
        @click="handleClick(el.name, row)"
      >
        {{ el.label }}
      </el-button>
    </template>
  </bs-table>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Watch, Vue } from 'vue-property-decorator';
import debounce from 'lodash/debounce';

@Component
export default class MonitorRuleTestDialog extends Vue {
  @Prop({ default: false }) disabled!: boolean;
  @PropSync('data', { default: () => [] }) tableData!: any[];

  private columnData = [
    {
      label: '监控规则名称',
      value: 'name',
      width: 210,
      showOverflowTooltip: false
    },
    {
      label: '规则表达式',
      value: 'mvel'
    },
    {
      label: '操作',
      value: 'operate',
      width: 140,
      showOverflowTooltip: false
    }
  ];
  private operateList: any[] = [
    {
      label: '配置',
      name: 'edit'
    },
    {
      label: '测试',
      name: 'test'
    },
    {
      label: '删除',
      name: 'delete'
    }
  ];
  private pageData = {
    pageSize: 10,
    currentPage: 1,
    total: 0,
    layout: 'total,prev,pager,next'
  };
  private cheackAll = false;
  private selectionChange = debounce(this.handleSelectionChange, 500);

  get checkedData() {
    return this.tableData.filter((el) => el.used);
  }

  @Watch('tableData', { deep: true, immediate: true })
  handleTableDataChange(val: any[]) {
    if (!Array.isArray(val)) return;
    this.pageData.total = this.tableData.length;
    this.cheackAll = this.checkedData.length === this.tableData.length;
  }
  handleCellClassName({ columnIndex }) {
    return columnIndex === 1 ? 'used-cell' : '';
  }
  handelCommandChange(value: boolean) {
    for (const i in this.tableData) {
      this.$set(this.tableData[i], 'used', value);
    }
  }
  getShowData(row: any = {}) {
    return typeof row.mvel === 'string' ? row.mvel : row.mvel.resultExpression;
  }
  pageChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
  }
  handleClick(...args) {
    this.$emit('click', ...args);
  }
  /* 处理表格全选事件 */
  handleCheckedAll(isChecked) {
    this.tableData.forEach((el) => {
      this.$set(el, 'used', isChecked);
    });
  }
  /* 处理表格选中事件 */
  handleSelectionChange(data: any[]) {
    this.cheackAll = data.length === this.tableData.length;
    const idList = data.map(({ id }) => id);
    this.tableData.forEach((el) => this.$set(el, 'used', idList.includes(el.id)));
  }
  goLastPage() {
    this.pageData.total = this.tableData.length;
    this.pageData.currentPage = Math.ceil(this.pageData.total / this.pageData.pageSize);
  }

  handleCurrentPage() {
    this.pageData.total = this.tableData.length;
    const total = Math.ceil(this.pageData.total / this.pageData.pageSize);
    this.pageData.currentPage =
      this.pageData.currentPage < total ? this.pageData.currentPage : total;
  }
}
</script>

<style lang="scss" scoped>
::v-deep .bs-table {
  &-footer-slot {
    padding-left: 0;
  }
  &__index {
    .cell {
      padding: 0;
      text-align: center;
    }
  }
}
::v-deep .used-cell {
  .cell {
    text-align: center;
  }
}
.config-table {
  &__total {
    font-weight: bolder;
    letter-spacing: 1px;
  }
}
.show {
  &-content {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    text-align: left;
  }
  &-placeholder {
    color: #c0c4cc;
    text-align: left;
  }
}
</style>
