<template>
  <bs-table
    style="border-top: 1px solid #ebeef5"
    border
    size="mini"
    row-key="id"
    height="390"
    paging-front
    show-index
    index-width="50px"
    :data="tableData"
    :page-data="pageData"
    :selection="!disabled"
    :crossing="!disabled"
    :column-settings="false"
    :column-data="columnData"
    :checked-rows="checkedData"
    :show-multiple-selection="false"
    :cell-class-name="handleCellClassName"
    @page-change="pageChange"
    @selection-change="selectionChange"
  >
    <!-- 选择全部 -->
    <template slot="footer-expand">
      <el-checkbox v-if="!disabled" v-model="cheackAll" @change="handleCheckedAll">
        {{ $t('pa.flow.selectAll1') }}
      </el-checkbox>
      <span class="config-table__total">{{ $t('pa.flow.msg123', [checkedData.length]) }}</span>
    </template>
    <template slot="name" slot-scope="{ row }">
      <el-input
        v-model="row.name"
        minlength="1"
        maxlength="30"
        :placeholder="$t('pa.flow.placeholder0')"
        :disabled="disabled"
      />
    </template>
    <template slot="mvel" slot-scope="{ row }">
      <div v-if="getShowData(row)" class="show-content" v-html="getShowData(row)"></div>
      <div v-else class="show-placeholder">{{ $t('pa.flow.msg124') }}</div>
    </template>
    <template slot="operate" slot-scope="{ $index, row }">
      <el-button
        v-for="el in operateList"
        :key="el.name"
        type="text"
        :disabled="disabled"
        @click="handleClick(el.name, row)"
      >
        {{ el.label }}
      </el-button>
    </template>
  </bs-table>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Watch, Vue } from 'vue-property-decorator';
import debounce from 'lodash/debounce';

@Component
export default class MonitorRuleTestDialog extends Vue {
  @Prop({ default: false }) disabled!: boolean;
  @PropSync('data', { default: () => [] }) tableData!: any[];

  private columnData = [
    {
      label: this.$t('pa.flow.monitorRule'),
      value: 'name',
      width: 210,
      showOverflowTooltip: false
    },
    {
      label: this.$t('pa.flow.ruleStr'),
      value: 'mvel'
    },
    {
      label: this.$t('pa.action.action'),
      value: 'operate',
      width: this.isEn ? 250 : 140,
      showOverflowTooltip: false
    }
  ];
  private operateList: any[] = [
    {
      label: this.$t('pa.flow.config1'),
      name: 'edit'
    },
    {
      label: this.$t('pa.flow.test'),
      name: 'test'
    },
    {
      label: this.$t('pa.flow.del'),
      name: 'delete'
    }
  ];
  private pageData = {
    pageSize: 10,
    currentPage: 1,
    total: 0,
    layout: 'total,prev,pager,next'
  };
  private cheackAll = false;
  private selectionChange = debounce(this.handleSelectionChange, 500);

  get checkedData() {
    return this.tableData.filter((el) => el.used);
  }

  @Watch('tableData', { deep: true, immediate: true })
  handleTableDataChange(val: any[]) {
    if (!Array.isArray(val)) return;
    this.pageData.total = this.tableData.length;
    this.cheackAll = this.checkedData.length === this.tableData.length;
  }
  handleCellClassName({ columnIndex }) {
    return columnIndex === 1 ? 'used-cell' : '';
  }
  handelCommandChange(value: boolean) {
    for (const i in this.tableData) {
      this.$set(this.tableData[i], 'used', value);
    }
  }
  getShowData(row: any = {}) {
    return typeof row.mvel === 'string' ? row.mvel : row.mvel.resultExpression;
  }
  pageChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
  }
  handleClick(...args) {
    this.$emit('click', ...args);
  }
  /* 处理表格全选事件 */
  handleCheckedAll(isChecked) {
    this.tableData.forEach((el) => {
      this.$set(el, 'used', isChecked);
    });
  }
  /* 处理表格选中事件 */
  handleSelectionChange(data: any[]) {
    this.cheackAll = data.length === this.tableData.length;
    const idList = data.map(({ id }) => id);
    this.tableData.forEach((el) => this.$set(el, 'used', idList.includes(el.id)));
  }
  goLastPage() {
    this.pageData.total = this.tableData.length;
    this.pageData.currentPage = Math.ceil(this.pageData.total / this.pageData.pageSize);
  }

  handleCurrentPage() {
    this.pageData.total = this.tableData.length;
    const total = Math.ceil(this.pageData.total / this.pageData.pageSize);
    this.pageData.currentPage = this.pageData.currentPage < total ? this.pageData.currentPage : total;
  }
}
</script>

<style lang="scss" scoped>
::v-deep .bs-table {
  &-footer-slot {
    padding-left: 0;
  }
  &__index {
    .cell {
      padding: 0;
      text-align: center;
    }
  }
}
::v-deep .used-cell {
  .cell {
    text-align: center;
  }
}
.config-table {
  &__total {
    font-weight: bolder;
    letter-spacing: 1px;
  }
}
.show {
  &-content {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    text-align: left;
  }
  &-placeholder {
    color: #c0c4cc;
    text-align: left;
  }
}
</style>
