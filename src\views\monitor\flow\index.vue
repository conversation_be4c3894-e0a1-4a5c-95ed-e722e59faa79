<template>
  <pro-page title="流程监控" :fixed-header="false">
    <div slot="operation" class="operate-box">
      <el-select v-model="runStatus" placeholder="请选择运行状态" clearable @change="handleSearch">
        <el-option
          v-for="item in statusList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <bs-search
        v-model="searchObj.search.name"
        placeholder="请输入项目、流程名称"
        style="width: 210px; margin-left: 10px"
        maxlength="30"
        @search="handleSearch"
      />
      <template v-for="el in btnList">
        <el-dropdown
          v-if="el.type === 'ElDropdown'"
          :key="el.label"
          @command="headerOperateHandler($event, el.event)"
        >
          <el-button style="margin-left: 10px" type="primary">
            {{ el.label }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in el.options"
              :key="item.command"
              :command="item.command"
            >
              {{ item.text }}
              <el-tooltip effect="light" placement="top" :content="item.content">
                <i :class="item.icon"></i>
              </el-tooltip>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          v-if="el.type === 'ElButton'"
          :key="el.label"
          style="margin-left: 10px"
          type="primary"
          @click="headerOperateHandler('', el.event)"
        >
          {{ el.label }}
        </el-button>
      </template>
    </div>
    <div class="info">
      <div class="table-header">
        <span> <i>全部流程：</i>{{ allFlowNum }} </span>
        <span> <i>未运行流程：</i>{{ jobStatusData.NONE || '0' }} </span>
        <span> <i>运行流程：</i>{{ jobStatusData.RUNNING || '0' }} </span>
        <span> <i>失败流程：</i>{{ jobStatusData.FAILED || '0' }} </span>
        <span> <i>完成流程：</i>{{ jobStatusData.FINISHED || '0' }} </span>
        <span> <i>未知流程：</i>{{ jobStatusData.UNKNOWN || '0' }} </span>
        <span> <i>KILLED流程：</i>{{ jobStatusData.KILLED || '0' }} </span>
      </div>
    </div>
    <!-- table -->
    <div style="height: calc(100% - 76px)">
      <bs-table
        v-loading="tableLoading"
        crossing
        ref="tableRef"
        selection
        row-key="id"
        :height="height"
        :data="tableData.tableData"
        :checked-rows="selectedList"
        :page-data="searchObj.pageData"
        :column-data="tableData.columnData"
        @refresh="getListData"
        @page-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
      >
        <template slot="jobName" slot-scope="{ row }">
          <a href="javascript:void(0);" @click="toFlowCanvas(row)">{{ row.jobName }}</a>
        </template>
        <template slot="jobRunTimeStatus" slot-scope="{ row }">
          <span :style="{ color: jobRunTimeStatusEnumColor[row.jobRunTimeStatus] }">{{
            jobRunTimeStatusEnumData[row.jobRunTimeStatus]
          }}</span>
        </template>
        <template slot="webUrl" slot-scope="{ row }">
          <a :href="row.webUrl" target="_blank">{{ row.webUrl }}</a>
        </template>
        <template slot="operator" slot-scope="{ row }">
          <template v-for="el in tableOpeList">
            <el-tooltip
              v-if="el.type === 'ElTooltip'"
              :key="el.label"
              :content="el.label"
              effect="light"
            >
              <i :class="el.icon" @click="operSingleHandler(el.state, el.event, row)"></i>
            </el-tooltip>
            <el-dropdown
              v-if="el.type === 'ElDropdown'"
              :key="el.label"
              @command="operSingleHandler($event, el.event, row)"
            >
              <i :class="el.icon" style="margin: 0 10px"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="item in el.options"
                  :key="item.command"
                  :command="item.command"
                >
                  {{ item.text }}
                  <el-tooltip effect="light" placement="top" :content="item.content">
                    <i :class="item.icon"></i>
                  </el-tooltip>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </template>
      </bs-table>
    </div>
    <!-- 批量流程配置 -->
    <batch-online-dialog
      v-if="showBatchFlowConfig"
      :show.sync="showBatchFlowConfig"
      :status="statusFlag"
      :list="selectedList"
      @close="closeDialog"
    />
    <!-- 流程配置 -->
    <resource-single-dialog
      v-if="showSingleFlowConfig"
      :show.sync="showSingleFlowConfig"
      :status="statusFlag"
      :is-monitor="true"
      :project-id="activeJob.projectId"
      :flow-id="activeJob.jobId"
      @close="closeDialog"
    />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { get, post } from '@/apis/utils/net';
import * as _ from 'lodash';
import {
  URL_JOB_MONITOR_LIST,
  URL_JOB_OFFLINE,
  URL_JOB_RESTART,
  URL_JOB_STATUS,
  URL_JOB_STATUS_LIST
} from '@/apis/commonApi';
import moment from 'moment';
import { hasPermission } from '@/utils';
@Component({
  name: 'MonitorFlow',
  components: {
    'batch-online-dialog': () => import('@/components/batch-online-dialog/index.vue'),
    'resource-single-dialog': () =>
      import('@/components/single-flow-config/resource-single-dialog.vue')
  }
})
export default class MonitorFlow extends Vue {
  // 各状态流程汇总信息
  jobStatusData: any = {};
  activeJob: any = {};
  // 流程状态
  runStatus = 'FAILED';
  private timer: any;
  searchObj: ISearchObj = {
    search: {
      name: '',
      runtimeStatus: ''
    },
    pageData: { pageSize: 20, currentPage: 1, total: 1 },
    sortData: {}
  };
  tableData: ITableData = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  tableLoading = false;
  // 流程状态列表
  statusList: any = [];
  selectedList: any[] = [];
  // 轮询请求接口间隔
  t = 60000;
  jobRunTimeStatusEnumColor = {};
  jobRunTimeStatusEnumData = {};
  statusFlag = false; // 是否基于上次状态启动
  // 控制单个流程配置的弹窗
  showSingleFlowConfig = false;
  // 控制流程批量配置的弹窗
  showBatchFlowConfig = false;

  // 全部流程数量
  get allFlowNum() {
    const { jobStatusData } = this;
    return Object.values(jobStatusData).reduce(
      (res: number, val: any) => res + Number(val || 0),
      0
    );
  }
  get btnList() {
    // 权限过滤
    return [
      {
        type: 'ElDropdown',
        label: '启动',
        event: 'handleBatchOnline',
        access: 'PA.MONITOR.FLOW.ON',
        options: [
          {
            content: '流程首次启动，或者修改kafka consumer group等信息后需要从头开始消费数据。',
            icon: 'el-icon-warning-outline',
            command: false,
            text: '无状态启动'
          },
          {
            content:
              '流程重启，需要接着上次checkpoint记录的位置，如kafka上次的offset位置继续消费。',
            icon: 'el-icon-warning-outline',
            command: true,
            text: '基于上次状态启动'
          }
        ]
      },
      {
        type: 'ElDropdown',
        label: '停止',
        event: 'offline',
        access: 'PA.MONITOR.FLOW.OFF',
        options: [
          {
            command: false,
            text: '停止'
          },
          {
            content: '即flink savepoint，用于暂停流程，流程重新启动，保证精准一次语义。',
            icon: 'el-icon-warning-outline',
            command: true,
            text: '停止并保留状态'
          }
        ]
      },
      {
        type: 'ElButton',
        label: '强制停止',
        event: 'offlineForce',
        access: 'PA.MONITOR.FLOW.FORCE_OFF'
      },
      {
        type: 'ElButton',
        label: '一键重启',
        event: 'restartForce',
        access: 'PA.MONITOR.FLOW.RESTART'
      }
    ].filter((item) => hasPermission(item.access));
  }

  get tableOpeList() {
    return [
      {
        type: 'ElTooltip',
        label: '详情',
        icon: 'iconfont icon-chakan',
        event: 'openDetail',
        access: 'PA.MONITOR.FLOW.FORCE_OFF'
      },
      {
        type: 'ElDropdown',
        label: '启动',
        icon: 'iconfont icon-qidong',
        event: 'handleSingleOnline',
        access: 'PA.MONITOR.FLOW.ON',
        options: [
          {
            content: '流程首次启动，或者修改kafka consumer group等信息后需要从头开始消费数据。',
            icon: 'el-icon-warning-outline',
            command: false,
            text: '无状态启动'
          },
          {
            content:
              '流程重启，需要接着上次checkpoint记录的位置，如kafka上次的offset位置继续消费。',
            icon: 'el-icon-warning-outline',
            command: true,
            text: '基于上次状态启动'
          }
        ]
      },
      {
        type: 'ElTooltip',
        label: '停止',
        icon: 'iconfont icon-guanbi',
        event: 'stop',
        state: false,
        access: 'PA.MONITOR.FLOW.OFF'
      },
      {
        type: 'ElTooltip',
        label: '停止并保留状态',
        icon: 'iconfont icon-guanbi',
        event: 'stop',
        state: true,
        access: 'PA.MONITOR.FLOW.OFF'
      }
    ].filter((item) => hasPermission(item.access));
  }
  get height() {
    return this.selectedList.length ? 'calc(100vh - 417px)' : 'calc(100vh - 360px)';
  }

  created() {
    if (this.$route.params.runStatus) {
      this.runStatus = this.$route.params.runStatus;
    }
    if (this.$route.query.name) {
      this.searchObj.search.name = this.$route.query.name;
      this.runStatus = this.$route.query.runStatus as string;
    }
    this.getStatusList();
    this.getListData();
    this.getJobStatusData();
  }

  destroyed() {
    clearTimeout(this.timer);
  }

  handleSearch() {
    this.searchObj.pageData.currentPage = 1;
    this.getListData();
  }

  headerOperateHandler(state: string, event: string) {
    event && this[event](state);
  }

  operSingleHandler(state: string, event: string, row) {
    event && this[event](row, state);
  }

  toFlowCanvas({ projectId, projectName, jobId, jobName }) {
    if (
      (this as any).$tabsNav
        .getAllTabs()
        .find((item) => item.title === jobName && item.value.split('flowId=')[1] === jobId)
    ) {
      const value = (this as any).$tabsNav
        .getAllTabs()
        .find((item) => item.title === jobName && item.value.split('flowId=')[1] === jobId).value;
      localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId: jobId }));
      this.$router.push({
        path: value
      });
    } else {
      this.$router.push({
        path: '/flow',
        query: {
          id: projectId,
          name: projectName,
          title: jobName,
          state: 'ALL',
          flowId: jobId
        }
      });
    }
  }
  // 跳转到详情
  openDetail(row) {
    const pathStr = `/monitor/flow/detail?flowId=${row.jobId}`;
    this.$router.push({
      path: pathStr,
      query: {
        title: row.jobName
      }
    });
  }

  handleSingleOnline(row, state) {
    this.activeJob = row;
    this.statusFlag = state;
    if (state) {
      this.showSingleFlowConfig = true;
    } else {
      // 无状态启动 进行提示
      this.$confirm('确定无状态启动吗？', '提示', {
        type: 'warning'
      })
        .then(() => {
          this.showSingleFlowConfig = true;
        })
        .catch(() => false);
    }
  }
  // 流程停止
  stop(row, savepoint) {
    this.tableLoading = true;
    Vue.axios
      .post(URL_JOB_OFFLINE + '?force=false', [{ jobId: row.jobId, savepoint }])
      .then((resp: any) => {
        if (resp.success) {
          this.$message.success(resp.msg);
          this.getListData();
          this.getJobStatusData();
        } else {
          this.$message.error(resp.msg);
        }
        this.tableLoading = false;
      })
      .catch(() => {
        this.tableLoading = false;
      });
  }

  handleCurrentChange(currentPage, pageSize) {
    this.searchObj.pageData.currentPage = currentPage;
    this.searchObj.pageData.pageSize = pageSize;
    this.getListData();
  }

  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.getListData();
  }
  fetchList() {
    this.getListData();
  }
  async getListData() {
    this.tableLoading = true;
    const searchObj: any = _.cloneDeep(this.searchObj);
    searchObj.search.name = searchObj.search.name.trim();
    searchObj.search.runtimeStatus = this.runStatus;
    const { success, data, msg, error } = await post(URL_JOB_MONITOR_LIST, searchObj);
    if (success) {
      const { tableData, columnData, pageData } = data;
      columnData.forEach((el) => {
        if (el.prop === 'jobRunTimeStatus') {
          this.jobRunTimeStatusEnumColor = el.enumColor;
          this.jobRunTimeStatusEnumData = el.enumData;
        }
        el.value = el.prop;
      });
      columnData.push({ label: '操作', value: 'operator', width: 140, fixed: 'right' });
      tableData.forEach((el) => {
        el.id = el.jobId;
        el.onlineTime = moment(el.onlineTime).format('YYYY-MM-DD HH:mm:ss');
        el.offlineTime = moment(el.offlineTime).format('YYYY-MM-DD HH:mm:ss');
      });
      this.searchObj.pageData = pageData;
      //表格数据变化选中数据也需变化
      if (this.selectedList.length) {
        for (const i in this.selectedList) {
          const target: any = tableData.find(({ jobId }) => jobId === this.selectedList[i]?.jobId);
          if (target) {
            this.selectedList[i] = { ...target, id: this.selectedList[i].id };
          }
        }
      }
      this.tableData = {
        columnData,
        tableData
      };
      clearTimeout(this.timer);
      this.timer = setTimeout(() => this.updateStatus(), this.t);
    } else {
      this.$message.error(msg || error);
    }
    this.tableLoading = false;
    this.$refs.tableRef?.doLayout();
  }

  async updateStatus() {
    const searchObj: any = _.cloneDeep(this.searchObj);
    searchObj.search.runtimeStatus = this.runStatus;
    const { data } = await post(URL_JOB_MONITOR_LIST, searchObj);
    this.tableData.tableData.forEach((item, index) => {
      this.$set(
        item,
        'jobRunTimeStatus',
        data.tableData[index] ? data.tableData[index].jobRunTimeStatus : ''
      );
    });
    // this.updateStatus();
    clearTimeout(this.timer);
    this.timer = setTimeout(() => this.updateStatus(), this.t);
  }

  //启动流程
  handleBatchOnline(val) {
    if (this.selectedList.length > 0) {
      this.statusFlag = val;
      if (val) {
        this.showBatchFlowConfig = true;
      } else {
        // 无状态启动 进行提示
        this.$confirm('确定无状态启动吗？', '提示', {
          type: 'warning'
        })
          .then(() => {
            this.showBatchFlowConfig = true;
          })
          .catch(() => false);
      }
    } else {
      this.$tip.warning('请选择要启动的流程');
    }
  }

  // 批量流程停止
  async offline(command) {
    const params: any = [];
    this.selectedList.forEach((n: any) => {
      params.push({
        jobId: n.jobId,
        savepoint: command
      });
    });
    if (params.length === 0) {
      this.$message.warning('请选择记录');
      return;
    }
    this.tableLoading = true;
    const { success, msg, error } = await post(URL_JOB_OFFLINE + '?force=false', params);
    if (success) {
      this.getListData();
      this.getJobStatusData();
    } else {
      this.$message.error(msg || error);
    }
    this.tableLoading = false;
  }

  // 强制停止
  async offlineForce() {
    const params: any = [];
    this.selectedList.forEach((n: any) => {
      params.push({
        jobId: n.jobId,
        savepoint: false
      });
    });
    if (params.length === 0) {
      this.$message.warning('请选择记录');
      return;
    }
    this.tableLoading = true;
    const url = URL_JOB_OFFLINE + '?force=true';
    const { success, msg, error } = await post(url, params);
    if (success) {
      this.getListData();
      this.getJobStatusData();
      this.$message.success(msg);
    } else {
      this.$message.error(msg || error);
    }
    this.tableLoading = false;
  }
  // 一键重启
  async restartForce() {
    const params: any = [];
    this.selectedList.forEach((n: any) => {
      params.push({
        jobId: n.jobId,
        savepoint: true
      });
    });
    if (params.length === 0) {
      this.$message.warning('请选择记录');
      return;
    }
    this.tableLoading = true;
    const url = URL_JOB_RESTART;
    const { success, msg, error } = await post(url, params);
    if (success) this.getListData();
    else {
      this.$message.error(msg || error);
    }
    this.tableLoading = false;
  }

  hasAuthority(hasRole: any, row: any) {
    return !_.isEmpty(_.toString(row.dataLevelType)) && row.dataLevelType !== 'PARENT' && hasRole;
  }

  // 多选回调
  handleSelectionChange(sel: any) {
    this.selectedList = sel;
  }
  // 获取流程各个状态信息
  getJobStatusData() {
    get(URL_JOB_STATUS).then((resp) => {
      this.jobStatusData = JSON.parse(resp.data);
    });
  }

  async getStatusList() {
    const { success, data, error } = await get(URL_JOB_STATUS_LIST);
    if (success) {
      const other = Object.entries(data || {}).map(([value, label]: any) => ({ label, value }));
      this.statusList = [{ label: '全部', value: 'ALL' }, ...other];
      return;
    }
    this.$message.error(error);
  }

  closeDialog() {
    this.showSingleFlowConfig = false;
    this.showBatchFlowConfig = false;
    this.getListData();
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-icon-sort {
  transform: rotate(90deg);
}
.info {
  background: #fff;
  padding: 10px 20px;
}
.table-header {
  height: 50px;
  background: #fafbfc;
  border-radius: 4px;
  border: 1px solid #f1f1f1;
  font-size: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  span {
    display: flex;
    align-items: center;
    color: #444;
    i {
      color: #777;
      font-style: normal;
    }
  }
  span::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #376eff;
    margin-right: 6px;
  }
}
.iconfont {
  cursor: pointer;
}
.iconfont + .iconfont {
  margin-left: 10px;
}
</style>
