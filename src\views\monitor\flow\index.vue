<template>
  <pro-page :fixed-header="false">
    <div slot="title">
      <span>{{ $t('pa.menu.flowMonitor') }}</span>
      <el-tooltip effect="light" placement="bottom-start">
        <div slot="content">
          <p style="margin-bottom: 8px">
            {{ $t('pa.monitor.text18') }}
          </p>
          <p v-for="tip in statusTips" :key="tip" class="content-point">
            <span class="bs-circle"></span>
            {{ tip }}
          </p>
        </div>
        <i class="iconfont icon-weizhi" style="cursor: pointer"></i>
      </el-tooltip>
    </div>
    <div slot="operation" class="operate-box">
      <bs-select
        v-model="runStatus"
        :class="['operate-box__select', isEn ? 'info-status' : '']"
        :options="statusList"
        multiple
        collapse-tags
        :placeholder="$t('pa.monitor.text21')"
        @change="handleSearch"
      />
      <bs-search
        v-model="searchObj.search.name"
        :placeholder="$t('pa.monitor.text22')"
        :class="{ 'info-search': isEn }"
        :style="{ width: isEn ? '195px' : '220px', marginLeft: '10px' }"
        maxlength="30"
        @search="handleSearch"
      />
      <template v-for="el in btnList">
        <el-dropdown v-if="el.type === 'ElDropdown'" :key="el.label" @command="headerOperateHandler($event, el.event)">
          <el-button style="margin-left: 10px" type="primary">
            {{ el.label }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in el.options" :key="item.command" :command="item.command">
              {{ item.text }}
              <el-tooltip effect="light" placement="top" :content="item.content">
                <i :class="item.icon"></i>
              </el-tooltip>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          v-if="el.type === 'ElButton'"
          :key="el.label"
          style="margin-left: 10px"
          type="primary"
          @click="headerOperateHandler('', el.event)"
        >
          {{ el.label }}
        </el-button>
      </template>
    </div>
    <div class="info">
      <div class="all-count">
        <svg-icon name="count" class="all-count__img" :style="scaleRatioStyle" />
        <span class="count-label" :style="scaleRatioStyle">{{ $t('pa.monitor.text19') }}</span>
        <span class="count-num" :style="scaleRatioStyle">{{ allFlowNum }}</span>
      </div>
      <div class="single-count">
        <div :style="scaleRatioStyle">
          <i class="iconfont icon-weiyunhang" style="color: #377cff"></i>
          <span class="count-label">{{ $t('pa.status.none') }}</span>
          <span class="count-num">{{ jobStatusData.NONE || '0' }}</span>
        </div>
        <div :style="scaleRatioStyle">
          <i class="iconfont icon-yunhangzhong" style="color: #ff9e2b"></i>
          <span class="count-label">{{ $t('pa.home.running') }}</span>
          <span class="count-num">{{ jobStatusData.RUNNING || '0' }}</span>
        </div>
        <div :style="scaleRatioStyle">
          <i class="iconfont icon-yiwancheng" style="color: #54c958"></i>
          <span class="count-label">{{ $t('pa.home.text12') }}</span>
          <span class="count-num">{{ jobStatusData.FINISHED || '0' }}</span>
        </div>
        <div :style="scaleRatioStyle">
          <i class="iconfont icon-yishibai" style="color: #ff5353"></i>
          <span class="count-label">{{ $t('pa.home.text13') }}</span>
          <span class="count-num">{{ jobStatusData.FAILED || '0' }}</span>
        </div>
        <div :style="scaleRatioStyle">
          <i class="iconfont icon-yiquxiao" style="color: #aaaaaa"></i>
          <span class="count-label">{{ $t('pa.home.text14') }}</span>
          <span class="count-num">{{ jobStatusData.CANCELED || '0' }}</span>
        </div>
        <div :style="scaleRatioStyle">
          <i class="iconfont icon-zhongqizhong" style="color: #8c6bd6"></i>
          <span class="count-label">{{ $t('pa.home.restart') }}</span>
          <span class="count-num">{{ jobStatusData.RESTARTING || '0' }}</span>
        </div>
        <div :style="scaleRatioStyle">
          <i class="iconfont icon-weizhi" style="color: #aaaaaa"></i>
          <span class="count-label">{{ $t('pa.status.unKnow') }}</span>
          <span class="count-num">{{ jobStatusData.UNKNOWN || '0' }}</span>
        </div>
      </div>
    </div>

    <div style="height: calc(100% - 76px)">
      <bs-table
        v-loading="tableLoading"
        selection
        crossing
        row-key="id"
        :height="selectedList.length ? 'calc(100vh - 412px)' : 'calc(100vh - 355px)'"
        :data="tableData.tableData"
        :column-data="tableData.columnData"
        :page-data="searchObj.pageData"
        :checked-rows="selectedList"
        @page-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
        @refresh="handleRefresh"
      >
        <template slot="jobName" slot-scope="{ row }">
          <a href="javascript:void(0);" @click="toFlowCanvas(row)">{{ row.jobName }}</a>
        </template>
        <template slot="jobRunTimeStatus" slot-scope="{ row }">
          <bs-tag :color="(runTimeStatus[row.jobRunTimeStatus] || {}).color" :border="false">
            {{ (runTimeStatus[row.jobRunTimeStatus] || {}).label }}
          </bs-tag>
        </template>
        <template slot="webUrl" slot-scope="{ row }">
          <a :href="row.webUrl" target="_blank">{{ row.webUrl }}</a>
        </template>
        <template slot="operator" slot-scope="{ row }">
          <template v-for="el in tableOpeList">
            <el-tooltip v-if="el.type === 'ElTooltip'" :key="el.label" :content="el.label" effect="light">
              <i :class="el.icon" @click="operSingleHandler(el.state, el.event, row)"></i>
            </el-tooltip>
            <el-dropdown v-if="el.type === 'ElDropdown'" :key="el.label" @command="operSingleHandler($event, el.event, row)">
              <i :class="el.icon" style="margin-left: 10px"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="item in el.options" :key="item.command" :command="item.command">
                  {{ item.text }}
                  <el-tooltip effect="light" placement="top" :content="item.content">
                    <i :class="item.icon"></i>
                  </el-tooltip>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </template>
      </bs-table>
    </div>

    <!-- 流程配置 -->
    <single-resource-config-dialog
      v-if="showSingleFlowConfig"
      :show.sync="showSingleFlowConfig"
      :project-id="activeJob.projectId"
      :flow-id="activeJob.jobId"
      :status="statusFlag"
      @close="closeDialog"
    />

    <!-- 批量操作弹框 -->
    <bs-dialog
      :visible.sync="batchOperationDialogVisible"
      :title="batchDialogTitle"
      width="500px"
      @confirm="handleBatchOperationConfirm"
      @cancel="handleBatchOperationCancel"
    >
      <el-form ref="batchForm" :model="batchForm" :rules="batchRules" label-width="120px">
        <el-form-item :label="$t('pa.flow.batchOperationName')" prop="batchName">
          <el-input
            v-model="batchForm.batchName"
            :placeholder="$t('pa.flow.batchOperationNamePlaceholder')"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item :label="$t('pa.flow.memo')" prop="memo">
          <el-input
            v-model="batchForm.memo"
            type="textarea"
            :placeholder="$t('pa.flow.memoPlaceholder')"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </bs-dialog>

    <!-- 新的资源配置弹框 -->
    <batch-resource-config-new-dialog :show.sync="resourceConfigDialogVisible" @close="handleResourceConfigClose" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { get, post } from '@/apis/utils/net';
import * as _ from 'lodash';
import { URL_JOB_MONITOR_LIST, URL_JOB_OFFLINE, URL_JOB_STATUS, URL_JOB_STATUS_LIST } from '@/apis/commonApi';
import { preOfflineFlow, saveBatch } from '@/apis/flowNewApi';
import dayjs from 'dayjs';
import { hasPermission } from '@/utils';
import '@/views/flow-new/design/flow-list/style/flow-list.scss';
import { runStatusColor } from '@/config';

@Component({
  name: 'MonitorFlow',
  components: {
    'single-resource-config-dialog': () => import('@/components/resource-config/single-resource-config-dialog.vue'),
    'batch-resource-config-new-dialog': () => import('@/components/resource-config/batch-resource-config-new-dialog.vue')
  }
})
export default class MonitorFlow extends Vue {
  // 各状态流程汇总信息
  jobStatusData: any = {};
  activeJob: any = {};
  // 流程状态
  runStatus: string[] = [];
  private timer: any;
  searchObj: ISearchObj = {
    search: {
      name: '',
      runtimeStatus: ''
    },
    pageData: { pageSize: this.$store.getters.pageSize, currentPage: 1, total: 1 },
    sortData: {}
  };
  tableData: ITableData = {
    columnData: [{ prop: '', label: '' }],
    tableData: []
  };
  tableLoading = false;
  // 流程状态列表
  statusList: any = [];
  selectedList: any[] = [];
  // 轮询请求接口间隔
  t = 60000;
  // 运行状态对应得文案及颜色
  runTimeStatus = {};
  statusFlag = false; // 是否基于上次状态启动
  // 控制单个流程配置的弹窗
  showSingleFlowConfig = false;

  // 批量操作弹框相关状态
  batchOperationDialogVisible = false;
  currentBatchAction = '';
  currentBatchParams: any = {};

  // 资源配置弹框状态
  resourceConfigDialogVisible = false;

  // 批量操作表单数据
  batchForm = {
    batchName: '',
    memo: ''
  };

  // 批量操作表单验证规则
  batchRules = {
    batchName: [
      { required: true, message: this.$t('pa.flow.batchOperationNameRequired'), trigger: 'blur' },
      { min: 1, max: 50, message: this.$t('pa.flow.batchOperationNameLength'), trigger: 'blur' }
    ]
  };

  statusTips = [
    this.$t('pa.home.text5'),
    this.$t('pa.home.text6'),
    this.$t('pa.home.text7'),
    this.$t('pa.home.text8'),
    this.$t('pa.home.text9'),
    this.$t('pa.home.text10'),
    this.$t('pa.home.text11')
  ];
  scaleRatio = 1; // 页面缩放比例
  get scaleRatioStyle() {
    return `transform: scale(${this.scaleRatio.toFixed(2)})`;
  }
  // 全部流程数量
  get allFlowNum() {
    const { jobStatusData } = this;
    return Object.values(jobStatusData).reduce((res: number, val: any) => res + Number(val || 0), 0);
  }

  // 批量操作弹框标题
  get batchDialogTitle() {
    const actionMap = {
      ONLINE: this.$t('pa.action.online'),
      OFFLINE: this.$t('pa.action.offline'),
      RESTART: this.$t('pa.monitor.flow.action.restartForce')
    };
    return actionMap[this.currentBatchAction] || this.$t('pa.flow.batchOperation');
  }

  // 生成默认批量操作名称
  generateDefaultBatchName(type: string): string {
    const now = new Date();
    const timestamp =
      now.getFullYear().toString() +
      (now.getMonth() + 1).toString().padStart(2, '0') +
      now.getDate().toString().padStart(2, '0') +
      now.getHours().toString().padStart(2, '0') +
      now.getMinutes().toString().padStart(2, '0') +
      now.getSeconds().toString().padStart(2, '0');

    const typeMap = {
      OFFLINE: this.$t('pa.flow.batchOffline'),
      RESTART: this.$t('pa.flow.batchRestart'),
      ONLINE: this.$t('pa.flow.batchOnline')
    };

    const typeName = typeMap[type] || this.$t('pa.flow.batchOperation');
    return `${typeName}_${timestamp}`;
  }
  get btnList() {
    // 权限过滤
    const btnList: any = [
      {
        type: 'ElDropdown',
        label: this.$t('pa.action.online'),
        event: 'handleBatchOnline',
        access: 'PA.MONITOR.FLOW.ON',
        options: [
          {
            content: this.$t('pa.monitor.flow.action.statelessTooltip'),
            icon: 'el-icon-warning-outline',
            command: false,
            text: this.$t('pa.monitor.flow.action.statelessOnline')
          },
          {
            content: this.$t('pa.monitor.flow.action.basedLastTooltip'),
            icon: 'el-icon-warning-outline',
            command: true,
            text: this.$t('pa.monitor.flow.action.basedLastOnline')
          }
        ]
      },
      {
        type: 'ElDropdown',
        label: this.$t('pa.action.offline'),
        event: 'offline',
        access: 'PA.MONITOR.FLOW.OFF',
        options: [
          {
            command: 'stop',
            text: this.$t('pa.action.offline')
          },
          {
            content: this.$t('pa.monitor.flow.action.retainTooltip'),
            icon: 'el-icon-warning-outline',
            command: 'retain',
            text: this.$t('pa.monitor.flow.action.retainOffline')
          },
          {
            command: 'force',
            text: this.$t('pa.monitor.flow.action.forceOffline')
          }
        ]
      },
      {
        type: 'ElButton',
        label: this.$t('pa.monitor.flow.action.restartForce'),
        event: 'restartForce',
        access: 'PA.MONITOR.FLOW.RESTART'
      }
    ].filter((item) => hasPermission(item.access));
    if (this.$store.getters.monitorUrl && hasPermission('PA.MONITOR.FLOW.RUNNING_MONITOR'))
      btnList.push({
        type: 'ElButton',
        label: this.$t('pa.monitor.text20'),
        event: 'jumpToGrafana'
      });
    return btnList;
  }

  get tableOpeList() {
    return [
      {
        type: 'ElTooltip',
        label: this.$t('pa.action.detail'),
        icon: 'iconfont icon-chakan',
        event: 'openDetail',
        access: 'PA.MONITOR.FLOW.FORCE_OFF'
      },
      {
        type: 'ElDropdown',
        label: this.$t('pa.action.online'),
        icon: 'iconfont icon-qidong',
        event: 'handleSingleOnline',
        access: 'PA.MONITOR.FLOW.ON',
        options: [
          {
            content: this.$t('pa.monitor.flow.action.statelessTooltip'),
            icon: 'el-icon-warning-outline',
            command: false,
            text: this.$t('pa.monitor.flow.action.statelessOnline')
          },
          {
            content: this.$t('pa.monitor.flow.action.basedLastTooltip'),
            icon: 'el-icon-warning-outline',
            command: true,
            text: this.$t('pa.monitor.flow.action.basedLastOnline')
          }
        ]
      },
      {
        type: 'ElDropdown',
        label: this.$t('pa.action.offline'),
        icon: 'iconfont icon-guanbi',
        event: 'stop',
        access: 'PA.MONITOR.FLOW.OFF',
        options: [
          {
            command: 'stop',
            text: this.$t('pa.action.offline')
          },
          {
            content: this.$t('pa.monitor.flow.action.retainTooltip'),
            icon: 'el-icon-warning-outline',
            command: 'retain',
            text: this.$t('pa.monitor.flow.action.retainOffline')
          },
          {
            command: 'force',
            text: this.$t('pa.monitor.flow.action.forceOffline')
          }
        ]
      }
    ].filter((item) => hasPermission(item.access));
  }

  created() {
    if (this.$route.params.runStatus) {
      this.runStatus = [this.$route.params.runStatus];
    }
    if (this.$route.query.name) {
      this.searchObj.search.name = this.$route.query.name;
      this.runStatus = [this.$route.query.runStatus as string];
    }
    this.getStatusList();
    this.getListData();
    this.getJobStatusData();
  }
  mounted() {
    this.calcScaleRatio(); // 初始化计算缩放比例
    window.addEventListener('resize', this.calcScaleRatio); // 监听窗口大小变化
  }
  destroyed() {
    clearTimeout(this.timer);
    window.removeEventListener('resize', this.calcScaleRatio); // 移除监听
  }
  calcScaleRatio() {
    const baseWidth = 1500; // 基准宽度
    const currentWidth = window.innerWidth; // 当前窗口宽度
    if (currentWidth > baseWidth) return (this.scaleRatio = 1);
    this.scaleRatio = currentWidth / baseWidth; // 计算缩放比例
  }
  handleRefresh() {
    this.getJobStatusData();
    this.getListData(true);
  }

  handleSearch() {
    this.searchObj.pageData.currentPage = 1;
    this.getListData();
  }

  headerOperateHandler(state: string, event: string) {
    event && this[event](state);
  }

  operSingleHandler(state: string, event: string, row) {
    event && this[event](row, state);
  }

  toFlowCanvas({ projectRootId, jobId, jobName }) {
    if (
      (this as any).$tabNav.getAllTabs().find((item) => item.title === jobName && item.value.split('flowId=')[1] === jobId)
    ) {
      const value = (this as any).$tabNav
        .getAllTabs()
        .find((item) => item.title === jobName && item.value.split('flowId=')[1] === jobId).value;
      localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId: jobId }));
      this.$router.push({
        path: value
      });
    } else {
      this.$router.push({
        path: '/flow',
        query: {
          id: projectRootId,
          title: jobName,
          state: 'ALL',
          flowId: jobId
        }
      });
    }
  }
  // 跳转到详情
  openDetail(row) {
    const pathStr = `/monitor/flow/detail?flowId=${row.jobId}`;
    this.$router.push({
      path: pathStr,
      query: {
        title: row.jobName
      }
    });
  }

  handleSingleOnline(row, state) {
    this.activeJob = row;
    this.statusFlag = state;
    if (state) {
      this.showSingleFlowConfig = true;
    } else {
      // 无状态启动 进行提示
      this.$confirm(this.$t('pa.monitor.flow.action.statelessConfirm'), this.$t('pa.prompt'), {
        type: 'warning'
      })
        .then(() => {
          this.showSingleFlowConfig = true;
        })
        .catch(() => false);
    }
  }
  // 流程停止
  async stop(row, savepoint) {
    if (savepoint !== 'retain') {
      const type = { stop: this.$t('pa.action.offline'), force: this.$t('pa.monitor.flow.action.forceOffline') }[savepoint];
      await this.$confirm(this.$t('pa.flow.msg44', [type, row.jobName]), this.$t('pa.prompt'), {
        type: 'warning',
        customClass: 'flow-list__confirm'
      });
    }
    this.tableLoading = true;
    try {
      const { success: preSuccess, msg: preMsg } = await preOfflineFlow([{ jobId: row.jobId }]);
      if (!preSuccess) throw preMsg;
      const {
        success,
        msg,
        data = [],
        msgType
      } = await post(URL_JOB_OFFLINE + `?force=${savepoint === 'force'}`, [
        { jobId: row.jobId, savepoint: savepoint === 'retain' }
      ]);
      this.tableLoading = false;
      if (success) {
        this.$tip.success(msg);
        this.getListData();
        this.getJobStatusData();
        return;
      }
      this.$tip.errorPro({ msgType, msg, data });
    } catch (err) {
      this.$tip.error({ message: err, duration: 5000 });
      this.tableLoading = false;
    }
  }

  handleCurrentChange(currentPage, pageSize) {
    this.searchObj.pageData.currentPage = currentPage;
    this.searchObj.pageData.pageSize = pageSize;
    this.getListData();
  }

  handleSortChange(val) {
    this.searchObj.sortData = val;
    this.getListData();
  }
  fetchList() {
    this.getListData();
  }
  async getListData(refresh = false) {
    this.tableLoading = true;
    const searchObj: any = _.cloneDeep(this.searchObj);
    searchObj.search.name = searchObj.search.name.trim();
    searchObj.search.runtimeStatus = this.runStatus;
    const { success, data } = await post(URL_JOB_MONITOR_LIST, searchObj);
    if (success) {
      const { tableData, columnData, pageData } = data;
      columnData.forEach((el) => {
        el.value = el.prop;
      });
      columnData.push({ label: this.$t('pa.action.action'), value: 'operator', width: 140, fixed: 'right' });
      tableData.forEach((el) => {
        el.id = el.jobId;
        el.onlineTime = el.onlineTime ? dayjs(el.onlineTime).format('YYYY-MM-DD HH:mm:ss') : '';
        el.offlineTime = el.offlineTime ? dayjs(el.offlineTime).format('YYYY-MM-DD HH:mm:ss') : '';
      });
      this.searchObj.pageData = pageData;
      //表格数据变化选中数据也需变化
      if (this.selectedList.length) {
        const map = tableData.reduce((res, item) => {
          res.set(item.id, item);
          return res;
        }, new Map());
        this.selectedList.forEach((item) => {
          if (map.has(item.id)) {
            Object.assign(item, map.get(item.id));
          }
        });
      }
      this.tableData = {
        columnData,
        tableData
      };
      const timer = setTimeout(() => {
        this.updateStatus();
        clearTimeout(timer);
      }, this.t);
    }
    refresh && this.$message.success(this.$t('pa.tip.refreshSuccess'));
    this.tableLoading = false;
  }

  async updateStatus() {
    const searchObj: any = _.cloneDeep(this.searchObj);
    searchObj.search.runtimeStatus = this.runStatus;
    const { data } = await post(URL_JOB_MONITOR_LIST, searchObj);
    this.tableData.tableData.forEach((item, index) => {
      this.$set(item, 'jobRunTimeStatus', data.tableData[index] ? data.tableData[index].jobRunTimeStatus : '');
    });
    this.timer = setTimeout(() => {
      this.updateStatus();
    }, this.t);
  }

  //启动流程
  handleBatchOnline(val) {
    if (this.selectedList.length === 0) return this.$tip.warning(this.$t('pa.monitor.flow.action.onlineLeast'));

    this.currentBatchAction = 'ONLINE';
    this.currentBatchParams = {
      savepoint: val // true=基于上次状态启动, false=无状态启动
    };

    // 设置默认批量操作名称
    this.batchForm.batchName = this.generateDefaultBatchName('ONLINE');
    this.batchForm.memo = '';

    if (val) {
      // 基于上次状态启动，需要资源配置
      this.batchOperationDialogVisible = true;
    } else {
      // 无状态启动 进行提示
      this.$confirm(this.$t('pa.monitor.flow.action.statelessConfirm'), this.$t('pa.prompt'), {
        type: 'warning'
      })
        .then(() => {
          this.batchOperationDialogVisible = true;
        })
        .catch(() => false);
    }
  }

  // 批量流程停止
  async offline(command) {
    if (this.selectedList.length === 0) return this.$message.warning(this.$t('pa.monitor.flow.action.leastOne'));

    if (command !== 'retain') {
      const type = { stop: this.$t('pa.action.offline'), force: this.$t('pa.monitor.flow.action.forceOffline') }[command];
      const jobNames = this.selectedList
        .slice(0, 3)
        .map((n: any) => `【${n.jobName}】`)
        .join('、');
      await this.$confirm(
        `您确定要${type}${jobNames}${this.selectedList.length > 3 ? this.$t('pa.flow.deng') : ''}${
          this.selectedList.length
        }个流程吗？`,
        this.$t('pa.prompt'),
        { type: 'warning', customClass: 'flow-list__confirm' }
      );
    }

    this.currentBatchAction = 'OFFLINE';
    this.currentBatchParams = {
      savepoint: command === 'retain', // true=停止并保留状态, false=普通停止和强制停止
      force: command === 'force' // true=强制停止, false=普通停止和保留状态停止
    };

    // 设置默认批量操作名称
    this.batchForm.batchName = this.generateDefaultBatchName('OFFLINE');
    this.batchForm.memo = '';

    this.batchOperationDialogVisible = true;
  }

  // 一键重启
  async restartForce() {
    if (this.selectedList.length === 0) {
      this.$message.warning(this.$t('pa.monitor.flow.action.leastOne'));
      return;
    }

    this.currentBatchAction = 'RESTART';
    this.currentBatchParams = {
      savepoint: true // 重启操作总是保存状态点
    };

    // 设置默认批量操作名称
    this.batchForm.batchName = this.generateDefaultBatchName('RESTART');
    this.batchForm.memo = '';

    this.batchOperationDialogVisible = true;
  }
  jumpToGrafana() {
    window.open(this.$store.getters.monitorUrl);
  }

  hasAuthority(hasRole: any, row: any) {
    return !_.isEmpty(_.toString(row.dataLevelType)) && row.dataLevelType !== 'PARENT' && hasRole;
  }

  // 多选回调
  handleSelectionChange(sel: any) {
    this.selectedList = sel;
  }
  // 获取流程各个状态信息
  getJobStatusData() {
    get(URL_JOB_STATUS).then((resp) => {
      this.jobStatusData = JSON.parse(resp.data);
    });
  }

  async getStatusList() {
    const { data = {} } = (await get(URL_JOB_STATUS_LIST)) || {};
    this.statusList = Object.entries(data).map(([value, label]: any) => ({ label, value }));
    this.runTimeStatus = Object.keys(data).reduce((res, key) => {
      res[key] = {
        label: data[key],
        color: runStatusColor[key]
      };
      return res;
    }, {});
  }

  // 批量操作弹框确认
  async handleBatchOperationConfirm() {
    try {
      await (this.$refs.batchForm as any).validate();

      const { batchName, memo } = this.batchForm;
      const flowIds = this.selectedList.map((item) => item.jobId);

      // 如果是启动操作，都需要显示资源配置弹框
      if (this.currentBatchAction === 'ONLINE') {
        this.showResourceConfigDialog(flowIds, this.currentBatchParams.savepoint, batchName, memo);
        return;
      }

      // 执行批量操作
      await this.executeBatchOperation(this.currentBatchAction, flowIds, batchName, memo, this.currentBatchParams);

      // 关闭弹框
      this.batchOperationDialogVisible = false;
    } catch (error) {
      // 表单验证错误已通过其他方式处理
    }
  }

  // 批量操作弹框取消
  handleBatchOperationCancel() {
    this.batchOperationDialogVisible = false;
    this.currentBatchAction = '';
    this.currentBatchParams = {};
    // 清理表单数据
    this.batchForm.batchName = '';
    this.batchForm.memo = '';
    // 清理表单验证状态
    this.$nextTick(() => {
      (this.$refs.batchForm as any)?.clearValidate();
    });
  }

  // 显示资源配置弹框
  showResourceConfigDialog(flowIds: string[], savepoint: boolean, batchName: string, memo: string) {
    this.currentBatchParams = {
      ...this.currentBatchParams,
      flowIds,
      savepoint,
      batchName,
      memo
    };

    // 关闭批量操作弹框，延迟显示资源配置弹框以避免闪现
    this.batchOperationDialogVisible = false;
    setTimeout(() => {
      this.resourceConfigDialogVisible = true;
    }, 100);
  }

  // 执行批量操作
  async executeBatchOperation(action: string, flowIds: string[], batchName: string, memo: string, extraParams: any = {}) {
    let jobConfigs: any = {
      savepoint: extraParams.savepoint !== undefined ? extraParams.savepoint : false,
      force: extraParams.force || false
    };

    // 如果有资源配置数据，需要解析并合并
    if (extraParams.configData) {
      let actualConfigData = {};
      try {
        if (
          extraParams.configData.pajob &&
          extraParams.configData.pajob.properties &&
          typeof extraParams.configData.pajob.properties === 'string'
        ) {
          actualConfigData = JSON.parse(extraParams.configData.pajob.properties);
        } else if (extraParams.configData.properties && typeof extraParams.configData.properties === 'string') {
          actualConfigData = JSON.parse(extraParams.configData.properties);
        } else {
          actualConfigData = extraParams.configData;
        }
      } catch (error) {
        // JSON 解析失败时使用原始数据
        actualConfigData = extraParams.configData;
      }

      // 将资源配置数据合并到 jobConfigs 中
      jobConfigs = {
        ...actualConfigData,
        savepoint: extraParams.savepoint !== undefined ? extraParams.savepoint : false,
        force: extraParams.force || false
      };
    }

    const batchData = {
      batchName,
      jobList: flowIds,
      action,
      memo,
      jobConfigs
    };

    try {
      const result = await saveBatch(batchData);
      if (result.success) {
        this.$message.success(this.$t('pa.monitor.flow.action.batchOperationSuccess') as string);
        this.getListData();
      } else {
        this.$message.error(result.error || (this.$t('pa.flow.batchOperationFailed') as string));
      }
    } catch (error) {
      this.$message.error(this.$t('pa.flow.batchOperationFailed') as string);
    }
  }

  // 资源配置弹框关闭处理
  async handleResourceConfigClose(configData?: any) {
    this.resourceConfigDialogVisible = false;

    if (configData && this.currentBatchParams) {
      // 用户点击了保存，执行批量启动
      const { flowIds, batchName, memo } = this.currentBatchParams;
      await this.executeBatchOperation(this.currentBatchAction, flowIds, batchName, memo, {
        ...this.currentBatchParams,
        configData
      });
    }

    // 清理参数
    this.currentBatchParams = {};
    this.getListData();
  }

  closeDialog() {
    this.showSingleFlowConfig = false;
    this.getListData();
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-icon-sort {
  transform: rotate(90deg);
}
.operate-box__select {
  width: 160px;
  ::v-deep .el-input {
    width: 160px !important;
  }
}
.info {
  display: flex;
  background: #fff;
  padding: 15px 20px 0;
  &-status {
    width: 190px;
    ::v-deep .el-input {
      width: 190px !important;
    }
  }
  &-search {
    ::v-deep .el-input {
      width: 195px !important;
    }
  }
  .all-count,
  .single-count {
    height: 60px;
    border: 1px solid rgba(241, 241, 241, 1);
    border-radius: 4px;
    background: #fafbfc;
  }
  .all-count {
    display: flex;
    align-items: center;
    width: 200px;
    padding-left: 10px;
    margin-right: 20px;
    &__img {
      width: 40px;
      height: 40px;
      margin-right: 14px;
      margin-top: 6px;
    }
  }
  .single-count {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    > div {
      display: flex;
      align-items: center;
      .iconfont {
        margin-right: 8px;
        font-size: 14px;
      }
    }
    > div:last-child {
      margin-right: 20px;
    }
  }
  .count-label {
    margin-right: 20px;
    color: $--bs-color-text-secondary;
    @media screen and (max-width: 1400px) {
      margin-right: 10px;
    }
  }
  .count-num {
    font-family: DINAlternate-Bold;
    font-size: 20px;
    color: #444444;
    font-weight: 700;
  }
}
.iconfont + .iconfont {
  margin-left: 10px;
}
::v-deep .bs-pro-page__header .bs-pro-page__header-operation .el-input {
  width: 220px;
}
::v-deep .bs-pro-page__header-title .iconfont {
  padding-left: 4px;
  font-size: 16px;
  font-weight: normal;
  color: rgba(0, 0, 0, 0.25);
}
.content-point {
  display: flex;
  align-items: flex-start;
  .bs-circle {
    width: 4px;
    height: 4px;
    background: #444;
    margin-right: 8px;
    margin-top: 6px;
  }
}
</style>
