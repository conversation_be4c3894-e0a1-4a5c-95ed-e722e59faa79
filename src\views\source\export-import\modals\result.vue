<template>
  <bs-dialog
    title="导入结果"
    :visible.sync="visible"
    width="40%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    append-to-body
  >
    <div class="result-content">
      <div class="result-content_line">
        本次成功导入{{ data.successNumber }}个数据，失败导入{{
          data.failedNumber
        }}个数据，失败导入数据如下：
      </div>
      <div v-if="resultOfSubmit.job && resultOfSubmit.job.f0 > 0" class="result-content_line">
        流程：有{{ resultOfSubmit.job.f0 }}个流程失败导入，分别是 {{ resultOfSubmit.job.f2 }}
      </div>
      <div
        v-if="resultOfSubmit.function && resultOfSubmit.function.f0 > 0"
        class="result-content_line"
      >
        方法：有{{ resultOfSubmit.function.f0 }}个方法失败导入，分别是
        {{ resultOfSubmit.function.f2 }}
      </div>
      <div v-if="resultOfSubmit.jar && resultOfSubmit.jar.f0 > 0" class="result-content_line">
        依赖：有{{ resultOfSubmit.jar.f0 }}个依赖失败导入，分别是 {{ resultOfSubmit.jar.f2 }}
      </div>
      <div v-if="resultOfSubmit.table && resultOfSubmit.table.f0 > 0" class="result-content_line">
        表：有{{ resultOfSubmit.table.f0 }}个表失败导入，分别是 {{ resultOfSubmit.table.f2 }}
      </div>
      <div v-if="resultOfSubmit.view && resultOfSubmit.view.f0 > 0" class="result-content_line">
        视图：有{{ resultOfSubmit.view.f0 }}个视图失败导入，分别是 {{ resultOfSubmit.view.f2 }}
      </div>
      <div v-if="resultOfSubmit.item && resultOfSubmit.item.f0 > 0" class="result-content_line">
        选项：有{{ resultOfSubmit.item.f0 }}个选项失败导入，分别是 {{ resultOfSubmit.item.f2 }}
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog(false)">关 闭</el-button>
    </span>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, Emit, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
@Component
export default class Result extends PaBase {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: {} }) data: any;
  resultOfSubmit: any = { jar: {}, job: {}, function: {}, table: {}, view: {}, item: {} };

  @Watch('visible')
  onVisibleChange(val) {
    if (val) {
      const keys = Object.keys(this.data.resultOfSubmit);
      keys.forEach((item) => {
        this.data.resultOfSubmit[item].f2 = '';
        this.data.resultOfSubmit[item].f1.forEach((el) => {
          this.data.resultOfSubmit[item].f2 += '{' + el + '}、';
        });
        this.data.resultOfSubmit[item].f2 = this.data.resultOfSubmit[item].f2.slice(
          0,
          this.data.resultOfSubmit[item].f2.length - 1
        );
      });
      this.resultOfSubmit = this.data.resultOfSubmit;
    }
  }

  @Emit('close')
  private closeDialog() {}
}
</script>
<style lang="scss" scoped>
.result-content {
  margin: 5px 0px;
  // height: 400px;
  // display: flex;
  // flex-direction: column;
  // justify-content: space-between;
  font-size: 15px;

  &_line {
    margin: 5px 0px;
  }
}
</style>
