<template>
  <div>
    <el-tooltip :disabled="flows.length < 4" :content="allFlowInfo" placement="top" effect="light">
      <p class="flow-info">{{ flowInfo }}</p>
    </el-tooltip>
    <pro-form ref="proForm" :value="formData" :form-items="formItems" :options="{ labelWidth: isEn ? 186 : 106 }">
      <bs-cascader
        slot="parentIds"
        v-model="formData.parentIds"
        :options="dirOptions"
        :placeholder="placeholder"
        filterable
        :props="cascaderProps"
        popper-class="dir-cascader"
        style="width: 100%"
      />
    </pro-form>
    <p v-if="isMoving" class="flow-tip">{{ $t('pa.flow.msg250') }}</p>
  </div>
</template>
<script lang="ts">
import { batchCopy, batchMove, getAllProjectTree } from '@/apis/flowNewApi';
import { Component, Prop, Vue } from 'vue-property-decorator';
type Type = 'move' | 'copy';
interface Flow {
  id: string;
  name: string;
}
@Component
export default class flowMovingCopying extends Vue {
  @Prop() flows!: Flow[];
  @Prop() type!: Type;
  dirOptions = [];
  formData: any = {
    parentIds: []
  };
  formItems = [
    {
      type: 'custom',
      label: this.$t('pa.flow.tDir'),
      prop: 'parentIds',
      rules: [{ required: true, message: this.$t('pa.flow.placeholder47'), trigger: 'blur' }]
    }
  ];
  get isMoving() {
    return this.type === 'move';
  }
  get flowInfo() {
    const { isMoving, flows } = this;
    const len = flows.length;
    const showFlowNames =
      flows
        .slice(0, 3)
        .map(({ name }) => '【' + name + '】')
        .join('、') + (len > 3 ? '…' : '');
    return isMoving ? this.$t('pa.flow.msg251', [showFlowNames, len]) : this.$t('pa.flow.msg252', [showFlowNames, len]);
  }
  get allFlowInfo() {
    const { flows } = this;
    return flows.map(({ name }) => '【' + name + '】').join('、');
  }
  get cascaderProps() {
    const props = { checkStrictly: true, emitPath: false };
    return this.isMoving ? props : Object.assign(props, { multiple: true });
  }
  get placeholder() {
    return this.isMoving ? this.$t('pa.placeholder.select') : this.$t('pa.flow.placeholder48');
  }
  async created() {
    // 获取所有项目及项目下的目录
    const { data } = await getAllProjectTree({});
    const transform = (data: any[]) => {
      return (Array.isArray(data) ? data : []).map(({ nodeId, nodeName, children }) => ({
        value: nodeId,
        label: nodeName,
        children: Array.isArray(children) && children.length ? transform(children) : undefined
      }));
    };
    this.dirOptions = transform(data);
  }
  async confirmForMoving(done) {
    const params = {
      jobIds: this.flows.map((e) => e.id),
      projectIds: [this.formData.parentIds]
    };
    const {
      data: { successNum, failedNum, projectNameExist, projectNotDev }
    } = await batchMove(params);
    const projectNameExistStr = projectNameExist.length ? projectNameExist.map((name) => `【${name}】`).join('、') : '';
    const projectNotDevStr = projectNotDev.length ? projectNotDev.map((name) => `【${name}】`).join('、') : '';
    if (failedNum === 0) {
      this.$message.success(this.$t('pa.flow.msg253', [successNum]));
      done(true);
    } else if (successNum > 0) {
      this.$message.info({
        dangerouslyUseHTMLString: true,
        customClass: 'flow-moving',
        duration: 0,
        showClose: true,
        message: `<p>${this.$t('pa.flow.msg254', [successNum, failedNum])}</p>
          ${projectNameExistStr && '<p class="error-msg">' + this.$t('pa.flow.msg255') + projectNameExistStr + '</p>'}
          ${projectNotDevStr && '<p class="error-msg">' + this.$t('pa.flow.msg256') + projectNotDevStr + '</p>'}
          `
      });
      done(true);
    } else {
      this.$message.error({
        dangerouslyUseHTMLString: true,
        customClass: 'flow-moving',
        duration: 0,
        showClose: true,
        message: `<p>${this.$t('pa.flow.msg257', [failedNum])}</p>
          ${projectNameExistStr && '<p class="error-msg">' + this.$t('pa.flow.msg255') + projectNameExistStr + '</p>'}
          ${projectNotDevStr && '<p class="error-msg">' + this.$t('pa.flow.msg256') + projectNotDevStr + '</p>'}
          `
      });
      done(false);
    }
  }
  async confirmForCopying(done) {
    const params = {
      jobIds: this.flows.map((e) => e.id),
      projectIds: this.formData.parentIds
    };
    const { success, msg } = await batchCopy(params);
    this.$message[success ? 'success' : 'error'](msg);
    done(success);
  }
  // 配合表单确认按钮执行方法
  public confirm(done) {
    (this.$refs.proForm as any).validate(async (vaild) => {
      if (!vaild) return done(false);
      this.isMoving ? this.confirmForMoving(done) : this.confirmForCopying(done);
    });
  }
}
</script>

<style lang="scss" scoped>
.flow-info {
  margin-bottom: 30px;
}
.flow-tip {
  color: $--bs-color-text-secondary;
}
</style>
<style lang="scss">
.flow-moving {
  align-items: flex-start;
}
.flow-moving p + p {
  margin-top: 10px;
}
.flow-moving .error-msg {
  position: relative;
  padding-left: 12px;
}
.flow-moving .error-msg::before {
  position: absolute;
  top: 4px;
  left: 0;
  content: '';
  display: block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: $--bs-color-text-primary;
}
.flow-moving.el-message--error .error-msg::before {
  background: $--bs-color-red;
}
</style>
