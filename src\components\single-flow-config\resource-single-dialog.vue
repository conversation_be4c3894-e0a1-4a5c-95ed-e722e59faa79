<template>
  <bs-dialog
    v-loading="loading"
    width="650px"
    :title="title"
    :visible.sync="display"
    @close="close"
  >
    <div class="container">
      <config-form
        v-if="display"
        ref="configRef"
        :title.sync="title"
        :flow-id="flowId"
        :project-id="projectId"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
      <el-button v-if="isMonitor" type="primary" @click="online">启动</el-button>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import { post } from '@/apis/utils/net';
import { URL_JOB_ONLINE, URL_JOB_PREONLINE } from '@/apis/commonApi';
@Component({
  components: {
    ConfigForm: () => import('@/components/single-flow-config/index.vue')
  }
})
export default class ResourceSingleConfig extends Vue {
  @PropSync('show', { default: false }) display!: boolean;
  @Prop({ default: '' }) flowId!: string;
  @Prop({ default: '' }) projectId!: string;
  @Prop({ default: false }) isFullScreen!: boolean;
  @Prop({ default: false }) status!: boolean; // 流程监控列表-启动-无状态启动&基于上次状态启动
  @Prop({ default: false }) isMonitor!: boolean; // 流程监控列表-启动-无状态启动&基于上次状态启动
  @Ref('configRef') readonly configRef!: any;

  private title = '流程配置';
  private disabled = false;
  private loading = false;

  async handleSubmit() {
    await this.configRef.handleSubmit();
    this.$emit('close');
    this.display = false;
  }

  close() {
    this.display = false;
  }

  async online() {
    await this.configRef.handleSubmit();
    const params = [
      {
        id: this.flowId,
        fromLastCheckpoint: this.status
      }
    ];
    const res = await post(URL_JOB_PREONLINE, {
      relationBatchTag: false,
      jobs: params.map(({ id }) => id)
    });
    if (res.success) {
      this.$tip.success(res.msg);
      const { success, msg, data } = await post(URL_JOB_ONLINE, params);
      if (success) {
        this.loading = false;
        this.$tip.success(msg);
        this.close();
        this.$emit('close');
        return;
      }
      this.loading = false;
      this.$tip.errorPro(msg, data);
      return;
    }
    this.loading = false;
    this.$tip.errorPro(res.msg, res.data);
  }
}
</script>
<style lang="scss" scoped>
.container {
  ::v-deep .el-collapse {
    border-top: unset;
  }

  ::v-deep .resource-title {
    margin-left: 16px;
    padding-left: 6px;
    width: 100%;
    font-size: 14px;
    font-weight: 500;
    color: #444444;
    line-height: 20px;
    &::before {
      content: '';
      position: relative;
      left: 0;
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 8px;
      background: #ff9e2b;
      border-radius: 2px;
    }
  }
  ::v-deep .el-form-item {
    &__content {
      display: flex;
      align-items: center;

      .resource-item {
        display: inline-block;
        width: calc(100% - 30px);
        .el-select,
        .el-input-number,
        .el-date-editor {
          width: 100%;
        }
        .el-form-item {
          display: inline-block;
          width: 50%;
        }
      }
    }
  }
}
</style>
