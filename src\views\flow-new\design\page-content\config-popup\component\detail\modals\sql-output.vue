<template>
  <el-dialog
    :width="width"
    :title="title"
    :visible.sync="display"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <!-- 表单 -->
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      :disabled="disabled"
      :label-width="labelWidth"
      class="sql-output-container"
    >
      <!-- 选择源表 -->
      <el-form-item label="选择源表" prop="sourceTable">
        <el-select
          v-model="form.sourceTable"
          filterable
          clearable
          placeholder="请选择源表"
          @change="handleSourceTableChange"
        >
          <el-option
            v-for="item in sourceTableList"
            :key="item.name"
            :value="item.name"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <!-- 选择输出表 -->
      <el-form-item label="选择输出表" prop="sinkTable">
        <el-select
          v-model="form.sinkTable"
          filterable
          clearable
          placeholder="请选择输出表"
          @change="handleSinkTableChange"
        >
          <el-option
            v-for="item in sinkTableList"
            :key="item.name"
            :value="item.name"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <!-- 选中表 -->
      <el-form-item v-if="sinkTableName" label="选中输出表">
        <div class="sql-output-selected">
          <span @click="goTableDetail">{{ sinkTableName }}</span>
        </div>
      </el-form-item>
      <!-- 字段映射表 -->
      <map-table
        :height="185"
        :thead="tableHead"
        :data="fieldMappingData"
        @change="handleMapTableChange"
      />
      <!-- 错误信息 -->
      <div class="sql-output-error">{{ showErrorMsg ? errorMsg : '' }}</div>
      <!-- 连接器配置 -->
      <connector-config
        ref="connector"
        :disabled="disabled"
        :data="connectorConfigData"
        :origin-data="connectorInfo"
        :node-name="this.data.nodeName"
        @change="handleConnectorChange"
      />
    </el-form>
    <!-- footer -->
    <sql-footer
      slot="footer"
      change-name
      :data.sync="form"
      @submit="submit"
      @cancel="closeDialog"
    />
    <!-- 详情 -->
    <views-detail-dialog
      v-if="showDetail"
      :id="form.sinkTableId"
      :show.sync="showDetail"
      :type="form.sinkTableType"
    />
  </el-dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, PropSync, Ref, Vue } from 'vue-property-decorator';
import { cloneDeep } from 'lodash';
import Form from 'bs-ui-pro/lib/form';
import ConnectorConfig from './components/connector-config.vue';
import MapTable from './components/map-table.vue';
import { get, post } from '@/apis/utils/net';
import {
  URL_GET_ALL_TABLE_INFO,
  URL_GET_CONNECTOR_INFO,
  URL_SQL_ANALYSIS_FIELDS
} from '@/apis/commonApi';

@Component({
  components: {
    MapTable,
    ConnectorConfig,
    SqlFooter: () => import('./components/sql-footer.vue'),
    ViewsDetailDialog: () => import('@/components/views-detail-dialog/index.vue')
  }
})
export default class SqlOutput extends Vue {
  @Prop() data!: any;
  @Prop() jobData!: any;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @PropSync('show', { type: Boolean, default: false }) display!: boolean;
  @Ref('form') readonly formRef!: Form;
  @Ref('connector') readonly connector!: ConnectorConfig;
  private title = 'sql输出';
  private labelWidth = '100px';
  private width = '650px';
  private form: any = {
    sourceTable: '', // 源表
    sourceTableType: '', // 源表类型
    sinkTable: '', // 输出表
    sinkTableId: '', // 输出表id
    sinkTableType: '', // 输出表类型
    autoChangeName: true
  }; // 表单
  private rules = {
    sourceTable: [
      {
        required: true,
        message: '请选择源表',
        trigger: 'change'
      }
    ],
    sinkTable: [
      {
        required: true,
        message: '请选择正确的输出表',
        trigger: 'change'
      }
    ]
  }; // 表单校验
  private tableHead = [
    {
      prop: 'name',
      label: '输出表',
      minWidth: '50%'
    },
    {
      prop: 'select',
      label: '源表',
      minWidth: '50%',
      isSelect: true
    }
  ]; // 表头
  private sourceTableList: any[] = []; // 源表数据
  private sinkTableList: any[] = []; // 输出表数据
  private fieldMappingData: any[] = []; // 字段隐射数据
  private connectorConfigData: any[] = []; // 连接器配置数据
  private connectorInfo: any = {}; // 连接器配置信息
  private sourceTableFields: any[] = []; // // 源表字段
  private sinkTableFields: any[] = []; // 输出表字段
  private outputFieldsMap: any = {}; // 映射数据输出字段
  private showErrorMsg = false; // 是否显示错误信息
  private errorMsg = '请选择输出表字段！'; // 错误信息
  private outputFields: any[] = []; // 输出信息
  private showDetail = false;
  private id = '';
  private type = '';
  private connectorId = '';

  get sinkTableName() {
    const [{ label }] = this.tableHead;
    return label === '输出表' ? '' : label;
  }

  async created() {
    await this.getOutputTableList();
    if (!['PROD', 'PUB'].includes(this.jobData.jobStatus)) {
      await this.getViewFields();
    }
    const properties = cloneDeep(this.data.properties || {});
    this.outputFieldsMap = await cloneDeep(properties.outputFieldsMap || {});
    this.connectorInfo = await cloneDeep(properties.connectorInfo || {});
    delete properties.outputFieldsMap;
    delete properties.connectorInfo;
    this.form = { ...this.form, ...properties };
    if (Object.keys(properties).length > 0 && !('autoChangeName' in properties)) {
      this.form.autoChangeName = false;
    }
    await this.handleSourceTableChange(this.form.sourceTable, true);
    this.handleSinkTableChange(this.form.sinkTable, true);
  }

  /* 获取输出表列表 */
  async getOutputTableList() {
    const { error, data, success }: any = await get(URL_GET_ALL_TABLE_INFO);
    if (success) {
      this.sinkTableList = Array.isArray(data) ? data : [];
      this.sinkTableList.push({
        id: '',
        name: '',
        fields: [],
        type: 'TABLE'
      });
      return;
    }
    this.$tip.error(error);
  }

  /* 获取连接器数据 */
  async getConnectorInfo(connectorId: string) {
    this.connectorConfigData = [];
    if (!connectorId) {
      this.connectorConfigData = [];
      return;
    }
    try {
      const { error, data, success }: any = await get(URL_GET_CONNECTOR_INFO, {
        connectorId,
        propertyType: 'SINK'
      });
      if (success) {
        const newData = JSON.parse(data);
        this.connectorConfigData = Array.isArray(newData) ? newData : [];
        return;
      }
      this.$tip.error(error);
    } catch (e) {
      this.connectorConfigData = [];
    }
  }

  /* 源表数据变化 */
  async handleSourceTableChange(tableName = '', isFirst = false) {
    this.tableHead[1].label = tableName || '源表';
    const el = this.sourceTableList.find(({ name }: any) => name === tableName);
    this.form.sourceTableType = el && el.type ? el.type : '';
    this.form.sourceResType = el && el.sourceResType ? el.sourceResType : null;
    this.sourceTableFields = el && el.fields ? [...el.fields] : [];
    this.generateFieldMappingData(isFirst, 'source');
  }

  /* 根据jobData解析字段 */
  async getViewFields() {
    const loading = this.$loading({ text: '字段解析中...' });
    try {
      const paJob = cloneDeep(this.jobData);
      const { nodeId, inputFields } = this.data;
      const { error, data, success }: any = await post(
        `${URL_SQL_ANALYSIS_FIELDS}?nodeId=${nodeId}`,
        { ...paJob }
      );
      if (success) {
        const newData = Array.isArray(data) ? data : [];
        const newInputFieldsAll = Array.isArray(inputFields) ? inputFields : [];
        const newInputFields = newInputFieldsAll.filter(({ type }: any) => type !== 'VIEW');
        this.sourceTableList = [...newData, ...newInputFields];
        loading.close();
        return;
      }
      loading.close();
      this.$tip.errorPro(error, data);
    } catch (e) {
      loading.close();
    }
  }

  /* 选择表数据变化 */
  handleSinkTableChange(tableName = '', isFirst = false) {
    const el = this.sinkTableList.find(({ name }: any) => name === tableName);
    if (el) {
      this.form.sinkTableId = el && el.id ? el.id : '';
      this.tableHead[0].label = el && el.name ? el.name : '输出表';
      this.form.sinkTableType = el && el.type ? el.type : '';
      const properties = el && el.properties ? { ...el.properties } : {};
      this.form = { ...this.form, ...properties };
      this.sinkTableFields = el && el.fields ? [...el.fields] : [];
      this.outputFields = el ? [{ ...el, outputable: true }] : [];

      this.connectorId = el && el.connectorId ? el.connectorId : '';
      this.generateFieldMappingData(isFirst, 'sink');
    } else {
      this.connectorId = '';
      if (tableName) {
        this.$tip.warning('当前选中表不存在');
      }
    }
  }

  /* 生成字段对应数据 */
  generateFieldMappingData(isFirst = false, type = '') {
    if (['PUB', 'PROD'].includes(this.jobData.jobStatus)) {
      return this.setFieldMappingData();
    }
    if (!isFirst) {
      this.outputFieldsMap = {};
      this.connectorInfo = {};
    }
    this.connectorId && this.getConnectorInfo(this.connectorId);
    this.showErrorMsg = false;
    const { sourceTableFields, sinkTableFields, outputFieldsMap } = this;
    if (sourceTableFields.length < 1 || sinkTableFields.length < 1) {
      this.fieldMappingData = [];
      if (sourceTableFields.length < 1) {
        if (!isFirst && type === 'source') {
          this.$tip.error('源表对应字段为空，请重新选择源表');
        }
        return;
      }
      if (sinkTableFields.length < 1) {
        if (!isFirst && type === 'sink') {
          this.$tip.error('输出表对应字段为空，请重新选择输出表');
        }
        return;
      }
    }
    this.fieldMappingData = sinkTableFields.map((name: string) => {
      const temp: any = { name };
      let key = name;
      if (name.includes('.')) key = name.split('.')[1];
      temp.options = [...sourceTableFields];
      temp.defaultValue =
        typeof outputFieldsMap[key] === 'undefined'
          ? sourceTableFields.find((item: any) => item === key)
          : outputFieldsMap[key] || null;
      return temp;
    });
  }

  setFieldMappingData() {
    const list = Array.isArray(this.sinkTableFields) ? this.sinkTableFields : [];
    this.fieldMappingData = list.map((name) => {
      return {
        name,
        defaultValue: this.outputFieldsMap[name] || null,
        options: Object.values(this.outputFieldsMap)
      };
    });
  }

  /* 映射表数据变化 */
  handleMapTableChange(data: any) {
    Object.keys(data).forEach((key: any) => {
      if (!data[key]) {
        data[key] = null;
      }
    });
    this.showErrorMsg = false;
    this.outputFieldsMap = { ...data };
  }

  handleConnectorChange(data: any) {
    this.connectorInfo = { ...data };
  }

  /* 前往表或视图详情 */
  goTableDetail() {
    const { sinkTable: name, sinkTableType: type } = this.form;
    const hasData = this.sinkTableList.some((el: any) => el.name === name);
    if (!hasData) {
      this.$tip.error(`表或视图${this.sinkTableName}不存在，无法查看详情！`);
      return;
    }
    if (!type) {
      this.$tip.error(`表或视图${this.sinkTableName}数据不完整，无法查看详情！`);
      return;
    }
    this.showDetail = true;
  }

  /* 确定 提交 */
  async submit() {
    await this.formRef.validate();
    await this.validateOutputFieldsMap();
    if (this.connectorConfigData.length > 0) {
      await this.connector.validate();
    }
    this.closeDialog(true);
  }

  /* 输出表字段校验 */
  validateOutputFieldsMap() {
    const result = Object.values(this.outputFieldsMap).some((o) => o);
    this.showErrorMsg = !result;
    return result ? null : Promise.reject(this.errorMsg);
  }

  @Emit('close')
  private closeDialog(needUpdate) {
    if (needUpdate) {
      const properties = cloneDeep(this.form);
      const { connectorInfo, data, outputFieldsMap, outputFields } = this;
      const jobNode = cloneDeep(data);
      jobNode.outputFields = cloneDeep(outputFields);
      if (!properties.sourceResType) {
        delete properties.sourceResType;
      }
      jobNode.properties = { ...properties, connectorInfo, outputFieldsMap };
      jobNode.nodeName = this.handleNodeName();
      this.reset();
      return { needUpdate, jobNode };
    }
    this.reset();
  }

  reset() {
    this.display = false;
    this.connectorInfo = [];
    this.outputFieldsMap = {};
    this.outputFields = [];
    this.formRef.resetFields();
  }
  handleNodeName() {
    const index = this.data.nodeName.lastIndexOf('_');
    if (!this.form.autoChangeName || index < 0) return this.data.nodeName;
    return `${this.form.sinkTable}${this.data.nodeName.substring(index)}`;
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__header {
  padding-left: 16px;
  padding-right: 16px;
  border-bottom: solid 1px #e4e7ed;
  &::before {
    content: ' ';
    position: relative;
    left: 0;
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 4px;
    border: 3px solid #ff9c00;
    background: #fff;
    border-radius: 50%;
    box-sizing: content-box;
  }
}
::v-deep .el-dialog__footer {
  padding: 13px 20px;
  border-top: solid 1px #e4e7ed;
}
.sql-output {
  &-container {
    ::v-deep .el {
      &-select {
        width: 100%;
      }

      &-table {
        td,
        th {
          padding: 4px 0;
        }
      }
    }
  }

  &-error {
    margin: 8px 0;
    height: 16px;
    text-align: left;
    color: #e2484a;
    font-size: 12px;
  }

  &-selected {
    text-align: left;

    span {
      color: #2440b3;

      &:hover {
        cursor: pointer;
        color: #315efb;
        text-decoration: underline;
      }
    }
  }
}
</style>
