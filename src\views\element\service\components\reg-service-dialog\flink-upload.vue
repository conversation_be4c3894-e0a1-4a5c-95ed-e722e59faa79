<template>
  <div style="position: relative">
    <el-link v-if="fileUrl" :underline="false" type="primary">{{ fileUrl }}</el-link>
    <bs-upload
      :ref="prop"
      action=""
      :accept="componentProps.accept"
      :limit="1"
      :auto-upload="false"
      :upload-text="uploadText"
      :on-change="handleFieldChange"
      :on-remove="handleRemoveFile"
      :tip-text="componentProps.textTip"
    />
    <div v-if="!isDfs" class="bs-upload-mask" @click="handleUploadClick"></div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class FlinkUpload extends Vue {
  // 属性
  @Prop({ default: () => {} }) options!: any;
  @Prop({ default: () => {} }) formData!: any;
  // 上一次保存的文件路径
  fileUrl = '';
  created() {
    this.fileUrl = this.formData[this.prop];
  }
  get prop() {
    return (this.options || {}).prop;
  }
  get uploadText() {
    return this.fileUrl ? this.$t('pa.reupload') : this.$t('pa.choseFile');
  }
  // 是否开启分布式系统
  get isDfs() {
    return this.$store.getters.enableDFS;
  }
  get componentProps() {
    return (this.options || {}).componentProps;
  }
  handleUploadClick() {
    this.$message.warning(this.$t('pa.tip.dfs'));
  }
  handleFieldChange(data) {
    if (data.size > Math.pow(1024, 3)) {
      this.$message.warning(this.$t('pa.tip.size1GB'));
    }
    this.formData[this.prop] = data.raw;
  }
  handleRemoveFile() {
    this.formData[this.prop] = this.fileUrl;
  }
}
</script>
<style lang="scss" scoped>
::v-deep .bs-upload {
  line-height: 1;
  .el-upload__tip {
    display: inline;
    margin-top: 0px;
    margin-left: 10px;
  }
}
.bs-upload-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 102px;
  height: 28px;
  background: transparent;
  cursor: pointer;
}
</style>
