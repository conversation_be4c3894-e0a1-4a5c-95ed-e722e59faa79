<template>
  <div class="flow-code" @contextmenu.prevent @click="$emit('canvas-click')">
    <div v-if="!disabled && !isFullScreen" class="flow-code-btns">
      <div class="flow-code-save">
        <span v-if="autoSaveDate" :class="[isAutoSaving ? 'el-icon-refresh' : 'el-icon-success']"></span>
        <span v-if="autoSaveDate" class="flow-code-save__text">{{ saveText }}</span>
      </div>
      <!-- 暂时隐藏格式化功能 -->
      <!-- <el-button plain size="small" icon="iconfont icon-geshihua" @click="format">
          格式化
        </el-button> -->
      <el-tooltip placement="bottom" effect="light">
        <div slot="content">
          <p>{{ $t('pa.flow.useUtil') }}</p>
          <p>{{ $t('pa.flow.qkey') }}：/</p>
        </div>
        <el-button plain size="small" @click="insertUtilKeyword">「/」{{ $t('pa.flow.util') }} </el-button>
      </el-tooltip>
      <el-button v-if="!isFullScreen" plain size="small" icon="iconfont icon-quanping2" @click="fullScreen">
        {{ $t('pa.flow.quanping') }}
      </el-button>
    </div>
    <bs-code
      :key="randomNum"
      ref="code"
      :value="value"
      language="sql"
      :operatable="false"
      :read-only="disabled || isRunning"
      :trigger-characters="triggerCharacters"
      :hints="getAsyncHints"
      :extra-style="extraStyle"
      :options="codeOptions"
      @change="handleCodeChange"
      @contextMenu="handleCodeContextMenu"
    />
    <!-- 生成个人SQL片段 弹窗 -->
    <GenerateCodeDialog
      v-if="generateCodeVisible"
      :visible.sync="generateCodeVisible"
      :code="selectionCode"
      @confirm="getAllSqlCode"
    />
    <!-- 智能工具 -->
    <UtilsPopper
      v-if="utilsPopperVisible"
      :position="cursorPosition"
      :visible="utilsPopperVisible"
      @close="utilsPopperVisible = false"
    >
      <!-- 表字段生成sql -->
      <GenerateTableField slot="gen-field" :content="curContent" @generate="generateFields" />
    </UtilsPopper>
  </div>
</template>
<script lang="ts">
import { cloneDeep, debounce } from 'lodash';
import { Component, Vue, Ref, Prop, Watch } from 'vue-property-decorator';
import GenerateCodeDialog from './components/generate-code-dialog.vue';
import GenerateTableField from './components/generate-table-field.vue';
import UtilsPopper from './components/utils-popper.vue';
import { generateSuggestions, resolveCustomKeywords, getTriggerCharacters } from './service';
import { getAllSqlList } from '@/apis/sqlApi';
import { hasPermission } from '@/utils';
import { getErrorInfos } from '../../flow-list/service';
import { transformLineErrorInfo } from '../../utils';
import { getSqlSaveInterval } from '@/apis/flowNewApi';
import dayjs from 'dayjs';
import i18n from '@/i18n';
interface Suggestions {
  label: string;
  insertText: string;
  kind: any;
  documentation?: string;
}
interface ErrorInfo {
  range: {
    startLineNumber: number;
    startColumn: number;
    endLineNumber: number;
    endColumn: number;
  };
  message: string;
}
const CONTEXT_MENU = [
  {
    label: i18n.t('pa.flow.realTest'),
    value: 'realTest',
    access: 'PA.FLOW.FLOW_MGR.SESSION'
  },
  {
    label: i18n.t('pa.flow.title2'),
    value: 'codeGenerate'
  }
];
@Component({
  name: 'FlowCode',
  components: {
    GenerateCodeDialog,
    GenerateTableField,
    UtilsPopper
  }
})
export default class FlowSqlCode extends Vue {
  @Ref('code') bsCode: any;
  @Prop() content!: string;
  @Prop() flowId!: string;
  @Prop() flowStatus!: string;
  @Prop({ default: false }) isFullScreen!: boolean;
  @Prop({ default: false }) isRunning!: boolean;
  value = '';
  triggerCharacters = getTriggerCharacters();
  // 代码片段关键字
  codeKeywords: Suggestions[] = [];
  // sql通用关键字
  customKeywords: string[] = [];
  // 控制生成代码片段弹窗显隐
  generateCodeVisible = false;
  // 选中的代码片段
  selectionCode = '';
  debounceResolveCustomKeywords: any = null;
  codeOptions = { minimap: { enabled: true }, fixedOverflowWidgets: true };
  randomNum = 0;

  isAutoSaving = false; // 是否在自动保存中
  autoSaveDate = ''; // 自动保存的时间
  saveTimer: number | null = null;

  // 是否显示智能工具框
  utilsPopperVisible = false;
  curContent = '';
  // 光标坐标轴
  cursorPosition = [0, 0];
  // 手动插入的光标位置
  insertPosition: { lineNumber: number; column: number } | null = null;
  get disabled() {
    return this.flowStatus !== 'DEV';
  }
  get extraStyle() {
    const addHeight = this.disabled ? 0 : 40;
    return {
      height: `calc(100vh - ${this.isFullScreen ? 100 : 166 + addHeight}px)`
    };
  }
  get codeKeywordLabels() {
    return (this.codeKeywords || []).map((i) => i.label);
  }
  get suggestions() {
    return cloneDeep(generateSuggestions(this.customKeywords, this.bsCode.monaco).concat(this.codeKeywords));
  }
  get saveText() {
    return this.isAutoSaving ? this.$t('pa.flow.autoSaving') : this.$t('pa.flow.autoSaved') + this.autoSaveDate;
  }
  @Watch('content', { immediate: true })
  handleContentChange(newVal) {
    this.value = newVal || '';
  }
  activated() {
    this.randomNum = Math.random() * 100000;
  }
  created() {
    this.value = typeof this.content === 'string' ? this.content : '';
    this.debounceResolveCustomKeywords = debounce(
      () => (this.customKeywords = resolveCustomKeywords(this.bsCode.getValue(), this.codeKeywordLabels)),
      200
    );
    // 有代码片段库的权限则加载代码片段库的数据
    if (hasPermission('PA.FLOW.FLOW_MGR.SQL_CODE')) {
      this.getAllSqlCode();
    }
    // 注册自动保存的轮询
    this.registerInterval();
  }
  mounted() {
    this.bsCode.monaco.editor.defineTheme('flow-sql-theme', {
      base: 'vs', // 以vs主题为模板
      inherit: true, // 继承vs主题的样式
      // colors: {
      //   'editorLineNumber.foreground': '#e5e5e5' // 行号颜色
      // },
      rules: [
        { token: 'comment', foreground: '#aaaaaa' }, // 注释颜色
        {
          token: 'string.sql', // sql语句里字符串颜色 FF0000 改为 008000
          foreground: '#008000'
        },
        {
          token: 'predefined.sql', // FF00FF 改为 0000ff
          foreground: '#0000ff'
        }
      ]
    });
    this.bsCode.monaco.editor.setTheme('flow-sql-theme');
    // 该流程是否有需要显示得报错信息
    if (getErrorInfos()[this.flowId]) {
      const { errorInfo } = JSON.parse(getErrorInfos()[this.flowId] || '{}');
      const lineErrors = JSON.parse(errorInfo || '[]');
      this.setCodeError(transformLineErrorInfo(lineErrors));
      // 显示报错后进行移除
      this.$emit('removeErrorInfo', this.flowId);
    }
  }
  deactivated() {
    this.destroyPopoverMenu();
  }
  destroyed() {
    this.destroyPopoverMenu();
  }
  destroyPopoverMenu() {
    try {
      (this as any).$bsPopoverMenu.destroy();
    } catch (e) {
      if (e) console.log('no popover menu instance.');
    }
    // 清除自动保存的定时器
    this.saveTimer && clearInterval(this.saveTimer);
  }
  // 获取所有的sql代码片段
  async getAllSqlCode() {
    const { data = [], success } = await getAllSqlList();
    if (!success) return;
    this.codeKeywords = data.map((item) => ({
      label: item.shortCode,
      insertText: this.$store.getters.decrypt(item.body),
      documentation: item.name,
      kind: this.bsCode.monaco.languages.CompletionItemKind.Module
    }));
  }
  getAsyncHints() {
    return new Promise((resolve) => {
      resolve({
        suggestions: this.suggestions
      });
    });
  }
  // 格式化代码内容
  format() {
    this.bsCode.format();
  }
  // 全屏
  fullScreen() {
    window.open(`#/flowFullScreen?flowId=${this.flowId}`);
  }
  // 处理代码内容变更
  handleCodeChange(val) {
    // 处理全部删除代码后 value未更新 导致无法触发bs-code的watch事件
    !val && (this.value = '');
    // 变量解析
    this.debounceResolveCustomKeywords();
    this.showUtilPopper(val);
    if (val !== this.value) {
      this.$emit('content-change', true);
    }
  }
  handleCodeContextMenu({ event }) {
    this.selectionCode = this.bsCode.getSelection();
    const contextMenu = CONTEXT_MENU.filter((el) => hasPermission(el.access));
    contextMenu.forEach((el) => {
      if (['realTest'].includes(el.value)) {
        el['disabled'] = this.disabled;
      }
    });

    this.selectionCode &&
      (this as any).$bsPopoverMenu.show(
        {
          position: [event.posx, event.posy],
          list: contextMenu
        },
        (value) => {
          this[value]();
        }
      );
  }
  realTest() {
    this.$emit('real-test', this.selectionCode);
  }
  codeGenerate() {
    this.generateCodeVisible = true;
  }
  // 显示智能工具popper  isManual = true手动调用
  showUtilPopper(val: string) {
    const getPosition = () => {
      const toNum = (s) => {
        return Number(s.replace('px', ''));
      };
      const { lineNumber, column } = this.getPosition();
      const scrollDom: any = document.querySelector('.editor-scrollable .lines-content');
      const scrollTop = (scrollDom && scrollDom.style && toNum(scrollDom.style.top)) || 0;
      const scrollLeft = (scrollDom && scrollDom.style && toNum(scrollDom.style.left)) || 0;
      return [
        Math.ceil(64 + (column - 2) * 7.7 + scrollLeft), // 64 line-number width 7.7 一列宽度
        Math.ceil(40 + (lineNumber - 1) * 19 + scrollTop) // 40 操作栏高度 19 一行宽度
      ];
    };
    const getLastestStr = () => {
      return this.bsCode.getPositionValue();
    };
    if (this.insertPosition) {
      this.bsCode.monacoInstance.revealLineInCenter(this.insertPosition.lineNumber);
      this.setPosition(this.insertPosition);
      this.insertPosition = null;
    }
    // 等待代码区域DOM更新 获取光标所在位置最新的输入
    setTimeout(() => {
      if (getLastestStr() === '/') {
        this.curContent = val;
        this.cursorPosition = getPosition();
        this.utilsPopperVisible = true;
      } else {
        this.utilsPopperVisible = false;
      }
    }, 100);
  }
  // 生成字段代码
  generateFields(codeLines) {
    const { lineNumber } = this.getPosition();
    const lines = this.getContent().split('\n');
    if (lines[lineNumber - 1].replace(/\s*/g, '') === '/') {
      lines.splice(lineNumber - 1, 1, ...codeLines);
    } else {
      // 当前行不为空 往下一行插入
      lines[lineNumber - 1].replace('/', '');
      lines.splice(lineNumber, 0, ...codeLines);
    }
    this.value = lines.join('\n');
    this.utilsPopperVisible = false;
    // 手动触发content-change
    this.$emit('content-change', true);
  }
  // 插入智能
  insertUtilKeyword() {
    if (this.utilsPopperVisible) return;
    const { lineNumber, column } = this.getPosition();
    const lines = this.getContent().split('\n');
    const count = this.getRemarkLineCount();
    // 插入行
    const insertLineNumber = lineNumber < count ? lines.length + 1 : lineNumber;
    lines[insertLineNumber - 1] =
      (lines[insertLineNumber - 1] || '').slice(0, column) + '/' + (lines[insertLineNumber - 1] || '').slice(column);
    this.value = lines.join('\n');
    this.insertPosition = { lineNumber: insertLineNumber, column: column + 1 };
  }
  // 注册自动保存的接口轮询调用
  async registerInterval() {
    if (this.disabled) return;
    const { data } = await getSqlSaveInterval();
    this.saveTimer = setInterval(() => {
      this.$emit('auto-save');
    }, data * 1000);
  }
  // 获取头部注释行的行数
  getRemarkLineCount() {
    const lines = this.getContent().split('\n');
    let count = 0;
    for (let i = 0; i < Math.min(lines.length, 10); i++) {
      if (lines[i] && !/^--/.test(lines[i])) {
        break;
      }
      count++;
    }
    return count;
  }
  // 获取光标位置
  public getPosition() {
    const ins = this.bsCode.monacoInstance;
    let { lineNumber, column } = ins?.getPosition() || { lineNumber: 1, column: 1 };
    const lastLineNumber = ins?.getModel()?.getLineCount() || 0;
    if (lineNumber === 1 && column === 1 && lastLineNumber) {
      lineNumber = lastLineNumber;
    }
    return { lineNumber, column };
  }
  // 设置光标位置
  public setPosition({ lineNumber, column }) {
    this.bsCode.monacoInstance.setPosition({ lineNumber: lineNumber, column: column });
  }
  // 设置代码错误信息
  public setCodeError(errorInfo: ErrorInfo[]) {
    this.bsCode.setErrorLine(errorInfo);
  }
  // 移除代码错误信息
  public removeCodeError() {
    this.bsCode.removeErrorLine();
  }
  // 插入代码
  public insertCode(code) {
    const lines = this.getContent().split('\n');
    // 头部注释行计数
    const count = this.getRemarkLineCount();
    this.value =
      count < 2
        ? `${code}
${this.getContent()}`
        : `${lines.slice(0, count).join('\n')}
${code}
${lines.slice(count).join('\n')}`;
    // 手动触发content-change
    this.$emit('content-change', true);
  }
  // 获取代码内容
  public getContent() {
    return this.bsCode.getValue();
  }
  public updateSaveTime(success) {
    success && (this.autoSaveDate = dayjs().format('YYYY-MM-DD HH:mm:ss'));
    this.isAutoSaving = false;
  }
}
</script>
<style scoped lang="scss">
.flow-code {
  position: relative;
  ::v-deep .bs-code {
    border: none;
    background-color: #fafcff;
  }
  ::v-deep .monaco-editor {
    .monaco-editor-background,
    .margin {
      background-color: #fafcff;
    }
    .line-numbers {
      color: #e5e5e5;
    }
    .monaco-hover {
      z-index: 2000;
    }
  }
  &-btns {
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 20px;
    background: #fff;
    border-bottom: 1px solid $--bs-color-border-lighter;
    .el-button {
      padding: 4px 10px;
    }
  }
  .flow-code-save {
    display: flex;
    align-items: center;
    flex: 1;
    color: $--bs-color-text-placeholder;
    .el-icon-refresh,
    .el-icon-success {
      font-size: 18px;
    }
    .el-icon-success {
      color: $--bs-color-green;
    }
    &__text {
      line-height: 18px;
      margin-left: 10px;
    }
  }
}
</style>
