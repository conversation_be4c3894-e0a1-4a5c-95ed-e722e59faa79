.test-content {
  // border-top: 1px solid #d4dce2;
  height: 340px;
  width: 100%;
  background-color: #fff;
  .test-close-icon {
    position: absolute;
    right: 6px;
    cursor: pointer;
  }
  .tab-title {
    height: 40px;
    // border-bottom:1px solid #F1F1F1;
  }
  .tab-content {
    display: flex;
    height: calc(100% - 68px);
    padding-left: 16px;
    padding-right: 16px;
    .table-content {
      height: calc(100% - 23px);
      margin-top: 10px;
      border: 1px solid #d4dce2;
      background: #fff;
      &__result {
        margin-top: 21px;
      }
      &__list {
        i {
          cursor: pointer;
          padding: 0 5px;
        }
      }
    }
    .data-box {
      width: 50%;
      height: 100%;
      position: relative;
      .data-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
    .result-data-box {
      width: calc(50% - 20px);
      height: 100%;
      margin-left: 20px;
      &__title {
        position: relative;
        top: 6px;
        display: inline-block;
      }
    }
  }
}
.result-content-box {
  height: 350px;
  overflow: auto;
  text-align: initial;
}
.iconfont {
  cursor: pointer;
  color: #528eff;
}
::v-deep .el-tabs__header {
  width: 90%;
}
