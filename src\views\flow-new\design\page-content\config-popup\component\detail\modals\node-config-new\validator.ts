import i18n from '@/i18n';

const FIELD_REG = /^[A-Za-z0-9-_!@#￥%&*()]+$/;
const validators = {
  // 校验文本内容中是否有空格
  validateTrim: (rule, value, callback) => {
    if (typeof value === 'string' && /\s/.test(value)) {
      callback(new Error(i18n.t('pa.flow.msg158') as string));
    } else {
      callback();
    }
  },
  // 校验字段是否合规
  validateField: (rule, value, callback) => {
    const fields = Array.isArray(value) ? value : [value];
    if (fields.some((f) => !FIELD_REG.test(f || ''))) {
      callback(new Error(i18n.t('pa.flow.msg166') as string));
    } else {
      callback();
    }
  }
};
export default validators;
export { FIELD_REG };
