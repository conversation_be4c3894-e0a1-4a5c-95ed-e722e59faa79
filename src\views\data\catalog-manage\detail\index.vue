<template>
  <pro-page v-loading="loading" :active-tab="activeTab" :tabs="tabs" class="page-content" @tab-click="handleTabClick">
    <div slot="operation">
      <span v-show="activeTab === '2'" :class="isEn ? 'page-content__title' : ''" :title="baseInfo.databaseName">{{
        baseInfo.databaseName
      }}</span>
      <bs-select
        v-show="activeTab === '2'"
        v-model="tableSearch.tableName"
        :options="tableNameOptions"
        filterable
        :loading="remoteLoading"
        class="page-content__select"
      />
      <bs-search
        v-show="activeTab === '2'"
        v-model="tableSearch.keyword"
        :class="isEn ? 'page-content__search' : ''"
        :placeholder="$t('pa.placeholder.keyPlaceholder')"
      />
      <el-button v-if="activeTab === '3'" type="primary" @click="download">{{ $t('pa.action.downloadRelation') }}</el-button>
    </div>
    <!-- 基本信息 -->
    <base-info v-show="activeTab === '1'" :base-info="baseInfo" />
    <!-- 表信息 -->
    <table-info v-show="activeTab === '2'" :id="id" ref="tableInfoRef" :table-search="tableSearch" />
    <!-- 引用关系 -->
    <reference-relation v-show="activeTab === '3'" :id="id" :type="selectType" relation="catalog" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Vue, Ref } from 'vue-property-decorator';
import { downloadCatalog, getCatalogTableList, viewCatalogDetail } from '@/apis/dataApi';
import { dateFormat } from '@/utils/format';
@Component({
  components: {
    BaseInfo: () => import('./base-info.vue'),
    TableInfo: () => import('./table-info.vue'),
    ReferenceRelation: () => import('@/views/data/components/reference-relation.vue')
  }
})
export default class CatalogDetail extends Vue {
  @Ref('tableInfoRef') readonly tableInfo!: any;
  id = '';
  activeTab = '1';
  tabs = [
    {
      value: '1',
      label: this.$t('pa.baseInformation')
    },
    {
      value: '2',
      label: this.$t('pa.data.table.detail.tableInfo')
    },
    {
      value: '3',
      label: this.$t('pa.citationRelation')
    }
  ];
  selectType = 'JOB';
  tableNameOptions = [];
  tableSearch = {
    tableName: '',
    keyword: ''
  };
  loading = false;
  remoteLoading = false;
  baseInfo = { databaseName: '' };

  created() {
    this.id = this.$route.query.id as string;
    this.getBaseInfo();
    this.getTableNameOptions();
  }
  handleTabClick(val) {
    this.activeTab = val;
  }
  async getTableNameOptions() {
    try {
      this.remoteLoading = true;
      const { data, success, msg } = await getCatalogTableList(this.id);
      if (!success) return this.$message.error(msg);
      this.tableNameOptions = data.map((item) => ({
        label: item,
        value: item
      }));
    } finally {
      this.remoteLoading = false;
    }
  }
  // 获取基本信息
  async getBaseInfo() {
    try {
      this.loading = true;
      const { data, success, msg } = await viewCatalogDetail(this.id);
      if (!success) return this.$message.error(msg);
      data.createTime = dateFormat(data.createTime);
      data.updateTime = dateFormat(data.updateTime);
      this.baseInfo = data;
    } finally {
      this.loading = false;
    }
  }
  download() {
    downloadCatalog(this.id);
  }
}
</script>
<style scoped lang="scss">
.page-content {
  height: calc(100vh - 110px);
  overflow-y: auto;
  ::v-deep .bs-pro-page__header {
    padding: 0 10px !important;
  }
  &__title {
    width: calc(100% - 480px);
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
    margin-left: 25px;
  }
  &__search {
    width: 235px !important;
    ::v-deep .el-input {
      width: 235px;
    }
  }
  &__select {
    margin-inline: 30px 10px;
  }
}
</style>
