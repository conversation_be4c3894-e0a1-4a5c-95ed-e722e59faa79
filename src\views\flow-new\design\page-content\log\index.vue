<template>
  <div class="log__container">
    <!-- 筛选 -->
    <filter-bar :data.sync="config" @click="handleClick" @chnage="handleChange" />
    <!-- 日志 -->
    <div v-loading="loading" class="log-content">
      <pre ref="logRef" class="log-content__detail"></pre>
    </div>
    <!-- 下载 -->
    <download-dialog v-if="showDownloadDialog" :show.sync="showDownloadDialog" :data="config" :flow-id="flowId" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Ref, Vue } from 'vue-property-decorator';
import { webSocket } from 'rxjs/webSocket';
import { URL_LOG_REPLAY } from '@/apis/commonApi';
import { get } from '@/apis/utils/net';
import dayjs from 'dayjs';
import { baseUrl } from '@/config';

const starTime = dayjs().subtract(1, 'hours').toDate();
@Component({
  components: {
    FilterBar: () => import('./filter-bar.vue'),
    DownloadDialog: () => import('./download-dialog.vue')
  }
})
export default class FlowLog extends Vue {
  @Prop({ default: '' }) flowId!: string;
  @Ref('logRef') readonly log!: HTMLPreElement;

  private loading = false;
  private socket: any = null;
  private config: any = {
    dateTime: [starTime, new Date()],
    keyWord: '',
    level: 'info',
    scroll: true, // 是否自动滚动
    timeStamp: 0,
    isManualMode: true, // 手动模式
    lineNum: 0 // 行数
  };
  private showDownloadDialog = false;

  created() {
    if (!this.flowId) return;
    this.resetWebsocket();
    this.log && (this.log.innerHTML = '');
    this.config.level = 'info';
    this.config.scroll = true;
    this.config.isManualMode = true;
    this.config.dateTime = [starTime, new Date()];
  }

  destroyed() {
    this.resetWebsocket();
  }
  resetWebsocket() {
    this.socket = null;
    this.config.level = 'info';
    this.config.scroll = true;
    this.config.lineNum = 0;
    this.config.timeStamp = 0;
  }
  handleClick(type: string) {
    switch (type) {
      case 'search':
        this.getLogData();
        break;
      case 'export':
        this.config.dateTime ? (this.showDownloadDialog = true) : this.$tip.warning(this.$t('pa.flow.msg224'));
        break;
      case 'stop':
        this.socket = null;
        break;
      default:
        break;
    }
  }
  async getLogData() {
    try {
      this.loading = true;
      const { success, data, error } = await get(URL_LOG_REPLAY, this.generateParams(this.config));
      this.config.timeStamp === 0 && this.log && (this.log.innerHTML = '');
      if (success) {
        this.loading = false;
        if (Array.isArray(data.logs)) {
          if (data.logs.length === 1) {
            this.$tip.warning(this.$t('pa.flow.msg225'));
          }
          data.logs.forEach((el) => this.printLog(el));
        }
        this.config.timeStamp = data?.next;
        return;
      }
      this.renderMsg(error);
      this.loading = false;
    } catch {
      this.loading = false;
    }
  }
  generateParams(data: any) {
    return {
      begTime: dayjs(data.dateTime[0]).format('YYYY-MM-DD HH:mm'),
      endTime: dayjs(data.dateTime[1]).format('YYYY-MM-DD HH:mm'),
      jobId: this.flowId,
      keyWord: data.keyWord,
      level: data.level,
      timeStamp: data.timeStamp || 0
    };
  }
  handleChange() {
    this.resetWebsocket();
    if (!this.config.isManualMode) {
      this.log && (this.log.innerHTML = '');
      this.initLogSocket();
    } else {
      this.config.timeStamp = 0;
    }
  }
  initLogSocket() {
    this.socket = webSocket(`${this.getSocketUrl()}/flowWs/${this.flowId}/${this.config.level}`);
    this.socket && this.socket.next({ first: true });
    this.socket.subscribe((res) => {
      if (res.message) {
        let throwableInfo = '';
        if (Array.isArray(res?.throwableInfo?.throwableStrRep)) {
          res.throwableInfo.throwableStrRep.forEach((n) => {
            throwableInfo = `${throwableInfo}\n${n}`;
          });
        }
        this.printLog(res);
      }
    });
  }
  getSocketUrl() {
    return `ws://${window.location.hostname}:${window.location.port}${
      process.env.NODE_ENV === 'development' ? baseUrl.prev : window.location.pathname.replace('/pipeace.html', '')
    }`;
  }
  printLog(log: string) {
    if (this.config.lineNum > 500) {
      this.log.firstElementChild && this.log.removeChild(this.log.firstElementChild);
    } else {
      this.config.lineNum++;
    }
    this.renderMsg(log);
  }

  renderMsg(msg: string) {
    if (!this.log) return;
    const div = document.createElement('div');
    div.innerHTML = msg;
    div.style.color = this.config.level !== 'info' ? 'red' : 'black';
    this.log.appendChild(div);
    this.config.scroll && (this.log.scrollTop = this.log.scrollHeight);
  }
}
</script>
<style lang="scss" scoped>
.log {
  &__container {
    height: calc(100% - 40px);
  }
  &-content {
    padding: 0 16px;
    height: calc(100% - 80px);
    font-size: 14px;
    color: #666666;
    overflow: hidden;

    &__detail {
      padding: 8px;
      height: calc(100% - 30px);
      border: 1px solid gray;
      line-height: 1.5;
      overflow-x: auto;
      overflow-y: auto;
    }
  }
}
</style>
