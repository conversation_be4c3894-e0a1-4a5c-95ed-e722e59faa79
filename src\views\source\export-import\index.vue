<template>
  <pro-page v-loading="loading" :title="$t('pa.data.text15')" fixed-header>
    <div class="impExp">
      <div v-access="'PA.RES.PRIVATE.TRANSPORT.MENU'" class="impExp-content">
        <div class="impExp-card" style="margin-right: 64px">
          <div class="impExp-card-content">
            <div class="impExp-card-text">
              <div class="impExp-card-text--title">{{ $t('pa.data.text16') }}</div>
              <div class="impExp-card-text--memo">{{ $t('pa.data.text17') }}</div>
            </div>
            <svg-icon class="svg-impExp" name="import" />
          </div>
          <bs-upload
            class="upload"
            accept=".zip"
            :headers="headers"
            :action="importAction()"
            :show-file-list="false"
            :before-upload="handleBeforeUpload"
            :on-success="handleSuccess"
            :on-error="handleError"
          >
            <el-button
              v-access="'PA.RES.PRIVATE.IMPORT'"
              class="icon icon-xiazai"
              :class="isEn ? 'icon__en' : ''"
              type="primary"
            >
              {{ $t('pa.data.text13') }}
            </el-button>
          </bs-upload>
        </div>
        <div class="impExp-card">
          <div class="impExp-card-content">
            <div class="impExp-card-text">
              <div class="impExp-card-text--title">{{ $t('pa.data.text14') }}</div>
              <div class="impExp-card-text--memo">{{ exportText }}</div>
            </div>
            <svg-icon class="svg-impExp" name="export" />
          </div>
          <el-button
            v-access="'PA.RES.PRIVATE.EXPORT'"
            type="primary"
            class="icon icon-shangchuan"
            :class="isEn ? 'icon__en' : ''"
            @click="showExp"
          >
            {{ $t('pa.data.text21') }}
          </el-button>
        </div>
      </div>
      <div class="impExp-tip-content">
        <div class="tip-header"><svg-icon class="tip-header-svg" :name="tipSvgName" />{{ $t('pa.data.text18') }}</div>
        <ol class="tip-content">
          <li>{{ $t('pa.data.text19') }}</li>
          <li>{{ $t('pa.data.text20') }}</li>
          <li>
            {{ $t('pa.data.text22') }}
            <router-link v-if="hasWarehousePermission" to="/element/warehouse">{{ warehouse }}</router-link>
            <span v-else>{{ warehouse }}</span>
            {{ $t('pa.data.text23') }}
          </li>
          <li>
            {{ $t('pa.data.text24') }}
            <router-link v-if="hasSettingPermission" to="/assets/setting">{{ $t('pa.data.text25') }}</router-link>
            <span v-else>{{ $t('pa.data.text25') }}</span>
            {{ $t('pa.data.text26') }}
          </li>
        </ol>
      </div>
    </div>
    <ExportDialog v-if="exportDialogVisible" :visible.sync="exportDialogVisible" />
    <ImportDialog v-if="importDialogVisible" :visible.sync="importDialogVisible" :data="previewData" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Inject, Vue } from 'vue-property-decorator';
import { baseUrl } from '@/config';
import { getToken, hasPermission } from '@/utils';
import { IPreviewData } from './interface';
import { IMP_PREVIEW } from '@/apis/impExpApi';
import { invalidRequestHandler } from '@/apis/utils/http-base';
import { getLanguage } from '@/i18n';
@Component({
  components: {
    ImportDialog: () => import('./modals/import-dialog.vue'),
    ExportDialog: () => import('./modals/export-dialog.vue')
  }
})
export default class ExpImp extends Vue {
  @Inject('enableSql') enableSql;
  exportDialogVisible = false;
  loading = false;
  headers = { Authorization: getToken() };
  importDialogVisible = false;
  previewData: IPreviewData = {
    dataPreviews: [],
    count: 0,
    impId: '',
    statisticsDetails: []
  };
  warehouse = this.$t('pa.componentLibMgr');

  get exportText() {
    return this.enableSql ? this.$t('pa.data.text27') : this.$t('pa.data.text28');
  }
  get tipSvgName() {
    return (
      {
        'zh-CN': 'tips',
        'zh-HK': 'tips-hk',
        'en-US': 'tips-en'
      }[getLanguage()] || 'tips'
    );
  }

  get hasWarehousePermission() {
    return hasPermission('PA.ELE.WAREHOUSE.MENU');
  }

  get hasSettingPermission() {
    return hasPermission('ASSETS.SETTING');
  }

  showExp() {
    this.exportDialogVisible = true;
  }
  // 上传文件接口
  importAction() {
    return baseUrl.prev + IMP_PREVIEW;
  }
  // 上传前校验文件类型是否是zip文件
  handleBeforeUpload(file) {
    let isCorrectFile = file.name.split('.')[file.name.split('.').length - 1] === 'zip';
    isCorrectFile ? (this.loading = true) : this.$message.error(this.$t('pa.data.text12'));
    return isCorrectFile;
  }
  handleSuccess(rsp) {
    this.loading = false;
    const { success, error, msg, data } = rsp;
    if (!success) return this.$message.error(msg || error);
    this.importDialogVisible = true;
    this.previewData = data;
  }
  handleError(err: any) {
    if (err.status === 401) {
      // 重新登录
      invalidRequestHandler(true);
    } else {
      this.$message.error(JSON.parse(err.message).msg || JSON.parse(err.message).error);
    }
    this.loading = false;
  }
}
</script>
<style lang="scss" scoped>
.impExp {
  height: calc(100vh - 175px);
  padding-top: 2%;
  padding-bottom: 2%;
  overflow: auto;
  background: #fff;
  &-content {
    display: flex;
    justify-content: center;
    align-items: stretch;
    .impExp-card {
      display: inline-flex;
      flex-direction: column;
      justify-content: space-between;
      min-height: 200px;
      width: 44%;
      max-width: 540px;
      background: linear-gradient(to bottom, rgba(55, 124, 255, 0.06), rgba(55, 124, 255, 0.02));
      border: 1px solid rgba(55, 124, 255, 0.2);
      padding: 20px 55px;
      border-radius: 8px;
      &:hover {
        background: rgba(55, 124, 255, 0.1);
        border: 2px solid rgba(55, 124, 255, 1);
        box-shadow: 2px 2px 20px 0px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
      }
      &-content {
        display: flex;
        min-height: 120px;
      }
      &-text {
        flex: 1;
        padding-bottom: 10px;
        &--title {
          font-size: 16px;
          color: #001757;
          font-weight: 500;
        }
        &--memo {
          margin-top: 22px;
          font-size: 14px;
          color: rgba(0, 23, 87, 0.6);
          font-weight: 400;
        }
      }
      .svg-impExp {
        width: 83px;
        height: 81px;
        margin-left: 38px;
      }
      .icon {
        width: 120px;
        font-family: 'iconfont' !important;
        font-size: 14px;
        border-radius: 20px;
        border: 0;
        &__en {
          width: 150px;
        }
      }
      .upload {
        ::v-deep .el-upload {
          width: 120px;
        }
      }
    }
  }
  &-tip-content {
    width: calc(88% + 64px);
    max-width: 1144px;
    margin: 5vh auto 0;
    padding: 30px 78px;
    background: #f7f9fb;
    border-radius: 8px;
    .tip-header {
      font-size: 14px;
      color: #444444;
      font-weight: 600;
      &-svg {
        width: 86px;
        height: 20px;
      }
    }
    .tip-content {
      list-style: none;
      margin-top: 16px;
      li {
        line-height: 26px;
      }
    }
  }
}
::v-deep .el-card__body {
  padding: 0;
}
</style>
