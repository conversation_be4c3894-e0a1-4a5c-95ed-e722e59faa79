<template>
  <pro-page v-loading="loading" title="导入导出" fixed-header class="import-export">
    <el-card v-if="hasFeatureAuthority('PA.RES.PRIVATE.TRANSPORT.MENU')" class="export-card">
      <div class="export-text"><b>数据导出：</b>{{ exportText }}</div>
      <el-button v-access="'PA.RES.PRIVATE.EXPORT'" class="icon icon-xiazai" @click="showExp">
        导出
      </el-button>
    </el-card>
    <el-card v-if="hasFeatureAuthority('PA.RES.PRIVATE.TRANSPORT.MENU')" class="import-card">
      <div class="import-text"><b>数据导入：</b>将备份包中的数据增量导入到平台中。</div>
      <Upload
        class="upload"
        :headers="headers"
        :action="importAction()"
        :show-upload-list="false"
        accept=".zip"
        :on-success="handleSuccess"
        :before-upload="spinShow"
      >
        <el-button v-access="'PA.RES.PRIVATE.IMPORT'" class="icon icon-shangchuan"> 导入</el-button>
      </Upload>
    </el-card>
    <export-modal :visible="expVisible" @close="closeDialog" />
    <import-modal :visible="impVisible" :data="impData" @close="closeDialog" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Inject, InjectReactive } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { baseUrl } from '@/config';
import { URL_EXPIMP_IMPORTUPLOAD } from '@/apis/commonApi';
@Component({
  components: {
    'export-modal': () => import('./modals/export.vue'),
    'import-modal': () => import('./modals/import.vue')
  }
})
export default class ExpImp extends PaBase {
  @Inject('enableSql') enableSql;
  @InjectReactive('isFuseMode') isFuseMode!: boolean;
  expVisible = false;
  impVisible = false;
  loading = false;
  exportText = '将平台中的流程';
  impData: any = { asset: [], job: [], table: [], view: [], item: [] };
  headers = { Authorization: sessionStorage.getItem('token') };

  created() {
    this.getExportText();
  }
  getExportText() {
    if (!this.isFuseMode) {
      this.exportText += '、数据定义、方法、第三方类库';
    }
    if (this.enableSql) {
      this.exportText += '、表、视图、选项';
    }
    this.exportText += '导出。';
  }
  showExp() {
    this.expVisible = true;
  }
  closeDialog() {
    this.expVisible = false;
    this.impVisible = false;
  }
  importAction() {
    return baseUrl.prev + URL_EXPIMP_IMPORTUPLOAD;
  }

  spinShow() {
    this.loading = true;
  }

  handleSuccess(rsp) {
    this.loading = false;
    this.parseResponse(rsp, () => {
      const { asset, job, table, view, item, impId } = rsp.data;
      this.impData.impId = impId;
      this.impData.job = job;
      if (!this.isFuseMode) {
        this.impData.asset = asset;
      }
      if (this.enableSql) {
        this.impData.table = table;
        this.impData.view = view;
        this.impData.item = item;
      }
      this.impVisible = true;
    });
  }
}
</script>
<style scoped>
.import-text,
.export-text {
  font-size: 20px;
}
.upload {
  display: inline-block;
}
.export-card {
  margin: 100px 0px 20px 100px;
  width: 70%;
}
.import-card {
  margin: 0px 0px 0px 100px;
  width: 70%;
}
.import-export {
  background: #fff;
  height: calc(100vh - 114px);
}
.icon {
  font-family: 'iconfont' !important;
  font-size: 14px;
  color: #777;
  font-style: normal;
  border-color: #ccc;
}
</style>
