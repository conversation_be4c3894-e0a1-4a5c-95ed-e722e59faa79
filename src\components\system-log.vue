<template>
  <div>
    <bs-dialog
      title="日志"
      :visible.sync="systemLogVisible"
      width="90%"
      :footer-visible="false"
      @close="closeSystemLog"
    >
      <pre class="system-log__pre">
        {{ this.log }}
      </pre>
    </bs-dialog>
  </div>
</template>

<script lang="ts">
import { Vue, Component, PropSync, Prop } from 'vue-property-decorator';
@Component
export default class SystemLog extends Vue {
  @Prop({ type: String, default: '' }) log!: string;
  @PropSync('visible', { default: false, type: Boolean }) systemLogVisible;
  closeSystemLog() {
    this.systemLogVisible = false;
  }
}
</script>

<style lang="scss" scoped>
.system-log__pre {
  height: 500px;
  overflow: auto;
  color: #000000;
  font-size: 14px;
  text-align: left;
  line-height: 20px;
}
</style>
