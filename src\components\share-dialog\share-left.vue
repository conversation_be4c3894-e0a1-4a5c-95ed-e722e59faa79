<template>
  <div v-loading="loading" class="share-left">
    <el-tabs :value="activeTab" @tab-click="tabChange">
      <el-tab-pane v-for="tab in tabPaneList" :key="tab.name" :label="tab.label" :name="tab.name">
        <bs-search
          v-model="tab.keyword"
          :placeholder="$t('pa.placeholder.orgNameOrId')"
          class="share-left__search"
          @search="handleSearch"
        />
        <el-checkbox
          v-if="tab.treeData.length"
          v-model="tab.checkAll"
          :indeterminate="tab.indeterminate"
          class="share-left__checkAll"
          @change="handleCheckAllChange"
        >
          {{ $t('pa.action.allSelect') }}
        </el-checkbox>
        <el-tree
          ref="treeRef"
          :empty-text="$t('pa.noData')"
          :data="tab.treeData"
          show-checkbox
          default-expand-all
          node-key="id"
          :props="defaultProps"
          check-strictly
          :filter-node-method="filterNode"
          @node-click="nodeClick"
          @check-change="handleCheckChange"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop, PropSync, Ref, Watch } from 'vue-property-decorator';
import Tree from 'bs-ui-pro/lib/tree';
import { includesPro, getOrgMap } from './utils';
import { get } from '@/apis/utils/net';
@Component
export default class ShareLeft extends Vue {
  @PropSync('curTab', { default: '' }) activeTab!: string;
  @Prop({ default: 'server', type: String }) type;
  @Prop({ default: '', type: String }) resId;
  @Ref('treeRef') readonly tree!: Tree;
  defaultProps = {
    children: 'childrenShareSysOrg',
    label: 'name'
  };
  loading = false;

  // TAB配置 全部机构\已分享机构
  tabPaneList = [
    {
      label: this.$t('pa.allOrg'),
      name: 'allOrg',
      keyword: '',
      treeData: [],
      checkAll: false,
      indeterminate: false // 复选框样式
    },
    {
      label: this.$t('pa.sharedOrg'),
      name: 'sharedOrg',
      keyword: '',
      treeData: [],
      checkAll: false,
      indeterminate: false
    }
  ];

  @Watch('resId', { immediate: true })
  resIdChange(resId) {
    if (resId) {
      this.getAllOrgList();
      this.getSharedOrgList();
    }
  }

  async getAllOrgList() {
    try {
      this.loading = true;
      const { data, success, msg, error } = await get(getOrgMap[this.type], {
        [this.type === 'catalog' ? 'catalogId' : 'resId']: this.resId,
        type: 'ALL'
      });
      this.loading = false;
      if (!success) return this.$message.error(error || msg);
      this.$set(this.tabPaneList[0], 'treeData', data);
    } catch (e) {
      this.loading = false;
    }
  }

  async getSharedOrgList() {
    try {
      this.loading = true;
      const { data, success, msg, error } = await get(getOrgMap[this.type], {
        [this.type === 'catalog' ? 'catalogId' : 'resId']: this.resId,
        type: 'SHARE'
      });
      this.loading = false;
      if (!success) return this.$message.error(error || msg);
      this.$set(this.tabPaneList[1], 'treeData', data);
    } catch (e) {
      this.loading = false;
    }
  }

  tabChange(tab) {
    if (this.activeTab === tab.name) return;
    this.activeTab = tab.name;
  }

  // 触发搜索
  handleSearch(keyword: string) {
    this.tree[this.activeTab === 'allOrg' ? 0 : 1].filter(keyword);
    this.setCheckAll();
  }

  /* 全选、反选 */
  handleCheckAllChange(checked) {
    const ids = this.getShowIds(this.tabPaneList[this.activeTab === 'allOrg' ? 0 : 1].treeData);
    if (ids.length > 1000) return this.$message.warning(this.$t('pa.tip.maxLength1000'));
    ids.forEach((id) => {
      this.tree[this.activeTab === 'allOrg' ? 0 : 1].setChecked(id, checked);
    });
  }
  //获取当前显示的树id
  getShowIds(data, ids: any = []) {
    data.forEach((item) => {
      const node = this.tree[this.activeTab === 'allOrg' ? 0 : 1].getNode(item);
      node.visible && ids.push(item.id);
      Array.isArray(item.childrenShareSysOrg) && this.getShowIds(item.childrenShareSysOrg, ids);
    });
    return ids;
  }

  filterNode(value, data) {
    if (!value) return true;
    return includesPro(data.name, value) || includesPro(data.id, value);
  }

  handleCheckChange(data) {
    const checkedNode = this.tree[this.activeTab === 'allOrg' ? 0 : 1].getCheckedNodes();
    if (checkedNode.length > 1000) {
      this.tree[this.activeTab === 'allOrg' ? 0 : 1].setChecked(data, false);
      return this.$message.warning(this.$t('pa.tip.maxLength1000'));
    }
    this.setCheckAll();
    this.$emit('checkChange', checkedNode);
  }

  setCheckAll() {
    const checkedNode = this.tree[this.activeTab === 'allOrg' ? 0 : 1].getCheckedNodes();
    const total = this.getShowIds(this.tabPaneList[this.activeTab === 'allOrg' ? 0 : 1].treeData).length;
    this.tabPaneList[this.activeTab === 'allOrg' ? 0 : 1].checkAll = checkedNode.length === total;
    this.tabPaneList[this.activeTab === 'allOrg' ? 0 : 1].indeterminate =
      checkedNode.length < total && checkedNode.length > 0;
  }

  nodeClick(data) {
    this.$emit('nodeClick', data);
  }

  closeTag(id) {
    this.tree[this.activeTab === 'allOrg' ? 0 : 1].setChecked(id, false);
  }
}
</script>

<style lang="scss" scoped>
.share-left {
  &__search {
    margin: 0 6px;
    width: 100% !important;
    ::v-deep .el-input {
      width: 100% !important;
    }
  }
  &__checkAll {
    margin: 20px 24px 0;
  }
  ::v-deep .el-tabs {
    &--top {
      height: 100%;
    }
    &__header {
      margin: unset;
      height: 60px;
      border-bottom: 1px solid $--bs-color-border-lighter;
    }
    &__nav {
      height: 60px;
      margin-left: 42px;
    }
    &__item {
      height: 60px;
      line-height: 60px;
      font-weight: 600;
    }
    &__item:not(.is-active) {
      color: $--bs-color-text-secondary;
      font-weight: 400;
    }
    &__content {
      height: calc(100% - 60px);
      overflow: unset !important;
      padding: 16px 14px;
    }
  }
  ::v-deep .el-tree {
    padding: 0;
  }
}
</style>
