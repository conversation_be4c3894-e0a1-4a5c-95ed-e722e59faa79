import cloneDeep from 'lodash/cloneDeep';

export const componentConfigPreTreated = (componentList: any[], content: any) => {
  if (!content || !Array.isArray(content.nodes)) return;
  const componentMaps = getComponentInfo(componentList, content);
  content.nodes.forEach((item: any) => {
    const { className, properties } = item;
    const { isChange, rules, hideElMaping }: any = componentMaps[className] || {};
    if (isChange) {
      const propertiesCopy = cloneDeep(properties) || {};
      const noValidList = rules.reduce((pre: any, next: any) => {
        const { model, hideEl } = next;
        if (!hideElMaping.includes(model)) {
          const value = propertiesCopy[model];
          if (hideEl && Array.isArray(hideEl[value])) {
            pre.push(...hideEl[value]);
          }
        }
        return pre;
      }, []);
      const validRules = rules.filter(({ model }: any) => !noValidList.includes(model));
      item.properties = validRules.reduce((pre: any, { model: key, defaultVal: value }: any) => {
        pre[key] = key in propertiesCopy ? propertiesCopy[key] : value;
        return pre;
      }, {});
    }
  });
  return content;
};
export const getComponentInfo = (componentList: any[], content: any) => {
  return componentList.reduce((pre: any, { paJobComponentList }: any) => {
    if (Array.isArray(paJobComponentList)) {
      paJobComponentList.forEach(({ className, properties, updateTime }: any) => {
        if (properties && JSON.parse(properties)) {
          const hideElMaping: any[] = [];
          const rules = JSON.parse(properties).forms.map((item: any) => {
            const { model, label, defaultVal, componentCfg } = item;
            const temp = { model, label, defaultVal };
            if (componentCfg && componentCfg.hideEl) {
              if (Object.keys(componentCfg.hideEl).length < 1) {
                delete componentCfg.hideEl;
              } else {
                temp['hideEl'] = componentCfg.hideEl;
                hideElMaping.concat(Object.values(componentCfg.hideEl).flat(2));
              }
            }
            return temp;
          });
          const node = content.nodes.find((o) => o.className === className);
          pre[className] = { rules, hideElMaping, isChange: true };
          if (node && node.updateTime) {
            pre[className].isChange = updateTime > (node.updateTime || 0);
          }
        }
      });
    }
    return pre;
  }, {});
};
