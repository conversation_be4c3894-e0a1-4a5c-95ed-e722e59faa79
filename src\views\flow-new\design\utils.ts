import cloneDeep from 'lodash/cloneDeep';
import { SESSION_STORAGE_KEY_FOR_COMPILEFLOWS } from '@/constant';
import { baseUrl } from '@/config';
const getComponentInfo = (componentList: any[], content: any) => {
  return componentList.reduce((pre: any, { paJobComponentList }: any) => {
    if (Array.isArray(paJobComponentList)) {
      paJobComponentList.forEach(({ className, properties, updateTime }: any) => {
        if (properties && JSON.parse(properties)) {
          const hideElMaping: any[] = [];
          const rules = JSON.parse(properties).forms.map((item: any) => {
            const { model, label, defaultVal, componentCfg } = item;
            const temp = { model, label, defaultVal };
            if (componentCfg && componentCfg.hideEl) {
              if (Object.keys(componentCfg.hideEl).length < 1) {
                delete componentCfg.hideEl;
              } else {
                temp['hideEl'] = componentCfg.hideEl;
                hideElMaping.concat(Object.values(componentCfg.hideEl).flat(2));
              }
            }
            return temp;
          });
          const node = content.nodes.find((o) => o.className === className);
          pre[className] = { rules, hideElMaping, isChange: true };
          if (node && node.updateTime) {
            pre[className].isChange = updateTime > (node.updateTime || 0);
          }
        }
      });
    }
    return pre;
  }, {});
};

export const componentConfigPreTreated = (componentList: any[], content: any) => {
  if (!content || !Array.isArray(content.nodes)) return;
  const componentMaps = getComponentInfo(componentList, content);
  content.nodes.forEach((item: any) => {
    const { className, properties } = item;
    const { isChange, rules, hideElMaping }: any = componentMaps[className] || {};
    if (isChange) {
      const propertiesCopy = cloneDeep(properties) || {};
      const noValidList = rules.reduce((pre: any, next: any) => {
        const { model, hideEl } = next;
        if (!hideElMaping.includes(model)) {
          const value = propertiesCopy[model];
          if (hideEl && Array.isArray(hideEl[value])) {
            pre.push(...hideEl[value]);
          }
        }
        return pre;
      }, []);
      const validRules = rules.filter(({ model }: any) => !noValidList.includes(model));
      item.properties = validRules.reduce((pre: any, { model: key, defaultVal: value }: any) => {
        pre[key] = key in propertiesCopy ? propertiesCopy[key] : value;
        return pre;
      }, {});
    }
  });
  return content;
};

// 获取最流程列表的组件实例
export const getLatestFlowListRef = ($children, flowId) => {
  if (!flowId) return $children[$children.length - 1].$refs.flowListRef;
  return ($children.find((vm: any) => vm.flowId === flowId) || {}).$refs.flowListRef;
};
// ============编译流程状态存储相关==============
class CompileFlowStore {
  key = '';
  constructor() {
    this.key = SESSION_STORAGE_KEY_FOR_COMPILEFLOWS;
  }
  // 获取正在编译中的流程
  getCompilingIds() {
    return JSON.parse(sessionStorage.getItem(this.key) || '[]');
  }
  // 流程是否处于正在编译中
  iscompiling(id) {
    return this.getCompilingIds().includes(id);
  }
  // 添加编译中的流程ID
  add(id) {
    const ids = this.getCompilingIds();
    sessionStorage.setItem(this.key, JSON.stringify([...new Set([...ids, id])]));
  }
  // 移除编译中的流程ID
  remove(id) {
    const ids = this.getCompilingIds();
    const idx = ids.indexOf(id);
    if (idx > -1) {
      ids.splice(idx, 1);
      sessionStorage.setItem(this.key, JSON.stringify(ids));
    }
  }
}
export const compileFlowStore = new CompileFlowStore();

// 转换flink sql代码编辑逐行保存信息 用于setCodeError方法调用
export const transformLineErrorInfo = (lineErrors) => {
  if (!Array.isArray(lineErrors)) return [];
  return lineErrors.map(({ errorMsg, sqlPos: { endColumn, endRow, startColumn, startRow } }) => ({
    range: { startLineNumber: startRow, startColumn, endLineNumber: endRow, endColumn },
    message: errorMsg
  }));
};

export const getSocketUrl = () => {
  return `ws://${window.location.hostname}:${window.location.port}${
    process.env.NODE_ENV === 'development' ? baseUrl.prev : window.location.pathname.replace('/pipeace.html', '')
  }`;
};
