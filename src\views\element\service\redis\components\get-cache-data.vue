<template>
  <div class="bs-detail-block">
    <div class="bs-detail__header padA20">
      <div class="bs-detail__header-title">缓存查询</div>
    </div>
    <div class="tab-content">
      <el-row>
        <el-col :span="8">
          <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="100px">
            <el-form-item label="database:" prop="database">
              <el-input-number v-model="formData.database" :min="0" :max="15" />
            </el-form-item>
            <el-form-item label="key:" prop="key">
              <el-input v-model="formData.key" style="width: 178px" />
            </el-form-item>
            <div style="text-align: center; margin-bottom: 20px">
              <el-button type="primary" :loading="loading" @click="submit('ruleForm')">
                查 询
              </el-button>
            </div>
          </el-form>
        </el-col>
        <el-col :span="16">
          <div style="padding: 10px 10px">
            <div>结果:</div>
            <div style="padding: 0px 30px">{{ result }}</div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject } from 'vue-property-decorator';
import { URL_RES_DETAIL_REDIS_GETCACHEDATA } from '@/apis/commonApi';
import * as _ from 'lodash';
import { PaBase } from '@/common/pipeace-base';
@Component({
  components: {}
})
export default class GetCacheData extends PaBase {
  detailData: any = {};
  loading = false;
  result = '';
  formData: any = {
    database: 0,
    key: ''
  };
  rules: any = {
    database: [{ required: true, message: '请输入database', trigger: 'blur' }],
    key: [{ required: true, message: '请输入key', trigger: 'blur' }]
  };
  @Inject('comParams') comParams;
  @Inject('comDetailRecord') comDetailRecord;
  created() {
    this.loadData(this.comDetailRecord.val || {});
  }
  submit(formName: string) {
    this.result = '加载中...';
    const form: any = this.$refs[formName];
    form.validate((valid: any) => {
      if (valid) {
        this.loading = true;
        this.doGet(URL_RES_DETAIL_REDIS_GETCACHEDATA, {
          params: {
            id: this.detailData.id,
            key: this.formData.key,
            database: this.formData.database
          }
        }).then((resp: any) => {
          this.parseResponse(resp, () => {
            this.result = resp.data;
            if (_.toString(this.result) === '') {
              this.result = '暂无数据';
            }
          });
          this.loading = false;
        });
      } else {
        this.$message.error('请检查输入内容');
        return false;
      }
    });
  }

  async loadData(data: any) {
    this.formData.database = 0;
    this.detailData = data;
  }
}
</script>
<style scoped></style>
