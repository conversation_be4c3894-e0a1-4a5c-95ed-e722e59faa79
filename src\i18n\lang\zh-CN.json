{"pa": {"AdvancedConnectorAttr": "高级连接器属性", "action": {"action": "操作", "add": "新建", "addNewRow": "添加一行", "addRow": "添加行", "allSelect": "全选", "analysis": "解析", "applyAll": "应用全部", "batchAdjust": "批量调整", "batchDel": "批量删除", "batchUAdd": "批量新增", "buildOneself": "自建", "cancel": "取消", "cancelAll": "全部取消", "close": "关闭", "confirm": "确认", "copy": "复制", "cover": "覆盖", "create": "创建", "dataClear": "清除无效数据", "del": "删除", "detail": "详情", "download": "下载", "downloadRelation": "下载引用关系", "edit": "编辑", "editCustom": "编辑[{0}]", "editIcon": "编辑图标", "editIconCustom": "编辑图标[{0}]", "enable": "启用", "execute": "执行", "export": "导出", "import": "导入", "makeSure": "确定", "mockSubmit": "模拟提交", "offline": "停止", "online": "启动", "output": "输出", "preview": "预览", "recycle": "回收", "refresh": "刷新", "register": "注册", "resourceAllocate": "资源分配", "rollBack": "回滚", "run": "运行", "save": "保存", "search": "查询", "selectAll": "全部选择", "share": "分享", "skip": "跳过", "staging": "暂存", "submit": "提交", "syncTopic": "同步集群Topic", "terminal": "终端", "test": "测试", "testConnect": "测试连接", "tip": "提示", "transfer": "转换", "update": "更新", "upload": "上传", "uploadFiles": "上传文件", "versionContrast": "版本比对", "view": "查看", "viewSourceCode": "查看源码", "viewVersion": "查看历史版本"}, "addOrg": "创建机构", "adjustCpu": "调整CPU", "adjustCpuTo": "调整CPU为:", "adjustMemoryMB": "调整内存(MB)", "adjustMemoryTo": "调整内存为:", "adjustSlots": "调整Slots", "adjustSlotsTo": "调整Slots为:", "advancedParams": "高级参数", "all": "全部", "allOrg": "全部机构", "allQueue": "全部队列", "argument": "参数", "argumentType": "参数类型", "assets": {"title1": "数据管理", "title2": "数据定义", "title3": "第三方类库", "title4": "方法", "title5": "导入导出"}, "attempts": "尝试次数", "backoffMultiplier": "增长乘数", "baseConnectorAttr": "基础连接器属性", "baseInfo": "基本信息", "baseInformation": "基本信息", "basicParams": "基本参数", "basisChange": "以下任一情况作为变更依据：1、配置的数据定义在平台内不存在；2、配置的数据定义与平台内相同全类名数据定义的内容不一致", "batch": "批量", "batchText0": "您确定要删除选中的 [${name}] ${num}条记录吗？", "batchText1": "您选中的 [${name}] ${num}条记录被关联，无法删除！[${name}] ${num}条记录没有关联，您确定要删除吗？", "batchText2": "您选中的 [${name}] ${num}条记录被关联，无法删除！", "blood": {"center": "居中", "dep": "依赖上游", "dynamic": "动态", "exitFullScreen": "退出全屏", "fit": "适应画布", "fullScreen": "全屏", "homologyInfo": "同源信息", "homologyNode": "同源节点", "nodeName": "节点名称", "nodeType": "节点类型", "queryNode": "查询节点", "relatedInfo": "关联信息", "searchblood": "查询血缘", "serviceType": "服务类型", "size": "实际尺寸", "tip1": "请在左侧选择查询节点，将以查询节点为中心点进行血缘探索", "tip10": "血缘关系中节点不同，但是其实际代表的物理集群服务地址+资源（例如kafka服务ip:port+topic）相同或有交集", "tip11": "暂无数据权限，不支持查看", "tip12": "当前节点已与查询节点形成循环关系，不支持收缩", "tip13": "单击查看节点详情", "tip14": "，双击将该节点作为查询节点", "tip2": "暂无数据权限，无法查询该节点血缘", "tip3": "画布中存在节点与查询节点形成循环，已自动展开环上节点的直接关联节点", "tip4": "静态服务（配置服务时指定具体资源）：请在资源输入框输入名称查询；", "tip5": "依赖上游服务（配置服务时使用上游字段值来指定具体资源）：请在资源输入框输入“依赖上游数据”关键词；", "tip6": "无资源服务（配置服务时未指定具体资源）：请在资源输入框输入“无“关键词", "tip7": "支持以流程（已发布）、表、Catalog、部分服务资源（静态、依赖上游、无资源）作为查询节点，暂不支持动态服务作为查询。服务查询提示：", "tip8": "支持以流程（已发布）、部分服务资源（静态、依赖上游、无资源）作为查询节点，暂不支持动态服务作为查询。服务查询提示：", "tip9": "查询条件不完整，请检查"}, "cacheInfo": "缓存信息", "cacheQuery": "缓存查询", "canNotSync": "无法同步，", "cataLog": {"test6": "Catalog详情", "text1": "catalog名称", "text2": "请输入catalog名称", "text3": "catalog名称需要以字母开头且只包含字母、数字与下划线", "text5": "Catalog管理"}, "catalogName": "Catalog名称", "checkLoading": "正在进行数据校验，请稍等...", "checkpointInterval": "checkpoint周期", "choseFile": "选择文件", "citationRelation": "引用关系", "closing": "关闭中", "cloudService": "云服务", "cluster": "集群", "clusterAddress": "集群地址", "clusterMemory": "集群总内存", "clusterName": "集群名称", "clusterRemainingMemory": "集群剩余内存", "clusterResidualCpu": "集群剩余CPU", "clusterResidualMemory": "集群总CPU", "clusterResidualSlots": "集群剩余slots", "clusterResourceUsage": "集群资源使用情况", "clusterSlots": "集群总slots", "clusterUsedCpu": "集群已使用CPU", "clusterUsedMemory": "集群已使用内存", "clusterUsedSlots": "集群已使用slots", "codeCompare": "源码比对", "codeOrName": "请输入编码或名称", "comments": "请输入描述", "company": "邦盛科技", "compareVersion": "对比版本：{0}", "componentLibMgr": "组件库管理", "confirmDialog": "字段【{0}】有对应的输入字段但未选中。点击确定将丢弃未选中配置，是否要继续提交？", "consume": "消费查看", "copies": "副本数", "cpFailed": "可容忍的检查点故障", "cpMinPause": "checkpoint最小停顿时间", "cpTimeout": "checkpoint超时时间", "cpUnaligned": "是否开启未对齐检查点", "cpuCores": "CPU核数", "creator": "创建人", "cronExpression": "cron表达式", "currentCpu": "当前剩余CPU", "currentMemory": "当前剩余内存（MB）", "currentSlots": "当前剩余slots", "customFields": "自定义字段", "customParams": "自定义参数", "data": {"baseFields": "基础字段", "codeView": "源码查看", "copyError": "复制失败", "copySuccess": "复制成功", "option": {"addOption": "新建选项", "optionName": "选项名称", "optionType": "选项类型", "placeholder": {"optionNamePlaceholder": "请输入选项名称", "optionTypePlaceholder": "请选择选项类型"}, "validateTip": "不能以数字开头"}, "seniorFields": "高级字段", "table": {"addTable": "新建表", "change": "变更", "copyTable": "复制表", "detail": {"addAttribute": "添加属性", "addField": "添加字段", "attribute": "属 性", "baseConnector": "基础连接器", "businessCaliber": "业务口径", "chineseName": "中文名", "columnCluster": "列簇", "connector": "连接器", "connectorNature": "连接器属性", "creatorPhone": "创建人电话", "databaseType": "数据库类型", "excess3": "多于3", "fieldClassify": "字段分类", "fieldName": "字段名", "fieldType": "字段类型", "lessThan2": "少于2", "partition": "分区", "placeholder": {"fieldPlaceholder1": "请输入字段名", "fieldPlaceholder2": "请在基础字段表内添加字段类型=“TIMESTAMP(3)”字段", "servicePlaceholder1": "请选择服务", "servicePlaceholder2": "请选择数据库类型", "tablePlaceholder1": "请选择前缀名", "tablePlaceholder2": "请填写表名", "tablePlaceholder3": "请选择后缀名", "tablePlaceholder4": "请输入业务口径"}, "primaryKey": "主键", "seniorConnector": "高级连接器", "serviceAddress": "服务地址", "serviceInfo": "服务信息", "serviceType": "服务类型", "sqlParsing": "SQL解析", "sqlPreview": "SQL预览", "sync": "同步", "tableFields": "表字段", "tableInfo": "表信息", "tableName": "表名", "template": "模板", "tips": {"delConfirm": "您确定要删除{0}字段吗？", "fieldRFepeat": "列表内存在{0}个重复字段，请删除不必要的字段", "hbaseTip1": "HBase服务类型：表字段只能设置1个主键", "hbaseTip2": "HBase服务类型：表字段必须要设置1个主键", "hbaseTip3": "HBase服务类型：表字段除主键行字段外，其他字段必须设置列簇", "hbaseTip4": "字段信息为必填项，请完善字段信息后重试", "leastOne": "字段信息或高级表字段需要至少填写一项", "linkerTip": "请填写连接器值", "needToDel": "请选择需要删除的字段", "noPermissions": "暂无当前{0}数据权限，请重新选择", "noServePermissions": "暂无当前服务数据权限，请重新选择", "notAllowedTip": "字段不允许{0}个", "notEmpty": "{0}不能为空", "repeat": "存在重复字段，请修改后提交", "repeatTips": "存在重复字段，请修改", "seniorLinkerTip1": "请填写高级连接器属性字段", "seniorLinkerTip2": "高级连接器属性字段属性值不能重复，请重试", "syncSuccess": "共同步{0}个字段", "templateTooltip": "不同{0}类型的模板会有差异，请按照{1}类型进行下载使用。", "type2": "上传文件不大于10MB"}, "value": "值", "zipPackage": "zip包"}, "editTable": "编辑表", "incomplete": "待完善", "placeholder": {"inputPlaceholder1": "请输入表名称，中文名称", "inputPlaceholder2": "请输入字段名，中文名", "inputPlaceholder3": "请选择水位线字段名", "inputPlaceholder4": "请选择水位线数值", "inputPlaceholder5": "请选择水位线单位", "inputPlaceholder6": "请输入处理时间字段名", "inputPlaceholder7": "请输入自定义字段名", "inputPlaceholder8": "高级字段不能为空", "select": "请选择服务类型"}, "tooltip": {"tooltip1": "表结构变更，请及时同步", "tooltip2": "flink内置字段，处理时间、水位线等", "tooltip3": "连接器高级属性配置"}}, "text1": "引用数量", "text10": "只能上传excel类型的文件!", "text11": "不可导入原因", "text12": "请上传zip格式文件", "text13": "点击导入", "text14": "资源导出", "text15": "资源导入导出", "text16": "资源导入", "text17": "将导出的资源文件中的数据增量导入到平台中。", "text18": "下列资产请在执行导入操作前完成准备，以提升导入效率：", "text19": "1、机构、用户、角色等数据请通过SQL方式导入导出 或 在导入环境重新创建", "text2": "引用当前资产的下游资产数量", "text20": "2、服务请确保导入前在导入环境完成注册（推荐服务类型、名称保持一致，以便系统自动匹配服务信息）", "text21": "点击导出", "text22": "3、流程组件请通过【", "text23": "】中的上传和下载功能", "text24": "4、方法、第三方类库、数据定义等底层资产请通过【", "text25": "数据管理-导入导出", "text26": "】功能", "text27": "将平台中的流程（含所属项目和目录）、选项、UDF、表、Catalog、SQL片段、模板导出。", "text28": "将平台中的流程（含所属项目和目录）、选项、模板导出。", "text29": "流程前缀", "text3": "你确定要删除{0}字段吗？", "text30": "流程后缀", "text31": "目录", "text32": "请选择资产类型", "text33": "请输入ID或名称搜索", "text34": "不可导入校验", "text35": "重复性校验", "text36": "下一步", "text37": "上一步", "text4": "不能上传超过10MB的文件!", "text5": "第二步：上传模板文件", "text7": "不同服务类型的模板会有差异，请按照服务类型进行下载使用。", "text8": "1.文件格式ZIP\n         2.大小不能超过10MB", "text9": "请上传文件", "udf": {"addUdf": "新建UDF", "detail": {"SyncAll": "全部同步", "addMode": "创建方式", "addPhone": "创建电话", "check": "校验", "correlationType": "关联方法", "downloadFile": "下载模板文件", "explain": "功能说明", "fieldSync": "字段同步", "individualTest": "单独方法测试", "manualWriting": "手动编写", "methodName": "方法名", "mockTest": "模拟环境测试", "param1": "参数1", "param2": "参数2", "placeholder": {"addTypePlaceholder": "请选择创建方式", "domainPlaceholder": "请填写域名", "explainPlaceholder": "1.入参描述  2.描述 3.功能说明", "methodNamePlaceholder": "请输入方法名搜索", "name": "请填写UDF名", "typeInputPlaceholder": "请填写类型", "typePlaceholder": "请选择类型", "updataPlaceholder": "请填写更新内容"}, "returnType": "返回类型", "savePrompt": "保存提示", "selectFile": "选择文件", "testData": "测试数据", "tips": {"StagingSuccess": "暂存成功", "addSuccess": "新建成功", "codeTip": "源码不能为空", "delConfirm": "确定删除此行？", "dominTip": "请输入英文", "downloadSteps1": "第一步：下载模板文件", "importCoverTip": "导入将覆盖之前的数据，是否覆盖？", "importTip": "注意:导入后原数据将被清空，请谨慎操作！", "nameTip": "UDF名称需要以字母开头且只包含字母、数字与下划线", "numberOfTests": "测试数据不允许超过5个", "onTests": "必须保留一行测试数据", "sourceTip": "关联方法作为静态方法添加在DefaultGlobalFunction类中。用户进行关联后可在UDF源码中以 DefaultGlobalFunction.关联方法名称(参数) 的方式使用。", "zipTip": "上传zip包的UDF需要提交后才能测试!"}, "type": "类型", "udfDomainName": "UDF主类全域名", "udfName": "UDF名", "uploadZip": "上传zip包"}, "editUdf": "编辑UDF", "placeholder": {"udfExplainPlaceholder": "请输入功能说明", "udfNamePlaceholder": "请输入UDF名，中文名", "udfPlaceholder": "请选择UDF类型"}, "submit": "已提交", "viewUdf": "查看UDF"}}, "dataDict": "数据字典", "dataPreview": "数据预览", "dataViewMode": "查看数据方式", "databaseName": "database名称", "defaultCode": "查询语句（{0}）", "delData": "请选择需要删除的数据", "delay": "固定重启延迟时间", "deployTimeout": "上线超时时间", "differentEncoding": "编码和上级编码不能相同", "disableOperatorChain": "是否打断算子链", "docs": "文档", "downloadFailed": "下载失败", "dsFlow": "Datestream流程", "editCategory": "编辑分类", "elementLoading": "配置加载中", "enableCheckPoint": "是否开启checkpoint", "encoding": "编码", "encodingPlaceholder": "请输入编码", "errorDetail": "错误信息详情", "evictedObject": "淘汰对象", "executionLog": "执行日志", "expiredObject": "过期对象", "failureRateDelay": "失败重启延迟时间", "failureRateInterval": "时间间隔", "failuresPerInterval": "重启次数", "fieldConfig": "字段配置", "fieldInfo": "字段信息", "fieldName": "字段名称", "fieldParams": "字段参数", "fieldType": "字段类型", "fields": "字段", "file": "文件", "filterConditionList": "过滤条件列表", "flink": "加工引擎", "flow": {"3mm": "最近三个月", "add": "增加", "addCondition": "增加条件", "addData": "添加数据", "addNextRow": "添加下一行", "addTest": "添加测试用例", "address": "地址", "all": "全部", "allClose": "全部关闭", "allOpen": "全部开启", "attr": "属性", "autoSaved": "已自动备份", "autoSaving": "自动备份中", "autoUpdateField": "可自动更新字段", "baseConnector": "基础连接器", "baseInfo": "​基础信息", "baseInfo1": "基本信息", "baseParam": "基本参数", "batch": "批量操作", "batchCancel": "取消批任务", "batchCancelConfirm": "确定要取消该批量操作吗？", "batchCancelFailed": "取消失败，任务已开始运行", "batchCancelPublish": "批量取消发布", "batchCancelSuccess": "取消成功", "batchKill": "强制停止", "batchKillConfirm": "确定要强制停止该批量操作吗？", "batchKillFailed": "强制停止失败", "batchKillSuccess": "强制停止成功", "batchMode": "批模式", "batchOffline": "批量停止", "batchOnline": "批量启动", "batchOperation": "批量操作", "batchOperationDetail": "批量操作详情", "batchOperationFailed": "批量操作添加失败", "batchOperationInfo": "批量任务", "batchOperationName": "批操作名称", "batchOperationNameLength": "长度在 1 到 50 个字符", "batchOperationNamePlaceholder": "请输入批操作名称", "batchOperationNameRequired": "请输入批操作名称", "batchOperationSuccess": "批量操作添加成功，可点击批量任务按钮查看", "batchPublish": "批量发布", "batchRestart": "批量重启", "batchSize": "批处理数量", "bingxingdu": "并行度", "bloodRelation": "血缘关系", "business": "业务口径", "cFlow": "流程复制", "cancel": "取消", "cancelAll": "全部取消", "cancelPublish": "取消发布", "canclePublish": "取消发布中", "canvas": "画布", "category": "类型", "chineseName": "中文名", "className": "全类名", "close": "关闭", "clusterTest": "测试集群", "clusterType": "集群类型", "code": "源码", "codeKey": "片段缩略词", "codeLibrary": "代码片段库", "codeName": "片段名称", "comConfig": "组件配置", "comInfo": "组件信息", "comLoading": "组件加载中", "commonConfig": "通用配置", "compile": "编译", "complier": "编译中", "component": "流程组件", "component1": "组件", "componentName": "组件名称", "componentNum": "组件数", "componentType": "组件类型", "condition": "条件", "config1": "配置", "config2": "条件配置", "config3": "配置条件", "config4": "其他配置", "configDetail": "配置明细", "configErr": "配置错误", "configMsg1": "的配置未完成！", "configMsg2": "的【", "configMsg3": "】字段未配置！", "configured": "已配置", "confirm": "确定", "connector": "连接器", "connectorAttr": "连接器属性", "connectorVal": "属性值", "copy": "复制", "copyFlow": "复制流程", "copyed": "已复制", "creatFlow": "新建流程", "createDir": "新建目录", "createOrg": "创建机构", "createProject": "新建项目", "createRow": "添加行", "createSubDir": "新建子目录", "createTime": "创建时间", "creater": "创建人", "csvTrans": "csv解析", "currentVersion": "当前版本", "customCron": "自定义：{0}", "customField": "自定义字段", "customSize": "自定义JAR包", "d": "日", "dDir": "删除目录", "dataMap": "数据集", "deadline": "截止时间", "defaultMethod": "默认方法", "del": "删除", "delFlow": "删除流程", "delMsg": "确认删除吗", "delMsg1": "您确定要删除该行数据吗?", "delMsg2": "您确定要删除选中的数据吗", "delProject": "删除项目", "deng": "等", "design": "流程设计", "detail": "查看详情", "dev": "开发", "eDir": "编辑目录", "eFlow": "编辑流程", "edit": "编辑", "editContent": "编辑区", "editProject": "编辑项目", "editSql": "编辑SQL片段", "editSuccess": "修改成功", "editTime": "修改时间", "editor": "修改人", "endDate": "结束日期", "endSession": "终止会话", "errorMessage": "错误信息", "exit": "退出批量", "export": "导出", "exportLog": "导出日志", "fDir": "父级目录", "fenqu": "分区", "field": "字段", "fieldConfig": "字段配置", "fieldInfo": "字段信息", "fieldMap": "字段映射", "fieldMapTitle": "字段映射配置", "fieldName": "字段名", "fieldName1": "字段名称", "fieldParam": "字段参数", "fieldTrans": "字段转换", "fieldTransMethod": "字段转换方式", "fieldType": "字段类型", "fieldTypeMap": "字段类型映射", "fieldUpdateTip": "字段自动更新提示", "file": "文件采集", "filter": "筛选", "filter1": "流程筛选", "flow": "流程", "flowMemo": "流程备注", "flowName": "流程名称", "flowRelation": "引用流程：", "flowRunInfo": "流程运行信息", "flowTest": "流程测试", "forceStop": "强制停止", "format": "YYYY年MM月DD日 HH:mm", "format1": "YYYY年MM月DD日", "from": "来自", "gen": "生成", "genSql": "表字段生成sql", "getBatchDetailFailed": "获取批量操作详情失败", "getBatchInfoFailed": "获取批量操作信息失败", "h": "时", "h1": "小时", "highParam": "高级参数", "hignConnector": "高级连接器", "hignLevel": "高级", "hignMode": "高级模式", "historyRun": "历史运行情况", "historyRun1": "历史运行", "hiveConfig": "HIVE配置参数", "http": "HTTP 输入", "indexField": "索引字段", "indicatorId": "指标ID", "indicatorName": "指标名称", "inputCompent": "输入组件", "inputField": "输入字段", "jarPkg": "流程jar包", "jdbcType": "数据库类型", "jsonTtrans": "json转换", "key": "键", "key1": "左流key字段", "key10": "计算引擎", "key11": "策略", "key12": "维度值", "key13": "推送字段", "key14": "参数名", "key15": "参数值", "key16": "定时模式", "key17": "表匹配方式", "key18": "指定唯一表", "key19": "动态表", "key2": "左流输出字段", "key20": "动态表名", "key21": "是否UPSERT", "key22": "更新字段", "key23": "批量大小", "key3": "右流key字段", "key4": "右流输出字段", "key5": "分区字段", "key6": "HIVE字段", "key7": "HIVE字段类型", "key8": "上游输入字段类型", "key9": "上游输入字段", "keyBy": "keyBy字段", "label1": "全‘&&’关系", "label10": "事件时间", "label11": "乱序时间", "label12": "过滤模板", "label13": "更新频率", "label14": "映射模板", "label15": "系统编号", "label16": "映射字段", "label17": "源代码值", "label18": "源代码含义", "label19": "标准代码值", "label2": "全‘||’关系", "label20": "标准代码含义", "label21": "系统编号字段", "label22": "路由模板", "label23": "左流配置", "label24": "key字段", "label25": "输出字段前缀", "label26": "右流配置", "label27": "join方式", "label28": "内连接", "label29": "时间下界", "label3": "自定义关系", "label30": "时间上界", "label31": "允许乱序时间", "label32": "时间类型", "label33": "左连接", "label34": "右连接", "label35": "窗口类型", "label36": "滑动窗口", "label37": "滚动窗口", "label38": "窗口大小", "label39": "滑动步长", "label4": "方法入参", "label40": "生成方式", "label41": "枚举值", "label42": "小数位", "label43": "数据范围", "label44": "字符类型", "label45": "时间范围", "label46": "输出方式", "label47": "模拟输出", "label48": "真实输出", "label49": "用例名称", "label5": "上游组件字段", "label50": "用例数据", "label51": "手动添加", "label52": "csv输入", "label53": "随机造数", "label54": "数据预览", "label55": "数据修改", "label56": "最大反压比", "label57": "checkpoint失败次数", "label58": "checkpoint最大时间", "label59": "checkpoint平均时间", "label6": "阈值", "label60": "taskManager最大使用内存", "label61": "jobManager最大使用内存", "label62": "GC次数", "label63": "流程运行时间", "label64": "最大输入流量", "label65": "最大输出流量", "label66": "数据定义全类名输出", "label67": "数据定义字段", "label68": "是否本地分区", "label69": "更新主键", "label7": "延时时间", "label70": "周期模式", "label71": "基于主键查询", "label72": "基于更新时间查询", "label73": "基于索引字段查询", "label74": "普通分页查询", "label75": "基于JDBC流式查询", "label76": "禁用", "label77": "跳过NULL值", "label78": "请选择是否启用跳过NULL值功能", "label79": "请选择跳过NULL值设置", "label8": "单位秒", "label80": "启用后，当输入字段值为NULL时，将跳过更新该字段，保持数据库中的原有值不变。仅在UPSERT模式下生效", "leaveMsg": "确定离开当前页面吗？", "level": "维度", "loadingTip": "拼命加载中...", "log": "日志", "logInfo": "日志信息", "logText": "输入输出日志", "logicConfig": "指标配置( {0} )", "logicConfig1": "指标配置", "lowData": "低质量数据", "m": "分", "m1": "分钟", "mFlow": "流程移动", "mainKey": "主键", "maxRow": "最大行数", "maxRunTime": "最大运行时间", "mei": "每", "meiD": "每日", "meiH": "每小时", "meiM": "每分钟", "meiS": "每秒", "memo": "描述", "memoPlaceholder": "请输入备注信息（可选）", "mgr": "流程管理", "mm": "月", "mm1": "本月", "mockTest": "模拟输入测试", "mode": "流程模式", "monitor": "流程监控", "monitorMgr": "监控管理", "monitorRule": "监控规则名称", "move": "移动", "moveFlow": "移动流程", "msg1": "请先保存再启动会话！", "msg10": "流程首次启动，或者修改kafka consumer group等信息后需要从头开始消费数据。", "msg100": "第({0})条记录的名称与其他字段名有重复", "msg101": "第({0})条记录未配置完整", "msg102": "第({0})条记录方法的参数未配置完整", "msg103": "请选择计算引擎", "msg104": "请选择命名空间", "msg105": "请选择维度", "msg106": "请选择指标名", "msg107": "请输入指标ID", "msg108": "指标ID不可重复", "msg109": "请选择需要删除的指标", "msg11": "流程重启，需要接着上次checkpoint记录的位置，如kafka上次的offset位置继续消费。", "msg110": "选择上游字段的某个字段作为维度值进行查询", "msg111": "选择上游字段的某个字段作为维度值进行查询，若是不选择则使用默认值", "msg112": "请配置指标", "msg113": "指定上游的某个字段作为报送的值，该值需是一个包含@type的JSONString字符串", "msg114": "指定推送生效的脚本列表，若是不指定则默认所有脚本都生效", "msg115": "请输入服务地址", "msg116": "请选择策略", "msg117": "请选则维度值", "msg118": "请进行指标配置", "msg119": "请输入批处理数量", "msg12": "即flink savepoint，用于暂停流程，流程重新启动，保证精准一次语义。", "msg120": "请输入超时时间", "msg121": "请输入存放指标查询结果的对应字段", "msg122": "结果字段不可重复", "msg123": "（已选择{0}项）", "msg124": "请配置规则表达式", "msg125": "第{0}条记录未配置完整", "msg126": "第{0}条记录的名称与其他字段名有重复", "msg127": "请选择数据库类型", "msg128": "请选择服务名称", "msg129": "请选择要查询的表名，若是基于正则匹配多个表名进行查询，可以写为${正则表达式}的格式，如${table[0-9]}，正则地查询对匹配的表不会动态发现", "msg130": "选择一些数据库的字段, 这些字段将会被查询并输出到下游", "msg131": "请选择输出字段", "msg132": "请输入where条件，可以为空", "msg133": "where条件不能输入?", "msg134": "请选择查询方式", "msg135": "请选择时间字段", "msg137": "单位毫秒，默认100 * 1000", "msg138": "请输入分页大小", "msg139": "指定查询的起始时间，非必填。合法的时间格式将会失去焦点后自动转换成时间戳。", "msg14": "确认删除这些流程吗?", "msg140": "请输入区间大小", "msg141": "请输入起始时间", "msg142": "请输入合法时间表达式", "msg143": "请选择索引字段", "msg144": "即每次查询的最大记录条数，默认100条", "msg145": "当数据库中没有新数据后，下一次查询的间隔时间.单位毫秒，默认为60000。", "msg146": "请输入间隔时间", "msg147": "游标重置模式", "msg148": "请选择表匹配方式", "msg149": "请选择表，并配置映射字段信息", "msg150": "请输入动态表名", "msg151": "表名支持支持时间宏，如使用时间宏则表名表达式必须用table_{yyyyMMdd}形式，匹配到的表的字段应完全一致", "msg152": "请选择是否UPSERT", "msg153": "若选择`否`，则为`insert`语句; 若选择`是`，则会根据主键进行`insert`或`update`，主键是自动获取的", "msg154": "指定update时更新的字段，未选择的字段将不会更新，默认更新所有字段。", "msg155": "每次写入数据库时的记录条数，默认100条", "msg156": "适当的增加批量大小可以增加数据的写入速度", "msg157": "超过该时间，若是数据量还未积攒到指定的批量大小，则会输出，单位为`秒`", "msg158": "文本内容有空格，请确认输入是否有误", "msg159": "请配置表对应的映射字段", "msg160": "注意：处理规则引用的方法中，如需输入固定参数，需要对特殊字符串进行转义，如\"需写为\\\\\"。", "msg161": "第{0}条记录方法的参数未配置完整", "msg162": "组件的输出字段=自定义字段+选中的上游输入字段", "msg163": "顺序:自定义字段>上游输入字段", "msg164": "请输入自定义字段", "msg165": "请填写自定义字段", "msg166": "标签不可以包含特殊字符，仅可以包含字母，数字，_-!@#￥%&*()", "msg167": "只能输入一个输出字段", "msg168": "不能输入重复字段", "msg169": "自定义字段不能与上游输入字段重复", "msg17": "确定无状态启动吗？", "msg170": "请输入全路径表名", "msg171": "请至少选择一项上游输入字段", "msg172": "请处理错误提示信息，再提交", "msg173": "基本参数中[{0}]所填字段名与字段配置中的字段有重复", "msg174": "基本参数中[数据库名/表名]与[全路径表名]，至少填写一个。", "msg175": "基本参数中[数据库名]和[表名]要么都填，要么都不填。", "msg176": "基本参数中[参数列名]、[自定义参数列名]，至少填写一个。", "msg177": "基本参数中所填字段名与字段配置中的字段有重复", "msg178": "请检查输入内容", "msg179": "请输入输出字段", "msg18": "您确定要{0}{1}{2}{3}个流程吗？", "msg180": "存在重复输出数据,请重新输入", "msg181": "请输入合法的逻辑关系！", "msg182": "条件数量已经达到上限", "msg183": "您确定要删除配置条件{0}吗?", "msg184": "请输入合法的逻辑表达式", "msg185": "条件{0}", "msg186": "逻辑关系不完整：{0}没有被写入表达式！", "msg187": "条件配置有误请检查条件配置项！", "msg188": "请选择上游组件字段", "msg189": "请输入阈值", "msg190": "长度在 1 到 50 个字符", "msg191": "{0}已经被删除", "msg192": "指定一个字段作为keyBy", "msg193": "时间语义", "msg194": "指定一个字段作为时间字段", "msg195": "最大允许乱序时间，单位秒", "msg196": "条件配置A", "msg197": "条件配置B", "msg198": "请输入延时时间", "msg199": "请选择延时时间", "msg2": "流程并未改变，不需要保存", "msg20": "流程最多支持100个节点", "msg200": "请选择时间语义", "msg201": "请输入乱序时间", "msg202": "请填写更新频率", "msg203": "选择上个节点输出的字段，作为json字段提取组件的输出", "msg204": "请填写输出字段", "msg205": "请在左侧输入您要转换的json", "msg206": "请填写左侧流的join字段", "msg207": "请填写左侧流的时间字段", "msg208": "请填写左侧流的输出字段的前缀", "msg209": "请填写左侧流的输出字段", "msg21": "流程{0}还没保存，确定离开吗？", "msg210": "请填写右侧流的join字段", "msg211": "请填写右侧流的时间字段", "msg212": "请填写右侧流的输出字段的前缀", "msg213": "请填写右侧流的输出字段", "msg214": "请选择流的join方式", "msg215": "请输入时间上界大小，单位秒", "msg216": "允许乱序的时间", "msg217": "允许乱序的时间,单位秒", "msg218": "请输入时间窗口大小，单位秒", "msg219": "请选择时间类型", "msg22": "请先保存再配置资源！", "msg220": "请输入名称，长度不超过150字符", "msg221": "名称中存在特殊字符{0}", "msg222": "请输入导出字段，以逗号分隔", "msg223": "请先选择日志类型", "msg224": "请先选择时间范围再进行下载", "msg225": "没有查询到任何信息，请点击继续查询获取更多", "msg226": "请选择用例", "msg227": "请输入小数位数", "msg228": "请输入时间范围", "msg229": "请选择字符类型", "msg23": "上游输入字段", "msg230": "请输入枚举值", "msg231": "请选择枚举值", "msg232": "请输入枚举值，如有多个请用英文逗号分隔", "msg233": "请输入数据范围", "msg234": "开始范围不能大于结束范围", "msg235": "不能为空", "msg236": "{0}测试数据不能为空", "msg237": "{0}不能为空", "msg238": "{0}用例数据不能为空", "msg239": "{0}造数逻辑不能为空", "msg24": "流程解析失败", "msg240": "请输入用例名称", "msg241": "请输入用例数据", "msg242": "请选择造数逻辑", "msg243": "请输入行数", "msg244": "执行中，请稍等...", "msg245": "用例{0}__测试结果", "msg246": "请先设置流程", "msg247": "确定删除【{0}】该条数据吗?", "msg248": "注：若项目内存在流程将无法删除，项目内无流程将会把项目和项目内所有目录一起删除", "msg249": "项目+目录最多支持{0}级。", "msg25": "字段信息格式错误", "msg250": "注：非开发状态的流程不能移动，系统已为您自动过滤", "msg251": "将{0}共{1}个流程移动至目标项目目录", "msg252": "将{0}共{1}个流程复制至目标项目目录，若选择多个项目目录，则每个项目目录下都会新增所选流程", "msg253": "{0}个流程移动成功", "msg254": "{0}个流程移动成功，{1}个流程移动失败，失败原因如下：", "msg255": "流程名称在目标项目中已存在：", "msg256": "流程状态为非开发：", "msg257": "{0}个流程移动失败，失败原因如下：", "msg258": "查看目录详情", "msg259": "流程发布失败!详细原因请点击画布行进行查看。", "msg26": "{0}保存提示", "msg260": "流程状态非开发，不可编辑", "msg261": "所选流程状态均为非开发，不可移动", "msg262": "确认删除该目录吗？", "msg263": "注：若目录下存在流程将无法删除，目录下无流程将会把目录和所有子目录一起删除", "msg264": "仅支持已发布的流程查看血缘关系", "msg265": "流程{0}有修改，请先保存再发布！", "msg266": "请在左侧流程管理选择一个流程查看，若无流程请先新建", "msg267": "只支持jar格式", "msg268": "请输入全类名", "msg269": "请上传流程jar包", "msg27": "{0}，请稍等...", "msg270": "该模式需要配置分布式文件系统", "msg271": "只能上传一个文件", "msg272": "只能上传jar类型文件", "msg273": "文件大小限制1GB", "msg274": "所有采集组件的处理数据量总和", "msg275": "所有采集组件的处理数据量总和/运行时长（秒）", "msg276": "所有组件（平均传输延时+平均处理延时）总和", "msg277": "批量设置组件输入输出日志开关", "msg278": "输入输出日志开关用于控制流程发布后是否在运行日志中打印流程节点的输入和输出数据信息，开启代表打印，关闭代表不打印。", "msg279": "组件{0}的【输出字段】和【输入字段】未配置完成！", "msg28": "正在努力生成sql脚本", "msg280": "组件{0}的【数据定义】未配置完成！", "msg281": "组件{0}的【字段映射】未配置完成！", "msg282": "组件{0}的【输入字段】中，{1}字段不存在！", "msg283": "表别名仅支持字母、数字和下划线", "msg284": "系统将获取所有目标表的字段并按规范生成sql。例如：画布已存在", "msg285": "配置表和别名（user别名为u，area别名为a）后点击生成，则会生成下列sql：", "msg286": "注意：建议对生成sql进行检查修改，以符合实际需求", "msg287": "快速获取所有目标表的字段并生成sql", "msg288": "{0}节点配置（组件：{1}）", "msg289": "组件的输出字段=选中的上游输入字段", "msg29": "请选择连接器和表用途", "msg290": "顺序:上游输入字段", "msg291": "表单项【{0}】解析validator函数报错: {1}", "msg292": "请选择上游输入字段", "msg293": "字段【{0}】有对应的输入字段但未选中。点击确定将丢弃未选中配置，是否要继续提交？", "msg294": "请选择数据定义", "msg295": "请输入字段搜索", "msg296": "开启后，将会把数据定义全类名作为“@type”字段的值一起输出；关闭则不输出", "msg297": "将上方选中的数据定义字段以JSON格式组装后放入该字段（全类名根据配置决定是否输出）", "msg298": "不可以包含特殊字符，仅可以包含字母、数字、中划线及下划线", "msg299": "（已选择{0}项作为输出内容）", "msg3": "保存中，请稍等...", "msg30": "编译中，请稍等...", "msg300": "请选择关联字段【{0}】的输入字段", "msg301": "衍生字段表达式：{0}，无需配置输入字段", "msg302": "请确保输出的衍生字段【{0}】的关联字段【{1}】选中", "msg303": "请选择数据定义字段【{0}】对应的输入字段", "msg304": "指定update时更新的字段，未选择的字段将不会更新，默认更新所有字段", "msg305": "使用本地分区方式会将数据直接写入分区表中", "msg306": "分区字段中存储了本地分区后的分区表名例如table_202407", "msg307": "请选择分区字段", "msg308": "请选择更新主键", "msg309": "请先配置该字段的映射关系", "msg31": "请输入流程名称", "msg310": "根据数据表的主键进行增量查询", "msg311": "需在业务侧保证修改的数据的更新时间字段为最新，能够读取到包括更新在内的增量数据；时间字段需建立索引才能加快查询效率，否则每次都会进行全表扫描", "msg312": "对于没有主键的表，基于索引字段查询，能够加快查询效率", "msg313": "使用分页查询，对于大数据量的表，越到后面查询速率越慢", "msg314": "使用JDBC流式查询，不提供状态保存的能力，适合用于全表迁移；对于数据量越大的表，所需内存越大。建议使用多并行度进行查询", "msg315": "请输入字段", "msg316": "该开关用于控制流程发布后是否在运行日志中打印流程节点的输入和输出数据信息，开启代表打印，关闭代表不打印。", "msg317": "灰色区域为上游输入字段，不可修改字段名称和类型", "msg318": "的名称或者类型被修改", "msg319": "第", "msg32": "请选择要操作的流程", "msg320": "条记录的参数类型与处理规则不符", "msg321": "条记录的的处理规则不存在", "msg33": "请选择要启动的流程", "msg34": "流程上线失败！", "msg35": "流程停止失败", "msg36": "流程上线失败", "msg37": "流程发布失败", "msg38": "流程发布失败!详细原因请点击画布中异常SQL代码行进行查看。", "msg39": "流程编译失败", "msg4": "请先保存再测试！", "msg41": "没有需要发布的流程", "msg42": "没有需要取消发布的流程", "msg43": "没有需要上线的流程", "msg44": "您确定要{0}【{1}】个流程吗？", "msg45": "没有需要停止的流程", "msg46": "请先启动会话", "msg47": "请选择测试集群", "msg48": "设置测试任务运行时间上限，当执行时间超过阈值时，自动终止测试任务；运行时间最大可设置为{0}秒。", "msg49": "设置测试任务执行结果的阈值上限，当测试任务产生的结果数据已达到阈值，则自动终止测试任务；行数最大可设置为{0}行。", "msg50": "是否确认回滚?", "msg51": "因上游字段的增删导致以下组件字段出现变更，请确定是否进行自动更新", "msg52": "红色字段：被删除的输出字段", "msg53": "绿色字段：增加的输出字段", "msg54": "红色字段：被删除的字段", "msg55": "绿色字段：增加的字段", "msg56": "当前组件使用到的上游输出字段，不包括可选字段", "msg57": "请输入大于0小于100的整数", "msg58": "名称中存在特殊字符", "msg59": "名称已存在", "msg60": "点击左侧“流程组件”开始流程开发", "msg61": "存在配置异常组件", "msg62": "超过最大连接数：【{0}】最多支持{1}个输入连接", "msg63": "组件【{0}】的输出字段和组件【{1}】输入字段不匹配。 {2} {3}", "msg64": "组件{0}{1}", "msg65": "{0}：【{1}】组件更新完将删除{2}个字段，请检查更新。", "msg66": "{0}为空：【{1}】组件更新完将删除{2}个字段，导致当前组件的{3}为空，请检查更新。", "msg67": "组件{0}的配置未完成！", "msg68": "组件{0}'的【表/视图】字段未配置！", "msg69": "组件{0}'的【视图】未选择！", "msg7": "确认删除该项目吗?", "msg70": "组件{0}'未配置完成", "msg71": "组件{0}'【源表】未选择！", "msg72": "组件{0}'【输出表】未选择！", "msg73": "组件{0}'【输出表字段】未选择！", "msg74": "请设置片段缩略词，以便于在编辑区编写代码时通过 缩略词+回车 快速使用SQL片段", "msg75": "请输入非空格字符", "msg76": "确定删除SQL片段？", "msg77": "首字符仅可输入字母/数字/./", "msg78": "代码片段库用于展示具有复用性的公用/个人SQL片段", "msg79": "是由系统管理员维护，提供的SQL片段，", "msg80": "系统所有用户可用。", "msg81": "系统用户维护、仅供个人使用的SQL片段。", "msg82": "请输入正确的键", "msg83": "至少选择一条数据", "msg84": "请选择字段【{0}】对应的输入字段", "msg85": "请选择主键和分区字段", "msg87": "仅支持如下格式的json语句：{\"key1\":\"value\",\"key2\":[1,2],\"key3\":{\"key4\":\"xx\"}}", "msg88": "请输入json", "msg89": "请输入解析内容", "msg90": "采用英文逗号分隔", "msg91": "字段{0}不存在", "msg92": "字段{0}重复", "msg93": "字段{0}已配置为{1}类型，再次配置将更新字段类型", "msg94": "至少保留一个类型映射行", "msg95": "最大支持新增20个类型映射行", "msg96": "请设置字段类型及数据精准度", "msg97": "请设置字段类型及字段信息", "msg98": "注意：处理规则引用的方法中，如需输入固定参数，需要对特殊字符串进行转义，如'需写为\\'。", "msg99": "请完善信息", "name": "名称", "nameSpace": "命名空间", "new": "新建", "new1": "新增", "newSql": "新建SQL片段", "no": "否", "noData": "暂无更多数据", "noStatusRun": "无状态启动", "nodeDetail": "节点详情", "nodeLabel1": "输入数据量", "nodeLabel10": "近一天数据处理量", "nodeLabel2": "输出数据量", "nodeLabel3": "失败数据量", "nodeLabel4": "平均传输延时", "nodeLabel5": "平均处理延时", "nodeLabel6": "预警数量", "nodeLabel7": "近一分钟数据处理量", "nodeLabel8": "近十分钟数据处理量", "nodeLabel9": "近一小时数据处理量", "notConfig": "未配置", "notInInputs": "不存在输入字段中。", "notInOutputs": "不存在输出字段中。", "numTotal": "数字总位数", "numTotal1": "小数点后位数", "online": "上线", "onlineMode": "间隔周期模式：{0}上线，每隔{1}小时{2}分执行", "onlineMode1": "时间周期模式：{0}上线，每天{1}运行", "onlineTime": "最新启动时间", "onlineTip": "一次性上线：{0}上线", "onlining": "启动中", "open": "开启", "operationStatus": "操作状态", "operationTarget": "操作对象", "operationTime": "操作时间", "operationType": "操作类型", "operator": "操作", "other": "其他", "output": "输出", "outputChange": "上游输出变更自动", "outputComponent": "输出组件", "outputFields": "输出字段", "pageSize": "分页大小", "paramMap": "参数映射", "paramType": "参数类型", "params": "参数", "params1": "参数{0}", "personCodeLibrary": "个人SQL片段库", "placeholder0": "请输入", "placeholder1": "请输入项目名称模糊搜索", "placeholder10": "请输入流程备注", "placeholder11": "请输入并行度", "placeholder12": "请输入最大运行时间", "placeholder13": "请输入最大行数", "placeholder14": "请输入标签、备注", "placeholder15": "请输入名称，长度不超过200字符", "placeholder16": "请输入片段缩略词", "placeholder17": "请选择输入字段", "placeholder18": "不输入默认使用英文逗号(,)", "placeholder19": "请选择字段", "placeholder2": "请输入流程名称、集群名称", "placeholder20": "请输入字段名称", "placeholder21": "请选择", "placeholder22": "请输入内容", "placeholder23": "请选择推送字段", "placeholder24": "请选择keyBy字段", "placeholder25": "存放指标查询结果的字段", "placeholder26": "指定table匹配方式，可指定唯一table或者通过时间表达式配置动态table", "placeholder27": "支持黏贴多个字段，回车后自动输入，默认采用英文逗号分隔", "placeholder28": "请选择方法", "placeholder29": "请选择过滤模板", "placeholder3": "请输入项目或目录", "placeholder30": "请选择映射模板", "placeholder31": "请选择系统编号字段", "placeholder32": "请选择映射字段", "placeholder33": "请选择路由模板", "placeholder34": "请填写输入字段", "placeholder35": "请输入时间下界大小，单位秒", "placeholder36": "请选择窗口类型", "placeholder37": "请输入滑动步长，单位秒", "placeholder38": "搜索多个关键字用逗号分隔", "placeholder39": "请选择日志类型", "placeholder4": "请输入查询关键字", "placeholder40": "请选择数据输出方式", "placeholder41": "请输入用例名称，长度不超过30字符", "placeholder42": "请输入流程名称查询", "placeholder43": "长度不超过30字，超过则不支持输入", "placeholder44": "请选择父级目录", "placeholder45": "请选择前缀", "placeholder46": "请选择后缀", "placeholder47": "请选择目标项目目录", "placeholder48": "请选择，可选择多个", "placeholder49": "表别名（选填）", "placeholder5": "请输入名称，长度不超过30字符", "placeholder50": "请输入任务名称模糊搜索", "placeholder51": "请输入作业名称模糊搜索", "placeholder6": "请输入备注", "placeholder7": "请输入名称", "placeholder8": "请输入流程名称，长度不超过30字符", "placeholder9": "请选择流程类型", "processComponent": "处理组件", "processed": "已处理", "prod": "已上线", "project": "项目", "projectInfo": "全部项目目录", "projectName": "项目名称", "pub": "已发布", "publicCodeLibrary": "公用SQL片段库", "publicMethod": "共享方法", "publish": "发布", "publishing": "发布中", "pushLogic": "推送配置", "qkey": "快捷键", "qt": "嵌套", "quanping": "全屏", "queryInterval": "区间大小", "queryStrategy": "查询方式", "rDir": "根目录", "random": "随机生成", "randomEnum": "随机生成-枚举值", "realTest": "真实输入测试", "refresh": "刷新", "relation": "引用关系", "relation1": "逻辑关系", "relationImg": "关系图", "reload": "重新上传", "remark": "备注", "reset": "重置", "resourceName": "资源名称", "result": "结果", "resultFields": "结果字段", "resultStr": "结果表达式", "resultView": "结果查看", "rollBack": "回滚", "rowNum": "行数", "rule": "处理规则", "ruleStr": "规则表达式", "run1": "执行", "runInfo": "运行信息", "runLog": "执行日志", "runStatus": "执行状态", "runTest": "运行测试", "s": "秒", "save": "保存", "scriptList": "脚本列表", "scroll": "滚动", "seach1": "搜索", "search": "查询", "search1": "继续查询", "searchAll": "全量搜索", "searchConfig": "查询配置", "searchResult": "个搜索结果", "select": "重新选择", "selectAll": "全选", "selectAll1": "选择全部", "selectAll2": "全部选择", "selectFile": "选择文件", "selectTable": "请选择表", "selectTimeRange": "选择时间范围", "selectableProject": "可选项目", "selected": "已选择", "selectedProject": "已选项目", "serve": "服务", "serveAddress": "服务地址", "serveInfo": "​服务信息", "serveName": "服务名称", "serveType": "服务类型", "serviceInfo": "全部服务信息", "session": "会话", "sessionConfig": "会话配置", "showAll": "显示全部", "showSelected": "只显示已选中", "showUnselected": "只显示未选中", "sortByProjectName": "按照项目名称首字母排序", "sortByUpdateTime": "按照更新时间倒叙排序", "sourseTable": "原始表", "splitStr": "分隔符", "sqlCode": "SQL代码", "sqlDetail": "SQL片段详情", "startDate": "开始日期", "startSession": "启动会话", "startTime": "起始时间", "status": "流程状态", "statusRun": "基于上次状态启动", "steam": "流", "stop": "停止", "stopTime": "最新停止时间", "stopWithStatus": "停止并保留状态", "stopping": "停止中", "str": "表达式", "streamingMode": "流模式", "tDir": "目标项目目录", "table": "表", "tableMgr": "表管理", "tableName": "表名", "tag": "标签", "target": "输出目标", "test": "测试", "testData": "测试数据", "testEp": "测试用例", "testResult": "测试结果", "tiao": "条", "timeField": "时间字段", "timeToRollForward": "时间前滚", "timeout": "超时时间", "tip": "提示", "title1": "资源配置({0})", "title2": "生成个人SQL片段", "title3": "造数逻辑", "title4": "执行提示信息", "title5": "【{0}】用例的日志", "total": "共", "trans": "转换", "type": "流程类型", "unknownError": "未知错误", "updateTime": "更新时间", "updater": "更新人", "use": "启用", "useUtil": "一键调用智能工具", "util": "智能工具", "val": "值", "version": "版本信息", "version1": "版本", "versionCompare": "版本比对", "view": "查看", "view1": "概览视图", "view2": "项目视图", "view3": "查看组件监控数据", "viewCondition": "查看条件", "waitOnline": "待启动", "waitTime": "间隔时间", "warnning": "预警", "waykey1": "先推再查", "waykey2": "先查询再推送", "waykey3": "仅查询", "waykey4": "仅推送", "week": "星期", "where": "where条件", "wu": "无", "xiang": "项", "y": "年", "yes": "是", "ys": "衍生", "zhi": "至"}, "flowDataSetChange": "流程{0}配置的数据定义发生变更，请确认是否继续？", "flowDetail": "流程详情：{0}", "flowInfo": "流程信息", "flowList": "流程列表", "flowName": "流程", "flowOnlineException": "流程上线异常，您可以在流程监控模块执行强制停止操作", "flowType": "服务资源类型未选择", "form": "来自：{0}", "formDesign": "表单设计", "format": "格式化", "freeMemory": "JVM堆内存", "freeSlots": "可用slot总数", "getConfFailed": "获取配置失败", "getMenuFailed": "获取菜单失败", "globalSearch": "全局搜索", "graph": "图形", "groupError": "表单项【{0}】缺少必要字段[group]，表单项无法显示", "groupInfoError": "表单项【{0}】group字段值[{1}]不存在分组信息中，表单项无法显示", "handleLoading": "正在{0}数据，请稍等...", "handleRule": "处理规则", "handleTime": "处理时间", "hbaseCode": "请点击左侧表名查询，最多查询20条数据", "historyVersion": "历史版本", "hiveCode": "请点击左侧表名或表字段，最多查询20条数据", "home": {"count": "数量", "data": "数据", "dataCount": "数据量", "distribution": "流程状态/数量分布", "engineMatter": "引擎资源使用情况", "flowTotal": "流程总量", "hdMillion": "亿", "individual": "个", "memory": "内存", "noPage": "无指定的服务列表页面", "regist": "服务注册量", "resource": "资源", "restart": "重启中", "running": "运行中", "service": "服务", "success": "成功", "text1": "当日流程数据处理情况", "text10": "重启中：流程正在重启", "text11": "未知：流程状态未知（因平台或加工引擎异常，无法获取到流程运行信息）", "text12": "已完成", "text13": "已失败", "text14": "已取消", "text15": "占比（%）", "text2": "当日数据处理总量", "text3": "当日流程数据处理量", "text4": "已完成发布的流程（流程状态为已发布或已上线）的运行状态分布情况，运行状态定义如下：", "text5": "未运行：流程已发布，但是未上线", "text6": "运行中：流程正在运行", "text7": "已完成：流程已完成（批模式或者有界流程完成后，会显示为已完成）", "text8": "已失败：流程运行失败", "text9": "已取消：流程被取消", "thousand": "万", "timeout": "查询超时，请", "title": "PA监控云盘", "tryAgain": "重试", "unused": "未使用", "updateTime": "数据更新时间", "used": "已使用", "warning": "待处理预警数"}, "homePage": "主页", "host": "主机", "hostName": "主机名", "iconFile": "图标文件", "indexCode": "请点击左侧index查询，最多查询20条数据", "initialBackoff": "初始间隔时间", "intervalPeriod": "间隔周期", "jdbc": "数据库", "jitterFactor": "振动因子", "jobManagerLimitCpu": "job manager CPU爆发倍数", "jobManagerLimitMemory": "job manager 内存爆发倍数", "jobManagerMemory": "job manager内存(MB)", "jobManagerRequestCpu": "job manager CPU", "jobMgrInfo": "JobManager信息", "jobMgrMemory": "Jobmanager 堆内存", "jobRunningRule": "任务运行规则", "jobTag": "请输入标签，长度不超过30字符", "key": "键", "labelError": "表单项【{0}】缺少必要字段[label]", "lagTotal": "lag总计", "length2to30": "长度在 2 到 30 个字符", "listView": "列表展示", "loading": "拼命加载中...", "loadingText": "数据正在加载中...", "location": "所属位置", "log": "日志", "log4jFile": "log4j配置", "logBack": "用户会话过期请重新登录", "logExpired": "登录过期，请重新登录", "logOutputKafka": "是否输出日志到Kafka", "logout": "退出登录", "managedMemory": "Flink管理的内存", "maxBackoff": "最大间隔时间", "memoryCpuSetting": "内存、CPU设置", "memoryRemain": "内存剩余", "memoryUsed": "内存使用", "menu": {"bloodRelation": "血缘关系", "dataDefine": "数据定义", "filterTemplate": "过滤模板", "flowMonitor": "流程监控", "mapLibrary": "映射字段库", "mapTemplate": "映射模板", "monitorWarn": "监控预警", "optionManage": "选项管理", "routeTemplate": "路由模板", "serviceMonitor": "服务监控", "sheetManage": "表管理", "sqlClip": "SQL片段", "sqlClipLib": "SQL片段库", "udf": "UDF管理"}, "method": "方法", "modeSelection": "模式选择", "monitor": {"flow": {"action": {"basedLastOnline": "基于上次状态启动", "basedLastTooltip": "流程重启，需要接着上次checkpoint记录的位置，如kafka上次的offset位置继续消费。", "batchOperationSuccess": "批量操作添加成功", "forceOffline": "强制停止", "leastOne": "请选择记录", "onlineLeast": "请选择要启动的流程", "restartForce": "一键重启", "retainOffline": "停止并保留状态", "retainTooltip": "即flink savepoint，用于暂停流程，流程重新启动，保证精准一次语义。", "statelessConfirm": "确定无状态启动吗？", "statelessOnline": "无状态启动", "statelessTooltip": "流程首次启动，或者修改kafka consumer group等信息后需要从头开始消费数据。"}}, "service": {"connect": "连接", "unConnect": "未连接"}, "text1": "控制触发规则预警后是否发送通知，设置不发送后仍然会触发预警并生成记录（平台内查看）。", "text10": "当预警规则状态为启用，则会在配置的生效时间内按照执行周期进行定时检查。执行周期通过cron表达式配置，cron表达式6位分别对应：秒/分/时/日/月/周，例如：‘0 */1 * * * ?’表示每分钟执行1次；‘30 30 8 * * ?’表示每天8点30分30秒执行1次", "text11": "生效时间用于设置系统对资源进行监控的时间范围，仅在每天该时间段内按执行周期对资源进行检查", "text12": "预警通知内容", "text13": "状态", "text14": "请输入生效时间", "text15": "请输入预警通知内容", "text16": "请选择处理状态", "text17": "未处理", "text18": "流程监控针对已完成发布的流程（流程状态为已发布或已上线），支持查看运行情况以及运维管理。运行状态定义如下：", "text19": "总计", "text2": "注意：通知功能需要系统对接，以实现短信、电话、群机器人等方式告知相关负责人", "text20": "流程运行监控", "text21": "请选择运行状态", "text22": "请输入项目目录、流程名称", "text23": "请选择预警状态", "text24": "请选择预警规则", "text25": "请选择资源类型", "text26": "当预警规则状态为启用，则会在配置的生效时间内按照执行周期进行定时检查。", "text27": "执行周期通过cron表达式配置，cron表达式6位分别对应：秒/分/时/日/月/周，例如：‘0", "text28": "*/1 * * * ?’表示每分钟执行1次；‘30 30 8 * * ?’表示每天8点30分30秒执行1次", "text29": "仅看未处理预警>0", "text3": "当资源检查触发预警规则时，通知下游系统的消息内容。可自定义内容文案，下列参数可作为动态变量使用：", "text30": "暂无数据权限", "text4": "title：资源名称；", "text5": "resType：资源类型；", "text6": "ruleType：预警规则；", "text7": "problem：发生问题的内容。", "text8": "例如kafka服务：testkafka连通失败，则会提示“资源名称：testkafka；资源类型：Kakfa；预警规则：连通性检查；发生问题：失败连接的节点:[10.100.1.23:6311]", "text9": "请输入预警规则", "warningRule": {"detail": {"allHandle": "全部处理", "detailInfo": "详细信息", "handle": "处理", "lastMonth": "最近一个月", "lastThreeMonths": "最近三个月", "lastWeek": "最近一周", "noNeedToBeProcessed": "没有需要处理的记录", "warningRecord": "预警记录"}, "edit": {"cron": "执行周期", "cronPlaceholder": "请输入执行周期", "effectiveTime": "生效时间", "endTime": "结束时间", "noticeUser": "通知人", "noticeUserPlaceholder": "请选择通知人", "ruleType": "规则类型", "saveAndOnline": "保存并重启", "sendEnable": "预警通知发送", "silent": "静默周期", "silentPlaceholder": "请输入静默周期", "startTime": "起始时间"}, "resNamePlaceholder": "请输入资源名称"}}, "monitorRuleName": "监控规则名称", "name": "名称", "nameTip": "{0}等{1}个流程{2}", "nameTip2": "{0}{1}个流程{2}", "namespace": "命名空间", "namespaceInfo": "namespace信息", "newCategory": "新建分类", "noData": "暂无数据", "node": "节点", "nodeInfo": "节点信息", "notPermission": "暂无操作权限，请联系管理员", "notUpdateModeJobName": "的基本参数、高级参数和自定义参数将被更新，", "notes": "备注", "objectCount": "对象数量(主)", "once": "一次性", "onlineDate": "上线日期", "onlineTime": "上线时间", "onlyValue": "仅Value", "opening": "开启中", "orgAllocated": "机构已分配", "orgAvailableSlots": "机构可用slots", "orgCPU": "机构CPU", "orgChildrenCpu": "机构已分配CPU", "orgChildrenMemory": "机构已分配内存", "orgChildrenSlots": "机构已分配slots", "orgCpu": "机构可用CPU", "orgField": "组织机构字段", "orgId": "机构ID", "orgMemory": "机构可用内存", "orgName": "机构名称", "orgOrQueueSetting": "机构/队列设置", "orgResidualCpu": "机构剩余CPU", "orgResidualMemory": "机构剩余内存", "orgResidualSlots": "机构剩余slots", "orgSlots": "机构Slots", "orgSurplus": "机构剩余", "orgUsed": "机构已使用", "orgUsedCpu": "机构已使用CPU", "orgUsedMemory": "机构已使用内存", "orgUsedSlots": "机构已使用slots", "outputFields": "输出字段", "pageDesign": "页面设计", "parallelism": "默认并行度", "params": {"delConfirm": "此操作将永久删除该记录, 是否继续?", "template": {"addTemplate": "新建模板", "delFieldSuccess": "删除字段成功", "delSuccess": "删除模板成功", "detail": {"addField": "添加字段", "addFilter": "添加过滤条件", "addRoute": "添加路由", "addRule": "添加条件", "baseInfo": "基本配置", "cnName": "中文名称", "downloadTemplate": "模板下载", "editField": "编辑字段", "errorTip": "错误提示的文案", "fieldList": "字段列表", "fieldName": "判断字段", "fieldType": "字段类型", "fieldValue": "值", "file": "文件", "filterRule": "规则", "filterRuleRequired": "过滤条件不能为空", "lackRule": "未配置条件", "mapField": "映射字段", "newBuiltField": "新建字段", "notFullyConfigured": "未配置完整", "notMismatching": "字段类型与字段值不匹配", "onlyExcel": "只能上传excel文件", "onlyOneExcel": "只能上传一个excel文件", "operator": "操作符", "route": "路由", "routeList": "路由列表", "rule": "条件", "selectFile": "选择文件", "serialName": "标准名称", "serialNumber": "标准编码", "service": "集群", "servicePlaceholder": "请选择集群", "serviceType": "服务类型", "sourceCode": "源代码值", "sourceCodeComment": "源代码含义", "standardCode": "标准代码值", "standardCodeComment": "标准代码含义", "systemName": "系统名称", "systemNumber": "系统编号", "target": "输出目标", "targetRequired": "输出目标不能为空", "templateName": "模板名称"}, "editTemplate": "编辑模板", "historicalFilterTemplate": "历史过滤模板", "historicalMapTemplate": "历史映射模板", "historicalRouteTemplate": "历史路由模板", "historicalTemplate": "历史模板", "name": "请输入模板名称"}}, "parentEncoding": "上级编码", "parentEncodingNotExist": "上级编码不存在请重新选择分类", "parentEncodingPlaceholder": "请输入上级编码", "partCount": "分区数", "patternError": "表单项【{0}】解析pattern报错: {1}", "physicalMemory": "物理内存", "placeholder": {"afterCpu": "请输入调整后的CPU", "afterMemory": "请输入调整后的内存", "afterSlots": "请输入调整后的Slots", "attempts": "请输入尝试次数", "checkpointInterval": "请输入checkpoint周期", "complete": "请完善信息", "consumeMode": "请选择消费策略", "consumer": "请输入消费者", "consumerName": "请输入消费者名称", "copies": "请输入副本数", "cpFailed": "请输入托管内存大小因子", "cpMinPause": "请输入最小停顿时间", "cronExpression": "请输cron表达式", "database": "请输入database", "delay": "请输入固定重启延迟时间", "failureRateDelay": "请输入失败重启延迟时间", "failureRateInterval": "请输入时间间隔", "failuresPerInterval": "请输入重启次数", "fieldName": "请输入字段名称", "groupId": "请输入group-id", "input": "请输入", "jobManagerMemory": "请输入job manager内存(MB)", "jobManagerRequestCpu": "请输入job manager CPU", "jobRunningRule": "请选择任务运行规则", "key": "请输入key", "keyPlaceholder": "请输入查询关键字", "keyWord": "请输入搜索关键字", "keyword": "请输入关键字搜索", "name": "请输入名称", "nameOrUrl": "请输入名称或服务地址", "namespace": "请输入namespace", "newKey": "请输入键", "notesPlaceholder": "请输入备注", "onlineTime": "请选择上线时间", "orgIds": "请选择分配机构", "orgName": "请输入机构名称", "orgNameFilter": "请输入机构名称过滤", "orgNameOrId": "请输入机构名称/ID", "partCount": "请输入分区数", "partitionOffset": "请输入partition:offset;partition:offset", "queue": "请选择队列", "queueList": "请选择可分配的队列", "queueName": "输入队列名称", "restartStrategy": "请选择重启策略", "search": "请输入内容", "select": "请选择", "set": "请输入set", "sqlPlaceholder": "请输入SQL代码", "stateBackend": "请选择状态后端", "tableName": "请输入表名", "taskManagerFrac": "请输入taskmanager托管内存因子", "taskManagerLimitCpu": "请输入task manager CPU 爆发倍数", "taskManagerLimitMemory": "请输入task manager 内存爆发倍数", "taskManagerRequestCpu": "请输入task manager CPU个数", "taskManagerSlotNumber": "请输入task manager slot个数", "time": "请输入时间", "topicName": "请选择topic名称", "updatePlaceholder": "请输入更新内容", "useCasesPlaceholder": "请输入用例数据", "vaguePlaceholder": "输入字段名模糊搜索", "value": "请输入值"}, "pleaseSelect": "请选择要", "port": "端口", "portal": {"title1": "用户权限", "title2": "机构管理", "title3": "用户管理", "title4": "角色管理", "title5": "系统配置", "title6": "安全配置", "title7": "内容配置", "title8": "操作日志", "title9": "个人中心"}, "preLackCpu": "预计剩余Cpu不足", "preLackMemory": "预计剩余内存不足", "preLackSlots": "预计剩余slots不足", "preRemainCpu": "预计剩余CPU", "preRemainMemory": "预计剩余内存（MB）", "preferenceRelation": "引用关系({0})", "prescribed": "自定义", "process": "流程", "prompt": "提示", "propError": "表单项【{0}】缺少必要字段[prop]", "queryTimeout": "查询超时，请刷新重试", "queue": "队列", "queueResource": "队列资源", "queueResourceDetail": "队列资源详情", "rawData": "原始数据", "read": "读", "readError": "读错误", "realTestResult": "查看真实输入测试结果", "refreshSource": "刷新资源使用情况", "remainCpu": "剩余CPU", "remainMemoryMB": "剩余内存(MB)", "remainSlot": "剩余Slots", "remainSlots": "预计剩余slots", "repeatProp": "存在重复prop[{0}]", "repeatTime": "重复时间", "requestTimeout": "请求超时！", "resType": "节点类型未选择", "resetBackoffThreshold": "最小稳定运行时间", "resource": {"importExport": {"assetId": "资产ID", "assetName": "资产名称", "assetType": "资产类型", "diffContent": "差异内容", "exportDataPreview": "导出数据预览", "keyplaceholder": "输入关键字进行过滤", "option": "选项", "project": "项目", "selectAll": "全选", "selectNone": "全不选", "tablePrefix": "表前缀", "tableSuffix": "表后缀"}, "sql": {"dataTip": "请选择数据", "delTip": "确认要删除选中数据?", "detail": {"alreadyExists": "以下SQL片段缩略词在平台已存在", "duplicateData": "重复数据", "file": "文件", "partAcronym": "片段缩略词", "partName": "片段名称", "placeholder": {"acronymPlaceholder": "请输入片段缩略词", "name": "请输入片段名称", "notesPlaceholder": "请输入备注信息"}, "sqlCode": "SQL代码", "tips": {"inputAcronymTips": "首字符仅可输入字母/数字/./"}, "tooltip": {"tooltip1": "请设置片段缩略词，以便于在编辑区编写代码时通过 缩略词+回车 快速使用SQL片段"}}}, "tip1": "打断算子链，可用于分析流程性能瓶颈，会对流程性能产生影响", "tip2": "进行周期性状态快照，保证数据一致性，会对流程性能产生影响"}, "resourceConf": "资源配置({0})", "resourceConfig": "资源配置", "resourceList": "请填写资源名称", "resourceNotFound": "{0} 资源未被发现", "restartStrategy": "重启策略", "result": "结果", "reupload": "重新上传", "ruleExpression": "规则表达式", "scriptInfo": "脚本信息", "searchDoc": "文档查询", "searchResult": "查询结果", "selectVersion": "选中版本：{0}", "sentry": "哨兵", "serialNumber": "序号", "service": "服务", "serviceDetail": "服务详情", "serviceId": "节点名称未选择", "serviceMgr": "服务管理", "serviceName": "流程类型未选择", "serviceType": "服务类型", "serviceTypeRule": "请选择服务资源类型", "sessionExpired": "可能您的会话已过期，是否要跳转到登录页面？", "sharedOrg": "已分享机构", "sharingMethod": "共享方法", "sheetEdit": "表管理：{0}", "showHomologyGraph": "点击【查询血缘】后，将以该同源节点作为查询节点展示血缘关系图", "single": "单个", "singlePoint": "单点", "singleText0": "您确定删除选中的数据吗？", "singleText1": "${name}已经被关联，无法删除！", "slotsNumber": "slot总数", "slotsSetting": "Slots设置", "someProject": "到的项目", "sourceCode": "源码", "sqlFlow": "SQL流程", "sqlFragment": "SQL片段{0}", "stateBackend": "状态后端", "status": {"develop": "开发", "none": "未运行", "online": "已上线", "published": "已发布", "unKnow": "未知", "underDdevelopment": "开发中"}, "statusStorage": "状态的后端存储", "streamcube": "计算引擎", "syncMig": "将【{0}】流程的配置同步到{1}。执行同步后，{2}{3}{4}您确定要同步吗？", "syncSetting": "同步配置", "syncSuccess": "同步成功，请保存或启动", "systemPath": "安装路径的占位符${install_dir_key},端口的占位符${port_key}", "systemPlaceholder": "系统内置占位符", "table": "表", "tableCode": "请选择左侧表名或表字段，最多查询20条数据", "tableInfo": "表信息", "tableName": "表名称", "targetOrg": "目标机构", "taskManage": "task managers", "taskManagerFrac": "taskmanager托管内存因子", "taskManagerLimitCpu": "task manager CPU 爆发倍数", "taskManagerLimitMemory": "task manager 内存爆发倍数", "taskManagerMemory": "task manager内存(MB)", "taskManagerRequestCpu": "task manager CPU", "taskManagerSlotNumber": "task manager slot个数", "taskMgrInfo": "TaskManager信息", "taskMgrMemory": "Taskmanager 堆内存", "taskMgrSlots": "Taskmanager slot数量", "testResult": "测试结果", "thirdPartyLibs": "第三方类库", "timePeriod": "时间周期", "timeSinceLastHeartbeat": "最后心跳时间", "tip": {"addFile": "请添加文件", "checkMessage": "请检查输入内容", "checkResult": "统计节点连接情况，连接节点数/总节点数", "choseData": "请选择数据", "choseOne": "请选择一条数据", "clusterNotExist": "该集群不存在", "codeLoading": "源码加载中...", "color": "红色代表删除，黄色代表更新，蓝色代表新增", "compareVersion": "请选择需要对比的版本", "configureAtOne": "请至少配置一个条件", "connectError": "{0}连接失败", "connectSuccess": "连接成功", "cpFailed": "checkpoint最大失败次数", "cpUnaligned": "开启后可以提高checkpoint的速度，但会增加内存使用", "cron": "请再次输入正确cron表达式", "dataQualityMonitoring": "数据质量监控组件提示\n          \n          该组件配置的监控规则采用 mvel 表达式实现，遵循mvel表达式的语法。\n          表达式的结果必须是一个boolean类型的值。下列是一些示例：\n\n          假设上游传递的字段为 ： id,name,counta,msg,countb,countc\n          1.字段的使用：以 # 开头 ， 以 # 结尾。 例如: #counta# > 10\n          2.符号的使用：&&、||、>、&lt;、>=、&lt;=、==、%、*、+、-等， null 或者\n          nil 表示空值。\n          例如：(#countc# + #counta# &lt; 200) && #countb# &lt; 990\n          例如：#name# == null || #id# == null\n          \n          3.自定义方法的使用：需要先在【元件管理】-【资源管理】-【方法】里面先定义一个方法，然后才能使用。\n          例如先定义了方法：\n\n          public static boolean getBoolean(Integer id, String name)\n            { return name == null || id == null || id &lt; 9999;\n          }\n\n          在此处编辑的表达式则可为：\n          getBoolean(#id#,#name#)\n          或者\n          #counta # * #countb# &lt; 99999 && getBoolean(#id#,#name#)", "delConfirm": "确定删除该条数据吗?", "delSuccess": "删除成功", "deleteConfirm": "此操作将永久删除该记录, 是否继续?", "deleted": "已经被删除", "deployTimeout": "请输入上线超时时间", "dfs": "该模式需要配置分布式文件系统", "fieldRepeat": "基本参数中[ {0} ]所填字段名与字段配置中的字段有重复", "fileCount": "最多上传 {0} 个文件", "greaterStartTime": "请选择大于当前时间的上线时间", "intervalHour": "请输入小时", "intervalMinute": "请输入分钟", "isApplyParallelism": "同步到每个组件", "jobManagerLimitCpu": "请输入job manager CPU爆发倍数", "jobManagerLimitMemory": "请输入job manager 内存爆发倍数", "jobManagerMemory": "请输入job manager内存", "jobManagerRequestCpu": "请输入job manager CPU", "jobMgrLimitCpu": "该配置项用于开启JobManager CPU爆发能力，指定了JobManager运行时能够使用的CPU上限", "jobMgrLimitMemory": "该配置项用于开启JobManager内存爆发能力，指定了1个JobManager运行时能够使用的内存上限。", "jobMgrMemory": "JobManager负责整个job的生命周期管理，包括资源申请，状态监控，协调、控制的执行过程如处理调度任务、保存checkpoint、容错等；该配置项指定 了JVM 堆内存。", "jobMgrRequestCpu": "该配置项指定了JobManager运行分配的最小CPU", "jobRunningRule": "请选择任务运行规则", "loading": "数据正在加载中...", "logging": "登录中...", "map": "注意：处理规则引用的方法中，如需输入固定参数，需要对特殊字符串进行转义，如\"需写为\\\"。", "maxLength1000": "单次最大可选择1000条数据", "maximumInput": "最大支持输入{0}字符", "mode": "请选择流程模式", "namespace": "yarn通过命名空间实现用户和资源的管理，配置该项指定提交到集群的某个命名空间中。", "nonSpaceChar": "请输入非空格字符", "notEndWithComma": "内容结尾不能是逗号", "notHasChinese": "内容不能包含中文", "notHasChineseComma": "内容不能包含中文逗号", "notHasSpaceBreak": "内容不能包含空格或换行", "notStartWithComma": "内容开头不能是逗号", "onlyJar": "只能上传jar类型的文件!", "onlyJar250MB": "只能上传jar文件，且不超过250MB", "onlyPngSvg": "只能上传png、svg类型的文件!", "onlyPngSvg20KB": "只能上传png、svg类型的文件，且不超过20KB", "orgConfig": "部分机构未完成{0}，请完成配置", "queue": "yarn通过队列实现用户和资源的管理，配置该项指定提交到集群的某个队列中。", "recordConfig": "第 {0} 条记录未配置完整", "recordName": "第 {0} 条记录的名称与其他字段名有重复", "recordParams": "第 {0} 条记录方法的参数未配置完整", "recycle": "确定回收机构名下已分配的资源？", "refreshSuccess": "刷新成功", "related": "当前资源存在引用关系，修改后可能会受到影响", "repeatTime": "请选择重复时间", "rollBackConfirm": "是否确认回滚?", "sameCode": "版本号{0}与选中版本号{1}源码一致", "sameQueueOrgData": "请选择同一队列下的机构数据", "scp": "提示:文件默认上传到用户根目录", "selectFile": "请选择文件", "selectedOrg": "已选择机构（{0}/1000）：", "shareAccess": "默认分享的权限：", "shareTopic": "请选择分享的Topic：", "size1GB": "文件大小限制1GB", "size20KB": "不能上传超过20KB的文件!", "size250MB": "不能上传超过250MB的文件!", "someKey": "存在相同键", "startConnectTest": "开始连接测试...", "startDate": "请选择上线日期", "startTime": "请选择上线时间", "taskManagerFrac": "作用于RocksDB状态后端，批流流程中的排序，哈希表以及缓存中间结果。", "taskManagerLimitCpu": "请输入task manager CPU 爆发倍数", "taskManagerLimitMemory": "请输入task manager 内存爆发倍数", "taskManagerMemory": "请输入task manager内存", "taskManagerRequestCpu": "请输入task manager CPU个数", "taskManagerSlotNumber": "请输入task manager slot数量", "taskMgrLimitCpu": "该配置用于开启TaskManager CPU爆发能力，指定了TaskManager运行时能够使用的CPU上限", "taskMgrLimitMemory": "该配置项用于开启TaskManager内存爆发能力，指定了TaskManager运行时能够使用的内存上限。", "taskMgrMemory": "taskManager负责具体的任务执行，设置该项为对应任务在每个节点上的内存数量。", "taskMgrRequestCpu": "该配置项指定了TaskManager运行分配的最小CPU", "taskMgrSlotNumber": "配置一个TaskManager有多少个并发的slot数，类似于每个TaskManager内有多少个线程。", "terminal": "终端[{0}]", "timeFormat": "日期格式：{0}", "typeJudge": "只能上传{0}类型的文件!", "updateSuccess": "更新成功", "uploadFiles": "请上传{0}文件"}, "totalCpu": "总CPU", "totalMemory": "总内存", "totalMemoryMB": "总内存(MB)", "totalSlots": "总Slots", "udfInfo": "UDF信息", "udjInfo": "UDJ信息", "underExecution": "执行中，请稍等...", "unit": {"day": "天", "hour": "时", "hours": "小时", "minute": "分", "second": "秒"}, "unknownError": "未知错误：{0}", "unknownErrorCode": "未知错误，错误代码：{0}", "unknownFile": "未知文件", "unused": "未使用", "updateModeJobName": "的基本参数、高级参数、自定义参数和模式选择将被更新，", "uploadBatch": "批量上传(目前只针对不存在的组件)", "uploadError": "（上传失败）{0}", "uploadSingle": "单个上传", "used": "已使用", "userCancel": "用户取消", "userCenter": "个人中心", "value": "值", "value1": "值1", "value1Placeholder": "请输入值1内容", "value2": "值2", "value2Placeholder": "请输入值2内容", "value3": "值3", "value3Placeholder": "请输入值3内容", "value4": "值4", "value4Placeholder": "请输入值4内容", "viewField": "查看字段", "visibleError": "表单项【{0}】解析visible函数报错: {1}", "warehouse": "组件库", "warnRule": "预警规则", "waterLevelLine": "水位线", "will": "将", "write": "写", "writeError": "写错误", "wu": "无"}}