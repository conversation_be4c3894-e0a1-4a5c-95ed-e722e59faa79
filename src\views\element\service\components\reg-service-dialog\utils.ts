import validators from './validator';

/**
 * @description 转换visible字段，将字符串转换成函数
 * @param options 表单项配置
 **/
export const convertVisible = (options: Record<string, any> = {}) => {
  if (options.visible && typeof options.visible === 'string') {
    /* eslint-disable no-eval */
    options.visible = eval(options.visible);
  }
};

/**
 * @description 转换rules字段
 * @param options 表单项配置
 * TODO:此处原逻辑validator是由后端返回的函数字符串，现在不需要后端返回
 **/
export const convertRules = (options: Record<string, any> = {}) => {
  (options.rules || []).forEach((rule) => {
    // validator对应的方法名称转换成对应方法
    if (rule.validator && typeof rule.validator === 'string' && validators[rule.validator]) {
      rule.validator = validators[rule.validator];
    }
    // 将pattern对应的正则规则字符串转换成正则对象
    if (rule.pattern && typeof rule.pattern === 'string') {
      /* eslint-disable no-eval */
      rule.pattern = eval(rule.pattern);
    }
  });
};

/**
 * @description 设置不可编辑表单项
 * @param options 表单项配置
 **/
export const setEditable = (options) => {
  if (!options.componentProps) {
    options.componentProps = {};
  }
  options.componentProps.disabled = options.editable === false;
};
