import { cloneDeep, isString } from 'lodash';
import { ExtraChangedFields, extraOriginPoint, OriginChangedFields, OriginFields, OriginNode } from './interface';
import { checkFormItemRequired, checkFormItemVisable } from './service';
import store from '@/store';
import i18n from '@/i18n';
// 全量输入输出组件
export const FullInputAndOutputComponents = [
  'FILTER', // 过滤
  'DYNAMIC_FILTER', // 动态过滤
  'DATA_QUALITY_MONITOR', // 数据质量监控
  'DELAY_PROCESSING', // 延时处理
  'Dynamic-MAP', // 动态映射
  'KEY_BY_OR_WATER_MARK' // keyBy和水位线
];
// 需要特殊处理的组件
const SpecialHandlingComponents = [
  { JOIN: 'cn.com.bsfit.pipeace.component.process.join.PaJoinComponent' }, // 双流组件
  { JOIN: 'cn.com.bsfit.pipeace.component.process.join.PaIntervalJoinComponent' },
  { 'JSON-EXTRACT': 'cn.com.bsfit.pipeace.component.process.convert.JsonExtractComponent' }, // JSON字段提取 （解析前解析后的字段都会输出）
  { DELAY_PROCESSING: 'cn.com.bsfit.pipeace.component.process.delay.PartEventTimeoutFunction' }, // 延时处理组件,
  { JDBC: 'cn.com.bsfit.pipeace.component.flink.jdbc.JdbcSinkComponent' } // 数据库输出组件
];

// 节点自动更新的数据
class NodeUpdateInfo {
  nodeId: string;
  nodeName: string;
  operateType: string; // 组件类型
  portId: string; // 输入端口id
  msg: string[];
  sourceChangedFields: OriginChangedFields; // 上游变更的输出字段
  targetChangedFields: ExtraChangedFields[]; // 当前组件变更的字段
  constructor(targetNode: OriginNode, targetPortId, sourceChangedFields) {
    this.nodeId = targetNode.nodeId;
    this.nodeName = targetNode.nodeName;
    this.operateType = this.getOperateType(targetNode.operateType);
    this.portId = targetPortId;
    this.msg = [];
    this.sourceChangedFields = sourceChangedFields;
    this.targetChangedFields = [];
  }
  getOperateType(type) {
    return {
      SOURCE: i18n.t('pa.flow.inputCompent'),
      PROCESS: i18n.t('pa.flow.processComponent'),
      SINK: i18n.t('pa.flow.outputComponent')
    }[type];
  }
}
// 节点更新数据中具体的字段更新信息
class ChangeInfo {
  model: string;
  label: string;
  status: 0 | 1 | 2; // 0 节点不需要更新  1 节点更新后未报错  2.节点更新后报错
  changedFields: OriginChangedFields;
  isOutput: boolean; // 是否是组件的输出字段
  constructor(model, label, vaild, changedFields, isOutput = false) {
    this.model = model || '';
    this.label = label || '';
    this.changedFields = changedFields || { delFields: [], addFields: [] };
    this.status = vaild ? (changedFields.delFields.length || changedFields.addFields.length ? 1 : 0) : 2;
    this.isOutput = isOutput;
  }
}
export default NodeUpdateInfo;
//  ========================基础工具方法======================

// 字段是否变更
export const isFieldschanged = (changedFields) => {
  return changedFields.delFields.length || changedFields.addFields.length;
};

// 字段排序是否变更
export const isFieldsSortingChanged = (oldFields, newFields) => {
  return oldFields.map((i) => `${i.name}$$$${i.type}`).join('') !== newFields.map((i) => `${i.name}$$$${i.type}`).join('');
};

// 过滤组件输出字段
export const filterOutputFields = (outputFields) => {
  return (outputFields || []).filter((i) => i.outputable !== false);
};

// 获取字段的变更信息
export const returnFieldChangeInfo = (oldVal, newVal) => {
  const fieldMaps = new Map();
  const cb1 = (item) => {
    const key = `${item.name}$$$${item.type}`;
    fieldMaps.set(key, item);
    return key;
  };
  const cb2 = (key) => {
    return fieldMaps.get(key);
  };
  const oldFields = new Set((oldVal || []).map(cb1));
  const newFields = new Set((newVal || []).map(cb1));
  return {
    delFields: [...oldFields].filter((i) => !newFields.has(i)).map(cb2),
    addFields: [...newFields].filter((i) => !oldFields.has(i)).map(cb2)
  };
};

// 根据组件表单配置生成字段显隐的映射关系
const getFormItemShowMaps = (componentProperties) => {
  const maps = new Map();
  if (!Array.isArray(componentProperties)) return maps;
  componentProperties.forEach((item) => {
    const { hideEl = {} } = item.componentCfg || {};
    Object.keys(hideEl).forEach((key) => {
      if (Array.isArray(hideEl[key])) {
        hideEl[key].forEach((val) => {
          !maps.get(val) && maps.set(val, []);
          maps.get(val).push([[item.model], key]);
        });
      }
    });
  });
  return maps;
};

export const joinFieldToString = (name, type = 'String') => {
  const SPLIT_STR = '$$$';
  return name + SPLIT_STR + type;
};

// ========================校验组件输出变更后后续组件的数据变更相关======================

const getChangeInfoForSpecialComponents = (node, port, delFields, changedFiels) => {
  const delFieldStrs = delFields.map((dItem) => dItem.name);
  const fns = {
    ['JOIN']: () => {
      const { leftOutputFieldPrefix, rightOutputFieldPrefix } = node.properties || {};
      const inIdx = node.pointIn.findIndex((item) => item.uuid === port);
      const regExp = inIdx === 0 ? `^${leftOutputFieldPrefix}` : `^${rightOutputFieldPrefix}`;
      return node.outputFields.filter((field) => {
        const nonPrefixFieldName = field.name.replace(new RegExp(regExp), '');
        if (delFieldStrs.includes(nonPrefixFieldName)) {
          changedFiels.delFields.push(field);
        }
        return !delFieldStrs.includes(nonPrefixFieldName);
      });
    }
  };
  return fns[node.type] ? fns[node.type]() : [];
};

const getFieldChangeInfoForSpecialComponents = (node, port, delFields) => {
  const delFieldStrs = delFields.map((dItem) => dItem.name);
  const checkField = (field, label) => {
    const data = (node.properties || {})[field] || '';
    if (delFieldStrs.includes(data)) {
      return [
        new ChangeInfo(field, label, false, {
          delFields: [{ name: data, type: 'String' }],
          addFields: []
        })
      ];
    } else {
      return [];
    }
  };
  const changeInfos: any = [];
  const fns = {
    ['JOIN']: () => {
      const inIdx = node.pointIn.findIndex((item) => item.uuid === port);
      const fn = (key, label) => {
        let value = (node.properties || {})[key];
        const itemDelFields: any[] = [];
        if (Array.isArray(value)) {
          value = value.filter((item) => {
            if (delFieldStrs.includes(item)) {
              itemDelFields.push({ name: item, type: 'String' });
            }
            return !delFieldStrs.includes(item);
          });
        }
        itemDelFields.length &&
          changeInfos.push(new ChangeInfo(key, label, !!value.length, { delFields: itemDelFields, addFields: [] }));
        return itemDelFields;
      };
      const leftFields = {
        leftKey: i18n.t('pa.flow.key1'),
        leftOutputField: i18n.t('pa.flow.key2')
      };
      const rightFields = {
        rightKey: i18n.t('pa.flow.key3'),
        rightOutputField: i18n.t('pa.flow.key4')
      };
      const o = inIdx === 0 ? leftFields : rightFields;
      Object.keys(o).forEach((k) => fn(k, o[k]));
      return changeInfos;
    },
    ['JSON-EXTRACT']: () => {
      return checkField('inputField', i18n.t('pa.flow.inputField'));
    },
    ['DELAY_PROCESSING']: () => {
      return checkField('keyByModel', i18n.t('pa.flow.key5'));
    },
    ['JDBC']: () => {
      const delFields: { name: string; type: string }[] = [];
      ((node.properties || {})['outputFields'] || []).forEach((i) => {
        delFieldStrs.includes(i) && delFields.push({ name: i, type: 'String' });
      });
      return [
        new ChangeInfo('outputFields', i18n.t('pa.flow.outputFields'), false, {
          delFields,
          addFields: []
        })
      ];
    }
  };
  return fns[node.type] ? fns[node.type]() : [];
};

// 获取当前层级所有的输出端点合集
export const generatePoints = (queue) => {
  return queue.reduce((res: extraOriginPoint[], item) => {
    return res.concat(item.pointOut.map((p) => Object.assign(p, { nodeId: item.nodeId })));
  }, []);
};

// 设置当前节点的报错信息
export const setChangeInfoMsg = (nodeUpdateInfo, changeInfo, sourceNodeName: string) => {
  if (!changeInfo || changeInfo.status === 0) return;
  const delNum = changeInfo.changedFields.delFields.length;
  let msg = '';
  if (changeInfo.status === 1) {
    msg = i18n.t('pa.flow.msg65', [changeInfo.label, sourceNodeName, delNum]) as string;
  }
  if (changeInfo.status === 2) {
    msg = i18n.t('pa.flow.msg66', [changeInfo.label, sourceNodeName, delNum, changeInfo.label]) as string;
  }
  msg && !nodeUpdateInfo.msg.includes(msg) && nodeUpdateInfo.msg.push(msg);
};

// 设置当前节点的变更信息
export const setTargetChangedFields = (nodeUpdateInfo, changeInfo) => {
  if (!changeInfo || changeInfo.status === 0) return;
  if (nodeUpdateInfo.targetChangedFields.find((i) => i.model === changeInfo.model && i.label === changeInfo.label)) return;
  nodeUpdateInfo.targetChangedFields.push(changeInfo);
};

// 根据上游输出字段的变更：更新节点的输出 TODO:特殊组件的处理
export const updateInputAndOutpyFields = (
  sourceOutputFields: OriginFields,
  targetOutputFields: OriginFields,
  { delFields },
  targetNode,
  targetPort // 输入端口
) => {
  let changedFiels: OriginChangedFields = { delFields: [], addFields: [] };
  if (
    FullInputAndOutputComponents.includes(targetNode.type) ||
    store.getters.componentListMap.get(targetNode.className).outputs === 'all'
  ) {
    changedFiels = returnFieldChangeInfo(filterOutputFields(targetOutputFields), filterOutputFields(sourceOutputFields));
    targetOutputFields = cloneDeep(filterOutputFields(sourceOutputFields));
  } else if (['JOIN'].includes(targetNode.type)) {
    targetOutputFields = getChangeInfoForSpecialComponents(targetNode, targetPort, delFields, changedFiels);
  } else if (['DD-MAP'].includes(targetNode.type)) {
    const delFieldStrs = delFields.map((dItem) => dItem.name);
    const filedList = targetNode.properties?.filedList || [];
    filedList.forEach((it) => {
      if (delFieldStrs.includes(it.chooseFieldFromParent)) {
        changedFiels.delFields.push(it.chooseFieldFromParent);
      }
    });
  } else {
    const delFieldStrs = delFields.map((dItem) => dItem.name);
    targetOutputFields = filterOutputFields(targetOutputFields).filter((item) => {
      if (delFieldStrs.includes(item.name)) {
        changedFiels.delFields.push(item);
      }
      return !delFieldStrs.includes(item.name);
    });
  }
  // 校验更新后的数据 输出组件的输出字段outputable默认为false此处不进行过滤
  let vaild =
    targetNode.operateType === 'SINK' ? !!targetOutputFields.length : !!filterOutputFields(targetOutputFields).length;
  // DD-MAP组件特殊处理
  if (['DD-MAP'].includes(targetNode.type)) {
    const delFieldStrs = delFields.map((dItem) => dItem.name);
    const filedList = targetNode.properties?.filedList || [];
    vaild = filedList.every((it) => delFieldStrs.includes(it.chooseFieldFromParent));
  }
  return {
    outputFields: targetOutputFields,
    changeInfo:
      changedFiels.delFields.length || changedFiels.addFields.length
        ? new ChangeInfo('outputFields', i18n.t('pa.flow.outputFields'), vaild, changedFiels, true)
        : null
  };
};

// 根据上游输出字段的变更：更新使用上游节点输出作为数据源的表单字段
export const updateUseInputFields = (targetNode, targetPort, { delFields }, { forms, newComponent }) => {
  const changedFields: any[] = [];
  const delFieldStrs = delFields.map((dItem) => dItem.name);
  // 判断表单项是否使用上游输出字段作为数据源
  const isUseInputField = (properties) => {
    // 其他特殊逻辑与上游输入字段相关联
    const otherUserInputFieldCondition = () => {
      return (
        targetNode.type === 'CONVERT' && targetNode.properties.type === 'TUPLE_2_TUPLE' && properties.type === 'tagInput'
      );
    };
    return (
      (properties.componentCfg && (properties.componentCfg.useInput || properties.componentCfg.useInputAndTarget)) ||
      (newComponent && properties.useInput) ||
      otherUserInputFieldCondition()
    );
  };
  // 判断表单项是否显示
  const isShowFormItem = (properties, showMaps) => {
    if (newComponent) return checkFormItemVisable(properties.visible, targetNode.properties);
    const { model } = properties || {};
    const data = showMaps.get(model);
    if (!data) return true;
    return data.every(([k, v]) => targetNode.properties && targetNode.properties[k] !== v);
  };
  // 表单字段对应的值更新后是否为空
  const isEmpty = (data) => {
    return Array.isArray(data) ? data.length === 0 : !data;
  };
  // 获取表单字段的增删信息
  const updateUseInputField = (data) => {
    const itemDelFields: any[] = [];
    if (Array.isArray(data)) {
      data = data.filter((item) => {
        if (delFieldStrs.includes(item)) {
          itemDelFields.push({ name: item, type: 'String' });
          return false;
        } else {
          return true;
        }
      });
    } else if (delFieldStrs.includes(data)) {
      itemDelFields.push({ name: data, type: 'String' });
      data = '';
    }
    return {
      data,
      changeFields: { delFields: itemDelFields, addFields: [] }
    };
  };
  if (SpecialHandlingComponents.map((o) => Object.values(o)[0]).includes(targetNode.className)) {
    changedFields.push(...getFieldChangeInfoForSpecialComponents(targetNode, targetPort, delFields));
  } else if (Array.isArray(forms)) {
    const showMaps = getFormItemShowMaps(forms);
    forms.forEach((item) => {
      // 是否为上游输出字段作为数据源的字段
      if (isUseInputField(item) && isShowFormItem(item, showMaps)) {
        // 更新当前字段
        const value = cloneDeep(targetNode.properties![item.model || item.prop]);
        const { data, changeFields } = updateUseInputField(value);
        if (changeFields.delFields.length) {
          const isValueEmpty = checkFormItemRequired(item) && isEmpty(data);
          changedFields.push(new ChangeInfo(item.model || item.prop, item.label, !isValueEmpty, changeFields));
        }
      }
    });
  }
  return changedFields;
};

// 合并变更信息
const mergeChangedFields = (changedFields, preChangedFields) => {
  const cb = (data) => `${data.name}$$$${data.type || 'String'}`;
  const fieldStr = (k) => {
    return {
      '00': addFields.map(cb),
      '10': delFields.map(cb),
      '01': preAddFields.map(cb),
      '11': preDelFields.map(cb)
    }[k];
  };
  const includesField = (field, type: 'add' | 'del', isPre: boolean) => {
    return fieldStr(`${type === 'add' ? '0' : '1'}${isPre ? '1' : '0'}`).includes(cb(field));
  };
  let { addFields, delFields } = changedFields;
  let preAddFields = preChangedFields.addFields;
  let preDelFields = preChangedFields.delFields;
  addFields = addFields.filter((field) => !includesField(field, 'del', true));
  delFields = delFields.filter((field) => !includesField(field, 'add', true));
  preAddFields = preAddFields.filter((field) => !includesField(field, 'del', false));
  preDelFields = preDelFields.filter((field) => !includesField(field, 'add', false));
  return {
    addFields: preAddFields.concat(addFields.filter((item) => !fieldStr('01').includes(cb(item)))),
    delFields: preDelFields.concat(delFields.filter((item) => !fieldStr('11').includes(cb(item))))
  };
};

// 合并上游变更信息
export const mergeSourceChangedFields = (changedFields, taregtNodeId, targetPortId, preUpdateDatas) => {
  const preUpdateData = preUpdateDatas.find((item) => item.nodeId === taregtNodeId && item.portId === targetPortId);
  if (!preUpdateData) return changedFields;
  return mergeChangedFields(changedFields, preUpdateData.sourceChangedFields);
};

// 根据更新信息更新节点信息
export const updateNodeData = (node, targetChangedFields) => {
  const del = (value, delFields) => {
    const delFieldStrs = delFields.map((field) => joinFieldToString(field.name, field.type));
    if (Array.isArray(value)) {
      return value.filter((item) => {
        const str = isString(item) ? joinFieldToString(item) : joinFieldToString(item.name, item.type);
        return !delFieldStrs.includes(str);
      });
    } else if (delFieldStrs.includes(joinFieldToString(value))) {
      return '';
    }
  };
  for (const i in targetChangedFields) {
    const { model, changedFields, isOutput } = targetChangedFields[i];
    const { delFields, addFields } = changedFields;
    if (isOutput) {
      if (
        FullInputAndOutputComponents.includes(node.type) ||
        store.getters.componentListMap.get(node.className).outputs === 'all'
      ) {
        node.outputFields = cloneDeep(node.inputFields);
      } else {
        node.outputFields = del(node.outputFields, delFields);
        node.outputFields.push(...addFields);
      }
    } else if (node.properties) {
      node.properties[model] = del(node.properties[model], delFields);
    }
  }
};

// 更新信息合并
export const appendToNodeUpdateData = (nodeUpdateDatas, newUpdateNodes) => {
  const nodeIds = newUpdateNodes.map((i) => i.nodeId + i.portId);
  return nodeUpdateDatas.filter((i) => !nodeIds.includes(i.nodeId + i.portId)).concat(newUpdateNodes);
};

//  ========================连线时字段校验相关======================
// 判断输入输出的端点所带的字段是否一直 （支持字段都一直但是顺序不同）
export const isFieldsEqual = (sourceData, targetData) => {
  const toDataStr = (arr) => {
    return cloneDeep(arr)
      .sort((a, b) => a.name.toUpperCase() - b.name.toUpperCase())
      .map((item) => item.name + item.type)
      .join('|');
  };
  if (Array.isArray(sourceData) && Array.isArray(targetData)) {
    if (sourceData.length !== targetData.length) return false;
    return toDataStr(sourceData) === toDataStr(targetData);
  }
  return true;
};

// 连线时输入输出的有出入的字段
export const fieldCheckForConnect = (sourceData, targetData) => {
  // 输入字段中不存在输出字段中的字段
  const overInputs = new Set((targetData || []).map((item) => `${item.name}(${item.type})`));
  // 输出字段中不存在输入字段中的字段
  const overOutputs = new Set((sourceData || []).map((item) => `${item.name}(${item.type})`));
  return {
    overInputs: [...overInputs].filter((i) => !overOutputs.has(i)),
    overOutputs: [...overOutputs].filter((i) => !overInputs.has(i))
  };
};
