<template>
  <bs-dialog
    title="数据预览"
    :visible.sync="visible"
    width="60%"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-tabs v-model="activeName" v-loading="loading">
      <el-tab-pane label="原始" name="first">
        <bs-code :value="topicData" :read-only="true" :operatable="false" />
      </el-tab-pane>
      <el-tab-pane label="格式化" name="second">
        <div align="right">
          <bs-search v-model="sep" class="marR12" placeholder="请输入列分隔符" />
          <el-button type="primary" @click="dataformat">转换</el-button>
          <bs-table
            height="400px"
            :data="tableData"
            :column-data="columnData"
            :page-data="pageData"
            :column-settings="false"
            @page-change="pageChange"
          />
        </div>
      </el-tab-pane>
    </el-tabs>

    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">关闭</el-button>
    </span>
  </bs-dialog>
</template>

<script lang="ts">
import { Component, Prop, Emit, Vue } from 'vue-property-decorator';
import { split, isArray, isObject, isString } from 'lodash';
import { pulsarPreview, kafkaPreview, rocketPreview } from '@/apis/serviceApi';
@Component
export default class TopicPreview extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: '' }) resId!: string;
  @Prop() topic!: any;

  activeName = 'first';
  topicData = '';
  sep = ''; // 分隔符
  lines: any = [];
  columnData: any = [];
  tableData: any = [];
  pageData = { pageSize: 25, currentPage: 1, total: 0 };
  loading = true;
  type: any = '';

  pageChange(currentPage, pageSize) {
    this.pageData.currentPage = currentPage;
    this.pageData.pageSize = pageSize;
    this.getData();
  }

  async getData() {
    this.loading = true;
    const { data, success, msg } =
      this.type === 'KAFKA'
        ? await kafkaPreview(this.resId, this.topic)
        : this.type === 'PULSAR'
        ? await pulsarPreview(this.resId, this.topic)
        : await rocketPreview(this.resId, this.topic.topic);
    this.loading = false;
    if (success) {
      data.forEach((n) => {
        this.topicData = this.topicData + n + '\n';
        this.lines.push(n);
      });
      return;
    }
    this.$message.error(msg);
  }

  dataformat() {
    let cols: any = [];
    const datas: any = [];
    this.lines.forEach((n) => {
      let obj;
      try {
        obj = JSON.parse(n);
      } catch (e) {
        obj = n;
      }
      const isArr = isArray(obj);
      const isObj = isObject(obj);
      const isStr = isString(obj);
      if (isStr) {
        // string
        const values = split(obj, this.sep);
        if (cols.length < values.length) {
          cols = [];
          for (let i = 0; i < values.length; i++) {
            cols.push({
              label: '值' + i,
              value: 'v' + i
            });
          }
          this.columnData = cols;
        }
        const val = {};
        for (let i = 0; i < values.length; i++) {
          val['v' + i] = values[i];
        }
        datas.push(val);
      } else {
        if (isObj && isArr) {
          // json array
          if (cols.length === 0) {
            // 生成表头
            for (const key of Object.keys(obj[0])) {
              cols.push({
                label: key,
                value: key
              });
            }
            this.columnData = cols;
          }
          obj.forEach((m) => {
            datas.push(m);
          });
        } else {
          // json object
          if (cols.length === 0) {
            // 生成表头
            for (const key of Object.keys(obj)) {
              cols.push({
                label: key,
                value: key
              });
            }
            this.columnData = cols;
          }
          // 生成数据
          datas.push(obj);
        }
      }
    });
    this.tableData = datas;
  }

  @Emit('close')
  closeDialog() {
    this.$emit('update:visible', false);
  }

  created() {
    this.type = this.$route.query.resType;
    this.getData();
  }
}
</script>

<style scoped></style>
