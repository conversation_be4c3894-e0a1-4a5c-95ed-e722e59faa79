<template>
  <el-dialog
    :title="data.nodeName + '组件配置'"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    width="40%"
    append-to-body
  >
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-position="left"
      label-width="110px"
      class="form-content"
    >
      <el-form-item label="映射模板" prop="mappingTemplateId">
        <el-popover trigger="hover" placement="bottom" class="form-content__popover" width="700">
          <div v-if="mapInfo">
            <el-table :data="mapInfo" style="width: 100%">
              <el-table-column prop="systemId" label="系统编号" />
              <el-table-column prop="mappingFieldName" label="映射字段" />
              <el-table-column prop="sourceCode" label="源代码值" />
              <el-table-column prop="sourceCodeComment" label="源代码含义" width="100px" />
              <el-table-column prop="standardCode" label="标准代码值" />
              <el-table-column prop="standardCodeComment" label="标准代码含义" width="120px" />
            </el-table>
          </div>
          <el-select
            slot="reference"
            v-model="ruleForm.mappingTemplateId"
            placeholder="请选择映射模板"
            clearable
            filterable
            :disabled="disabled"
            style="width: 100%"
            @change="getFieldList"
          >
            <el-option
              v-for="rule in templateList"
              :key="rule.id"
              :label="rule.mappingTemplateName"
              :value="rule.id"
            />
          </el-select>
        </el-popover>
      </el-form-item>
      <el-form-item label="系统编号字段" prop="systemIdField">
        <el-select
          v-model="ruleForm.systemIdField"
          placeholder="请选择系统编号字段"
          clearable
          filterable
          :disabled="disabled"
          style="width: 100%"
        >
          <el-option
            v-for="field in data.inputFields"
            :key="field.name"
            :label="field.name"
            :value="field.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="映射字段" prop="mapFields">
        <bs-select
          v-model="ruleForm.mapFields"
          multiple
          placeholder="请选择映射字段"
          clearable
          filterable
          show-all
          collapse-tags
          :disabled="disabled"
          style="width: 100%"
          :options="selectOptions(data.inputFields || [])"
        />
      </el-form-item>
      <el-form-item label="更新频率" prop="updateInterval">
        <el-input v-model="ruleForm.updateInterval" type="number">
          <template slot="append">秒</template>
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <div style="width: 60%">
        <el-switch
          v-model="printLog"
          :disabled="disabled"
          style="display: block; float: left"
          active-color="#13ce66"
          inactive-color="#ff4949"
          active-text="开启日志"
          inactive-text="关闭日志"
        />
      </div>
      <el-button @click="closeDialog(false)">取消</el-button>
      <el-button v-if="!disabled" type="primary" @click="submit('ruleForm')">确定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { get } from '@/apis/utils/net';
import { cloneDeep } from 'lodash';

@Component
export default class DynamicMap extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop({ default: false }) disabled!: boolean;

  printLog = false;
  ruleForm: any = {
    systemIdField: '',
    mapFields: '',
    mappingTemplateId: '',
    updateInterval: 600
  };
  rules: any = {
    systemIdField: [{ required: true, message: '请选择系统编号字段', trigger: 'change' }],
    mapFields: [{ required: true, message: '请选择映射模板', trigger: 'change' }],
    mappingTemplateId: [{ required: true, message: '请选择映射模板', trigger: 'change' }],
    updateInterval: [{ required: true, message: '请填写更新频率', trigger: 'blur' }]
  };

  templateList: any = [];
  mapInfo: any = [];

  async created() {
    await this.getList();
    const { properties } = this.data;
    this.printLog = this.data.printLog || false;
    if (properties) {
      this.ruleForm = properties;
      this.getFieldList();
    }
  }

  selectOptions(data) {
    return data.map((item) => {
      return {
        label: item.name,
        value: item.name
      };
    });
  }

  async getList() {
    // 获取模板列表
    const { data, success } = await get('/rs/pa/mappingGroup/listAll');
    if (success) {
      this.templateList = data;
    }
  }

  async getFieldList() {
    // 获取选中模板的详情信息
    const { data, success } = await get('/rs/pa/mappingGroup/listGroupMapping', {
      id: this.ruleForm.mappingTemplateId
    });
    if (success) {
      this.mapInfo = data;
    }
  }

  submit(formName: any) {
    const nodeDot = cloneDeep(this.data);
    const formRef: any = this.$refs[formName];
    formRef.validate((valid) => {
      if (valid) {
        nodeDot.outputFields = nodeDot.inputFields;
        nodeDot.outputFields.forEach((item) => {
          item.outputable = true;
        });
        nodeDot.properties = this.ruleForm;
        nodeDot.printLog = this.printLog;
        this.closeDialog(true, nodeDot);
      }
    });
  }

  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    return { needUpdate, jobNode };
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__content .el-input-group {
  vertical-align: baseline;
}
::v-deep .el-select {
  width: 100%;
}
</style>
