<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    :confirm-button="{ disabled }"
    width="40%"
    append-to-body
    @close="closeDialog(false)"
    @confirm="submit('ruleForm')"
  >
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-position="left" label-width="110px" class="form-content">
      <el-form-item :label="$t('pa.flow.label14')" prop="mappingTemplateId">
        <el-popover trigger="hover" placement="bottom" class="form-content__popover" width="700">
          <div v-if="mapInfo">
            <el-table :data="mapInfo" style="width: 100%">
              <el-table-column prop="systemId" :label="$t('pa.flow.label15')" />
              <el-table-column prop="mappingFieldName" :label="$t('pa.flow.label16')" />
              <el-table-column prop="sourceCode" :label="$t('pa.flow.label17')" />
              <el-table-column prop="sourceCodeComment" :label="$t('pa.flow.label18')" width="100px" />
              <el-table-column prop="standardCode" :label="$t('pa.flow.label19')" />
              <el-table-column prop="standardCodeComment" :label="$t('pa.flow.label20')" width="120px" />
            </el-table>
          </div>
          <el-select
            slot="reference"
            v-model="ruleForm.mappingTemplateId"
            :placeholder="$t('pa.flow.placeholder30')"
            clearable
            filterable
            :disabled="disabled"
            style="width: 100%"
            @change="getFieldList"
          >
            <el-option v-for="rule in templateList" :key="rule.id" :label="rule.mappingTemplateName" :value="rule.id" />
          </el-select>
        </el-popover>
      </el-form-item>
      <el-form-item :label="$t('pa.flow.label21')" prop="systemIdField">
        <el-select
          v-model="ruleForm.systemIdField"
          :placeholder="$t('pa.flow.placeholder31')"
          clearable
          filterable
          :disabled="disabled"
          style="width: 100%"
        >
          <el-option v-for="field in data.inputFields" :key="field.name" :label="field.name" :value="field.name" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('pa.flow.label16')" prop="mapFields">
        <bs-select
          v-model="ruleForm.mapFields"
          multiple
          :placeholder="$t('pa.flow.placeholder32')"
          clearable
          filterable
          show-all
          collapse-tags
          :disabled="disabled"
          style="width: 100%"
          :options="selectOptions(data.inputFields || [])"
        />
      </el-form-item>
      <el-form-item :label="$t('pa.flow.label13')" prop="updateInterval">
        <el-input v-model="ruleForm.updateInterval" type="number" :disabled="disabled">
          <template slot="append">{{ $t('pa.flow.s') }}</template>
        </el-input>
      </el-form-item>
    </el-form>
    <print-log slot="footer-left" v-model="printLog" :disabled="disabled" />
  </el-dialog>
</template>

<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { get } from '@/apis/utils/net';
import { cloneDeep } from 'lodash';
import PrintLog from './components/print-log.vue';

@Component({
  components: {
    PrintLog
  }
})
export default class DynamicMap extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() data!: any;
  @Prop({ default: false }) disabled!: boolean;

  printLog = false;
  ruleForm: any = {
    systemIdField: '',
    mapFields: '',
    mappingTemplateId: '',
    updateInterval: 600
  };
  rules: any = {
    systemIdField: [{ required: true, message: this.$t('pa.flow.placeholder31'), trigger: 'change' }],
    mapFields: [{ required: true, message: this.$t('pa.flow.placeholder30'), trigger: 'change' }],
    mappingTemplateId: [{ required: true, message: this.$t('pa.flow.placeholder30'), trigger: 'change' }],
    updateInterval: [{ required: true, message: this.$t('pa.flow.msg202'), trigger: 'blur' }]
  };

  templateList: any = [];
  mapInfo: any = [];
  get title() {
    const { nodeName = '', componentName = '' } = this.data || {};
    return this.$t('pa.flow.msg288', [nodeName, componentName]);
  }
  async created() {
    await this.getList();
    const { properties } = this.data;
    this.printLog = this.data.printLog || false;
    if (properties) {
      this.ruleForm = properties;
      this.getFieldList();
    }
  }

  selectOptions(data) {
    return data.map((item) => {
      return {
        label: item.name,
        value: item.name
      };
    });
  }

  async getList() {
    // 获取模板列表
    const { data, success } = await get('/rs/pa/mappingGroup/listAll');
    if (success) {
      this.templateList = data;
    }
  }

  async getFieldList() {
    // 获取选中模板的详情信息
    const { data, success } = await get('/rs/pa/mappingGroup/listGroupMapping', {
      id: this.ruleForm.mappingTemplateId
    });
    if (success) {
      this.mapInfo = data;
    }
  }

  submit(formName: any) {
    const nodeDot = cloneDeep(this.data);
    const formRef: any = this.$refs[formName];
    formRef.validate((valid) => {
      if (valid) {
        nodeDot.outputFields = nodeDot.inputFields;
        nodeDot.outputFields.forEach((item) => {
          item.outputable = true;
        });
        nodeDot.properties = this.ruleForm;
        nodeDot.printLog = this.printLog;
        this.closeDialog(true, nodeDot);
      }
    });
  }

  @Emit('close')
  private closeDialog(needUpdate, jobNode) {
    return { needUpdate, jobNode };
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__content .el-input-group {
  vertical-align: baseline;
}
::v-deep .el-select {
  width: 100%;
}
</style>
