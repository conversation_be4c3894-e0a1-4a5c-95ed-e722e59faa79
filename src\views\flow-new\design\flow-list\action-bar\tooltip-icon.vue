<template>
  <span :class="['tooltip-icon', disabled ? 'is-disabled' : '']">
    <el-tooltip :content="content" effect="light" :open-delay="500" placement="bottom">
      <i :class="icon" @click="$emit('click')"></i>
    </el-tooltip>
  </span>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
@Component
export default class TooltipIcon extends Vue {
  @Prop() icon!: string;
  @Prop() content!: string;
  @Prop() disabled!: boolean;
}
</script>

<style lang="scss" scoped>
.tooltip-icon {
  margin-right: 20px;
  font-size: 16px;
  cursor: pointer;
  color: #757d86;
  &.is-disabled > i {
    color: #c8c8c8;
    cursor: not-allowed;
  }
}
</style>
