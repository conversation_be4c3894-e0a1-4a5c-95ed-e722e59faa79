<template>
  <bs-dialog
    width="800px"
    append-to-body
    class="mapping-dialog"
    :title="$t('pa.flow.fieldMapTitle')"
    :visible.sync="display"
  >
    <!-- header -->
    <div class="mapping-header">
      <!-- title -->
      <span class="mapping-header__title">{{ $t('pa.flow.fieldMap') }}</span>
      <!-- search -->
      <bs-search v-model="keyword" :placeholder="$t('pa.flow.placeholder0')" @search="handleSearch" />
    </div>
    <bs-table
      v-loading="loading"
      border
      stripe
      :height="370"
      paging-front
      row-key="id"
      size="mini"
      align="center"
      :data="tableData"
      :page-data="pageData"
      selection
      crossing
      :selectable="!disabled"
      :column-settings="false"
      :column-data="columnData"
      :checked-rows="checkedData"
      :show-multiple-selection="false"
      cell-class-name="mapping-center"
      header-cell-class-name="mapping-center"
      @page-change="handlePageChange"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择全部 -->
      <template slot="footer-expand">
        <el-checkbox v-model="checkAll" :disabled="disabled" @change="handleCheckedAll">
          {{ $t('pa.flow.selectAll1') }}
        </el-checkbox>
        <span class="mapping-dialog__total">
          <span>（{{ $t('pa.flow.selected') }}</span>
          <span>{{ currentSelectionData.length }}</span>
          <span>{{ $t('pa.flow.xiang') }}）</span>
        </span>
      </template>
      <!-- key -->
      <template slot="id" slot-scope="{ row }">
        <div class="mapping-key">
          <el-tooltip v-hide effect="light" placement="top" :content="row.id">
            <span class="mapping-key__id">{{ row.id }}</span>
          </el-tooltip>
          <bs-tag v-if="row.isPrimarykey">{{ $t('pa.flow.mainKey') }}</bs-tag>
          <bs-tag v-if="row.isPartition" color="green">{{ $t('pa.flow.fenqu') }}</bs-tag>
        </div>
      </template>
      <!-- value -->
      <template slot="value" slot-scope="{ row }">
        <el-select
          v-model="row.value"
          clearable
          filterable
          size="small"
          :disabled="disabled"
          :placeholder="$t('pa.flow.placeholder17')"
        >
          <el-option v-for="el in inputFields" :key="el.value" :value="el.value" :label="el.label" />
        </el-select>
      </template>
    </bs-table>
    <div slot="footer">
      <el-button @click="display = false">{{ disabled ? $t('pa.flow.close') : $t('pa.flow.cancel') }}</el-button>
      <el-button v-if="!disabled" type="primary" @click="handleConfirm">{{ $t('pa.flow.confirm') }}</el-button>
    </div>
  </bs-dialog>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue, Watch } from 'vue-property-decorator';
import type { TableItem } from '../type';
import { getValue, hide, includesPro } from '../utils';
import { uniqBy, cloneDeep } from 'lodash';

@Component({ directives: { hide } })
export default class FieldMappingDialog extends Vue {
  @Prop() tableName!: string;
  @Prop() type!: string;
  @Prop() resId!: string;
  @Prop({ default: '' }) outputColumns!: string | string[];
  @Prop({ default: () => [] }) preFields!: any;
  @Prop({ default: () => [] }) outputFields!: string[];
  @Prop({ default: () => [] }) inputFields!: any[];
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ default: () => [] }) tableMappingFields!: any[];
  @PropSync('show', { type: Boolean, default: false }) display!: boolean;

  loading = false;
  private keyword = '';
  private pageData = {
    pageSize: 10,
    currentPage: 1,
    total: 0,
    layout: 'total, prev, pager, next'
  };
  private checkAll = false;
  private tableData: any[] = [];
  private checkedData: any[] = [];
  private currentSelectionData: TableItem[] = [];
  private isRendered = true;
  tableFields: any[] = [];
  get value() {
    const temp = Array.isArray(this.outputColumns) ? this.outputColumns : this.outputColumns.split(',').filter(Boolean);
    return temp.reduce((pre: any, next: string, index: number) => {
      pre[next] = this.outputFields[index];
      return pre;
    }, {});
  }
  get rawTableData() {
    return !this.disabled
      ? this.tableFields.map(({ id, isPrimarykey, isPartition }) => ({
          id,
          isPrimarykey,
          isPartition,
          value: getValue(id, this.inputFields, this.value)
        }))
      : Object.entries(this.value).map(([id, value]) => {
          let isPrimarykey = false;
          let isPartition = false;
          const item = this.tableFields.find((el = {}) => el.id === id) || {};
          'isPrimarykey' in item && (isPrimarykey = item.isPrimarykey);
          'isPartition' in item && (isPartition = item.isPartition);
          return { id, value, isPrimarykey, isPartition };
        });
  }

  get columnData() {
    return [
      {
        label: this.tableName,
        value: 'id',
        width: '296px',
        showOverflowTooltip: false
      },
      {
        label: this.$t('pa.flow.inputField'),
        value: 'value',
        width: '296px',
        showOverflowTooltip: false
      }
    ];
  }

  @Watch('tableData', { deep: true })
  handleCurrentSelectionDataChange(data) {
    const shouldUpdateCheckedRows = this.preFields && this.preFields.checkedData && this.preFields.checkedData.length;
    const shouldUpdateTableData =
      !this.isRendered && this.preFields && this.preFields.tableData && this.preFields.tableData.length;
    this.isRendered && !shouldUpdateCheckedRows && this.$set(this, 'checkedData', this.currentSelectionData);
    shouldUpdateTableData &&
      this.tableData.forEach((el) => {
        const matchItem = this.preFields.tableData.find((item) => item.id === el.id && item.isPartition === el.isPartition);
        if (matchItem) el.value = matchItem.value;
      });
    !this.isRendered &&
      shouldUpdateCheckedRows &&
      (this.checkedData = this.tableData.filter((el) =>
        this.preFields.checkedData.some((item) => item.id === el.id && item.isPartition === el.isPartition)
      ));
    !this.isRendered && (this.isRendered = true);
    this.checkAll = !data.length
      ? false
      : this.currentSelectionData.filter((el) => this.tableData.map((el) => el.id).includes(el.id)).length ===
        this.tableData.length;
  }

  @Watch('tableMappingFields', { immediate: true, deep: true })
  handleTableMappingFieldsChange(data) {
    try {
      this.loading = true;
      if (!data) return;
      this.tableFields = cloneDeep(data);
      this.handleSearch('');
    } finally {
      setTimeout(() => {
        this.loading = false;
      }, 300);
    }
  }
  /* 处理搜索事件 */
  handleSearch(keyword: string) {
    this.tableData = !keyword ? this.rawTableData : (this.rawTableData as any).filter(({ id }) => includesPro(id, keyword));
    this.pageData = { ...this.pageData, currentPage: 1, total: this.tableData.length };
    this.$set(
      this,
      'checkedData',
      (this.checkedData = Object.keys(this.value)
        .map((key) => this.tableData.find((el) => String(el.id) === key))
        .filter(Boolean))
    );
    this.isRendered = false;
  }
  handlePageChange(currentPage) {
    this.pageData.currentPage = currentPage;
  }
  /* 处理表格选中事件 */
  handleSelectionChange(data: TableItem[]) {
    const tableDataIds = this.tableData.map((el) => el.id);
    const preSelections = this.currentSelectionData.filter((el) => !tableDataIds.includes(el.id));
    if (!data.length && preSelections.length) {
      this.$set(this, 'currentSelectionData', preSelections);
    } else if (data.length && preSelections.length) {
      this.$set(this, 'currentSelectionData', uniqBy([...data, ...preSelections], 'id'));
    } else {
      this.$set(this, 'currentSelectionData', data);
    }
    if (!this.keyword) {
      this.checkAll = this.currentSelectionData.length === this.tableData.length;
    } else {
      this.checkAll =
        this.currentSelectionData.filter((el) => this.tableData.map((el) => el.id).includes(el.id)).length ===
        this.tableData.length;
    }
  }
  /* 处理表格全选事件 */
  handleCheckedAll(isChecked) {
    this.checkedData = isChecked ? this.tableData : [];
    this.handleSelectionChange(this.checkedData);
  }
  /* 处理确认事件 */
  async handleConfirm() {
    await this.checkFieids();
    const result = this.getSpecialField();
    if (result) {
      await this.$confirm(this.$t('pa.confirmDialog', [result]), this.$t('pa.prompt'));
    }
    let primarykey = '';
    const [fields, columns] = this.currentSelectionData.reduce(
      (pre: any, { id, value, isPrimarykey }) => {
        pre[0].push(value);
        pre[1].push(id);
        isPrimarykey && (primarykey = id);
        return pre;
      },
      [[], []]
    );
    this.$emit('change', ...[fields, columns, primarykey]);
    this.$emit('pre-data-change', {
      tableData: this.tableData,
      checkedData: this.currentSelectionData
    });
    this.display = false;
  }
  /* 字段映射校验 */
  checkFieids() {
    return new Promise((resolve: any, reject: any) => {
      if (this.currentSelectionData.length < 1) {
        this.$tip.error(this.$t('pa.flow.msg83'));
        return reject(false);
      }
      const target = this.currentSelectionData.find((el) => !el.value);
      if (target) {
        this.$tip.error(this.$t('pa.flow.msg84', [target.id]));
        return reject(false);
      }
      if (
        this.type === 'HUDI' &&
        (this.currentSelectionData.every((i) => !i.isPrimarykey) || this.currentSelectionData.every((i) => !i.isPartition))
      ) {
        this.$tip.error(this.$t('pa.flow.msg85'));
        return reject(false);
      }
      resolve(true);
    });
  }
  /* 获取第一个但未选的字段 */
  getSpecialField() {
    const idList = this.tableData.reduce((pre: string[], { id, value }: any) => {
      value && pre.push(id);
      return pre;
    }, []);
    const selectedIdList = this.currentSelectionData.reduce((pre: string[], { id }: any) => {
      id && pre.push(id);
      return pre;
    }, []);
    for (const i of idList) {
      if (!selectedIdList.includes(i)) return i;
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-select {
  width: 100%;
}
::v-deep .mapping-center {
  text-align: center;
}
::v-deep .bs-table-footer-slot {
  padding-left: 0;
  text-align: left;
}

.mapping {
  &-dialog {
    ::v-deep .el-dialog__body {
      padding-bottom: 0;
      min-height: 490px !important;
    }
    &__total {
      font-weight: bolder;
      letter-spacing: 1px;
    }
  }
  &-header {
    display: flex;
    justify-content: space-between;
    align-content: center;
    margin-bottom: 16px;
    height: 32px;
    &__title {
      font-weight: bold;
      font-size: 14px;
    }
  }

  &-key {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    &__id {
      display: inline-block;
      width: calc(100% - 45px);
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}
</style>
