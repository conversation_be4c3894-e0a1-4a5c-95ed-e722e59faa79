import { get, post, put } from './utils/net';

// 获取预警规则枚举
export const getRuleTypeList = () => {
  return post(`/rs/pa/warnRule/getRuleTypeList`);
};

// 查询监控预警列表
export const searchWarnings = (params) => {
  return post(`/rs/pa/warnRule/lists`, params);
};

// 获取资源类型列表
export const getResourceTypes = () => {
  return get(`/rs/pa/warnRule/getResourceType`);
};

export const getRecordList = (params: any, data: any) => {
  return post(`/rs/pa/warnRecord/listByRuleId`, data, { params });
};
export const updateState = (data: any[]) => {
  return put('/rs/pa/warnRecord/updateState', data);
};
export const updateAllState = (ruleId: string) => {
  return get('/rs/pa/warnRecord/updateAllState', { ruleId });
};
export const updateWarnRule = (data: any) => {
  return put('/rs/pa/warnRule/update', data);
};
export const updateWarnRuleState = (data: any[]) => {
  return put('/rs/pa/warnRule/updateState', data);
};
/* 获取预警规则 */
export const getWarnRule = (id: string) => {
  return get('/rs/pa/warnRule/findById', { id });
};
export const getUserList = (orgId: string) => {
  return get('/rs/pa/portal/listUserByOrgId', { orgId });
};
export const getSubList = (parentCode: string) => {
  return get('/rs/pa/dic/getSubByParent', { parentCode });
};
export const getNoticeTemplate = () => {
  return get('/rs/pa/dic/getNoticeTemplate');
};
