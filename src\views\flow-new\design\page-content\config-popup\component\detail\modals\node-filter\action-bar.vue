<template>
  <div class="action-bar__container">
    <!-- 增加条件 -->
    <el-button
      v-if="name === 'configDetails'"
      class="action-bar__button"
      type="primary"
      :disabled="disabled"
      @click.native="handleClick"
    >
      {{ $t('pa.flow.addCondition') }}
    </el-button>
    <!-- tab -->
    <el-tabs v-model="name">
      <el-tab-pane v-for="el in tabList" :key="el.name" :label="el.label" :name="el.name" />
    </el-tabs>
  </div>
</template>
<script lang="ts">
import { Component, PropSync, Prop, Vue } from 'vue-property-decorator';
import { debounce } from 'lodash';

@Component
export default class ActionBar extends Vue {
  @PropSync('activeName', { default: 'configDetails' }) name!: string;
  @Prop({ default: false }) disabled!: boolean;

  private tabList: any[] = [
    {
      label: this.$t('pa.flow.configDetail'),
      name: 'configDetails'
    },
    {
      label: this.$t('pa.flow.relationImg'),
      name: 'relationChart'
    }
  ];
  private handleClick = debounce(() => this.$emit('click'));
}
</script>
<style lang="scss" scoped>
$marginTop: 15px;
$padding: 20px;
$tabHeight: 48px;
$borderColor: #f1f1f1;
.action-bar {
  &__container {
    position: relative;
    margin-top: 24px;
    height: $tabHeight;

    ::v-deep .el-tabs {
      border-top: 1px solid $borderColor;

      &__nav-wrap {
        padding-left: $padding;
      }

      &__item {
        padding: 0 $marginTop !important;
        height: $tabHeight;
        line-height: $tabHeight;
      }
    }
  }

  &__button {
    position: absolute;
    top: 50%;
    right: $padding;
    z-index: 15;
    transform: translateY(-50%);
  }
}
</style>
