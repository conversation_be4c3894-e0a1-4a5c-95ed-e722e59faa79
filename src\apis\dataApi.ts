import { del, get, post, put } from './utils/net';

// 获取选项列表
export const getItemList = (data: unknown) => {
  return post('/rs/pa/item/list', data);
};
// 删除选项列表项
export const delItems = (data: unknown) => {
  return del('/rs/pa/item/deleteById', {}, { data });
};

// 获取选项类型列表
export const getItemTypes = () => {
  return get('/rs/pa/item/allTypes');
};

// 新建选项
export const addItem = (data: unknown) => {
  return post('/rs/pa/item/add', data);
};

// 获取表选项 select
export const getItemListByType = (data: unknown) => {
  return get('/rs/pa/item/listByType', data);
};

// 服务类型列表
export const getResList = () => {
  return get('/rs/pa/component/connectorType/SQL');
};

// 获取视图列表
export const getViewList = (data: unknown) => {
  return post('/rs/pa/sql/view/list', data);
};

// 删除视图列表
export const delViews = (data: unknown) => {
  return del('/rs/pa/sql/view/deleteById', {}, { data });
};

// 视图模块获取数据表
export const getSearchableTables = () => {
  return get('/rs/pa/sql/view/searchableTables');
};

// 获取视图表信息
export const getTableInfo = (data: unknown) => {
  return post('/rs/pa/sql/view/tableInfo', data);
};

// 新建视图
export const addView = (data: unknown) => {
  return post('/rs/pa/sql/view/add', data);
};

// 更新视图
export const updateView = (data: unknown) => {
  return put('/rs/pa/sql/view/update', data);
};

// 获取视图详情
export const getViewDetail = (data: unknown) => {
  return get('/rs/pa/sql/view/findById', data);
};

// 视图代码预览
export const previewView = (data: unknown) => {
  return post('/rs/pa/sql/view/sql/preview', data);
};

// 获取视图历史列表
export const getViewHistory = (data: unknown) => {
  return post('/rs/pa/sql/viewHis/list', data);
};

// 获取历史视图的sql源码
export const getViewHistorySourceCode = (data: unknown) => {
  return get('/rs/pa/sql/viewHis/sql/sourceCode', data);
};

// 视图历史版本回滚
export const viewHistoryRollBack = (data: unknown) => {
  return put('/rs/pa/sql/viewHis/rollBack', data);
};

// 表管理 表列表数据
export const getChartList = (data: unknown) => {
  return post('/rs/pa/sql/table/list', data);
};

// 表管理 删除表
export const delCharts = (data: unknown) => {
  return del('/rs/pa/sql/table/deleteById', {}, { data });
};

// 表管理 - 详情 - 数据预览
export const dataPreviewForTable = (data: unknown) => {
  return get('/rs/pa/sql/table/sql/dataPreview', data);
};

// 试图理 - 详情 - 数据预览
export const dataPreviewForView = (data: unknown) => {
  return get('/rs/pa/sql/view/sql/dataPreview', data);
};

// 获取查询资源
export const sourceInfo = (type) => {
  return get(`/rs/pa/lineage/getResTypeColumn?resType=${type}`);
};
