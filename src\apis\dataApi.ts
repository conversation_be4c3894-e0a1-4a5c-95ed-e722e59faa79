import { del, get, post, put, download } from './utils/net';

// 获取选项列表
export const getItemList = (data: unknown) => {
  return post('/rs/pa/item/list', data);
};
// 删除选项列表项
export const delItems = (data: unknown) => {
  return del('/rs/pa/item/deleteById', {}, { data });
};

// 获取选项类型列表
export const getItemTypes = () => {
  return get('/rs/pa/item/allTypes');
};

// 新建选项
export const addItem = (data: unknown) => {
  return post('/rs/pa/item/add', data);
};

// 获取表选项 select
export const getItemListByType = (data: unknown) => {
  return get('/rs/pa/item/listByType', data);
};

// 服务类型列表
export const getResList = () => {
  return get('/rs/pa/component/connectorConfig/SQL');
};
// 服务类型列表
export const getResTypeList = () => {
  return get('/rs/pa/component/connectorType/SQL');
};

// 表管理-服务类型列表
export const getSheetManageResList = () => {
  return get('/rs/pa/component/connectorDetailType/SQL');
};

// 表管理 表列表数据
export const getChartList = (data: unknown) => {
  return post('/rs/pa/sql/table/list', data);
};

// 表管理 删除表
export const delCharts = (data: unknown) => {
  return del('/rs/pa/sql/table/deleteById', {}, { data });
};

// 表管理 - 详情 - 数据预览
export const dataPreviewForTable = (data: unknown) => {
  return get('/rs/pa/sql/table/sql/dataPreview', data);
};
// 表管理-详情-表字段-同步
export const getAutoSyncTable = (data: any) => {
  return post(`/rs/pa/sql/table/autoSync/${data.resType}`, data);
};
// 表管理-详情-表字段-字段类型枚举值
export const getTypeEnum = () => {
  return get('/rs/pa/dic/findByCode?code=flink_sql_data_type');
};
// 试图理 - 详情 - 数据预览
export const dataPreviewForView = (data: unknown) => {
  return get('/rs/pa/sql/view/sql/dataPreview', data);
};

// UDF管理-获取关联方法个数
export const countByName = (pattern: unknown) => {
  return get(`rs/fdl/func/countByName?pattern=${pattern}`);
};

// 获取查询资源
export const sourceInfo = (type) => {
  return get(`/rs/pa/lineage/getResTypeColumn?resType=${type}`);
};

// Catalog管理 - 列表数据
export const getCatalogList = (data: unknown) => {
  return post('/rs/pa/sql/catalog/list', data);
};

// Catalog管理 - 注册
export const addCatalog = (data: unknown) => {
  return post('/rs/pa/sql/catalog/add', data);
};
// Catalog管理 - 编辑更新
export const updateCatalog = (data: unknown) => {
  return put('/rs/pa/sql/catalog/update', data);
};

// Catalog管理 - 删除
export const delCatalog = (data: unknown) => {
  return del('/rs/pa/sql/catalog/delete', {}, { data });
};

// Catalog管理 - 获取catalog详情
export const viewCatalogDetail = (catalogId: string) => {
  return get(`/rs/pa/sql/catalog/basic?catalogId=${catalogId}`);
};

// Catalog管理 - 获取字段信息
export const getCatalogTableFields = (data: unknown) => {
  return post('/rs/pa/sql/catalog/tableFields', data);
};

// Catalog管理 - 获取表列表
export const getCatalogTableList = (catalogId: string) => {
  return get(`/rs/pa/sql/catalog/tableList?catalogId=${catalogId}`);
};
// Catalog管理 - 获取服务类型
export const getServerType = () => {
  return get(`/rs/pa/sql/catalog/catalogResList`);
};

// Catalog管理 - 获取服务名称列表
export const getServerNameList = (resType: string) => {
  return get(`/rs/pa/sql/catalog/listRes/${resType}`);
};

// Catalog管理 - 引用关系下载
export const downloadCatalog = (id: string) => {
  return download(`/rs/pa/sql/catalog/exportRelation?id=${id}`);
};

export const getPrefixList = () => {
  return get('/rs/pa/item/listByType?itemType=PRE_FIX');
};
export const getSuffixList = () => {
  return get('/rs/pa/item/listByType?itemType=SUF_FIX');
};
export const getJdbcTypeList = () => {
  return get('/rs/pa/sql/table/getJdbcRes');
};
export const getServiceList = (type: string) => {
  return post(`/rs/pa/res/listRes/${type}`);
};
export const getConnectorList = (type: string, jdbcType: string) => {
  return get(`/rs/pa/component/connectorInfo/${type}`, { jdbcType });
};
export const getTableData = (id: string) => {
  return get('/rs/pa/sql/table/findById', { id });
};
export const getColumnData = (resType: string) => {
  return get('/rs/pa/res/getResTitle', { resType });
};
export const tableDownload = (resType: string) => {
  return download(`/rs/pa/sql/table/download/${resType}`);
};
export const tableImport = (resType: string, data: any) => {
  return post(`/rs/pa/sql/table/import/${resType}`, data, {
    timeout: 1500000,
    headers: { ContentType: 'multipart/form-data' }
  });
};
export const sqlPreview = (data: any) => {
  return post('/rs/pa/sql/table/sql/preview', data);
};
export const tableExport = (resType: string, id: string) => {
  return download(`/rs/pa/sql/table/export/${resType}?id=${id}`);
};
export const addTable = (data: any) => {
  return post('/rs/pa/sql/table/add', data);
};
export const updateTable = (data: any) => {
  return put('/rs/pa/sql/table/update', data);
};
