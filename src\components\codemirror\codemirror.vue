<template>
  <codemirror
    ref="myCm"
    class="code-mirror"
    :value="code"
    :options="options"
    :height="height"
    @changes="valueChange"
  />
</template>

<script>
import { codemirror } from 'vue-codemirror';
import 'codemirror/lib/codemirror.css';

import CodeMirror from 'codemirror/lib/codemirror';

/*代码高亮支持的语言*/
import 'codemirror/mode/javascript/javascript.js';
import 'codemirror/mode/clike/clike.js';

/*引入css文件，用以支持主题*/
import 'codemirror/theme/eclipse.css';

/*引入js，绑定Vim*/
import 'codemirror/addon/dialog/dialog.css';
import 'codemirror/keymap/vim.js';
import 'codemirror/addon/search/searchcursor.js';

/*改善vim输入命令时的样式*/
import 'codemirror/addon/dialog/dialog.js';

/*支持代码折叠*/
import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/fold/foldcode.js';
import 'codemirror/addon/fold/foldgutter.js';
import 'codemirror/addon/fold/brace-fold.js';
import 'codemirror/addon/fold/comment-fold.js';

/*全屏模式*/
import 'codemirror/addon/display/fullscreen.css';
import 'codemirror/addon/display/fullscreen.js';

/*括号匹配*/
import 'codemirror/addon/edit/matchbrackets.js';

/*自动补全*/
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/show-hint.js';
import 'codemirror/addon/hint/anyword-hint.js';
import 'codemirror/addon/hint/javascript-hint.js';
import 'codemirror/addon/hint/sql-hint.js';

/*行注释*/
import 'codemirror/addon/comment/comment.js';

/*格式化*/
import './format';

/*代码检查错误*/
import 'codemirror/addon/lint/lint.css';
import 'codemirror/addon/lint/lint.js';

export default {
  name: 'CodeMirror',
  components: {
    codemirror
  },
  props: {
    options: Object,
    height: String,
    code: String,
    originCode: String
  },
  data() {
    return {
      codeValue: '',
      firstCome: false
    };
  },
  computed: {
    codemirror() {
      if (!this.$refs.myCm.codemirror.options.readOnly && !this.firstCome) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.firstCome = true;
        this.codemirror.setOption('extraKeys', {
          a: this.completeAfter,
          b: this.completeAfter,
          c: this.completeAfter,
          d: this.completeAfter,
          e: this.completeAfter,
          f: this.completeAfter,
          g: this.completeAfter,
          h: this.completeAfter,
          i: this.completeAfter,
          j: this.completeAfter,
          k: this.completeAfter,
          l: this.completeAfter,
          m: this.completeAfter,
          n: this.completeAfter,
          o: this.completeAfter,
          p: this.completeAfter,
          q: this.completeAfter,
          r: this.completeAfter,
          s: this.completeAfter,
          t: this.completeAfter,
          u: this.completeAfter,
          v: this.completeAfter,
          w: this.completeAfter,
          x: this.completeAfter,
          y: this.completeAfter,
          z: this.completeAfter,
          '.': this.completeAfter,
          ':': this.completeAfter,
          A: this.completeAfter,
          B: this.completeAfter,
          C: this.completeAfter,
          D: this.completeAfter,
          E: this.completeAfter,
          F: this.completeAfter,
          G: this.completeAfter,
          H: this.completeAfter,
          I: this.completeAfter,
          J: this.completeAfter,
          K: this.completeAfter,
          L: this.completeAfter,
          M: this.completeAfter,
          N: this.completeAfter,
          O: this.completeAfter,
          P: this.completeAfter,
          Q: this.completeAfter,
          R: this.completeAfter,
          S: this.completeAfter,
          T: this.completeAfter,
          U: this.completeAfter,
          V: this.completeAfter,
          W: this.completeAfter,
          X: this.completeAfter,
          Y: this.completeAfter,
          Z: this.completeAfter,
          // '=': this.completeIfInTag,
          'Ctrl-Q': 'autocomplete',
          Tab: function (cm) {
            const spaces = Array(cm.getOption('indentUnit') + 1).join(' ');
            cm.replaceSelection(spaces);
          }
        });
      }
      return this.$refs.myCm.codemirror;
    }
  },
  mounted() {
    this.firstCome = false;
  },
  methods: {
    valueChange(mirrorObj, data) {
      const value = this.codemirror.getValue();
      if (data[0].origin === '+delete') {
        this.codeValue = value;
      } else {
        this.codeValue = value ? value : this.originCode;
      }

      if (!value) {
        this.codemirror.setValue(this.codeValue);
      }
      this.$emit('saveCode', this.codeValue);
    },
    // 代码格式化
    autoFormat() {
      const totalLines = this.codemirror.lineCount();
      this.codemirror.autoFormatRange({ line: 0, ch: 0 }, { line: totalLines });
      this.codemirror.setSelection({ line: 0, ch: 0 }, { line: 0, ch: 0 });
    },
    completeAfter(cm) {
      cm.showHint({
        completeSingle: false
      });
      return CodeMirror.Pass;
    },
    periodCompleteAfter(cm) {
      const cur = cm.getCursor();
      const curLine = cm.getLine(cur.line);
      const end = cur.ch;
      const start = end;
      const data = {
        nowSymbol: '.',
        preSourceCode: curLine
      };
      return prompt(data).then(
        (resp) => {
          if (resp.data.success) {
            const hintList = resp.data.data.map((re) => '.' + re);
            cm.showHint({
              completeSingle: false,
              hint: () => {
                return {
                  list: hintList,
                  from: CodeMirror.Pos(cur.line, start),
                  to: CodeMirror.Pos(cur.line, end)
                };
              }
            });
          }
        },
        () => {
          this.$Message.error(this.$t('cube.scripts.recycleList.alertTip5'));
        }
      );
    },

    getValueBeforeCursor(curLine) {
      let value = '';
      for (let i = 0; i < curLine; i++) {
        value += this.codemirror.getLine(i);
      }
      return value;
    }
  }
};
</script>
<style scoped></style>
