<template>
  <pro-grid
    type="info"
    title="字段条件"
    tooltip="条件逻辑：字段相同是or，字段不同是and。"
    style="margin-bottom: 20px; width: 100%"
  >
    <el-button slot="operation" type="primary" size="small" @click="addCondition(conditions)">
      添加条件
    </el-button>
    <div :class="['tab-content', isError ? 'error' : '']">
      <el-table class="rule-table" :data="conditions" style="width: 100%">
        <el-table-column prop="condition" label="条件" width="100">
          <div slot-scope="scope">条件{{ scope.$index + 1 }}</div>
        </el-table-column>
        <el-table-column prop="fieldName" label="判断字段">
          <div slot-scope="scope">
            <el-select
              :value="conditions[scope.$index].fieldName"
              filterable
              @visible-change="handleVisibleChange"
              @change="
                (val) => {
                  handleFieldChange(conditions[scope.$index], val);
                }
              "
              @focus="removeError"
            >
              <el-option
                v-for="item in fieldOptions"
                :key="item.fieldName"
                :label="item.fieldName"
                :value="item.fieldName + '/' + item.fieldType"
              />
            </el-select>
          </div>
        </el-table-column>
        <el-table-column prop="fieldType" label="字段类型" />
        <el-table-column prop="operator" label="操作符">
          <div slot-scope="scope">
            <el-select v-model="conditions[scope.$index].operator" @focus="removeError">
              <el-option v-for="sItem in symbolLists" :key="sItem" :label="sItem" :value="sItem" />
            </el-select>
          </div>
        </el-table-column>
        <el-table-column prop="fieldValue" label="值">
          <div slot-scope="scope">
            <el-input
              v-model="conditions[scope.$index].fieldValue"
              placeholder="请输入"
              @focus="removeError"
            />
          </div>
        </el-table-column>
        <el-table-column prop="action" label="操作" width="100" align="center">
          <div slot-scope="scope">
            <i
              class="pointer iconfont icon-shanchu"
              @click="delCondition(conditions, scope.$index)"
            ></i>
          </div>
        </el-table-column>
      </el-table>
      <p class="error-tips">{{ errorMsg }}</p>
    </div>
  </pro-grid>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({})
export default class FieldRule extends Vue {
  @Prop({
    default() {
      return [];
    }
  })
  conditions!: any[];
  @Prop({
    default() {
      return [];
    }
  })
  fieldLists!: any[];
  @Prop() status!: '0' | '1' | '2';
  // 操作类型
  symbolLists = ['=', '<', '>', '>=', '<=', 'in'];
  // 可选字段数据
  fieldOptions: any[] = [];
  isError = false;
  errorMsg = '';
  // 设置错误状态
  setError(msg) {
    this.isError = true;
    this.errorMsg = msg;
  }
  removeError() {
    this.isError = false;
    this.errorMsg = '';
  }
  // 添加条件
  addCondition() {
    this.conditions.push({
      fieldName: '',
      fieldType: '',
      operator: '',
      fieldValue: ''
    });
  }
  // 删除条件
  delCondition(conditions, index) {
    conditions.splice(index, 1);
  }
  // 远程搜索
  remoteMethod(query) {
    if (query !== '') {
      this.fieldOptions = this.fieldLists.filter((item) => {
        return item.fieldName.toLowerCase().indexOf(query.toLowerCase()) > -1;
      });
    }
  }
  // 处理select展开收起事件
  handleVisibleChange(val) {
    if (val) {
      this.fieldOptions = [...this.fieldLists];
    }
  }
  // 字段选择后填充字段类型
  handleFieldChange(condition, selectField) {
    const values = selectField.split('/');
    condition.fieldName = values[0];
    condition.fieldType = values[1];
  }
}
</script>
<style lang="scss" scoped>
.tab-content {
  width: 100%;
  border-top: 1px solid $--bs-color-border-lighter;
  padding: 20px 25px;
  .error-tips {
    display: none;
    color: $--bs-color-red;
    padding-top: 4px;
  }
}
.tab-content.error {
  ::v-deep .el-table {
    border: 1px solid $--bs-color-red;
  }
  .error-tips {
    display: block;
  }
}
.pointer {
  cursor: pointer;
}
.rule-table {
  border: 1px solid $--bs-color-border-lighter;
  border-bottom: none;
}
</style>
