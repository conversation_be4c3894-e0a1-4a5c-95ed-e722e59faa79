<template>
  <div>
    <!-- 复合操作菜单 -->
    <action-bar
      :is-batch.sync="isBatch"
      :is-checked-all.sync="isCheckedAll"
      :indeterminate="indeterminate"
      @click="handleClick"
      @search="handleSearch"
      @change="isCheckedAllChange"
    />
    <!-- 流程列表 -->
    <node-list
      :data="flowList"
      :is-batch="isBatch"
      :flow-id="flowId"
      :loading="loading"
      @click="getCurFlow"
      @change="getSelected"
      @rightClick="rightClick"
    />
    <!-- 批量流程配置 -->
    <batch-online-dialog
      v-if="batchOnlineDialogVisible"
      :project-id="projectId"
      :list="selectedData"
      :status="statusFlag"
      :show.sync="batchOnlineDialogVisible"
      @close="updateFlow"
    />

    <add-flow-dialog
      v-if="addFlowDialogVisible"
      :project-id="projectId"
      :visible.sync="addFlowDialogVisible"
      @close="handleAddEditFlowDialogClose"
    />
    <!-- 复制/移动流程弹窗 -->
    <batch-copy-dialog
      v-if="batchCopyDialogVisible"
      :list="selectedData"
      :show.sync="batchCopyDialogVisible"
      :is-copy="isCopyDialog"
      @close="handleBatchCopyDialogClose"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Inject, Watch } from 'vue-property-decorator';
import { getFlowList, exportFlows, deleteFlows } from '@/apis/flowNewApi';
import ActionBar from './action-bar.vue';
import NodeList from './node-list.vue';
import { SET_FLOW_SEARCH_OBJ } from '@/store/event-names/mutations';
@Component({
  name: 'FlowList',
  components: {
    ActionBar,
    NodeList,
    'add-flow-dialog': () => import('./modals/add-flow.vue'),
    'batch-online-dialog': () => import('@/components/batch-online-dialog/index.vue'),
    'batch-copy-dialog': () => import('../../project/components/batch-copy-dialog.vue')
  }
})
export default class FlowList extends Vue {
  @Inject()
  publish!: Function;
  @Inject()
  cancelPublish!: Function;
  @Inject()
  offline!: Function;

  flowId: any = '';
  selectedFlowIds: any = [];
  loading = true;
  flowList: any = [];
  searchObj: any = {};
  statusFlag = false; // 是否基于上次状态启动
  isBatch = false; // 是否批量操作
  isCheckedAll = false; // 是否全选

  addFlowDialogVisible = false;
  batchCopyDialogVisible = false;
  isCopyDialog = false;
  batchOnlineDialogVisible = false;

  // 是否半选
  get indeterminate() {
    return Boolean(
      this.selectedFlowIds.length && this.selectedFlowIds.length < this.flowList.length
    );
  }

  get projectId() {
    return this.$route.query.id;
  }

  get selectedData() {
    return this.flowList.filter((el) => {
      return this.selectedFlowIds.includes(el.id);
    });
  }

  @Watch('isBatch')
  isBatchChange(val: boolean) {
    if (!val) {
      this.selectedFlowIds = [];
      this.flowList.forEach((el: any) => {
        this.$set(el, 'checked', false);
      });
    }
    val && this.fetchList();
  }
  isCheckedAllChange(val: boolean) {
    this.selectedFlowIds = [];
    this.flowList.forEach((el: any) => {
      this.$set(el, 'checked', val);
      if (val) {
        this.selectedFlowIds.push(el.id);
      }
    });
  }

  created() {
    this.searchObj = this.getSearchObj();
  }

  activated() {
    // 如果搜索条件和默认打开该tab的条件不一致，取store里的搜索条件
    if (this.$store.state.job.flowSearchObj) {
      this.searchObj = this.$store.state.job.flowSearchObj;
      this.$store.commit(SET_FLOW_SEARCH_OBJ, null);
    }
    this.flowId = this.$route.query?.flowId;
    this.fetchList();
  }

  getSearchObj() {
    const jobStatus = this.$route.query.state;
    return {
      id: this.projectId,
      name: '',
      jobStatus: jobStatus || 'ALL',
      jobType: 'ALL',
      jobRunTimeStatus: 'ALL',
      mode: 'ALL'
    };
  }

  async handleBatchCopyDialogClose() {
    this.selectedFlowIds = [];
    this.isCheckedAll = false;
    await this.fetchList();
    this.isNoFlowId();
  }

  getSelected(isChecked: boolean, data: any) {
    if (isChecked) {
      this.selectedFlowIds.push(data.id);
    } else {
      this.selectedFlowIds.splice(this.selectedFlowIds.indexOf(data.id), 1);
    }
    this.isCheckedAll =
      this.selectedFlowIds.length && this.selectedFlowIds.length === this.flowList.length;
  }

  // 批量操作：调用父组件【flow/inde.vue】里的方法[value方法名，state下拉选中的值（停止、停止并保留状态）]
  handleClick({ value, state }) {
    if (value !== 'add' && value !== 'refresh') {
      if (this.selectedFlowIds.length > 0) {
        // 其余操作必须选中流程后方可操作
        if (value === 'offline') {
          this[value]({
            state,
            flowIds: this.selectedFlowIds.map((n: any) => {
              return {
                jobId: n,
                savepoint: state
              };
            })
          });
          return;
        } else if (value === 'online') {
          this[value](state);
          return;
        }
        this[value]({ state, flowIds: this.selectedFlowIds, isBatch: true }); // isBatch只用于在批量发布【publish】时不进行前后端校验的判断
        return;
      }
      this.$message.warning('请选择要操作的流程');
    } else {
      this[value]();
    }
  }

  //右击菜单点击操作
  rightClick(value) {
    this.selectedFlowIds = [this.flowId];
    this[value]();
  }

  config() {
    this.$emit('config');
  }

  add() {
    this.addFlowDialogVisible = true;
  }

  refresh() {
    this.fetchList();
  }

  copy() {
    this.isCopyDialog = true;
    this.batchCopyDialogVisible = true;
  }

  move() {
    this.isCopyDialog = false;
    this.batchCopyDialogVisible = true;
  }

  //启动流程
  online(state) {
    if (this.selectedFlowIds.length > 0) {
      this.statusFlag = state;
      if (state) {
        this.batchOnlineDialogVisible = true;
      } else {
        // 无状态启动 进行提示
        this.$confirm('确定无状态启动吗？', '提示', {
          type: 'warning'
        })
          .then(() => {
            this.batchOnlineDialogVisible = true;
          })
          .catch(() => false);
      }
    } else {
      this.$tip.warning('请选择要启动的流程');
    }
  }

  async export(param = false) {
    const exportAll = typeof param === 'boolean';
    const ids = exportAll ? this.flowList.map((el) => el.id) : this.selectedFlowIds;
    const loading = this.$loading({ lock: true, text: '导出中，请稍等...' });
    const res = await exportFlows(ids);
    if (res.blob && res.fileName) {
      const responseUrl = window.URL.createObjectURL(new Blob([res.blob]));
      const link = document.createElement('a');
      link.href = responseUrl;
      link.setAttribute('download', res.fileName); // 文件名
      link.click();
      window.URL.revokeObjectURL(responseUrl); // 释放掉blob对象);
      this.$message.success(
        `成功导出${exportAll ? this.flowList.length : this.selectedFlowIds.length}个流程`
      );
    } else {
      if (res instanceof Blob) {
        const reader = new FileReader();
        reader.onload = (e: any) => {
          const { error: message } = JSON.parse(e.target.result);
          this.$message({
            message,
            type: 'error'
          });
        };
        reader.readAsText(res);
      }
    }
    loading.close();
  }

  // 获取当前流程
  getCurFlow(data) {
    if (this.isBatch) return; //批量操作不可点击切换流程
    // 当前项目下无流程：id为undefined，从卡片列表进来flowId为undefined
    if (data.id && this.flowId !== data.id) {
      this.flowId = data.id;
      // 如果搜索条件和默认打开该tab的条件不一致，保存搜索条件
      this.$store.commit(
        SET_FLOW_SEARCH_OBJ,
        JSON.stringify(this.searchObj) === JSON.stringify(this.getSearchObj())
          ? null
          : this.searchObj
      );
      this.$emit('flowClick', { ...data, state: this.searchObj.jobStatus });
    }
  }

  updateFlow() {
    this.selectedFlowIds = [];
    this.isCheckedAll = false;
    this.$emit('update');
    this.fetchList();
  }

  // 删除流程
  async delete() {
    this.$confirm('确认删除该流程吗?', '删除流程', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(async () => {
      const { success, msg } = await deleteFlows(this.selectedFlowIds);
      if (success) {
        this.$message.success(msg);
        await this.fetchList();
        this.isNoFlowId();
        this.selectedFlowIds = [];
        return;
      }
      this.$message.error(msg);
    });
  }

  // 搜索
  handleSearch(obj: any) {
    this.searchObj = Object.assign(this.searchObj, obj);
    this.fetchList();
  }

  handleAddEditFlowDialogClose() {
    this.fetchList();
  }

  async fetchList() {
    !this.searchObj.jobStatus && (this.searchObj.jobStatus = 'ALL');
    this.loading = true;
    this.flowList = [];
    const { success, data, msg } = await getFlowList(this.searchObj);
    this.loading = false;
    if (success) {
      this.$nextTick(() => {
        this.flowList = data.children;
      });
      return;
    }
    this.$message.error(msg);
  }
  //移动或删除后判断选中的流程还在不在
  isNoFlowId() {
    if (!this.flowList.length) {
      this.$emit('flowClick', {
        jobName: this.$route.query.name,
        state: this.searchObj.jobStatus
      });
    } else if (!this.flowList.find((item) => item.id === this.flowId)) {
      this.$emit('flowClick', {
        ...this.flowList[0],
        state: this.searchObj.jobStatus
      });
    }
  }
}
</script>

<style lang="scss" scoped></style>
