<template>
  <div v-loading="flowListLoading">
    <!-- 复合操作菜单 -->
    <action-bar
      ref="actionBarRef"
      :is-batch.sync="isBatch"
      :is-checked-all="isCheckedAll"
      :indeterminate="indeterminate"
      @click="handleClick"
      @search="handleSearch"
      @select-all="handleSelectAll"
    />
    <!-- 流程列表 -->
    <bs-tree
      v-if="flowList.length"
      ref="bsTree"
      :value="flowId || dirId || (hasQuery ? '' : flowList[0].nodeId)"
      :data="flowList"
      :show-checkbox="isBatch"
      :props="treeProps"
      node-key="nodeId"
      :default-expand-all="hasQuery"
      :expand-on-click-node="false"
      :default-expanded-keys="expandedKeys"
      @change="handleTreeCheckChange"
    >
      <div slot-scope="{ label, actived, level, raw }" class="tree-node" @click="handelNodeClick(raw)">
        <!-- 目录 -->
        <template v-if="raw.nodeType === 'PROJECT'">
          <i class="el-icon-folder"></i>
          <el-tooltip v-hide effect="light" placement="top" :content="label">
            <span class="dir-name">{{ label }}</span>
          </el-tooltip>
          <span>（{{ raw.jobCount }}）</span>
          <bs-popover-menu
            v-if="actived && !isBatch"
            :list="getProjectOperationMenu(level)"
            @click="(v) => menuItemClick(v, raw)"
          />
        </template>
        <!-- 流程 -->
        <template v-if="raw.nodeType === 'JOB'">
          <span class="node-type">{{ raw.jobTypeName }}</span>
          <el-tooltip v-hide effect="light" placement="top" :content="label">
            <span class="flow-name">{{ label }}</span>
          </el-tooltip>
          <bs-popover-menu v-if="actived && !isBatch" :list="flowOperationMenu" @click="(v) => menuItemClick(v, raw)" />
          <el-tooltip :open-delay="500" effect="light" placement="right" :content="getStatusText(raw.jobStatus, raw.nodeId)">
            <span
              :class="getStatusClass(raw)"
              @click="errorInfos[raw.nodeId] && showErrorMsg(raw.nodeId)"
              @click.stop
            ></span>
          </el-tooltip>
        </template>
      </div>
    </bs-tree>
    <bs-empty v-if="!flowList.length && !loading" />
    <!-- 批量流程配置 -->
    <batch-resource-config-dialog
      v-if="batchOnlineDialogVisible"
      :show.sync="batchOnlineDialogVisible"
      :list="selectedData"
      :status="statusFlag"
      @close="hanldeResourceConfigClose"
    />
  </div>
</template>

<script lang="ts">
import { Component, Ref, Vue, Watch } from 'vue-property-decorator';
import { getFlowList, deleteFlows, delDir } from '@/apis/flowNewApi';
import ActionBar from './action-bar/index.vue';
import { ADD_LOAING_PROJECT_IDS, SET_FLOW_SEARCH_OBJ } from '@/store/event-name';
import { FLOW_STATUS_MAP, getErrorInfos, removeErrorInfo, setErrorInfo } from './service';
import { JobErrorMap, JobStatus, JobStatusMap, JobType, MsgType } from '../interface';
import './style/flow-list.scss';
import { openDirDetailDialog, openDirEditingDialog, openFlowEditingDialog, openFlowMovingDailog } from './modals';
import { hasPermission } from '@/utils';
import { transformLineErrorInfo } from '../utils';
// 存储流程报错信息
@Component({
  name: 'FlowList',
  components: {
    ActionBar,
    'batch-resource-config-dialog': () => import('@/components/resource-config/batch-resource-config-dialog.vue')
  }
})
export default class FlowList extends Vue {
  @Ref('actionBarRef') readonly actionBarRef!: any;
  @Ref('bsTree') readonly bsTree!: any;
  flowId: any = '';
  // NOTE:此处将projectId从计算树形变成常规属性是因为deactivated中无法读取计算属性
  projectId = this.$route.query.id;
  curFlow: any = {};
  selectedFlowIds: any = [];
  selectedData: any = [];
  loading = true;
  flowList: any = [];
  searchObj: any = {};
  statusFlag = false; // 是否基于上次状态启动
  isBatch = false; // 是否批量操作
  isCheckedAll = false; // 是否全选
  indeterminate = false;
  batchOnlineDialogVisible = false;
  flowListLoading = false;
  // 流程错误信息 {[flowId]: [error msg]}
  errorInfos = [];
  treeProps = { label: 'nodeName' };
  // 流程中间状态
  inStatus = ['INPUB', 'INPROD', 'INOFF'];

  // 项目下的流程总数 用于判断是否全选
  flowCount = 0;
  // 记录展开的节点
  expandedKeys: string[] = [];
  showDirEmpty = false;
  // 流程操作菜单
  get flowOperationMenu(): any {
    return [
      { label: this.$t('pa.flow.eFlow'), value: 'editFlow', access: 'PA.FLOW.FLOW_MGR.EDIT' },
      { label: this.$t('pa.flow.copyFlow'), value: 'copyFlow', access: 'PA.FLOW.FLOW_MGR.COPY' },
      { label: this.$t('pa.flow.moveFlow'), value: 'moveFlow', access: 'PA.FLOW.FLOW_MGR.MOVE' },
      { label: this.$t('pa.flow.delFlow'), value: 'delFlow', access: 'PA.FLOW.FLOW_MGR.DELETE' }
    ].filter((el) => hasPermission(el.access));
  }

  // 目录的最大层级
  get dirMaxLevel() {
    return this.$store.getters.projectLevelsNumber;
  }
  get dirId() {
    return this.$route.query.dirId;
  }
  // 存在搜索关键字
  get hasQuery() {
    return !!this.searchObj.name;
  }
  @Watch('isBatch')
  isBatchChange(val: boolean) {
    if (!val) {
      this.selectedFlowIds = [];
      this.indeterminate = false;
      this.bsTree.setAllChecked(false);
    }
  }
  async activated() {
    this.searchObj = this.getSearchObj(false);
    !this.searchObj.name && this.initActionBar(); // 重置actionBar
    this.flowId = this.$route.query?.flowId;
    this.expandedKeys = [this.flowId || this.dirId];
    await this.fetchList(true);
    // 初始化若不带flowId 选中第一项
    if (!this.flowId && !this.dirId && this.flowList[0]) {
      if (this.flowList[0].nodeType === 'PROJECT') {
        this.$emit('toggle-empty', (this.showDirEmpty = true));
      } else {
        this.handleFlowClick(this.flowList[0]);
      }
    }
    if (this.dirId || this.flowList.length === 0) {
      this.$emit('toggle-empty', (this.showDirEmpty = true));
    }
    // 切换流程保留流程树滚动位置
    this.initScroll();
  }
  removeScroll() {
    const scrollDom = this.$el.getElementsByClassName('vue-recycle-scroller')[0];
    scrollDom && scrollDom.removeEventListener('scroll', this.setTreeScrollTop);
  }
  deactivated() {
    this.setExpandNodes();
    this.removeScroll();
  }
  initScroll() {
    this.$nextTick(() => {
      const scrollDom = this.$el.getElementsByClassName('vue-recycle-scroller')[0];
      if (!scrollDom) return;
      scrollDom.addEventListener('scroll', this.setTreeScrollTop);
      // 从项目进入后重置滚动高度到0
      if (!this.flowId && !this.dirId) {
        this.setTreeScrollTop({ target: { scrollTop: 0 } });
      }
      // setTimeout等待bs-tree的dom加载完毕
      setTimeout(() => {
        scrollDom.scrollTop = this.getTreeScrollTop();
      }, 0);
    });
  }
  // 获取目录操作菜单
  getProjectOperationMenu(level) {
    return [
      { label: this.$t('pa.flow.detail'), value: 'dirDetail', access: 'PA.FLOW.CATALOGUE_MGR.VIEW' },
      { label: this.$t('pa.flow.eDir'), value: 'editDir', access: 'PA.FLOW.CATALOGUE_MGR.EDIT' },
      { label: this.$t('pa.flow.dDir'), value: 'delDir', access: 'PA.FLOW.CATALOGUE_MGR.DELETE' },
      { label: this.$t('pa.flow.creatFlow'), value: 'addFlow', access: 'PA.FLOW.FLOW_MGR.ADD' }
    ]
      .concat(
        level < this.dirMaxLevel - 2
          ? [{ label: this.$t('pa.flow.createSubDir'), value: 'addSubDir', access: 'PA.FLOW.CATALOGUE_MGR.ADD' }]
          : []
      )
      .filter((el) => hasPermission(el.access));
  }
  // 从store中获取存储的searchObj
  getSearchObjInStore() {
    const { flowSearchObj } = this.$store.state.job || {};
    flowSearchObj && this.$store.commit(SET_FLOW_SEARCH_OBJ, null);
    return { ...(flowSearchObj || {}) };
  }
  // 搜索条件和默认打开该tab的条件不一致，保存搜索条件
  setSearchObjInStore() {
    const isSearchChanged = JSON.stringify(this.searchObj) === JSON.stringify(this.getSearchObj());
    this.$store.commit(SET_FLOW_SEARCH_OBJ, isSearchChanged ? null : this.searchObj);
  }
  getSearchObj(isDefult = true) {
    const ALL = 'ALL';
    const { state: jobStatus } = this.$route.query;
    const DEFAULT_PARAMS = {
      id: this.projectId,
      name: '',
      jobStatus: jobStatus || ALL,
      jobType: ALL,
      jobRunTimeStatus: ALL,
      mode: ALL,
      clusterType: ALL
    };
    if (isDefult) return { ...DEFAULT_PARAMS };
    return { ...DEFAULT_PARAMS, ...this.getSearchObjInStore() };
  }
  // TODO
  // 批量操作：调用父组件【flow-new/design/index.vue】里的方法[value方法名，state下拉选中的值（停止、停止并保留状态、强制停止）]
  handleClick({ value, state }) {
    const selectedData = this.bsTree.getCheckedNodes().filter((i) => i.raw && i.raw.nodeType === 'JOB');
    this.selectedFlowIds = selectedData.map((i) => i.id);
    if (this.selectedFlowIds.length === 0) return this.$message.warning(this.$t('pa.flow.msg32'));
    // 其余操作必须选中流程后方可操作
    if (value === 'offline') {
      if (state !== 'retain') {
        const type = { stop: this.$t('pa.flow.stop'), force: this.$t('pa.flow.forceStop') }[state];
        const jobNames = selectedData
          .slice(0, 3)
          .map(({ label }) => `【${label}】`)
          .join('、');
        this.$confirm(
          this.$t('pa.flow.msg18', [
            type,
            jobNames,
            this.selectedFlowIds.length > 3 ? this.$t('pa.flow.deng') : '',
            this.selectedFlowIds.length
          ]),
          this.$t('pa.flow.tip'),
          { type: 'warning', customClass: 'flow-list__confirm' }
        ).then(() => {
          this.addLoadingProjectIds();
          this.$emit('offline', {
            state,
            flowIds: this.selectedFlowIds.map((n: any) => {
              return {
                jobId: n,
                savepoint: state === 'retain'
              };
            })
          });
        });
      } else {
        this.addLoadingProjectIds();
        this.$emit('offline', {
          state,
          flowIds: this.selectedFlowIds.map((n: any) => {
            return {
              jobId: n,
              savepoint: state === 'retain'
            };
          })
        });
      }
    } else if (value === 'online') {
      // 因流程列表的数据结构变更导致批量上线无法使用 做一层数据转换
      this.selectedData = selectedData.map(({ raw }) => ({
        id: raw.nodeId,
        projectId: raw.projectId,
        jobName: raw.nodeName,
        jobType: raw.jobType,
        memo: raw.memo,
        orgId: raw.orgId,
        originalJobName: raw.originalJobName,
        prefix: raw.prefix,
        suffix: raw.suffix,
        properties: raw.properties,
        jobStatus: raw.jobStatus
      }));
      this.online(state);
    } else if (value === 'publish') {
      this.addLoadingProjectIds();
      // isBatch只用于在批量发布【publish】时不进行前后端校验的判断
      const flows = selectedData.map((i) => ({ id: i.id, jobType: i.raw?.jobType || JobType.PROCESSFLOW }));
      this.$emit(value, { state, flows: flows, isBatch: true });
    } else if (value === 'cancelPublish') {
      this.addLoadingProjectIds();
      this.$emit(value, { state, flowIds: this.selectedFlowIds, isBatch: true });
    } else if (value === 'copyFlow') {
      this.copyFlow(selectedData.map((i) => ({ id: i.id, name: i.label })));
    } else if (value === 'moveFlow') {
      this.moveFlow(selectedData.map((i) => ({ id: i.id, name: i.label, jobStatus: (i.raw || {}).jobStatus })));
    } else {
      this[value]({ state, flowIds: this.selectedFlowIds, isBatch: true });
    }
  }

  //启动流程
  async online(state) {
    if (this.selectedFlowIds.length === 0) {
      return this.$tip.warning(this.$t('pa.flow.msg33'));
    }
    // 无状态启动 进行提示
    if (!state) {
      await this.$confirm(this.$t('pa.flow.msg17'), this.$t('pa.flow.tip'), {
        type: 'warning'
      });
    }
    this.statusFlag = state;
    this.batchOnlineDialogVisible = true;
  }

  // 处理流程点击
  handleFlowClick(data) {
    // 点击流程隐藏目录对应的空白区间
    if (!this.isBatch) this.$emit('toggle-empty', (this.showDirEmpty = false));
    // 批量操作 | 相同流程 不可点击切换流程
    if (this.isBatch || (this.flowId === data.nodeId && data.jobType !== 'UDJ')) return;
    this.flowId = data.nodeId;
    this.setSearchObjInStore();
    this.$emit('flow-click', { ...data, state: this.searchObj.jobStatus });
  }
  // 存储当前展开的节点id
  setExpandNodes() {
    if (!this.bsTree) return;
    sessionStorage.setItem(
      `EXPEND_NODES_${this.projectId}`,
      this.bsTree
        .getExpendNodes()
        .map((item) => item.id)
        .join('、')
    );
  }
  // 获取当前存在的节点
  getExpandNodes(expandedKeys: string[] = []) {
    const data: any = (sessionStorage.getItem(`EXPEND_NODES_${this.projectId}`) || '').split('、');
    return [...new Set(expandedKeys.concat(data).filter(Boolean))];
  }
  // 处理目录点击
  handelNodeClick(raw) {
    this.setExpandNodes();
    if (this.isBatch) return false;
    (this as any).$tabNav.updateTabTitle(this.$route.fullPath, raw.nodeName);
    if (raw.nodeType === 'JOB') {
      this.handleFlowClick(raw);
    } else {
      this.$emit('toggle-empty', (this.showDirEmpty = true));
    }
  }
  updateFlow() {
    this.selectedFlowIds = [];
    this.isCheckedAll = false;
    this.$emit('update');
    this.fetchList();
  }

  // 搜索
  handleSearch(obj: any) {
    this.searchObj = Object.assign(this.searchObj, obj || {});
    this.fetchList();
  }
  // 批量操作状态下 清空已经选择
  clearChecked() {
    if (!this.isBatch) return;
    this.isCheckedAll = false;
    this.indeterminate = false;
    this.selectedFlowIds = [];
    this.bsTree && this.bsTree.setAllChecked(false);
  }
  // 获取流程列表
  async fetchList(isInit = false) {
    this.expandedKeys = this.getExpandNodes(this.expandedKeys);
    !this.searchObj.jobStatus && (this.searchObj.jobStatus = 'ALL');
    this.loading = true;
    this.flowList = [];
    const { data = { children: [] } } = await getFlowList(this.searchObj);
    this.loading = false;
    this.flowCount = 0;
    this.curFlow = null;
    const tansform = (data, parentId) => {
      return data.map((item) => {
        // 映射流程 && 计算流程总数
        if (item.nodeType === 'JOB') {
          item.jobTypeName = FLOW_STATUS_MAP[item.jobType];
          this.flowCount++;
        }
        // 空目录禁止选中
        if (item.nodeType === 'PROJECT' && item.children.length === 0) {
          item.disabled = true;
        }
        item.parentId = parentId;
        item.children = tansform(item.children, item.nodeId);
        item.nodeId === this.flowId && (this.curFlow = item);
        return item;
      });
    };
    this.flowList = tansform(data.children, data.nodeId);
    this.clearChecked();
    // 若存在报错信息，进行处理
    this.errorInfos = getErrorInfos();
  }
  //移动或删除后判断选中的流程不存在后跳转至第一个节点
  toDefaultNode() {
    if (this.flowList.length) {
      this.setSearchObjInStore();
      const { nodeId, nodeName, nodeType } = this.flowList[0] || {};
      this.$router.push({
        name: 'refresh',
        query: {
          ...this.$route.query,
          state: this.searchObj.jobStatus,
          title: nodeName,
          flowId: nodeType === 'JOB' ? nodeId : '' // 若节点不是流程 flowId传空
        }
      });
    } else {
      this.$emit('toggle-empty', (this.showDirEmpty = true));
      this.$route.query.name && (this as any).$tabNav.updateTabTitle(this.$route.fullPath, this.$route.query.name);
    }
  }
  // 处理资源配置关闭
  async hanldeResourceConfigClose(data) {
    if (data) {
      data.forEach(({ jobId, success, errorInfo, msgType = MsgType.SIMPLE }) => {
        if (success) {
          return this.removeFlowError(jobId);
        }
        this.setFlowError(jobId, {
          type: JobStatus.PUB,
          data: [{ errorInfo }],
          msgType,
          msg: msgType === MsgType.DETAIL ? this.$t('pa.flow.msg34') : errorInfo
        });
      });
    }
    this.clearChecked();
    await this.fetchList();
    this.curFlow && this.$emit('update-flow', this.curFlow);
  }
  // 处理全选
  handleSelectAll(val) {
    this.isCheckedAll = val;
    this.selectedFlowIds = [];
    this.indeterminate = false;
    this.bsTree.setAllChecked(val);
  }
  // 处理树的选择
  handleTreeCheckChange(data, rows, child) {
    const checkedCount = this.bsTree.getCheckedNodes().filter((i) => i.raw && i.raw.nodeType === 'JOB').length;
    // 处理全选状态
    this.isCheckedAll = checkedCount === this.flowCount;
    this.indeterminate = !this.isCheckedAll && checkedCount > 0;
  }
  getStatusText(jobStatus: string, id) {
    const { errorInfos } = this;
    const { type } = JSON.parse(errorInfos[id] || '{}');
    return errorInfos[id] && JobErrorMap[type] ? JobErrorMap[type] : JobStatusMap[jobStatus];
  }

  // 根据流程状态展示不同颜色圆点
  getStatusClass({ jobStatus, nodeId }) {
    const { errorInfos, inStatus } = this;
    if (errorInfos[nodeId]) return 'el-icon-warning';
    if (inStatus.includes(jobStatus)) return 'el-icon-loading';
    const mapping = {
      PROD: 'node-status--green',
      PUB: 'node-status--blue'
    };
    return `node-status ${mapping[jobStatus] || 'node-status--grey'}`;
  }
  // 提示错误信息
  showErrorMsg(flowId: string) {
    const { errorInfo, msgType, msg, data } = JSON.parse(this.errorInfos[flowId] || '{}');
    // 当前流程为报错流程且错误类型为lineMessage 则将错误显示到代码上
    if (msgType === MsgType.LINE_MESSAGE) {
      if (flowId !== this.flowId || this.showDirEmpty) {
        // 直接return不去清除报错，进入对应流程页面再显示后移除报错
        return this.$tip.error({
          message: this.$t('pa.flow.msg38')
        });
      }
      this.$emit('setCodeError', transformLineErrorInfo(JSON.parse(errorInfo || '[]')));
    } else if (msgType === MsgType.NODE_MESSAGE) {
      if (flowId !== this.flowId || this.showDirEmpty) {
        return this.$tip.error({
          message: this.$t('pa.flow.msg259')
        });
      }
      this.$emit('setCanvasError', JSON.parse(errorInfo || '[]'));
    } else if (msgType === MsgType.DETAIL) {
      this.$tip.errorPro({ msgType, msg, data });
    } else {
      this.$tip.error({ message: errorInfo || msg, duration: 5000 });
    }
    this.removeFlowError(flowId);
  }
  // popper-menu click回调
  menuItemClick(value, raw) {
    this[value](raw);
  }
  // 新建流程
  async addFlow({ nodeId }) {
    const { projectId } = this;
    const needRefresh = await openFlowEditingDialog({ projectId, parentId: nodeId });
    needRefresh && this.fetchList();
  }
  // 编辑流程
  async editFlow({ nodeId, parentId, jobStatus }) {
    if (jobStatus !== 'DEV') return this.$message.error(this.$t('pa.flow.msg260'));
    const { projectId } = this;
    const needRefresh = await openFlowEditingDialog({ id: nodeId, projectId, parentId: parentId });
    if (needRefresh) {
      await this.fetchList();
      (this as any).$tabNav.updateTabTitle(this.$route.fullPath, this.curFlow.nodeName);
      this.curFlow && this.$emit('update-flow', this.curFlow);
    }
  }
  // 删除流程
  async delFlow(row) {
    await this.$confirm(this.$t('pa.flow.msg14'), this.$t('pa.flow.delFlow'));
    const { success, msg } = await deleteFlows(row.nodeId ? [row.nodeId] : this.selectedFlowIds);
    if (success) {
      this.$message.success(msg);
      await this.fetchList();
      this.toDefaultNode();
      return;
    }
    this.$message.error(msg);
  }
  // 复制流程
  async copyFlow(data: any) {
    const flows = Array.isArray(data) ? data : [{ id: data.nodeId, name: data.nodeName }];
    const needRefresh = await openFlowMovingDailog({ type: 'copy', flows });
    needRefresh && this.fetchList();
  }
  // 移动流程
  async moveFlow(data) {
    const flows = (
      Array.isArray(data) ? data : [{ id: data.nodeId, name: data.nodeName, jobStatus: data.jobStatus }]
    ).filter((i) => i.jobStatus === 'DEV');
    if (flows.length === 0) return this.$message.error(this.$t('pa.flow.msg261'));
    const needRefresh = await openFlowMovingDailog({ type: 'move', flows });
    if (needRefresh) {
      await this.fetchList();
      this.curFlow ? this.$emit('update-flow', this.curFlow) : this.toDefaultNode();
    }
  }
  // 查看目录详情
  dirDetail({ nodeId }) {
    openDirDetailDialog(nodeId);
  }
  // 编辑目录
  async editDir({ nodeId, nodeName, parentId }) {
    const { projectId } = this;
    const needRefresh = await openDirEditingDialog({ id: nodeId, projectName: nodeName, parentId: parentId, projectId });
    needRefresh && this.fetchList();
  }
  // 新建子目录
  async addSubDir({ nodeId }) {
    const { projectId } = this;
    const needRefresh = await openDirEditingDialog({ parentId: nodeId, projectId });
    needRefresh && this.fetchList();
  }
  // 删除目录
  async delDir({ nodeId }) {
    await this.$confirm(
      `<p>${this.$t('pa.flow.msg262')}</p>
      <p style="color: #777777;line-height: 20px;margin-top: 6px;">${this.$t('pa.flow.msg263')}</p>`,
      this.$t('pa.flow.dDir'),
      {
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }
    );
    const { success, msg } = await delDir({ id: nodeId });
    this.$message[success ? 'success' : 'error'](msg);
    await this.fetchList();
    this.flowId && this.$emit('toggle-empty', (this.showDirEmpty = false));
  }
  // 记录流程树的滚动距离
  setTreeScrollTop({ target }: any) {
    sessionStorage.setItem('scroll-top-' + this.$route.query.id, target.scrollTop);
  }
  getTreeScrollTop() {
    return Number(sessionStorage.getItem('scroll-top-' + this.$route.query.id)) || 0;
  }
  // 设置流程状态
  public setFlowStatus(flowId, status) {
    const target = this.flowList.find((item) => item.id === flowId);
    target && (target.jobStatus = status);
  }
  // 设置流程错误信息
  public setFlowError(flowId, errorMsg) {
    this.errorInfos = setErrorInfo(flowId, JSON.stringify(errorMsg));
  }
  // 移除流程报错信息
  public removeFlowError(flowId) {
    this.errorInfos = removeErrorInfo(flowId);
  }

  // 切换项目后，隐藏批量操作、搜索操作
  public initActionBar() {
    this.actionBarRef.rollback(); // 退出批量
    this.actionBarRef.showSelectInput = false;
    this.actionBarRef.name = '';
  }

  // 保存需要loading的项目Id，用于置灰批量操作：[publish]、[cancelPublish]、[online]、[offline]图标状态
  public addLoadingProjectIds() {
    this.$store.commit(ADD_LOAING_PROJECT_IDS, this.projectId);
  }
}
</script>

<style lang="scss" scoped>
.dir-name {
  flex: 0 1 auto;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.flow-name {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.node-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 4px;
  margin-right: 4px;
  cursor: pointer;

  &--green {
    background: $--bs-color-green;
  }
  &--blue {
    background: $--bs-color-primary;
  }
  &--grey {
    background: $--bs-color-text-placeholder;
  }
}
.el-icon-warning {
  font-size: 16px;
  color: $--bs-color-red;
  cursor: pointer;
  margin-right: 8px;
}
::v-deep .bs-tree-item--actived {
  background-color: #fff;
  .bs-tree-item-info {
    background-color: $--bs-color-background-base;
    border-radius: 4px;
  }
}
::v-deep .bs-tree-item:hover {
  background-color: #fff;
  .bs-tree-item-info {
    background-color: $--bs-color-background-light;
    border-radius: 4px;
  }
}
.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  transition: all ease 0.2;
  .el-icon-folder {
    font-size: 18px;
    margin-right: 8px;
  }
  .node-type {
    display: inline-block;
    width: 32px;
    height: 18px;
    line-height: 18px;
    margin-right: 8px;
    font-size: 12px;
    border-radius: 4px;
    text-align: center;
    background-color: $--bs-color-primary;
    color: #fff;
  }
  .bs-popover-menu {
    flex: 1 0 auto;
    padding-right: 4px;
    padding-bottom: 2px;
    text-align: right;
  }
  .flow-name + .bs-popover-menu {
    flex-grow: 0;
  }
}
</style>
