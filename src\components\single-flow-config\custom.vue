<template>
  <el-collapse class="advanced-config-container">
    <el-collapse-item name="custom">
      <div slot="title" class="resource-title">自定义参数</div>
      <div style="margin-bottom: 8px; text-align: right">
        <el-button size="small" @click="create">添加一行</el-button>
      </div>
      <bs-table :data="tableData" :column-data="columnData" :column-settings="false">
        <template slot="header-name" slot-scope="{ row }">
          <span style="color: red; margin-right: 4px">*</span>{{ row.label }}
        </template>
        <template slot="header-value" slot-scope="{ row }">
          <span style="color: red; margin-right: 4px">*</span>{{ row.label }}
        </template>
        <template slot="key" slot-scope="{ row }">
          <el-input
            v-if="row.edit"
            v-model="row.key"
            maxlength="200"
            class="input"
            oninput="value=value.replace(/[\u4E00-\u9FA5]/g,'')"
            :style="getrepeat(row.key) ? 'border-color: red' : 'border-color: #dcdfe6'"
          />
          <span v-else>{{ row.key }}</span>
        </template>
        <template slot="value" slot-scope="{ row }">
          <el-input v-if="row.edit" v-model="row.value" maxlength="200" />
          <span v-else>{{ row.value }}</span>
        </template>
        <template slot="action" slot-scope="{ row, $index }">
          <el-button type="text" @click="handleEdit($index)">
            {{ row.edit ? '保存' : '编辑' }}
          </el-button>
          <el-button type="text" @click="handleDel(row, $index)">删除</el-button>
        </template>
      </bs-table>
      <div style="color: red">{{ hasRepeat() }}</div>
    </el-collapse-item>
  </el-collapse>
</template>

<script lang="ts">
import { Vue, Component, Prop, PropSync, Watch } from 'vue-property-decorator';

@Component
export default class CustomConfig extends Vue {
  @Prop({ type: Object, default: () => ({}) }) property!: any;
  @Prop({ type: Object, default: () => ({}) }) data!: any;
  @Prop({ type: Boolean, default: false }) isCloud!: boolean;
  @PropSync('data', { type: Object, default: () => ({}) }) form!: any;
  columnData = [
    {
      label: '键',
      value: 'key'
    },
    {
      label: '值',
      value: 'value'
    },
    {
      label: '操作',
      value: 'action',
      width: 120
    }
  ];
  tableData: any = [];

  @Watch('data', { immediate: true })
  dataChange(val) {
    this.tableData = val.configs || [];
  }
  //重复提示
  hasRepeat() {
    const res = new Map();
    const list = this.tableData.filter((item) => item.key);
    list.forEach((el) => {
      if (!res.has(el.key)) {
        res.set(el.key, 1);
      }
    });
    return res.size !== list.length ? '存在相同键值' : '';
  }
  // 查看是否有重复数据
  getrepeat(val) {
    const has = this.tableData.filter((item) => item.key && item.key === val);
    return has.length > 1;
  }
  //添加一行
  create() {
    this.tableData.push({
      key: '',
      value: '',
      edit: true
    });
  }
  //删除
  handleDel(row, index) {
    this.$confirm(`您确定要删除${row.key}键吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      this.tableData.splice(index, 1);
    });
  }
  handleEdit(index) {
    this.tableData = this.tableData.map((item, target) => {
      return {
        ...item,
        edit: index === target ? !item.edit : item.edit
      };
    });
  }
  save() {
    const has = this.tableData.filter((item) => !item.key || !item.value);
    if (has.length) {
      this.$message.warning('自定义参数不能为空');
      return;
    }
    return true;
  }
}
</script>

<style scoped lang="scss">
.input {
  ::v-deep .el-input__inner {
    border-color: inherit;
  }
}
</style>
