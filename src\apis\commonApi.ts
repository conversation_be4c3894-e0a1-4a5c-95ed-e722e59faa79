/**
 *  得到用户信息
 */
export const URL_SESSION_USER = '/rs/pa/portal/session/user';
/**
 * 用户登录接口
 */
export const URL_LOGIN = '/j_spring_security_check';
/**
 * 用户登出
 */
export const URL_LOGOUT = '/j_spring_security_logout';

/*************字典功能接口 开始*************/
/**
 * 查询字典表数据
 */
export const URL_DIC_LIST = '/rs/pa/dic/list';
/**
 * 查找字典表数据
 */
export const URL_DIC_FIND = '/rs/pa/dic/findById';
/**
 * 删除字典表数据
 */
export const URL_DIC_DELETE = '/rs/pa/dic/deleteById';
/**
 * 添加字典表数据
 */
export const URL_DIC_ADD = '/rs/pa/dic/add';
/**
 * 修改字典表数据
 */
export const URL_DIC_UPDATE = '/rs/pa/dic/update';

export const URL_DIC_GETSUBBYPARENT = '/rs/pa/dic/getSubByParent';

export const URL_DIC_FINDBYCODE = '/rs/pa/dic/findByCode';

export const URL_DIC_GETALLPARENT = '/rs/pa/dic/getAllParent';
/**
 * 查询字典表下级数据
 */
export const URL_DIC_SUB_LIST = '/rs/pa/dic/subList';
/*************字典功能接口 结束*************/

/*************安装包管理功能接口 开始*************/
/**
 * 查询安装包表数据
 */
export const URL_INSTALLATIONPACKAGE_LIST = '/rs/pa/installationPackage/list';
/**
 * 删除安装包表数据
 */
export const URL_INSTALLATIONPACKAGE_DELETE = '/rs/pa/installationPackage/deleteById';
/**
 * 新建安装包表数据
 */
export const URL_INSTALLATIONPACKAGE_ADD = '/rs/pa/installationPackage/add';
/**
 * 更新安装包表数据
 */
export const URL_INSTALLATIONPACKAGE_UPDATE = '/rs/pa/installationPackage/update';
/**
 * 下载安装包表数据
 */
export const URL_INSTALLATIONPACKAGE_DOWNLOAD = '/rs/pa/installationPackage/download';
/**
 * 下载组件包
 */
export const URL_INSTALLATIONPACKAGE_DOWNLOAD_COM = '/rs/pa/installationPackage/downloadCom';
export const URL_INSTALLATIONPACKAGE_FIND = '/rs/pa/installationPackage/findById';
/*************安装包管理功能接口 结束*************/

/*************主机管理功能接口 开始*************/
/**
 * 查询主机表数据
 */
export const URL_HOST_LIST = '/rs/pa/host/list';
/**
 * 删除主机表数据
 */
export const URL_HOST_DELETE = '/rs/pa/host/deleteById';
/**
 * 更新主机表数据
 */
export const URL_HOST_UPDATE = '/rs/pa/host/update';
/**
 * 添加主机表数据
 */
export const URL_HOST_ADD = '/rs/pa/host/add';
/**
 * 主机登录测试
 */
export const URL_HOST_TESTCONNECT = '/rs/pa/host/testConnect';
/**
 * 主机文件上传
 */
export const URL_HOST_SCP = '/rs/pa/host/scp';
/**
 * 查询主句
 */
export const URL_HOST_FIND = '/rs/pa/host/findById';
/**
 * 查询主句
 */
export const URL_HOST_FIND_BY_TITLE = '/rs/pa/host/findByTitle';
/**
 * 查询主机引用关系
 */
export const URL_HOST_FINDDEPENDS = '/rs/pa/host/findDepends';
/**
 * 查询对应机构能引用的主机
 */
export const URL_HOST_LISTQUOTERES = '/rs/pa/host/listQuoteRes';
/*************主机管理功能接口 结束*************/
/*************主机管理功能接口 结束*************/

/*************数据分享管理接口 开始*************/
/**
 * 添加不能使用关系记录
 */
export const URL_RESNOTUSE_ADD = '/rs/pa/resnotuse/add';
/**
 * 查询不能使用资源的机构
 */
// export const URL_RESNOTUSE_LISTNOTUSE = '/rs/pa/resnotuse/listNotUse';
/**
 * 查询可以使用资源的机构
 */
export const URL_RESNOTUSE_LISTCANUSE = '/rs/pa/resnotuse/listCanUse';
/**
 * 改变自动分享开关
 */
export const URL_RESNOTUSE_CHANGE_AUTO_SHARE = '/rs/pa/resnotuse/changeAutoShare';
/*************数据分享管理接口 结束*************/

/*************Portal功能接口 开始*************/
/**
 * 获取机构ID开始往下的所有机构列表
 */
export const URL_PORTAL_LISTSUBORGS = '/rs/pa/portal/listSubOrgs';

export const URL_PORTAL_LISTOPERLOG = '/rs/pa/portal/listOperLog';

export const URL_PORTAL_EXPORTLOG = '/rs/pa/portal/exportLog';

export const URL_PORTAL_LISTSERVICEFORHOME = '/rs/pa/portal/listServiceForHome';

export const URL_PORTAL_LISTWARNFORHOME = '/rs/pa/portal/listWarnForHome';

export const URL_PORTAL_ALLORGS = 'rs/pa/portal/listAllOrgs';
/**
 * 获取机构ID下的所有用户
 */
export const URL_PORTAL_LISTUSERBYORGID = '/rs/pa/portal/listUserByOrgId';
/**
 * 获取流程的指标信息
 */
export const URL_PORTAL_GETFLOWFORHOME = '/rs/pa/portal/getFlowForHome';
/**
 * 获取平台名称
 */
export const URL_PORTAL_GETPLATFORM = '/rs/pa/portal/getPlatform';
/**
 * 获取用户信息
 */
export const URL_PORTAL_GETUSERINFO = '/rs/pa/portal/getUserInfo';

/*************Portal功能接口 结束*************/

/*************服务管理功能接口 开始*************/
/**
 * 查询服务表数据
 */
export const URL_RES_LIST = '/rs/pa/res/list';
/**
 * 删除服务表数据
 */
export const URL_RES_DELETE = '/rs/pa/res/deleteById';
/**
 * 添加服务表数据
 */
export const URL_RES_ADD = '/rs/pa/res/add';
/**
 * 更新服务表数据
 */
export const URL_RES_UPDATE = '/rs/pa/res/update';
/**
 * 通过ID查找服务表数据
 */
export const URL_RES_FINDBYID = '/rs/pa/res/findById';
/**
 * 服务连通性检查
 */
export const URL_RES_TESTCONNECT = '/rs/pa/res/testConnect';
/**
 * 查询引用关系列表
 */
export const URL_RES_FINDDEPENDS = '/rs/pa/res/listRelation';

/**
 * 查询对应机构能引用的资源
 */
export const URL_RES_LISTQUOTERES = '/rs/pa/res/listQuoteRes';
/*************服务管理功能接口 结束*************/

/*************服务详细信息功能接口 开始*************/
/**
 * 查询服务表数据
 */
export const URL_RES_DETAIL_AEROSPIKE_CACHEINFO = '/rs/pa/res/detail/aerospike/cacheInfo';
export const URL_RES_DETAIL_AEROSPIKE_GETCACHEDATA = '/rs/pa/res/detail/aerospike/getCacheData';

/**
 * mysql运行sql
 */
export const URL_RES_DETAIL_MYSQL_SQL = '/rs/pa/res/detail/mysql/sql';

/**
 * 获取kafka所有topic
 */
export const URL_RES_DETAIL_KAFKA_CREATETOPIC = '/rs/pa/res/detail/kafka/createTopic';

/**
 * 获取kafka所有topic
 */
export const URL_RES_DETAIL_KAFKA_TOPIC_LIST = '/rs/pa/res/detail/kafka/topicList';
/**
 * 删除topic
 */
export const URL_RES_DELETE_KAFKA_TOPIC = '/rs/pa/res/detail/kafka/delete';
/**
 * 分享topic
 */
export const URL_RES_SHARE_KAFKA_TOPIC = '/rs/pa/res/detail/kafka/share';
/**
 * 返回已授权的组织机构
 */
export const URL_RES_SHARE_KAFKA_LIST_ORGS = '/rs/pa/res/detail/kafka/listOrgsById';

/**
 * 获取kafka所有topic
 */
export const URL_RES_DETAIL_KAFKA_TOPIC = '/rs/pa/res/detail/kafka/topic';

/**
 * 查看topic 消费情况
 */
export const URL_RES_DETAIL_KAFKA_DESCRIBEGROUP = '/rs/pa/res/detail/kafka/describeGroup';
/**
 * 获取topic上的消费者
 */
export const URL_RES_DETAIL_KAFKA_GROUP = '/rs/pa/res/detail/kafka/group';
/**
 * 获取topic上最近几条记录
 */
export const URL_RES_DETAIL_KAFKA_PREVIEW = '/rs/pa/res/detail/kafka/preview';
/**
 * 获取决策引擎规则列表
 */
export const URL_RES_DETAIL_SDM_RULE = '/rs/pa/res/detail/sdm/rule';
/**
 * 获取决策引擎规则列表
 */
export const URL_RES_DETAIL_REDIS_CACHEINFO = '/rs/pa/res/detail/redis/cacheInfo';
export const URL_RES_DETAIL_REDIS_GETCACHEDATA = '/rs/pa/res/detail/redis/getCacheData';
/**
 * 获取flink的overview信息
 */
export const URL_RES_DETAIL_FINK_OVERVIEW = '/rs/pa/res/detail/flink/overview';
/**
 * 获取flink的jobmanagerConf信息
 */
export const URL_RES_DETAIL_FINK_JOBMANAGERCONF = '/rs/pa/res/detail/flink/jobmanagerConf';
/**
 * 获取flink的taskmanagers信息
 */
export const URL_RES_DETAIL_FINK_TASKMANAGERS = '/rs/pa/res/detail/flink/taskmanagers';
/**
 * 获取rocketmq的所有topic列表
 */
export const URL_RES_DETAIL_ROCKETMQ_ALLTOPICS = '/rs/pa/res/detail/rocketmq/allTopics';
/**
 * 获取rocketmq的所有topic列表
 */
export const URL_RES_DETAIL_ROCKETMQ_GROUPS = '/rs/pa/res/detail/rocketmq/groups';
/**
 * 获取rocketmq的消费情况
 */
export const URL_RES_DETAIL_ROCKETMQ_DESCRIBEGROUP = '/rs/pa/res/detail/rocketmq/describeGroup';
/**
 * 获取rocketmq预览数据
 */
export const URL_RES_DETAIL_ROCKETMQ_PREVIEW = '/rs/pa/res/detail/rocketmq/preview';
/**
 * 获取rocketmq创建topic
 */
export const URL_RES_DETAIL_ROCKETMQ_CREATETOPIC = '/rs/pa/res/detail/rocketmq/createTopic';
/*************服务详细信息功能接口 结束*************/

/*************服务节点功能接口 开始*************/
/**
 * 查询服务节点
 */
export const URL_RES_NODE_LIST = '/rs/pa/resNode/listNode';
/**
 * 查询服务节点
 */
export const URL_RES_NODE_LISTFORMONITOR = '/rs/pa/resNode/listNodeForMonitor';
/**
 * 添加服务节点
 */
export const URL_RES_NODE_ADD = '/rs/pa/resNode/addNode';
/**
 * 更新服务节点
 */
export const URL_RES_NODE_UPDATE = '/rs/pa/resNode/updateNode';
/**
 * 删除服务节点
 */
export const URL_RES_NODE_DELETE = '/rs/pa/resNode/deleteNode';
/**
 * 安装服务节点
 */
export const URL_RES_NODE_INSTALL = '/rs/pa/resNode/install';
/**
 * 启动服务节点
 */
export const URL_RES_NODE_START = '/rs/pa/resNode/start';
/**
 * 停止服务节点
 */
export const URL_RES_NODE_STOP = '/rs/pa/resNode/stop';
/*************服务节点功能接口 结束*************/

/*************预警规则功能接口 开始*************/
/**
 * 查询预警规则表数据
 */
export const URL_WARNRULE_LIST = '/rs/pa/warnRule/list';
/**
 * 根据资源ID查询预警规则表数据
 */
export const URL_WARNRULE_LISTBYRESID = '/rs/pa/warnRule/listByResId';

export const URL_WARNRULE_FIND = '/rs/pa/warnRule/findById';
export const URL_WARNRULE_UPDATE = '/rs/pa/warnRule/update';
export const URL_WARNRULE_UPDATESTATE = '/rs/pa/warnRule/updateState';

export const URL_WARNRECORD_LISTBYRULEID = '/rs/pa/warnRecord/listByRuleId';
export const URL_WARNRECORD_FIND = '/rs/pa/warnRecord/findById';
export const URL_WARNRECORD_UPDATESTATE = '/rs/pa/warnRecord/updateState';
export const URL_WARNRECORD_UPDATEALLSTATE = '/rs/pa/warnRecord/updateAllState';
/*************预警规则功能接口 结束*************/

/*************流程设计功能接口 结束*************/
export const URL_PROJECT_ADD = '/rs/pa/project/add';
export const URL_PROJECT_UPDATE = '/rs/pa/project/update';
export const URL_PROJECT_DELETE = '/rs/pa/project/deleteById';
export const URL_PROJECT_LIST = '/rs/pa/project/list';
export const URL_PROJECT_FINDBYID = '/rs/pa/project/findById';
export const URL_PROJECT_LISTBYORGID = '/rs/pa/project/getProjectsByOrgId';

export const URL_JOB_ADD = '/rs/pa/job/add';
export const URL_JOB_DELETE = '/rs/pa/job/deleteById';
export const URL_JOB_FINDBYID = '/rs/pa/job/findById';
export const URL_JOB_UPDATE = '/rs/pa/job/update';
export const URL_JOBRES_UPDATE_LIST = '/rs/pa/job/updateResList';
export const URL_JOB_RESOURCECONFIG = '/rs/pa/job/resourceConfig';
export const URL_JOB_PRECOMPILE = '/rs/pa/job/preCompile';
export const URL_JOB_COMPILE = '/rs/pa/job/compileCode';
export const URL_JOB_PREONLINE = '/rs/pa/job/preOnline';
export const URL_JOB_ONLINE = '/rs/pa/job/online';
export const URL_JOB_OFFLINE = '/rs/pa/job/offline';
export const URL_JOB_RESTART = '/rs/pa/job/restart';
export const URL_JOB_PREPUBLISH = '/rs/pa/job/prePublish';
export const URL_JOB_PUBLISH = '/rs/pa/job/publish';
export const URL_JOB_CANCEL_PUBLISH = '/rs/pa/job/cancelPublish';
export const URL_JOB_COPY = '/rs/pa/job/copy';
export const URL_JOB_MOVE = '/rs/pa/job/move';
export const URL_JOB_SEARCH = '/rs/pa/job/search';
export const URL_JOB_VAERSION_COMPONENT_LIST = '/rs/pa/job/getJobComponents';
export const URL_JOB_MONITOR_LIST = '/rs/pa/job/jobMonitor';
export const URL_JOB_STATUS = '/rs/pa/job/jobStatusStatistics';
export const URL_JOB_SOURCECODE = '/rs/pa/job/sourceCode';
export const URL_JOB_COM_REF = '/rs/pa/job/componentDataGlobalRef';
export const URL_COMPONENT_SEARCH = '/rs/pa/component/search';
export const URL_COMPONENT_DELETE = '/rs/pa/component/deleteById';
export const URL_COMPONENT_LIST = '/rs/pa/component/list';
export const URL_COMPONENT_SAVE = '/rs/pa/component/save';
export const URL_COMPONENT_BATCH_SAVE = '/rs/pa/component/batchSave';
export const URL_COMPONENT_UPDATE_ICON = '/rs/pa/component/updateIcon';
export const URL_COMPONENT_FIND_BY_ID = '/rs/pa/component/findById';
// 获取所有共享+独享返回不为void的方法列表
export const URL_COMPONENT_FUNC_LIST = '/rs/pa/component/common/notVoidFuncList';
export const URL_EXTRACT_JSON = '/rs/pa/component/extract/json';

export const URL_HISTORY_LIST = '/rs/pa/job/historyList';
export const URL_VERSION_LIST = '/rs/pa/job/versionList';
export const URL_VERSION_JOB_LIST = '/rs/pa/job/getJobByJobVersionId';

export const URL_TEST_DATA_LIST = '/rs/pa/job/test/data/list';
export const URL_TEST_MONITOR_RULE = '/rs/pa/job/mvel/test';
export const URL_TEST_DATA_ADD = '/rs/pa/job/test/data/add';
export const URL_TEST_DATA_DELETE = '/rs/pa/job/test/data/delete';
export const URL_TEST_DATA_UPDATE = '/rs/pa/job/test/data/update';
export const URL_TEST_DATA_VIEW = '/rs/pa/job/test/data/view';
export const URL_TEST_DATA_RUN = '/rs/pa/job/test/run';
export const URL_TEST_DATA_RUN_SQL = '/rs/pa/job/test/runSql';
export const URL_TEST_DATA_SOURCELIST = '/rs/pa/job/test/data/sourceList';

export const URL_COMPONENT_METRICS = '/rs/pa/job/componentMetrics';

export const URL_LOG_REPLAY = '/rs/pa/job/logReplay';
export const URL_LOG_DOWNLOAD = '/rs/pa/job/downloadWarnData';
export const URL_IQ_DATA_DOWNLOAD = '/rs/pa/job/downloadInferiorQualityData';
/*************流程设计功能接口 结束*************/

/*************资产功能接口 结束*************/
export const URL_ASSETS_FUNC_NOT_VOID_RETURN_LIST = '/rs/pa/assets/getFuncsNotVoidReturn';
export const URL_ASSETS_FUNC_BOOLEAN_RETURN_LIST = '/rs/pa/assets/getFuncsBooleanReturn';
export const URL_ASSETS_PROMPT_LIST = '/rs/pa/assets/getPrompt';
/*************资产功能接口 结束*************/

/*************资源类型及配置信息接口 开始*************/
/**
 * 获取服务资源和流程资源类型的列表
 */
export const URL_RESCONF_GETRESTYPELIST = '/rs/pa/resConf/getResTypeList';
/**
 * 获取服务资源和流程资源类型的列表
 */
export const URL_RESCONF_GETALLTYPELIST = '/rs/pa/resConf/getAllTypeList';
/**
 * 获取表单配置
 */
export const URL_RESCONF_GETFORMCONF = '/rs/pa/resConf/getFormConf';
/*************资源类型及配置信息接口 结束*************/

/*************资源配置接口 开始*************/
/**
 * 根据集群id和机构id获取此集群分配详情页面的基本信息
 */
export const URL_FLINK_CLUSTERMSG = '/rs/pa/clusterResConf/clusterMsg';
/**
 * 根据机构id和集群id获取下级机构资源详情
 */
export const URL_FLINK_CHILDRENORGRESLIST = '/rs/pa/clusterResConf/childrenOrgResList';
/**
 * 调整和分配给下级机构的资源
 */
export const URL_FLINK_ALLOCATED = '/rs/pa/clusterResConf/allocated';
/**
 * 根据机构id获取本机构资源汇总信息
 */
export const URL_FLINK_ORGRESMSG = '/rs/pa/clusterResConf/orgResMsg';
/**
 * 根据机构 id 获取 队列信息
 */
export const URL_FLINK_QUEUENAME = '/rs/pa/clusterResConf/getQueueName';
/**
 * 选中相应队列名称后重新获取其他展示的数据
 */
export const URL_FLINK_QUEUETOMSG = '/rs/pa/clusterResConf/allocateMsg';
/**
 * 根据注册类型获取队列列表数据
 */
export const URL_FLINK_QUEUELIST = '/rs/pa/clusterResConf/getQueueList';
/*************资源配置接口 结束*************/

/* 流程导出 */
export const URL_JOB_EXPORT = '/rs/pa/job/export';
/* 流程导入文件上传 */
export const URL_JOB_IMPORT_FILE = '/rs/pa/job/importFile';
/* 流程导入重复文件处理 */
export const URL_JOB_IMPORT_JOB = '/rs/pa/job/importJob';
/* 刷新集群信息 */
export const URL_REFRESH_CLUSTER_MSG = '/rs/pa/clusterResConf/refreshClusterMsg';
/* 获取流程状态 */
export const URL_JOB_STATUS_LIST = '/rs/pa/job/jobStatusEnum';

/*************数据管理接口 开始*************/
export const URL_JOB_TABLE_LIST = '/rs/pa/sql/table/list';
// 数据管理服务类型
export const URLTYPE_SQL = '/rs/pa/component/connectorType/SQL';
export const URLLISTRES = '/rs/pa/res/listRes/';
export const URL_DATASOURCE = '/rs/pa/sql/table/dataSource/';
export const URL_LISTBYTYPE = '/rs/pa/item/listByType?itemType=PRE_FIX';
export const URL_LISTBYTYPE_SUFFIX = '/rs/pa/item/listByType?itemType=SUF_FIX';
// export const URL_ITEMLIST = '/rs/pa/item/list';
export const URL_FINDBYCODE = '/rs/pa/dic/findByCode?code=flink_sql_data_type';
// export const URL_ALLTYPES = '/rs/pa/item/allTypes';
export const URL_ITEMADD = '/rs/pa/sql/table/add';
// export const URL_OPTION_ADD = '/rs/pa/item/add';
export const URL_UPDATE = '/rs/pa/sql/table/update';
// export const URL_DELETE_BY_ID = '/rs/pa/item/deleteById';
export const URL_TABLE_DELETE_BY_ID = '/rs/pa/sql/table/deleteById';
export const URL_TABLE_FIND_BY_ID = '/rs/pa/sql/table/findById';
export const URL_TABLE_RESCONF = 'rs/pa/sql/table/dataSourceConfig?file=resConf/';
export const URL_TABLE_SHARESYSORG = '/rs/pa/sql/table/listShareSysOrg';
export const URL_VIEW_SHARESYSORG = '/rs/pa/sql/view/listShareSysOrg';
export const URL_TABLE_SHARESYSORGPUT = '/rs/pa/sql/table/shareSqlTable';
export const URL_VIEW_SHARESYSORGPUT = '/rs/pa/sql/view/shareSqlView';
export const URL_TABLE_DOWNLOAD = '/rs/pa/sql/table/download';
export const URL_TABLE_IMPORT = '/rs/pa/sql/table/import';
export const URL_TABLE_EXPORT = '/rs/pa/sql/table/export';
export const URL_APPROVAL_APPLY = '/rs/pa/approval/apply';
export const URL_CONNECTORINFO = '/rs/pa/component/connectorInfo/';
export const URL_TABLEHIS = '/rs/pa/sql/tableHis/list';
export const URL_TABLEHIS_ROLLLBACK = '/rs/pa/sql/tableHis/rollBack';
export const URL_VIEW_SOURCECODE = '/rs/pa/sql/viewHis/sql/sourceCode';
export const URL_VIEWHIS = '/rs/pa/sql/viewHis/list';
export const URL_VIEWHIS_ROLLLBACK = '/rs/pa/sql/viewHis/rollBack';
export const URL_SQL_PREVIEW = '/rs/pa/sql/table/sql/preview';
export const URL_AUTOSYNC = '/rs/pa/sql/table/autoSync/';
export const URL_ADVANCEFIELD = '/rs/pa/dic/findByCode?code=advanceFieldInfo';
export const URL_COMPARE_INIT = '/rs/pa/sql/table/compareInit';
export const URL_COMPARE_VERSION = '/rs/pa/sql/table/compareVersion';
export const URL_LISTVERSION = '/rs/pa/sql/tableHis/listVersion';
export const URL_GETSUBBYPARENT = '/rs/pa/dic/getSubByParent?parentCode=PaSqlTableNotes';
/* 视图管理 */
// export const URL_VIEW_LIST = '/rs/pa/sql/view/list';
// export const URL_VIEW_DELETE = '/rs/pa/sql/view/deleteById';
// export const URL_VIEW_ADD = '/rs/pa/sql/view/add';
export const URL_VIEW_FIND = '/rs/pa/sql/view/findById';
// export const URL_VIEW_SEARCHABLETABLES = '/rs/pa/sql/view/searchableTables';
export const URL_VIEW_PREVIEW = '/rs/pa/sql/view/sql/preview';
export const URL_VIEW_TABLEINFO = '/rs/pa/sql/view/tableInfo';
// export const URL_VIEW_UPDATE = '/rs/pa/sql/view/update';
/*************数据管理接口 结束*************/
/*************UDF接口 结束*************/
export const URL_UDF_ADD = '/rs/pa/sql/udf/add';
export const URL_UDF_DELETE = '/rs/pa/sql/udf/delete';
export const URL_UDF_FINDBYID = '/rs/pa/sql/udf/findById';
export const URL_UDF_DOWLOAD = '/rs/pa/sql/udf/download';
export const URL_UDF_TEST = '/rs/pa/sql/udf/test';
export const URL_UDF_TESTSQL = '/rs/pa/sql/udf/udfTestSql';
export const URL_UDF_TEST_FUNC = '/rs/pa/sql/udf/testFunc';
export const URL_UDF_TEST_SQL = '/rs/pa/sql/udf/testSql';
export const URL_UDF_UPDATE = '/rs/pa/sql/udf/update';
export const URL_UDF_LIST = '/rs/pa/sql/udf/list';
export const URL_UDF_SHARESYS = '/rs/pa/sql/udf/listShareSysOrg';
export const URL_UDF_UPDATESQLUDF = '/rs/pa/sql/udf/shareSqlUdf';
export const URL_UDF_UDFHIS = '/rs/pa/sql/udfHis/list';
export const URL_UDF_ROLLBACK = '/rs/pa/sql/udfHis/rollback';
export const URL_UDF_USELIST = '/rs/pa/sql/udf/useList';
export const URL_UDF_TEMPLATE = '/rs/pa/sql/udf/download/template';
export const URL_UDF_DOWNLOAD = '/rs/pa/sql/udf/download';
export const URL_UDF_TEMPLATE_CODE = '/rs/pa/sql/udf/getTemplateByUdfType';
export const URL_UDF_DELETE_VALIDATE = '/rs/pa/sql/udf/validateById';
export const URL_UDF_ADD_ON_WRITE = '/rs/pa/sql/udf/addOnWrite';
export const URL_UDF_ADD_ON_UPLOAD = '/rs/pa/sql/udf/addOnUpload';
export const URL_UDF_UPDATE_ON_WRITE = '/rs/pa/sql/udf/updateOnWrite';
export const URL_UDF_UPDATE_ON_UPLOAD = '/rs/pa/sql/udf/updateOnUpload';
export const URL_UDF_ASSEMBLE_CODE = '/rs/pa/sql/udf/assembleSourceCode';
export const URL_UDF_PARSE_UDF_FUNC = '/rs/pa/sql/udf/parseUdfFunc';
export const URL_UDF_TEST_ALL = '/rs/pa/sql/udf/testAll';

/*************UDF接口 结束*************/

/*************机构资源接口 开始*************/

export const URL_JAR_LIST_PRIVATE_JAR = 'rs/pa/jar/listPrivateJarAndClass';
export const URL_JAR_LIST_SHARE_JAR = 'rs/pa/jar/listShareJarAndClass';
export const URL_FUNC_ADD = '/rs/pa/func/add';
export const URL_FUNC_LIST = '/rs/pa/func/list';
export const URL_FUNC_BODY = '/rs/pa/func/body';
export const URL_FUNC_HISTORY_LIST = '/rs/pa/func/listHis';
export const URL_FUNC_HISTORY_ROLL_BACK = '/rs/pa/func/rollback';
export const URL_COMMON_FUNC_LIST = 'rs/fdl/func/listBySearchData';
export const URL_OTHER_CLASS_LIST = 'rs/fdl/jar/listByName';
export const URL_FUNC_DELETE = '/rs/pa/func/deleteById';
export const URL_FUNC_FIND = '/rs/pa/func/findById';
export const URL_FUNC_VALIDATE = '/rs/pa/func/validate';
export const URL_FUNC_ADDMANUALIMPORT = '/rs/pa/func/addManualImport';
export const URL_FUNC_LISTCOMMITWITHSCOPE = '/rs/pa/func/listCommitWithScope';
export const URL_FUNC_LISALLSCOPE = '/rs/pa/func/listAllScope';

export const URL_FUNC_FUNCTEST = '/rs/pa/func/funcTest';
export const URL_FUNC_PARSE_FUNC = '/rs/pa/func/parseFunc';
export const URL_FUNC_DOWNLOAD = '/rs/pa/func/download';
export const URL_FUNC_FORMAT = '/rs/pa/func/format';
export const URL_FUNC_INPUT = '/rs/pa/func/funcInputArgs';
/*************机构资源接口 结束*************/
/*************审批管理接口 开始*************/
/* 申请资源 */
export const URL_APPROVAL_APP = '/rs/pa/approval/application';
/* 审批列表 */
export const URL_APPROVAL_LIST = '/rs/pa/approval/list';
/* 审批 */
export const URL_APPROVAL_STATUS = '/rs/pa/approval/status';
/* 我的审批 */
export const URL_APPROVAL_MINE = '/rs/pa/approval/myApplication';
/*************审批管理接口 结束*************/
/* 获取所有表和视图信息 */
export const URL_GET_ALL_TABLE = '/rs/pa/sql/table/getAllTable';
/* 获取所有表和视图信息 */
export const URL_GET_TABLE_FIELDS = '/rs/pa/sql/table/getTableFields';
/* 获取连接器属性 */
export const URL_GET_CONNECTOR_INFO = '/rs/pa/component/connectorInfo';
/* 获取输出视图 */
export const URL_GET_ANALYSIS_VIEW = '/rs/pa/component/sql/analysisView';
/* 获取所有表信息 */
export const URL_GET_ALL_TABLE_INFO = '/rs/pa/sql/table/getAllTable/TABLE';
/* 获取视图字段 */
export const URL_SQL_ANALYSIS_FIELDS = '/rs/pa/component/sql/analysisFields';
/* 批模式流程启动 */
export const URL_SQL_BATCH_JOB = '/rs/pa/job/sqlBatchJobOnline';
/*************引用关系相关接口 开始*************/
/* 获取流程-表/流程-视图引用关系列表 */
export const URL_GET_TABLE_RELATION_LIST = '/rs/pa/sql/table/listRelation';
/* 导出字段信息到excel文件 */
export const URL_EXPORT_TABLE_RELATION_LIST = '/rs/pa/sql/table/exportRelation';
/* 获取流程-视图引用关系列表 */
export const URL_GET_VIEW_RELATION_LIST = '/rs/pa/sql/view/listRelation';
/* 导出字段信息到excel文件 */
export const URL_EXPORT_VIEW_RELATION_LIST = '/rs/pa/sql/view/exportRelation';
/* 获取流程-视图引用关系列表 */
export const URL_GET_UDF_RELATION_LIST = '/rs/pa/sql/udf/listRelation';
/* 导出字段信息到excel文件 */
export const URL_EXPORT_UDF_RELATION_LIST = '/rs/pa/sql/udf/exportRelation';
/* 获取流程-视图引用关系列表 */
export const URL_GET_TOPIC_RELATION_LIST = '/rs/pa/res/detail/kafka/listRelation';
/* 导出字段信息到excel文件 */
export const URL_EXPORT_TOPIC_RELATION_LIST = '/rs/pa/res/detail/kafka/exportRelation';
/* 导出服务管理引用关系列表 */
export const URL_EXPORT_SERVICE_RELATION_LIST = '/rs/pa/res/exportRelation';
/* 获取引用关系列表 */
export const URL_GET_FUNC_UDF_RELATION_LIST = '/rs/pa/func/listRelation/UDF';
export const URL_GET_FUNC_JOB_RELATION_LIST = '/rs/pa/func/listRelation/JOB';
export const URL_GET_FUNC_CEP_RELATION_LIST = '/rs/pa/func/listRelation/CEP';

/* 判断有无引用关系 */
export const URL_HAS_RELATION_LIST = '/rs/pa/res/serverRelationCount';
/*************流程源码对比相关接口 开始*************/
/* 获取历史流程源码 */
export const URL_GET_HIS_SOURCE_CODE = '/rs/pa/job/hisSourceCode';
/*************流程源码对比相关接口 结束*************/
/* 上线进度查询 */
export const URL_JOB_PROGRESS = '/rs/pa/job/progress';
export const URL_RES_FLINK = '/rs/pa/res/flink';

/*************导入导出接口 开始*************/
export const URL_EXPIMP_GETRESLIST = '/rs/pa/expImp/getResList';
export const URL_EXPIMP_DOWNLOADRECORDS = '/rs/pa/expImp/downloadRecords';
export const URL_EXPIMP_IMPORTUPLOAD = '/rs/pa/expImp/importUpload';
export const URL_EXPIMP_IMPORTSUBMIT = '/rs/pa/expImp/importSubmit';
/*************导入导出接口 结束*************/
/**
 *  获取服务端信息
 */
export const serverCfg = '/pa/openapi/serverCfg';

/* 过滤组件接口 start */
export const URL_GET_FUNC_LIST = '/rs/pa/filter/static/getFuncList';
export const URL_GET_FUNC_BODY = '/rs/pa/filter/static/getFuncBody';
/* 过滤组件接口 end */

export const URL_SERVICE_LIST = '/rs/pa/portal/listQuoteRes/JDBC';
export const URL_TABLE_LIST = '/rs/pa/portal/getSubList/JDBC';
export const URL_TABLE_FIEID = '/rs/pa/res/detail/mysql/column';
export const URL_DEPLOY_CONFIG = '/rs/pa/conf/deployConf';
