// 查询字典表数据
export const URL_DIC_LIST = '/rs/pa/dic/list';
// 查找字典表数据
export const URL_DIC_FIND = '/rs/pa/dic/findById';
// 删除字典表数据
export const URL_DIC_DELETE = '/rs/pa/dic/deleteById';
// 添加字典表数据
export const URL_DIC_ADD = '/rs/pa/dic/add';
// 修改字典表数据
export const URL_DIC_UPDATE = '/rs/pa/dic/update';
export const URL_DIC_GETSUBBYPARENT = '/rs/pa/dic/getSubByParent';
// 查询字典表下级数据
export const URL_DIC_SUB_LIST = '/rs/pa/dic/subList';

// 查询安装包表数据
export const URL_INSTALLATIONPACKAGE_LIST = '/rs/pa/installationPackage/list';
// 删除安装包表数据
export const URL_INSTALLATIONPACKAGE_DELETE = '/rs/pa/installationPackage/deleteById';
// 新建安装包表数据
export const URL_INSTALLATIONPACKAGE_ADD = '/rs/pa/installationPackage/add';
// 更新安装包表数据
export const URL_INSTALLATIONPACKAGE_UPDATE = '/rs/pa/installationPackage/update';
// 下载安装包表数据
export const URL_INSTALLATIONPACKAGE_DOWNLOAD = '/rs/pa/installationPackage/download';
// 下载组件包
export const URL_INSTALLATIONPACKAGE_DOWNLOAD_COM = '/rs/pa/installationPackage/downloadCom';
export const URL_INSTALLATIONPACKAGE_FIND = '/rs/pa/installationPackage/findById';

// 查询主机表数据
export const URL_HOST_LIST = '/rs/pa/host/list';
// 删除主机表数据
export const URL_HOST_DELETE = '/rs/pa/host/deleteById';
// 更新主机表数据
export const URL_HOST_UPDATE = '/rs/pa/host/update';
// 添加主机表数据
export const URL_HOST_ADD = '/rs/pa/host/add';
// 主机登录测试
export const URL_HOST_TESTCONNECT = '/rs/pa/host/testConnect';
// 主机文件上传
export const URL_HOST_SCP = '/rs/pa/host/scp';
// 查询主句
export const URL_HOST_FIND = '/rs/pa/host/findById';
export const URL_HOST_FIND_BY_TITLE = '/rs/pa/host/findByTitle';
// 查询主机引用关系
export const URL_HOST_FINDDEPENDS = '/rs/pa/host/findDepends';
// 查询对应机构能引用的主机
export const URL_HOST_LISTQUOTERES = '/rs/pa/host/listQuoteRes';

// 添加不能使用关系记录
export const URL_RESNOTUSE_ADD = '/rs/pa/resnotuse/add';
export const URL_SHARE_RESOURCE_BATCH = '/rs/pa/resnotuse/shareResourceBatch';

// 获取服务分享节点树
export const URL_SHAR_NODETREE = '/rs/pa/resnotuse/listShareSysOrg';

// 获取分享机构
export const URL_SYS_ORG = '/rs/pa/resnotuse/allOrSharedSysOrg';
export const URL_KAFKA_SYS_ORG = '/rs/pa/res/detail/kafka/allOrSharedSysOrg';

// 获取分享权限
export const URL_LIST_SHARE_AUTHORITIES = '/rs/pa/resnotuse/listShareAuthorities';

// 获取机构已分享的topic
export const URL_LIST_SHARED_TOPIC = '/rs/pa/res/detail/kafka/listSharedTopic';

// 改变自动分享开关
export const URL_RESNOTUSE_CHANGE_AUTO_SHARE = '/rs/pa/resnotuse/changeAutoShare';

export const URL_PORTAL_LISTOPERLOG = '/rs/pa/portal/listOperLog';
export const URL_PORTAL_EXPORTLOG = '/rs/pa/portal/exportLog';
// 获取机构ID下的所有用户
export const URL_PORTAL_LISTUSERBYORGID = '/rs/pa/portal/listUserByOrgId';
// 获取用户信息
export const URL_PORTAL_GETUSERINFO = '/rs/pa/portal/getUserInfo';

// 查询服务表数据
export const URL_RES_LIST = '/rs/pa/res/list';
// 删除服务表数据
export const URL_RES_DELETE = '/rs/pa/res/deleteById';
// 添加服务表数据
export const URL_RES_ADD = '/rs/pa/res/add';
// 更新服务表数据
export const URL_RES_UPDATE = '/rs/pa/res/update';
// 通过ID查找服务表数据
export const URL_RES_FINDBYID = '/rs/pa/res/findById';
// 服务连通性检查
export const URL_RES_TESTCONNECT = '/rs/pa/res/testConnect';
// 查询服务表数据
export const URL_RES_DETAIL_AEROSPIKE_CACHEINFO = '/rs/pa/res/detail/aerospike/cacheInfo';
export const URL_RES_DETAIL_AEROSPIKE_GETCACHEDATA = '/rs/pa/res/detail/aerospike/getCacheData';
// 获取kafka所有topic
export const URL_RES_DETAIL_KAFKA_CREATETOPIC = '/rs/pa/res/detail/kafka/createTopic';
// 获取kafka所有topic
export const URL_RES_DETAIL_KAFKA_TOPIC_LIST = '/rs/pa/res/detail/kafka/topicAvailableList';
// 分享topic
export const URL_RES_SHARE_KAFKA_TOPIC = '/rs/pa/res/detail/kafka/share';
// 改造后分享 topic
export const URL_SHARE_TOPIC_BATCH = '/rs/pa/res/detail/kafka/shareTopicBatch';
// kafka分享Topic
export const URL_SHARE_KAFKA_TOPIC = '/rs/pa/res/detail/kafka/listShareSysOrg';

// 查看topic 消费情况
export const URL_RES_DETAIL_KAFKA_DESCRIBEGROUP = '/rs/pa/res/detail/kafka/describeGroup';
// 获取topic上的消费者
export const URL_RES_DETAIL_KAFKA_GROUP = '/rs/pa/res/detail/kafka/group';
// 获取topic上最近几条记录
export const URL_RES_DETAIL_KAFKA_PREVIEW = '/rs/pa/res/detail/kafka/previewTopicData';
// 获取决策引擎规则列表
export const URL_RES_DETAIL_SDM_RULE = '/rs/pa/res/detail/sdm/rule';
// 获取决策引擎规则列表
export const URL_RES_DETAIL_REDIS_CACHEINFO = '/rs/pa/res/detail/redis/cacheInfo';
export const URL_RES_DETAIL_REDIS_GETCACHEDATA = '/rs/pa/res/detail/redis/getCacheData';
// 获取flink的jobmanagerConf信息
export const URL_RES_DETAIL_FINK_JOBMANAGERCONF = '/rs/pa/res/detail/flink/jobmanagerConf';
// 获取flink的taskmanagers信息
export const URL_RES_DETAIL_FINK_TASKMANAGERS = '/rs/pa/res/detail/flink/taskmanagers';
// 获取rocketmq的所有topic列表
export const URL_RES_DETAIL_ROCKETMQ_ALLTOPICS = '/rs/pa/res/detail/rocketmq/allTopics';
// 获取rocketmq的所有topic列表
export const URL_RES_DETAIL_ROCKETMQ_GROUPS = '/rs/pa/res/detail/rocketmq/groups';
// 获取rocketmq的消费情况
export const URL_RES_DETAIL_ROCKETMQ_DESCRIBEGROUP = '/rs/pa/res/detail/rocketmq/describeGroup';
// 获取rocketmq预览数据
export const URL_RES_DETAIL_ROCKETMQ_PREVIEW = '/rs/pa/res/detail/rocketmq/preview';
// 获取rocketmq创建topic
export const URL_RES_DETAIL_ROCKETMQ_CREATETOPIC = '/rs/pa/res/detail/rocketmq/createTopic';
// 查询服务节点
export const URL_RES_NODE_LIST = '/rs/pa/resNode/listNode';
export const URL_RES_NODE_LISTFORMONITOR = '/rs/pa/resNode/listNodeForMonitor';
// 添加服务节点
export const URL_RES_NODE_ADD = '/rs/pa/resNode/addNode';
// 更新服务节点
export const URL_RES_NODE_UPDATE = '/rs/pa/resNode/updateNode';
// 删除服务节点
export const URL_RES_NODE_DELETE = '/rs/pa/resNode/deleteNode';
// 安装服务节点
export const URL_RES_NODE_INSTALL = '/rs/pa/resNode/install';
// 启动服务节点
export const URL_RES_NODE_START = '/rs/pa/resNode/start';
// 停止服务节点
export const URL_RES_NODE_STOP = '/rs/pa/resNode/stop';

// 查询预警规则表数据
export const URL_WARNRULE_LIST = '/rs/pa/warnRule/list';
// 根据资源ID查询预警规则表数据
export const URL_WARNRULE_LISTBYRESID = '/rs/pa/warnRule/listByResId';
export const URL_WARNRULE_FIND = '/rs/pa/warnRule/findById';
export const URL_WARNRULE_UPDATE = '/rs/pa/warnRule/update';
export const URL_WARNRULE_UPDATESTATE = '/rs/pa/warnRule/updateState';
export const URL_WARNRECORD_LISTBYRULEID = '/rs/pa/warnRecord/listByRuleId';
export const URL_WARNRECORD_UPDATESTATE = '/rs/pa/warnRecord/updateState';
export const URL_WARNRECORD_UPDATEALLSTATE = '/rs/pa/warnRecord/updateAllState';

export const URL_JOB_FINDBYID = '/rs/pa/job/findById';
export const URL_JOB_OFFLINE = '/rs/pa/job/offline';
export const URL_JOB_RESTART = '/rs/pa/job/restart';
export const URL_JOB_MONITOR_LIST = '/rs/pa/job/jobMonitor';
export const URL_JOB_STATUS = '/rs/pa/job/jobStatusStatistics';
export const URL_JOB_SOURCECODE = '/rs/pa/job/sourceCode';

export const URL_COMPONENT_SEARCH = '/rs/pa/component/search';
export const URL_COMPONENT_DELETE = '/rs/pa/component/deleteById';
export const URL_COMPONENT_LIST = '/rs/pa/component/list';
export const URL_COMPONENT_SAVE = '/rs/pa/component/save';
export const URL_COMPONENT_BATCH_SAVE = '/rs/pa/component/batchSave';
export const URL_COMPONENT_UPDATE_ICON = '/rs/pa/component/updateIcon';
export const URL_COMPONENT_FIND_BY_ID = '/rs/pa/component/findById';
// 获取所有共享+独享返回不为void的方法列表
export const URL_COMPONENT_FUNC_LIST = '/rs/pa/component/common/notVoidFuncList';
export const URL_COMPONENT_METRICS = '/rs/pa/job/componentMetrics';

export const URL_EXTRACT_JSON = '/rs/pa/component/extract/json';

export const URL_HISTORY_LIST = '/rs/pa/job/historyList';

export const URL_VERSION_LIST = '/rs/pa/job/versionList';

export const URL_TEST_MONITOR_RULE = '/rs/pa/job/mvel/test';
export const URL_TEST_DATA_DELETE = '/rs/pa/job/test/data/delete';
export const URL_TEST_DATA_RUN = '/rs/pa/job/test/run';
export const URL_TEST_DATA_RUN_SQL = '/rs/pa/job/test/runSql';

export const URL_LOG_REPLAY = '/rs/pa/job/logReplay';
export const URL_LOG_DOWNLOAD = '/rs/pa/job/downloadWarnData';

export const URL_IQ_DATA_DOWNLOAD = '/rs/pa/job/downloadInferiorQualityData';

export const URL_ASSETS_FUNC_BOOLEAN_RETURN_LIST = '/rs/pa/assets/getFuncsBooleanReturn';
export const URL_ASSETS_PROMPT_LIST = '/rs/pa/assets/getPrompt';

// 获取服务资源和流程资源类型的列表
export const URL_RESCONF_GETRESTYPELIST = '/rs/pa/resConf/getResTypeList';
export const URL_RESCONF_GETALLTYPELIST = '/rs/pa/resConf/getAllTypeList';
// 获取表单配置
export const URL_RESCONF_GETFORMCONF = '/rs/pa/resConf/getFormConf';

// 根据集群id和机构id获取此集群分配详情页面的基本信息
export const URL_FLINK_CLUSTERMSG = '/rs/pa/clusterResConf/clusterMsg';
// 根据机构id和集群id获取下级机构资源详情
export const URL_FLINK_CHILDRENORGRESLIST = '/rs/pa/clusterResConf/orgResAllocateList';

// 查询加工引擎-资源分配-分配按钮的显示与隐藏
export const URL_RESOURCE_ALLOCA = '/rs/pa/res/validAssign';

// 调整和分配给下级机构的资源
export const URL_FLINK_ALLOCATED = '/rs/pa/clusterResConf/allocated';
// 根据机构 id 获取 队列信息
export const URL_FLINK_QUEUENAME = '/rs/pa/clusterResConf/getQueueName';
// 选中相应队列名称后重新获取其他展示的数据
export const URL_FLINK_QUEUETOMSG = '/rs/pa/clusterResConf/allocateMsg';
// 根据注册类型获取队列列表数据
export const URL_FLINK_QUEUELIST = '/rs/pa/clusterResConf/getQueueList';

/* 流程导入文件上传 */
export const URL_JOB_IMPORT_FILE = '/rs/pa/job/importFile';
/* 获取流程状态 */
export const URL_JOB_STATUS_LIST = '/rs/pa/job/jobStatusEnum';
/* 刷新集群信息 */
export const URL_REFRESH_CLUSTER_MSG = '/rs/pa/clusterResConf/refreshClusterMsg';

// 数据管理服务类型
export const URLTYPE_SQL = '/rs/pa/component/connectorType/SQL';
export const URLLISTRES = '/rs/pa/res/listRes/';
export const URL_GETURL = 'rs/pa/res/getUrl';
export const URL_LISTBYTYPE = '/rs/pa/item/listByType?itemType=PRE_FIX';
export const URL_LISTBYTYPE_SUFFIX = '/rs/pa/item/listByType?itemType=SUF_FIX';
export const URL_FINDBYCODE = '/rs/pa/dic/findByCode?code=flink_sql_data_type';
export const URL_ITEMADD = '/rs/pa/sql/table/add';
export const URL_UPDATE = '/rs/pa/sql/table/update';
export const URL_TABLE_FIND_BY_ID = '/rs/pa/sql/table/findById';
export const URL_TABLE_RESCONF = 'rs/pa/sql/table/dataSourceConfig?file=resConf/';
export const URL_TABLE_SHARESYSORG = '/rs/pa/sql/table/listShareSysOrg';
export const URL_VIEW_SHARESYSORG = '/rs/pa/sql/view/listShareSysOrg';
export const URL_TABLE_SHARESYSORGPUT = '/rs/pa/sql/table/shareSqlTable';
export const URL_VIEW_SHARESYSORGPUT = '/rs/pa/sql/view/shareSqlView';
export const URL_TABLE_DOWNLOAD = '/rs/pa/sql/table/download';
export const URL_TABLE_IMPORT = '/rs/pa/sql/table/import';
export const URL_TABLE_EXPORT = '/rs/pa/sql/table/export';
export const URL_CONNECTORINFO = '/rs/pa/component/connectorInfo/';
export const URL_TABLEHIS = '/rs/pa/sql/tableHis/list';
export const URL_TABLEHIS_ROLLLBACK = '/rs/pa/sql/tableHis/rollBack';
export const URL_SQL_PREVIEW = '/rs/pa/sql/table/sql/preview';
export const URL_COMPARE_INIT = '/rs/pa/sql/table/compareInit';
export const URL_COMPARE_VERSION = '/rs/pa/sql/table/compareVersion';
export const URL_LISTVERSION = '/rs/pa/sql/tableHis/listVersion';
export const URL_GETSUBBYPARENT = '/rs/pa/dic/getSubByParent?parentCode=PaSqlTableNotes';
export const URL_SHARE_SQL_TABLE_BATCH = '/rs/pa/sql/table/shareSqlTableBatch';
export const URL_TABLE_SYS_ORG = '/rs/pa/sql/table/allOrSharedSysOrg';
// 获取数据库类型及类型对应的服务
export const URL_GET_JDBC_TYPE = '/rs/pa/sql/table/getJdbcRes';

export const URL_UDF_FINDBYID = '/rs/pa/sql/udf/findById';
export const URL_UDF_DOWLOAD = '/rs/pa/sql/udf/download';
export const URL_UDF_TEST = '/rs/pa/sql/udf/test';
export const URL_UDF_TESTSQL = '/rs/pa/sql/udf/udfTestSql';
export const URL_UDF_TEST_FUNC = '/rs/pa/sql/udf/testFunc';
export const URL_UDF_LIST = '/rs/pa/sql/udf/list';
export const URL_UDF_SHARESYS = '/rs/pa/sql/udf/listShareSysOrg';
export const URL_UDF_UPDATESQLUDF = '/rs/pa/sql/udf/shareSqlUdf';
export const URL_UDF_UDFHIS = '/rs/pa/sql/udfHis/list';
export const URL_UDF_ROLLBACK = '/rs/pa/sql/udfHis/rollback';
export const URL_UDF_TEMPLATE = '/rs/pa/sql/udf/download/template';
export const URL_UDF_TEMPLATE_CODE = '/rs/pa/sql/udf/getTemplateByUdfType';
export const URL_UDF_ADD_ON_WRITE = '/rs/pa/sql/udf/addOnWrite';
export const URL_UDF_ADD_ON_UPLOAD = '/rs/pa/sql/udf/addOnUpload';
export const URL_UDF_UPDATE_ON_WRITE = '/rs/pa/sql/udf/updateOnWrite';
export const URL_UDF_UPDATE_ON_UPLOAD = '/rs/pa/sql/udf/updateOnUpload';
export const URL_UDF_ASSEMBLE_CODE = '/rs/pa/sql/udf/assembleSourceCode';
export const URL_UDF_PARSE_UDF_FUNC = '/rs/pa/sql/udf/parseUdfFunc';
export const URL_UDF_SYS_ORG = '/rs/pa/sql/udf/allOrSharedSysOrg';
export const URL_SHARE_SQL_UDF_BATCH = '/rs/pa/sql/udf/shareSqlUdfBatch';

//catalog分享
export const URL_LIST_SHARE_CATALOG = '/rs/pa/sql/catalog/allOrSharedSysOrg';
//批量操作catalog分享信息
export const URL_LIST_SHARE_CATALOG_BATCH = '/rs/pa/sql/catalog/shareSqlTableBatch';

export const URL_COMMON_FUNC_LIST = 'rs/fdl/func/listBySearchData';
export const URL_FUNC_VALIDATE = '/rs/pa/func/validate';
export const URL_FUNC_FORMAT = '/rs/pa/func/format';

/* 获取流程-表/流程-视图引用关系列表 */
export const URL_GET_TABLE_RELATION_LIST = '/rs/pa/sql/table/listRelation';
/* 导出字段信息到excel文件 */
export const URL_EXPORT_TABLE_RELATION_LIST = '/rs/pa/sql/table/exportRelation';
/* 获取流程-视图引用关系列表 */
export const URL_GET_VIEW_RELATION_LIST = '/rs/pa/sql/view/listRelation';
/* 导出字段信息到excel文件 */
export const URL_EXPORT_VIEW_RELATION_LIST = '/rs/pa/sql/view/exportRelation';
/* 获取流程-视图引用关系列表 */
export const URL_GET_UDF_RELATION_LIST = '/rs/pa/sql/udf/listRelation';
/* 导出字段信息到excel文件 */
export const URL_EXPORT_UDF_RELATION_LIST = '/rs/pa/sql/udf/exportRelation';
/* 获取流程-视图引用关系列表 */
export const URL_GET_TOPIC_RELATION_LIST = '/rs/pa/res/detail/kafka/listRelation';
/* 导出字段信息到excel文件 */
export const URL_EXPORT_TOPIC_RELATION_LIST = '/rs/pa/res/detail/kafka/exportRelation';
/* 导出服务管理引用关系列表 */
export const URL_EXPORT_SERVICE_RELATION_LIST = '/rs/pa/res/exportRelation';
/* 获取引用关系列表 */
export const URL_GET_FUNC_UDF_RELATION_LIST = '/rs/pa/func/listRelation/UDF';
export const URL_GET_FUNC_JOB_RELATION_LIST = '/rs/pa/func/listRelation/JOB';
export const URL_GET_FUNC_CATALOG_RELATION_LIST = '/rs/pa/sql/catalog/listRelation';

/* 判断有无引用关系 */
export const URL_HAS_RELATION_LIST = '/rs/pa/res/serverRelationCount';
/* 获取历史流程源码 */
export const URL_GET_HIS_SOURCE_CODE = '/rs/pa/job/hisSourceCode';

export const URL_EXPIMP_EXPORT = '/rs/pa/expImp/export';
export const URL_GET_FUNC_LIST = '/rs/pa/filter/static/getFuncList';
export const URL_GET_FUNC_BODY = '/rs/pa/filter/static/getFuncBody';

export const URL_SERVICE_LIST = '/rs/pa/portal/listQuoteRes/JDBC';
export const URL_TABLE_LIST = '/rs/pa/portal/getSubList/JDBC';
export const URL_TABLE_FIEID = '/rs/pa/res/detail/mysql/column';
export const URL_GET_TABLE_INFO = '/rs/pa/sql/getTableManageInfo';
