<template>
  <div class="config-container">
    <!-- xia -->
    <el-form
      v-for="(el, index) in conditions"
      :key="el.name"
      ref="form"
      :model="el"
      class="config-item"
      :disabled="disabled"
      :class="{ 'config-item--height': el.expand }"
    >
      <!-- 头部 -->
      <logic-header
        :index="index"
        :uuid="el.name"
        :catch.sync="store"
        :disabled="disabled"
        :data.sync="conditions[index]"
        @change="handleChange"
        @copy="handleCopy"
        @delete="handleDelete"
      />
      <!-- 配置详情 -->
      <logic-main
        v-if="el.showConfig"
        :index="index"
        :disabled="disabled"
        :field-list="fieldList"
        :data.sync="conditions[index]"
      />
    </el-form>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import LogicHeader from './header.vue';
import LogicMain from './main.vue';

@Component({ components: { LogicHeader, LogicMain } })
export default class LogicConfig extends Vue {
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => [] }) fieldList!: any[];
  @Prop({ default: () => ({}) }) expressionMapping!: any;
  @Prop({ default: () => [] }) methodList!: any[];
  @PropSync('data', { default: () => [] }) conditions!: any[];
  @PropSync('methodSourceCode', { default: () => ({}) }) methodCode!: any;
  @PropSync('catch', { type: Object, default: () => ({}) }) store!: any;

  private showExpression = false;
  private showExpressions: any = {};
  private activeNames = {};
  private methodRules: any[] = [
    {
      required: true,
      message: this.$t('pa.flow.placeholder28'),
      trigger: 'change'
    }
  ];

  /* 处理函数名修改事件 */
  handleChange(index: number) {
    this.$emit('change', index);
    const { funcArgs, name } = this.conditions[index];
    this.$set(this.conditions[index], 'expand', funcArgs.length > 0 ? name : '');
    this.$set(this.conditions[index], 'showConfig', this.conditions[index].expand || funcArgs.length > 0);
  }
  /* 处理复制条件事件 */
  handleCopy(index: number) {
    this.$emit('copy', index);
  }
  /* 处理删除条件事件 */
  async handleDelete(index: number) {
    try {
      const { name } = this.conditions[index];
      await this.$confirm(this.$t('pa.flow.msg183', [name]), this.$t('pa.prompt'));
      this.conditions.splice(index, 1);
      this.conditions.forEach((el, order) => {
        this.$set(this.conditions[order], 'name', String.fromCharCode(order + 65));
      });
    } catch {}
  }
  validate() {
    return Promise.all((this.$refs.form as any).map((el) => el.validate()));
  }
}
</script>
<style lang="scss" scoped>
.config {
  &-container {
    height: 485px;
    overflow-x: hidden;
    overflow-y: auto;
    background: #ffffff;
  }

  &-item {
    padding: 16px 20px;
    margin: 0 auto 16px;
    width: calc(100% - 40px);
    min-height: 100px;
    background: #fafbfc;
    border-radius: 4px;
    border: 1px solid #f1f1f1 !important;
    box-sizing: border-box;

    ::v-deep .el-autocomplete {
      width: 100%;
    }

    &--height {
      padding-bottom: 0;
      min-height: unset;
    }

    ::v-deep .el-form {
      &-item {
        margin: 0;

        &.is-error {
          margin-bottom: 20px;
        }
      }
    }
  }
}
</style>
