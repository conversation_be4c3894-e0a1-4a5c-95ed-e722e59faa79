<template>
  <div>
    <bs-dialog
      :title="$t('pa.data.udf.detail.correlationType')"
      :visible.sync="relationDialogVisible"
      class="relation-dialog"
      width="1000px"
      @confirm="handleConfirm"
    >
      <!-- 主体内容 -->
      <div class="relation-dialog__content">
        <!-- 头部信息 -->
        <div class="relation-dialog__header">
          <bs-search
            v-model="searchContent"
            :placeholder="$t('pa.data.udf.detail.placeholder.methodNamePlaceholder')"
            class="relation-dialog__header--search"
            @search="handleSearch"
          />
        </div>
        <div class="relation-dialog__outer">
          <!-- 主体内容-右侧表格 -->
          <div class="relation-dialog__content--right">
            <bs-table
              v-loading="relationTableLoading"
              row-key="id"
              :checked-rows="checkedFuncData"
              :column-data="columnData"
              :column-settings="false"
              :data="tableData"
              :page-data="pageData"
              height="230"
              crossing
              selection
              @page-change="handleListPageChange"
              @selection-change="handleSelectionChange"
              @select="handleSelect"
              @select-all="handleSelectAll"
            />
          </div>
        </div>
      </div>
    </bs-dialog>
  </div>
</template>

<script lang="ts">
import { debounce, uniqBy } from 'lodash';
import { Vue, Component, Prop, PropSync, Watch } from 'vue-property-decorator';
import { URL_COMMON_FUNC_LIST } from '@/apis/commonApi';
import { countByName } from '@/apis/dataApi';
import { get } from '@/apis/utils/net';

@Component
export default class RelationDialog extends Vue {
  @PropSync('visible', { type: Boolean }) relationDialogVisible;
  @Prop({ default: '' }) type!: string;
  // 后端返回的选中数据
  @Prop({ default: {} }) checkedData!: any;
  // 搜索内容
  private searchContent = '';
  private tableData: any = [];
  private columnData: any = [
    {
      label: this.$t('pa.serialNumber'),
      type: 'selection',
      width: '100',
      value: 'index'
    },
    {
      label: this.$t('pa.data.udf.detail.methodName'),
      value: 'name'
    },
    {
      label: this.$t('pa.data.udf.detail.returnType'),
      value: 'retType'
    }
  ];
  private relationTableLoading = false;
  // 分页数据
  private pageData = {
    pageSize: this.$store.getters.pageSize,
    currentPage: 1,
    total: 0
  };
  // 共享分页数据
  private shareParams = {
    searchName: '',
    offset: 0,
    length: this.$store.getters.pageSize
  };
  private isSelect = false;
  private debounceGetFuncList = debounce(this.getFuncList, 500);
  // 选中方法
  private checkedFuncData: any = [];
  private shareOtherFuncs: any[] = [];
  private shareTotalData: any[] = [];

  @Watch('relationDialogVisible', { immediate: true })
  async handleRelationDialogVisibleChange(isVisible) {
    if (!isVisible) return;
    this.pageData.currentPage = 1;
    this.searchContent = '';
    this.getTotalList();
    this.getFuncList();
  }
  async getTotalList() {
    const shareRes = await get(URL_COMMON_FUNC_LIST, {
      searchName: '',
      offset: 0,
      length: 0
    });
    this.shareTotalData = shareRes.data;
  }

  handleSelectionChange(selections) {
    this.$set(this, 'checkedFuncData', uniqBy(selections, 'id'));
  }

  handleSelect(...params) {
    const current = params[1];
    if (this.shareOtherFuncs.includes(current.funcId)) {
      const index = this.shareOtherFuncs.findIndex((value) => value === current.funcId);
      this.shareOtherFuncs.splice(index, 1);
    } else {
      this.shareOtherFuncs.push(current.funcId);
    }
    const shares: any[] = this.shareTotalData.map((el) => {
      if (this.shareOtherFuncs.includes(el.funcId)) return el;
    });
    this.isSelect = true;
    this.checkedData.otherFuncs = shares.filter(Boolean);
  }

  handleSelectAll(selections, isChecked) {
    this.isSelect = true;
    if (isChecked) {
      this.tableData.forEach((el) => {
        const condition = !this.shareOtherFuncs.includes(el.funcId);
        if (condition) this.shareOtherFuncs.push(el['funcId']);
      });
    } else {
      this.tableData.forEach((el) => {
        const condition = this.shareOtherFuncs.includes(el.funcId);
        if (condition) {
          const index = this.shareOtherFuncs.findIndex((value) => value === el['funcId']);
          this.shareOtherFuncs.splice(index, 1);
        }
      });
    }
    const shares = this.tableData.map((el) => {
      if (this.shareOtherFuncs.includes(el.funcId)) return el;
    });
    this.checkedData.otherFuncs = shares.filter(Boolean);
  }

  transferCheckedData() {
    let shares: any = null;
    if (this.isSelect) {
      shares =
        (uniqBy(this.checkedData.otherFuncs, 'funcId') as any)
          .map((item) => {
            if (item.funcType === 'SHARE') return item.funcId;
          })
          .filter(Boolean) || [];
    } else {
      shares = this.checkedData.otherFuncs.map((el) => el.funcId).filter(Boolean) || [];
    }
    this.tableData.forEach((el: any) => {
      shares.forEach((item) => {
        if (item === el.funcId || item === el.id) this.checkedFuncData.push(el);
      });
    });
    this.checkedFuncData = uniqBy(this.checkedFuncData, 'id');
  }

  handleSearch(queryString) {
    this.shareParams.searchName = queryString;
    this.pageData.currentPage = 1;
    this.debounceGetFuncList();
  }

  /**
   * 获取关联方法列表数据（共享方法列表为assets接口，返回数据需要单独处理）
   */
  async getFuncList() {
    this.relationTableLoading = true;
    this.getDDLength();
    this.shareParams.searchName = this.searchContent;
    try {
      const { success, msg, data } = await get(URL_COMMON_FUNC_LIST, this.shareParams);
      this.relationTableLoading = false;
      if (success) {
        let { tableData } = data;
        tableData = data;
        this.pageData.pageSize = this.shareParams.length;
        this.shareParams.offset = this.pageData.currentPage - 1;
        tableData.forEach((el, index) => {
          el.index = index + 1;
        });
        this.tableData = [];
        this.tableData = tableData;
        this.transferCheckedData();
      } else {
        this.$message.error(msg);
      }
    } catch {
      this.relationTableLoading = false;
    }
  }

  async getDDLength() {
    const { success, data } = await countByName(this.shareParams.searchName);
    if (success) this.pageData.total = data;
  }

  handleListPageChange(cur, size) {
    this.pageData.pageSize = size;
    this.pageData.currentPage = cur;
    this.shareParams.offset = (this.pageData.currentPage - 1) * size;
    this.debounceGetFuncList();
  }

  handleConfirm() {
    const params: any = {
      otherFuncs: [],
      imports: [],
      preImports: []
    };
    const otherFuncs = this.checkedFuncData.map(({ name, scope, funcId, id }) => ({
      name,
      ...(scope !== undefined && { scope }),
      funcId: funcId ? funcId : id,
      funcType: 'SHARE'
    }));
    params.otherFuncs = otherFuncs;
    !this.checkedData.otherFuncs.length && (params.otherFuncs = this.checkedData.otherFuncs);
    this.$emit('getData', params);
    this.relationDialogVisible = false;
  }
}
</script>

<style lang="scss" scoped>
.relation-dialog {
  ::v-deep .bs-dialog.bs-dialog-small .el-dialog__body {
    height: 330px;
    max-height: 330px;
  }
  ::v-deep .el-dialog__body {
    overflow: hidden;
    padding: 0;
  }
  ::v-deep .el-table {
    min-height: 193px;
    max-height: 504px;
  }
  ::v-deep .bs-table__base {
    display: none;
  }
  &__header {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    border-bottom: 1px solid #f1f1f1;
    &--search {
      margin: 10px 0;
      margin-right: 20px;
    }
  }
  &__outer {
    display: flex;
    height: 100%;
  }
  &__content {
    display: flex;
    flex-direction: column;
    height: 100%;
    &--right {
      width: 100%;
      height: 560px;
    }
  }
}
</style>
