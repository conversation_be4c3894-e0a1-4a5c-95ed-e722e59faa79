<template>
  <div v-loading="loading" class="flow">
    <div v-show="false"><element-list :editor="editor" :job-type="flowType" /></div>
    <div style="width: 100%">
      <header class="header">
        <div class="title">
          {{ baseInfo.jobName || $t('pa.flow.flowName') }} ( {{ $t('pa.flow.currentVersion') }}：
          <span class="version-color">{{ baseInfo.jobVersion }}</span>
          )
          <span> -- {{ $t('pa.flow.tag') }}：{{ baseInfo.jobTag }}</span>
        </div>
      </header>
      <!--流程画布-->
      <flow-canvas
        v-if="showCanvas"
        ref="flowCanvasRef"
        v-loading="canvasLoading"
        :element-loading-text="canvasLoadingText"
        :flow-status="'PUB'"
        :flow-id="flowId"
        :flow-type="flowType"
        :content="jobContent"
        @node-detail="handleNodeDetail"
        @node-config="handleNodeConfig"
        @canvas-ready="handleCanvasReady"
        @content-change="handleContentChange"
        @canvas-active-change="(e) => (componentInfo = e)"
      />
      <!-- SQL流程代码编辑页 -->
      <flow-sql-code
        v-if="showSqlCanvas"
        ref="flowSqlCodeRef"
        :content="jobContent"
        :flow-id="flowId"
        :flow-status="'PUB'"
        :is-full-screen="true"
        @content-change="handleContentChange"
      />
    </div>
    <!-- 组件详情配置 -->
    <component-detail-config
      v-if="showCompDetailConfig"
      :show.sync="showCompDetailConfig"
      :data.sync="currentNodeData"
      :flow-data="rawData"
      @update="handleNodeConfigUpdate"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Ref } from 'vue-property-decorator';
import { getFlowVersionId } from '@/apis/flowNewApi';
import FlowCanvas from './page-content/canvas/canvas.vue';
import FlowStore from './store/flow-store';
import { cloneDeep } from 'lodash';
import { isFlinkSql } from '@/utils';

@Component({
  name: 'Flow',
  components: {
    'element-list': () => import('./element-list.vue'),
    'header-info': () => import('./header-info.vue'),
    'flow-canvas': () => import('./page-content/canvas/canvas.vue'),
    'flow-sql-code': () => import('./page-content/sql-code/index.vue'),
    ComponentDetailConfig: () => import('./page-content/config-popup/component/detail/index.vue')
  }
})
export default class FlowVersion extends Vue {
  @Ref('flowCanvasRef') readonly flowCanvas!: FlowCanvas;
  @Ref('flowSqlCodeRef') readonly flowSqlCode!: any;
  flowStore: any = null;
  componentStore: any = null;
  jobContent: any = {};
  baseInfo: any = {};
  editor = null;
  flowType = 'PROCESSFLOW';
  private isChange = false;
  private staticData: any = null;

  private componentInfo: any = null;
  private showCompDetailConfig = false;
  private currentNodeData: any = null;
  private isCloseTag = false;
  private loading = true;
  rawData: any = null;

  // 画布loading
  canvasLoading = false;
  canvasLoadingText = this.$t('pa.flow.loadingTip');
  get flowId() {
    return this.baseInfo.id;
  }

  get showCanvas() {
    return this.flowId && !isFlinkSql(this.flowType);
  }

  get showSqlCanvas() {
    return this.flowId && isFlinkSql(this.flowType);
  }

  created() {
    this.$route.query?.flowId && this.getCurFlow({ id: this.$route.query?.flowId });
  }

  /* right start */

  /* 节点信息查看事件 */
  handleNodeDetail(data) {
    this.componentInfo = data;
  }
  /* 组件详情配置事件 */
  handleNodeConfig(data: any) {
    this.rawData = this.getNewestFlow(false);
    this.currentNodeData = data;
    this.showCompDetailConfig = true;
  }
  /* 组件详情配置更新事件 */
  handleNodeConfigUpdate(jobNode) {
    jobNode.jobId = this.flowId;
    this.flowCanvas?.updateNodeData(jobNode.nodeId, jobNode);
    this.flowCanvas?.updateNodeStatus({ id: jobNode.nodeId, status: 1 }); // 状态更新
  }

  handleContentChange(e) {
    this.isChange = Boolean(e);
  }
  /* header end */

  getNewestFlow(isString = true) {
    const content = this.flowCanvas.getContent();
    return {
      ...cloneDeep(this.staticData),
      content: isString ? JSON.stringify(content) : cloneDeep(content),
      jobStatus: 'END'
    };
  }

  async initFlow(data: any) {
    if (!data) return;
    this.flowStore = new FlowStore(data);
    this.baseInfo = data;
    this.flowType = this.baseInfo.jobType;
    this.jobContent = this.flowStore.content;
    this.staticData = this.flowStore.staticData;
    this.handleCanvasLoading(data);
    await this.$nextTick();
    this.flowCanvas && this.flowCanvas.renderCanvas();
  }

  // 获取当前流程信息
  async getCurFlow({ id }) {
    // 处理id为空的情况：项目下无流程
    const { data } = await getFlowVersionId({ jobVersionId: id });
    if (isFlinkSql(data.jobType)) {
      data.content = this.$store.getters.decrypt(data.content);
    }
    this.loading = false;
    this.initFlow(data);
  }

  // 处理画布loading逻辑
  handleCanvasLoading(data) {
    const { content, jobStatus } = data;
    const isEmpty = JSON.stringify(content) === '{"nodes":[],"edges":[]}';
    if (!isEmpty && ['INPROD', 'INPUB'].includes(jobStatus)) {
      this.canvasLoading = true;
      this.canvasLoadingText = this.$t('pa.flow.loadingTip');
    }
  }

  handleCanvasReady(editor) {
    this.editor = editor;
    this.flowCanvas && this.flowCanvas.renderCanvas();
  }
}
</script>
<style lang="scss" scoped>
.flow {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 2000;
  background: white;
  .header {
    line-height: 60px;
    padding: 0 20px;
    .title {
      font-size: 14px;
      font-weight: bolder;
    }
    .title::before {
      content: ' ';
      position: relative;
      left: 0;
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 8px;
      background: #ff9e2b;
      border-radius: 2px;
    }
    .version-color {
      color: #ff9e2b;
    }
  }
  ::v-deep .dag-style {
    height: calc(100vh - 60px) !important;
  }
}
</style>
