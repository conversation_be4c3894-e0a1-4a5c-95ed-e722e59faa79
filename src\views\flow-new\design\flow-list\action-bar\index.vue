<template>
  <div class="action-bar">
    <div v-if="mode === 'DEFAULT'" class="action-bar__content">
      <!--遍历一级操作栏：新建、批量操作、筛选（级联）、搜索-->
      <template v-for="el in flowActionList">
        <tooltip-icon
          v-if="el.type === 'ElTooltip'"
          :key="el.value"
          :icon="el.icon"
          :content="el.content"
          @click="handler(el.value)"
        />
        <!-- 筛选popover -->
        <filter-popover
          v-if="el.type === 'ElPopover'"
          :key="el.value"
          ref="filterPopover"
          :icon="el.icon"
          :visible.sync="filterPopoverVisible"
          @search="handleFilter"
        />
        <dropdown-icon
          v-if="el.type === 'ElDropdown'"
          :key="el.value"
          :icon="el.icon"
          :options="el.options"
          @click="(event) => handler(event)"
        />
      </template>
    </div>
    <!--遍历批量操作栏：发布、取消发布、启动、停止（停止&保留状态停止）、复制、移动-->
    <div v-if="mode === 'BATCH'" class="action-bar__batch">
      <div class="action-bar__batch-header">
        <el-checkbox v-model="innerIsChecked" :indeterminate="indeterminate" @change="handleChange">
          {{ $t('pa.flow.selectAll') }}
        </el-checkbox>
        <span class="action-bar__text-btn" @click="rollback">{{ $t('pa.flow.exit') }}</span>
      </div>
      <div class="action-bar-oper">
        <template v-for="el in batchActionList">
          <tooltip-icon
            v-if="el.type === 'ElTooltip'"
            :key="el.value"
            :icon="el.icon"
            :content="el.content"
            :disabled="isBatchIconDisabled(el.value)"
            @click="batchHandler('', el.value)"
          />
          <span v-if="el.type === 'splitTag'" :key="el.content" class="action-bar__split">|</span>
          <dropdown-icon
            v-if="el.type === 'ElDropdown'"
            :key="el.value"
            :icon="el.icon"
            :options="el.options"
            :disabled="isBatchIconDisabled(el.value)"
            @click="(event) => batchHandler(event, el.value)"
          />
        </template>
      </div>
    </div>
    <!--显示搜索栏-->
    <div v-if="mode === 'SEARCH'" class="action-bar__content" :class="{ 'action-bar__content--search': isEn }">
      <bs-search
        v-model="name"
        :placeholder="$t('pa.flow.placeholder42')"
        :maxlength="20"
        size="small"
        @search="handleSearch"
      />
      <span class="action-bar__text-btn" @click="cancel">{{ $t('pa.flow.cancel') }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, PropSync, Watch } from 'vue-property-decorator';
import { debounce } from 'lodash';
import { hasPermission } from '@/utils';
import { flowActionList, batchActionList } from './config';
import FilterPopover from '../../../components/filter-popover.vue';
import TooltipIcon from './tooltip-icon.vue';
import DropdownIcon from './dropdown-icon.vue';
import { openDirEditingDialog, openFlowEditingDialog } from '../modals';
@Component({
  components: {
    FilterPopover,
    TooltipIcon,
    DropdownIcon
  }
})
export default class FlowListHeader extends Vue {
  @PropSync('isBatch', { default: false }) innerIsBatch!: boolean;
  @Prop({ default: false }) isCheckedAll!: boolean;
  @Prop({ default: false }) indeterminate!: boolean;
  innerIsChecked = false;
  // 当前显示的内容
  mode: 'DEFAULT' | 'BATCH' | 'SEARCH' = 'DEFAULT';
  filterPopoverVisible = false; //控制筛选显隐

  name = '';
  oldQueryid: any = ''; //存储旧文件id
  filterParams: any = {
    jobType: 'ALL',
    jobStatus: 'ALL',
    jobRunTimeStatus: 'ALL',
    clusterType: 'ALL',
    mode: 'ALL'
  };
  batchHandler: any = debounce(this.debounceBatchHandler, 500);
  @Watch('isCheckedAll')
  handleCheckAllChange(val: boolean) {
    this.innerIsChecked = val;
  }
  get projectId() {
    return this.$route.query.id;
  }
  // 操作列表按钮
  get flowActionList() {
    return flowActionList.filter((el) =>
      Array.isArray(el.access) ? el.access.some((access) => hasPermission(access)) : hasPermission(el.access)
    );
  }
  // 批量操作按钮
  get batchActionList() {
    return batchActionList.filter((el) => hasPermission(el.access));
  }
  // 批量操作的状态：如果id在store保存的数组里，则置灰[publish]、[cancelPublish]、[online]、[offline]图标状态
  get iconDisabled() {
    return !!this.$store.getters.loadingProjectIds.includes(this.$route.query.id);
  }
  // 批量操作是否可用
  isBatchIconDisabled(value) {
    return ['publish', 'cancelPublish', 'online', 'offline'].includes(value) && this.iconDisabled;
  }

  activated() {
    if (this.$store.getters.flowSearchObj) {
      this.filterParams = { ...this.$store.getters.flowSearchObj };
      this.name = this.$store.getters.flowSearchObj.name;
      this.name && (this.mode = 'SEARCH');
    } else {
      if (this.oldQueryid === '') {
        this.filterParams.jobStatus = this.$route.query?.state || 'ALL';
      }
    }
  }
  deactivated() {
    this.oldQueryid = this.$route.query?.id;
  }

  // value: 方法名【add、batch、filter、search、refresh】
  handler(value: string) {
    console.log(value);
    value === 'refresh' ? debounce(this[value], 500)(value) : this[value](value);
  }
  // value: 批量方法
  debounceBatchHandler(state: string, value: string) {
    if (this.isBatchIconDisabled(value)) return; // 置灰的按钮不可操作
    this.$emit('click', { value, state }); // 流程的批量操作
  }
  // 新建流程
  async addFlow(value: string) {
    const needRefresh = await openFlowEditingDialog({ projectId: this.projectId });
    needRefresh && this.$emit('search');
  }
  // 新建目录
  async addDir(value: string) {
    const needRefresh = await openDirEditingDialog({ projectId: this.projectId as string });
    needRefresh && this.$emit('search');
  }
  // 切换批量操作按钮
  batchFlow() {
    this.mode = 'BATCH';
    this.innerIsBatch = true;
  }
  // 点击搜索图标，显示搜索栏
  search() {
    this.mode = 'SEARCH';
  }
  // 刷新流程列表
  refresh(value: string) {
    this.$emit('search');
  }
  // 退出批量操作
  public rollback() {
    this.mode = 'DEFAULT';
    this.innerIsChecked = false;
    this.innerIsBatch = false;
  }
  // 取消搜索
  cancel() {
    this.mode = 'DEFAULT';
    this.name = '';
    this.handleSearch('');
  }
  //  触发搜索：1.筛选项变化
  handleFilter(params) {
    this.filterParams = params;
    this.$emit('search', { ...this.filterParams, name: this.name });
  }

  // 触发搜索：2.搜索栏值变化
  handleSearch(value: string) {
    this.$emit('search', { ...this.filterParams, name: value });
  }
  // 是否全选，全选
  handleChange(isChecked: boolean) {
    this.innerIsChecked = isChecked;
    this.$emit('select-all', isChecked);
  }
}
</script>
<style scoped lang="scss">
.action-bar {
  display: flex;
  align-items: center;
  padding: 0 20px;
  min-height: 50px;
  border-bottom: 1px solid #f1f1f1;
  &__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    .tooltip-icon:last-child {
      flex: 1;
      text-align: right;
      margin-right: 0;
    }
    &--search {
      ::v-deep .el-input {
        width: 220px;
      }
    }
  }

  .filter-popover {
    margin-right: 20px;
    cursor: pointer;
  }
  &__batch {
    flex: 1;
    padding-bottom: 15px;
    &-header {
      width: 100%;
      height: 50px;
      padding-right: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  &__text-btn {
    font-size: 14px;
    color: $--bs-color-text-placeholder;
    cursor: pointer;
    margin-left: 20px;
  }

  &__split {
    color: $--bs-color-border-lighter;
    margin-right: 20px;
  }
  &-oper {
    height: 20px;
    display: flex;
    align-items: center;
  }
}
</style>
