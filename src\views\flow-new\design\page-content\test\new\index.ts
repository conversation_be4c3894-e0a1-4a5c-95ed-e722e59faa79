import { Message } from 'bs-ui-pro';
import _ from 'lodash';

const typeInfo: any = {
  STRING: 'input',
  CHAR: 'input',
  VARCHAR: 'input',
  DATE: 'datetime',
  TIME: 'datetime',
  TIMESTAMP: 'datetime',
  'TIMESTAMP(3)': 'datetime',
  TINYINT: 'numberinput',
  SMALLINT: 'numberinput',
  INT: 'numberinput',
  BIGINT: 'numberinput',
  FLOAT: 'numberinput',
  DOUBLE: 'numberinput'
};
const timeType: any = {
  DATE: 'yyyy-MM-dd',
  TIME: 'HH:mm:ss'
};

// 处理sql列表头部
export const getColumns = (data) => {
  const column = data.map((item) => {
    const name = item.fieldName.replace('.', '/');
    return {
      label: item.fieldName,
      value: name,
      formProps: {
        type: typeInfo[item.fieldType] || 'input',
        format: timeType[item.fieldType] || '',
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        placeholder: (item.fieldType && item.fieldType.toLowerCase()) || 'string',
        rules: [
          { required: true, message: `请输入${item.fieldName}` },
          {
            pattern: /(^S*)|(s*)$/g,
            message: '不能为空',
            trigger: 'blur'
          }
        ]
      }
    };
  });
  return column;
};
// 新增column参数转义
const keyInfo = (obj) => {
  const object = {};
  Object.keys(obj).forEach((item) => {
    const name = item.replace('/', '.');
    object[name] = obj[item];
  });
  return object;
};

// 编辑column参数转义
const nokeyInfo = (obj, has) => {
  const object = {};
  Object.keys(obj).forEach((item) => {
    const name = item.replace('.', '/');
    object[name] = obj[item];
  });
  return _.pick(object, has);
};
// sql类型保存数据处理
export const saveParams = (obj, list) => {
  const { typeOfData } = obj;
  const data = list.map((item) => {
    if (typeOfData === 'MANUAL' || typeOfData === 'CUSTOM') {
      const content =
        typeOfData === 'MANUAL'
          ? item.handList.data.map((it) => {
              return keyInfo(it);
            })
          : item.csvList.csvData;
      return {
        sourceNodeId: item.value,
        sourceNodeName: item.name,
        content: typeOfData === 'MANUAL' ? JSON.stringify(content) : content
      };
    } else {
      return {
        sourceNodeId: item.value,
        sourceNodeName: item.name,
        content: item.list.data.map((it) => {
          const { fieldName, fieldType, other } = it;
          return {
            fieldName,
            fieldType,
            ...other
          };
        })
      };
    }
  });

  if (typeOfData === 'CUSTOM' && obj.delimiter === '') {
    obj.delimiter = ',';
  }
  if (typeOfData !== 'CUSTOM') {
    obj.delimiter = '';
  }
  return {
    ...obj,
    data
  };
};
// data数据类型保存
export const saveInfo = (obj, list) => {
  const { jobId, testDataName, id, typeOfData, delimiter } = obj;
  const form =
    typeOfData === 'MANUAL'
      ? { jobId, testDataName, id, typeOfData }
      : { jobId, testDataName, id, typeOfData, delimiter };
  const data = list.map((item) => {
    return {
      sourceNodeId: item.value,
      sourceNodeName: item.name,
      content: typeOfData === 'MANUAL' ? item.handList.data : item.csvList.csvData
    };
  });
  if (obj.typeOfData === 'CUSTOM' && obj.delimiter === '') obj.delimiter = ',';
  if (obj.typeOfData !== 'CUSTOM') obj.delimiter = '';
  return {
    ...form,
    data: data
  };
};
// 获取data类型数据处理
export const getDataResult = (resp, form, process) => {
  const { data, typeOfData, ...other } = resp;
  form.typeOfData = typeOfData;
  form = { ...form, ...other };
  process = process.map((item) => {
    const list = data.find((it) => it.sourceNodeId === item.value);
    const info = typeOfData === 'MANUAL' ? JSON.parse(list.content) : list.content;
    const keys: any = [];
    if (typeOfData === 'MANUAL') {
      form.delimiter = '';
      item.handList.columns
        .filter((item) => item.type !== 'index')
        .forEach((he: any) => {
          keys.push(he.value);
        });
      return {
        ...item,
        handList: {
          ...item.handList,
          data: info.map((item) => {
            return _.pick(item, keys);
          })
        }
      };
    } else {
      if (form.delimiter === '') form.delimiter = ',';
      return {
        ...item,
        csvList: {
          csvData: info
        }
      };
    }
  });

  return { form, process };
};
// sql编辑数据处理
export const getParams = (resp, form, process) => {
  const { data, ...other } = resp;
  form = { ...form, ...other };
  process = process.map((item) => {
    const list = data.find((it) => it.sourceNodeId === item.value);
    if (!list) {
      return item;
    }
    const info = resp.typeOfData !== 'CUSTOM' ? JSON.parse(list.content) : list.content;
    const keys: any = [];
    item.handList.columns
      .filter((item) => item.type !== 'index')
      .forEach((he: any) => {
        keys.push(he.value);
      });
    if (!info.length) {
      return item;
    }
    if (resp.typeOfData === 'MANUAL') {
      return {
        ...item,
        handList: {
          ...item.handList,
          data: info.map((it) => {
            return nokeyInfo(it, keys);
          })
        }
      };
    } else if (resp.typeOfData === 'CUSTOM') {
      return {
        ...item,
        csvList: {
          csvData: info
        }
      };
    } else {
      return {
        ...item,
        list: {
          ...item.list,
          data: info.map((it, index) => {
            const { fieldName, fieldType, ...other } = it;
            return {
              index: index + 1,
              fieldName,
              fieldType,
              other
            };
          })
        }
      };
    }
  });

  return { form, process };
};

export const validataProcess = (data, sql, type) => {
  //全局提示，csv输入状态下，测试数据非空校验
  if (type === 'CUSTOM') {
    for (let i = 0; i < data.length; i++) {
      if (data[i].csvList.csvData === '') {
        Message({ message: `${data[i].name}测试数据不能为空`, type: 'error' });
        return false;
      }
    }
  }
  if (!sql) {
    for (let i = 0; i < data.length; i++) {
      if (data[i].handList.data.length !== 0) {
        const lis = data[i].handList.data;
        for (let j = 0; j < lis.length; j++) {
          for (const key in lis[j]) {
            if (!lis[j][key]) {
              Message({ message: `${data[i].name}不能为空`, type: 'error' });
              return false;
            }
          }
        }
      }
    }
    return true;
  } else {
    if (type === 'MANUAL') {
      for (let i = 0; i < data.length; i++) {
        if (data[i].handList.data.length === 0) {
          Message({ message: `${data[i].name}用例数据不能为空`, type: 'error' });
          return false;
        } else if (data[i].handList.data.length !== 0) {
          const lis = data[i].handList.data;
          for (let j = 0; j < lis.length; j++) {
            const keys = Object.keys(lis[j]).length;
            if (keys === 0) {
              Message({ message: `${data[i].name}不能为空`, type: 'error' });
              return false;
            }
            for (const key in lis[j]) {
              if (!key && !lis[j][key]) {
                Message({ message: `${data[i].name}不能为空`, type: 'error' });
                return false;
              }
            }
          }
        }
      }
      return true;
    } else {
      for (let i = 0; i < data.length; i++) {
        const has = data[i].list.data.every((it) => it.other.type);
        if (!has) {
          Message({ message: `${data[i].name}造数逻辑不能为空不能为空`, type: 'error' });
          return false;
        }
      }
      return true;
    }
  }
};
