<template>
  <bs-dialog
    title="批量操作详情"
    :visible.sync="dialogVisible"
    size="large"
    width="60%"
    :before-close="handleClose"
    @confirm="handleConfirm"
  >
    <!-- 弹框内容 -->
    <div class="batch-detail-dialog">
      <!-- 表格区域 -->
      <div class="batch-detail-dialog__content">
        <!-- 搜索区域 -->
        <div class="batch-detail-dialog__search">
          <!-- 流程名称搜索 -->
          <bs-search
            v-model.trim="searchParams.jobName"
            placeholder="请输入流程名称"
            maxlength="50"
            style="width: 200px; margin-right: 12px"
            @search="handleSearch"
          />

          <!-- 操作结果筛选 -->
          <bs-select
            v-model="searchParams.operationSuccess"
            placeholder="操作结果"
            clearable
            style="width: 120px; margin-right: 12px"
            :options="operationSuccessOptions"
            @change="handleSearch"
          />

          <!-- 重置按钮 -->
          <el-button @click="handleReset">重置</el-button>
        </div>

        <!-- 表格 -->
        <bs-table
          v-loading="tableLoading"
          :data="tableData.tableData"
          class="batch-detail-dialog__table"
          height="400px"
          row-key="jobId"
          :column-data="tableData.columnData"
          :page-data="tableData.pageData"
          :column-settings="false"
          @page-change="handleCurrentChange"
        >
          <!-- 流程名称列 slot -->
          <div slot="jobName" slot-scope="{ row }" class="jobName-slot">
            <el-tooltip :content="row.jobName" effect="light" placement="top">
              <div class="jobName" @click="toFlowCanvas(row)">
                {{ row.jobName }}
              </div>
            </el-tooltip>
          </div>

          <!-- 流程当前状态列 slot -->
          <template slot="jobStatus" slot-scope="{ row }">
            <el-tag size="mini" :type="getJobStatusTagType(row.jobStatus)">
              {{ row.jobStatus }}
            </el-tag>
          </template>
        </bs-table>
      </div>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { getBatchOperationDetailList } from '@/apis/flowNewApi';
import { dateFormat } from '@/common/utils';

interface IBatchDetailSearchParams {
  batchEventId: string; // 批操作ID
  jobName?: string; // 流程名称，支持模糊搜索
  operationSuccess?: string; // 操作结果：SUCCESS/FAILED/UNKNOWN
}

@Component
export default class BatchDetailDialog extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() batchData!: any; // 批量操作的基本信息

  tableLoading = false;

  // 表格配置
  tableData: any = {
    columnData: [],
    tableData: [],
    pageData: {
      pageSize: 10,
      currentPage: 1,
      total: 0,
      layout: 'total, prev, pager, next'
    }
  };

  searchParams: IBatchDetailSearchParams = {
    batchEventId: '', // 批操作ID
    jobName: '', // 流程名称
    operationSuccess: '' // 操作结果
  };

  // 操作结果选项
  operationSuccessOptions = [
    { label: '成功', value: 'SUCCESS' },
    { label: '失败', value: 'FAILED' },
    { label: '未知', value: 'UNKNOWN' }
  ];

  get dialogVisible() {
    return this.visible;
  }

  set dialogVisible(val: boolean) {
    this.$emit('update:visible', val);
  }

  @Watch('visible')
  onVisibleChange(val: boolean) {
    if (val && this.batchData) {
      this.searchParams.batchEventId = this.batchData.batchEventId;
      this.searchParams.jobName = '';
      this.searchParams.operationSuccess = '';
      this.getBatchDetailList();
    }
  }

  // 获取批量操作详情列表
  async getBatchDetailList() {
    try {
      this.tableLoading = true;

      // 构建搜索参数，只传递有值的参数
      const search: any = {
        batchEventId: this.searchParams.batchEventId
      };
      if (this.searchParams.jobName) {
        search.jobName = this.searchParams.jobName;
      }
      if (this.searchParams.operationSuccess) {
        search.operationSuccess = this.searchParams.operationSuccess;
      }

      const params = {
        pageData: {
          pageSize: this.tableData.pageData.pageSize,
          currentPage: this.tableData.pageData.currentPage,
          total: this.tableData.pageData.total
        },
        search
      };

      const { data, success, msg, error } = await getBatchOperationDetailList(params);

      if (success) {
        const { columnData, tableData, pageData } = data;

        // 处理枚举值转换和时间格式化
        const processedTableData = tableData.map((row: any) => {
          const processedRow = { ...row };
          columnData.forEach((column: any) => {
            if (
              column.dataType === 'Enum' &&
              column.enumData &&
              processedRow[column.value] !== undefined
            ) {
              // 将枚举值转换为对应的显示文本
              processedRow[column.value] =
                column.enumData[processedRow[column.value]] || processedRow[column.value];
            } else if (
              column.dataType === 'Date' &&
              processedRow[column.value] !== undefined &&
              processedRow[column.value] !== null
            ) {
              // 将时间戳转换为日期时间格式
              processedRow[column.value] = dateFormat(processedRow[column.value]);
            }
          });
          return processedRow;
        });

        this.tableData = {
          columnData,
          tableData: processedTableData,
          pageData: {
            ...pageData,
            layout: 'total, prev, pager, next'
          }
        };
      } else {
        this.$message.error(error || msg);
      }
    } catch (e) {
      this.$message.error('获取批量操作详情失败');
    } finally {
      this.tableLoading = false;
    }
  }

  // 搜索处理
  handleSearch() {
    (this.tableData.pageData as any).currentPage = 1;
    this.getBatchDetailList();
  }

  // 重置搜索条件
  handleReset() {
    this.searchParams.jobName = '';
    this.searchParams.operationSuccess = '';
    (this.tableData.pageData as any).currentPage = 1;
    this.getBatchDetailList();
  }

  // 分页处理
  handleCurrentChange(currentPage: number, pageSize: number) {
    this.tableData.pageData.currentPage = currentPage;
    this.tableData.pageData.pageSize = pageSize;
    this.getBatchDetailList(); // 重新获取数据
  }

  // 关闭弹框
  handleClose() {
    this.dialogVisible = false;
    // 重置数据
    this.tableData.tableData = [];
    (this.tableData.pageData as any).currentPage = 1;
  }

  // 确认按钮处理
  handleConfirm() {
    this.handleClose();
  }

  // 跳转到流程画布
  toFlowCanvas(row: any) {
    const title = row.jobName;
    const flowId = row.jobId;
    const projectId = row.projectId;
    const projectName = row.projectName;

    // 先关闭 dialog
    this.handleClose();

    if (
      (this as any).$tabsNav
        .getAllTabs()
        .find((item: any) => item.title === title && item.value.split('flowId=')[1] === flowId)
    ) {
      const value = (this as any).$tabsNav
        .getAllTabs()
        .find(
          (item: any) => item.title === title && item.value.split('flowId=')[1] === flowId
        ).value;
      localStorage.setItem('flow', JSON.stringify({ state: 'ALL', flowId }));
      this.$router.push({
        path: value
      });
    } else {
      this.$router.push({
        path: 'flow',
        query: {
          id: projectId,
          name: projectName,
          title,
          state: 'ALL',
          flowId
        }
      });
    }
  }

  // 获取流程状态tag类型
  getJobStatusTagType(status: string): string {
    const typeMap = {
      发布中: 'warning',
      已上线: 'success',
      开发: 'info',
      审批通过: 'primary',
      申批中: 'warning',
      已发布: 'primary',
      上线中: 'warning'
    };

    return typeMap[status] || 'info';
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0;
}

.batch-detail-dialog {
  &__table {
    width: 100%;
  }

  &__search {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-radius: 4px;
    padding-left: 16px;
    padding-right: 16px;
  }

  // 流程名称样式
  .jobName-slot {
    .jobName {
      display: inline-block;
      max-width: 180px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
        color: #2d8cf0;
      }
    }
  }
}
</style>
