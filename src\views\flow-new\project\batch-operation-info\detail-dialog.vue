<template>
  <bs-dialog
    :title="$t('pa.flow.batchOperationDetail')"
    :visible.sync="dialogVisible"
    size="large"
    :before-close="handleClose"
    @confirm="handleConfirm"
  >
    <!-- 弹框内容 -->
    <div class="batch-detail-dialog">
      <!-- 搜索区域 -->
      <div class="batch-detail-dialog__header">
        <bs-search
          v-model.trim="keywords"
          :placeholder="$t('pa.flow.placeholder51')"
          maxlength="30"
          style="width: 250px"
          @search="handleSearch"
        />
        <!-- 运行状态筛选 -->
        <bs-select
          v-model="searchParams.executeStatus"
          multiple
          collapse-tags
          clearable
          :placeholder="$t('pa.flow.runStatus')"
          :options="statusOptions"
          style="width: 215px"
          @change="handleStatusFilter"
        />

        <el-button size="small" icon="el-icon-refresh" @click="refreshList">
          {{ $t('pa.flow.refresh') }}
        </el-button>
      </div>

      <!-- 表格区域 -->
      <div class="batch-detail-dialog__content">
        <bs-table
          v-loading="tableLoading"
          :data="tableData.tableData"
          class="batch-detail-table"
          height="400px"
          row-key="jobId"
          :column-settings="false"
          :column-data="tableData.columnData"
          :page-data="tableData.pageData"
          @page-change="handleCurrentChange"
        >
          <!-- 流程状态列 slot -->
          <template slot="jobStatus" slot-scope="{ row }">
            <bs-tag size="mini" :color="getJobStatusColor(row.jobStatus)">
              <i :class="getJobStatusIcon(row.jobStatus)"></i>
              {{ getJobStatusText(row.jobStatus) }}
            </bs-tag>
          </template>

          <!-- 执行状态列 slot -->
          <template slot="executeStatus" slot-scope="{ row }">
            <bs-tag size="mini" :color="getExecuteStatusColor(row.executeStatus)">
              {{ getExecuteStatusText(row.executeStatus) }}
            </bs-tag>
          </template>
        </bs-table>
      </div>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { getBatchOperationDetailList } from '@/apis/flowNewApi';

interface IBatchDetailSearchParams {
  batchId: string; // 批次id
  executeStatus: string[]; // 执行状态：0(PendingExecution)、1(ExecutionSuccess)、2(ExecutionFailed)
  jobName: string; // 作业名称
}

@Component
export default class BatchDetailDialog extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() batchData!: any; // 批量操作的基本信息

  keywords = '';
  tableLoading = false;

  // 状态选项
  statusOptions: any[] = [];

  // 状态枚举数据
  jobStatusEnumData: any = {};
  executeStatusEnumData: any = {};

  // 表格配置
  tableData: any = {
    columnData: [],
    tableData: [],
    pageData: {
      pageSize: 10,
      currentPage: 1,
      total: 0,
      layout: 'total, prev, pager, next'
    }
  };

  searchParams: IBatchDetailSearchParams = {
    batchId: '', // 批次id
    executeStatus: [], // 执行状态：0(PendingExecution)、1(ExecutionSuccess)、2(ExecutionFailed)
    jobName: this.keywords // 作业名称
  };

  sortData: any = { order: 'DESC', prop: 'createTime' };

  get dialogVisible() {
    return this.visible;
  }

  set dialogVisible(val: boolean) {
    this.$emit('update:visible', val);
  }

  @Watch('visible')
  onVisibleChange(val: boolean) {
    if (val && this.batchData) {
      this.searchParams.batchId = this.batchData.batchId;
      this.getBatchDetailList();
    }
  }

  // 获取批量操作详情列表
  async getBatchDetailList() {
    try {
      this.tableLoading = true;

      const params = {
        pageData: this.tableData.pageData, // 使用bs-table的分页数据
        search: this.searchParams,
        sortData: this.sortData
      };

      const { data, success, msg, error } = await getBatchOperationDetailList(params);

      if (success) {
        const { columnData, tableData, pageData } = data;

        this.tableData = {
          columnData,
          tableData,
          pageData: {
            ...pageData,
            layout: 'total, prev, pager, next' // 保持简化分页布局
          }
        };

        // 初始化状态选项
        this.initStatusOptions(columnData);
      } else {
        this.$message.error(error || msg);
      }
    } catch (e) {
      this.$message.error(this.$t('pa.flow.getBatchDetailFailed') as string);
    } finally {
      this.tableLoading = false;
    }
  }

  // 初始化状态选项和枚举数据
  initStatusOptions(columnData: any[]) {
    // 提取执行状态枚举数据
    const executeStatusColumn = columnData.find((col) => col.value === 'executeStatus');
    if (executeStatusColumn && executeStatusColumn.enumData) {
      this.executeStatusEnumData = executeStatusColumn.enumData;
      this.statusOptions = Object.keys(executeStatusColumn.enumData).map((key) => ({
        label: executeStatusColumn.enumData[key],
        value: key
      }));
    }

    // 提取流程状态枚举数据
    const jobStatusColumn = columnData.find((col) => col.value === 'jobStatus');
    if (jobStatusColumn && jobStatusColumn.enumData) {
      this.jobStatusEnumData = jobStatusColumn.enumData;
    }
  }

  // 搜索和筛选处理
  handleSearch() {
    this.searchParams.jobName = this.keywords;
    this.handleFilter();
  }

  // 状态筛选处理
  handleStatusFilter() {
    this.handleFilter();
  }

  // 统一的筛选处理
  private handleFilter() {
    this.tableData.pageData.currentPage = 1;
    this.getBatchDetailList();
  }

  // 刷新列表
  refreshList() {
    this.getBatchDetailList();
  }

  // 分页处理 - bs-table的分页回调
  handleCurrentChange(currentPage: number, pageSize: number) {
    this.tableData.pageData.currentPage = currentPage;
    this.tableData.pageData.pageSize = pageSize;
    this.getBatchDetailList(); // 重新获取数据
  }

  // 关闭弹框
  handleClose() {
    this.dialogVisible = false;
    // 重置数据
    this.keywords = '';
    this.searchParams.jobName = '';
    this.searchParams.executeStatus = [];
    this.tableData.tableData = [];
    (this.tableData.pageData as any).currentPage = 1;
  }

  // 确认按钮处理
  handleConfirm() {
    this.handleClose();
  }

  // 获取流程状态文本
  getJobStatusText(status: string): string {
    if (!status) return '';
    return this.jobStatusEnumData[status] || status;
  }

  // 工具方法：根据 value 找到对应的 key
  getKeyByValue(enumData: any, value: string): string {
    if (!enumData || !value) return '';
    return Object.keys(enumData).find((key) => enumData[key] === value) || '';
  }

  // 获取流程状态颜色
  getJobStatusColor(status: string): string {
    const colorMap = {
      INPUB: 'purple', // 发布中
      PROD: 'green', // 已上线
      DEV: 'purple', // 开发
      PASS: '', // 审批通过
      INAPP: 'purple', // 审批中
      PUB: 'green', // 已发布
      INPROD: '', // 上线中
      UNPUB: '', // 取消发布中
      INOFF: '' // 停止中
    };

    return colorMap[status] || '';
  }

  // 获取流程状态图标
  getJobStatusIcon(status: string): string {
    const iconMap = {
      INPUB: 'iconfont icon-shangxianzhong', // 发布中
      INPROD: 'iconfont icon-shangxianzhong', // 上线中
      INOFF: 'iconfont icon-shangxianzhong', // 停止中
      UNPUB: 'iconfont icon-shangxianzhong' // 取消发布中
    };
    return iconMap[status] || '';
  }

  // 获取执行状态文本
  getExecuteStatusText(status: string): string {
    if (!status) return '';
    return this.executeStatusEnumData[status] || status;
  }

  // 获取执行状态颜色
  getExecuteStatusColor(status: string): string {
    const colorMap = {
      '0': '', // 未执行
      '1': 'green', // 执行成功
      '2': 'red', // 执行失败
      '3': '' // 执行跳过
    };
    return colorMap[status] || '';
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
}

.batch-detail-dialog {
  &__header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0 20px 18px 20px;
  }

  &__content {
    .batch-detail-table {
      width: 100%;
    }
  }
}
</style>
