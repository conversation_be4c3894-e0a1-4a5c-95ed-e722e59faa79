<template>
  <pro-page title="选项管理" :loading="loading" :fixed-header="false">
    <div slot="operation" class="operate-box">
      <bs-search
        v-model="search"
        placeholder="请输入选项名称"
        maxlength="30"
        @search="getListData"
      />
      <el-button
        v-access="'PA.DATA.ITEM.ADD'"
        type="primary"
        style="margin-left: 10px"
        @click="newOption"
      >
        新建选项
      </el-button>
    </div>
    <bs-table
      :height="selectedIds.length ? 'calc(100vh - 344px)' : 'calc(100vh - 288px)'"
      :data="tableData"
      :column-data="columnData"
      :page-data="pageData"
      :selection="true"
      @page-change="handlePageChange"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
      @refresh="getListData"
    >
      <el-button
        slot="headerOperator"
        v-access="'PA.DATA.ITEM.DELETE'"
        size="small"
        @click="delOptions()"
      >
        删除
      </el-button>
      <template slot="action" slot-scope="{ row }">
        <i
          v-if="hasDelAuthority(row.dataLevelType)"
          class="iconfont icon-shanchu"
          @click="delOptions(row)"
        ></i>
      </template>
    </bs-table>
    <option-add v-if="dialogVisible" :visible="dialogVisible" @close="closeDialog" />
  </pro-page>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import { getItemList, delItems } from '@/apis/dataApi';
import { hasPermission } from '@/utils';
import CommonDelete from '@/utils/mixins/common-delete';
import moment from 'moment';
@Component({
  components: {
    'option-add': () => import('./modals/edit-option-dialog.vue')
  }
})
export default class OptionManage extends Mixins(CommonDelete) {
  dialogVisible = false;
  loading = false;
  search = '';
  sortData: any = {
    updateTime: 'DESC'
  };
  pageData = { pageSize: 20, currentPage: 1, total: 1 };
  tableData: any = [];
  columnData: any = [];
  selectedIds: string[] = [];
  // 获取列表数据
  getListData() {
    const { search, pageData, sortData } = this;
    const params = {
      search: search ? search.trim() : '',
      pageData,
      sortData
    };
    getItemList(params).then((resp: any) => {
      resp.data.tableData.forEach((item: any) => {
        item.createTime = moment(item.createTime).format('YYYY-MM-DD HH:mm:ss');
      });
      this.tableData = resp.data.tableData;
      this.columnData = resp.data.columnData;
      this.pageData.total = resp.data.pageData.total || 1;
    });
  }
  // 判断是否有删除权限
  hasDelAuthority(dataLevelType: string) {
    return ['SELF', 'CHILD'].includes(dataLevelType) && hasPermission('PA.DATA.ITEM.DELETE');
  }
  // 新建选项
  newOption() {
    this.dialogVisible = true;
  }
  // 删除选项
  delOptions(row: any) {
    const ids = !row
      ? this.selectedIds
      : { id: row.id, name: row.itemName, reference: row.itemReference };
    this.commonDel(ids, async (delIds) => {
      const { success, msg } = await delItems(delIds);
      if (success) {
        this.getListData();
        this.$message.success(msg);
      }
    });
  }
  // 表格页码修改回调
  handlePageChange(page: number, size: number) {
    this.pageData.currentPage = page;
    this.pageData.pageSize = size;
    this.getListData();
  }
  // 表格排序回调
  handleSortChange(val) {
    this.sortData = {
      [val.prop]: val.order === 'ascending' ? 'ASC' : 'DESC'
    };
    this.getListData();
  }
  // 表格多选回调
  handleSelectionChange(sel: any) {
    this.selectedIds = sel.map((e) => ({ id: e.id, name: e.itemName, reference: e.itemReference }));
  }
  // 关闭弹窗
  closeDialog(needFresh: any) {
    needFresh && this.getListData();
    this.dialogVisible = false;
  }
  created() {
    this.getListData();
    (this.search as any) = this.$route.query.resTitle;
  }
}
</script>

<style scoped>
.iconfont.icon-shanchu {
  cursor: pointer;
}
</style>
