// 流立方stream-cube开始
.cube-main-view {
  height: calc(100vh - 110px) !important;
  overflow-y: auto;
}
.cube-main-view,
.cube-detail-content {
  margin: unset !important;
  padding: 10px !important;
  border-top: 1px solid #d4dce2;
}
// 资产assets开始
.assets-version {
  height: calc(100vh - 150px) !important;
}

.assets-dd-view-body {
  height: calc(100vh - 135px) !important;
  overflow: auto;
}

.assets-func .CodeMirror {
  border: unset;
}
.padA20 {
  padding: 10px 20px;
  height: 50px;
}
.padX20 {
  padding: 5px 20px 0px;
}
.col-3 {
  width: 25%;
  margin-bottom: 20px;
  padding: 0 10px;
}
.col-12 {
  width: 100%;
  margin-bottom: 20px;
  padding: 0 10px;
}
.bs-detail-block {
  background: #fff;
  text-align: left;
  .bs-detail__header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    background: #fff;
    // line-height: 50px;
    // padding: 0 20px;
    &-title {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex: 1;
      font-size: 14px;
      font-weight: 700;
      padding-right: 12px;
      color: #444;
      &:before {
        content: ' ';
        position: relative;
        left: 0;
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 6px;
        background: #ff9c00;
      }
    }
    .bs-page__header-operation {
      display: flex;
      align-items: center;
    }
  }
  .bs-detail__content {
    padding: 0 20px 20px;
  }
  .bs-detail-info {
    display: flex;
    flex-wrap: wrap;
    margin-left: -5px;
    margin-right: -5px;
    &__item {
      padding-left: 5px;
      font-size: 12px;
    }
    &__label {
      color: #777;
      margin-right: 12px;
      font-size: 14px;
    }
    &__content {
      color: #444;
    }
  }
}

// assets 样式覆盖
.bs-layout .bs-tab-nav {
  & .assets-list {
    height: calc(100vh - 110px) !important;
    padding-left: 0px !important;
    padding-right: 0px !important;
    .layout-header .header-left,
    .layout-header .head-row {
      height: 64px;
    }
    .layout-header .circle {
      width: 12px;
      height: 12px;
    }
    .layout-header .tb-title {
      font-size: 16px;
    }
    .ivu-page {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 40px;
      padding-right: 20px;
      background-color: #fff;
    }
  }
  & .assets-detail {
    padding-left: 0px !important;
    padding-right: 0px !important;
    .list-table {
      overflow: auto;
      .ivu-table-wrapper {
        min-width: 1600px;
      }
    }
  }
}
.ivu-modal-mask,
.ivu-modal-wrap {
  z-index: 2013 !important;
}
