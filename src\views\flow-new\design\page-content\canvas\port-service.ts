import { OriginNode } from './interface';
// 输入链接桩 组件对应的限制数量
const pointInLimit = {
  JOIN: 1
};
// 根据组件配置中的节点配置生成dag端点生成需要的数据
export const getDagPort = ({ inEndPoint, outEndPoint, pointIn, pointOut }: OriginNode) => {
  // inEndPoint/outEndPoint来自组件配置中的组件配置信息，生成必要信息后可删除该字段
  // pointIn/pointOut 来自组件信息中配置端点信息的字段
  // inPort/outPort 用于节点端点的渲染 id与uuid对应
  const _inEndPoint = JSON.parse(inEndPoint || '[]') || [];
  const _outEndPoint = JSON.parse(outEndPoint || '[]') || [];
  const inPort = _inEndPoint.map((point, index) => ({
    description: point.label,
    id: (pointIn[index] || {}).uuid
  }));
  const outPort = _outEndPoint.map((point, index) => ({
    description: point.label,
    id: (pointOut[index] || {}).uuid
  }));
  return { inPort, outPort };
};

// 根据daga端点配置数据优化原始数据中的端点数据 增加端点携带数据 端点类型
export const getPort = (inPort, outPort, item, portDataMaps) => {
  const { inputFields, outputFields, type } = item;
  const pointIn = inPort.map((port, idx) => {
    const data =
      (item.pointIn && item.pointIn[idx] && item.pointIn[idx].data) ||
      (inputFields || []).filter((item) => item.outputable !== false);
    portDataMaps.set(port.id, data);
    return {
      type: port.type || 'IN', // 端点类型填充
      uuid: port.id,
      limit: pointInLimit[type] || Infinity, // 链接桩可被链接的限制区数量，目前双流组件的输入需要限制一个输入
      data
    };
  });
  const pointOut = outPort.map((port, idx) => {
    const data =
      (item.pointOut && item.pointOut[idx] && item.pointOut[idx].data) ||
      (outputFields || []).filter((item) => item.outputable !== false);
    portDataMaps.set(port.id, data);
    return {
      type: port.type || 'OUT', // 端点类型填充
      uuid: port.id,
      limit: Infinity,
      data
    };
  });
  return { pointIn, pointOut };
};
